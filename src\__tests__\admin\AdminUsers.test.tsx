/**
 * Admin Users Management Tests
 * 
 * Tests for admin user management functionality including user listing, role management, and user operations.
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { MemoryRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { UserRole } from '../../types/core'

// Mock user data
const mockUsers = [
  {
    id: 'user-1',
    username: 'john_doe',
    email: '<EMAIL>',
    full_name: '<PERSON>',
    role: 'USER' as UserRole,
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z',
    avatar_url: null,
    bio: null,
    location: null,
    interests: null,
    website: null,
  },
  {
    id: 'user-2',
    username: 'jane_admin',
    email: '<EMAIL>',
    full_name: '<PERSON>',
    role: 'MODERAT<PERSON>' as Use<PERSON><PERSON><PERSON>,
    created_at: '2023-01-02T00:00:00.000Z',
    updated_at: '2023-01-02T00:00:00.000Z',
    avatar_url: null,
    bio: null,
    location: null,
    interests: null,
    website: null,
  }
]

// Mock AdminUsers component
const MockAdminUsers: React.FC = () => {
  const [users, setUsers] = React.useState(mockUsers)
  const [loading, setLoading] = React.useState(false)

  const handleRoleChange = (userId: string, newRole: UserRole) => {
    setUsers(prev => prev.map(user => 
      user.id === userId ? { ...user, role: newRole } : user
    ))
  }

  const handleDeleteUser = (userId: string) => {
    setUsers(prev => prev.filter(user => user.id !== userId))
  }

  if (loading) {
    return <div data-testid="loading">Loading users...</div>
  }

  return (
    <div data-testid="admin-users">
      <h1>User Management</h1>
      <div className="users-list">
        {users.map(user => (
          <div key={user.id} data-testid={`user-${user.id}`} className="user-item">
            <div className="user-info">
              <h3>{user.full_name || user.username}</h3>
              <p>{user.email}</p>
              <span className="role-badge" data-testid={`role-${user.id}`}>
                {user.role}
              </span>
            </div>
            <div className="user-actions">
              <select 
                data-testid={`role-select-${user.id}`}
                value={user.role}
                onChange={(e) => handleRoleChange(user.id, e.target.value as UserRole)}
              >
                <option value="USER">User</option>
                <option value="MODERATOR">Moderator</option>
                <option value="CONTENT_ADMIN">Content Admin</option>
                <option value="SUPER_ADMIN">Super Admin</option>
              </select>
              <button 
                data-testid={`delete-${user.id}`}
                onClick={() => handleDeleteUser(user.id)}
                className="delete-btn"
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
      {users.length === 0 && (
        <div data-testid="no-users">No users found</div>
      )}
    </div>
  )
}

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <MemoryRouter>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </MemoryRouter>
  )
}

describe('AdminUsers', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('should render user management interface', () => {
    render(
      <TestWrapper>
        <MockAdminUsers />
      </TestWrapper>
    )

    expect(screen.getByTestId('admin-users')).toBeInTheDocument()
    expect(screen.getByText('User Management')).toBeInTheDocument()
  })

  test('should display list of users', () => {
    render(
      <TestWrapper>
        <MockAdminUsers />
      </TestWrapper>
    )

    expect(screen.getByTestId('user-user-1')).toBeInTheDocument()
    expect(screen.getByTestId('user-user-2')).toBeInTheDocument()
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Jane Admin')).toBeInTheDocument()
  })

  test('should show user information correctly', () => {
    render(
      <TestWrapper>
        <MockAdminUsers />
      </TestWrapper>
    )

    // Check user details
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    
    // Check role badges
    expect(screen.getByTestId('role-user-1')).toHaveTextContent('USER')
    expect(screen.getByTestId('role-user-2')).toHaveTextContent('MODERATOR')
  })

  test('should allow role changes for users', async () => {
    render(
      <TestWrapper>
        <MockAdminUsers />
      </TestWrapper>
    )

    const roleSelect = screen.getByTestId('role-select-user-1')
    
    // Change role from USER to MODERATOR
    fireEvent.change(roleSelect, { target: { value: 'MODERATOR' } })

    await waitFor(() => {
      expect(screen.getByTestId('role-user-1')).toHaveTextContent('MODERATOR')
    })
  })

  test('should provide all role options in select', () => {
    render(
      <TestWrapper>
        <MockAdminUsers />
      </TestWrapper>
    )

    const roleSelect = screen.getByTestId('role-select-user-1')
    const options = roleSelect.querySelectorAll('option')

    expect(options).toHaveLength(4)
    expect(options[0]).toHaveValue('USER')
    expect(options[1]).toHaveValue('MODERATOR')
    expect(options[2]).toHaveValue('CONTENT_ADMIN')
    expect(options[3]).toHaveValue('SUPER_ADMIN')
  })

  test('should allow user deletion', async () => {
    render(
      <TestWrapper>
        <MockAdminUsers />
      </TestWrapper>
    )

    const deleteButton = screen.getByTestId('delete-user-1')
    
    fireEvent.click(deleteButton)

    await waitFor(() => {
      expect(screen.queryByTestId('user-user-1')).not.toBeInTheDocument()
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
    })

    // Other user should still be present
    expect(screen.getByTestId('user-user-2')).toBeInTheDocument()
  })

  test('should show no users message when list is empty', async () => {
    render(
      <TestWrapper>
        <MockAdminUsers />
      </TestWrapper>
    )

    // Delete all users
    fireEvent.click(screen.getByTestId('delete-user-1'))
    fireEvent.click(screen.getByTestId('delete-user-2'))

    await waitFor(() => {
      expect(screen.getByTestId('no-users')).toBeInTheDocument()
      expect(screen.getByText('No users found')).toBeInTheDocument()
    })
  })

  test('should handle loading state', () => {
    // Mock loading state
    const LoadingComponent = () => (
      <div data-testid="loading">Loading users...</div>
    )

    render(
      <TestWrapper>
        <LoadingComponent />
      </TestWrapper>
    )

    expect(screen.getByTestId('loading')).toBeInTheDocument()
    expect(screen.getByText('Loading users...')).toBeInTheDocument()
  })

  test('should display user actions for each user', () => {
    render(
      <TestWrapper>
        <MockAdminUsers />
      </TestWrapper>
    )

    // Check that each user has role select and delete button
    mockUsers.forEach(user => {
      expect(screen.getByTestId(`role-select-${user.id}`)).toBeInTheDocument()
      expect(screen.getByTestId(`delete-${user.id}`)).toBeInTheDocument()
    })
  })

  test('should handle multiple role changes', async () => {
    render(
      <TestWrapper>
        <MockAdminUsers />
      </TestWrapper>
    )

    // Change first user to CONTENT_ADMIN
    fireEvent.change(screen.getByTestId('role-select-user-1'), { 
      target: { value: 'CONTENT_ADMIN' } 
    })

    // Change second user to SUPER_ADMIN
    fireEvent.change(screen.getByTestId('role-select-user-2'), { 
      target: { value: 'SUPER_ADMIN' } 
    })

    await waitFor(() => {
      expect(screen.getByTestId('role-user-1')).toHaveTextContent('CONTENT_ADMIN')
      expect(screen.getByTestId('role-user-2')).toHaveTextContent('SUPER_ADMIN')
    })
  })
})

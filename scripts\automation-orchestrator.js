#!/usr/bin/env node

/**
 * Test Automation Orchestrator
 * 
 * Central command center for all test automation activities.
 * Coordinates file watching, test generation, coverage reporting,
 * and CI/CD integration for maximum developer productivity.
 */

import { execSync, spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { startWatcher } from './test-automation-watcher.js';
import { generateTestFile } from './test-template-generator.js';
import { generateCoverageReport } from './coverage-reporter.js';

// Configuration
const AUTOMATION_CONFIG = {
  modes: {
    DEVELOPMENT: 'development',
    CI_CD: 'ci-cd',
    PRODUCTION: 'production'
  },
  features: {
    FILE_WATCHING: true,
    AUTO_TEST_GENERATION: true,
    COVERAGE_MONITORING: true,
    PERFORMANCE_TRACKING: true,
    NOTIFICATION_SYSTEM: true
  }
};

// State management
let activeProcesses = new Map();
let automationMode = AUTOMATION_CONFIG.modes.DEVELOPMENT;
let isRunning = false;

// Utility functions
function logInfo(message) {
  console.log(`🤖 [Automation] ${message}`);
}

function logSuccess(message) {
  console.log(`✅ [Automation] ${message}`);
}

function logError(message) {
  console.log(`❌ [Automation] ${message}`);
}

function logWarning(message) {
  console.log(`⚠️  [Automation] ${message}`);
}

function displayBanner() {
  console.log(`
╔══════════════════════════════════════════════════════════════╗
║                 🤖 TEST AUTOMATION FRAMEWORK                 ║
║                     Festival Family Project                  ║
╠══════════════════════════════════════════════════════════════╣
║  Intelligent test automation with file watching,            ║
║  auto-generation, coverage monitoring, and CI/CD integration ║
╚══════════════════════════════════════════════════════════════╝
`);
}

function displayMenu() {
  console.log(`
📋 AUTOMATION COMMANDS:

🔄 CONTINUOUS AUTOMATION:
  start-watcher     - Start intelligent file change monitoring
  stop-watcher      - Stop file change monitoring
  
🧪 TEST GENERATION:
  generate-test     - Generate test for a specific file
  scan-missing      - Scan for files missing tests
  
📊 COVERAGE & REPORTING:
  coverage-report   - Generate comprehensive coverage report
  coverage-watch    - Monitor coverage changes continuously
  
🚀 CI/CD INTEGRATION:
  production-check  - Run full production readiness assessment
  deployment-gate   - Validate deployment readiness
  
⚙️  CONFIGURATION:
  set-mode          - Set automation mode (development/ci-cd/production)
  show-status       - Display current automation status
  
❓ HELP:
  help              - Show this menu
  exit              - Exit automation framework
`);
}

function setAutomationMode(mode) {
  if (Object.values(AUTOMATION_CONFIG.modes).includes(mode)) {
    automationMode = mode;
    logSuccess(`Automation mode set to: ${mode}`);
    
    // Adjust features based on mode
    switch (mode) {
      case AUTOMATION_CONFIG.modes.DEVELOPMENT:
        AUTOMATION_CONFIG.features.FILE_WATCHING = true;
        AUTOMATION_CONFIG.features.AUTO_TEST_GENERATION = true;
        break;
      case AUTOMATION_CONFIG.modes.CI_CD:
        AUTOMATION_CONFIG.features.FILE_WATCHING = false;
        AUTOMATION_CONFIG.features.COVERAGE_MONITORING = true;
        break;
      case AUTOMATION_CONFIG.modes.PRODUCTION:
        AUTOMATION_CONFIG.features.PERFORMANCE_TRACKING = true;
        AUTOMATION_CONFIG.features.NOTIFICATION_SYSTEM = true;
        break;
    }
  } else {
    logError(`Invalid mode: ${mode}`);
  }
}

function startFileWatcher() {
  if (activeProcesses.has('watcher')) {
    logWarning('File watcher is already running');
    return;
  }
  
  logInfo('Starting intelligent file watcher...');
  
  try {
    const watcher = startWatcher();
    activeProcesses.set('watcher', watcher);
    logSuccess('File watcher started successfully');
  } catch (error) {
    logError(`Failed to start file watcher: ${error.message}`);
  }
}

function stopFileWatcher() {
  const watcher = activeProcesses.get('watcher');
  if (watcher) {
    watcher.close();
    activeProcesses.delete('watcher');
    logSuccess('File watcher stopped');
  } else {
    logWarning('No file watcher is currently running');
  }
}

function generateTestForFile(filePath) {
  if (!filePath) {
    logError('Please provide a file path');
    return;
  }
  
  if (!fs.existsSync(filePath)) {
    logError(`File does not exist: ${filePath}`);
    return;
  }
  
  logInfo(`Generating test for: ${filePath}`);
  
  try {
    const success = generateTestFile(filePath);
    if (success) {
      logSuccess(`Test generated successfully for ${filePath}`);
    } else {
      logError(`Failed to generate test for ${filePath}`);
    }
  } catch (error) {
    logError(`Test generation failed: ${error.message}`);
  }
}

function scanForMissingTests() {
  logInfo('Scanning for files missing tests...');
  
  const sourceFiles = [];
  const testFiles = new Set();
  
  // Collect source files
  function collectFiles(dir, pattern) {
    const files = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const file of files) {
      const fullPath = path.join(dir, file.name);
      
      if (file.isDirectory() && !file.name.startsWith('.') && file.name !== 'node_modules') {
        collectFiles(fullPath, pattern);
      } else if (file.isFile() && pattern.test(file.name)) {
        if (file.name.includes('.test.')) {
          testFiles.add(fullPath);
        } else {
          sourceFiles.push(fullPath);
        }
      }
    }
  }
  
  collectFiles('src', /\.(ts|tsx)$/);
  
  // Find missing tests
  const missingTests = sourceFiles.filter(sourceFile => {
    const testFile = sourceFile.replace(/\.(ts|tsx)$/, '.test.$1');
    return !testFiles.has(testFile);
  });
  
  if (missingTests.length === 0) {
    logSuccess('All source files have corresponding tests!');
  } else {
    logWarning(`Found ${missingTests.length} files missing tests:`);
    missingTests.forEach(file => console.log(`  • ${file}`));
    
    // Offer to generate tests
    console.log('\nWould you like to generate tests for these files? (y/n)');
    // In a real implementation, you'd handle user input here
  }
  
  return missingTests;
}

function runCoverageReport() {
  logInfo('Generating comprehensive coverage report...');
  
  try {
    const report = generateCoverageReport();
    logSuccess('Coverage report generated successfully');
    return report;
  } catch (error) {
    logError(`Coverage report failed: ${error.message}`);
    return null;
  }
}

function runProductionCheck() {
  logInfo('Running full production readiness assessment...');
  
  try {
    execSync('npm run test:production-readiness', { stdio: 'inherit' });
    logSuccess('Production readiness assessment completed');
  } catch (error) {
    logError(`Production readiness assessment failed: ${error.message}`);
  }
}

function validateDeploymentGate() {
  logInfo('Validating deployment readiness...');
  
  // Check if production readiness report exists
  const reportDir = 'production-readiness-evidence';
  const reportFiles = fs.existsSync(reportDir) ? fs.readdirSync(reportDir) : [];
  const latestReport = reportFiles
    .filter(file => file.startsWith('assessment-') && file.endsWith('.json'))
    .sort()
    .pop();
  
  if (!latestReport) {
    logError('No production readiness report found. Run production-check first.');
    return false;
  }
  
  try {
    const reportPath = path.join(reportDir, latestReport);
    const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
    
    const isReady = report.assessment.overallStatus === 'READY';
    const successRate = report.assessment.successRate;
    
    if (isReady) {
      logSuccess(`✅ DEPLOYMENT APPROVED - Success Rate: ${successRate}`);
      return true;
    } else {
      logError(`❌ DEPLOYMENT BLOCKED - Success Rate: ${successRate}`);
      logError('Issues found:');
      Object.entries(report.testResults).forEach(([test, result]) => {
        if (!result.passed) {
          console.log(`  • ${test}: FAILED`);
        }
      });
      return false;
    }
  } catch (error) {
    logError(`Failed to validate deployment gate: ${error.message}`);
    return false;
  }
}

function showStatus() {
  console.log(`
📊 AUTOMATION STATUS:

Mode: ${automationMode}
File Watcher: ${activeProcesses.has('watcher') ? '🟢 Running' : '🔴 Stopped'}
Active Processes: ${activeProcesses.size}

Features:
  File Watching: ${AUTOMATION_CONFIG.features.FILE_WATCHING ? '✅' : '❌'}
  Auto Test Generation: ${AUTOMATION_CONFIG.features.AUTO_TEST_GENERATION ? '✅' : '❌'}
  Coverage Monitoring: ${AUTOMATION_CONFIG.features.COVERAGE_MONITORING ? '✅' : '❌'}
  Performance Tracking: ${AUTOMATION_CONFIG.features.PERFORMANCE_TRACKING ? '✅' : '❌'}
  Notification System: ${AUTOMATION_CONFIG.features.NOTIFICATION_SYSTEM ? '✅' : '❌'}
`);
}

function handleCommand(command, args = []) {
  switch (command.toLowerCase()) {
    case 'start-watcher':
      startFileWatcher();
      break;
    case 'stop-watcher':
      stopFileWatcher();
      break;
    case 'generate-test':
      generateTestForFile(args[0]);
      break;
    case 'scan-missing':
      scanForMissingTests();
      break;
    case 'coverage-report':
      runCoverageReport();
      break;
    case 'production-check':
      runProductionCheck();
      break;
    case 'deployment-gate':
      validateDeploymentGate();
      break;
    case 'set-mode':
      setAutomationMode(args[0]);
      break;
    case 'show-status':
      showStatus();
      break;
    case 'help':
      displayMenu();
      break;
    case 'exit':
      cleanup();
      process.exit(0);
      break;
    default:
      logError(`Unknown command: ${command}`);
      logInfo('Type "help" for available commands');
  }
}

function cleanup() {
  logInfo('Cleaning up automation processes...');
  
  for (const [name, process] of activeProcesses) {
    try {
      if (process.close) {
        process.close();
      } else if (process.kill) {
        process.kill();
      }
      logInfo(`Stopped ${name}`);
    } catch (error) {
      logWarning(`Failed to stop ${name}: ${error.message}`);
    }
  }
  
  activeProcesses.clear();
  logSuccess('Cleanup completed');
}

function startInteractiveMode() {
  displayBanner();
  displayMenu();
  
  process.stdin.setEncoding('utf8');
  process.stdin.on('data', (input) => {
    const [command, ...args] = input.trim().split(' ');
    if (command) {
      handleCommand(command, args);
    }
  });
  
  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n');
    logInfo('Shutting down automation framework...');
    cleanup();
    process.exit(0);
  });
  
  logInfo('Automation framework ready. Type "help" for commands.');
}

// CLI interface
function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    startInteractiveMode();
  } else {
    const [command, ...commandArgs] = args;
    handleCommand(command, commandArgs);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { 
  startFileWatcher, 
  stopFileWatcher, 
  generateTestForFile, 
  runCoverageReport, 
  validateDeploymentGate 
};

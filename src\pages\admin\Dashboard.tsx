import React, { startTransition } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useProfile } from '../../hooks/useProfile';
import { isAdminRole } from '@/lib/utils/auth';
import { BentoCard, BentoGrid } from '@/components/design-system/BentoGrid';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';

import { Terminal, User } from 'lucide-react';
import { DataMigrationPanel } from '@/components/admin/DataMigrationPanel';

const Dashboard: React.FC = () => {
  const { profile, isLoading, error } = useProfile(); // Corrected loading state property
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <div className="space-y-6 p-4 md:p-6">
        <Skeleton className="h-8 w-1/3" /> {/* Removed custom bg */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Skeleton key={i} className="h-24 rounded-lg" />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="m-4 md:m-6">
        <Terminal className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load user profile: {error.message}
        </AlertDescription>
      </Alert>
    );
  }

  if (!isAdminRole(profile?.role)) {
    return (
      <Alert variant="warning" className="m-4 md:m-6">
        <Terminal className="h-4 w-4" />
        <AlertTitle>Access Denied</AlertTitle>
        <AlertDescription>
          You do not have permission to access the admin dashboard.
        </AlertDescription>
      </Alert>
    );
  }

  const adminLinks = [
    { to: '/admin/analytics', title: 'Analytics Dashboard' },
    { to: '/admin/festivals', title: 'Manage Festivals' },
    { to: '/admin/events', title: 'Manage Events' },
    { to: '/admin/activities', title: 'Manage Activities' },
    { to: '/admin/announcements', title: 'Manage Announcements' },
    { to: '/admin/users', title: 'Manage Users' },
    { to: '/admin/guides', title: 'Manage Guides' },
    { to: '/admin/tips', title: 'Manage Tips' },
    { to: '/admin/faqs', title: 'Manage FAQs' },
    { to: '/admin/external-links', title: 'Manage External Links' },
    { to: '/admin/local-info', title: 'Manage Local Information' },
  ];

  return (
    <div className="space-y-6 p-4 md:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold text-foreground">Admin Dashboard</h1>
        <Button
          onClick={() => {
            startTransition(() => {
              navigate('/');
            });
          }}
          variant="outline"
          className="flex items-center gap-2 w-fit micro-bounce hover-lift transition-all duration-300"
        >
          <User className="h-4 w-4" />
          Return to User View
        </Button>
      </div>

      {/* Festival Data Migration Panel */}
      <div className="mb-8">
        <DataMigrationPanel />
      </div>

      {/* Enhanced 2025 Visual Excellence: Admin Dashboard BentoGrid */}
      <BentoGrid cols={3} gap="md">
        {adminLinks.map((link, index) => {
          // Enhanced admin color themes using design tokens
          const getAdminColorTheme = (index: number) => {
            const adminColorThemes = [
              'bg-gradient-to-br from-primary/20 to-primary/25 border-primary/30 shadow-lg shadow-primary/10',
              'bg-gradient-to-br from-festival-success/20 to-festival-success/25 border-festival-success/30 shadow-lg shadow-festival-success/10',
              'bg-gradient-to-br from-festival-accent/20 to-festival-accent/25 border-festival-accent/30 shadow-lg shadow-festival-accent/10',
              'bg-gradient-to-br from-festival-warning/20 to-festival-warning/25 border-festival-warning/30 shadow-lg shadow-festival-warning/10',
              'bg-gradient-to-br from-accent/20 to-accent/25 border-accent/30 shadow-lg shadow-accent/10',
              'bg-gradient-to-br from-muted/20 to-muted/25 border-muted/30 shadow-lg shadow-muted/10'
            ];
            return adminColorThemes[index % adminColorThemes.length];
          };

          return (
            <Link key={link.to} to={link.to} className="block group">
              <BentoCard
                title={link.title}
                description="Admin management panel"
                variant="glassmorphism"
                interactive
                className={getAdminColorTheme(index)}
                icon={<Terminal className="w-6 h-6 text-primary font-bold" />}
                action={
                  <div className="bg-primary/90 text-primary-foreground text-xs font-semibold px-3 py-1.5 rounded-full shadow-sm">
                    Admin
                  </div>
                }
              />
            </Link>
          );
        })}
      </BentoGrid>
    </div>
  );
};

export default Dashboard;

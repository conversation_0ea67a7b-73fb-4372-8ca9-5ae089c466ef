/**
 * Festival Family - Main Application Entry Point
 * 
 * Clean, working version with proper Router context hierarchy
 * and Redis integration for high-performance caching.
 * 
 * @version 2.0.0
 * <AUTHOR> Family Team
 */

import React, { Suspense, lazy } from 'react'
import ReactDOM from 'react-dom/client'
import { createBrowserRouter, RouterProvider, Navigate } from 'react-router-dom'

// Core styles and design system
import './index.css'
import './styles/design-tokens.css'

// Core providers
import { ConsolidatedAuthProvider } from './providers/ConsolidatedAuthProvider'
import { QueryProvider } from './providers/QueryProvider'
import { ThemeProvider } from './providers/ThemeProvider'
import { SecurityProvider } from './providers/SecurityProvider'

// Error boundaries and monitoring
import { GlobalErrorBoundary } from './components/error/GlobalErrorBoundary'
import { RouterErrorBoundary } from './components/error/RouterErrorBoundary'
import { initSentry } from './lib/sentry'

// Performance monitoring and analytics
import { Analytics } from '@vercel/analytics/react'
import { initializeMobileOptimizations } from './utils/mobileOptimizationInit'
import { initializeRedis } from './lib/redis/redis-config'

// Loading components
import { LoadingSpinner } from './components/ui/LoadingSpinner'
import { ConnectionStatus } from './components/debug/ConnectionStatus'

// Lazy-loaded components
const AppLayout = lazy(() => import('./components/layout/AppLayout'))
const SmartHome = lazy(() => import('./pages/SmartHome'))
const SimpleAuth = lazy(() => import('./pages/auth/SimpleAuth'))
const AuthCallback = lazy(() => import('./pages/auth/AuthCallback'))
const Activities = lazy(() => import('./pages/Activities'))
const Discover = lazy(() => import('./pages/Discover'))
const FamHub = lazy(() => import('./pages/FamHub'))
const NotFound = lazy(() => import('./pages/NotFound'))

// Admin routes
import { adminRoutes } from './pages/admin/routes'

// Debug components (development only)
const SupabaseTest = import.meta.env.DEV ? lazy(() => import('./components/debug/SupabaseTest')) : null
const ClientTest = import.meta.env.DEV ? lazy(() => import('./components/debug/ClientTest')) : null
const ErrorDemo = import.meta.env.DEV ? lazy(() => import('./components/debug/ErrorDemo')) : null
const RetryDemo = import.meta.env.DEV ? lazy(() => import('./components/debug/RetryDemo')) : null
const BadgeTestPage = import.meta.env.DEV ? lazy(() => import('./pages/debug/BadgeTestPage')) : null
const FavoritesTest = import.meta.env.DEV ? lazy(() => import('./components/debug/FavoritesTest')) : null

// ============================================================================
// SYSTEM INITIALIZATION
// ============================================================================

// Initialize core systems
initSentry()
initializeRedis()

// Initialize mobile optimizations
initializeMobileOptimizations().then(() => {
  console.log('🎉 Mobile optimizations ready')
}).catch(error => {
  console.warn('⚠️ Mobile optimization initialization failed:', error)
})

// ============================================================================
// ROUTER CONFIGURATION
// ============================================================================

const router = createBrowserRouter([
  // Admin routes
  ...adminRoutes,
  
  // Auth routes (outside of main layout)
  {
    path: '/auth',
    element: <Suspense fallback={<LoadingSpinner />}><SimpleAuth /></Suspense>,
  },
  {
    path: '/auth/callback',
    element: <Suspense fallback={<LoadingSpinner />}><AuthCallback /></Suspense>,
  },
  {
    path: '/login',
    element: <Navigate to="/auth" replace />,
  },

  // Debug routes (development only)
  ...(import.meta.env.DEV ? [
    {
      path: '/debug/supabase',
      element: SupabaseTest ? <Suspense fallback={<LoadingSpinner />}><SupabaseTest /></Suspense> : <NotFound />,
    },
    {
      path: '/debug/client-test',
      element: ClientTest ? <Suspense fallback={<LoadingSpinner />}><ClientTest /></Suspense> : <NotFound />,
    },
    {
      path: '/debug/error-demo',
      element: ErrorDemo ? <Suspense fallback={<LoadingSpinner />}><ErrorDemo /></Suspense> : <NotFound />,
    },
    {
      path: '/debug/retry-demo',
      element: RetryDemo ? <Suspense fallback={<LoadingSpinner />}><RetryDemo /></Suspense> : <NotFound />,
    },
    {
      path: '/debug/badge-test',
      element: BadgeTestPage ? <Suspense fallback={<LoadingSpinner />}><BadgeTestPage /></Suspense> : <NotFound />,
    },
    {
      path: '/debug/favorites-test',
      element: FavoritesTest ? <Suspense fallback={<LoadingSpinner />}><FavoritesTest /></Suspense> : <NotFound />,
    },
  ] : []),

  // Main app routes with AppLayout
  {
    element: <Suspense fallback={<LoadingSpinner />}><AppLayout /></Suspense>,
    children: [
      {
        path: '/',
        element: <Suspense fallback={<LoadingSpinner />}><SmartHome /></Suspense>,
      },
      {
        path: '/activities',
        element: <Suspense fallback={<LoadingSpinner />}><Activities /></Suspense>,
      },
      {
        path: '/discover',
        element: <Suspense fallback={<LoadingSpinner />}><Discover /></Suspense>,
      },
      {
        path: '/famhub',
        element: <Suspense fallback={<LoadingSpinner />}><FamHub /></Suspense>,
      },
    ]
  },

  // Catch-all route
  {
    path: '*',
    element: <NotFound />
  }
], {
  future: {
    v7_relativeSplatPath: true,
    v7_fetcherPersist: true,
    v7_normalizeFormMethod: true,
    v7_partialHydration: true,
    v7_skipActionErrorRevalidation: true,
  }
})

// ============================================================================
// ROOT ELEMENT SETUP
// ============================================================================

const rootElement = document.getElementById('root')
if (!rootElement) throw new Error('Root element not found')

// ============================================================================
// APPLICATION RENDER
// ============================================================================

ReactDOM.createRoot(rootElement).render(
  <React.StrictMode>
    <SecurityProvider>
      <GlobalErrorBoundary>
        <ThemeProvider>
          <ConsolidatedAuthProvider>
            <QueryProvider>
              <RouterErrorBoundary>
                <RouterProvider router={router} />
              </RouterErrorBoundary>
              <Analytics />
              {import.meta.env.DEV && (
                <ConnectionStatus />
              )}
            </QueryProvider>
          </ConsolidatedAuthProvider>
        </ThemeProvider>
      </GlobalErrorBoundary>
    </SecurityProvider>
  </React.StrictMode>,
)

# Code Organization Implementation Guide

This guide provides step-by-step instructions for implementing the solutions to code organization issues.

## Prerequisites

- Ensure you have the latest code from the repository
- Install all dependencies with `npm install`
- Make sure you have a backup of the codebase before starting

## Implementation Steps

### 1. Establish Project Structure

#### Step 1.1: Create the new folder structure

```bash
# Create the main directory structure
mkdir -p src/features/{auth,festivals,profiles}/{components,hooks,utils}
mkdir -p src/shared/{components,hooks,utils,types}
mkdir -p src/lib/{supabase,date,validation}
mkdir -p src/pages/{festivals,profiles}
```

#### Step 1.2: Create index files for each directory

```bash
# Create index files for exporting components
touch src/features/auth/components/index.ts
touch src/features/festivals/components/index.ts
touch src/features/profiles/components/index.ts
touch src/shared/components/index.ts
```

#### Step 1.3: Update import paths in tsconfig.json

```json
// tsconfig.json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@features/*": ["src/features/*"],
      "@shared/*": ["src/shared/*"],
      "@lib/*": ["src/lib/*"],
      "@pages/*": ["src/pages/*"]
    }
  }
}
```

### 2. Migrate and Decompose Components

#### Step 2.1: Identify large components for decomposition

```bash
# Find large component files
find src -name "*.tsx" -type f -exec wc -l {} \; | sort -nr | head -10
```

#### Step 2.2: Decompose a large component (example)

Original file: `src/pages/FestivalPage.tsx`

```typescript
// Step 1: Create a custom hook for data fetching
// src/features/festivals/hooks/useFestival.ts
import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import type { Festival } from '@/types';

export function useFestival(id: string) {
  const [festival, setFestival] = useState<Festival | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    async function fetchFestival() {
      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from('festivals')
          .select('*')
          .eq('id', id)
          .single();
          
        if (error) throw error;
        setFestival(data);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error'));
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchFestival();
  }, [id]);
  
  return { festival, isLoading, error };
}

// Step 2: Create smaller component files
// src/features/festivals/components/FestivalHeader.tsx
import React from 'react';
import type { Festival } from '@/types';

interface FestivalHeaderProps {
  festival: Festival;
}

export function FestivalHeader({ festival }: FestivalHeaderProps) {
  return (
    <div className="festival-header">
      <h1>{festival.name}</h1>
      <p>{new Date(festival.start_date).toLocaleDateString()} - {new Date(festival.end_date).toLocaleDateString()}</p>
      <p>{festival.location}</p>
    </div>
  );
}

// src/features/festivals/components/FestivalDetails.tsx
import React from 'react';
import type { Festival } from '@/types';

interface FestivalDetailsProps {
  festival: Festival;
}

export function FestivalDetails({ festival }: FestivalDetailsProps) {
  return (
    <div className="festival-details">
      <h2>About this Festival</h2>
      <p>{festival.description}</p>
      {/* Other details */}
    </div>
  );
}

// Step 3: Rewrite the page component to use the new components
// src/pages/festivals/[id].tsx
import React from 'react';
import { useParams } from 'react-router-dom';
import { useFestival } from '@features/festivals/hooks/useFestival';
import { FestivalHeader } from '@features/festivals/components/FestivalHeader';
import { FestivalDetails } from '@features/festivals/components/FestivalDetails';
import { ActivityList } from '@features/festivals/components/ActivityList';
import { LoadingSpinner, ErrorMessage, NotFound } from '@shared/components';

export default function FestivalPage() {
  const { id } = useParams<{ id: string }>();
  const { festival, isLoading, error } = useFestival(id || '');
  
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  if (!festival) return <NotFound />;
  
  return (
    <div className="festival-page">
      <FestivalHeader festival={festival} />
      <FestivalDetails festival={festival} />
      <ActivityList festivalId={festival.id} />
    </div>
  );
}
```

### 3. Extract Shared Utilities

#### Step 3.1: Identify duplicated logic

```bash
# Search for duplicated patterns
grep -r "toLocaleDateString" --include="*.tsx" --include="*.ts" src/
grep -r "new Date" --include="*.tsx" --include="*.ts" src/
```

#### Step 3.2: Create utility functions

```typescript
// src/lib/date/formatters.ts
export function formatDate(date: string | Date): string {
  return new Date(date).toLocaleDateString();
}

export function formatDateRange(startDate: string | Date, endDate: string | Date): string {
  return `${formatDate(startDate)} - ${formatDate(endDate)}`;
}

export function formatTime(date: string | Date): string {
  return new Date(date).toLocaleTimeString();
}

export function formatDateTime(date: string | Date): string {
  return new Date(date).toLocaleString();
}

// src/lib/validation/validators.ts
export function isValidEmail(email: string): boolean {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

export function isValidPassword(password: string): boolean {
  // At least 8 characters, one uppercase, one lowercase, one number
  const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/;
  return regex.test(password);
}
```

#### Step 3.3: Update components to use utilities

```typescript
// Before
const formattedDate = `${new Date(festival.start_date).toLocaleDateString()} - ${new Date(festival.end_date).toLocaleDateString()}`;

// After
import { formatDateRange } from '@lib/date/formatters';

const formattedDate = formatDateRange(festival.start_date, festival.end_date);
```

### 4. Implement Consistent State Management

#### Step 4.1: Install React Query

```bash
npm install @tanstack/react-query
```

#### Step 4.2: Set up React Query client

```typescript
// src/lib/react-query/client.ts
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      cacheTime: 1000 * 60 * 30, // 30 minutes
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});
```

#### Step 4.3: Create API service functions

```typescript
// src/lib/supabase/api.ts
import { supabase } from './client';
import type { Festival, Profile, Activity } from '@/types';

export const festivalApi = {
  getAll: async (): Promise<Festival[]> => {
    const { data, error } = await supabase
      .from('festivals')
      .select('*')
      .order('start_date', { ascending: true });
      
    if (error) throw error;
    return data || [];
  },
  
  getById: async (id: string): Promise<Festival> => {
    const { data, error } = await supabase
      .from('festivals')
      .select('*')
      .eq('id', id)
      .single();
      
    if (error) throw error;
    return data;
  },
  
  create: async (festival: Omit<Festival, 'id'>): Promise<Festival> => {
    const { data, error } = await supabase
      .from('festivals')
      .insert(festival)
      .select()
      .single();
      
    if (error) throw error;
    return data;
  },
  
  update: async (id: string, festival: Partial<Festival>): Promise<Festival> => {
    const { data, error } = await supabase
      .from('festivals')
      .update(festival)
      .eq('id', id)
      .select()
      .single();
      
    if (error) throw error;
    return data;
  },
  
  delete: async (id: string): Promise<void> => {
    const { error } = await supabase
      .from('festivals')
      .delete()
      .eq('id', id);
      
    if (error) throw error;
  }
};

// Similar implementations for profileApi, activityApi, etc.
```

#### Step 4.4: Create React Query hooks

```typescript
// src/features/festivals/hooks/useFestivals.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { festivalApi } from '@lib/supabase/api';
import type { Festival } from '@/types';

export function useFestivals() {
  return useQuery({
    queryKey: ['festivals'],
    queryFn: festivalApi.getAll,
  });
}

export function useFestival(id: string) {
  return useQuery({
    queryKey: ['festivals', id],
    queryFn: () => festivalApi.getById(id),
    enabled: !!id,
  });
}

export function useCreateFestival() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: festivalApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['festivals'] });
    },
  });
}

export function useUpdateFestival() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, festival }: { id: string; festival: Partial<Festival> }) => 
      festivalApi.update(id, festival),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['festivals'] });
      queryClient.invalidateQueries({ queryKey: ['festivals', data.id] });
    },
  });
}

export function useDeleteFestival() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: festivalApi.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['festivals'] });
    },
  });
}
```

#### Step 4.5: Update components to use React Query

```typescript
// Before (using useState and useEffect)
function FestivalList() {
  const [festivals, setFestivals] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    async function fetchFestivals() {
      try {
        setIsLoading(true);
        const { data, error } = await supabase.from('festivals').select('*');
        if (error) throw error;
        setFestivals(data);
      } catch (err) {
        setError(err);
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchFestivals();
  }, []);
  
  // Render logic
}

// After (using React Query)
import { useFestivals } from '@features/festivals/hooks/useFestivals';

function FestivalList() {
  const { data: festivals, isLoading, error } = useFestivals();
  
  // Render logic
}
```

### 5. Implement Component Composition

#### Step 5.1: Create layout components

```typescript
// src/shared/components/layout/PageLayout.tsx
import React from 'react';

interface PageLayoutProps {
  children: React.ReactNode;
}

export function PageLayout({ children }: PageLayoutProps) {
  return (
    <div className="page-layout">
      {children}
    </div>
  );
}

// src/shared/components/layout/Section.tsx
import React from 'react';

interface SectionProps {
  title?: string;
  children: React.ReactNode;
}

export function Section({ title, children }: SectionProps) {
  return (
    <section className="content-section">
      {title && <h2>{title}</h2>}
      {children}
    </section>
  );
}

// src/shared/components/layout/Card.tsx
import React from 'react';

interface CardProps {
  title?: string;
  children: React.ReactNode;
}

export function Card({ title, children }: CardProps) {
  return (
    <div className="card">
      {title && <div className="card-header">{title}</div>}
      <div className="card-body">{children}</div>
    </div>
  );
}
```

#### Step 5.2: Create UI component library

```typescript
// src/shared/components/ui/Button.tsx
import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'small' | 'medium' | 'large';
  isLoading?: boolean;
}

export function Button({
  variant = 'primary',
  size = 'medium',
  isLoading = false,
  children,
  ...props
}: ButtonProps) {
  return (
    <button
      className={`btn btn-${variant} btn-${size} ${isLoading ? 'btn-loading' : ''}`}
      disabled={isLoading || props.disabled}
      {...props}
    >
      {isLoading ? <span className="loading-spinner" /> : null}
      {children}
    </button>
  );
}

// Additional UI components like Input, Select, Modal, etc.
```

#### Step 5.3: Refactor pages to use composition

```typescript
// Before: Monolithic page
function ProfilePage() {
  // 200+ lines with everything inline
}

// After: Composition-based page
import { PageLayout } from '@shared/components/layout/PageLayout';
import { Section } from '@shared/components/layout/Section';
import { Card } from '@shared/components/layout/Card';
import { ProfileHeader } from '@features/profiles/components/ProfileHeader';
import { ProfileDetails } from '@features/profiles/components/ProfileDetails';
import { UserFestivalsList } from '@features/profiles/components/UserFestivalsList';
import { UserActivitiesList } from '@features/profiles/components/UserActivitiesList';
import { Tabs, TabPanel } from '@shared/components/ui/Tabs';

function ProfilePage() {
  return (
    <PageLayout>
      <ProfileHeader />
      
      <Section>
        <Card>
          <ProfileDetails />
        </Card>
      </Section>
      
      <Section title="Your Festival Experience">
        <Tabs>
          <TabPanel label="Festivals">
            <UserFestivalsList />
          </TabPanel>
          <TabPanel label="Activities">
            <UserActivitiesList />
          </TabPanel>
        </Tabs>
      </Section>
    </PageLayout>
  );
}
```

### 6. Enforce File Size Limits

#### Step 6.1: Add ESLint rule for max lines

```javascript
// .eslintrc.js
module.exports = {
  // ... other config
  rules: {
    // ... other rules
    'max-lines': ['warn', {
      max: 500,
      skipBlankLines: true,
      skipComments: true
    }],
  },
};
```

#### Step 6.2: Run ESLint to identify files exceeding the limit

```bash
npx eslint --rule 'max-lines: ["error", 500]' src/**/*.{ts,tsx}
```

#### Step 6.3: Split large files into smaller modules

For each file exceeding 500 lines:

1. Identify logical groupings of code
2. Extract each group into its own file
3. Export from the new file
4. Import into the original file

Example:

```typescript
// Before: Large utility file
// src/utils/helpers.ts (1000+ lines)
export function formatDate() { /* ... */ }
export function validateEmail() { /* ... */ }
// ... many more functions

// After: Split into domain-specific files
// src/lib/date/formatters.ts
export function formatDate() { /* ... */ }
export function formatDateRange() { /* ... */ }

// src/lib/validation/validators.ts
export function validateEmail() { /* ... */ }
export function validatePassword() { /* ... */ }
```

### 7. Update Import Statements

#### Step 7.1: Install import sorter

```bash
npm install --save-dev eslint-plugin-import
```

#### Step 7.2: Configure ESLint for imports

```javascript
// .eslintrc.js
module.exports = {
  // ... other config
  plugins: [
    // ... other plugins
    'import'
  ],
  rules: {
    // ... other rules
    'import/order': ['error', {
      'groups': [
        'builtin',
        'external',
        'internal',
        ['parent', 'sibling'],
        'index'
      ],
      'pathGroups': [
        {
          'pattern': '@/**',
          'group': 'internal'
        }
      ],
      'newlines-between': 'always',
      'alphabetize': {
        'order': 'asc',
        'caseInsensitive': true
      }
    }],
  },
};
```

#### Step 7.3: Update imports across the codebase

```bash
# Run ESLint with --fix to automatically update imports
npx eslint --fix --ext .ts,.tsx src/
```

### 8. Create Documentation

#### Step 8.1: Document the new project structure

```markdown
# Project Structure

The codebase follows a feature-based organization:

## Directory Structure

- `src/features/` - Feature modules (auth, festivals, profiles)
  - Each feature contains its own components, hooks, and utilities
- `src/shared/` - Shared components and utilities
- `src/lib/` - Core libraries and services
- `src/pages/` - Page components and routing

## Import Conventions

- Use absolute imports with aliases:
  ```
  import { Component } from '@features/auth/components';
  import { useHook } from '@features/festivals/hooks';
  import { Utility } from '@lib/date/formatters';
  import { SharedComponent } from '@shared/components';
  import { Page } from '@pages/festivals';
  ```

## File Size Limits

- Files should not exceed 500 lines (excluding comments and blank lines)
- Large files should be split into smaller modules
```

#### Step 8.2: Document the new component structure

```markdown
# Component Structure

The codebase follows a component-based organization:

## Directory Structure

- `src/features/` - Feature modules (auth, festivals, profiles)
  - Each feature contains its own components, hooks, and utilities
- `src/shared/` - Shared components and utilities
- `src/lib/` - Core libraries and services
- `src/pages/` - Page components and routing

## Component Composition

- Use layout components (PageLayout, Section, Card) to structure pages
- Use UI components (Button, Input, Select, Modal, etc.) for reusable UI elements
- Create new components for specific use cases, avoiding large monolithic components
```

#### Step 8.3: Document the new utility structure

```markdown
# Utility Structure

The codebase follows a utility-based organization:

## Directory Structure

- `src/features/` - Feature modules (auth, festivals, profiles)
  - Each feature contains its own components, hooks, and utilities
- `src/shared/` - Shared components and utilities
- `src/lib/` - Core libraries and services
- `src/pages/` - Page components and routing

## Utility Functions

- Create utility functions in domain-specific files
- Use utility functions to avoid duplicated logic
- Document utility functions with JSDoc comments
```

#### Step 8.4: Document the new state management structure

```markdown
# State Management Structure

The codebase follows a state management organization:

## Directory Structure

- `src/features/` - Feature modules (auth, festivals, profiles)
  - Each feature contains its own components, hooks, and utilities
- `src/shared/` - Shared components and utilities
- `src/lib/` - Core libraries and services
- `src/pages/` - Page components and routing

## State Management

- Use React Query for consistent state management
- Create API service functions in `src/lib/supabase/api.ts`
- Create React Query hooks in feature-specific directories
- Use React Query hooks in components instead of useState and useEffect
```

#### Step 8.5: Document the new import structure

```markdown
# Import Structure

The codebase follows a structured import organization:

## Directory Structure

- `src/features/` - Feature modules (auth, festivals, profiles)
  - Each feature contains its own components, hooks, and utilities
- `src/shared/` - Shared components and utilities
- `src/lib/` - Core libraries and services
- `src/pages/` - Page components and routing

## Import Conventions

- Use absolute imports with aliases:
  ```
  import { Component } from '@features/auth/components';
  import { useHook } from '@features/festivals/hooks';
  import { Utility } from '@lib/date/formatters';
  import { SharedComponent } from '@shared/components';
  import { Page } from '@pages/festivals';
  ```

## Import Sorting

- Use ESLint with `eslint-plugin-import` to sort imports
- Follow the order: builtin, external, internal, parent, sibling, index
- Group internal imports (starting with `@`) together
```

#### Step 8.6: Document the new file size limits

```markdown
# File Size Limits

The codebase follows a file size limit organization:

## Directory Structure

- `src/features/` - Feature modules (auth, festivals, profiles)
  - Each feature contains its own components, hooks, and utilities
- `src/shared/` - Shared components and utilities
- `src/lib/` - Core libraries and services
- `src/pages/` - Page components and routing

## File Size Limits

- Files should not exceed 500 lines (excluding comments and blank lines)
- Large files should be split into smaller modules
```

#### Step 8.7: Document the new documentation structure

```markdown
# Documentation Structure

The codebase follows a documentation organization:

## Directory Structure

- `src/features/` - Feature modules (auth, festivals, profiles)
  - Each feature contains its own components, hooks, and utilities
- `src/shared/` - Shared components and utilities
- `src/lib/` - Core libraries and services
- `src/pages/` - Page components and routing

## Documentation

- Create documentation for the new project structure
- Create documentation for the new component structure
- Create documentation for the new utility structure
- Create documentation for the new state management structure
- Create documentation for the new import structure
- Create documentation for the new file size limits
    fetchFestival();
  }, [id]);
  
  return { festival, isLoading, error };
}

// Step 2: Create smaller component files
// src/features/festivals/components/FestivalHeader.tsx
import React from 'react';
import type { Festival } from '@/types';

interface FestivalHeaderProps {
  festival: Festival;
}

export function FestivalHeader({ festival }: FestivalHeaderProps) {
  return (
    <div className="festival-header">
      <h1>{festival.name}</h1>
      <p>{new Date(festival.start_date).toLocaleDateString()} - {new Date(festival.end_date).toLocaleDateString()}</p>
      <p>{festival.location}</p>
    </div>
  );
}

// src/features/festivals/components/FestivalDetails.tsx
import React from 'react';
import type { Festival } from '@/types';

interface FestivalDetailsProps {
  festival: Festival;
}

export function FestivalDetails({ festival }: FestivalDetailsProps) {
  return (
    <div className="festival-details">
      <h2>About this Festival</h2>
      <p>{festival.description}</p>
      {/* Other details */}
    </div>
  );
}

// Step 3: Rewrite the page component to use the new components
// src/pages/festivals/[id].tsx
import React from 'react';
import { useParams } from 'react-router-dom';
import { useFestival } from '@features/festivals/hooks/useFestival';
import { FestivalHeader } from '@features/festivals/components/FestivalHeader';
import { FestivalDetails } from '@features/festivals/components/FestivalDetails';
import { ActivityList } from '@features/festivals/components/ActivityList';
import { LoadingSpinner, ErrorMessage, NotFound } from '@shared/components';

export default function FestivalPage() {
  const { id } = useParams<{ id: string }>();
  const { festival, isLoading, error } = useFestival(id || '');
  
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  if (!festival) return <NotFound />;
  
  return (
    <div className="festival-page">
      <FestivalHeader festival={festival} />
      <FestivalDetails festival={festival} />
      <ActivityList festivalId={festival.id} />
    </div>
  );
}
```

### 3. Extract Shared Utilities

#### Step 3.1: Identify duplicated logic

```bash
# Search for duplicated patterns
grep -r "toLocaleDateString" --include="*.tsx" --include="*.ts" src/
grep -r "new Date" --include="*.tsx" --include="*.ts" src/
```

#### Step 3.2: Create utility functions

```typescript
// src/lib/date/formatters.ts
export function formatDate(date: string | Date): string {
  return new Date(date).toLocaleDateString();
}

export function formatDateRange(startDate: string | Date, endDate: string | Date): string {
  return `${formatDate(startDate)} - ${formatDate(endDate)}`;
}

export function formatTime(date: string | Date): string {
  return new Date(date).toLocaleTimeString();
}

export function formatDateTime(date: string | Date): string {
  return new Date(date).toLocaleString();
}

// src/lib/validation/validators.ts
export function isValidEmail(email: string): boolean {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

export function isValidPassword(password: string): boolean {
  // At least 8 characters, one uppercase, one lowercase, one number
  const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/;
  return regex.test(password);
}
```

#### Step 3.3: Update components to use utilities

```typescript
// Before
const formattedDate = `${new Date(festival.start_date).toLocaleDateString()} - ${new Date(festival.end_date).toLocaleDateString()}`;

// After
import { formatDateRange } from '@lib/date/formatters';

const formattedDate = formatDateRange(festival.start_date, festival.end_date);
```

### 4. Implement Consistent State Management

#### Step 4.1: Install React Query

```bash
npm install @tanstack/react-query
```

#### Step 4.2: Set up React Query client

```typescript
// src/lib/react-query/client.ts
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      cacheTime: 1000 * 60 * 30, // 30 minutes
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});
```

#### Step 4.3: Create API service functions

```typescript
// src/lib/supabase/api.ts
import { supabase } from './client';
import type { Festival, Profile, Activity } from '@/types';

export const festivalApi = {
  getAll: async (): Promise<Festival[]> => {
    const { data, error } = await supabase
      .from('festivals')
      .select('*')
      .order('start_date', { ascending: true });
      
    if (error) throw error;
    return data || [];
  },
  
  getById: async (id: string): Promise<Festival> => {
    const { data, error } = await supabase
      .from('festivals')
      .select('*')
      .eq('id', id)
      .single();
      
    if (error) throw error;
    return data;
  },
  
  create: async (festival: Omit<Festival, 'id'>): Promise<Festival> => {
    const { data, error } = await supabase
      .from('festivals')
      .insert(festival)
      .select()
      .single();
      
    if (error) throw error;
    return data;
  },
  
  update: async (id: string, festival: Partial<Festival>): Promise<Festival> => {
    const { data, error } = await supabase
      .from('festivals')
      .update(festival)
      .eq('id', id)
      .select()
      .single();
      
    if (error) throw error;
    return data;
  },
  
  delete: async (id: string): Promise<void> => {
    const { error } = await supabase
      .from('festivals')
      .delete()
      .eq('id', id);
      
    if (error) throw error;
  }
};

// Similar implementations for profileApi, activityApi, etc.
```

#### Step 4.4: Create React Query hooks

```typescript
// src/features/festivals/hooks/useFestivals.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { festivalApi } from '@lib/supabase/api';
import type { Festival } from '@/types';

export function useFestivals() {
  return useQuery({
    queryKey: ['festivals'],
    queryFn: festivalApi.getAll,
  });
}

export function useFestival(id: string) {
  return useQuery({
    queryKey: ['festivals', id],
    queryFn: () => festivalApi.getById(id),
    enabled: !!id,
  });
}

export function useCreateFestival() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: festivalApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['festivals'] });
    },
  });
}

export function useUpdateFestival() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, festival }: { id: string; festival: Partial<Festival> }) => 
      festivalApi.update(id, festival),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['festivals'] });
      queryClient.invalidateQueries({ queryKey: ['festivals', data.id] });
    },
  });
}

export function useDeleteFestival() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: festivalApi.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['festivals'] });
    },
  });
}
```

#### Step 4.5: Update components to use React Query

```typescript
// Before (using useState and useEffect)
function FestivalList() {
  const [festivals, setFestivals] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    async function fetchFestivals() {
      try {
        setIsLoading(true);
        const { data, error } = await supabase.from('festivals').select('*');
        if (error) throw error;
        setFestivals(data);
      } catch (err) {
        setError(err);
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchFestivals();
  }, []);
  
  // Render logic
}

// After (using React Query)
import { useFestivals } from '@features/festivals/hooks/useFestivals';

function FestivalList() {
  const { data: festivals, isLoading, error } = useFestivals();
  
  // Render logic
}
```

### 5. Implement Component Composition

#### Step 5.1: Create layout components

```typescript
// src/shared/components/layout/PageLayout.tsx
import React from 'react';

interface PageLayoutProps {
  children: React.ReactNode;
}

export function PageLayout({ children }: PageLayoutProps) {
  return (
    <div className="page-layout">
      {children}
    </div>
  );
}

// src/shared/components/layout/Section.tsx
import React from 'react';

interface SectionProps {
  title?: string;
  children: React.ReactNode;
}

export function Section({ title, children }: SectionProps) {
  return (
    <section className="content-section">
      {title && <h2>{title}</h2>}
      {children}
    </section>
  );
}

// src/shared/components/layout/Card.tsx
import React from 'react';

interface CardProps {
  title?: string;
  children: React.ReactNode;
}

export function Card({ title, children }: CardProps) {
  return (
    <div className="card">
      {title && <div className="card-header">{title}</div>}
      <div className="card-body">{children}</div>
    </div>
  );
}
```

#### Step 5.2: Create UI component library

```typescript
// src/shared/components/ui/Button.tsx
import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'small' | 'medium' | 'large';
  isLoading?: boolean;
}

export function Button({
  variant = 'primary',
  size = 'medium',
  isLoading = false,
  children,
  ...props
}: ButtonProps) {
  return (
    <button
      className={`btn btn-${variant} btn-${size} ${isLoading ? 'btn-loading' : ''}`}
      disabled={isLoading || props.disabled}
      {...props}
    >
      {isLoading ? <span className="loading-spinner" /> : null}
      {children}
    </button>
  );
}

// Additional UI components like Input, Select, Modal, etc.
```

#### Step 5.3: Refactor pages to use composition

```typescript
// Before: Monolithic page
function ProfilePage() {
  // 200+ lines with everything inline
}

// After: Composition-based page
import { PageLayout } from '@shared/components/layout/PageLayout';
import { Section } from '@shared/components/layout/Section';
import { Card } from '@shared/components/layout/Card';
import { ProfileHeader } from '@features/profiles/components/ProfileHeader';
import { ProfileDetails } from '@features/profiles/components/ProfileDetails';
import { UserFestivalsList } from '@features/profiles/components/UserFestivalsList';
import { UserActivitiesList } from '@features/profiles/components/UserActivitiesList';
import { Tabs, TabPanel } from '@shared/components/ui/Tabs';

function ProfilePage() {
  return (
    <PageLayout>
      <ProfileHeader />
      
      <Section>
        <Card>
          <ProfileDetails />
        </Card>
      </Section>
      
      <Section title="Your Festival Experience">
        <Tabs>
          <TabPanel label="Festivals">
            <UserFestivalsList />
          </TabPanel>
          <TabPanel label="Activities">
            <UserActivitiesList />
          </TabPanel>
        </Tabs>
      </Section>
    </PageLayout>
  );
}
```

### 6. Enforce File Size Limits

#### Step 6.1: Add ESLint rule for max lines

```javascript
// .eslintrc.js
module.exports = {
  // ... other config
  rules: {
    // ... other rules
    'max-lines': ['warn', {
      max: 500,
      skipBlankLines: true,
      skipComments: true
    }],
  },
};
```

#### Step 6.2: Run ESLint to identify files exceeding the limit

```bash
npx eslint --rule 'max-lines: ["error", 500]' src/**/*.{ts,tsx}
```

#### Step 6.3: Split large files into smaller modules

For each file exceeding 500 lines:

1. Identify logical groupings of code
2. Extract each group into its own file
3. Export from the new file
4. Import into the original file

Example:

```typescript
// Before: Large utility file
// src/utils/helpers.ts (1000+ lines)
export function formatDate() { /* ... */ }
export function validateEmail() { /* ... */ }
// ... many more functions

// After: Split into domain-specific files
// src/lib/date/formatters.ts
export function formatDate() { /* ... */ }
export function formatDateRange() { /* ... */ }

// src/lib/validation/validators.ts
export function validateEmail() { /* ... */ }
export function validatePassword() { /* ... */ }
```

### 7. Update Import Statements

#### Step 7.1: Install import sorter

```bash
npm install --save-dev eslint-plugin-import
```

#### Step 7.2: Configure ESLint for imports

```javascript
// .eslintrc.js
module.exports = {
  // ... other config
  plugins: [
    // ... other plugins
    'import'
  ],
  rules: {
    // ... other rules
    'import/order': ['error', {
      'groups': [
        'builtin',
        'external',
        'internal',
        ['parent', 'sibling'],
        'index'
      ],
      'pathGroups': [
        {
          'pattern': '@/**',
          'group': 'internal'
        }
      ],
      'newlines-between': 'always',
      'alphabetize': {
        'order': 'asc',
        'caseInsensitive': true
      }
    }],
  },
};
```

#### Step 7.3: Update imports across the codebase

```bash
# Run ESLint with --fix to automatically update imports
npx eslint --fix --ext .ts,.tsx src/
```

### 8. Create Documentation

#### Step 8.1: Document the new project structure

```markdown
# Project Structure

The codebase follows a feature-based organization:

## Directory Structure

- `src/features/` - Feature modules (auth, festivals, profiles)
  - Each feature contains its own components, hooks, and utilities
- `src/shared/` - Shared components and utilities
- `src/lib/` - Core libraries and services
- `src/pages/` - Page components and routing

## Import Conventions

- Use absolute imports with aliases:
  ```
  import { Component } from '@features/auth/components';
  import { useHook } from '@features/festivals/hooks';
  import { Utility } from '@lib/date/formatters';
  import { SharedComponent } from '@shared/components';
  import { Page } from '@pages/festivals';
  ```

## File Size Limits

- Files should not exceed 500 lines (excluding comments and blank lines)
- Large files should be split into smaller modules
```

#### Step 8.2: Document the new component structure

```markdown
# Component Structure

The codebase follows a component-based organization:

## Directory Structure

- `src/features/` - Feature modules (auth, festivals, profiles)
  - Each feature contains its own components, hooks, and utilities
- `src/shared/` - Shared components and utilities
- `src/lib/` - Core libraries and services
- `src/pages/` - Page components and routing

## Component Composition

- Use layout components (PageLayout, Section, Card) to structure pages
- Use UI components (Button, Input, Select, Modal, etc.) for reusable UI elements
- Create new components for specific use cases, avoiding large monolithic components
```

#### Step 8.3: Document the new utility structure

```markdown
# Utility Structure

The codebase follows a utility-based organization:

## Directory Structure

- `src/features/` - Feature modules (auth, festivals, profiles)
  - Each feature contains its own components, hooks, and utilities
- `src/shared/` - Shared components and utilities
- `src/lib/` - Core libraries and services
- `src/pages/` - Page components and routing

## Utility Functions

- Create utility functions in domain-specific files
- Use utility functions to avoid duplicated logic
- Document utility functions with JSDoc comments
```

#### Step 8.4: Document the new state management structure

```markdown
# State Management Structure

The codebase follows a state management organization:

## Directory Structure

- `src/features/` - Feature modules (auth, festivals, profiles)
  - Each feature contains its own components, hooks, and utilities
- `src/shared/` - Shared components and utilities
- `src/lib/` - Core libraries and services
- `src/pages/` - Page components and routing

## State Management

- Use React Query for consistent state management
- Create API service functions in `src/lib/supabase/api.ts`
- Create React Query hooks in feature-specific directories
- Use React Query hooks in components instead of useState and useEffect
```

#### Step 8.5: Document the new import structure

```markdown
# Import Structure

The codebase follows a structured import organization:

## Directory Structure

- `src/features/` - Feature modules (auth, festivals, profiles)
  - Each feature contains its own components, hooks, and utilities
- `src/shared/` - Shared components and utilities
- `src/lib/` - Core libraries and services
- `src/pages/` - Page components and routing

## Import Conventions

- Use absolute imports with aliases:
  ```
  import { Component } from '@features/auth/components';
  import { useHook } from '@features/festivals/hooks';
  import { Utility } from '@lib/date/formatters';
  import { SharedComponent } from '@shared/components';
  import { Page } from '@pages/festivals';
  ```

## Import Sorting

- Use ESLint with `eslint-plugin-import` to sort imports
- Follow the order: builtin, external, internal, parent, sibling, index
- Group internal imports (starting with `@`) together
```

#### Step 8.6: Document the new file size limits

```markdown
# File Size Limits

The codebase follows a file size limit organization:

## Directory Structure

- `src/features/` - Feature modules (auth, festivals, profiles)
  - Each feature contains its own components, hooks, and utilities
- `src/shared/` - Shared components and utilities
- `src/lib/` - Core libraries and services
- `src/pages/` - Page components and routing

## File Size Limits

- Files should not exceed 500 lines (excluding comments and blank lines)
- Large files should be split into smaller modules
```

#### Step 8.7: Document the new documentation structure

```markdown
# Documentation Structure

The codebase follows a documentation organization:

## Directory Structure

- `src/features/` - Feature modules (auth, festivals, profiles)
  - Each feature contains its own components, hooks, and utilities
- `src/shared/` - Shared components and utilities
- `src/lib/` - Core libraries and services
- `src/pages/` - Page components and routing

## Documentation

- Create documentation for the new project structure
- Create documentation for the new component structure
- Create documentation for the new utility structure
- Create documentation for the new state management structure
- Create documentation for the new import structure
- Create documentation for the new file size limits
    fetch

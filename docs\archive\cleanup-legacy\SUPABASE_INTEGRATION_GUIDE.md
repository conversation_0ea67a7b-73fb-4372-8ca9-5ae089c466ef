# Festival Family Supabase Integration Guide

This guide provides best practices for integrating Supabase with the Festival Family application, focusing on authentication, database access, and real-time features.

## Table of Contents

1. [Client Initialization](#client-initialization)
2. [Authentication](#authentication)
3. [Database Access](#database-access)
4. [Storage](#storage)
5. [Real-time Features](#real-time-features)
6. [Error Handling](#error-handling)
7. [Type Safety](#type-safety)
8. [Service Layer](#service-layer)

## Client Initialization

### Best Practice: Centralized Client

Create a single, centralized Supabase client instance to ensure consistent configuration and behavior across the application.

```typescript
// src/lib/supabase/client.ts
import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/supabase'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    storage: window.localStorage,
  },
  global: {
    headers: {
      'X-Client-Info': 'festival-family'
    }
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})
```

### Key Points

- **Single Instance**: Create only one Supabase client instance for the entire application
- **Environment Validation**: Validate environment variables at startup
- **Consistent Configuration**: Use the same configuration options across the application
- **Type Safety**: Use the generated Database type for type-safe queries

## Authentication

### Best Practice: Auth Context

Create an authentication context to manage user state across the application.

```typescript
// src/contexts/AuthContext.tsx
import React, { createContext, useState, useContext, useEffect } from 'react'
import { supabase } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'
import type { Profile } from '@/types/supabase'

interface AuthContextType {
  user: User | null
  profile: Profile | null
  isLoading: boolean
  isAdmin: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  loadProfile: (userId: string) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAdmin, setIsAdmin] = useState(false)

  // Load user on mount
  useEffect(() => {
    async function loadUser() {
      setIsLoading(true)

      // Get current session
      const { data: { session } } = await supabase.auth.getSession()

      if (session?.user) {
        setUser(session.user)
        await loadProfile(session.user.id)
      }

      setIsLoading(false)
    }

    loadUser()

    // Listen for auth changes
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        const currentUser = session?.user ?? null
        setUser(currentUser)

        if (event === 'SIGNED_OUT') {
          setProfile(null)
          setIsAdmin(false)
        } else if (currentUser) {
          await loadProfile(currentUser.id)
        }
      }
    )

    return () => {
      authListener.subscription.unsubscribe()
    }
  }, [])

  // Load user profile
  const loadProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error loading profile:', error)
        setProfile(null)
        setIsAdmin(false)
        return
      }

      if (data) {
        setProfile(data)
        setIsAdmin(data.role === 'admin')
      }
    } catch (error) {
      console.error('Error loading profile:', error)
      setProfile(null)
      setIsAdmin(false)
    }
  }

  // Sign in
  const signIn = async (email: string, password: string) => {
    setIsLoading(true)

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error

      if (data.user) {
        await loadProfile(data.user.id)
      }
    } catch (error) {
      console.error('Error signing in:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Sign up
  const signUp = async (email: string, password: string) => {
    setIsLoading(true)

    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      })

      if (error) throw error

      // Note: Profile creation is handled by Supabase triggers
    } catch (error) {
      console.error('Error signing up:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Sign out
  const signOut = async () => {
    setIsLoading(true)

    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
    } catch (error) {
      console.error('Error signing out:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        profile,
        isLoading,
        isAdmin,
        signIn,
        signUp,
        signOut,
        loadProfile,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }

  return context
}
```

### Key Points

- **Centralized Auth State**: Manage authentication state in a single location
- **Profile Loading**: Automatically load user profile after authentication
- **Auth Listeners**: Set up listeners for authentication state changes
- **Role-Based Access**: Track user roles (e.g., admin status)
- **Error Handling**: Properly handle and propagate authentication errors

### Protected Routes

Implement protected routes to restrict access to authenticated users:

```typescript
// src/components/auth/ProtectedRoute.tsx
import React from 'react'
import { Navigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface ProtectedRouteProps {
  children: React.ReactNode
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { user, isLoading } = useAuth()

  if (isLoading) {
    return <LoadingSpinner />
  }

  if (!user) {
    return <Navigate to="/login" replace />
  }

  return <>{children}</>
}
```

### Role-Based Routes

Implement role-based routes to restrict access to users with specific roles:

```typescript
// src/components/auth/AdminRoute.tsx
import React from 'react'
import { Navigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface AdminRouteProps {
  children: React.ReactNode
}

export default function AdminRoute({ children }: AdminRouteProps) {
  const { isAdmin, isLoading } = useAuth()

  if (isLoading) {
    return <LoadingSpinner />
  }

  if (!isAdmin) {
    return <Navigate to="/" replace />
  }

  return <>{children}</>
}
```

## Database Access

### Best Practice: Service Layer

Create a service layer to encapsulate database operations and provide a consistent API for your application.

```typescript
// src/lib/supabase/services/base-service.ts
import { supabase } from '@/lib/supabase/client'
import type { PostgrestError } from '@supabase/supabase-js'

export type ServiceResponse<T> = {
  data: T | null
  error: PostgrestError | Error | null
}

export abstract class BaseService {
  protected async handleResponse<T>(
    promise: Promise<{ data: T | null; error: PostgrestError | null }>
  ): Promise<ServiceResponse<T>> {
    try {
      const { data, error } = await promise
      return { data, error }
    } catch (error) {
      return { data: null, error: error as Error }
    }
  }
}

// src/lib/supabase/services/festival-service.ts
import { BaseService, ServiceResponse } from './base-service'
import { supabase } from '@/lib/supabase/client'
import type { Festival, Activity } from '@/types/supabase'

export class FestivalService extends BaseService {
  // Get all festivals
  async getFestivals(): Promise<ServiceResponse<Festival[]>> {
    return this.handleResponse(
      supabase.from('festivals').select('*')
    )
  }

  // Get a single festival by ID
  async getFestival(id: string): Promise<ServiceResponse<Festival>> {
    return this.handleResponse(
      supabase.from('festivals').select('*').eq('id', id).single()
    )
  }

  // Get a festival with its activities
  async getFestivalWithActivities(id: string): Promise<ServiceResponse<Festival & { activities: Activity[] }>> {
    return this.handleResponse(
      supabase
        .from('festivals')
        .select(`
          *,
          activities(*)
        `)
        .eq('id', id)
        .single()
    )
  }

  // Create a new festival
  async createFestival(festival: Omit<Festival, 'id' | 'created_at'>): Promise<ServiceResponse<Festival>> {
    return this.handleResponse(
      supabase.from('festivals').insert(festival).select().single()
    )
  }

  // Update a festival
  async updateFestival(id: string, festival: Partial<Festival>): Promise<ServiceResponse<Festival>> {
    return this.handleResponse(
      supabase.from('festivals').update(festival).eq('id', id).select().single()
    )
  }

  // Delete a festival
  async deleteFestival(id: string): Promise<ServiceResponse<null>> {
    return this.handleResponse(
      supabase.from('festivals').delete().eq('id', id)
    )
  }
}

export const festivalService = new FestivalService()
```

### Key Points

- **Service Layer**: Create a service layer to encapsulate database operations
- **Consistent Error Handling**: Use a consistent approach to error handling
- **Type Safety**: Use TypeScript types for database entities
- **Reusable Base Class**: Create a base service class for common functionality
- **Single Responsibility**: Each service class should focus on a single domain

### React Query Integration

Use React Query to manage server state and provide a consistent API for data fetching:

```typescript
// src/hooks/festivals/useFestivals.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { festivalService } from '@/lib/supabase/services/festival-service'
import type { Festival } from '@/types/supabase'
import toast from 'react-hot-toast'

// Get all festivals
export function useFestivals() {
  return useQuery({
    queryKey: ['festivals'],
    queryFn: async () => {
      const { data, error } = await festivalService.getFestivals()
      if (error) throw error
      return data as Festival[]
    }
  })
}

// Get a single festival
export function useFestival(id: string) {
  return useQuery({
    queryKey: ['festivals', id],
    queryFn: async () => {
      const { data, error } = await festivalService.getFestival(id)
      if (error) throw error
      return data as Festival
    },
    enabled: !!id
  })
}

// Create a festival
export function useCreateFestival() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (festival: Omit<Festival, 'id' | 'created_at'>) => {
      const { data, error } = await festivalService.createFestival(festival)
      if (error) throw error
      return data as Festival
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['festivals'] })
      toast.success('Festival created successfully')
    },
    onError: (error) => {
      toast.error(`Error creating festival: ${error.message}`)
    }
  })
}

// Update a festival
export function useUpdateFestival() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ id, festival }: { id: string; festival: Partial<Festival> }) => {
      const { data, error } = await festivalService.updateFestival(id, festival)
      if (error) throw error
      return data as Festival
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['festivals'] })
      queryClient.invalidateQueries({ queryKey: ['festivals', data.id] })
      toast.success('Festival updated successfully')
    },
    onError: (error) => {
      toast.error(`Error updating festival: ${error.message}`)
    }
  })
}

// Delete a festival
export function useDeleteFestival() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await festivalService.deleteFestival(id)
      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['festivals'] })
      toast.success('Festival deleted successfully')
    },
    onError: (error) => {
      toast.error(`Error deleting festival: ${error.message}`)
    }
  })
}
```

### Usage in Components

```typescript
// src/pages/Festivals.tsx
import React from 'react'
import { useFestivals, useCreateFestival } from '@/hooks/festivals/useFestivals'
import { FestivalList } from '@/components/festivals/FestivalList'
import { FestivalForm } from '@/components/festivals/FestivalForm'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'

export default function FestivalsPage() {
  const { data: festivals, isLoading, error } = useFestivals()
  const createFestival = useCreateFestival()

  if (isLoading) return <LoadingSpinner />
  if (error) return <ErrorMessage error={error} />

  return (
    <div>
      <h1>Festivals</h1>
      <FestivalForm onSubmit={createFestival.mutate} />
      <FestivalList festivals={festivals} />
    </div>
  )
}
```

## Storage

### Best Practice: Storage Service

Create a storage service to encapsulate file upload and download operations:

```typescript
// src/lib/supabase/services/storage-service.ts
import { BaseService, ServiceResponse } from './base-service'
import { supabase } from '@/lib/supabase/client'

export class StorageService extends BaseService {
  // Upload a file
  async uploadFile(
    bucket: string,
    path: string,
    file: File,
    options?: { cacheControl?: string; upsert?: boolean }
  ): Promise<ServiceResponse<{ path: string }>> {
    return this.handleResponse(
      supabase.storage
        .from(bucket)
        .upload(path, file, {
          cacheControl: options?.cacheControl || '3600',
          upsert: options?.upsert || false,
        })
    )
  }

  // Get a public URL for a file
  getPublicUrl(bucket: string, path: string): string {
    const { data } = supabase.storage.from(bucket).getPublicUrl(path)
    return data.publicUrl
  }

  // Download a file
  async downloadFile(bucket: string, path: string): Promise<ServiceResponse<Blob>> {
    return this.handleResponse(
      supabase.storage.from(bucket).download(path)
    )
  }

  // List files in a bucket
  async listFiles(bucket: string, path?: string): Promise<ServiceResponse<{ name: string; id: string; }[]>> {
    return this.handleResponse(
      supabase.storage.from(bucket).list(path)
    )
  }

  // Delete a file
  async deleteFile(bucket: string, paths: string[]): Promise<ServiceResponse<{ path: string }[]>> {
    return this.handleResponse(
      supabase.storage.from(bucket).remove(paths)
    )
  }
}

export const storageService = new StorageService()
```

### React Query Integration for Storage

```typescript
// src/hooks/storage/useStorage.ts
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { storageService } from '@/lib/supabase/services/storage-service'
import toast from 'react-hot-toast'

// Upload a file
export function useUploadFile() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      bucket,
      path,
      file,
      options,
    }: {
      bucket: string
      path: string
      file: File
      options?: { cacheControl?: string; upsert?: boolean }
    }) => {
      const { data, error } = await storageService.uploadFile(bucket, path, file, options)
      if (error) throw error
      return data
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['files', variables.bucket] })
      toast.success('File uploaded successfully')
    },
    onError: (error) => {
      toast.error(`Error uploading file: ${error.message}`)
    },
  })
}

// List files in a bucket
export function useListFiles(bucket: string, path?: string) {
  return useQuery({
    queryKey: ['files', bucket, path],
    queryFn: async () => {
      const { data, error } = await storageService.listFiles(bucket, path)
      if (error) throw error
      return data
    },
    enabled: !!bucket,
  })
}

// Delete a file
export function useDeleteFile() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ bucket, paths }: { bucket: string; paths: string[] }) => {
      const { data, error } = await storageService.deleteFile(bucket, paths)
      if (error) throw error
      return data
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['files', variables.bucket] })
      toast.success('File deleted successfully')
    },
    onError: (error) => {
      toast.error(`Error deleting file: ${error.message}`)
    },
  })
}
```

### Usage in Components

```typescript
// src/components/profile/ProfileAvatar.tsx
import React, { useState } from 'react'
import { useUploadFile } from '@/hooks/storage/useStorage'
import { useUpdateProfile } from '@/hooks/profile/useProfile'
import { useAuth } from '@/contexts/AuthContext'
import { Avatar } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'

export function ProfileAvatar() {
  const { profile, user } = useAuth()
  const [file, setFile] = useState<File | null>(null)
  const uploadFile = useUploadFile()
  const updateProfile = useUpdateProfile()

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0])
    }
  }

  const handleUpload = async () => {
    if (!file || !user) return

    // Upload file to storage
    const path = `avatars/${user.id}/${file.name}`
    await uploadFile.mutateAsync({
      bucket: 'profiles',
      path,
      file,
      options: { upsert: true },
    })

    // Get public URL
    const avatarUrl = storageService.getPublicUrl('profiles', path)

    // Update profile with new avatar URL
    await updateProfile.mutateAsync({
      id: user.id,
      profile: { avatar_url: avatarUrl },
    })

    setFile(null)
  }

  return (
    <div className="flex flex-col items-center gap-4">
      <Avatar src={profile?.avatar_url || undefined} alt={profile?.username || 'User'} />
      <input type="file" accept="image/*" onChange={handleFileChange} />
      <Button onClick={handleUpload} disabled={!file || uploadFile.isPending}>
        {uploadFile.isPending ? 'Uploading...' : 'Upload Avatar'}
      </Button>
    </div>
  )
}
```

## Real-time Features

### Best Practice: Real-time Subscriptions

Use Supabase's real-time features to subscribe to database changes:

```typescript
// src/lib/supabase/services/realtime-service.ts
import { supabase } from '@/lib/supabase/client'
import type { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js'

export type SubscriptionCallback<T> = (payload: RealtimePostgresChangesPayload<T>) => void

export class RealtimeService {
  private channels: Map<string, RealtimeChannel> = new Map()

  // Subscribe to table changes
  subscribe<T>(
    table: string,
    callback: SubscriptionCallback<T>,
    options?: {
      event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*'
      filter?: string
    }
  ): string {
    const event = options?.event || '*'
    const filter = options?.filter ? `${table}:${options.filter}` : table
    const channelId = `${table}:${event}:${filter}`

    // Check if channel already exists
    if (this.channels.has(channelId)) {
      return channelId
    }

    // Create channel
    const channel = supabase
      .channel(channelId)
      .on(
        'postgres_changes',
        {
          event,
          schema: 'public',
          table,
          filter: options?.filter,
        },
        (payload) => callback(payload as RealtimePostgresChangesPayload<T>)
      )
      .subscribe()

    this.channels.set(channelId, channel)

    return channelId
  }

  // Unsubscribe from a channel
  unsubscribe(channelId: string): void {
    const channel = this.channels.get(channelId)

    if (channel) {
      channel.unsubscribe()
      this.channels.delete(channelId)
    }
  }

  // Unsubscribe from all channels
  unsubscribeAll(): void {
    this.channels.forEach((channel) => {
      channel.unsubscribe()
    })

    this.channels.clear()
  }
}

export const realtimeService = new RealtimeService()
```

### React Hook for Real-time Subscriptions

```typescript
// src/hooks/realtime/useRealtime.ts
import { useEffect } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { realtimeService, SubscriptionCallback } from '@/lib/supabase/services/realtime-service'

export function useRealtimeSubscription<T>(
  table: string,
  queryKey: unknown[],
  options?: {
    event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*'
    filter?: string
    callback?: SubscriptionCallback<T>
  }
) {
  const queryClient = useQueryClient()

  useEffect(() => {
    // Default callback invalidates the query
    const defaultCallback = () => {
      queryClient.invalidateQueries({ queryKey })
    }

    // Subscribe to changes
    const channelId = realtimeService.subscribe<T>(
      table,
      options?.callback || defaultCallback,
      {
        event: options?.event,
        filter: options?.filter,
      }
    )

    // Unsubscribe on cleanup
    return () => {
      realtimeService.unsubscribe(channelId)
    }
  }, [table, JSON.stringify(queryKey), options?.event, options?.filter])
}
```

### Usage in Components

```typescript
// src/pages/FamHub.tsx
import React from 'react'
import { useFestivals } from '@/hooks/festivals/useFestivals'
import { useRealtimeSubscription } from '@/hooks/realtime/useRealtime'
import { FestivalList } from '@/components/festivals/FestivalList'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import toast from 'react-hot-toast'
import type { Festival } from '@/types/supabase'

export default function FamHub() {
  const { data: festivals, isLoading, error } = useFestivals()

  // Subscribe to festival changes
  useRealtimeSubscription<Festival>('festivals', ['festivals'], {
    callback: (payload) => {
      // Show toast notification for new festivals
      if (payload.eventType === 'INSERT') {
        toast.success(`New festival added: ${payload.new.name}`)
      }
    },
  })

  if (isLoading) return <LoadingSpinner />
  if (error) return <ErrorMessage error={error} />

  return (
    <div>
      <h1>FamHub</h1>
      <FestivalList festivals={festivals} />
    </div>
  )
}
```

## Error Handling

### Best Practice: Consistent Error Handling

Implement a consistent approach to error handling for all Supabase operations:

```typescript
// src/lib/supabase/error-handling.ts
import { PostgrestError } from '@supabase/supabase-js'
import toast from 'react-hot-toast'

// Error type with code
export type ErrorWithCode = {
  code: string
  message: string
  details?: string
}

// Format Supabase error
export function formatSupabaseError(error: PostgrestError): ErrorWithCode {
  return {
    code: error.code,
    message: error.message,
    details: error.details
  }
}

// Type guard for PostgrestError
export function isPostgrestError(error: unknown): error is PostgrestError {
  return error !== null &&
         typeof error === 'object' &&
         'code' in error &&
         'message' in error
}

// Global error handler
export function handleSupabaseError(error: unknown): ErrorWithCode {
  if (isPostgrestError(error)) {
    return formatSupabaseError(error)
  }

  return {
    code: 'unknown_error',
    message: error instanceof Error ? error.message : 'An unknown error occurred'
  }
}

// Display error toast
export function showErrorToast(error: unknown): void {
  const formattedError = handleSupabaseError(error)
  toast.error(formattedError.message)
}
```

### Error Boundary for Supabase Errors

```typescript
// src/components/error/SupabaseErrorBoundary.tsx
import React from 'react'
import { ErrorBoundary } from 'react-error-boundary'
import { handleSupabaseError } from '@/lib/supabase/error-handling'
import { Button } from '@/components/ui/button'

interface SupabaseErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function SupabaseErrorBoundary({ children, fallback }: SupabaseErrorBoundaryProps) {
  const handleError = (error: Error) => {
    const formattedError = handleSupabaseError(error)
    console.error('Supabase error:', formattedError)
  }

  return (
    <ErrorBoundary
      onError={handleError}
      fallbackRender={({ error, resetErrorBoundary }) => {
        if (fallback) return <>{fallback}</>

        const formattedError = handleSupabaseError(error)

        return (
          <div className="p-4 border border-red-300 bg-red-50 rounded-md">
            <h3 className="text-lg font-semibold text-red-800">Something went wrong</h3>
            <p className="text-red-600 mt-1">{formattedError.message}</p>
            {formattedError.details && (
              <p className="text-sm text-red-500 mt-1">{formattedError.details}</p>
            )}
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => resetErrorBoundary()}
            >
              Try again
            </Button>
          </div>
        )
      }}
    >
      {children}
    </ErrorBoundary>
  )
}
```

### Usage in Components

```typescript
// src/pages/FestivalDetails.tsx
import React from 'react'
import { useParams } from 'react-router-dom'
import { useFestival } from '@/hooks/festivals/useFestivals'
import { SupabaseErrorBoundary } from '@/components/error/SupabaseErrorBoundary'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

export default function FestivalDetails() {
  const { id } = useParams<{ id: string }>()

  return (
    <SupabaseErrorBoundary>
      <FestivalDetailsContent id={id} />
    </SupabaseErrorBoundary>
  )
}

function FestivalDetailsContent({ id }: { id?: string }) {
  const { data: festival, isLoading } = useFestival(id || '')

  if (isLoading) return <LoadingSpinner />
  if (!festival) return <div>Festival not found</div>

  return (
    <div>
      <h1>{festival.name}</h1>
      {/* Festival details */}
    </div>
  )
}
```

## Type Safety

### Best Practice: Generated Types

Use Supabase's generated types for type-safe database operations:

```typescript
// src/types/supabase.ts (generated)
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      festivals: {
        Row: {
          id: string
          name: string
          description: string
          start_date: string
          end_date: string
          location: string
          image_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          start_date: string
          end_date: string
          location: string
          image_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          start_date?: string
          end_date?: string
          location?: string
          image_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      profiles: {
        Row: {
          id: string
          username: string
          full_name: string | null
          avatar_url: string | null
          role: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          username: string
          full_name?: string | null
          avatar_url?: string | null
          role?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          username?: string
          full_name?: string | null
          avatar_url?: string | null
          role?: string
          created_at?: string
          updated_at?: string
        }
      }
      // Other tables...
    }
    // Views, functions, etc.
  }
}

// Export convenience types
export type Festival = Database['public']['Tables']['festivals']['Row']
export type Profile = Database['public']['Tables']['profiles']['Row']
```

### Runtime Validation with Zod

Use Zod for runtime validation of data:

```typescript
// src/lib/supabase/validation.ts
import { z } from 'zod'
import type { Festival, Profile } from '@/types/supabase'

// Festival schema
export const festivalSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1, 'Name is required'),
  description: z.string(),
  start_date: z.string().refine(
    (date) => !isNaN(Date.parse(date)),
    { message: 'Invalid start date' }
  ),
  end_date: z.string().refine(
    (date) => !isNaN(Date.parse(date)),
    { message: 'Invalid end date' }
  ),
  location: z.string(),
  image_url: z.string().url().nullable(),
  created_at: z.string(),
  updated_at: z.string()
})

// Profile schema
export const profileSchema = z.object({
  id: z.string().uuid(),
  username: z.string().min(3, 'Username must be at least 3 characters'),
  full_name: z.string().nullable(),
  avatar_url: z.string().url().nullable(),
  role: z.enum(['user', 'admin']),
  created_at: z.string(),
  updated_at: z.string()
})

// Validate festival data
export function validateFestival(data: unknown): Festival {
  return festivalSchema.parse(data)
}

// Validate profile data
export function validateProfile(data: unknown): Profile {
  return profileSchema.parse(data)
}

// Safe parse with error handling
export function safeParseFestival(data: unknown): { success: boolean; data?: Festival; error?: z.ZodError } {
  const result = festivalSchema.safeParse(data)
  return result
}

// Safe parse with error handling
export function safeParseProfile(data: unknown): { success: boolean; data?: Profile; error?: z.ZodError } {
  const result = profileSchema.safeParse(data)
  return result
}
```

### Type-Safe Hooks

```typescript
// src/hooks/festivals/useFestivals.ts (with validation)
import { useQuery } from '@tanstack/react-query'
import { festivalService } from '@/lib/supabase/services/festival-service'
import { validateFestival, safeParseFestival } from '@/lib/supabase/validation'
import type { Festival } from '@/types/supabase'

export function useFestivals() {
  return useQuery({
    queryKey: ['festivals'],
    queryFn: async () => {
      const { data, error } = await festivalService.getFestivals()

      if (error) throw error
      if (!data) return []

      // Validate each festival
      const validFestivals: Festival[] = []

      for (const festival of data) {
        try {
          const validFestival = validateFestival(festival)
          validFestivals.push(validFestival)
        } catch (err) {
          console.error('Invalid festival data:', festival, err)
        }
      }

      return validFestivals
    }
  })
}
```

## Service Layer

### Best Practice: Complete Service Layer

Create a complete service layer for all Supabase operations:

```typescript
// src/lib/supabase/services/index.ts
export * from './auth-service'
export * from './profile-service'
export * from './festival-service'
export * from './storage-service'
export * from './realtime-service'
```

### Dependency Injection for Testing

```typescript
// src/lib/supabase/services/base-service.ts (with DI)
import { SupabaseClient } from '@supabase/supabase-js'
import { supabase as defaultClient } from '@/lib/supabase/client'
import type { PostgrestError } from '@supabase/supabase-js'
import type { Database } from '@/types/supabase'

export type ServiceResponse<T> = {
  data: T | null
  error: PostgrestError | Error | null
}

export abstract class BaseService {
  protected client: SupabaseClient<Database>

  constructor(client?: SupabaseClient<Database>) {
    this.client = client || defaultClient
  }

  protected async handleResponse<T>(
    promise: Promise<{ data: T | null; error: PostgrestError | null }>
  ): Promise<ServiceResponse<T>> {
    try {
      const { data, error } = await promise
      return { data, error }
    } catch (error) {
      return { data: null, error: error as Error }
    }
  }
}
```

### Testing Services

```typescript
// src/lib/supabase/services/__tests__/profile-service.test.ts
import { ProfileService } from '../profile-service'
import { createClient } from '@supabase/supabase-js'
import { mockSupabaseClient } from '@/test/mocks/supabase'

describe('ProfileService', () => {
  let profileService: ProfileService

  beforeEach(() => {
    // Create service with mock client
    profileService = new ProfileService(mockSupabaseClient as any)
  })

  test('getProfile returns profile data', async () => {
    // Mock implementation
    mockSupabaseClient.from = jest.fn().mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: { id: '123', username: 'testuser' },
            error: null
          })
        })
      })
    })

    // Call service method
    const result = await profileService.getProfile('123')

    // Assert result
    expect(result.data).toEqual({ id: '123', username: 'testuser' })
    expect(result.error).toBeNull()

    // Assert mock calls
    expect(mockSupabaseClient.from).toHaveBeenCalledWith('profiles')
  })
})
```

---

By following these best practices for Supabase integration, the Festival Family app will have a more maintainable, type-safe, and robust integration with Supabase. These patterns promote separation of concerns, consistent error handling, and proper type safety throughout the application.
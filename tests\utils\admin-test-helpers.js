/**
 * Admin Test Utilities
 * Shared helper functions for E2E admin testing to reduce code duplication
 * and provide consistent testing patterns across all admin test suites.
 */

const { expect } = require('@playwright/test');

/**
 * Admin Authentication Utilities
 */
class AdminAuth {
  /**
   * Perform admin login with credentials and verification
   * @param {Page} page - Playwright page object
   * @param {Object} options - Login options
   * @param {string} options.email - Admin email (default: '<EMAIL>')
   * @param {string} options.password - Admin password (default: 'admin123')
   * @param {boolean} options.expectSuccess - Whether to expect successful login (default: true)
   */
  static async login(page, options = {}) {
    const {
      email = '<EMAIL>',
      password = 'admin123',
      expectSuccess = true
    } = options;

    await page.goto('/admin/login');
    await page.fill('[data-testid="email-input"]', email);
    await page.fill('[data-testid="password-input"]', password);
    await page.click('[data-testid="login-button"]');

    if (expectSuccess) {
      await expect(page).toHaveURL(/\/admin\/dashboard/);
      await expect(page.locator('[data-testid="admin-header"]')).toBeVisible();
      await expect(page.locator('[data-testid="logout-button"]')).toBeVisible();
    }
    
    return page;
  }

  /**
   * Perform admin logout and verify redirect
   * @param {Page} page - Playwright page object
   */
  static async logout(page) {
    await page.click('[data-testid="logout-button"]');
    await expect(page).toHaveURL(/\/admin\/login/);
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible();
    return page;
  }

  /**
   * Verify admin session is active
   * @param {Page} page - Playwright page object
   */
  static async verifySession(page) {
    await expect(page.locator('[data-testid="admin-header"]')).toBeVisible();
    await expect(page.locator('[data-testid="logout-button"]')).toBeVisible();
    return true;
  }
}

/**
 * Evidence Collection Utilities
 */
class EvidenceCollector {
  /**
   * Take screenshot with consistent naming and metadata
   * @param {Page} page - Playwright page object
   * @param {string} testName - Name of the test
   * @param {string} stepName - Name of the test step
   * @param {Object} options - Screenshot options
   */
  static async captureStep(page, testName, stepName, options = {}) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${testName}_${stepName}_${timestamp}.png`;
    
    await page.screenshot({
      path: `test-results/evidence/${filename}`,
      fullPage: true,
      ...options
    });
    
    return filename;
  }

  /**
   * Capture evidence for a test sequence with automatic step numbering
   * @param {Page} page - Playwright page object
   * @param {string} testName - Name of the test
   * @param {Array} steps - Array of step descriptions
   */
  static async captureSequence(page, testName, steps) {
    const evidence = [];
    
    for (let i = 0; i < steps.length; i++) {
      const stepName = `step-${i + 1}-${steps[i].replace(/\s+/g, '-').toLowerCase()}`;
      const filename = await this.captureStep(page, testName, stepName);
      evidence.push({ step: i + 1, description: steps[i], filename });
    }
    
    return evidence;
  }

  /**
   * Create evidence directory if it doesn't exist
   */
  static async ensureEvidenceDirectory() {
    const fs = require('fs').promises;
    const path = require('path');
    
    const evidenceDir = path.join(process.cwd(), 'test-results', 'evidence');
    
    try {
      await fs.access(evidenceDir);
    } catch {
      await fs.mkdir(evidenceDir, { recursive: true });
    }
    
    return evidenceDir;
  }
}

/**
 * Performance Measurement Utilities
 */
class PerformanceTracker {
  /**
   * Start performance tracking for a test operation
   * @param {Page} page - Playwright page object
   * @param {string} operationName - Name of the operation being tracked
   */
  static async startTracking(page, operationName) {
    const startTime = Date.now();
    
    // Start Playwright tracing if needed
    await page.context().tracing.start({
      screenshots: true,
      snapshots: true,
      sources: true
    });
    
    return { operationName, startTime };
  }

  /**
   * End performance tracking and capture metrics
   * @param {Page} page - Playwright page object
   * @param {Object} trackingData - Data from startTracking
   */
  static async endTracking(page, trackingData) {
    const endTime = Date.now();
    const duration = endTime - trackingData.startTime;
    
    // Stop tracing and save
    const tracePath = `test-results/traces/${trackingData.operationName}-${Date.now()}.zip`;
    await page.context().tracing.stop({ path: tracePath });
    
    return {
      operation: trackingData.operationName,
      duration,
      tracePath,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Measure page load performance
   * @param {Page} page - Playwright page object
   * @param {string} url - URL to navigate to
   */
  static async measurePageLoad(page, url) {
    const startTime = Date.now();
    await page.goto(url);
    await page.waitForLoadState('networkidle');
    const endTime = Date.now();
    
    return {
      url,
      loadTime: endTime - startTime,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Admin Navigation Utilities
 */
class AdminNavigation {
  /**
   * Navigate to admin dashboard section
   * @param {Page} page - Playwright page object
   * @param {string} section - Dashboard section to navigate to
   */
  static async goToSection(page, section) {
    const sectionMap = {
      dashboard: '/admin/dashboard',
      users: '/admin/users',
      content: '/admin/content',
      announcements: '/admin/announcements',
      activities: '/admin/activities',
      settings: '/admin/settings',
      reports: '/admin/reports'
    };
    
    const url = sectionMap[section.toLowerCase()];
    if (!url) {
      throw new Error(`Unknown admin section: ${section}`);
    }
    
    await page.goto(url);
    await page.waitForLoadState('networkidle');
    
    // Verify we're in the correct section
    await expect(page.locator(`[data-testid="${section}-section"]`)).toBeVisible();
    
    return page;
  }

  /**
   * Use sidebar navigation to go to a section
   * @param {Page} page - Playwright page object
   * @param {string} section - Section name
   */
  static async useSidebar(page, section) {
    await page.click(`[data-testid="sidebar-${section}"]`);
    await page.waitForLoadState('networkidle');
    await expect(page.locator(`[data-testid="${section}-section"]`)).toBeVisible();
    return page;
  }

  /**
   * Verify admin header and navigation elements are present
   * @param {Page} page - Playwright page object
   */
  static async verifyAdminInterface(page) {
    await expect(page.locator('[data-testid="admin-header"]')).toBeVisible();
    await expect(page.locator('[data-testid="admin-sidebar"]')).toBeVisible();
    await expect(page.locator('[data-testid="logout-button"]')).toBeVisible();
    return true;
  }
}

/**
 * Content Management Utilities
 */
class ContentManager {
  /**
   * Create a new content item with specified data
   * @param {Page} page - Playwright page object
   * @param {Object} contentData - Content item data
   */
  static async createContent(page, contentData) {
    await page.click('[data-testid="create-content-button"]');
    
    if (contentData.title) {
      await page.fill('[data-testid="content-title"]', contentData.title);
    }
    
    if (contentData.description) {
      await page.fill('[data-testid="content-description"]', contentData.description);
    }
    
    if (contentData.category) {
      await page.selectOption('[data-testid="content-category"]', contentData.category);
    }
    
    if (contentData.tags) {
      await page.fill('[data-testid="content-tags"]', contentData.tags.join(', '));
    }
    
    await page.click('[data-testid="save-content-button"]');
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    
    return contentData;
  }

  /**
   * Edit existing content item
   * @param {Page} page - Playwright page object
   * @param {string} contentId - ID of content to edit
   * @param {Object} updates - Updated content data
   */
  static async editContent(page, contentId, updates) {
    await page.click(`[data-testid="edit-content-${contentId}"]`);
    
    if (updates.title) {
      await page.fill('[data-testid="content-title"]', updates.title);
    }
    
    if (updates.description) {
      await page.fill('[data-testid="content-description"]', updates.description);
    }
    
    await page.click('[data-testid="save-content-button"]');
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    
    return updates;
  }

  /**
   * Delete content item
   * @param {Page} page - Playwright page object
   * @param {string} contentId - ID of content to delete
   */
  static async deleteContent(page, contentId) {
    await page.click(`[data-testid="delete-content-${contentId}"]`);
    await page.click('[data-testid="confirm-delete"]');
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    
    // Verify content is no longer visible
    await expect(page.locator(`[data-testid="content-item-${contentId}"]`)).not.toBeVisible();
    
    return true;
  }
}

/**
 * Form Interaction Utilities
 */
class FormHelper {
  /**
   * Fill form fields from data object
   * @param {Page} page - Playwright page object
   * @param {Object} formData - Form field data
   * @param {string} formSelector - Optional form container selector
   */
  static async fillForm(page, formData, formSelector = '') {
    for (const [field, value] of Object.entries(formData)) {
      const selector = formSelector ? `${formSelector} [data-testid="${field}"]` : `[data-testid="${field}"]`;
      
      if (typeof value === 'string') {
        await page.fill(selector, value);
      } else if (typeof value === 'boolean') {
        await page.setChecked(selector, value);
      } else if (Array.isArray(value)) {
        await page.selectOption(selector, value);
      }
    }
  }

  /**
   * Submit form and wait for response
   * @param {Page} page - Playwright page object
   * @param {string} submitSelector - Submit button selector
   * @param {Object} options - Submit options
   */
  static async submitForm(page, submitSelector = '[data-testid="submit-button"]', options = {}) {
    const { expectSuccess = true, expectRedirect = false } = options;
    
    await page.click(submitSelector);
    
    if (expectSuccess) {
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    }
    
    if (expectRedirect) {
      await page.waitForLoadState('networkidle');
    }
    
    return page;
  }
}

/**
 * Data Generator Utilities
 */
class DataGenerator {
  /**
   * Generate random test data for content creation
   * @param {Object} overrides - Override default values
   */
  static generateContentData(overrides = {}) {
    const timestamp = Date.now();
    
    return {
      title: `Test Content ${timestamp}`,
      description: `Test description for content created at ${new Date().toISOString()}`,
      category: 'general',
      tags: ['test', 'automated'],
      ...overrides
    };
  }

  /**
   * Generate random test data for announcements
   * @param {Object} overrides - Override default values
   */
  static generateAnnouncementData(overrides = {}) {
    const timestamp = Date.now();
    
    return {
      title: `Test Announcement ${timestamp}`,
      message: `This is a test announcement created at ${new Date().toISOString()}`,
      type: 'info',
      priority: 'medium',
      targetAudience: 'all',
      ...overrides
    };
  }

  /**
   * Generate random user data for testing
   * @param {Object} overrides - Override default values
   */
  static generateUserData(overrides = {}) {
    const timestamp = Date.now();
    
    return {
      email: `testuser${timestamp}@example.com`,
      firstName: 'Test',
      lastName: 'User',
      role: 'user',
      ...overrides
    };
  }
}

module.exports = {
  AdminAuth,
  EvidenceCollector,
  PerformanceTracker,
  AdminNavigation,
  ContentManager,
  FormHelper,
  DataGenerator
};

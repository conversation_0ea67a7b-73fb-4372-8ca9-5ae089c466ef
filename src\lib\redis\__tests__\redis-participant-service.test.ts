/**
 * Redis Participant Service Tests
 * 
 * Comprehensive test suite for Redis participant service functionality.
 * Tests caching, fallback behavior, performance tracking, and error handling.
 * 
 * @module RedisParticipantServiceTests
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { redisParticipantService } from '../redis-participant-service'

// Mock Redis client
const mockRedis = {
  incr: vi.fn(),
  decr: vi.fn(),
  set: vi.fn(),
  get: vi.fn(),
  del: vi.fn(),
  expire: vi.fn(),
  lrange: vi.fn(),
  lpush: vi.fn(),
  ltrim: vi.fn(),
}

// Mock Redis initialization
vi.mock('@upstash/redis', () => ({
  Redis: vi.fn(() => mockRedis)
}))

describe('RedisParticipantService', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks()
  })

  afterEach(() => {
    // Clean up after each test
    vi.resetAllMocks()
  })

  describe('incrementParticipantCount', () => {
    it('should increment participant count successfully', async () => {
      // Arrange
      const activityId = 'test-activity-123'
      const expectedCount = 5
      mockRedis.incr.mockResolvedValue(expectedCount)
      mockRedis.expire.mockResolvedValue(1)

      // Act
      const result = await redisParticipantService.incrementParticipantCount(activityId)

      // Assert
      expect(result).toBe(expectedCount)
      expect(mockRedis.incr).toHaveBeenCalledWith(`activity:${activityId}:participants:count`)
      expect(mockRedis.expire).toHaveBeenCalledWith(`activity:${activityId}:participants:count`, 300)
    })

    it('should handle Redis errors gracefully', async () => {
      // Arrange
      const activityId = 'test-activity-123'
      mockRedis.incr.mockRejectedValue(new Error('Redis connection failed'))

      // Act
      const result = await redisParticipantService.incrementParticipantCount(activityId)

      // Assert
      expect(result).toBe(1) // Database fallback value
      expect(mockRedis.incr).toHaveBeenCalled()
    })

    it('should track performance metrics', async () => {
      // Arrange
      const activityId = 'test-activity-123'
      mockRedis.incr.mockResolvedValue(3)
      mockRedis.expire.mockResolvedValue(1)

      // Act
      const startTime = performance.now()
      await redisParticipantService.incrementParticipantCount(activityId)
      const endTime = performance.now()

      // Assert
      expect(endTime - startTime).toBeLessThan(1000) // Should be fast
    })
  })

  describe('decrementParticipantCount', () => {
    it('should decrement participant count successfully', async () => {
      // Arrange
      const activityId = 'test-activity-123'
      const expectedCount = 3
      mockRedis.decr.mockResolvedValue(expectedCount)
      mockRedis.expire.mockResolvedValue(1)

      // Act
      const result = await redisParticipantService.decrementParticipantCount(activityId)

      // Assert
      expect(result).toBe(expectedCount)
      expect(mockRedis.decr).toHaveBeenCalledWith(`activity:${activityId}:participants:count`)
      expect(mockRedis.expire).toHaveBeenCalledWith(`activity:${activityId}:participants:count`, 300)
    })

    it('should prevent negative counts', async () => {
      // Arrange
      const activityId = 'test-activity-123'
      mockRedis.decr.mockResolvedValue(-1)
      mockRedis.set.mockResolvedValue('OK')
      mockRedis.expire.mockResolvedValue(1)

      // Act
      const result = await redisParticipantService.decrementParticipantCount(activityId)

      // Assert
      expect(result).toBe(0)
      expect(mockRedis.set).toHaveBeenCalledWith(`activity:${activityId}:participants:count`, 0)
    })

    it('should handle Redis errors gracefully', async () => {
      // Arrange
      const activityId = 'test-activity-123'
      mockRedis.decr.mockRejectedValue(new Error('Redis connection failed'))

      // Act
      const result = await redisParticipantService.decrementParticipantCount(activityId)

      // Assert
      expect(result).toBe(0) // Database fallback value
    })
  })

  describe('invalidateParticipantCount', () => {
    it('should invalidate cache successfully', async () => {
      // Arrange
      const activityId = 'test-activity-123'
      mockRedis.del.mockResolvedValue(1)

      // Act
      await redisParticipantService.invalidateParticipantCount(activityId)

      // Assert
      expect(mockRedis.del).toHaveBeenCalledWith(`activity:${activityId}:participants:count`)
    })

    it('should handle Redis errors gracefully', async () => {
      // Arrange
      const activityId = 'test-activity-123'
      mockRedis.del.mockRejectedValue(new Error('Redis connection failed'))

      // Act & Assert - Should not throw
      await expect(redisParticipantService.invalidateParticipantCount(activityId)).resolves.toBeUndefined()
    })
  })

  describe('checkRateLimit', () => {
    it('should allow actions within rate limit', async () => {
      // Arrange
      const userId = 'user-123'
      const action = 'join_activity'
      const limit = 10
      mockRedis.incr.mockResolvedValue(5)
      mockRedis.expire.mockResolvedValue(1)

      // Act
      const result = await redisParticipantService.checkRateLimit(userId, action, limit)

      // Assert
      expect(result).toBe(true)
      expect(mockRedis.incr).toHaveBeenCalledWith(`rate_limit:${userId}:${action}`)
    })

    it('should block actions exceeding rate limit', async () => {
      // Arrange
      const userId = 'user-123'
      const action = 'join_activity'
      const limit = 5
      mockRedis.incr.mockResolvedValue(6)

      // Act
      const result = await redisParticipantService.checkRateLimit(userId, action, limit)

      // Assert
      expect(result).toBe(false)
    })

    it('should set expiry for first request', async () => {
      // Arrange
      const userId = 'user-123'
      const action = 'join_activity'
      mockRedis.incr.mockResolvedValue(1)
      mockRedis.expire.mockResolvedValue(1)

      // Act
      await redisParticipantService.checkRateLimit(userId, action)

      // Assert
      expect(mockRedis.expire).toHaveBeenCalledWith(`rate_limit:${userId}:${action}`, 3600)
    })

    it('should allow on Redis errors', async () => {
      // Arrange
      const userId = 'user-123'
      const action = 'join_activity'
      mockRedis.incr.mockRejectedValue(new Error('Redis connection failed'))

      // Act
      const result = await redisParticipantService.checkRateLimit(userId, action)

      // Assert
      expect(result).toBe(true) // Allow on error
    })
  })

  describe('getPerformanceMetrics', () => {
    it('should return performance metrics successfully', async () => {
      // Arrange
      const operation = 'increment_participant'
      const mockMetrics = [
        JSON.stringify({ operation, responseTime: 50, cacheHit: true, timestamp: Date.now() }),
        JSON.stringify({ operation, responseTime: 75, cacheHit: false, timestamp: Date.now() })
      ]
      mockRedis.lrange.mockResolvedValue(mockMetrics)

      // Act
      const result = await redisParticipantService.getPerformanceMetrics(operation)

      // Assert
      expect(result).toHaveLength(2)
      expect(result[0]).toHaveProperty('operation', operation)
      expect(result[0]).toHaveProperty('responseTime', 50)
      expect(result[0]).toHaveProperty('cacheHit', true)
      expect(mockRedis.lrange).toHaveBeenCalledWith(`perf:${operation}`, 0, 99)
    })

    it('should return empty array on Redis errors', async () => {
      // Arrange
      const operation = 'increment_participant'
      mockRedis.lrange.mockRejectedValue(new Error('Redis connection failed'))

      // Act
      const result = await redisParticipantService.getPerformanceMetrics(operation)

      // Assert
      expect(result).toEqual([])
    })

    it('should handle malformed JSON gracefully', async () => {
      // Arrange
      const operation = 'increment_participant'
      const mockMetrics = ['invalid-json', '{"valid": "json"}']
      mockRedis.lrange.mockResolvedValue(mockMetrics)

      // Act & Assert - Should not throw
      await expect(redisParticipantService.getPerformanceMetrics(operation)).resolves.toEqual([])
    })
  })

  describe('Redis Client Initialization', () => {
    it('should handle Redis client initialization failure', async () => {
      // This test verifies that the service gracefully handles Redis unavailability
      // The actual implementation should fall back to database operations
      
      const activityId = 'test-activity-123'
      
      // Act - Should not throw even if Redis is unavailable
      const incrementResult = await redisParticipantService.incrementParticipantCount(activityId)
      const decrementResult = await redisParticipantService.decrementParticipantCount(activityId)
      
      // Assert - Should return fallback values
      expect(typeof incrementResult).toBe('number')
      expect(typeof decrementResult).toBe('number')
    })
  })

  describe('Cache Key Generation', () => {
    it('should generate consistent cache keys', async () => {
      // Arrange
      const activityId = 'test-activity-123'
      mockRedis.incr.mockResolvedValue(1)
      mockRedis.expire.mockResolvedValue(1)

      // Act
      await redisParticipantService.incrementParticipantCount(activityId)

      // Assert
      expect(mockRedis.incr).toHaveBeenCalledWith('activity:test-activity-123:participants:count')
    })

    it('should handle special characters in activity IDs', async () => {
      // Arrange
      const activityId = 'test-activity-with-special-chars-@#$%'
      mockRedis.incr.mockResolvedValue(1)
      mockRedis.expire.mockResolvedValue(1)

      // Act
      await redisParticipantService.incrementParticipantCount(activityId)

      // Assert
      expect(mockRedis.incr).toHaveBeenCalledWith('activity:test-activity-with-special-chars-@#$%:participants:count')
    })
  })
})

/**
 * Minimal Sziget Festival Data Population
 * 
 * This script populates essential Sziget Festival data with only core fields.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🎪 Populating Sziget Festival Data (Minimal)');
console.log('============================================');

// Enhanced Sziget Festival 2025 Data (from Google Sheets)
const szigetData = {
  // Festival metadata with contact information and practical details
  festivalMetadata: {
    contact_info: {
      organizers: [
        {
          name: "Festival Family Team",
          role: "Main Organizers",
          instagram: "@festivalfamily",
          facebook: "Festival Family Community",
          whatsapp_group: "Festival Family Sziget 2025",
          email: "<EMAIL>"
        }
      ],
      social_media: {
        instagram: "https://instagram.com/festivalfamily",
        facebook: "https://facebook.com/festivalfamilycommunity",
        whatsapp: "Festival Family WhatsApp Groups",
        website: "https://festivalfamily.com"
      },
      emergency_contacts: {
        festival_family_coordinator: "+31 6 12345678",
        sziget_info: "+36 1 372 0013",
        emergency_services: "112"
      }
    },
    budapest_info: {
      transportation: {
        airport_to_city: "Airport Shuttle (100E bus) - 900 HUF, Metro M3 to city center",
        city_to_sziget: "HÉV suburban train to Filatorigát + shuttle bus, or Boat from Vigadó tér",
        public_transport: "BKK day passes available - 1650 HUF/day, 4150 HUF/3 days",
        taxi_apps: "Bolt, Uber, FőTaxi available in Budapest",
        bike_rental: "MOL Bubi bike sharing system throughout the city"
      },
      accommodation: {
        budget: "Hostels in city center 15-30 EUR/night, Camping on Sziget Island",
        mid_range: "Hotels and Airbnb 40-80 EUR/night in Pest side",
        luxury: "Thermal hotels and luxury accommodations 100+ EUR/night",
        festival_camping: "Basic camping on Sziget Island included with festival pass"
      },
      food_and_drink: {
        hungarian_specialties: "Goulash, Lángos, Chimney cake (Kürtőskalács), Fisherman's soup",
        budget_eating: "Street food markets, local 'étterem' restaurants",
        currency: "Hungarian Forint (HUF) - 1 EUR ≈ 380 HUF",
        tipping: "10-15% in restaurants, round up for taxis and bars"
      },
      attractions: {
        must_see: "Parliament Building, Széchenyi Thermal Baths, Fisherman's Bastion, Chain Bridge",
        thermal_baths: "Széchenyi, Gellért, Rudas - perfect for post-festival recovery",
        nightlife: "Ruin pubs in Jewish Quarter (Szimpla Kert, Instant), Danube boat parties",
        day_trips: "Danube Bend (Szentendre, Visegrád), Lake Balaton (1.5h by train)"
      }
    },
    festival_tips: {
      packing_essentials: [
        "Comfortable walking shoes and flip-flops",
        "Sunscreen and hat for daytime",
        "Warm clothes for evening (can get cool by the Danube)",
        "Portable phone charger/power bank",
        "Reusable water bottle (free water stations available)",
        "Cash in HUF for vendors (many don't accept cards)",
        "Wet wipes and hand sanitizer",
        "Earplugs for camping areas"
      ],
      money_saving_tips: [
        "Buy festival food vouchers in advance for discounts",
        "Bring your own alcohol to camping areas (allowed)",
        "Use public transport day passes instead of taxis",
        "Eat one meal per day outside the festival for budget savings",
        "Share accommodation costs with Festival Family members"
      ],
      festival_navigation: [
        "Download the official Sziget app for stage times and maps",
        "Meet at the Festival Family base camp daily at 2 PM",
        "Use the Ferris wheel as a landmark for orientation",
        "Plan your day around main stage acts but explore smaller stages",
        "Check weather forecast and plan indoor activities for rain"
      ],
      health_and_safety: [
        "Stay hydrated - free water stations throughout the festival",
        "Use sunscreen during day events (strong summer sun)",
        "Pace yourself - it's a week-long festival",
        "Keep emergency contacts saved in your phone",
        "Know the location of medical tents and first aid stations",
        "Travel in groups, especially late at night"
      ]
    }
  },
  events: [
    {
      title: 'Festival Family Pre-Meet',
      description: 'Get to know your Festival Family before the main event! Connect with fellow festival-goers and plan your Sziget adventure together.',
      start_date: '2025-03-21T12:00:00Z',
      end_date: '2025-03-24T18:00:00Z',
      location: 'Location TBD'
    },
    {
      title: 'Early Move-In Setup',
      description: 'Help set up the Festival Family base camp and get early access to the festival grounds.',
      start_date: '2025-08-04T10:00:00Z',
      end_date: '2025-08-04T18:00:00Z',
      location: 'Sziget Festival Grounds'
    },
    {
      title: 'Festival Family Reunion',
      description: 'Annual reunion for all Festival Family members to reconnect and plan future adventures.',
      start_date: '2025-10-10T14:00:00Z',
      end_date: '2025-10-13T18:00:00Z',
      location: 'Nijmegen, Netherlands'
    }
  ],
  
  activities: [
    {
      title: 'Fam Hangout Opening',
      description: 'Official opening of the Festival Family hangout space at base camp. Meet your festival family and get oriented!',
      type: 'meetup',
      start_date: '2025-08-04T19:30:00Z',
      end_date: '2025-08-04T20:30:00Z',
      location: 'Sziget Festival Base Camp'
    },
    {
      title: 'Festival Family Dinner',
      description: 'Special dinner for Festival Family members at a location near Sziget Festival. Great opportunity to bond with your festival tribe!',
      type: 'meetup',
      start_date: '2025-08-05T18:00:00Z',
      end_date: '2025-08-05T23:00:00Z',
      location: 'Restaurant near Sziget Festival'
    },
    {
      title: 'Sziget Festival Scavenger Hunt',
      description: 'Explore the festival grounds with your Festival Family through an exciting scavenger hunt. Prizes for winners!',
      type: 'other',
      start_date: '2025-08-06T09:00:00Z',
      end_date: '2025-08-06T16:00:00Z',
      location: 'Sziget Festival Grounds'
    },
    {
      title: 'Daily Fam Meet + Group Photo',
      description: 'Daily gathering point for Festival Family members. Connect, share experiences, and take group photos.',
      type: 'meetup',
      start_date: '2025-08-06T14:00:00Z',
      end_date: '2025-08-06T16:00:00Z',
      location: 'Sziget Festival Base Camp'
    },
    {
      title: 'Fam Teams Contest Day 1',
      description: 'Multi-day team competition for Festival Family members. Form teams and compete in various challenges!',
      type: 'other',
      start_date: '2025-08-06T16:00:00Z',
      end_date: '2025-08-06T18:00:00Z',
      location: 'Sziget Festival Base Camp'
    },
    {
      title: 'Fam Beerpong Tournament',
      description: 'Epic beer pong tournament at the Festival Family base camp. Bring your A-game and your festival spirit!',
      type: 'other',
      start_date: '2025-08-07T13:00:00Z',
      end_date: '2025-08-07T18:00:00Z',
      location: 'Basic Base Camp'
    },
    {
      title: 'Group Photos at Art Village',
      description: 'Professional group photos at the iconic Art Village entrance. Perfect for your festival memories!',
      type: 'other',
      start_date: '2025-08-07T16:00:00Z',
      end_date: '2025-08-07T17:00:00Z',
      location: 'Art Village Entrance'
    },
    {
      title: 'Fam Karaoke Night',
      description: 'Sing your heart out with your Festival Family! Karaoke night at the base camp hangout.',
      type: 'performance',
      start_date: '2025-08-08T20:00:00Z',
      end_date: '2025-08-08T23:00:00Z',
      location: 'Fam Hangout at Basic Base Camp'
    },
    {
      title: 'Beach Group Photo Session',
      description: 'Capture memories with your Festival Family at the beautiful Sziget beach area.',
      type: 'other',
      start_date: '2025-08-08T13:00:00Z',
      end_date: '2025-08-08T14:00:00Z',
      location: 'Sziget Festival Beach'
    },
    {
      title: 'Sziget Sign Group Photo',
      description: 'Iconic group photo at the famous Sziget letters sign. A must-have for your festival album!',
      type: 'other',
      start_date: '2025-08-10T16:00:00Z',
      end_date: '2025-08-10T17:00:00Z',
      location: 'Sziget Sign/Letters'
    },
    {
      title: 'Fam Outfit Day',
      description: 'Special themed outfit day! Bring and wear your Festival Family outfit for group photos.',
      type: 'other',
      start_date: '2025-08-11T09:00:00Z',
      end_date: '2025-08-11T23:00:00Z',
      location: 'Sziget Festival Grounds'
    },
    {
      title: 'Fam Goodbye Meet',
      description: 'Final gathering to say goodbye and exchange contact information. Last chance to hand over camping gear!',
      type: 'meetup',
      start_date: '2025-08-11T11:00:00Z',
      end_date: '2025-08-11T11:45:00Z',
      location: 'Sziget Festival Base Camp'
    },
    {
      title: 'Post-Festival Dinner',
      description: 'Dinner in Budapest for those staying an extra day. Continue the festival spirit in the city!',
      type: 'meetup',
      start_date: '2025-08-12T17:00:00Z',
      end_date: '2025-08-12T20:00:00Z',
      location: 'Budapest, Hungary'
    },
    {
      title: 'Closing Meetup & Toast',
      description: 'Final celebration and toast to another amazing Sziget experience with your Festival Family.',
      type: 'meetup',
      start_date: '2025-08-12T20:00:00Z',
      end_date: '2025-08-12T23:00:00Z',
      location: 'Budapest, Hungary'
    },
    {
      title: 'Fam Recovery Bath Visit',
      description: 'Relax and recover from the festival at one of Budapest\'s famous thermal baths.',
      type: 'other',
      start_date: '2025-08-13T14:00:00Z',
      end_date: '2025-08-13T18:00:00Z',
      location: 'Budapest Thermal Baths'
    }
  ]
};

// Population functions
async function updateFestivalWithMetadata(festivalId) {
  console.log('📋 Updating festival with enhanced metadata...');

  const { data, error } = await supabase
    .from('festivals')
    .update({
      metadata: szigetData.festivalMetadata
    })
    .eq('id', festivalId)
    .select();

  if (error) {
    console.error('❌ Error updating festival metadata:', error);
    throw error;
  }

  console.log('✅ Festival metadata updated with contact info, Budapest info, and tips');
  return data;
}

async function createInformationalActivities(festivalId) {
  console.log('📚 Creating informational activities...');

  const infoActivities = [
    {
      title: 'Festival Family Contact Information',
      description: 'Essential contact information for Festival Family organizers, emergency contacts, and social media links.',
      type: 'other',
      start_date: '2025-08-04T00:00:00Z',
      end_date: '2025-08-12T23:59:00Z',
      location: 'Information Available 24/7',
      festival_id: festivalId,
      metadata: {
        type: 'contact_info',
        data: szigetData.festivalMetadata.contact_info
      }
    },
    {
      title: 'Budapest Transportation Guide',
      description: 'Complete guide to getting around Budapest and to Sziget Festival, including public transport, taxis, and bike rentals.',
      type: 'other',
      start_date: '2025-08-04T00:00:00Z',
      end_date: '2025-08-12T23:59:00Z',
      location: 'Budapest, Hungary',
      festival_id: festivalId,
      metadata: {
        type: 'transportation_guide',
        data: szigetData.festivalMetadata.budapest_info.transportation
      }
    },
    {
      title: 'Budapest Accommodation & Food Guide',
      description: 'Recommendations for places to stay and eat in Budapest, from budget hostels to luxury hotels, plus Hungarian food specialties.',
      type: 'other',
      start_date: '2025-08-04T00:00:00Z',
      end_date: '2025-08-12T23:59:00Z',
      location: 'Budapest, Hungary',
      festival_id: festivalId,
      metadata: {
        type: 'accommodation_food_guide',
        data: {
          accommodation: szigetData.festivalMetadata.budapest_info.accommodation,
          food_and_drink: szigetData.festivalMetadata.budapest_info.food_and_drink
        }
      }
    },
    {
      title: 'Budapest Attractions & Nightlife',
      description: 'Must-see attractions in Budapest including thermal baths, historic sites, ruin pubs, and day trip options.',
      type: 'other',
      start_date: '2025-08-04T00:00:00Z',
      end_date: '2025-08-12T23:59:00Z',
      location: 'Budapest, Hungary',
      festival_id: festivalId,
      metadata: {
        type: 'attractions_guide',
        data: szigetData.festivalMetadata.budapest_info.attractions
      }
    },
    {
      title: 'Festival Packing Essentials',
      description: 'Complete packing checklist for Sziget Festival including clothing, electronics, and camping gear recommendations.',
      type: 'other',
      start_date: '2025-08-04T00:00:00Z',
      end_date: '2025-08-12T23:59:00Z',
      location: 'Preparation Information',
      festival_id: festivalId,
      metadata: {
        type: 'packing_guide',
        data: szigetData.festivalMetadata.festival_tips.packing_essentials
      }
    },
    {
      title: 'Money-Saving Tips for Sziget',
      description: 'Smart tips to save money during your Sziget experience, from food vouchers to accommodation sharing.',
      type: 'other',
      start_date: '2025-08-04T00:00:00Z',
      end_date: '2025-08-12T23:59:00Z',
      location: 'Budget Planning Information',
      festival_id: festivalId,
      metadata: {
        type: 'money_saving_tips',
        data: szigetData.festivalMetadata.festival_tips.money_saving_tips
      }
    },
    {
      title: 'Festival Navigation & Planning',
      description: 'Tips for navigating Sziget Festival, using the app, finding stages, and making the most of your week.',
      type: 'other',
      start_date: '2025-08-04T00:00:00Z',
      end_date: '2025-08-12T23:59:00Z',
      location: 'Sziget Festival Grounds',
      festival_id: festivalId,
      metadata: {
        type: 'navigation_guide',
        data: szigetData.festivalMetadata.festival_tips.festival_navigation
      }
    },
    {
      title: 'Health & Safety Guidelines',
      description: 'Important health and safety information for Sziget Festival including hydration, sun protection, and emergency procedures.',
      type: 'other',
      start_date: '2025-08-04T00:00:00Z',
      end_date: '2025-08-12T23:59:00Z',
      location: 'Safety Information',
      festival_id: festivalId,
      metadata: {
        type: 'health_safety_guide',
        data: szigetData.festivalMetadata.festival_tips.health_and_safety
      }
    }
  ];

  const { data, error } = await supabase
    .from('activities')
    .insert(infoActivities)
    .select();

  if (error) {
    console.error('❌ Error creating informational activities:', error);
    throw error;
  }

  console.log(`✅ Created ${data.length} informational activities`);
  return data;
}

async function createEvents(festivalId) {
  console.log('📅 Creating events...');
  
  const eventsWithFestivalId = szigetData.events.map(event => ({
    ...event,
    festival_id: festivalId
  }));
  
  const { data, error } = await supabase
    .from('events')
    .insert(eventsWithFestivalId)
    .select();
  
  if (error) {
    console.error('❌ Error creating events:', error);
    throw error;
  }
  
  console.log(`✅ Created ${data.length} events`);
  return data;
}

async function createActivities(festivalId) {
  console.log('🎯 Creating activities...');
  
  const activitiesWithFestivalId = szigetData.activities.map(activity => ({
    ...activity,
    festival_id: festivalId
  }));
  
  const { data, error } = await supabase
    .from('activities')
    .insert(activitiesWithFestivalId)
    .select();
  
  if (error) {
    console.error('❌ Error creating activities:', error);
    throw error;
  }
  
  console.log(`✅ Created ${data.length} activities`);
  return data;
}

// Main execution
async function main() {
  try {
    console.log(`🕐 Started at: ${new Date().toISOString()}\n`);
    
    // Get existing Sziget Festival
    const { data: existingFestival } = await supabase
      .from('festivals')
      .select('*')
      .eq('name', 'Sziget Festival 2025')
      .single();
    
    if (!existingFestival) {
      console.log('❌ Sziget Festival 2025 not found. Please create the festival first.');
      return;
    }
    
    console.log('✅ Found Sziget Festival 2025');
    console.log('🎪 Festival ID:', existingFestival.id);
    
    // Update festival with enhanced metadata
    await updateFestivalWithMetadata(existingFestival.id);

    // Create events and activities
    const events = await createEvents(existingFestival.id);
    const activities = await createActivities(existingFestival.id);
    const infoActivities = await createInformationalActivities(existingFestival.id);

    // Save summary
    const summary = {
      festival: existingFestival,
      events: events,
      activities: activities,
      infoActivities: infoActivities,
      summary: {
        festivalId: existingFestival.id,
        eventsCreated: events.length,
        activitiesCreated: activities.length,
        infoActivitiesCreated: infoActivities.length,
        totalItems: events.length + activities.length + infoActivities.length,
        enhancedDataIncluded: true
      },
      timestamp: new Date().toISOString()
    };
    
    const resultsDir = 'sziget-data-population-results';
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      `${resultsDir}/sziget-minimal-population-${Date.now()}.json`,
      JSON.stringify(summary, null, 2)
    );
    
    console.log('\n🎉 ENHANCED SZIGET FESTIVAL DATA POPULATION COMPLETE');
    console.log('===================================================');
    console.log(`🎪 Festival: ${existingFestival.name}`);
    console.log(`📅 Events: ${events.length} created`);
    console.log(`🎯 Activities: ${activities.length} created`);
    console.log(`📚 Info Activities: ${infoActivities.length} created`);
    console.log(`📊 Total Items: ${summary.summary.totalItems}`);
    console.log(`🆔 Festival ID: ${existingFestival.id}`);
    console.log('\n✨ ENHANCED DATA INCLUDED:');
    console.log('📞 Contact Information: Festival Family organizers, social media, emergency contacts');
    console.log('🚇 Budapest Transportation: Airport, public transport, taxis, bike rentals');
    console.log('🏨 Accommodation & Food: Budget to luxury options, Hungarian specialties');
    console.log('🏛️ Attractions & Nightlife: Must-see sights, thermal baths, ruin pubs');
    console.log('🎒 Festival Tips: Packing essentials, money-saving tips, navigation, safety');
    console.log(`📁 Results saved to: ${resultsDir}/sziget-enhanced-population-${Date.now()}.json`);
    
  } catch (error) {
    console.error('💥 Population failed:', error);
  }
  
  process.exit(0);
}

main();

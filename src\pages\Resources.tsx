import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Search, Lightbulb, BookOpen, HelpCircle, Star } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/lib/supabase';
import TipDetailsModal from '@/components/tips/TipDetailsModal';
import GuideDetailsModal from '@/components/guides/GuideDetailsModal';
import FAQDetailsModal from '@/components/faqs/FAQDetailsModal';
import { isMobileViewport } from '../utils/mobileUX';
import { UnifiedCard, FlexLayout, UnifiedButton, BentoCard, BentoGrid, UnifiedBadge } from '@/components/design-system';
// Removed legacy useColorManagement hook - using direct UnifiedBadge variants

interface Tip {
  id: string;
  title: string;
  content: string;
  description?: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  created_at: string;
}

interface Guide {
  id: string;
  title: string;
  content: string;
  description?: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  estimated_read_time?: number;
  created_at: string;
}

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  created_at: string;
}

// Database-driven tip category badge component
const TipCategoryBadge: React.FC<{ category: string | null }> = ({ category }) => {
  if (!category) {
    return <UnifiedBadge variant="secondary" size="sm">N/A</UnifiedBadge>;
  }

  return (
    <UnifiedBadge
      variant="category"
      size="sm"
      className="w-fit"
    >
      {category}
    </UnifiedBadge>
  );
};

const Resources: React.FC = () => {
  const [tips, setTips] = useState<Tip[]>([]);
  const [guides, setGuides] = useState<Guide[]>([]);
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [activeTab, setActiveTab] = useState('tips');

  // Mobile detection
  const [isMobile, setIsMobile] = useState(isMobileViewport());

  // Modal states
  const [selectedTip, setSelectedTip] = useState<Tip | null>(null);
  const [selectedGuide, setSelectedGuide] = useState<Guide | null>(null);
  const [selectedFaq, setSelectedFaq] = useState<FAQ | null>(null);

  useEffect(() => {
    fetchContent();
  }, []);

  // Mobile detection effect
  useEffect(() => {
    const handleResize = () => setIsMobile(isMobileViewport());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const fetchContent = async () => {
    try {
      setLoading(true);

      // Fetch published tips
      const { data: tipsData, error: tipsError } = await supabase
        .from('tips')
        .select('*')
        .eq('status', 'published')
        .order('is_featured', { ascending: false })
        .order('created_at', { ascending: false });

      if (tipsError) throw tipsError;

      // Fetch published guides
      const { data: guidesData, error: guidesError } = await supabase
        .from('guides')
        .select('*')
        .eq('status', 'published')
        .order('is_featured', { ascending: false })
        .order('created_at', { ascending: false });

      if (guidesError) throw guidesError;

      // Fetch published FAQs
      const { data: faqsData, error: faqsError } = await supabase
        .from('faqs')
        .select('*')
        .eq('status', 'published')
        .order('is_featured', { ascending: false })
        .order('created_at', { ascending: false });

      if (faqsError) throw faqsError;

      setTips((tipsData || []).map(tip => ({
        ...tip,
        description: tip.description || undefined,
        category: tip.category || undefined,
        tags: tip.tags || undefined,
        is_featured: tip.is_featured || undefined,
        view_count: tip.view_count || undefined,
        helpful_count: tip.helpful_count || undefined,
        created_at: tip.created_at || new Date().toISOString()
      })));
      setGuides((guidesData || []).map(guide => ({
        ...guide,
        description: guide.description || undefined,
        category: guide.category || undefined,
        tags: guide.tags || undefined,
        is_featured: guide.is_featured || undefined,
        view_count: guide.view_count || undefined,
        helpful_count: guide.helpful_count || undefined,
        estimated_read_time: guide.estimated_read_time || undefined,
        created_at: guide.created_at || new Date().toISOString()
      })));
      setFaqs((faqsData || []).map(faq => ({
        ...faq,
        category: faq.category || undefined,
        tags: faq.tags || undefined,
        is_featured: faq.is_featured || undefined,
        view_count: faq.view_count || undefined,
        helpful_count: faq.helpful_count || undefined,
        created_at: faq.created_at || new Date().toISOString()
      })));
    } catch (error) {
      console.error('Error fetching content:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterContent = (items: any[], type: string) => {
    return items.filter(item => {
      const matchesSearch = searchQuery === '' || 
        item.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.content?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.question?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.answer?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.tags?.some((tag: string) => tag.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesCategory = selectedCategory === 'all' || 
        selectedCategory === 'featured' && item.is_featured ||
        item.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  };

  const getCategories = (type: string) => {
    switch (type) {
      case 'tips':
        return ['SURVIVAL', 'SOCIAL', 'BUDGET', 'COMFORT', 'EXPERIENCE', 'SAFETY'];
      case 'guides':
        return ['SAFETY', 'PACKING', 'CAMPING', 'FOOD', 'TRANSPORT', 'PLANNING', 'SOCIAL'];
      case 'faqs':
        return ['GENERAL', 'ACCOUNT', 'ACTIVITIES', 'SAFETY', 'TECHNICAL', 'BILLING', 'PRIVACY'];
      default:
        return [];
    }
  };

  // Mobile-first content optimization helper
  const getOptimizedDescription = (title: string, description: string | null, category: string | null): string => {
    if (description && description.length > 0) {
      // Optimize existing descriptions for mobile
      return description.length > 80 ? `${description.substring(0, 77)}...` : description;
    }

    // Generate mobile-friendly fallback descriptions based on category and title
    const categoryDescriptions: Record<string, string> = {
      'SURVIVAL': 'Essential tips to stay safe and comfortable',
      'SOCIAL': 'Connect with fellow festival-goers',
      'BUDGET': 'Save money while maximizing fun',
      'COMFORT': 'Pack smart for maximum comfort',
      'EXPERIENCE': 'Enhance your festival adventure',
      'SAFETY': 'Stay safe and enjoy responsibly'
    };

    return category ? categoryDescriptions[category] || 'Helpful festival advice' : 'Helpful festival advice';
  };

  // Enhanced 2025 Visual Excellence Color Pattern - Stronger, More Vibrant
  const getResourceColorTheme = (index: number) => {
    const colorThemes = [
      'bg-gradient-to-br from-blue-500/20 to-blue-600/25 border-blue-500/30 shadow-lg shadow-blue-500/10',
      'bg-gradient-to-br from-emerald-500/20 to-emerald-600/25 border-emerald-500/30 shadow-lg shadow-emerald-500/10',
      'bg-gradient-to-br from-violet-500/20 to-violet-600/25 border-violet-500/30 shadow-lg shadow-violet-500/10',
      'bg-gradient-to-br from-orange-500/20 to-orange-600/25 border-orange-500/30 shadow-lg shadow-orange-500/10',
      'bg-gradient-to-br from-rose-500/20 to-rose-600/25 border-rose-500/30 shadow-lg shadow-rose-500/10',
      'bg-gradient-to-br from-amber-500/20 to-amber-600/25 border-amber-500/30 shadow-lg shadow-amber-500/10',
      'bg-gradient-to-br from-fuchsia-500/20 to-fuchsia-600/25 border-fuchsia-500/30 shadow-lg shadow-fuchsia-500/10',
      'bg-gradient-to-br from-cyan-500/20 to-cyan-600/25 border-cyan-500/30 shadow-lg shadow-cyan-500/10'
    ];
    return colorThemes[index % colorThemes.length];
  };

  const renderTipCard = (tip: Tip, index: number) => (
    <BentoCard
      key={tip.id}
      title={tip.title}
      variant="glassmorphism"
      className={`${getResourceColorTheme(index)} cursor-pointer`}
      onClick={() => setSelectedTip(tip)}
    >
      <FlexLayout direction="col" gap="md" className="h-full">
        {/* Header with Icon and Title */}
        <FlexLayout align="center" justify="between">
          <FlexLayout align="center" gap="sm">
            <div className="p-2 rounded-lg bg-accent/20">
              <Lightbulb className="w-5 h-5 text-accent" />
            </div>
            <h3 className="font-semibold text-primary-light text-lg leading-tight line-clamp-2">
              {tip.title}
            </h3>
          </FlexLayout>
          {tip.is_featured && (
            <UnifiedBadge variant="category" size="sm">
              <Star className="w-3 h-3 mr-1" />
              Featured
            </UnifiedBadge>
          )}
        </FlexLayout>

        {/* Category Badge */}
        <TipCategoryBadge category={tip.category || null} />

        {/* Optimized Description - Always Present for Consistent Hierarchy */}
        <p className="text-secondary-light text-sm leading-relaxed flex-1">
          {getOptimizedDescription(tip.title, tip.description || null, tip.category || null)}
        </p>

        {/* Stats Footer */}
        <FlexLayout align="center" justify="between" className="text-xs text-muted-light pt-2 border-t border-border/30">
          <span>{tip.view_count || 0} views</span>
          <span>{tip.helpful_count || 0} helpful</span>
        </FlexLayout>
      </FlexLayout>
    </BentoCard>
  );

  // Guide-specific content optimization
  const getOptimizedGuideDescription = (title: string, description: string | null, category: string | null): string => {
    if (description && description.length > 0) {
      // Optimize existing descriptions for mobile
      return description.length > 80 ? `${description.substring(0, 77)}...` : description;
    }

    // Generate mobile-friendly fallback descriptions for guides
    const guideDescriptions: Record<string, string> = {
      'PACKING': 'Complete checklist for festival essentials',
      'SAFETY': 'Stay safe and secure at festivals',
      'FOOD': 'Navigate festival dining options',
      'TRANSPORT': 'Get to and from festivals easily',
      'CAMPING': 'Master festival camping basics',
      'GENERAL': 'Comprehensive festival guidance'
    };

    return category ? guideDescriptions[category] || 'Step-by-step festival guide' : 'Step-by-step festival guide';
  };

  const renderGuideCard = (guide: Guide, index: number) => (
    <BentoCard
      key={guide.id}
      title={guide.title}
      variant="glassmorphism"
      className={`${getResourceColorTheme(index)} cursor-pointer`}
      onClick={() => setSelectedGuide(guide)}
    >
      <FlexLayout direction="col" gap="md" className="h-full">
        {/* Header with Icon and Title */}
        <FlexLayout align="center" justify="between">
          <FlexLayout align="center" gap="sm">
            <div className="p-2 rounded-lg bg-primary/20">
              <BookOpen className="w-5 h-5 text-primary" />
            </div>
            <h3 className="font-semibold text-primary-light text-lg leading-tight line-clamp-2">
              {guide.title}
            </h3>
          </FlexLayout>
          {guide.is_featured && (
            <UnifiedBadge variant="category" size="sm">
              <Star className="w-3 h-3 mr-1" />
              Featured
            </UnifiedBadge>
          )}
        </FlexLayout>

        {/* Badges Row */}
        <FlexLayout align="center" gap="sm" wrap>
          {guide.category && (
            <UnifiedBadge variant="secondary" size="sm">
              {guide.category.replace('_', ' ')}
            </UnifiedBadge>
          )}
          {guide.estimated_read_time && (
            <UnifiedBadge variant="secondary" size="sm">
              {guide.estimated_read_time} min read
            </UnifiedBadge>
          )}
        </FlexLayout>

        {/* Optimized Description - Always Present for Consistent Hierarchy */}
        <p className="text-secondary-light text-sm leading-relaxed flex-1">
          {getOptimizedGuideDescription(guide.title, guide.description || null, guide.category || null)}
        </p>

        {/* Stats Footer */}
        <FlexLayout align="center" justify="between" className="text-xs text-muted-light pt-2 border-t border-border/30">
          <span>{guide.view_count || 0} views</span>
          <span>{guide.helpful_count || 0} helpful</span>
        </FlexLayout>
      </FlexLayout>
    </BentoCard>
  );

  // FAQ answer optimization for mobile
  const getOptimizedAnswer = (answer: string): string => {
    if (!answer) return 'Tap to read the full answer';

    // For mobile, show a concise preview that encourages engagement
    if (answer.length > 100) {
      // Find the first sentence or logical break point
      const firstSentence = answer.split('.')[0];
      if (firstSentence.length > 80) {
        return `${answer.substring(0, 77)}...`;
      }
      return `${firstSentence}.`;
    }

    return answer;
  };

  const renderFaqCard = (faq: FAQ, index: number) => (
    <BentoCard
      key={faq.id}
      title={faq.question}
      variant="glassmorphism"
      className={`${getResourceColorTheme(index)} cursor-pointer`}
      onClick={() => setSelectedFaq(faq)}
    >
      <FlexLayout direction="col" gap="md" className="h-full">
        {/* Header with Icon and Question */}
        <FlexLayout align="center" justify="between">
          <FlexLayout align="center" gap="sm">
            <div className="p-2 rounded-lg bg-festival-success/20">
              <HelpCircle className="w-5 h-5 text-festival-success" />
            </div>
            <h3 className="font-semibold text-primary-light text-lg leading-tight line-clamp-2">
              {faq.question}
            </h3>
          </FlexLayout>
          {faq.is_featured && (
            <UnifiedBadge variant="category" size="sm">
              <Star className="w-3 h-3 mr-1" />
              Featured
            </UnifiedBadge>
          )}
        </FlexLayout>

        {/* Category Badge */}
        {faq.category && (
          <UnifiedBadge variant="secondary" size="sm" className="w-fit">
            {faq.category.replace('_', ' ')}
          </UnifiedBadge>
        )}

        {/* Optimized Answer Preview */}
        <p className="text-secondary-light text-sm leading-relaxed flex-1">
          {getOptimizedAnswer(faq.answer)}
        </p>

        {/* Stats Footer */}
        <FlexLayout align="center" justify="between" className="text-xs text-muted-light pt-2 border-t border-border/30">
          <span>{faq.view_count || 0} views</span>
          <span>{faq.helpful_count || 0} helpful</span>
        </FlexLayout>
      </FlexLayout>
    </BentoCard>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-foreground text-lg">Loading resources...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold text-foreground mb-4">Festival Resources</h1>
          <p className="text-muted-foreground text-lg">Tips, guides, and answers to help you make the most of your festival experience</p>
        </motion.div>

        {/* Search and Filter Section - Standardized Pattern */}
        <UnifiedCard variant="elevated" className="p-6 mb-6">
          <FlexLayout direction="col" gap="md">
            {/* Search Bar with Unified Design */}
            <FlexLayout align="center" gap="sm">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-light" />
                <Input
                  type="text"
                  placeholder={isMobile ? "Search resources..." : "Search festival resources..."}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-card border-neutral-200 text-primary-light placeholder:text-muted-light focus:border-festival-orange rounded-lg"
                  style={{ fontSize: '16px' }} // Prevents zoom on iOS
                />
              </div>

              {/* Desktop: Side-by-side filter controls */}
              {!isMobile && (
                <FlexLayout align="center" gap="sm">
                  <UnifiedButton
                    variant={selectedCategory === 'all' ? 'primary' : 'outline'}
                    size="md"
                    onClick={() => setSelectedCategory('all')}
                  >
                    All
                  </UnifiedButton>
                  <UnifiedButton
                    variant={selectedCategory === 'featured' ? 'primary' : 'outline'}
                    size="md"
                    onClick={() => setSelectedCategory('featured')}
                  >
                    <Star className="w-4 h-4 mr-2" />
                    Featured
                  </UnifiedButton>
                </FlexLayout>
              )}
            </FlexLayout>

            {/* Mobile: Filter controls below search */}
            {isMobile && (
              <FlexLayout align="center" gap="sm" wrap>
                <UnifiedButton
                  variant={selectedCategory === 'all' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory('all')}
                >
                  All
                </UnifiedButton>
                <UnifiedButton
                  variant={selectedCategory === 'featured' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory('featured')}
                >
                  <Star className="w-4 h-4 mr-2" />
                  Featured
                </UnifiedButton>
              </FlexLayout>
            )}
          </FlexLayout>
        </UnifiedCard>

        {/* Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="tips">Tips ({tips.length})</TabsTrigger>
            <TabsTrigger value="guides">Guides ({guides.length})</TabsTrigger>
            <TabsTrigger value="faqs">FAQs ({faqs.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="tips" className="mt-6">
            <BentoGrid cols={2} gap="sm">
              {filterContent(tips, 'tips').map((tip, index) => renderTipCard(tip, index))}
            </BentoGrid>
            {filterContent(tips, 'tips').length === 0 && (
              <div className="text-center py-12 text-muted-foreground">
                No tips found matching your criteria.
              </div>
            )}
          </TabsContent>

          <TabsContent value="guides" className="mt-6">
            <BentoGrid cols={2} gap="sm">
              {filterContent(guides, 'guides').map((guide, index) => renderGuideCard(guide, index))}
            </BentoGrid>
            {filterContent(guides, 'guides').length === 0 && (
              <div className="text-center py-12 text-muted-foreground">
                No guides found matching your criteria.
              </div>
            )}
          </TabsContent>

          <TabsContent value="faqs" className="mt-6">
            <BentoGrid cols={2} gap="sm">
              {filterContent(faqs, 'faqs').map((faq, index) => renderFaqCard(faq, index))}
            </BentoGrid>
            {filterContent(faqs, 'faqs').length === 0 && (
              <div className="text-center py-12 text-muted-foreground">
                No FAQs found matching your criteria.
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Modals */}
      <TipDetailsModal
        tip={selectedTip}
        isOpen={!!selectedTip}
        onClose={() => setSelectedTip(null)}
      />
      <GuideDetailsModal
        guide={selectedGuide}
        isOpen={!!selectedGuide}
        onClose={() => setSelectedGuide(null)}
      />
      <FAQDetailsModal
        faq={selectedFaq}
        isOpen={!!selectedFaq}
        onClose={() => setSelectedFaq(null)}
      />
    </div>
  );
};

export default Resources;

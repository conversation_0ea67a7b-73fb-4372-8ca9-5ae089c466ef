/**
 * Database Debug Script - Investigate <PERSON><PERSON><PERSON> and Admin User
 * 
 * This script will help us understand:
 * 1. What the actual profiles table schema looks like
 * 2. Whether the admin user exists
 * 3. What data is actually in the database
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please check your .env file for VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('🔍 Festival Family Database Investigation');
console.log('==========================================');
console.log(`📍 Supabase URL: ${supabaseUrl}`);
console.log(`🔑 Using anon key: ${supabaseAnonKey.substring(0, 20)}...`);
console.log('');

async function investigateDatabase() {
  try {
    // Test 1: Basic connection test
    console.log('🧪 Test 1: Basic Connection Test');
    console.log('--------------------------------');

    const { data: connectionTest, error: connectionError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);

    if (connectionError) {
      console.error('❌ Connection failed:', connectionError.message);
      console.error('Full error:', connectionError);
      return;
    }

    console.log('✅ Connection successful');
    console.log('');

    // Test 1.5: Test with authentication
    console.log('🧪 Test 1.5: Authentication Test');
    console.log('---------------------------------');

    try {
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'testpassword123'
      });

      if (authError) {
        console.error('❌ Authentication failed:', authError.message);
      } else {
        console.log('✅ Authentication successful');
        console.log('👤 User ID:', authData.user?.id);
        console.log('📧 Email:', authData.user?.email);

        // Now test profile fetching with authentication
        console.log('');
        console.log('🧪 Test 1.6: Authenticated Profile Fetch');
        console.log('----------------------------------------');

        const { data: authProfile, error: authProfileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', authData.user.id)
          .single();

        if (authProfileError) {
          console.error('❌ Authenticated profile fetch failed:', authProfileError.message);
          console.error('Error code:', authProfileError.code);
        } else {
          console.log('✅ Authenticated profile fetch successful:');
          console.log(JSON.stringify(authProfile, null, 2));
        }
      }
    } catch (authException) {
      console.error('❌ Authentication exception:', authException.message);
    }
    console.log('');

    // Test 2: Check profiles table structure
    console.log('🧪 Test 2: Profiles Table Structure');
    console.log('-----------------------------------');
    
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(5);
    
    if (profilesError) {
      console.error('❌ Profiles query failed:', profilesError.message);
      console.error('Full error:', profilesError);
    } else {
      console.log(`✅ Found ${profiles.length} profiles`);
      if (profiles.length > 0) {
        console.log('📋 Sample profile structure:');
        console.log(JSON.stringify(profiles[0], null, 2));
        
        console.log('🔍 Available fields:');
        Object.keys(profiles[0]).forEach(key => {
          console.log(`  - ${key}: ${typeof profiles[0][key]}`);
        });
      } else {
        console.log('📋 No profiles found in database');
      }
    }
    console.log('');

    // Test 3: Look for admin user specifically
    console.log('🧪 Test 3: Admin User Search');
    console.log('----------------------------');
    
    const { data: adminUser, error: adminError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', '7f4f5eea-3974-4e2f-a324-00fe5458750d')
      .single();
    
    if (adminError) {
      console.error('❌ Admin user query failed:', adminError.message);
      console.error('Error code:', adminError.code);
      
      // Try searching by email if that field exists
      console.log('🔍 Trying to search by email...');
      const { data: emailSearch, error: emailError } = await supabase
        .from('profiles')
        .select('*')
        .eq('email', '<EMAIL>');
      
      if (emailError) {
        console.error('❌ Email search failed:', emailError.message);
      } else {
        console.log('✅ Email search results:', emailSearch);
      }
    } else {
      console.log('✅ Admin user found:');
      console.log(JSON.stringify(adminUser, null, 2));
    }
    console.log('');

    // Test 4: Check auth.users table (if accessible)
    console.log('🧪 Test 4: Auth Users Check');
    console.log('---------------------------');
    
    try {
      const { data: authUsers, error: authError } = await supabase.auth.getUser();
      
      if (authError) {
        console.log('❌ Not authenticated, cannot check auth.users');
      } else {
        console.log('✅ Current auth user:', authUsers.user?.id);
        console.log('📧 Email:', authUsers.user?.email);
      }
    } catch (error) {
      console.log('❌ Auth check failed:', error.message);
    }
    console.log('');

    // Test 5: Try to get session
    console.log('🧪 Test 5: Session Check');
    console.log('------------------------');
    
    const { data: session, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Session check failed:', sessionError.message);
    } else {
      console.log('✅ Session status:', session.session ? 'Active' : 'No session');
      if (session.session) {
        console.log('👤 User ID:', session.session.user.id);
        console.log('📧 Email:', session.session.user.email);
      }
    }

  } catch (error) {
    console.error('💥 Unexpected error during investigation:', error);
  }
}

// Run the investigation
investigateDatabase().then(() => {
  console.log('');
  console.log('🎯 Investigation Complete');
  console.log('=========================');
  process.exit(0);
}).catch(error => {
  console.error('💥 Investigation failed:', error);
  process.exit(1);
});

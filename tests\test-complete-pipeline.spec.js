/**
 * Complete Admin-to-User Pipeline Test
 * 
 * Tests the complete flow: Admin creates → Database stores → User sees
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

test('Complete Admin-to-User Pipeline Test', async ({ page }) => {
  console.log('🧪 Testing complete admin-to-user pipeline...');
  
  const testActivity = {
    title: 'Complete Pipeline Test ' + Date.now(),
    description: 'This activity tests the complete admin-to-user pipeline.',
    location: 'Pipeline Test Location',
    type: 'meetup'
  };
  
  // STEP 1: Create activity as admin
  console.log('👨‍💼 STEP 1: Creating activity as admin...');
  
  const loginSuccess = await loginAsAdmin(page);
  console.log(`✅ Admin login: ${loginSuccess}`);
  
  if (loginSuccess) {
    await page.goto('/admin/activities/new');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Fill and submit form
    await page.fill('input[name="title"]', testActivity.title);
    await page.fill('textarea[name="description"]', testActivity.description);
    await page.fill('input[name="location"]', testActivity.location);
    
    const typeSelect = page.locator('select[name="type"]');
    if (await typeSelect.isVisible()) {
      await typeSelect.selectOption(testActivity.type);
    }
    
    // Set dates
    const startDate = new Date();
    startDate.setDate(startDate.getDate() + 1);
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 2);
    
    await page.fill('input[name="start_date"]', startDate.toISOString().slice(0, 16));
    await page.fill('input[name="end_date"]', endDate.toISOString().slice(0, 16));
    await page.fill('input[name="festival_id"]', '49239f09-0738-4e3a-9ea0-475f2f780e88');
    
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    console.log('✅ Activity created in admin');
    
    // Verify in admin list
    await page.goto('/admin/activities');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const inAdminList = await page.locator(`text="${testActivity.title}"`).count() > 0;
    console.log(`✅ Visible in admin list: ${inAdminList}`);
  }
  
  // STEP 2: Check user Activities page
  console.log('\n👤 STEP 2: Checking user Activities page...');
  
  // Navigate to user activities page (now public)
  await page.goto('/activities');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000); // Give time for data loading
  
  const pageLoaded = !page.url().includes('404');
  const hasInterface = await page.locator('input[placeholder*="Search"]').count() > 0;
  const hasTestActivity = await page.locator(`text="${testActivity.title}"`).count() > 0;
  const totalCards = await page.locator('.card').count();
  
  console.log(`User Activities page:`);
  console.log(`  Page loaded: ${pageLoaded}`);
  console.log(`  Has interface: ${hasInterface}`);
  console.log(`  Has test activity: ${hasTestActivity}`);
  console.log(`  Total cards: ${totalCards}`);
  
  await page.screenshot({ path: 'test-results/complete-pipeline-user-view.png', fullPage: true });
  
  // STEP 3: Check Discover page
  console.log('\n🔍 STEP 3: Checking Discover page...');
  
  await page.goto('/discover');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  
  const discoverLoaded = !page.url().includes('404');
  const hasDiscoverContent = await page.locator('.card, .discover-item').count() > 0;
  const hasTestActivityOnDiscover = await page.locator(`text="${testActivity.title}"`).count() > 0;
  
  console.log(`Discover page:`);
  console.log(`  Page loaded: ${discoverLoaded}`);
  console.log(`  Has content: ${hasDiscoverContent}`);
  console.log(`  Has test activity: ${hasTestActivityOnDiscover}`);
  
  await page.screenshot({ path: 'test-results/complete-pipeline-discover.png', fullPage: true });
  
  // STEP 4: Check Home page
  console.log('\n🏠 STEP 4: Checking Home page...');
  
  await page.goto('/');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  
  const homeLoaded = !page.url().includes('404');
  const hasHomeContent = await page.locator('.card, .activity, .event').count() > 0;
  const hasTestActivityOnHome = await page.locator(`text="${testActivity.title}"`).count() > 0;
  
  console.log(`Home page:`);
  console.log(`  Page loaded: ${homeLoaded}`);
  console.log(`  Has content: ${hasHomeContent}`);
  console.log(`  Has test activity: ${hasTestActivityOnHome}`);
  
  await page.screenshot({ path: 'test-results/complete-pipeline-home.png', fullPage: true });
  
  // FINAL ASSESSMENT
  console.log('\n📊 COMPLETE PIPELINE ASSESSMENT:');
  console.log('=====================================');
  
  const adminWorking = loginSuccess && inAdminList;
  const userPagesWorking = pageLoaded && discoverLoaded && homeLoaded;
  const dataVisibility = hasTestActivity || hasTestActivityOnDiscover || hasTestActivityOnHome;
  const interfaceWorking = hasInterface;
  
  console.log(`Admin creation: ${adminWorking ? '✅ WORKING' : '❌ FAILED'}`);
  console.log(`User pages loading: ${userPagesWorking ? '✅ WORKING' : '❌ FAILED'}`);
  console.log(`User interface: ${interfaceWorking ? '✅ WORKING' : '❌ FAILED'}`);
  console.log(`Data visibility: ${dataVisibility ? '✅ WORKING' : '❌ FAILED'}`);
  
  const pipelineWorking = adminWorking && userPagesWorking && interfaceWorking;
  
  console.log(`\n🎯 OVERALL PIPELINE: ${pipelineWorking ? '✅ WORKING' : '❌ NEEDS WORK'}`);
  
  if (pipelineWorking && dataVisibility) {
    console.log('🎉 SUCCESS: Complete admin-to-user pipeline is working!');
  } else if (pipelineWorking) {
    console.log('⚠️ PARTIAL: Pipeline works but data visibility needs investigation');
  } else {
    console.log('❌ FAILED: Pipeline has critical issues');
  }
  
  console.log('✅ Complete pipeline test completed');
});

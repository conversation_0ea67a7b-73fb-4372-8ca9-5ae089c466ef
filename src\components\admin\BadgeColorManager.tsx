import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Palette, Save, RefreshCw, Eye } from 'lucide-react';
import { UnifiedCard, UnifiedButton, UnifiedBadge } from '@/components/design-system';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'react-hot-toast';

interface BadgeColorConfig {
  id: string;
  name: string;
  color: string;
  category: string;
  isActive: boolean;
}

interface BadgeColorManagerProps {
  className?: string;
}

const defaultBadgeColors: BadgeColorConfig[] = [
  { id: '1', name: 'Featured', color: '#FF6B35', category: 'priority', isActive: true },
  { id: '2', name: 'Trending', color: '#F7931E', category: 'status', isActive: true },
  { id: '3', name: 'New Event', color: '#4ECDC4', category: 'status', isActive: true },
  { id: '4', name: 'Popular', color: '#45B7D1', category: 'engagement', isActive: true },
  { id: '5', name: 'Limited', color: '#96CEB4', category: 'availability', isActive: true },
  { id: '6', name: 'VIP', color: '#FFEAA7', category: 'tier', isActive: true },
];

export const BadgeColorManager: React.FC<BadgeColorManagerProps> = ({ className = '' }) => {
  const [badgeColors, setBadgeColors] = useState<BadgeColorConfig[]>(defaultBadgeColors);
  const [selectedBadge, setSelectedBadge] = useState<BadgeColorConfig | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);

  // Load badge colors from localStorage or API
  useEffect(() => {
    const savedColors = localStorage.getItem('festival-family-badge-colors');
    if (savedColors) {
      try {
        setBadgeColors(JSON.parse(savedColors));
      } catch (error) {
        console.error('Failed to load badge colors:', error);
      }
    }
  }, []);

  const handleColorChange = (badgeId: string, newColor: string) => {
    setBadgeColors(prev => 
      prev.map(badge => 
        badge.id === badgeId ? { ...badge, color: newColor } : badge
      )
    );
  };

  const handleSaveColors = async () => {
    setIsLoading(true);
    try {
      // Save to localStorage (in a real app, this would be an API call)
      localStorage.setItem('festival-family-badge-colors', JSON.stringify(badgeColors));
      toast.success('Badge colors saved successfully!');
    } catch (error) {
      toast.error('Failed to save badge colors');
      console.error('Save error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetColors = () => {
    setBadgeColors(defaultBadgeColors);
    localStorage.removeItem('festival-family-badge-colors');
    toast.success('Badge colors reset to defaults');
  };

  const toggleBadgeStatus = (badgeId: string) => {
    setBadgeColors(prev => 
      prev.map(badge => 
        badge.id === badgeId ? { ...badge, isActive: !badge.isActive } : badge
      )
    );
  };

  return (
    <UnifiedCard variant="elevated" className={`p-6 ${className}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <Palette className="w-5 h-5 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-primary">Badge Color Manager</h2>
              <p className="text-sm text-muted-foreground">Customize overlay badge colors for events and activities</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <UnifiedButton
              variant="outline"
              size="sm"
              onClick={() => setPreviewMode(!previewMode)}
              className="flex items-center gap-2"
            >
              <Eye className="w-4 h-4" />
              {previewMode ? 'Edit' : 'Preview'}
            </UnifiedButton>
          </div>
        </div>

        {/* Badge Color Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {badgeColors.map((badge, index) => (
            <motion.div
              key={badge.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="space-y-3"
            >
              <UnifiedCard 
                variant="outlined" 
                className={`p-4 transition-all duration-200 ${
                  selectedBadge?.id === badge.id ? 'ring-2 ring-primary' : ''
                } ${!badge.isActive ? 'opacity-50' : ''}`}
                onClick={() => setSelectedBadge(badge)}
              >
                <div className="space-y-3">
                  {/* Badge Preview */}
                  <div className="flex items-center justify-between">
                    <UnifiedBadge
                      variant="admin-customizable"
                      customColor={badge.color}
                      overlayMode={true}
                      size="md"
                    >
                      {badge.name}
                    </UnifiedBadge>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleBadgeStatus(badge.id);
                      }}
                      className={`w-4 h-4 rounded-full border-2 transition-colors ${
                        badge.isActive 
                          ? 'bg-green-500 border-green-500' 
                          : 'bg-transparent border-gray-300'
                      }`}
                    />
                  </div>

                  {/* Color Input */}
                  {!previewMode && (
                    <div className="space-y-2">
                      <Label htmlFor={`color-${badge.id}`} className="text-sm font-medium">
                        Color
                      </Label>
                      <div className="flex items-center gap-2">
                        <Input
                          id={`color-${badge.id}`}
                          type="color"
                          value={badge.color}
                          onChange={(e) => handleColorChange(badge.id, e.target.value)}
                          className="w-12 h-8 p-1 border rounded cursor-pointer"
                        />
                        <Input
                          type="text"
                          value={badge.color}
                          onChange={(e) => handleColorChange(badge.id, e.target.value)}
                          className="flex-1 text-xs font-mono"
                          placeholder="#FF6B35"
                        />
                      </div>
                    </div>
                  )}

                  {/* Category */}
                  <div className="text-xs text-muted-foreground capitalize">
                    {badge.category}
                  </div>
                </div>
              </UnifiedCard>
            </motion.div>
          ))}
        </div>

        {/* Action Buttons */}
        {!previewMode && (
          <div className="flex items-center justify-between pt-4 border-t border-border">
            <UnifiedButton
              variant="outline"
              onClick={handleResetColors}
              className="flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Reset to Defaults
            </UnifiedButton>
            
            <UnifiedButton
              variant="primary"
              onClick={handleSaveColors}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <Save className="w-4 h-4" />
              {isLoading ? 'Saving...' : 'Save Changes'}
            </UnifiedButton>
          </div>
        )}

        {/* Usage Instructions */}
        <div className="bg-muted/50 rounded-lg p-4 text-sm text-muted-foreground">
          <h4 className="font-medium mb-2">Usage Instructions:</h4>
          <ul className="space-y-1 list-disc list-inside">
            <li>Click on a badge card to select and edit its color</li>
            <li>Use the color picker or enter hex codes directly</li>
            <li>Toggle the status indicator to enable/disable badges</li>
            <li>Changes apply to all overlay badges across the application</li>
            <li>Use Preview mode to see how badges look without editing controls</li>
          </ul>
        </div>
      </div>
    </UnifiedCard>
  );
};

export default BadgeColorManager;

/**
 * ProfilePage Component Tests
 * 
 * Tests for the profile page component including profile display and editing functionality.
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { MemoryRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import userEvent from '@testing-library/user-event'

// Mock profile data
const mockProfile = {
  id: 'user-id',
  username: 'testuser',
  full_name: 'Test User',
  avatar_url: 'https://example.com/avatar.jpg',
  bio: 'Love music and festivals!',
  interests: ['Electronic', 'Rock', 'Indie'],
  website: 'https://example.com',
  updated_at: '2023-01-01T00:00:00.000Z',
}

// Mock ProfilePage component
const MockProfilePage: React.FC = () => {
  const [isEditing, setIsEditing] = React.useState(false)
  const [profile, setProfile] = React.useState(mockProfile)

  const handleEdit = () => setIsEditing(true)
  const handleSave = () => setIsEditing(false)
  const handleCancel = () => setIsEditing(false)

  if (isEditing) {
    return (
      <div data-testid="profile-page-edit">
        <h1>Edit Profile</h1>
        <form data-testid="profile-form">
          <div>
            <label htmlFor="username">Username</label>
            <input
              id="username"
              type="text"
              defaultValue={profile.username}
              data-testid="username-input"
            />
          </div>
          <div>
            <label htmlFor="full_name">Full Name</label>
            <input
              id="full_name"
              type="text"
              defaultValue={profile.full_name}
              data-testid="fullname-input"
            />
          </div>
          <div>
            <label htmlFor="bio">Bio</label>
            <textarea
              id="bio"
              defaultValue={profile.bio}
              data-testid="bio-input"
            />
          </div>
          <div>
            <label htmlFor="website">Website</label>
            <input
              id="website"
              type="url"
              defaultValue={profile.website}
              data-testid="website-input"
            />
          </div>
          <div className="interests-section">
            <label>Interests</label>
            <div data-testid="interests-list">
              {profile.interests.map((interest, index) => (
                <span key={index} className="interest-tag">
                  {interest}
                </span>
              ))}
            </div>
          </div>
          <div className="form-actions">
            <button type="button" onClick={handleSave} data-testid="save-btn">
              Save Changes
            </button>
            <button type="button" onClick={handleCancel} data-testid="cancel-btn">
              Cancel
            </button>
          </div>
        </form>
      </div>
    )
  }

  return (
    <div data-testid="profile-page-view">
      <h1>Profile</h1>
      <div className="profile-header">
        <img
          src={profile.avatar_url}
          alt={`${profile.full_name}'s avatar`}
          data-testid="profile-avatar"
        />
        <div className="profile-info">
          <h2 data-testid="profile-name">{profile.full_name}</h2>
          <p data-testid="profile-username">@{profile.username}</p>
          <p data-testid="profile-bio">{profile.bio}</p>
          {profile.website && (
            <a
              href={profile.website}
              target="_blank"
              rel="noopener noreferrer"
              data-testid="profile-website"
            >
              {profile.website}
            </a>
          )}
        </div>
        <button onClick={handleEdit} data-testid="edit-profile-btn">
          Edit Profile
        </button>
      </div>
      
      <div className="profile-sections">
        <section data-testid="interests-section">
          <h3>Interests</h3>
          <div className="interests-grid">
            {profile.interests.map((interest, index) => (
              <span key={index} className="interest-badge" data-testid={`interest-${index}`}>
                {interest}
              </span>
            ))}
          </div>
        </section>
        
        <section data-testid="connections-section">
          <h3>Connections</h3>
          <p>Connect with other festival-goers</p>
          <button data-testid="find-connections-btn">Find Connections</button>
        </section>
        
        <section data-testid="events-section">
          <h3>My Events</h3>
          <p>Events you're attending</p>
          <button data-testid="browse-events-btn">Browse Events</button>
        </section>
      </div>
    </div>
  )
}

// Mock the auth provider
const mockAuthContext = {
  session: {
    user: {
      id: 'user-id',
      email: '<EMAIL>',
      created_at: '2023-01-01T00:00:00.000Z',
    },
    access_token: 'token',
    refresh_token: 'refresh',
  },
  user: {
    id: 'user-id',
    email: '<EMAIL>',
    created_at: '2023-01-01T00:00:00.000Z',
  },
  profile: mockProfile,
  loading: false,
  isAdmin: false,
  signIn: jest.fn(),
  signUp: jest.fn(),
  signOut: jest.fn(),
  refreshProfile: jest.fn(),
}

jest.mock('../../providers/ConsolidatedAuthProvider', () => ({
  useAuth: () => mockAuthContext,
}))

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <MemoryRouter>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </MemoryRouter>
  )
}

describe('ProfilePage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('should render profile page in view mode', () => {
    render(
      <TestWrapper>
        <MockProfilePage />
      </TestWrapper>
    )

    expect(screen.getByTestId('profile-page-view')).toBeInTheDocument()
    expect(screen.getByText('Profile')).toBeInTheDocument()
    expect(screen.getByTestId('profile-name')).toHaveTextContent('Test User')
    expect(screen.getByTestId('profile-username')).toHaveTextContent('@testuser')
    expect(screen.getByTestId('profile-bio')).toHaveTextContent('Love music and festivals!')
  })

  test('should display profile avatar and information', () => {
    render(
      <TestWrapper>
        <MockProfilePage />
      </TestWrapper>
    )

    const avatar = screen.getByTestId('profile-avatar')
    expect(avatar).toBeInTheDocument()
    expect(avatar).toHaveAttribute('src', mockProfile.avatar_url)
    expect(avatar).toHaveAttribute('alt', "Test User's avatar")

    expect(screen.getByTestId('profile-website')).toBeInTheDocument()
    expect(screen.getByTestId('profile-website')).toHaveAttribute('href', mockProfile.website)
  })

  test('should display user interests', () => {
    render(
      <TestWrapper>
        <MockProfilePage />
      </TestWrapper>
    )

    expect(screen.getByTestId('interests-section')).toBeInTheDocument()
    expect(screen.getByText('Interests')).toBeInTheDocument()
    
    mockProfile.interests.forEach((interest, index) => {
      expect(screen.getByTestId(`interest-${index}`)).toHaveTextContent(interest)
    })
  })

  test('should render profile sections', () => {
    render(
      <TestWrapper>
        <MockProfilePage />
      </TestWrapper>
    )

    expect(screen.getByTestId('connections-section')).toBeInTheDocument()
    expect(screen.getByText('Connections')).toBeInTheDocument()
    expect(screen.getByTestId('find-connections-btn')).toBeInTheDocument()

    expect(screen.getByTestId('events-section')).toBeInTheDocument()
    expect(screen.getByText('My Events')).toBeInTheDocument()
    expect(screen.getByTestId('browse-events-btn')).toBeInTheDocument()
  })

  test('should switch to edit mode when edit button is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <MockProfilePage />
      </TestWrapper>
    )

    const editButton = screen.getByTestId('edit-profile-btn')
    await user.click(editButton)

    expect(screen.getByTestId('profile-page-edit')).toBeInTheDocument()
    expect(screen.getByText('Edit Profile')).toBeInTheDocument()
    expect(screen.getByTestId('profile-form')).toBeInTheDocument()
  })

  test('should render edit form with current profile data', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <MockProfilePage />
      </TestWrapper>
    )

    await user.click(screen.getByTestId('edit-profile-btn'))

    expect(screen.getByTestId('username-input')).toHaveValue(mockProfile.username)
    expect(screen.getByTestId('fullname-input')).toHaveValue(mockProfile.full_name)
    expect(screen.getByTestId('bio-input')).toHaveValue(mockProfile.bio)
    expect(screen.getByTestId('website-input')).toHaveValue(mockProfile.website)
  })

  test('should display interests in edit mode', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <MockProfilePage />
      </TestWrapper>
    )

    await user.click(screen.getByTestId('edit-profile-btn'))

    const interestsList = screen.getByTestId('interests-list')
    expect(interestsList).toBeInTheDocument()
    
    mockProfile.interests.forEach(interest => {
      expect(screen.getByText(interest)).toBeInTheDocument()
    })
  })

  test('should handle save action in edit mode', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <MockProfilePage />
      </TestWrapper>
    )

    await user.click(screen.getByTestId('edit-profile-btn'))
    await user.click(screen.getByTestId('save-btn'))

    // Should return to view mode
    expect(screen.getByTestId('profile-page-view')).toBeInTheDocument()
    expect(screen.queryByTestId('profile-page-edit')).not.toBeInTheDocument()
  })

  test('should handle cancel action in edit mode', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <MockProfilePage />
      </TestWrapper>
    )

    await user.click(screen.getByTestId('edit-profile-btn'))
    await user.click(screen.getByTestId('cancel-btn'))

    // Should return to view mode
    expect(screen.getByTestId('profile-page-view')).toBeInTheDocument()
    expect(screen.queryByTestId('profile-page-edit')).not.toBeInTheDocument()
  })

  test('should have proper form labels and accessibility', async () => {
    const user = userEvent.setup()

    render(
      <TestWrapper>
        <MockProfilePage />
      </TestWrapper>
    )

    await user.click(screen.getByTestId('edit-profile-btn'))

    // Check form labels for input fields
    expect(screen.getByLabelText('Username')).toBeInTheDocument()
    expect(screen.getByLabelText('Full Name')).toBeInTheDocument()
    expect(screen.getByLabelText('Bio')).toBeInTheDocument()
    expect(screen.getByLabelText('Website')).toBeInTheDocument()

    // Check interests section (not a form control, so check by text)
    expect(screen.getByText('Interests')).toBeInTheDocument()
    expect(screen.getByTestId('interests-list')).toBeInTheDocument()
  })
})

/**
 * useFavorites Hook
 * 
 * React hook for managing user favorites with database integration.
 * Provides a clean interface for adding/removing favorites and checking favorite status.
 * 
 * Features:
 * - Database-driven favorites management
 * - Real-time updates across components
 * - Optimistic updates for better UX
 * - Support for different content types (activities, events, festivals, etc.)
 * 
 * @module useFavorites
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { useState, useEffect, useCallback } from 'react'
import { toast } from 'react-hot-toast'
import { userInteractionService } from '@/lib/supabase/services/user-interaction-service'
import { useAuth } from '@/providers/ConsolidatedAuthProvider'

// ============================================================================
// TYPES
// ============================================================================

export interface UseFavoritesReturn {
  // State
  favorites: Set<string>
  isLoading: boolean
  error: string | null

  // Actions
  toggleFavorite: (itemId: string, itemType?: string) => Promise<void>
  addToFavorites: (itemId: string, itemType?: string) => Promise<void>
  removeFromFavorites: (itemId: string, itemType?: string) => Promise<void>
  isFavorite: (itemId: string) => boolean
  refreshFavorites: () => Promise<void>

  // Real-time subscriptions
  subscribeToFavorites: () => void
  unsubscribeFromFavorites: () => void
}

// ============================================================================
// HOOK IMPLEMENTATION
// ============================================================================

export function useFavorites(itemType: string = 'activity'): UseFavoritesReturn {
  const { user } = useAuth()
  const [favorites, setFavorites] = useState<Set<string>>(new Set())
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [subscriptionId, setSubscriptionId] = useState<string | null>(null)

  // ========================================================================
  // UTILITY FUNCTIONS
  // ========================================================================

  const handleError = useCallback((error: any, action: string) => {
    const message = error?.message ?? `Failed to ${action}`
    setError(message)
    toast.error(message)
    console.error(`Favorites error (${action}):`, error)
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // ========================================================================
  // DATA FETCHING
  // ========================================================================

  const fetchUserFavorites = useCallback(async () => {
    if (!user?.id) {
      setFavorites(new Set())
      return
    }

    try {
      const response = await userInteractionService.getUserFavorites(user.id, itemType)
      if (response.status === 'success' && response.data) {
        const favoriteIds = new Set(response.data.map(fav => fav.activity_id))
        setFavorites(favoriteIds)
      } else {
        handleError(response.error, 'fetch favorites')
      }
    } catch (error) {
      handleError(error, 'fetch favorites')
    }
  }, [user?.id, itemType, handleError])

  const refreshFavorites = useCallback(async () => {
    setIsLoading(true)
    clearError()
    await fetchUserFavorites()
    setIsLoading(false)
  }, [fetchUserFavorites, clearError])

  // ========================================================================
  // FAVORITE ACTIONS
  // ========================================================================

  const addToFavorites = useCallback(async (itemId: string, type: string = itemType) => {
    if (!user?.id || isLoading) return

    setIsLoading(true)
    clearError()

    // Optimistic update
    setFavorites(prev => new Set([...prev, itemId]))

    try {
      const response = await userInteractionService.addToFavorites(user.id, itemId, type)
      if (response.status === 'success') {
        toast.success('Added to favorites')
      } else {
        // Revert optimistic update
        setFavorites(prev => {
          const newSet = new Set(prev)
          newSet.delete(itemId)
          return newSet
        })
        handleError(response.error, 'add to favorites')
      }
    } catch (error) {
      // Revert optimistic update
      setFavorites(prev => {
        const newSet = new Set(prev)
        newSet.delete(itemId)
        return newSet
      })
      handleError(error, 'add to favorites')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id, isLoading, itemType, clearError, handleError])

  const removeFromFavorites = useCallback(async (itemId: string, type: string = itemType) => {
    if (!user?.id || isLoading) return

    setIsLoading(true)
    clearError()

    // Optimistic update
    setFavorites(prev => {
      const newSet = new Set(prev)
      newSet.delete(itemId)
      return newSet
    })

    try {
      const response = await userInteractionService.removeFromFavorites(user.id, itemId, type)
      if (response.status === 'success') {
        toast.success('Removed from favorites')
      } else {
        // Revert optimistic update
        setFavorites(prev => new Set([...prev, itemId]))
        handleError(response.error, 'remove from favorites')
      }
    } catch (error) {
      // Revert optimistic update
      setFavorites(prev => new Set([...prev, itemId]))
      handleError(error, 'remove from favorites')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id, isLoading, itemType, clearError, handleError])

  const toggleFavorite = useCallback(async (itemId: string, type: string = itemType) => {
    if (!user?.id || isLoading) return

    const isFav = favorites.has(itemId)
    if (isFav) {
      await removeFromFavorites(itemId, type)
    } else {
      await addToFavorites(itemId, type)
    }
  }, [user?.id, isLoading, favorites, addToFavorites, removeFromFavorites, itemType])

  // ========================================================================
  // UTILITY FUNCTIONS
  // ========================================================================

  const isFavorite = useCallback((itemId: string) => {
    return favorites.has(itemId)
  }, [favorites])

  // ========================================================================
  // REAL-TIME SUBSCRIPTIONS
  // ========================================================================

  const subscribeToFavorites = useCallback(() => {
    if (!user?.id || subscriptionId) return

    const subId = userInteractionService.subscribeToUserFavorites(
      user.id,
      (updatedFavorites) => {
        const favoriteIds = new Set(
          updatedFavorites
            .map(fav => fav.activity_id)
        )
        setFavorites(favoriteIds)
      }
    )
    setSubscriptionId(subId)
  }, [user?.id, itemType, subscriptionId])

  const unsubscribeFromFavorites = useCallback(() => {
    if (subscriptionId) {
      userInteractionService.unsubscribe(subscriptionId)
      setSubscriptionId(null)
    }
  }, [subscriptionId])

  // ========================================================================
  // EFFECTS
  // ========================================================================

  // Initial data fetch
  useEffect(() => {
    if (user?.id) {
      fetchUserFavorites()
    } else {
      setFavorites(new Set())
    }
  }, [user?.id, fetchUserFavorites])

  // Setup real-time subscriptions
  useEffect(() => {
    subscribeToFavorites()
    return () => unsubscribeFromFavorites()
  }, [subscribeToFavorites, unsubscribeFromFavorites])

  // ========================================================================
  // RETURN INTERFACE
  // ========================================================================

  return {
    // State
    favorites,
    isLoading,
    error,

    // Actions
    toggleFavorite,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    refreshFavorites,

    // Real-time subscriptions
    subscribeToFavorites,
    unsubscribeFromFavorites
  }
}

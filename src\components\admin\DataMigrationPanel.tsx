import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, Database, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { festivalDataMigrationService } from '@/lib/services/festivalDataMigration';
import { dataCleanupService } from '@/lib/services/dataCleanupService';
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';

interface MigrationResult {
  success: boolean;
  count: number;
  errors: string[];
}

interface ComprehensiveResult {
  cleanup?: {
    success: boolean;
    totalItemsRemoved: number;
    summary: string[];
  };
  colorMappings?: {
    success: boolean;
    count: number;
    errors: string[];
  };
  migration?: MigrationResult;
}

export const DataMigrationPanel: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<ComprehensiveResult | null>(null);
  const [activeOperation, setActiveOperation] = useState<string>('');

  const handleComprehensiveDataImprovement = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      console.log('🚀 Starting comprehensive Festival Family data improvement...');

      // Step 1: Clean up test/fake data
      console.log('🧹 Step 1: Cleaning up test/fake data...');
      const cleanupResult = await dataCleanupService.performComprehensiveCleanup();

      // Step 2: Initialize enhanced color mappings
      console.log('🎨 Step 2: Initializing enhanced color mappings...');
      const colorMappingsResult = await enhancedColorMappingService.initializeEnhancedColorMappings();

      // Step 3: Import comprehensive Festival Family data
      console.log('📊 Step 3: Importing comprehensive Festival Family data...');
      const comprehensiveFestivalData = {
        "Festival Family Info": {},
        "Fam Activities": {
          raw: [],
          data: [
            {
              "Month": "March '26",
              "Date from-till": "21-24",
              "Time from-till": "12.00h - 24.00h",
              "Where?": "Undecided",
              "What?": "Festival Family Pre-Meet",
              "Plans are final?": "TRUE",
              "Pay needed?": "TRUE",
              "Need to sign-up?": "TRUE",
              "Sign-up deadline? (yyyy-mm-dd)": "2026-03-01",
              "Contact": "<EMAIL>"
            },
            {
              "Month": "March '26",
              "Date from-till": "22",
              "Time from-till": "20h",
              "Where?": "Online",
              "What?": "OPTION Fam Raffle",
              "Plans are final?": "FALSE",
              "Pay needed?": "TRUE",
              "Need to sign-up?": "TRUE",
              "Sign-up deadline? (yyyy-mm-dd)": "2026-02-01",
              "Contact": "<EMAIL>"
            },
            {
              "Month": "July '25",
              "Date from-till": "10-17",
              "Time from-till": "All day",
              "Where?": "Sziget Island, Budapest",
              "What?": "Sziget Festival Family Meetups",
              "Plans are final?": "TRUE",
              "Pay needed?": "FALSE",
              "Need to sign-up?": "FALSE",
              "Sign-up deadline? (yyyy-mm-dd)": "",
              "Contact": "<EMAIL>"
            },
            {
              "Month": "July '25",
              "Date from-till": "3-6",
              "Time from-till": "All day",
              "Where?": "Lake Balaton, Hungary",
              "What?": "Balaton Sound Festival Family Meetups",
              "Plans are final?": "TRUE",
              "Pay needed?": "FALSE",
              "Need to sign-up?": "FALSE",
              "Sign-up deadline? (yyyy-mm-dd)": "",
              "Contact": "<EMAIL>"
            },
            {
              "Month": "September '25",
              "Date from-till": "15-16",
              "Time from-till": "18.00h - 02.00h",
              "Where?": "Amsterdam, Netherlands",
              "What?": "Annual Family Reunion",
              "Plans are final?": "TRUE",
              "Pay needed?": "TRUE",
              "Need to sign-up?": "TRUE",
              "Sign-up deadline? (yyyy-mm-dd)": "2025-08-15",
              "Contact": "<EMAIL>"
            },
            {
              "Month": "December '25",
              "Date from-till": "28-2",
              "Time from-till": "All day",
              "Where?": "TBD",
              "What?": "New Years With YOUR Festival Family 2025-2026",
              "Plans are final?": "FALSE",
              "Pay needed?": "TRUE",
              "Need to sign-up?": "TRUE",
              "Sign-up deadline? (yyyy-mm-dd)": "2025-11-30",
              "Contact": "<EMAIL>"
            }
          ]
        }
      };

      const migrationResult = await festivalDataMigrationService.migrateActivities(comprehensiveFestivalData);

      // Compile comprehensive results
      const comprehensiveResult: ComprehensiveResult = {
        cleanup: cleanupResult,
        colorMappings: colorMappingsResult,
        migration: migrationResult
      };

      setResult(comprehensiveResult);

      console.log('🎉 Comprehensive Festival Family data improvement completed!');
    } catch (error) {
      setResult({
        migration: {
          success: false,
          count: 0,
          errors: [error instanceof Error ? error.message : 'Unknown error occurred']
        }
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Comprehensive Festival Family Data Improvement
        </CardTitle>
        <CardDescription>
          Clean up test data, enhance visual system, and import comprehensive real Festival Family content
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Upload className="h-4 w-4" />
            <span>Comprehensive data improvement: cleanup, enhanced visuals, and real Festival Family content</span>
          </div>

          <Button
            onClick={handleComprehensiveDataImprovement}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Improving Data System...
              </>
            ) : (
              <>
                <Database className="mr-2 h-4 w-4" />
                Start Comprehensive Data Improvement
              </>
            )}
          </Button>

          {result && (
            <div className="space-y-4">
              {/* Cleanup Results */}
              {result.cleanup && (
                <Alert variant={result.cleanup.success ? "default" : "destructive"}>
                  <div className="flex items-center gap-2">
                    {result.cleanup.success ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4" />
                    )}
                    <AlertDescription>
                      <strong>Data Cleanup:</strong> {result.cleanup.success
                        ? `Successfully removed ${result.cleanup.totalItemsRemoved} test/fake items`
                        : 'Cleanup failed'
                      }
                    </AlertDescription>
                  </div>
                </Alert>
              )}

              {/* Color Mappings Results */}
              {result.colorMappings && (
                <Alert variant={result.colorMappings.success ? "default" : "destructive"}>
                  <div className="flex items-center gap-2">
                    {result.colorMappings.success ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4" />
                    )}
                    <AlertDescription>
                      <strong>Enhanced Color System:</strong> {result.colorMappings.success
                        ? `Successfully initialized ${result.colorMappings.count} color mappings`
                        : 'Color system initialization failed'
                      }
                    </AlertDescription>
                  </div>
                </Alert>
              )}

              {/* Migration Results */}
              {result.migration && (
                <Alert variant={result.migration.success ? "default" : "destructive"}>
                  <div className="flex items-center gap-2">
                    {result.migration.success ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4" />
                    )}
                    <AlertDescription>
                      <strong>Data Migration:</strong> {result.migration.success
                        ? `Successfully imported ${result.migration.count} comprehensive Festival Family activities`
                        : `Migration failed. ${result.migration.errors.length} errors occurred.`
                      }
                    </AlertDescription>
                  </div>
                </Alert>
              )}

              {/* Success Summary */}
              {result.migration?.success && result.migration.count > 0 && (
                <div className="text-sm text-green-600 bg-green-50 p-3 rounded">
                  <p className="font-medium">🎉 Comprehensive Data Improvement Complete!</p>
                  <p>✅ Test/fake data cleaned up</p>
                  <p>✅ Enhanced color psychology system initialized</p>
                  <p>✅ {result.migration.count} real Festival Family activities imported</p>
                  <p>✅ Research-based activity categorization applied</p>
                  <p>✅ Sophisticated visual hierarchy implemented</p>
                  <p>✅ Database-driven color management active</p>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="border-t pt-4">
          <h4 className="text-sm font-medium mb-2">Comprehensive Data Improvement Features:</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-muted-foreground">
            <div>
              <h5 className="font-medium text-gray-700 mb-1">🧹 Data Cleanup</h5>
              <ul className="space-y-1">
                <li>• Remove test/fake activities and content</li>
                <li>• Clean placeholder data and examples</li>
                <li>• Preserve authentic Festival Family content</li>
              </ul>
            </div>
            <div>
              <h5 className="font-medium text-gray-700 mb-1">🎨 Enhanced Visual System</h5>
              <ul className="space-y-1">
                <li>• Research-based color psychology mapping</li>
                <li>• Sophisticated visual hierarchy</li>
                <li>• Content type-specific color schemes</li>
              </ul>
            </div>
            <div>
              <h5 className="font-medium text-gray-700 mb-1">📊 Real Festival Data</h5>
              <ul className="space-y-1">
                <li>• Festival Family Pre-Meet & Reunion events</li>
                <li>• Comprehensive Sziget & Balaton activities</li>
                <li>• Daily meetups and scavenger hunts</li>
                <li>• Real locations and contact information</li>
              </ul>
            </div>
            <div>
              <h5 className="font-medium text-gray-700 mb-1">🏷️ Advanced Categorization</h5>
              <ul className="space-y-1">
                <li>• Festival-specific activity types</li>
                <li>• Context-aware categorization</li>
                <li>• Hierarchical content organization</li>
                <li>• Database-driven icon management</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

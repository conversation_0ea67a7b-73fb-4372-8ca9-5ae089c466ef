/**
 * Test RLS Fix Status
 * 
 * Quick test to check if RLS recursion issues are resolved despite policy errors
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL, 
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY
);

console.log('🔍 Testing RLS Fix Status After Policy Error');
console.log('============================================');

async function testRLSRecursionFix() {
  const results = {
    helperFunctionsExist: false,
    groupMembersAccessible: false,
    groupActivitiesAccessible: false,
    chatTablesAccessible: false,
    rlsRecursionFixed: false
  };

  // Test 1: Check if helper functions were created
  console.log('🔧 Testing helper functions...');
  try {
    const { data, error } = await supabase.rpc('is_group_member', { 
      group_id: '00000000-0000-0000-0000-000000000000' 
    });
    
    if (!error || !error.message.includes('function') || !error.message.includes('does not exist')) {
      results.helperFunctionsExist = true;
      console.log('✅ Helper functions exist');
    } else {
      console.log('❌ Helper functions missing:', error.message);
    }
  } catch (err) {
    console.log('⚠️ Helper function test error:', err.message);
  }

  // Test 2: Check group_members table accessibility
  console.log('👥 Testing group_members table...');
  try {
    const { data, error } = await supabase
      .from('group_members')
      .select('*')
      .limit(1);
    
    if (!error) {
      results.groupMembersAccessible = true;
      console.log('✅ group_members table accessible');
    } else if (error.message.includes('infinite recursion')) {
      console.log('❌ group_members still has recursion:', error.message);
    } else {
      console.log('⚠️ group_members other error:', error.message);
    }
  } catch (err) {
    console.log('💥 group_members test failed:', err.message);
  }

  // Test 3: Check group_activities table accessibility
  console.log('🎯 Testing group_activities table...');
  try {
    const { data, error } = await supabase
      .from('group_activities')
      .select('*')
      .limit(1);
    
    if (!error) {
      results.groupActivitiesAccessible = true;
      console.log('✅ group_activities table accessible');
    } else if (error.message.includes('infinite recursion')) {
      console.log('❌ group_activities still has recursion:', error.message);
    } else {
      console.log('⚠️ group_activities other error:', error.message);
    }
  } catch (err) {
    console.log('💥 group_activities test failed:', err.message);
  }

  // Test 4: Check chat tables accessibility
  console.log('💬 Testing chat tables...');
  const chatTables = ['chat_rooms', 'chat_room_members', 'chat_messages'];
  let chatTablesWorking = 0;
  
  for (const table of chatTables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (!error) {
        chatTablesWorking++;
        console.log(`✅ ${table} accessible`);
      } else if (error.message.includes('infinite recursion')) {
        console.log(`❌ ${table} still has recursion`);
      } else {
        console.log(`⚠️ ${table} other error:`, error.message);
      }
    } catch (err) {
      console.log(`💥 ${table} test failed:`, err.message);
    }
  }
  
  results.chatTablesAccessible = chatTablesWorking === chatTables.length;

  // Overall assessment
  results.rlsRecursionFixed = 
    results.groupMembersAccessible && 
    results.groupActivitiesAccessible && 
    results.chatTablesAccessible;

  return results;
}

async function main() {
  try {
    const results = await testRLSRecursionFix();
    
    console.log('\n🎉 RLS FIX STATUS SUMMARY');
    console.log('=========================');
    console.log(`🔧 Helper Functions: ${results.helperFunctionsExist ? 'EXISTS' : 'MISSING'}`);
    console.log(`👥 Group Members: ${results.groupMembersAccessible ? 'WORKING' : 'BLOCKED'}`);
    console.log(`🎯 Group Activities: ${results.groupActivitiesAccessible ? 'WORKING' : 'BLOCKED'}`);
    console.log(`💬 Chat Tables: ${results.chatTablesAccessible ? 'WORKING' : 'BLOCKED'}`);
    console.log(`🎯 Overall RLS Fix: ${results.rlsRecursionFixed ? '✅ SUCCESS' : '❌ NEEDS WORK'}`);
    
    console.log('\n📋 RECOMMENDATION:');
    if (results.rlsRecursionFixed) {
      console.log('✅ The policy error was just informational - core functionality is working!');
      console.log('✅ You can safely ignore the "policy already exists" error.');
      console.log('✅ RLS recursion issues have been resolved successfully.');
    } else {
      console.log('⚠️ The policy error may have prevented the fix from applying completely.');
      console.log('🔧 You may need to apply a modified version of the RLS fix migration.');
    }
    
  } catch (error) {
    console.error('💥 RLS fix test failed:', error);
  }
  
  process.exit(0);
}

main();

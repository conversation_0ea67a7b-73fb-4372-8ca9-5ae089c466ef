#!/usr/bin/env node

/**
 * Evidence-Based Application Verification
 * 
 * This script performs real HTTP requests and tests to verify
 * the application is working and collect evidence.
 */

import http from 'http';
import https from 'https';
import { promises as fs } from 'fs';
import { URL } from 'url';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'test-evidence-node';

// Ensure evidence directory exists
async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

// HTTP request helper
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      timeout: 10000,
      ...options
    }, (res) => {
      let data = '';
      
      res.on('data', chunk => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          statusMessage: res.statusMessage,
          headers: res.headers,
          data: data
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// Test 1: Application Accessibility
async function testApplicationAccessibility() {
  console.log('\n🌐 Test 1: Application Accessibility');
  console.log('====================================');
  
  const startTime = Date.now();
  
  try {
    console.log(`🔗 Testing: ${APP_URL}`);
    
    const response = await makeRequest(APP_URL);
    const loadTime = Date.now() - startTime;
    
    const evidence = {
      testName: 'Application Accessibility',
      timestamp: new Date().toISOString(),
      url: APP_URL,
      statusCode: response.statusCode,
      statusMessage: response.statusMessage,
      loadTimeMs: loadTime,
      contentLength: response.data.length,
      contentType: response.headers['content-type'],
      success: response.statusCode === 200
    };
    
    // Save HTML content
    await fs.writeFile(`${EVIDENCE_DIR}/01-page-content.html`, response.data);
    
    console.log(`✅ Status: ${response.statusCode} ${response.statusMessage}`);
    console.log(`⚡ Load Time: ${loadTime}ms`);
    console.log(`📄 Content Length: ${response.data.length} characters`);
    console.log(`📋 Content Type: ${response.headers['content-type']}`);
    
    // Save evidence
    await fs.writeFile(
      `${EVIDENCE_DIR}/01-accessibility-evidence.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    return evidence;
    
  } catch (error) {
    console.log(`❌ Application not accessible: ${error.message}`);
    
    const evidence = {
      testName: 'Application Accessibility',
      timestamp: new Date().toISOString(),
      url: APP_URL,
      success: false,
      error: error.message,
      loadTimeMs: Date.now() - startTime
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/01-accessibility-evidence.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    return evidence;
  }
}

// Test 2: HTML Content Analysis
async function testHtmlContentAnalysis() {
  console.log('\n📄 Test 2: HTML Content Analysis');
  console.log('=================================');
  
  try {
    const htmlContent = await fs.readFile(`${EVIDENCE_DIR}/01-page-content.html`, 'utf8');
    
    // Analyze HTML content
    const analysis = {
      testName: 'HTML Content Analysis',
      timestamp: new Date().toISOString(),
      contentLength: htmlContent.length,
      lineCount: htmlContent.split('\n').length,
      hasTitle: /<title>(.*?)<\/title>/.test(htmlContent),
      hasReact: /react|React/.test(htmlContent),
      hasVite: /vite|Vite/.test(htmlContent),
      hasSupabase: /supabase|Supabase/.test(htmlContent),
      hasRootDiv: /id="root"/.test(htmlContent),
      hasMetaViewport: /<meta.*viewport/.test(htmlContent),
      hasCharset: /<meta.*charset/.test(htmlContent)
    };
    
    // Extract title
    const titleMatch = htmlContent.match(/<title>(.*?)<\/title>/);
    analysis.pageTitle = titleMatch ? titleMatch[1] : 'No title found';
    
    console.log(`📋 Page Title: "${analysis.pageTitle}"`);
    console.log(`📊 Content Analysis:`);
    console.log(`   - Has Title Tag: ${analysis.hasTitle ? '✅' : '❌'}`);
    console.log(`   - React References: ${analysis.hasReact ? '✅' : '❌'}`);
    console.log(`   - Vite References: ${analysis.hasVite ? '✅' : '❌'}`);
    console.log(`   - Supabase References: ${analysis.hasSupabase ? '✅' : '❌'}`);
    console.log(`   - Root Div Present: ${analysis.hasRootDiv ? '✅' : '❌'}`);
    console.log(`   - Meta Viewport: ${analysis.hasMetaViewport ? '✅' : '❌'}`);
    console.log(`   - Charset Declaration: ${analysis.hasCharset ? '✅' : '❌'}`);
    console.log(`   - Content Length: ${analysis.contentLength} characters`);
    console.log(`   - Line Count: ${analysis.lineCount} lines`);
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/02-content-analysis.json`,
      JSON.stringify(analysis, null, 2)
    );
    
    return analysis;
    
  } catch (error) {
    console.log(`❌ Content analysis failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Test 3: Performance Testing
async function testPerformance() {
  console.log('\n⚡ Test 3: Performance Testing');
  console.log('==============================');
  
  const performanceTests = [];
  
  for (let i = 1; i <= 3; i++) {
    try {
      const startTime = Date.now();
      const response = await makeRequest(APP_URL);
      const loadTime = Date.now() - startTime;
      
      performanceTests.push({
        testRun: i,
        loadTimeMs: loadTime,
        statusCode: response.statusCode,
        contentLength: response.data.length,
        success: true
      });
      
      console.log(`🔄 Load ${i}: ${loadTime}ms (${response.statusCode})`);
      
    } catch (error) {
      performanceTests.push({
        testRun: i,
        success: false,
        error: error.message
      });
      
      console.log(`❌ Load ${i}: Failed - ${error.message}`);
    }
  }
  
  const successfulTests = performanceTests.filter(test => test.success);
  const averageLoadTime = successfulTests.length > 0 
    ? successfulTests.reduce((sum, test) => sum + test.loadTimeMs, 0) / successfulTests.length
    : null;
  
  const evidence = {
    testName: 'Performance Testing',
    timestamp: new Date().toISOString(),
    performanceTests,
    averageLoadTimeMs: averageLoadTime ? Math.round(averageLoadTime * 100) / 100 : null,
    successfulTests: successfulTests.length,
    totalTests: performanceTests.length,
    successRate: (successfulTests.length / performanceTests.length * 100).toFixed(1)
  };
  
  if (averageLoadTime) {
    console.log(`📊 Average Load Time: ${evidence.averageLoadTimeMs}ms`);
    console.log(`✅ Success Rate: ${evidence.successRate}%`);
  }
  
  await fs.writeFile(
    `${EVIDENCE_DIR}/03-performance-evidence.json`,
    JSON.stringify(evidence, null, 2)
  );
  
  return evidence;
}

// Test 4: Environment Configuration Check
async function testEnvironmentConfiguration() {
  console.log('\n🔧 Test 4: Environment Configuration');
  console.log('====================================');
  
  try {
    const envContent = await fs.readFile('.env.local', 'utf8');
    const envLines = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
    
    const config = {};
    envLines.forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        config[key.trim()] = valueParts.join('=').trim();
      }
    });
    
    const evidence = {
      testName: 'Environment Configuration',
      timestamp: new Date().toISOString(),
      envFileFound: true,
      configurationKeys: Object.keys(config),
      hasSupabaseUrl: !!config.VITE_SUPABASE_URL,
      hasSupabaseKey: !!config.VITE_SUPABASE_ANON_KEY,
      totalConfigs: Object.keys(config).length
    };
    
    console.log(`📄 .env.local file found`);
    console.log(`🔑 Configuration keys: ${evidence.configurationKeys.length}`);
    console.log(`   - VITE_SUPABASE_URL: ${evidence.hasSupabaseUrl ? '✅' : '❌'}`);
    console.log(`   - VITE_SUPABASE_ANON_KEY: ${evidence.hasSupabaseKey ? '✅' : '❌'}`);
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/04-environment-evidence.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    return evidence;
    
  } catch (error) {
    console.log(`⚠️ Environment file not accessible: ${error.message}`);
    
    const evidence = {
      testName: 'Environment Configuration',
      timestamp: new Date().toISOString(),
      envFileFound: false,
      error: error.message
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/04-environment-evidence.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    return evidence;
  }
}

// Generate Final Report
async function generateFinalReport(testResults) {
  console.log('\n📊 EVIDENCE-BASED VERIFICATION REPORT');
  console.log('=====================================');
  
  const report = {
    sessionInfo: {
      timestamp: new Date().toISOString(),
      applicationUrl: APP_URL,
      evidenceDirectory: EVIDENCE_DIR
    },
    testResults: {
      applicationAccessible: testResults.accessibility?.success || false,
      contentAnalysisCompleted: testResults.contentAnalysis?.testName === 'HTML Content Analysis',
      performanceTestsCompleted: testResults.performance?.totalTests || 0,
      environmentConfigurationFound: testResults.environment?.envFileFound || false
    },
    summary: {
      totalTests: 4,
      passedTests: 0,
      evidenceFilesGenerated: []
    }
  };
  
  // Count passed tests
  if (report.testResults.applicationAccessible) report.summary.passedTests++;
  if (report.testResults.contentAnalysisCompleted) report.summary.passedTests++;
  if (report.testResults.performanceTestsCompleted > 0) report.summary.passedTests++;
  if (report.testResults.environmentConfigurationFound) report.summary.passedTests++;
  
  // List evidence files
  try {
    const files = await fs.readdir(EVIDENCE_DIR);
    report.summary.evidenceFilesGenerated = files;
  } catch (error) {
    console.log(`⚠️ Could not list evidence files: ${error.message}`);
  }
  
  const successRate = (report.summary.passedTests / report.summary.totalTests * 100).toFixed(1);
  
  console.log(`✅ Tests Passed: ${report.summary.passedTests}/${report.summary.totalTests} (${successRate}%)`);
  console.log(`📁 Evidence Files: ${report.summary.evidenceFilesGenerated.length}`);
  console.log(`📄 Evidence Directory: ${EVIDENCE_DIR}`);
  
  if (report.testResults.applicationAccessible) {
    console.log(`⚡ Average Load Time: ${testResults.performance?.averageLoadTimeMs || 'N/A'}ms`);
  }
  
  await fs.writeFile(
    `${EVIDENCE_DIR}/final-verification-report.json`,
    JSON.stringify(report, null, 2)
  );
  
  console.log('\n📋 Evidence Files Generated:');
  report.summary.evidenceFilesGenerated.forEach(file => {
    console.log(`   - ${file}`);
  });
  
  return report;
}

// Main execution
async function runEvidenceBasedVerification() {
  console.log('🔍 EVIDENCE-BASED APPLICATION VERIFICATION');
  console.log('==========================================');
  console.log(`🕐 Start Time: ${new Date().toISOString()}`);
  
  await ensureEvidenceDir();
  
  const testResults = {};
  
  try {
    testResults.accessibility = await testApplicationAccessibility();
    
    if (testResults.accessibility.success) {
      testResults.contentAnalysis = await testHtmlContentAnalysis();
      testResults.performance = await testPerformance();
    }
    
    testResults.environment = await testEnvironmentConfiguration();
    
    const finalReport = await generateFinalReport(testResults);
    
    console.log('\n🏁 Evidence-based verification completed');
    return finalReport;
    
  } catch (error) {
    console.error('\n💥 Verification failed:', error);
    throw error;
  }
}

// Run the verification
runEvidenceBasedVerification()
  .then(() => {
    console.log('\n✅ All evidence-based tests completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Evidence-based verification failed:', error);
    process.exit(1);
  });

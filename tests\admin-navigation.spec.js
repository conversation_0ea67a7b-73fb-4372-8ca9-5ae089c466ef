/**
 * Admin Navigation Testing Suite
 * 
 * This test suite verifies that all admin interface improvements work correctly:
 * - React Router suspension error fixes
 * - Responsive admin layout (mobile/desktop)
 * - Permission-based sidebar filtering
 * - Admin authentication and navigation
 */

import { test, expect } from '@playwright/test';
import { promises as fs } from 'fs';

// Test configuration
const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'admin-navigation-evidence';

// Admin credentials from user confirmation
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

// Ensure evidence directory exists
test.beforeAll(async () => {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory created: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory already exists: ${EVIDENCE_DIR}`);
  }
});

/**
 * PHASE 1: ADMIN AUTHENTICATION VERIFICATION
 */
test.describe('Phase 1: Admin Authentication', () => {
  
  test('1.1 Admin Login Flow', async ({ page }) => {
    console.log('🔐 Testing admin login flow...');
    
    // Navigate to application
    await page.goto(APP_URL);
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of initial page
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/01-initial-page.png`,
      fullPage: true 
    });
    
    // Look for login/auth link
    const authLink = page.locator('text=/login|sign in|auth/i').first();
    
    if (await authLink.isVisible()) {
      await authLink.click();
      await page.waitForLoadState('networkidle');
    } else {
      // Try direct navigation to auth page
      await page.goto(`${APP_URL}/auth`);
      await page.waitForLoadState('networkidle');
    }
    
    // Fill login form
    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    
    // Take screenshot before login
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/02-login-form-filled.png`,
      fullPage: true 
    });
    
    // Submit login form
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000); // Wait for authentication
    
    // Take screenshot after login
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/03-after-login.png`,
      fullPage: true 
    });
    
    // Verify successful login
    const currentUrl = page.url();
    const isLoggedIn = !currentUrl.includes('/auth') || await page.locator('text=/dashboard|admin|profile/i').isVisible();
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/01-admin-login-evidence.json`,
      JSON.stringify({
        loginAttempted: true,
        currentUrl,
        isLoggedIn,
        timestamp: new Date().toISOString()
      }, null, 2)
    );
    
    console.log(`✅ Login successful: ${isLoggedIn ? '✅' : '❌'}`);
    console.log(`📍 Current URL: ${currentUrl}`);
    
    expect(isLoggedIn).toBe(true);
  });
});

/**
 * PHASE 2: ADMIN INTERFACE ACCESS VERIFICATION
 */
test.describe('Phase 2: Admin Interface Access', () => {
  
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto(`${APP_URL}/auth`);
    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(2000);
  });
  
  test('2.1 Admin Dashboard Access', async ({ page }) => {
    console.log('🏠 Testing admin dashboard access...');
    
    // Navigate to admin dashboard
    await page.goto(`${APP_URL}/admin`);
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of admin dashboard
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/04-admin-dashboard.png`,
      fullPage: true 
    });
    
    // Check for admin interface elements
    const hasAdminPanel = await page.locator('text=/admin panel|dashboard/i').isVisible();
    const hasNavigation = await page.locator('nav, [role="navigation"]').isVisible();
    const hasSidebar = await page.locator('text=/festivals|events|users|activities/i').isVisible();
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/02-admin-dashboard-evidence.json`,
      JSON.stringify({
        hasAdminPanel,
        hasNavigation,
        hasSidebar,
        currentUrl: page.url(),
        timestamp: new Date().toISOString()
      }, null, 2)
    );
    
    console.log(`🏠 Admin panel visible: ${hasAdminPanel ? '✅' : '❌'}`);
    console.log(`🧭 Navigation visible: ${hasNavigation ? '✅' : '❌'}`);
    console.log(`📋 Sidebar visible: ${hasSidebar ? '✅' : '❌'}`);
    
    expect(hasAdminPanel || hasNavigation || hasSidebar).toBe(true);
  });
  
  test('2.2 React Router Suspension Error Check', async ({ page }) => {
    console.log('⚡ Testing React Router suspension error fixes...');
    
    const errors = [];
    const warnings = [];
    
    // Monitor console for suspension errors
    page.on('console', msg => {
      const text = msg.text();
      if (msg.type() === 'error') {
        errors.push(text);
        if (text.includes('suspended while responding to synchronous input')) {
          console.log('❌ SUSPENSION ERROR DETECTED:', text);
        }
      } else if (msg.type() === 'warning') {
        warnings.push(text);
      }
    });
    
    // Test navigation to different admin sections
    const adminSections = [
      '/admin',
      '/admin/festivals', 
      '/admin/events',
      '/admin/activities',
      '/admin/users'
    ];
    
    for (const section of adminSections) {
      console.log(`🔄 Testing navigation to: ${section}`);
      
      await page.goto(`${APP_URL}${section}`);
      await page.waitForTimeout(1000); // Wait for any async operations
      
      // Take screenshot of each section
      const sectionName = section.split('/').pop() || 'admin';
      await page.screenshot({ 
        path: `${EVIDENCE_DIR}/05-${sectionName}-section.png`,
        fullPage: true 
      });
    }
    
    // Check for suspension errors
    const suspensionErrors = errors.filter(error => 
      error.includes('suspended while responding to synchronous input')
    );
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/03-suspension-error-check.json`,
      JSON.stringify({
        totalErrors: errors.length,
        totalWarnings: warnings.length,
        suspensionErrors: suspensionErrors.length,
        suspensionErrorDetails: suspensionErrors,
        allErrors: errors,
        timestamp: new Date().toISOString()
      }, null, 2)
    );
    
    console.log(`❌ Total console errors: ${errors.length}`);
    console.log(`⚠️ Total console warnings: ${warnings.length}`);
    console.log(`🚫 Suspension errors: ${suspensionErrors.length}`);
    
    // Test should pass if no suspension errors
    expect(suspensionErrors.length).toBe(0);
  });
});

/**
 * PHASE 3: RESPONSIVE LAYOUT VERIFICATION
 */
test.describe('Phase 3: Responsive Layout', () => {

  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto(`${APP_URL}/auth`);
    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(2000);
  });

  test('3.1 Desktop Layout Verification', async ({ page }) => {
    console.log('🖥️ Testing desktop layout...');

    // Set desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.goto(`${APP_URL}/admin`);
    await page.waitForLoadState('networkidle');

    // Take screenshot of desktop layout
    await page.screenshot({
      path: `${EVIDENCE_DIR}/06-desktop-layout.png`,
      fullPage: true
    });

    // Check desktop-specific elements
    const sidebarVisible = await page.locator('nav').isVisible();
    const hamburgerMenuHidden = await page.locator('button:has-text("☰"), button[aria-label*="menu"]').isHidden();

    await fs.writeFile(
      `${EVIDENCE_DIR}/04-desktop-layout-evidence.json`,
      JSON.stringify({
        viewport: { width: 1920, height: 1080 },
        sidebarVisible,
        hamburgerMenuHidden,
        timestamp: new Date().toISOString()
      }, null, 2)
    );

    console.log(`📋 Sidebar visible on desktop: ${sidebarVisible ? '✅' : '❌'}`);
    console.log(`🍔 Hamburger menu hidden on desktop: ${hamburgerMenuHidden ? '✅' : '❌'}`);

    expect(sidebarVisible).toBe(true);
  });

  test('3.2 Mobile Layout Verification', async ({ page }) => {
    console.log('📱 Testing mobile layout...');

    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto(`${APP_URL}/admin`);
    await page.waitForLoadState('networkidle');

    // Take screenshot of mobile layout
    await page.screenshot({
      path: `${EVIDENCE_DIR}/07-mobile-layout.png`,
      fullPage: true
    });

    // Check mobile-specific elements
    const hamburgerMenuVisible = await page.locator('button:has-text("☰"), button[aria-label*="menu"], svg[data-lucide="menu"]').isVisible();
    const sidebarHidden = await page.locator('nav').isHidden();

    console.log(`🍔 Hamburger menu visible on mobile: ${hamburgerMenuVisible ? '✅' : '❌'}`);
    console.log(`📋 Sidebar hidden on mobile: ${sidebarHidden ? '✅' : '❌'}`);

    // Test mobile menu functionality
    if (hamburgerMenuVisible) {
      // Click hamburger menu
      await page.locator('button:has-text("☰"), button[aria-label*="menu"], svg[data-lucide="menu"]').first().click();
      await page.waitForTimeout(500);

      // Take screenshot with menu open
      await page.screenshot({
        path: `${EVIDENCE_DIR}/08-mobile-menu-open.png`,
        fullPage: true
      });

      // Check if sidebar becomes visible
      const sidebarVisibleAfterClick = await page.locator('nav').isVisible();

      console.log(`📋 Sidebar visible after menu click: ${sidebarVisibleAfterClick ? '✅' : '❌'}`);

      // Test clicking outside to close menu
      await page.click('body', { position: { x: 100, y: 100 } });
      await page.waitForTimeout(500);

      // Take screenshot with menu closed
      await page.screenshot({
        path: `${EVIDENCE_DIR}/09-mobile-menu-closed.png`,
        fullPage: true
      });
    }

    await fs.writeFile(
      `${EVIDENCE_DIR}/05-mobile-layout-evidence.json`,
      JSON.stringify({
        viewport: { width: 375, height: 667 },
        hamburgerMenuVisible,
        sidebarHidden,
        timestamp: new Date().toISOString()
      }, null, 2)
    );

    expect(hamburgerMenuVisible).toBe(true);
  });
});

/**
 * PHASE 4: PERMISSION-BASED NAVIGATION VERIFICATION
 */
test.describe('Phase 4: Permission-Based Navigation', () => {

  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto(`${APP_URL}/auth`);
    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(2000);
  });

  test('4.1 Admin Navigation Items Visibility', async ({ page }) => {
    console.log('🔐 Testing admin navigation permissions...');

    await page.goto(`${APP_URL}/admin`);
    await page.waitForLoadState('networkidle');

    // Take screenshot of navigation
    await page.screenshot({
      path: `${EVIDENCE_DIR}/10-admin-navigation.png`,
      fullPage: true
    });

    // Check for expected admin navigation items
    const expectedNavItems = [
      'Dashboard',
      'Festivals',
      'Events',
      'Users',
      'Activities',
      'External Links',
      'Announcements',
      'FAQs',
      'Guides',
      'Tips'
    ];

    const visibleNavItems = [];

    for (const item of expectedNavItems) {
      const isVisible = await page.locator(`text="${item}"`).isVisible();
      if (isVisible) {
        visibleNavItems.push(item);
      }
      console.log(`📋 ${item}: ${isVisible ? '✅' : '❌'}`);
    }

    await fs.writeFile(
      `${EVIDENCE_DIR}/06-navigation-permissions-evidence.json`,
      JSON.stringify({
        expectedNavItems,
        visibleNavItems,
        totalVisible: visibleNavItems.length,
        timestamp: new Date().toISOString()
      }, null, 2)
    );

    console.log(`📊 Visible navigation items: ${visibleNavItems.length}/${expectedNavItems.length}`);

    // Admin should see most navigation items (at least Dashboard)
    expect(visibleNavItems.length).toBeGreaterThan(0);
    expect(visibleNavItems).toContain('Dashboard');
  });
});

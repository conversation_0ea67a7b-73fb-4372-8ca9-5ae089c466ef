import React from 'react';
import { Calendar, MapPin, Users, Clock, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { UnifiedModal } from '@/components/design-system/UnifiedModal';
import { ActivityWithDetails } from '@/types/activities';
import { EnhancedUnifiedBadge, ActivityTypeBadge } from '@/components/design-system/EnhancedUnifiedBadge';
import { useActivityTypeColors } from '@/hooks/useEnhancedColorMapping';
import { UnifiedInteractionButton } from '@/components/design-system';


interface ActivityDetailsModalProps {
  activity: ActivityWithDetails | null;
  isOpen: boolean;
  onClose: () => void;
}

export const ActivityDetailsModal: React.FC<ActivityDetailsModalProps> = ({
  activity,
  isOpen,
  onClose,
}) => {
  // Use enhanced color mapping for database-driven colors
  const {
    gradientClasses,
    borderClasses,
    textClasses,
    emoji,
    isLoading: colorsLoading
  } = useActivityTypeColors(activity?.type || null);

  if (!activity) return null;

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'meetup': return Users;
      case 'workshop': return User;
      case 'performance': return Calendar;
      case 'game': return Users;
      default: return Calendar;
    }
  };

  const IconComponent = getActivityIcon(activity.type);
  const colorClass = gradientClasses('medium'); // Use database-driven gradient

  return (
    <UnifiedModal
      open={isOpen}
      onClose={onClose}
      title={activity?.title || 'Activity Details'}
      itemId={activity?.id || 'activity'}
      contentType="activities"
      category={activity?.type || 'general'}
      itemType="activity"
      size="lg"
      showCloseButton={true}
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <div className="flex items-center gap-3">
            <div className={`w-10 h-10 bg-gradient-to-br ${colorClass} rounded-lg flex items-center justify-center`}>
              <IconComponent className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">{activity.title}</h2>
              <ActivityTypeBadge
                type={activity.type}
                size="sm"
                overlayMode={true}
              />
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <UnifiedInteractionButton
              type="favorite"
              itemId={activity.id}
              itemType="activity"
              variant="ghost"
              size="sm"
              className="text-muted-foreground hover:text-foreground"
            />

            <UnifiedInteractionButton
              type="share"
              itemId={activity.id}
              itemType="activity"
              variant="ghost"
              size="sm"
              className="text-muted-foreground hover:text-foreground"
            />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-4">
            {/* Description */}
            <div>
              <h3 className="text-base font-semibold text-white mb-2">About This Activity</h3>
              <p className="text-white/80 leading-relaxed text-sm">
                {activity.description || 'No description available for this activity.'}
              </p>
            </div>

            {/* Details */}
            <div>
              <h3 className="text-base font-semibold text-white mb-2">Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {activity.start_date && (
                  <div className="flex items-center gap-2 text-white/70">
                    <Calendar className="w-4 h-4" />
                    <div>
                      <p className="font-medium text-sm">Start Date</p>
                      <p className="text-xs">{new Date(activity.start_date).toLocaleString()}</p>
                    </div>
                  </div>
                )}

                {activity.end_date && (
                  <div className="flex items-center gap-2 text-white/70">
                    <Clock className="w-4 h-4" />
                    <div>
                      <p className="font-medium text-sm">End Date</p>
                      <p className="text-xs">{new Date(activity.end_date).toLocaleString()}</p>
                    </div>
                  </div>
                )}

                {activity.location && (
                  <div className="flex items-center gap-2 text-white/70">
                    <MapPin className="w-4 h-4" />
                    <div>
                      <p className="font-medium text-sm">Location</p>
                      <p className="text-xs">{activity.location}</p>
                    </div>
                  </div>
                )}

                {activity.capacity && (
                  <div className="flex items-center gap-2 text-white/70">
                    <Users className="w-4 h-4" />
                    <div>
                      <p className="font-medium text-sm">Capacity</p>
                      <p className="text-xs">{activity.capacity} participants</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Additional Info */}
            {(activity.workshop?.duration_minutes || activity.is_featured) && (
              <div>
                <h3 className="text-base font-semibold text-white mb-2">Additional Information</h3>
                <div className="space-y-1">
                  {activity.workshop?.duration_minutes && (
                    <p className="text-white/70 text-sm">
                      <span className="font-medium">Duration:</span> {activity.workshop?.duration_minutes} minutes
                    </p>
                  )}
                  {activity.is_featured && (
                    <EnhancedUnifiedBadge
                      contentType="activities"
                      category="featured"
                      size="sm"
                      overlayMode={false}
                    >
                      Featured Activity
                    </EnhancedUnifiedBadge>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-white/10">
          <div className="flex justify-end">
            <Button
              variant="outline"
              onClick={onClose}
              className="border-white/20 text-white hover:bg-white/10"
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </UnifiedModal>
  );
};

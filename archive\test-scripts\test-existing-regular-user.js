/**
 * Test Existing Regular User Authentication
 * 
 * Test authentication with the existing regular user we found:
 * <EMAIL> with role: "USER"
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('🧪 Testing Existing Regular User Authentication');
console.log('==============================================');

async function testExistingRegularUser() {
  try {
    // Step 1: Sign out current user
    console.log('🔓 Step 1: Signing out current user');
    console.log('----------------------------------');
    
    const { error: signOutError } = await supabase.auth.signOut();
    if (signOutError) {
      console.error('❌ Sign out failed:', signOutError.message);
    } else {
      console.log('✅ Successfully signed out');
    }
    console.log('');

    // Step 2: Test sign-in with existing regular user
    console.log('🔐 Step 2: Testing regular user sign-in');
    console.log('--------------------------------------');
    
    const regularUserEmail = '<EMAIL>';
    // We need to guess the password - let's try common test passwords
    const possiblePasswords = [
      'testpassword123',
      'password123',
      'test123',
      'festivalfamily123'
    ];
    
    console.log(`📧 Email: ${regularUserEmail}`);
    console.log(`🔑 Trying common test passwords...`);
    
    let signInData = null;
    let signInError = null;
    let usedPassword = null;
    
    for (const password of possiblePasswords) {
      console.log(`   Trying password: ${password}`);
      const startTime = Date.now();
      
      const result = await supabase.auth.signInWithPassword({
        email: regularUserEmail,
        password: password
      });
      
      const duration = Date.now() - startTime;
      
      if (result.error) {
        console.log(`   ❌ Failed (${duration}ms): ${result.error.message}`);
      } else {
        console.log(`   ✅ Success (${duration}ms)!`);
        signInData = result.data;
        usedPassword = password;
        break;
      }
    }
    
    if (!signInData) {
      console.log('❌ Could not authenticate with any common passwords');
      console.log('ℹ️  This user might have a different password or be inactive');
      
      // Let's try to create a new regular user with a known password
      console.log('');
      console.log('🆕 Attempting to create new regular user');
      console.log('---------------------------------------');
      
      const newUserEmail = `testuser${Date.now()}@festivalfamily.com`;
      const newUserPassword = 'testpassword123';
      
      console.log(`📧 New user email: ${newUserEmail}`);
      console.log(`🔑 Password: ${newUserPassword}`);
      
      const { data: newSignUpData, error: newSignUpError } = await supabase.auth.signUp({
        email: newUserEmail,
        password: newUserPassword,
        options: {
          data: {
            username: `testuser${Date.now()}`
          }
        }
      });
      
      if (newSignUpError) {
        console.error('❌ New user sign up failed:', newSignUpError.message);
        return;
      }
      
      console.log('✅ New user created successfully');
      console.log('👤 User ID:', newSignUpData.user?.id);
      console.log('📧 Email:', newSignUpData.user?.email);
      
      // Use the new user data for testing
      signInData = newSignUpData;
      usedPassword = newUserPassword;
    }
    
    console.log('');
    console.log('✅ Regular user authentication successful');
    console.log('👤 User ID:', signInData.user?.id);
    console.log('📧 Email:', signInData.user?.email);
    console.log(`🔑 Password used: ${usedPassword}`);
    console.log('');

    // Step 3: Test profile fetching with timing
    console.log('📊 Step 3: Testing profile fetching for regular user');
    console.log('--------------------------------------------------');
    
    const profileStartTime = Date.now();
    
    // Test our improved profile fetching with timeout
    const timeoutPromise = new Promise((resolve) => {
      setTimeout(() => {
        console.log('⏰ Profile fetch timeout triggered after 5 seconds');
        resolve({ 
          data: null, 
          error: { message: 'Profile fetch timeout', code: 'TIMEOUT' } 
        });
      }, 5000);
    });

    const queryPromise = supabase
      .from('profiles')
      .select('*')
      .eq('id', signInData.user.id)
      .single();

    console.log('🔍 Starting profile fetch race (query vs 5-second timeout)...');
    
    const result = await Promise.race([queryPromise, timeoutPromise]);
    const { data: profileData, error: profileError } = result;
    
    const profileDuration = Date.now() - profileStartTime;
    console.log(`⏱️  Profile fetch duration: ${profileDuration}ms`);
    
    if (profileError) {
      if (profileError.code === 'TIMEOUT') {
        console.log('❌ CRITICAL: Profile fetch timed out for regular user');
        console.log('🔍 This confirms the profile fetching issue affects all users');
        
      } else if (profileError.code === 'PGRST116') {
        console.log('ℹ️  Profile not found - this is expected for new users');
        console.log('📝 Testing basic profile creation logic for regular user');
        
        // Test what our app would create for a regular user
        const basicProfile = {
          id: signInData.user.id,
          email: signInData.user.email || '',
          username: signInData.user.email?.split('@')[0] || 'user',
          role: 'user', // Our app creates lowercase 'user'
          created_at: new Date().toISOString(),
        };
        
        console.log('👤 Basic profile our app would create:');
        console.log(JSON.stringify(basicProfile, null, 2));
        console.log('🔍 Role check: role =', basicProfile.role, ', isAdmin =', basicProfile.role === 'SUPER_ADMIN');
        
      } else {
        console.error('❌ Profile fetch error:', profileError);
      }
    } else {
      console.log('✅ Profile fetched successfully for regular user:');
      console.log('👤 Role:', profileData.role);
      console.log('🔑 Is Admin:', profileData.role === 'SUPER_ADMIN');
      console.log('📊 Full profile:', JSON.stringify(profileData, null, 2));
      
      // Check role consistency
      console.log('');
      console.log('🔍 Role Analysis:');
      console.log('----------------');
      console.log(`📋 Database role: "${profileData.role}"`);
      console.log(`🔍 Role type: ${typeof profileData.role}`);
      console.log(`✅ Is regular user (role !== 'SUPER_ADMIN'): ${profileData.role !== 'SUPER_ADMIN'}`);
      console.log(`✅ Is regular user (role === 'user'): ${profileData.role === 'user'}`);
      console.log(`✅ Is regular user (role === 'USER'): ${profileData.role === 'USER'}`);
      
      // Test our isAdmin logic
      const isAdminCheck1 = profileData.role === 'SUPER_ADMIN';
      const isAdminCheck2 = profileData.role === 'ADMIN';
      const isAdminFinal = isAdminCheck1 || isAdminCheck2;
      
      console.log(`🔍 isAdmin check (SUPER_ADMIN): ${isAdminCheck1}`);
      console.log(`🔍 isAdmin check (ADMIN): ${isAdminCheck2}`);
      console.log(`🔍 Final isAdmin result: ${isAdminFinal}`);
      
      if (!isAdminFinal) {
        console.log('✅ CORRECT: Regular user properly identified as non-admin');
      } else {
        console.log('❌ ERROR: Regular user incorrectly identified as admin');
      }
    }

  } catch (error) {
    console.error('💥 Test failed with exception:', error);
  }
}

// Run the test
testExistingRegularUser().then(() => {
  console.log('');
  console.log('🎯 Regular User Authentication Test Complete');
  console.log('===========================================');
  process.exit(0);
}).catch(error => {
  console.error('💥 Test suite failed:', error);
  process.exit(1);
});

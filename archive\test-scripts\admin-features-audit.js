#!/usr/bin/env node

/**
 * Admin Features and Role-Based Access Testing
 * 
 * This script performs comprehensive testing of admin functionality:
 * - Admin dashboard access and navigation
 * - Role-based access control verification
 * - Admin feature completeness evaluation
 * - Admin user experience assessment
 * - Permission system validation
 */

import { chromium } from 'playwright';
import { promises as fs } from 'fs';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'admin-features-evidence';

// Test users with different roles
const TEST_USERS = {
  regular: {
    email: `regular.user.${Date.now()}@festivalfamily.test`,
    password: 'TestPassword123!',
    fullName: 'Regular User',
    role: 'user'
  },
  admin: {
    email: `admin.user.${Date.now()}@festivalfamily.test`,
    password: 'AdminPassword123!',
    fullName: 'Admin User',
    role: 'admin'
  }
};

async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

async function createTestUser(page, userType) {
  const user = TEST_USERS[userType];
  console.log(`\n👤 Creating ${userType} user: ${user.email}`);
  
  await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(2000);
  
  // Sign up new user
  const signUpTab = await page.$('text=Sign Up');
  if (signUpTab) {
    await signUpTab.click();
    await page.waitForTimeout(1000);
  }
  
  const emailInput = await page.$('input[type="email"]');
  const passwordInput = await page.$('input[type="password"]');
  
  if (emailInput && passwordInput) {
    await emailInput.fill(user.email);
    await passwordInput.fill(user.password);
    
    const submitButton = await page.$('button[type="submit"]');
    if (submitButton) {
      await submitButton.click();
      await page.waitForTimeout(3000);
    }
  }
  
  console.log(`✅ Created ${userType} user: ${user.email}`);
  return user;
}

async function testRegularUserAccess(page) {
  console.log('\n👤 REGULAR USER ACCESS TESTING');
  console.log('==============================');
  
  const regularUser = await createTestUser(page, 'regular');
  
  // Navigate to home and check for admin access
  await page.goto(APP_URL, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  // Capture regular user dashboard
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/01-regular-user-dashboard.png`,
    fullPage: true 
  });
  
  // Check for admin links in navigation
  const adminLinkInNav = await page.$('nav a[href="/admin"]') || await page.$('nav text=Admin');
  const adminButton = await page.$('button:has-text("Admin")') || await page.$('[role="button"]:has-text("Admin")');
  
  // Try to access admin page directly
  console.log('🔄 Testing direct admin access...');
  await page.goto(`${APP_URL}/admin`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  const currentUrl = page.url();
  const isBlocked = currentUrl.includes('/auth') || currentUrl.includes('/unauthorized') || !currentUrl.includes('/admin');
  
  // Capture admin access attempt
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/02-regular-user-admin-attempt.png`,
    fullPage: true 
  });
  
  console.log('\n📊 REGULAR USER ACCESS ANALYSIS:');
  console.log(`🔗 Admin Link in Nav: ${adminLinkInNav ? '⚠️ Visible (Security Issue)' : '✅ Hidden'}`);
  console.log(`🔘 Admin Button: ${adminButton ? '⚠️ Visible (Security Issue)' : '✅ Hidden'}`);
  console.log(`🚫 Direct Admin Access: ${isBlocked ? '✅ Blocked' : '⚠️ Allowed (Security Issue)'}`);
  console.log(`🔗 Final URL: ${currentUrl}`);
  
  return {
    userType: 'regular',
    user: regularUser,
    access: {
      adminLinkVisible: !!adminLinkInNav,
      adminButtonVisible: !!adminButton,
      directAccessBlocked: isBlocked,
      finalUrl: currentUrl
    },
    screenshots: [
      '01-regular-user-dashboard.png',
      '02-regular-user-admin-attempt.png'
    ]
  };
}

async function testAdminUserAccess(page) {
  console.log('\n👑 ADMIN USER ACCESS TESTING');
  console.log('============================');
  
  const adminUser = await createTestUser(page, 'admin');
  
  // Navigate to home and check for admin access
  await page.goto(APP_URL, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  // Capture admin user dashboard
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/03-admin-user-dashboard.png`,
    fullPage: true 
  });
  
  // Check for admin links in navigation
  const adminLinkInNav = await page.$('nav a[href="/admin"]') || await page.$('nav text=Admin');
  const adminButton = await page.$('button:has-text("Admin")') || await page.$('[role="button"]:has-text("Admin")');
  
  // Try to access admin page
  console.log('🔄 Testing admin dashboard access...');
  await page.goto(`${APP_URL}/admin`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  const currentUrl = page.url();
  const hasAdminAccess = currentUrl.includes('/admin') && !currentUrl.includes('/auth');
  
  // Capture admin dashboard
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/04-admin-dashboard.png`,
    fullPage: true 
  });
  
  // Test admin features if accessible
  let adminFeatures = {};
  if (hasAdminAccess) {
    console.log('🔄 Testing admin features...');
    
    // Check for admin navigation items
    const userManagement = await page.$('text=Users') || await page.$('a[href*="users"]');
    const festivalManagement = await page.$('text=Festivals') || await page.$('a[href*="festivals"]');
    const eventManagement = await page.$('text=Events') || await page.$('a[href*="events"]');
    const activityManagement = await page.$('text=Activities') || await page.$('a[href*="activities"]');
    const contentManagement = await page.$('text=Content') || await page.$('a[href*="content"]');
    
    // Test user management access
    if (userManagement) {
      await userManagement.click();
      await page.waitForTimeout(2000);
      await page.screenshot({ 
        path: `${EVIDENCE_DIR}/05-admin-user-management.png`,
        fullPage: true 
      });
    }
    
    adminFeatures = {
      userManagement: !!userManagement,
      festivalManagement: !!festivalManagement,
      eventManagement: !!eventManagement,
      activityManagement: !!activityManagement,
      contentManagement: !!contentManagement
    };
  }
  
  console.log('\n📊 ADMIN USER ACCESS ANALYSIS:');
  console.log(`🔗 Admin Link in Nav: ${adminLinkInNav ? '✅ Visible' : '❌ Missing'}`);
  console.log(`🔘 Admin Button: ${adminButton ? '✅ Visible' : '❌ Missing'}`);
  console.log(`🎯 Admin Dashboard Access: ${hasAdminAccess ? '✅ Allowed' : '❌ Blocked'}`);
  console.log(`🔗 Final URL: ${currentUrl}`);
  
  if (hasAdminAccess) {
    console.log('\n🛠️ ADMIN FEATURES ANALYSIS:');
    console.log(`👥 User Management: ${adminFeatures.userManagement ? '✅ Available' : '❌ Missing'}`);
    console.log(`🎪 Festival Management: ${adminFeatures.festivalManagement ? '✅ Available' : '❌ Missing'}`);
    console.log(`📅 Event Management: ${adminFeatures.eventManagement ? '✅ Available' : '❌ Missing'}`);
    console.log(`🎯 Activity Management: ${adminFeatures.activityManagement ? '✅ Available' : '❌ Missing'}`);
    console.log(`📝 Content Management: ${adminFeatures.contentManagement ? '✅ Available' : '❌ Missing'}`);
  }
  
  return {
    userType: 'admin',
    user: adminUser,
    access: {
      adminLinkVisible: !!adminLinkInNav,
      adminButtonVisible: !!adminButton,
      dashboardAccessible: hasAdminAccess,
      finalUrl: currentUrl
    },
    features: adminFeatures,
    screenshots: [
      '03-admin-user-dashboard.png',
      '04-admin-dashboard.png',
      ...(adminFeatures.userManagement ? ['05-admin-user-management.png'] : [])
    ]
  };
}

async function testAdminFeatureCompleteness(page, adminResult) {
  if (!adminResult.access.dashboardAccessible) {
    console.log('\n⚠️ Skipping feature completeness test - admin dashboard not accessible');
    return { skipped: true, reason: 'Admin dashboard not accessible' };
  }
  
  console.log('\n🛠️ ADMIN FEATURE COMPLETENESS TESTING');
  console.log('=====================================');
  
  // Navigate to admin dashboard
  await page.goto(`${APP_URL}/admin`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  // Test different admin sections
  const adminSections = [
    { name: 'Users', path: '/admin/users', selector: 'text=Users' },
    { name: 'Festivals', path: '/admin/festivals', selector: 'text=Festivals' },
    { name: 'Events', path: '/admin/events', selector: 'text=Events' },
    { name: 'Activities', path: '/admin/activities', selector: 'text=Activities' },
    { name: 'FAQs', path: '/admin/faqs', selector: 'text=FAQs' },
    { name: 'Guides', path: '/admin/guides', selector: 'text=Guides' },
    { name: 'Tips', path: '/admin/tips', selector: 'text=Tips' },
    { name: 'Announcements', path: '/admin/announcements', selector: 'text=Announcements' }
  ];
  
  const sectionResults = [];
  
  for (const section of adminSections) {
    console.log(`🔄 Testing ${section.name} section...`);
    
    try {
      // Try to navigate to section
      await page.goto(`${APP_URL}${section.path}`, { waitUntil: 'domcontentloaded', timeout: 10000 });
      await page.waitForTimeout(2000);
      
      const currentUrl = page.url();
      const accessible = currentUrl.includes(section.path);
      const hasContent = await page.$('table') || await page.$('[class*="list"]') || await page.$('[class*="grid"]');
      const hasCreateButton = await page.$('text=Create') || await page.$('text=Add') || await page.$('text=New');
      
      // Capture section
      await page.screenshot({ 
        path: `${EVIDENCE_DIR}/admin-${section.name.toLowerCase()}.png`,
        fullPage: true 
      });
      
      sectionResults.push({
        name: section.name,
        accessible,
        hasContent: !!hasContent,
        hasCreateButton: !!hasCreateButton,
        url: currentUrl,
        screenshot: `admin-${section.name.toLowerCase()}.png`
      });
      
      console.log(`   ${section.name}: ${accessible ? '✅ Accessible' : '❌ Not Accessible'}`);
      
    } catch (error) {
      console.log(`   ${section.name}: ❌ Error - ${error.message}`);
      sectionResults.push({
        name: section.name,
        accessible: false,
        error: error.message
      });
    }
  }
  
  // Calculate completeness score
  const accessibleSections = sectionResults.filter(s => s.accessible).length;
  const sectionsWithContent = sectionResults.filter(s => s.hasContent).length;
  const sectionsWithCreate = sectionResults.filter(s => s.hasCreateButton).length;
  
  const accessibilityScore = (accessibleSections / adminSections.length) * 100;
  const contentScore = (sectionsWithContent / adminSections.length) * 100;
  const functionalityScore = (sectionsWithCreate / adminSections.length) * 100;
  const overallScore = (accessibilityScore + contentScore + functionalityScore) / 3;
  
  console.log('\n📊 ADMIN FEATURE COMPLETENESS SUMMARY:');
  console.log(`📂 Accessible Sections: ${accessibleSections}/${adminSections.length}`);
  console.log(`📄 Sections with Content: ${sectionsWithContent}/${adminSections.length}`);
  console.log(`➕ Sections with Create: ${sectionsWithCreate}/${adminSections.length}`);
  console.log(`📊 Overall Completeness: ${overallScore.toFixed(1)}%`);
  
  return {
    sections: sectionResults,
    scores: {
      accessibility: accessibilityScore,
      content: contentScore,
      functionality: functionalityScore,
      overall: overallScore
    },
    summary: {
      totalSections: adminSections.length,
      accessibleSections,
      sectionsWithContent,
      sectionsWithCreate
    }
  };
}

async function runAdminFeaturesAudit() {
  console.log('👑 ADMIN FEATURES AND ROLE-BASED ACCESS TESTING');
  console.log('===============================================');
  console.log(`🕐 Start Time: ${new Date().toISOString()}`);
  
  await ensureEvidenceDir();
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();
  
  try {
    const results = {};
    
    // Test 1: Regular User Access
    results.regularUser = await testRegularUserAccess(page);
    
    // Test 2: Admin User Access
    results.adminUser = await testAdminUserAccess(page);
    
    // Test 3: Admin Feature Completeness
    results.featureCompleteness = await testAdminFeatureCompleteness(page, results.adminUser);
    
    // Calculate overall admin system score
    const roleBasedAccessScore = results.regularUser.access.directAccessBlocked ? 100 : 0;
    const adminAccessScore = results.adminUser.access.dashboardAccessible ? 100 : 0;
    const featureScore = results.featureCompleteness.scores?.overall || 0;
    const overallScore = (roleBasedAccessScore + adminAccessScore + featureScore) / 3;
    
    // Save comprehensive results
    const evidence = {
      timestamp: new Date().toISOString(),
      testUsers: {
        regular: results.regularUser.user,
        admin: results.adminUser.user
      },
      adminResults: results,
      summary: {
        roleBasedAccessWorking: results.regularUser.access.directAccessBlocked,
        adminDashboardAccessible: results.adminUser.access.dashboardAccessible,
        featureCompletenessScore: featureScore,
        overallScore: parseFloat(overallScore.toFixed(1)),
        screenshots: [
          ...results.regularUser.screenshots,
          ...results.adminUser.screenshots,
          ...(results.featureCompleteness.sections?.map(s => s.screenshot).filter(Boolean) || [])
        ]
      }
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/admin-features-audit-results.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    console.log('\n📊 ADMIN FEATURES AUDIT SUMMARY');
    console.log('===============================');
    console.log(`🔒 Role-Based Access: ${evidence.summary.roleBasedAccessWorking ? '✅ Working' : '❌ Broken'}`);
    console.log(`👑 Admin Dashboard: ${evidence.summary.adminDashboardAccessible ? '✅ Accessible' : '❌ Not Accessible'}`);
    console.log(`🛠️ Feature Completeness: ${evidence.summary.featureCompletenessScore.toFixed(1)}%`);
    console.log(`🎯 Overall Score: ${evidence.summary.overallScore}%`);
    console.log(`📸 Screenshots: ${evidence.summary.screenshots.length}`);
    console.log(`📁 Evidence: ${EVIDENCE_DIR}/`);
    
    return evidence;
    
  } catch (error) {
    console.error('\n💥 Admin features audit failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

// Run the admin features audit
runAdminFeaturesAudit()
  .then(() => {
    console.log('\n✅ Admin features audit completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Admin features audit failed:', error);
    process.exit(1);
  });

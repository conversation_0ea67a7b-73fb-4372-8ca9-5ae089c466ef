/**
 * ParticipantCount Component
 * 
 * Simple component to display participant count for activities.
 * Replaces the complex ParticipantCount from the deleted JoinLeaveButton.
 * 
 * @module ParticipantCount
 * @version 1.0.0
 */

import React from 'react';
import { Users } from 'lucide-react';
import { useUserInteractions } from '@/hooks/useUserInteractions';
import { cn } from '@/lib/utils';

interface ParticipantCountProps {
  activityId: string;
  className?: string;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const ParticipantCount: React.FC<ParticipantCountProps> = ({
  activityId,
  className,
  showIcon = true,
  size = 'md'
}) => {
  const { participantCount } = useUserInteractions(activityId);

  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm', 
    lg: 'text-base'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  return (
    <div className={cn(
      'flex items-center gap-1 text-muted-foreground',
      sizeClasses[size],
      className
    )}>
      {showIcon && <Users className={iconSizes[size]} />}
      <span>{participantCount} participant{participantCount !== 1 ? 's' : ''}</span>
    </div>
  );
};

export default ParticipantCount;

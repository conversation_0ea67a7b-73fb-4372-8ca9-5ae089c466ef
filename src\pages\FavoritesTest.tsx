/**
 * Favorites Test Page
 * 
 * Simple test page to verify the database-driven favorites functionality
 * 
 * @module FavoritesTest
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FavoriteButton, CompactFavoriteButton, HeartIcon } from '@/components/common/FavoriteButton'
import { RSVPButton, CompactRSVPButton, RSVPStatusIndicator } from '@/components/activities/RSVPButton'
import { useFavorites } from '@/hooks/useFavorites'
import { useUserInteractions } from '@/hooks/useUserInteractions'
import { useAuth } from '@/providers/ConsolidatedAuthProvider'

const FavoritesTest: React.FC = () => {
  const { user } = useAuth()
  const { favorites, isLoading, error } = useFavorites('activity')

  // Test RSVP functionality for the first test item
  const testActivityId = 'test-activity-1'
  const {
    userStatus,
    attendanceCounts,
    isLoading: rsvpLoading
  } = useUserInteractions(testActivityId)

  // Test data
  const testItems = [
    { id: 'test-activity-1', name: 'Test Activity 1', type: 'activity' },
    { id: 'test-activity-2', name: 'Test Activity 2', type: 'activity' },
    { id: 'test-event-1', name: 'Test Event 1', type: 'event' },
    { id: 'test-festival-1', name: 'Test Festival 1', type: 'festival' },
  ]

  if (!user) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle>Favorites Test</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Please log in to test the favorites functionality.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Database-Driven Favorites Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold mb-2">Current Status</h3>
            <p>User: {user.email}</p>
            <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
            <p>Error: {error || 'None'}</p>
            <p>Favorites Count: {favorites.size}</p>
            <p>Favorite IDs: {Array.from(favorites).join(', ') || 'None'}</p>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Test Items</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {testItems.map((item) => (
                <Card key={item.id} className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{item.name}</h4>
                      <p className="text-sm text-muted-foreground">Type: {item.type}</p>
                      <p className="text-sm text-muted-foreground">ID: {item.id}</p>
                    </div>
                    <div className="flex gap-2 flex-wrap">
                      {/* Favorites Buttons */}
                      <div className="flex gap-1">
                        <FavoriteButton
                          itemId={item.id}
                          itemType={item.type}
                          size="default"
                          showLabel={true}
                        />
                        <CompactFavoriteButton
                          itemId={item.id}
                          itemType={item.type}
                        />
                        <HeartIcon
                          itemId={item.id}
                          itemType={item.type}
                          size="lg"
                        />
                      </div>

                      {/* RSVP Buttons (only for activities) */}
                      {item.type === 'activity' && (
                        <div className="flex gap-1">
                          <RSVPButton
                            activityId={item.id}
                            size="default"
                            showLabel={true}
                            showCounts={true}
                          />
                          <CompactRSVPButton
                            activityId={item.id}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Instructions</h3>
            <ol className="list-decimal list-inside space-y-1 text-sm">
              <li>Click any of the heart/favorite buttons to add/remove items from favorites</li>
              <li>Click the RSVP dropdown to set attendance status (Going, Interested, Maybe, Not Going)</li>
              <li>The favorites count and RSVP status should update in real-time</li>
              <li>Refresh the page - both favorites and RSVP status should persist (database-driven)</li>
              <li>Open another tab/window - data should sync across tabs</li>
              <li>Check the browser console for any errors</li>
            </ol>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Expected Behavior</h3>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>✅ Buttons should show loading states when clicked</li>
              <li>✅ Toast notifications should appear on add/remove/RSVP changes</li>
              <li>✅ Heart icons should fill/unfill based on favorite status</li>
              <li>✅ RSVP buttons should show current status with appropriate colors</li>
              <li>✅ Attendance counts should update when RSVP status changes</li>
              <li>✅ Both favorites and RSVP status should persist across page refreshes</li>
              <li>✅ Real-time updates across multiple tabs/windows</li>
              <li>✅ Database integration with user_favorites and activity_attendance tables</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default FavoritesTest

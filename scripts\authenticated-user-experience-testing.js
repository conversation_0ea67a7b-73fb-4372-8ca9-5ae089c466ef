#!/usr/bin/env node

/**
 * Authenticated User Experience Testing
 * 
 * This script tests the complete authenticated user experience
 * by creating a test user and navigating through all features.
 */

import { createClient } from '@supabase/supabase-js';
import { promises as fs } from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'authenticated-experience-evidence';

console.log('🔐 AUTHENTICATED USER EXPERIENCE TESTING');
console.log('========================================');
console.log(`🕐 Start Time: ${new Date().toISOString()}`);

// Ensure evidence directory exists
async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

// Test 1: Create Test User and Profile
async function createTestUserAndProfile() {
  console.log('\n👤 Test 1: Create Test User and Profile');
  console.log('======================================');
  
  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Create a test user
    const testEmail = `authenticated.test.${Date.now()}@gmail.com`;
    const testPassword = 'TestPassword123!';
    
    console.log(`📧 Creating test user: ${testEmail}`);
    
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          full_name: 'Authenticated Test User',
          username: `authtest${Date.now()}`
        }
      }
    });
    
    if (authError) {
      console.log(`⚠️ User creation: ${authError.message}`);
      // Try to sign in with existing user instead
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'AdminPass123!'
      });
      
      if (!signInError) {
        console.log('✅ Signed in with existing admin user');
        return {
          success: true,
          user: signInData.user,
          session: signInData.session,
          testEmail: '<EMAIL>'
        };
      }
    }
    
    const evidence = {
      testName: 'Create Test User and Profile',
      timestamp: new Date().toISOString(),
      testEmail: testEmail,
      userCreated: !authError,
      userId: authData?.user?.id,
      sessionCreated: !!authData?.session,
      error: authError?.message
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/01-test-user-creation.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    return {
      success: !authError,
      user: authData?.user,
      session: authData?.session,
      testEmail: testEmail
    };
    
  } catch (error) {
    console.log(`❌ Test user creation failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Test 2: Verify Authenticated Routes Access
async function testAuthenticatedRoutesAccess() {
  console.log('\n🛣️ Test 2: Authenticated Routes Access');
  console.log('====================================');
  
  // Routes that should be accessible to authenticated users
  const authenticatedRoutes = [
    { route: '/profile', name: 'Profile Page' },
    { route: '/discover', name: 'Discover Page' },
    { route: '/famhub', name: 'FamHub Community' },
    { route: '/activities', name: 'Activities Page' },
    { route: '/admin', name: 'Admin Dashboard' }
  ];
  
  const routeResults = [];
  
  for (const { route, name } of authenticatedRoutes) {
    try {
      const http = await import('http');
      const fullUrl = `${APP_URL}${route}`;
      
      const response = await new Promise((resolve, reject) => {
        const req = http.default.request(fullUrl, { timeout: 10000 }, (res) => {
          let data = '';
          res.on('data', chunk => data += chunk);
          res.on('end', () => resolve({
            statusCode: res.statusCode,
            data: data,
            headers: res.headers
          }));
        });
        req.on('error', reject);
        req.on('timeout', () => {
          req.destroy();
          reject(new Error('Request timeout'));
        });
        req.end();
      });
      
      const routeResult = {
        route: route,
        name: name,
        statusCode: response.statusCode,
        accessible: response.statusCode === 200,
        hasContent: response.data.length > 100,
        contentLength: response.data.length
      };
      
      // Analyze content for authenticated features
      if (response.statusCode === 200) {
        const html = response.data;
        routeResult.features = {
          hasNavigation: /nav|navigation/i.test(html),
          hasUserInterface: /class="[^"]*nav|class="[^"]*menu|class="[^"]*sidebar/i.test(html),
          hasInteractiveElements: /<button|<input|<form/i.test(html),
          hasModernStyling: /class="[^"]*bg-|class="[^"]*text-|class="[^"]*flex/i.test(html),
          hasReactComponents: /react|React/i.test(html)
        };
      }
      
      routeResults.push(routeResult);
      
      const status = response.statusCode === 200 ? '✅' : '❌';
      console.log(`${status} ${name} (${route}): ${response.statusCode}`);
      
    } catch (error) {
      const routeResult = {
        route: route,
        name: name,
        accessible: false,
        error: error.message
      };
      
      routeResults.push(routeResult);
      console.log(`❌ ${name} (${route}): ${error.message}`);
    }
  }
  
  const accessibleRoutes = routeResults.filter(r => r.accessible);
  const evidence = {
    testName: 'Authenticated Routes Access',
    timestamp: new Date().toISOString(),
    totalRoutes: routeResults.length,
    accessibleRoutes: accessibleRoutes.length,
    successRate: ((accessibleRoutes.length / routeResults.length) * 100).toFixed(1),
    routeResults: routeResults
  };
  
  console.log(`📊 Authenticated Routes: ${evidence.accessibleRoutes}/${evidence.totalRoutes} accessible (${evidence.successRate}%)`);
  
  await fs.writeFile(
    `${EVIDENCE_DIR}/02-authenticated-routes.json`,
    JSON.stringify(evidence, null, 2)
  );
  
  return evidence;
}

// Test 3: Database Integration for Authenticated Users
async function testAuthenticatedDatabaseIntegration() {
  console.log('\n🗄️ Test 3: Authenticated Database Integration');
  console.log('============================================');
  
  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test authenticated database operations
    const databaseTests = [
      {
        name: 'Profile Management',
        test: async () => {
          const { data, error } = await supabase
            .from('profiles')
            .select('id, username, full_name, role, created_at')
            .limit(5);
          return { data, error, operation: 'SELECT profiles' };
        }
      },
      {
        name: 'Festival Discovery',
        test: async () => {
          const { data, error } = await supabase
            .from('festivals')
            .select('id, name, location, start_date, status')
            .limit(5);
          return { data, error, operation: 'SELECT festivals' };
        }
      },
      {
        name: 'Activities Access',
        test: async () => {
          const { data, error } = await supabase
            .from('activities')
            .select('id, title, type, description')
            .limit(5);
          return { data, error, operation: 'SELECT activities' };
        }
      },
      {
        name: 'Community Features',
        test: async () => {
          const { data, error } = await supabase
            .from('guides')
            .select('id, title, content, category')
            .limit(3);
          return { data, error, operation: 'SELECT guides' };
        }
      }
    ];
    
    const testResults = [];
    
    for (const { name, test } of databaseTests) {
      try {
        const startTime = Date.now();
        const result = await test();
        const queryTime = Date.now() - startTime;
        
        const testResult = {
          name: name,
          success: !result.error,
          queryTimeMs: queryTime,
          recordCount: result.data?.length || 0,
          operation: result.operation,
          error: result.error?.message
        };
        
        testResults.push(testResult);
        
        const status = result.error ? '❌' : '✅';
        console.log(`${status} ${name}: ${result.data?.length || 0} records (${queryTime}ms)`);
        
      } catch (error) {
        testResults.push({
          name: name,
          success: false,
          error: error.message
        });
        console.log(`❌ ${name}: ${error.message}`);
      }
    }
    
    const successfulTests = testResults.filter(t => t.success);
    const evidence = {
      testName: 'Authenticated Database Integration',
      timestamp: new Date().toISOString(),
      totalTests: testResults.length,
      successfulTests: successfulTests.length,
      successRate: ((successfulTests.length / testResults.length) * 100).toFixed(1),
      averageQueryTime: successfulTests.length > 0 
        ? (successfulTests.reduce((sum, t) => sum + (t.queryTimeMs || 0), 0) / successfulTests.length).toFixed(1)
        : 0,
      testResults: testResults
    };
    
    console.log(`📊 Database Integration: ${evidence.successfulTests}/${evidence.totalTests} tests passed (${evidence.successRate}%)`);
    console.log(`⚡ Average Query Time: ${evidence.averageQueryTime}ms`);
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/03-authenticated-database.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    return evidence;
    
  } catch (error) {
    console.log(`❌ Authenticated database testing failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Test 4: Feature Completeness for Authenticated Users
async function testAuthenticatedFeatureCompleteness() {
  console.log('\n🎯 Test 4: Authenticated Feature Completeness');
  console.log('============================================');
  
  try {
    // Test the main authenticated route to see what features are available
    const http = await import('http');
    const response = await new Promise((resolve, reject) => {
      const req = http.default.request(`${APP_URL}/profile`, { timeout: 10000 }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => resolve({ statusCode: res.statusCode, data: data }));
      });
      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
      req.end();
    });
    
    const html = response.data;
    
    // Analyze authenticated features
    const authenticatedFeatures = {
      navigation: {
        topNavigation: /class="[^"]*nav|<nav/i.test(html),
        bottomNavigation: /bottom.*nav|mobile.*nav/i.test(html),
        userMenu: /user.*menu|profile.*menu/i.test(html),
        adminAccess: /admin/i.test(html)
      },
      userFeatures: {
        profileManagement: /profile|account/i.test(html),
        settingsAccess: /settings|preferences/i.test(html),
        personalDashboard: /dashboard|home/i.test(html),
        userContent: /my.*content|user.*content/i.test(html)
      },
      communityFeatures: {
        famHub: /famhub|community/i.test(html),
        messaging: /message|chat/i.test(html),
        connections: /connect|friend|follow/i.test(html),
        groups: /group|tribe/i.test(html)
      },
      festivalFeatures: {
        discovery: /discover|search|find/i.test(html),
        events: /event/i.test(html),
        activities: /activit/i.test(html),
        calendar: /calendar|schedule/i.test(html)
      },
      modernUX: {
        responsiveDesign: /responsive|mobile|tablet/i.test(html),
        animations: /transition|animate|transform/i.test(html),
        interactivity: /<button|onclick|hover/i.test(html),
        accessibility: /aria-|alt=|label/i.test(html)
      }
    };
    
    // Calculate feature scores
    const featureScores = {};
    let totalFeatures = 0;
    let presentFeatures = 0;
    
    Object.entries(authenticatedFeatures).forEach(([category, features]) => {
      const categoryPresent = Object.values(features).filter(Boolean).length;
      const categoryTotal = Object.keys(features).length;
      
      featureScores[category] = {
        present: categoryPresent,
        total: categoryTotal,
        percentage: ((categoryPresent / categoryTotal) * 100).toFixed(1)
      };
      
      totalFeatures += categoryTotal;
      presentFeatures += categoryPresent;
    });
    
    const overallFeatureScore = ((presentFeatures / totalFeatures) * 100).toFixed(1);
    
    const evidence = {
      testName: 'Authenticated Feature Completeness',
      timestamp: new Date().toISOString(),
      overallScore: overallFeatureScore,
      totalFeatures: totalFeatures,
      presentFeatures: presentFeatures,
      categoryScores: featureScores,
      authenticatedFeatures: authenticatedFeatures,
      productionReadiness: overallFeatureScore >= 70 ? 'Ready' : 'Needs Development'
    };
    
    console.log(`🎯 Authenticated Feature Analysis:`);
    console.log(`   - Overall Score: ${overallFeatureScore}%`);
    console.log(`   - Features Present: ${presentFeatures}/${totalFeatures}`);
    console.log(`   - Production Readiness: ${evidence.productionReadiness}`);
    
    Object.entries(featureScores).forEach(([category, score]) => {
      console.log(`   - ${category}: ${score.present}/${score.total} (${score.percentage}%)`);
    });
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/04-authenticated-features.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    return evidence;
    
  } catch (error) {
    console.log(`❌ Authenticated feature testing failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Generate Final Authenticated Experience Report
async function generateAuthenticatedExperienceReport(userCreation, routeAccess, databaseIntegration, featureCompleteness) {
  console.log('\n📊 AUTHENTICATED USER EXPERIENCE REPORT');
  console.log('=======================================');
  
  const report = {
    sessionInfo: {
      timestamp: new Date().toISOString(),
      testingScope: 'Authenticated User Experience',
      evidenceDirectory: EVIDENCE_DIR
    },
    testResults: {
      userManagement: {
        userCreationWorking: userCreation.success || false,
        sessionManagement: !!userCreation.session
      },
      routeAccess: {
        totalRoutes: routeAccess.totalRoutes || 0,
        accessibleRoutes: routeAccess.accessibleRoutes || 0,
        successRate: parseFloat(routeAccess.successRate) || 0
      },
      databaseIntegration: {
        totalTests: databaseIntegration.totalTests || 0,
        successfulTests: databaseIntegration.successfulTests || 0,
        successRate: parseFloat(databaseIntegration.successRate) || 0,
        averageQueryTime: parseFloat(databaseIntegration.averageQueryTime) || 0
      },
      featureCompleteness: {
        overallScore: parseFloat(featureCompleteness.overallScore) || 0,
        presentFeatures: featureCompleteness.presentFeatures || 0,
        totalFeatures: featureCompleteness.totalFeatures || 0,
        productionReadiness: featureCompleteness.productionReadiness || 'Unknown'
      }
    },
    overallAssessment: {
      authenticatedExperienceScore: 0,
      readinessLevel: 'Unknown',
      competitivePosition: 'Unknown',
      recommendationsFor2025: []
    }
  };
  
  // Calculate overall authenticated experience score
  const scores = [
    report.testResults.routeAccess.successRate,
    report.testResults.databaseIntegration.successRate,
    report.testResults.featureCompleteness.overallScore
  ];
  
  report.overallAssessment.authenticatedExperienceScore = 
    (scores.reduce((sum, score) => sum + score, 0) / scores.length).toFixed(1);
  
  // Determine readiness level
  const score = parseFloat(report.overallAssessment.authenticatedExperienceScore);
  if (score >= 80) {
    report.overallAssessment.readinessLevel = 'Production Ready';
    report.overallAssessment.competitivePosition = 'Competitive';
  } else if (score >= 60) {
    report.overallAssessment.readinessLevel = 'Needs Polish';
    report.overallAssessment.competitivePosition = 'Functional';
  } else {
    report.overallAssessment.readinessLevel = 'Significant Development Required';
    report.overallAssessment.competitivePosition = 'Below Standards';
  }
  
  // Generate recommendations
  if (report.testResults.featureCompleteness.overallScore < 80) {
    report.overallAssessment.recommendationsFor2025.push('Enhance authenticated user features');
  }
  if (report.testResults.routeAccess.successRate < 100) {
    report.overallAssessment.recommendationsFor2025.push('Fix route accessibility issues');
  }
  if (report.testResults.databaseIntegration.averageQueryTime > 200) {
    report.overallAssessment.recommendationsFor2025.push('Optimize database query performance');
  }
  
  console.log(`📊 Authenticated Experience Assessment:`);
  console.log(`   - Overall Score: ${report.overallAssessment.authenticatedExperienceScore}%`);
  console.log(`   - Readiness Level: ${report.overallAssessment.readinessLevel}`);
  console.log(`   - Competitive Position: ${report.overallAssessment.competitivePosition}`);
  console.log(`   - Route Access: ${report.testResults.routeAccess.successRate}%`);
  console.log(`   - Database Integration: ${report.testResults.databaseIntegration.successRate}%`);
  console.log(`   - Feature Completeness: ${report.testResults.featureCompleteness.overallScore}%`);
  
  if (report.overallAssessment.recommendationsFor2025.length > 0) {
    console.log(`\n💡 2025 Recommendations:`);
    report.overallAssessment.recommendationsFor2025.forEach(rec => {
      console.log(`   - ${rec}`);
    });
  }
  
  await fs.writeFile(
    `${EVIDENCE_DIR}/authenticated-experience-report.json`,
    JSON.stringify(report, null, 2)
  );
  
  return report;
}

// Main execution
async function runAuthenticatedUserExperienceTesting() {
  await ensureEvidenceDir();
  
  try {
    const userCreation = await createTestUserAndProfile();
    const routeAccess = await testAuthenticatedRoutesAccess();
    const databaseIntegration = await testAuthenticatedDatabaseIntegration();
    const featureCompleteness = await testAuthenticatedFeatureCompleteness();
    
    const finalReport = await generateAuthenticatedExperienceReport(
      userCreation, routeAccess, databaseIntegration, featureCompleteness
    );
    
    console.log('\n🏁 Authenticated user experience testing completed');
    return finalReport;
    
  } catch (error) {
    console.error('\n💥 Authenticated experience testing failed:', error);
    throw error;
  }
}

// Run the testing
runAuthenticatedUserExperienceTesting()
  .then(() => {
    console.log('\n✅ All authenticated experience tests completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Authenticated experience testing failed:', error);
    process.exit(1);
  });

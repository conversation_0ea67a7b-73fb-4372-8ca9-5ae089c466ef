/**
 * Comprehensive Festival Family Implementation Validation
 * 
 * This script validates all newly implemented features including:
 * - Content Management System
 * - Emergency Management System
 * - Profile System with Database Integration
 * - User Preferences System
 * - Complete Admin Dashboard Functionality
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Comprehensive Festival Family Implementation Validation');
console.log('========================================================');

// Test 1: Content Management System Validation
async function validateContentManagementSystem() {
  console.log('📝 Test 1: Content Management System Validation');
  console.log('-----------------------------------------------');
  
  const results = {
    tableExists: false,
    crudOperations: { create: false, read: false, update: false, delete: false },
    contentTypes: [],
    sampleContent: [],
    timestamp: new Date().toISOString()
  };
  
  try {
    // Test table existence and structure
    const { data: contentSample, error: readError } = await supabase
      .from('content_management')
      .select('*')
      .limit(5);
    
    if (!readError) {
      results.tableExists = true;
      results.crudOperations.read = true;
      results.sampleContent = contentSample || [];
      
      // Get unique content types
      results.contentTypes = [...new Set(contentSample?.map(c => c.content_type) || [])];
      
      console.log('✅ Content Management table exists and readable');
      console.log(`📊 Found ${contentSample?.length || 0} content items`);
      console.log(`🏷️ Content types: ${results.contentTypes.join(', ')}`);
    } else {
      console.log('❌ Content Management table not accessible:', readError.message);
      return results;
    }
    
    // Test CREATE operation
    const testContent = {
      content_key: 'test_validation_content',
      content_type: 'ui_text',
      title: 'Test Content',
      content: 'This is a test content for validation purposes.',
      is_active: false,
      metadata: { test: true }
    };
    
    const { data: createdContent, error: createError } = await supabase
      .from('content_management')
      .insert([testContent])
      .select()
      .single();
    
    if (!createError && createdContent) {
      results.crudOperations.create = true;
      console.log('✅ Content CREATE operation working');
      
      // Test UPDATE operation
      const { error: updateError } = await supabase
        .from('content_management')
        .update({ title: 'Updated Test Content' })
        .eq('id', createdContent.id);
      
      if (!updateError) {
        results.crudOperations.update = true;
        console.log('✅ Content UPDATE operation working');
      }
      
      // Test DELETE operation
      const { error: deleteError } = await supabase
        .from('content_management')
        .delete()
        .eq('id', createdContent.id);
      
      if (!deleteError) {
        results.crudOperations.delete = true;
        console.log('✅ Content DELETE operation working');
      }
    } else {
      console.log('❌ Content CREATE operation failed:', createError?.message);
    }
    
  } catch (error) {
    console.log('💥 Content Management validation error:', error.message);
  }
  
  return results;
}

// Test 2: Emergency Management System Validation
async function validateEmergencyManagementSystem() {
  console.log('\n🚨 Test 2: Emergency Management System Validation');
  console.log('------------------------------------------------');
  
  const results = {
    emergencyContactsTable: false,
    safetyInformationTable: false,
    crudOperations: {
      contacts: { create: false, read: false, update: false, delete: false },
      safety: { create: false, read: false, update: false, delete: false }
    },
    timestamp: new Date().toISOString()
  };
  
  try {
    // Test emergency_contacts table
    const { data: contactsData, error: contactsError } = await supabase
      .from('emergency_contacts')
      .select('*')
      .limit(1);
    
    if (!contactsError) {
      results.emergencyContactsTable = true;
      results.crudOperations.contacts.read = true;
      console.log('✅ Emergency contacts table accessible');
    } else {
      console.log('❌ Emergency contacts table error:', contactsError.message);
    }
    
    // Test safety_information table
    const { data: safetyData, error: safetyError } = await supabase
      .from('safety_information')
      .select('*')
      .limit(1);
    
    if (!safetyError) {
      results.safetyInformationTable = true;
      results.crudOperations.safety.read = true;
      console.log('✅ Safety information table accessible');
    } else {
      console.log('❌ Safety information table error:', safetyError.message);
    }
    
    // Test emergency contact CRUD operations
    if (results.emergencyContactsTable) {
      // Get a festival ID for testing
      const { data: festivals } = await supabase
        .from('festivals')
        .select('id')
        .limit(1);
      
      if (festivals && festivals.length > 0) {
        const testContact = {
          festival_id: festivals[0].id,
          contact_type: 'festival_family',
          name: 'Test Emergency Contact',
          phone: '+1234567890',
          email: '<EMAIL>',
          description: 'Test contact for validation',
          is_active: false
        };
        
        const { data: createdContact, error: createContactError } = await supabase
          .from('emergency_contacts')
          .insert([testContact])
          .select()
          .single();
        
        if (!createContactError && createdContact) {
          results.crudOperations.contacts.create = true;
          console.log('✅ Emergency contact CREATE working');
          
          // Test update and delete
          const { error: updateContactError } = await supabase
            .from('emergency_contacts')
            .update({ name: 'Updated Test Contact' })
            .eq('id', createdContact.id);
          
          if (!updateContactError) {
            results.crudOperations.contacts.update = true;
            console.log('✅ Emergency contact UPDATE working');
          }
          
          const { error: deleteContactError } = await supabase
            .from('emergency_contacts')
            .delete()
            .eq('id', createdContact.id);
          
          if (!deleteContactError) {
            results.crudOperations.contacts.delete = true;
            console.log('✅ Emergency contact DELETE working');
          }
        }
      }
    }
    
  } catch (error) {
    console.log('💥 Emergency Management validation error:', error.message);
  }
  
  return results;
}

// Test 3: User Preferences System Validation
async function validateUserPreferencesSystem() {
  console.log('\n⚙️ Test 3: User Preferences System Validation');
  console.log('--------------------------------------------');
  
  const results = {
    tableExists: false,
    crudOperations: { create: false, read: false, update: false, delete: false },
    preferenceCategories: [],
    timestamp: new Date().toISOString()
  };
  
  try {
    // Test user_preferences table
    const { data: preferencesData, error: preferencesError } = await supabase
      .from('user_preferences')
      .select('*')
      .limit(5);
    
    if (!preferencesError) {
      results.tableExists = true;
      results.crudOperations.read = true;
      
      // Get unique preference categories
      results.preferenceCategories = [...new Set(preferencesData?.map(p => p.preference_category) || [])];
      
      console.log('✅ User preferences table accessible');
      console.log(`📊 Found ${preferencesData?.length || 0} preference records`);
      console.log(`🏷️ Categories: ${results.preferenceCategories.join(', ')}`);
    } else {
      console.log('❌ User preferences table error:', preferencesError.message);
      return results;
    }
    
    // Test CRUD operations with a test user
    const { data: testUsers } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (testUsers && testUsers.length > 0) {
      const testPreference = {
        user_id: testUsers[0].id,
        preference_category: 'notifications',
        preference_key: 'test_validation',
        preference_value: { enabled: true, test: true }
      };
      
      const { data: createdPreference, error: createError } = await supabase
        .from('user_preferences')
        .insert([testPreference])
        .select()
        .single();
      
      if (!createError && createdPreference) {
        results.crudOperations.create = true;
        console.log('✅ User preference CREATE working');
        
        // Test update
        const { error: updateError } = await supabase
          .from('user_preferences')
          .update({ preference_value: { enabled: false, test: true } })
          .eq('id', createdPreference.id);
        
        if (!updateError) {
          results.crudOperations.update = true;
          console.log('✅ User preference UPDATE working');
        }
        
        // Test delete
        const { error: deleteError } = await supabase
          .from('user_preferences')
          .delete()
          .eq('id', createdPreference.id);
        
        if (!deleteError) {
          results.crudOperations.delete = true;
          console.log('✅ User preference DELETE working');
        }
      }
    }
    
  } catch (error) {
    console.log('💥 User Preferences validation error:', error.message);
  }
  
  return results;
}

// Test 4: Profile System Database Integration
async function validateProfileSystemIntegration() {
  console.log('\n👤 Test 4: Profile System Database Integration');
  console.log('---------------------------------------------');
  
  const results = {
    profileFieldsAvailable: [],
    updateFunctionality: false,
    avatarStorage: false,
    timestamp: new Date().toISOString()
  };
  
  try {
    // Check profiles table structure
    const { data: profileSample } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (profileSample && profileSample.length > 0) {
      results.profileFieldsAvailable = Object.keys(profileSample[0]);
      console.log('✅ Profile fields available:', results.profileFieldsAvailable.join(', '));
      
      // Check if extended fields exist
      const extendedFields = ['bio', 'website', 'interests'];
      const hasExtendedFields = extendedFields.some(field => 
        results.profileFieldsAvailable.includes(field)
      );
      
      if (hasExtendedFields) {
        console.log('✅ Extended profile fields detected');
      } else {
        console.log('⚠️ Extended profile fields (bio, website, interests) not found');
      }
    }
    
    // Test profile update functionality
    const { data: testUser } = await supabase
      .from('profiles')
      .select('id, full_name')
      .limit(1)
      .single();
    
    if (testUser) {
      const originalName = testUser.full_name;
      const testName = `Test Update ${Date.now()}`;
      
      // Test update
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ full_name: testName })
        .eq('id', testUser.id);
      
      if (!updateError) {
        results.updateFunctionality = true;
        console.log('✅ Profile update functionality working');
        
        // Restore original name
        await supabase
          .from('profiles')
          .update({ full_name: originalName })
          .eq('id', testUser.id);
      } else {
        console.log('❌ Profile update failed:', updateError.message);
      }
    }
    
    // Test avatar storage bucket
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (!bucketsError && buckets) {
      const avatarBucket = buckets.find(bucket => bucket.name === 'avatars');
      if (avatarBucket) {
        results.avatarStorage = true;
        console.log('✅ Avatar storage bucket exists');
      } else {
        console.log('⚠️ Avatar storage bucket not found');
      }
    }
    
  } catch (error) {
    console.log('💥 Profile System validation error:', error.message);
  }
  
  return results;
}

// Test 5: Admin Dashboard Completeness
async function validateAdminDashboardCompleteness() {
  console.log('\n👑 Test 5: Admin Dashboard Completeness');
  console.log('--------------------------------------');
  
  const results = {
    adminTables: {},
    adminFunctions: {},
    contentManagementReady: false,
    emergencyManagementReady: false,
    timestamp: new Date().toISOString()
  };
  
  const adminTables = [
    'announcements', 'tips', 'faqs', 'content_management', 
    'emergency_contacts', 'safety_information', 'user_preferences'
  ];
  
  // Test all admin-related tables
  for (const table of adminTables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*', { count: 'exact' })
        .limit(1);
      
      results.adminTables[table] = {
        accessible: !error,
        recordCount: error ? 0 : (data?.length || 0),
        error: error?.message
      };
      
      if (!error) {
        console.log(`✅ ${table}: Accessible`);
      } else {
        console.log(`❌ ${table}: ${error.message}`);
      }
    } catch (err) {
      results.adminTables[table] = {
        accessible: false,
        error: err.message
      };
    }
  }
  
  // Test admin functions
  const adminFunctions = ['is_admin', 'is_content_admin', 'can_manage_groups'];
  
  for (const func of adminFunctions) {
    try {
      const { data, error } = await supabase.rpc(func);
      
      results.adminFunctions[func] = {
        working: !error,
        result: data,
        error: error?.message
      };
      
      if (!error) {
        console.log(`✅ ${func}: Working`);
      } else {
        console.log(`❌ ${func}: ${error.message}`);
      }
    } catch (err) {
      results.adminFunctions[func] = {
        working: false,
        error: err.message
      };
    }
  }
  
  // Assess overall readiness
  const workingTables = Object.values(results.adminTables).filter(t => t.accessible).length;
  const workingFunctions = Object.values(results.adminFunctions).filter(f => f.working).length;
  
  results.contentManagementReady = 
    results.adminTables.content_management?.accessible &&
    results.adminTables.announcements?.accessible &&
    results.adminTables.tips?.accessible &&
    results.adminTables.faqs?.accessible;
  
  results.emergencyManagementReady = 
    results.adminTables.emergency_contacts?.accessible &&
    results.adminTables.safety_information?.accessible;
  
  console.log(`📊 Admin Tables Working: ${workingTables}/${adminTables.length}`);
  console.log(`⚙️ Admin Functions Working: ${workingFunctions}/${adminFunctions.length}`);
  console.log(`📝 Content Management Ready: ${results.contentManagementReady ? 'YES' : 'NO'}`);
  console.log(`🚨 Emergency Management Ready: ${results.emergencyManagementReady ? 'YES' : 'NO'}`);
  
  return results;
}

// Main execution
async function main() {
  try {
    console.log(`🕐 Started at: ${new Date().toISOString()}\n`);
    
    // Run all validation tests
    const contentResults = await validateContentManagementSystem();
    const emergencyResults = await validateEmergencyManagementSystem();
    const preferencesResults = await validateUserPreferencesSystem();
    const profileResults = await validateProfileSystemIntegration();
    const adminResults = await validateAdminDashboardCompleteness();
    
    // Compile comprehensive results
    const validationResults = {
      validationSuite: 'Comprehensive Festival Family Implementation Validation',
      timestamp: new Date().toISOString(),
      contentManagementSystem: contentResults,
      emergencyManagementSystem: emergencyResults,
      userPreferencesSystem: preferencesResults,
      profileSystemIntegration: profileResults,
      adminDashboardCompleteness: adminResults,
      overallAssessment: {
        implementationComplete: false,
        productionReady: false,
        criticalIssues: [],
        completionPercentage: 0
      }
    };
    
    // Calculate overall assessment
    let completedFeatures = 0;
    const totalFeatures = 5;
    
    if (contentResults.tableExists && Object.values(contentResults.crudOperations).every(Boolean)) {
      completedFeatures++;
    } else {
      validationResults.overallAssessment.criticalIssues.push('Content Management System incomplete');
    }
    
    if (emergencyResults.emergencyContactsTable && emergencyResults.safetyInformationTable) {
      completedFeatures++;
    } else {
      validationResults.overallAssessment.criticalIssues.push('Emergency Management System incomplete');
    }
    
    if (preferencesResults.tableExists && Object.values(preferencesResults.crudOperations).every(Boolean)) {
      completedFeatures++;
    } else {
      validationResults.overallAssessment.criticalIssues.push('User Preferences System incomplete');
    }
    
    if (profileResults.updateFunctionality) {
      completedFeatures++;
    } else {
      validationResults.overallAssessment.criticalIssues.push('Profile System integration incomplete');
    }
    
    if (adminResults.contentManagementReady && adminResults.emergencyManagementReady) {
      completedFeatures++;
    } else {
      validationResults.overallAssessment.criticalIssues.push('Admin Dashboard incomplete');
    }
    
    validationResults.overallAssessment.completionPercentage = (completedFeatures / totalFeatures) * 100;
    validationResults.overallAssessment.implementationComplete = completedFeatures === totalFeatures;
    validationResults.overallAssessment.productionReady = 
      validationResults.overallAssessment.implementationComplete && 
      validationResults.overallAssessment.criticalIssues.length === 0;
    
    // Save results
    const resultsDir = 'comprehensive-implementation-validation-results';
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      `${resultsDir}/implementation-validation-${Date.now()}.json`,
      JSON.stringify(validationResults, null, 2)
    );
    
    console.log('\n🎉 COMPREHENSIVE IMPLEMENTATION VALIDATION SUMMARY');
    console.log('==================================================');
    
    console.log('\n📝 CONTENT MANAGEMENT SYSTEM:');
    console.log(`   Table Exists: ${contentResults.tableExists ? 'YES' : 'NO'}`);
    console.log(`   CRUD Operations: ${Object.values(contentResults.crudOperations).filter(Boolean).length}/4 working`);
    console.log(`   Content Types: ${contentResults.contentTypes.length}`);
    
    console.log('\n🚨 EMERGENCY MANAGEMENT SYSTEM:');
    console.log(`   Emergency Contacts: ${emergencyResults.emergencyContactsTable ? 'YES' : 'NO'}`);
    console.log(`   Safety Information: ${emergencyResults.safetyInformationTable ? 'YES' : 'NO'}`);
    
    console.log('\n⚙️ USER PREFERENCES SYSTEM:');
    console.log(`   Table Exists: ${preferencesResults.tableExists ? 'YES' : 'NO'}`);
    console.log(`   CRUD Operations: ${Object.values(preferencesResults.crudOperations).filter(Boolean).length}/4 working`);
    
    console.log('\n👤 PROFILE SYSTEM INTEGRATION:');
    console.log(`   Update Functionality: ${profileResults.updateFunctionality ? 'YES' : 'NO'}`);
    console.log(`   Avatar Storage: ${profileResults.avatarStorage ? 'YES' : 'NO'}`);
    console.log(`   Extended Fields: ${profileResults.profileFieldsAvailable.length} available`);
    
    console.log('\n👑 ADMIN DASHBOARD COMPLETENESS:');
    console.log(`   Content Management Ready: ${adminResults.contentManagementReady ? 'YES' : 'NO'}`);
    console.log(`   Emergency Management Ready: ${adminResults.emergencyManagementReady ? 'YES' : 'NO'}`);
    
    console.log('\n🎯 OVERALL ASSESSMENT:');
    console.log(`   Implementation Complete: ${validationResults.overallAssessment.implementationComplete ? 'YES' : 'NO'}`);
    console.log(`   Completion Percentage: ${validationResults.overallAssessment.completionPercentage}%`);
    console.log(`   Production Ready: ${validationResults.overallAssessment.productionReady ? 'YES' : 'NO'}`);
    
    if (validationResults.overallAssessment.criticalIssues.length > 0) {
      console.log('\n🚨 CRITICAL ISSUES:');
      validationResults.overallAssessment.criticalIssues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`);
      });
    }
    
    console.log(`\n📁 Results saved to: ${resultsDir}/implementation-validation-${Date.now()}.json`);
    
  } catch (error) {
    console.error('💥 Comprehensive implementation validation failed:', error);
  }
  
  process.exit(0);
}

main();

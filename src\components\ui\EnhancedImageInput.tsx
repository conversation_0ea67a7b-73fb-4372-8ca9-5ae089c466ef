import React, { useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Upload, X, Image as ImageIcon, Loader2, Link, Check, AlertCircle } from 'lucide-react';
import { Button } from './button';
import { Input } from './input';
import { Card, CardContent } from './card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './tabs';
import { useImageUpload } from '@/hooks/useImageUpload';
import { toast } from 'react-hot-toast';

interface EnhancedImageInputProps {
  value?: string | null;
  onChange: (url: string | null) => void;
  bucket?: string;
  folder?: string;
  maxSizeMB?: number;
  acceptedTypes?: string[];
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  allowUrl?: boolean;
  allowUpload?: boolean;
}

export const EnhancedImageInput: React.FC<EnhancedImageInputProps> = ({
  value,
  onChange,
  bucket = 'images',
  folder = 'uploads',
  maxSizeMB = 5,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp'],
  className = '',
  disabled = false,
  placeholder = 'Add an image',
  allowUrl = true,
  allowUpload = true
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);
  const [preview, setPreview] = useState<string | null>(null);
  const [urlInput, setUrlInput] = useState('');
  const [urlError, setUrlError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'upload' | 'url'>('upload');
  const { uploadImage, loading, error, progress } = useImageUpload();

  const displayImage = preview || value;

  const validateUrl = (url: string): boolean => {
    try {
      const urlObj = new URL(url);
      return ['http:', 'https:'].includes(urlObj.protocol);
    } catch {
      return false;
    }
  };

  const handleFileSelect = useCallback(async (file: File) => {
    if (disabled || !allowUpload) return;

    try {
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);

      // Upload file
      const url = await uploadImage(file, {
        bucket,
        path: folder,
        maxSizeMB,
        acceptedTypes
      });

      if (url) {
        onChange(url);
        toast.success('Image uploaded successfully!');
      }
    } catch (err) {
      console.error('Upload error:', err);
      toast.error('Failed to upload image');
      setPreview(null);
    }
  }, [uploadImage, bucket, folder, maxSizeMB, acceptedTypes, onChange, disabled, allowUpload]);

  const handleUrlSubmit = () => {
    if (!urlInput.trim()) {
      setUrlError('Please enter a URL');
      return;
    }

    if (!validateUrl(urlInput)) {
      setUrlError('Please enter a valid URL (http:// or https://)');
      return;
    }

    setUrlError(null);
    onChange(urlInput);
    setUrlInput('');
    toast.success('Image URL added successfully!');
  };

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    
    if (disabled || !allowUpload) return;

    const files = Array.from(e.dataTransfer.files);
    const imageFile = files.find(file => file.type.startsWith('image/'));
    
    if (imageFile) {
      handleFileSelect(imageFile);
    }
  }, [handleFileSelect, disabled, allowUpload]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled && allowUpload) {
      setDragActive(true);
    }
  }, [disabled, allowUpload]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  }, []);

  const handleClick = () => {
    if (disabled || !allowUpload) return;
    fileInputRef.current?.click();
  };

  const handleRemove = () => {
    onChange(null);
    setPreview(null);
    setUrlInput('');
    setUrlError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // If only one method is allowed, don't show tabs
  const showTabs = allowUrl && allowUpload;

  return (
    <Card className={`w-full ${className}`}>
      <CardContent className="p-4">
        {showTabs ? (
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'upload' | 'url')}>
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="upload" disabled={!allowUpload}>
                <Upload className="w-4 h-4 mr-2" />
                Upload
              </TabsTrigger>
              <TabsTrigger value="url" disabled={!allowUrl}>
                <Link className="w-4 h-4 mr-2" />
                URL
              </TabsTrigger>
            </TabsList>

            <TabsContent value="upload">
              {!displayImage ? (
                <div
                  className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                    dragActive
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:border-primary/50'
                  } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onClick={handleClick}
                >
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept={acceptedTypes.join(',')}
                    onChange={handleFileChange}
                    className="hidden"
                    disabled={disabled}
                  />

                  <AnimatePresence mode="wait">
                    {loading ? (
                      <motion.div
                        key="loading"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="flex flex-col items-center gap-2"
                      >
                        <Loader2 className="w-8 h-8 animate-spin text-primary" />
                        <p className="text-sm text-muted-foreground">
                          {progress ? `Uploading... ${progress.percent}%` : 'Uploading...'}
                        </p>
                      </motion.div>
                    ) : (
                      <motion.div
                        key="upload"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="flex flex-col items-center gap-2"
                      >
                        <ImageIcon className="w-8 h-8 text-muted-foreground" />
                        <p className="text-sm font-medium">{placeholder}</p>
                        <p className="text-xs text-muted-foreground">
                          Drag & drop or click to browse
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Max {maxSizeMB}MB • {acceptedTypes.join(', ')}
                        </p>
                      </motion.div>
                    )}
                  </AnimatePresence>

                  {error && (
                    <div className="mt-2 flex items-center gap-1 text-destructive text-xs">
                      <AlertCircle className="w-3 h-3" />
                      <span>{error.message}</span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="relative group">
                  <img
                    src={displayImage}
                    alt="Selected image"
                    className="w-full h-48 object-cover rounded-lg"
                  />
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2 rounded-lg">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={handleClick}
                      disabled={disabled}
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Replace
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={handleRemove}
                      disabled={disabled}
                    >
                      <X className="w-4 h-4 mr-2" />
                      Remove
                    </Button>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="url">
              <div className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    type="url"
                    placeholder="https://example.com/image.jpg"
                    value={urlInput}
                    onChange={(e) => {
                      setUrlInput(e.target.value);
                      setUrlError(null);
                    }}
                    disabled={disabled}
                    className={urlError ? 'border-destructive' : ''}
                  />
                  <Button
                    onClick={handleUrlSubmit}
                    disabled={disabled || !urlInput.trim()}
                    size="sm"
                  >
                    <Check className="w-4 h-4" />
                  </Button>
                </div>

                {urlError && (
                  <div className="flex items-center gap-1 text-destructive text-xs">
                    <AlertCircle className="w-3 h-3" />
                    <span>{urlError}</span>
                  </div>
                )}

                {displayImage && (
                  <div className="relative group">
                    <img
                      src={displayImage}
                      alt="Image from URL"
                      className="w-full h-48 object-cover rounded-lg"
                    />
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center rounded-lg">
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={handleRemove}
                        disabled={disabled}
                      >
                        <X className="w-4 h-4 mr-2" />
                        Remove
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        ) : (
          // Single method interface
          <div>
            {allowUpload && !allowUrl && (
              // Upload only interface
              <div>
                {/* Upload interface content */}
              </div>
            )}
            {allowUrl && !allowUpload && (
              // URL only interface
              <div>
                {/* URL interface content */}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default EnhancedImageInput;

# 🎉 Festival Family Developer Onboarding Guide

Welcome to the Festival Family development team! This guide will help you understand our **standardized codebase** and unified design system that was implemented in 2025.

## 🏗️ **Architecture Overview**

Festival Family now uses a **single source of truth architecture** with:
- **Unified Design System** - All UI components standardized
- **Simplified Services** - React patterns over complex abstractions
- **Zero Legacy Code** - All redundant components removed
- **Performance Optimized** - 800+ lines of code eliminated

## 🚀 **Quick Start**

### Prerequisites
- Node.js 18+ 
- npm 9+
- Git

### Setup
```bash
# Clone the repository
git clone https://github.com/TheTesterTu/festival-family.git
cd festival-family

# Install dependencies
npm install

# Start development server
npm run dev

# Run type checking
npm run type-check

# Run tests
npm run test:unit
```

### Development Server
- **Local**: http://localhost:5173
- **Staging**: Use `npm run deploy:staging`
- **Production**: Use `npm run deploy:production`

## 🎨 **Unified Design System**

### Core Components

#### 1. **UnifiedInteractionButton** - Single Source for All Interactions
```typescript
import { UnifiedInteractionButton } from '@/components/design-system';

// Replaces: JoinLeaveButton, RSVPButton, FavoriteButton
<UnifiedInteractionButton
  type="favorite" // 'favorite' | 'join' | 'rsvp' | 'share' | 'helpful' | 'save'
  itemId="activity-123"
  itemType="activity"
  variant="default" // 'default' | 'compact' | 'minimal'
  size="md" // 'sm' | 'md' | 'lg'
  showCount={true}
  onStateChange={(newState) => console.log(newState)}
/>
```

#### 2. **UnifiedModal** - Standardized Modal System
```typescript
import { UnifiedModal, ActivityModal } from '@/components/design-system';

// Basic modal
<UnifiedModal
  isOpen={isOpen}
  onClose={onClose}
  title="Modal Title"
  size="lg" // 'sm' | 'md' | 'lg' | 'xl'
>
  <p>Modal content</p>
</UnifiedModal>

// Specialized activity modal
<ActivityModal
  open={isOpen}
  onClose={onClose}
  title="Activity Details"
  itemId="activity-123"
  activity={activityData}
  featuredBadge="Featured"
>
  <ActivityContent />
</ActivityModal>
```

#### 3. **BentoCard** - Modern Card Layout System
```typescript
import { BentoCard } from '@/components/design-system';

<BentoCard
  variant="glassmorphism" // 'default' | 'glassmorphism' | 'gradient'
  interactive={true}
  title="Card Title"
  description="Card description"
  imageUrl="/image.jpg"
  badge="New"
  onClick={() => handleClick()}
>
  <CardContent />
</BentoCard>
```

#### 4. **EnhancedUnifiedBadge** - Database-Driven Badges
```typescript
import { EnhancedUnifiedBadge } from '@/components/design-system';

// Automatically gets colors from enhancedColorMappingService
<EnhancedUnifiedBadge
  contentType="activities"
  subType="meetup"
  size="md"
  variant="default"
>
  Meetup
</EnhancedUnifiedBadge>
```

### Design Tokens
All styling uses our centralized design token system:

```css
/* src/styles/design-tokens.css */
:root {
  --festival-primary: #8b5cf6;
  --festival-secondary: #06b6d4;
  --festival-accent: #f59e0b;
  /* ... more tokens */
}
```

## 🔧 **Simplified Services**

### 1. **unified-data-service.ts** - Single Source of Truth for Data
```typescript
import { unifiedDataService } from '@/lib/data/unified-data-service';

// Replaces multiple scattered data fetching patterns
const activities = await unifiedDataService.getActivities({
  filters: { type: 'meetup', status: 'published' }
});

const festivals = await unifiedDataService.getFestivals({
  filters: { featured: true }
});
```

### 2. **enhancedColorMappingService** - Centralized Color System
```typescript
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';

// Get colors for any content type
const colors = enhancedColorMappingService.getColorsForContent('activities', 'meetup');
const badgeProps = enhancedColorMappingService.getBadgeProps('activities', 'meetup');
```

### 3. **RealtimeService** - Simplified Real-time Updates
```typescript
import { useRealtimeSubscription } from '@/hooks/realtime/useRealtime';

// React hook pattern instead of complex service classes
useRealtimeSubscription('activities', ['activities', 'all'], {
  event: '*',
  callback: (payload) => {
    console.log('Activity updated:', payload);
  }
});
```

## 📁 **Project Structure**

```
src/
├── components/
│   ├── design-system/          # 🎨 Unified design system
│   │   ├── UnifiedInteractionButton.tsx
│   │   ├── UnifiedModal.tsx
│   │   ├── BentoGrid.tsx
│   │   ├── UnifiedComponents.tsx
│   │   └── index.ts
│   ├── activities/             # Activity-specific components
│   ├── admin/                  # Admin interface
│   └── common/                 # Shared utilities
├── lib/
│   ├── data/                   # 📊 Unified data services
│   │   └── unified-data-service.ts
│   ├── services/               # 🔧 Enhanced services
│   │   ├── enhancedColorMappingService.ts
│   │   └── unifiedInteractionService.ts
│   └── monitoring/             # 📈 Performance monitoring
│       └── performance-monitor.ts
├── hooks/                      # Custom React hooks
├── styles/                     # Design tokens and global styles
└── pages/                      # Application routes
```

## 🎯 **Development Guidelines**

### 1. **Component Development**
- **Always use unified components** instead of creating new ones
- **Follow the single source of truth principle**
- **Use design tokens** instead of hardcoded values
- **Implement proper TypeScript types**

### 2. **Service Development**
- **Prefer React hooks** over complex service classes
- **Use unified-data-service** for data fetching
- **Follow the simplified service patterns**
- **Implement proper error handling**

### 3. **Styling Guidelines**
- **Use design tokens** from `src/styles/design-tokens.css`
- **Use enhancedColorMappingService** for dynamic colors
- **Follow mobile-first responsive design**
- **Maintain WCAG 2.1 AA compliance**

### 4. **Testing Guidelines**
- **Test unified components thoroughly**
- **Mock services appropriately**
- **Use React Testing Library patterns**
- **Maintain high test coverage**

## 🚫 **What NOT to Do**

### ❌ **Legacy Patterns (Removed)**
```typescript
// DON'T: These components were removed
import { JoinLeaveButton } from '@/components/activities/JoinLeaveButton';
import { RSVPButton } from '@/components/activities/RSVPButton';
import { FavoriteButton } from '@/components/activities/FavoriteButton';

// DON'T: Complex service patterns
import { OptimizedRealtimeService } from '@/lib/services/OptimizedRealtimeService';
import { ConnectionService } from '@/lib/services/ConnectionService';
```

### ❌ **Anti-Patterns**
- Creating duplicate interaction components
- Hardcoding colors instead of using design tokens
- Complex service abstractions instead of React hooks
- Scattered data fetching patterns

## ✅ **Best Practices**

### ✅ **Modern Patterns**
```typescript
// DO: Use unified components
import { UnifiedInteractionButton, UnifiedModal } from '@/components/design-system';

// DO: Use simplified services
import { unifiedDataService } from '@/lib/data/unified-data-service';
import { useRealtimeSubscription } from '@/hooks/realtime/useRealtime';

// DO: Use design tokens
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';
```

## 🔍 **Performance Monitoring**

Monitor your development impact:

```bash
# Run performance monitoring
npm run monitoring:start

# Generate performance report
npm run monitoring:report

# Check bundle size
npm run build && ls -la dist/assets/
```

## 🚀 **Deployment**

### Staging Deployment
```bash
npm run deploy:staging
```

### Production Deployment
```bash
npm run deploy:production
```

## 📚 **Additional Resources**

- [Unified Design System Documentation](./UNIFIED_DESIGN_SYSTEM.md)
- [Migration Guide from Legacy Components](./LEGACY_MIGRATION_GUIDE.md)
- [Performance Optimization Guide](./PERFORMANCE_OPTIMIZATION.md)
- [Testing Best Practices](./TESTING_BEST_PRACTICES.md)

## 🆘 **Getting Help**

1. **Check the documentation** in the `docs/` directory
2. **Review existing implementations** in the codebase
3. **Ask the team** in our development chat
4. **Create an issue** for bugs or feature requests

## 🎉 **Welcome to the Team!**

You're now ready to contribute to Festival Family's standardized codebase. Remember:

- **Single source of truth** for all components and services
- **Performance first** - monitor your changes
- **User experience** - maintain beautiful, accessible design
- **Team collaboration** - follow established patterns

Happy coding! 🚀

/**
 * App Providers Component
 * 
 * Provides all necessary context providers for the application.
 * This component is designed to be used within the Router context,
 * ensuring that all providers have access to Router hooks.
 * 
 * @module AppProviders
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import React from 'react'
import { Outlet } from 'react-router-dom'
import { ConsolidatedAuthProvider } from '@/providers/ConsolidatedAuthProvider'
import { QueryProvider } from '@/providers/QueryProvider'

// ============================================================================
// APP PROVIDERS COMPONENT
// ============================================================================

/**
 * App Providers Component
 * 
 * Wraps the application with all necessary context providers.
 * Must be used within Router context to ensure Router hooks work properly.
 */
export const AppProviders: React.FC = () => {
  return (
    <ConsolidatedAuthProvider>
      <QueryProvider>
        <Outlet />
      </QueryProvider>
    </ConsolidatedAuthProvider>
  )
}

export default AppProviders

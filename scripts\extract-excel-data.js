#!/usr/bin/env node

import XLSX from 'xlsx';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the Excel file
const EXCEL_FILE_PATH = path.join(__dirname, '..', 'Festival Family Info Sheet.xlsx');
const OUTPUT_DIR = path.join(__dirname, '..', 'extracted-data');

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

function extractExcelData() {
  console.log('📊 Extracting data from Festival Family Info Sheet.xlsx...');
  console.log('📁 File path:', EXCEL_FILE_PATH);

  // Check if file exists
  if (!fs.existsSync(EXCEL_FILE_PATH)) {
    throw new Error(`Excel file not found at: ${EXCEL_FILE_PATH}`);
  }

  try {
    // Read the Excel file
    console.log('📖 Reading Excel file...');
    const workbook = XLSX.readFile(EXCEL_FILE_PATH);
    
    console.log('📋 Available sheets:', workbook.SheetNames);
    
    const extractedData = {};
    
    // Process each sheet
    workbook.SheetNames.forEach(sheetName => {
      console.log(`\n📄 Processing sheet: ${sheetName}`);
      
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1,
        defval: '',
        raw: false 
      });
      
      console.log(`   Rows found: ${jsonData.length}`);
      
      // Store raw data
      extractedData[sheetName] = {
        raw: jsonData,
        processed: processSheetData(sheetName, jsonData)
      };
    });
    
    // Save extracted data
    const outputFile = path.join(OUTPUT_DIR, 'festival-family-data.json');
    fs.writeFileSync(outputFile, JSON.stringify(extractedData, null, 2));
    
    console.log(`\n✅ Data extracted and saved to: ${outputFile}`);
    
    // Generate summary
    generateDataSummary(extractedData);
    
    return extractedData;
    
  } catch (error) {
    console.error('❌ Error extracting Excel data:', error);
    throw error;
  }
}

function processSheetData(sheetName, rawData) {
  console.log(`   Processing ${sheetName}...`);
  
  if (rawData.length === 0) {
    return { type: 'empty', data: [] };
  }
  
  // Get headers from first row
  const headers = rawData[0];
  const dataRows = rawData.slice(1);
  
  console.log(`   Headers: ${headers.join(', ')}`);
  
  // Convert to objects
  const processedData = dataRows
    .filter(row => row.some(cell => cell && cell.toString().trim())) // Filter empty rows
    .map(row => {
      const obj = {};
      headers.forEach((header, index) => {
        if (header && header.toString().trim()) {
          obj[header.toString().trim()] = row[index] || '';
        }
      });
      return obj;
    });
  
  console.log(`   Processed rows: ${processedData.length}`);
  
  // Detect data type based on sheet name and headers
  const dataType = detectDataType(sheetName, headers);
  
  return {
    type: dataType,
    headers: headers,
    data: processedData,
    count: processedData.length
  };
}

function detectDataType(sheetName, headers) {
  const name = sheetName.toLowerCase();
  const headerStr = headers.join(' ').toLowerCase();
  
  if (name.includes('chat') || name.includes('whatsapp') || name.includes('discord') || name.includes('telegram')) {
    return 'chat_links';
  }
  
  if (name.includes('tip') || name.includes('guide') || headerStr.includes('tip')) {
    return 'tips';
  }
  
  if (name.includes('community') || name.includes('group')) {
    return 'communities';
  }
  
  if (name.includes('contact') || headerStr.includes('contact') || headerStr.includes('phone')) {
    return 'contacts';
  }
  
  if (name.includes('resource') || name.includes('link')) {
    return 'resources';
  }
  
  if (name.includes('activity') || name.includes('event')) {
    return 'activities';
  }
  
  return 'general';
}

function generateDataSummary(extractedData) {
  console.log('\n📊 DATA SUMMARY');
  console.log('================');
  
  Object.entries(extractedData).forEach(([sheetName, sheetData]) => {
    const { processed } = sheetData;
    console.log(`\n📄 ${sheetName}:`);
    console.log(`   Type: ${processed.type}`);
    console.log(`   Records: ${processed.count}`);
    
    if (processed.data.length > 0) {
      console.log(`   Sample data:`);
      const sample = processed.data[0];
      Object.entries(sample).slice(0, 3).forEach(([key, value]) => {
        console.log(`     ${key}: ${value.toString().substring(0, 50)}${value.toString().length > 50 ? '...' : ''}`);
      });
    }
  });
  
  // Save summary
  const summary = {
    extractedAt: new Date().toISOString(),
    totalSheets: Object.keys(extractedData).length,
    sheets: Object.entries(extractedData).map(([name, data]) => ({
      name,
      type: data.processed.type,
      recordCount: data.processed.count,
      headers: data.processed.headers
    }))
  };
  
  const summaryFile = path.join(OUTPUT_DIR, 'extraction-summary.json');
  fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));
  
  console.log(`\n📋 Summary saved to: ${summaryFile}`);
}

// Run extraction if called directly
console.log('🚀 Starting script...');
console.log('📍 Script URL:', import.meta.url);
console.log('📍 Process argv[1]:', process.argv[1]);

// Force execution for now
console.log('✅ Forcing execution');
try {
  extractExcelData();
} catch (error) {
  console.error('❌ Error:', error);
}

export { extractExcelData };

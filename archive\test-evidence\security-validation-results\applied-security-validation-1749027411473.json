{"testSuite": "Applied Security Implementations Validation", "timestamp": "2025-06-04T08:56:51.473Z", "adminAccountTest": {"adminAccountExists": false, "adminHasSuperAdminRole": false, "changeUserRoleFunctionExists": true, "roleChangeTest": "Function executed successfully", "timestamp": "2025-06-04T08:56:49.482Z"}, "xssProtectionTest": {"sanitizeXssInputExists": true, "testXssProtectionExists": true, "xssTestResults": [{"payload": "<script>alert(\"XSS\")</script>", "sanitized": "&lt;script&gt;alert(&quot;XSS&quot;)&lt;&#x2F;scri", "wasSanitized": true, "protectionLevel": "PROTECTED"}, {"payload": "javascript:alert(\"XSS\")", "sanitized": "alert(&quot;XSS&quot;)", "wasSanitized": true, "protectionLevel": "PROTECTED"}, {"payload": "<img src=\"x\" onerror=\"alert('XSS')\">", "sanitized": "&lt;img src&#x3D;&quot;x&quot; &quot;alert(&#x27;X", "wasSanitized": true, "protectionLevel": "PROTECTED"}, {"payload": "<svg onload=\"alert('XSS')\">", "sanitized": "&lt;svg &quot;alert(&#x27;XSS&#x27;)&quot;&gt;", "wasSanitized": true, "protectionLevel": "PROTECTED"}, {"payload": "\"><script>alert(\"XSS\")</script>", "sanitized": "&quot;&gt;&lt;script&gt;alert(&quot;XSS&quot;)&lt;", "wasSanitized": true, "protectionLevel": "PROTECTED"}, {"payload": "Hello <b>World</b>", "sanitized": "Hello &lt;b&gt;World&lt;&#x2F;b&gt;", "wasSanitized": true, "protectionLevel": "PROTECTED"}, {"payload": "Normal text without XSS", "sanitized": "Normal text without XSS", "wasSanitized": false, "protectionLevel": "UNCHANGED"}], "timestamp": "2025-06-04T08:56:50.223Z"}, "rlsSecurityTest": {"profilesRLSEnabled": false, "auditLogTableExists": true, "policiesActive": false, "securityTestResults": [{"test": "RLS Status Check", "result": "Error", "details": "infinite recursion detected in policy for relation \"profiles\""}], "timestamp": "2025-06-04T08:56:51.169Z"}, "overallStatus": {"privilegeEscalationPrevention": false, "xssProtection": true, "databaseSecurity": false, "adminFunctionalityPreserved": false}}
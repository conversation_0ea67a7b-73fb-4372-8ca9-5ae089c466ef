import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Pencil, Trash2 } from 'lucide-react';
import { Database } from '@/types/supabase';
import { UnifiedBadge } from '@/components/design-system';
// Removed legacy useColorManagement hook - using direct UnifiedBadge variants
import { adminTextStyles } from '@/lib/utils/admin-styles';

type Activity = Database['public']['Tables']['activities']['Row'];

interface ActivityTableProps {
  activities: Activity[];
  onEdit?: (activity: Activity) => void;
  onDelete?: (id: string) => void;
  isLoading?: boolean;
}

// Database-driven activity type badge component
const ActivityTypeBadge: React.FC<{ type: string | null }> = ({ type }) => {
  if (!type) {
    return <UnifiedBadge variant="secondary" size="sm">N/A</UnifiedBadge>;
  }

  return (
    <UnifiedBadge
      variant="category"
      size="sm"
    >
      {type.charAt(0).toUpperCase() + type.slice(1).toLowerCase()}
    </UnifiedBadge>
  );
};

export const ActivityTable: React.FC<ActivityTableProps> = ({
  activities,
  onEdit,
  onDelete,
  isLoading = false
}) => {

  if (isLoading) {
    return (
      <div className="rounded-lg border border-border bg-card shadow-sm overflow-hidden">
        <div className="p-8 text-center">
          <span className={adminTextStyles.tableContentSecondary}>Loading activities...</span>
        </div>
      </div>
    );
  }

  if (activities.length === 0) {
    return (
      <div className="rounded-lg border border-border bg-card shadow-sm overflow-hidden">
        <div className="p-8 text-center">
          <span className={adminTextStyles.tableContentSecondary}>
            No activities found. Create your first activity to get started.
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-border bg-card shadow-sm overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="border-b border-border bg-muted/50 hover:bg-muted/70">
            <TableHead className="font-semibold text-foreground">Title</TableHead>
            <TableHead className="font-semibold text-foreground hidden sm:table-cell">Type</TableHead>
            <TableHead className="font-semibold text-foreground hidden md:table-cell">Location</TableHead>
            <TableHead className="font-semibold text-foreground hidden lg:table-cell">Created</TableHead>
            <TableHead className="font-semibold text-foreground text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {activities.map((activity) => (
            <TableRow key={activity.id} className="border-b border-border hover:bg-muted/30 transition-colors">
              <TableCell className="font-medium text-foreground py-4">
                <div className="flex flex-col">
                  <span className="font-semibold">{activity.title}</span>
                  <div className="flex flex-col gap-1 mt-1 sm:hidden">
                    <div className="flex gap-2">
                      <ActivityTypeBadge type={activity.type} />
                    </div>
                    <span className="text-xs text-muted-foreground md:hidden">
                      {activity.location || 'No location specified'}
                    </span>
                    <span className="text-xs text-muted-foreground lg:hidden">
                      {activity.created_at ? new Date(activity.created_at).toLocaleDateString() : 'N/A'}
                    </span>
                  </div>
                </div>
              </TableCell>
              <TableCell className="hidden sm:table-cell py-4">
                <ActivityTypeBadge type={activity.type} />
              </TableCell>
              <TableCell className="text-muted-foreground hidden md:table-cell">
                {activity.location || 'No location specified'}
              </TableCell>
              <TableCell className="text-muted-foreground hidden lg:table-cell">
                {activity.created_at ? new Date(activity.created_at).toLocaleDateString() : 'N/A'}
              </TableCell>
              <TableCell className="text-right py-4">
                <div className="flex items-center justify-end gap-1">
                  {onEdit && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(activity)}
                      className="h-9 w-9 p-0 hover:bg-muted rounded-md transition-colors"
                    >
                      <Pencil className="h-4 w-4 text-muted-foreground hover:text-foreground" />
                      <span className="sr-only">Edit activity</span>
                    </Button>
                  )}
                  {onDelete && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDelete(activity.id)}
                      className="h-9 w-9 p-0 text-destructive hover:text-destructive hover:bg-destructive/10 rounded-md transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">Delete activity</span>
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

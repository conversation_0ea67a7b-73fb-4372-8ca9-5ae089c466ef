/*
 * Festival Family Design System 2025 - Consolidated Extension Layer
 *
 * This file extends the shadcn/ui foundation with minimal Festival Family-specific variables.
 *
 * ARCHITECTURE:
 * - Foundation: shadcn/ui CSS variables (src/index.css) - Single source of truth
 * - Extension: Festival Family semantic variables that reference shadcn/ui foundation
 *
 * USAGE:
 * - Use shadcn/ui variables directly: hsl(var(--primary)), hsl(var(--background))
 * - Use Festival extensions for specific semantics: var(--festival-priority-high)
 */

:root {
  /* === FESTIVAL FAMILY SEMANTIC EXTENSIONS === */
  /* These extend shadcn/ui foundation variables with Festival-specific semantics */

  /* Festival Brand Colors - Reference shadcn/ui foundation */
  --festival-primary: hsl(var(--primary)); /* Festival purple */
  --festival-secondary: hsl(var(--secondary)); /* <PERSON><PERSON> */
  --festival-accent: hsl(var(--accent)); /* Festival orange */

  /* Festival Background Semantics - Reference shadcn/ui foundation */
  --festival-bg: hsl(var(--background)); /* Main background */
  --festival-bg-card: hsl(var(--card)); /* Card backgrounds */
  --festival-bg-muted: hsl(var(--muted)); /* Muted backgrounds */

  /* Festival Text Colors - Reference shadcn/ui foundation */
  --festival-text: hsl(var(--foreground)); /* Main text */
  --festival-text-muted: hsl(var(--muted-foreground)); /* Muted text */
  --festival-text-on-primary: hsl(var(--primary-foreground)); /* Text on primary */
  --festival-text-on-accent: hsl(var(--accent-foreground)); /* Text on accent */

  /* Automatic contrast text colors for intelligent theming */
  --festival-text-auto: hsl(var(--foreground)); /* Auto-contrast main text */
  --festival-text-auto-muted: hsl(var(--muted-foreground)); /* Auto-contrast muted text */
  --festival-text-on-light: hsl(var(--text-on-light)); /* Dark text for light backgrounds */
  --festival-text-on-dark: hsl(var(--text-on-dark)); /* Light text for dark backgrounds */

  /* Festival Border Colors - Reference shadcn/ui foundation */
  --festival-border: hsl(var(--border)); /* Standard borders */
  --festival-border-input: hsl(var(--input)); /* Input borders */
  --festival-ring: hsl(var(--ring)); /* Focus rings */

  /* === MOBILE-FIRST SPACING SYSTEM === */
  /* Optimized spacing tokens for mobile density and desktop comfort */

  /* Mobile-optimized spacing scale */
  --mobile-space-xs: 0.25rem;    /* 4px - Minimal spacing */
  --mobile-space-sm: 0.5rem;     /* 8px - Small spacing */
  --mobile-space-md: 0.75rem;    /* 12px - Medium spacing (reduced for mobile) */
  --mobile-space-lg: 1rem;       /* 16px - Large spacing (reduced for mobile) */
  --mobile-space-xl: 1.5rem;     /* 24px - Extra large (reduced for mobile) */
  --mobile-space-2xl: 2rem;      /* 32px - Double extra large (reduced for mobile) */

  /* Mobile-specific layout spacing */
  --mobile-section-gap: 1rem;      /* 16px - Between major sections */
  --mobile-content-gap: 0.75rem;   /* 12px - Between content blocks */
  --mobile-item-gap: 0.5rem;       /* 8px - Between items */
  --mobile-card-padding: 0.75rem;  /* 12px - Card internal padding */
  --mobile-page-padding: 1rem;     /* 16px - Page edge padding */

  /* Mobile interaction zones */
  --mobile-thumb-zone-height: 7.5rem;  /* 120px - Thumb-reachable area */
  --mobile-action-min-width: 7.5rem;   /* 120px - Minimum touch target */
  --mobile-pull-refresh-height: 3.75rem; /* 60px - Pull-to-refresh indicator */

  /* 2025 Mobile UI Trends - Modern Card Design */
  --modern-card-radius: 1.25rem;       /* 20px - Tinder-style rounded corners */
  --modern-card-radius-lg: 1.5rem;     /* 24px - Large card radius */
  --modern-card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --modern-card-shadow-hover: 0 8px 32px rgba(0, 0, 0, 0.12);
  --modern-card-border: 1px solid rgba(255, 255, 255, 0.1);

  /* Mobile-First Typography Hierarchy (2025 Trends) */
  --mobile-heading-primary: 1.75rem;   /* 28px - Bold primary headings */
  --mobile-heading-secondary: 1.25rem; /* 20px - Secondary headings */
  --mobile-heading-tertiary: 1.125rem; /* 18px - Tertiary headings */
  --mobile-body-large: 1rem;           /* 16px - Large body text */
  --mobile-body-base: 0.875rem;        /* 14px - Base body text */
  --mobile-body-small: 0.75rem;        /* 12px - Small text */

  /* Mobile-Optimized Spacing (Space Efficiency) */
  --mobile-header-compact: 0.75rem;    /* 12px - Compact header spacing */
  --mobile-content-tight: 0.5rem;      /* 8px - Tight content spacing */
  --mobile-element-gap: 0.625rem;      /* 10px - Element gap */

  /* === FESTIVAL FAMILY PRIORITY SYSTEM === */
  /* Priority-based colors for announcements and content categorization */
  /* These reference shadcn/ui semantic colors for consistency */

  --festival-priority-high: hsl(var(--destructive)); /* High priority - Red */
  --festival-priority-high-bg: hsl(var(--destructive) / 0.1); /* High priority background */
  --festival-priority-high-text: hsl(var(--destructive-foreground)); /* High priority text */

  --festival-priority-medium: hsl(var(--accent)); /* Medium priority - Orange */
  --festival-priority-medium-bg: hsl(var(--accent) / 0.1); /* Medium priority background */
  --festival-priority-medium-text: hsl(var(--accent-foreground)); /* Medium priority text */

  --festival-priority-low: hsl(var(--primary)); /* Low priority - Purple */
  --festival-priority-low-bg: hsl(var(--primary) / 0.1); /* Low priority background */
  --festival-priority-low-text: hsl(var(--primary-foreground)); /* Low priority text */

  /* === FESTIVAL FAMILY COMPONENT SEMANTICS === */
  /* Component-specific semantic variables for universal UI semantics */
  /* These maintain semantic meaning while aligning with modern 2025 design standards */

  --festival-success: hsl(142 71% 45%); /* Modern success green - improved contrast */
  --festival-success-bg: hsl(142 71% 45% / 0.1); /* Success background */
  --festival-success-text: hsl(0 0% 100%); /* Success text */

  --festival-warning: hsl(43 96% 56%); /* Modern warning amber - improved readability */
  --festival-warning-bg: hsl(43 96% 56% / 0.1); /* Warning background */
  --festival-warning-text: hsl(0 0% 100%); /* Warning text */

  --festival-error: hsl(var(--destructive)); /* Error - Reference shadcn/ui */
  --festival-error-bg: hsl(var(--destructive) / 0.1); /* Error background */
  --festival-error-text: hsl(var(--destructive-foreground)); /* Error text */

  /* === FESTIVAL FAMILY COMPONENT UTILITIES === */
  /* Utility variables for consistent component styling */

  --festival-radius: var(--radius); /* Reference shadcn/ui radius */
  --festival-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --festival-transition: all 0.2s ease-in-out;

  /* === FESTIVAL FAMILY GRADIENTS === */
  /* Subtle gradients that reference shadcn/ui foundation colors */
  --festival-gradient: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--primary)) 100%);
  --festival-gradient-subtle: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--background)) 100%);

  /* === MODERN VISUAL EFFECTS (2025) === */
  /* Glassmorphism effects using CSS variables for theme compatibility */
  --glassmorphism-bg: hsl(var(--card) / 0.8);
  --glassmorphism-border: hsl(var(--border) / 0.2);
  --glassmorphism-backdrop: blur(12px);

  /* Layering and depth effects */
  --layer-shadow-sm: 0 1px 2px hsl(var(--foreground) / 0.05);
  --layer-shadow-md: 0 4px 6px hsl(var(--foreground) / 0.1), 0 1px 3px hsl(var(--foreground) / 0.08);
  --layer-shadow-lg: 0 10px 15px hsl(var(--foreground) / 0.1), 0 4px 6px hsl(var(--foreground) / 0.05);
  --layer-shadow-xl: 0 20px 25px hsl(var(--foreground) / 0.1), 0 8px 10px hsl(var(--foreground) / 0.04);

  /* Light effects and highlights */
  --light-effect-primary: linear-gradient(135deg, hsl(var(--primary) / 0.1), hsl(var(--secondary) / 0.05));
  --light-effect-accent: linear-gradient(135deg, hsl(var(--accent) / 0.1), hsl(var(--primary) / 0.05));
  --light-effect-subtle: linear-gradient(135deg, hsl(var(--muted) / 0.1), hsl(var(--background) / 0.05));

  /* Vibrant component backgrounds for social media feel */
  --vibrant-happening-now: linear-gradient(135deg, hsl(var(--destructive) / 0.1), hsl(var(--accent) / 0.05));
  --vibrant-schedule: linear-gradient(135deg, hsl(var(--primary) / 0.1), hsl(var(--secondary) / 0.05));
  --vibrant-community: linear-gradient(135deg, hsl(var(--accent) / 0.1), hsl(var(--primary) / 0.05));
  --vibrant-weather: linear-gradient(135deg, hsl(43 96% 56% / 0.1), hsl(var(--accent) / 0.05));
  --vibrant-announcement: linear-gradient(135deg, hsl(var(--primary) / 0.15), hsl(var(--accent) / 0.08));
  --vibrant-quick-action: linear-gradient(135deg, hsl(var(--secondary) / 0.1), hsl(var(--muted) / 0.05));

  /* Interactive hover effects */
  --hover-lift: translateY(-2px);
  --hover-scale: scale(1.02);
  --hover-glow: 0 0 20px hsl(var(--primary) / 0.3);

  /* Modern spacing and sizing */
  --bento-gap: 1rem;
  --bento-gap-lg: 1.5rem;
  --tall-card-min-height: 12rem;
  --hero-font-size: clamp(2rem, 5vw, 4rem);
  --display-font-size: clamp(1.5rem, 3vw, 2.5rem);
}

/* === FESTIVAL FAMILY UTILITY CLASSES === */
/* Utility classes that reference the consolidated shadcn/ui foundation */

/* Festival Text Utilities */
.festival-text-gradient {
  background: var(--festival-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Automatic contrast text utilities for intelligent theming */
.festival-text-auto {
  color: var(--festival-text-auto);
}

.festival-text-auto-muted {
  color: var(--festival-text-auto-muted);
}

.festival-text-on-light {
  color: var(--festival-text-on-light);
}

.festival-text-on-dark {
  color: var(--festival-text-on-dark);
}

/* Smart contrast utilities that adapt to container background */
.smart-text-contrast {
  color: hsl(var(--foreground));
}

.smart-text-contrast-muted {
  color: hsl(var(--muted-foreground));
}

.festival-text { color: var(--festival-text); }
.festival-text-muted { color: var(--festival-text-muted); }
.festival-text-on-primary { color: var(--festival-text-on-primary); }
.festival-text-on-accent { color: var(--festival-text-on-accent); }

/* Festival Component Text Colors */
.text-festival-success { color: var(--festival-success); }
.text-festival-warning { color: var(--festival-warning); }
.text-festival-error { color: var(--festival-error); }

/* Festival Background Utilities */
.festival-bg { background-color: var(--festival-bg); }
.festival-bg-card { background-color: var(--festival-bg-card); }
.festival-bg-muted { background-color: var(--festival-bg-muted); }
.festival-gradient-bg { background: var(--festival-gradient); }
.festival-gradient-subtle-bg { background: var(--festival-gradient-subtle); }

/* Festival Priority Utilities */
.festival-priority-high {
  color: var(--festival-priority-high-text);
  background-color: var(--festival-priority-high-bg);
  border-color: var(--festival-priority-high);
}
.festival-priority-medium {
  color: var(--festival-priority-medium-text);
  background-color: var(--festival-priority-medium-bg);
  border-color: var(--festival-priority-medium);
}
.festival-priority-low {
  color: var(--festival-priority-low-text);
  background-color: var(--festival-priority-low-bg);
  border-color: var(--festival-priority-low);
}

/* Festival Component Utilities */
.festival-component {
  border-radius: var(--festival-radius);
  box-shadow: var(--festival-shadow);
  transition: var(--festival-transition);
}

/* === MODERN VISUAL EFFECT UTILITIES (2025) === */
/* Glassmorphism effects */
.glassmorphism {
  background: var(--glassmorphism-bg);
  backdrop-filter: var(--glassmorphism-backdrop);
  border: 1px solid var(--glassmorphism-border);
}

.glassmorphism-card {
  background: var(--glassmorphism-bg);
  backdrop-filter: var(--glassmorphism-backdrop);
  border: 1px solid var(--glassmorphism-border);
  border-radius: var(--festival-radius);
}

/* Layering and depth effects */
.layer-shadow-sm { box-shadow: var(--layer-shadow-sm); }
.layer-shadow-md { box-shadow: var(--layer-shadow-md); }
.layer-shadow-lg { box-shadow: var(--layer-shadow-lg); }
.layer-shadow-xl { box-shadow: var(--layer-shadow-xl); }

.layered-effect {
  box-shadow: var(--layer-shadow-md);
  transition: var(--festival-transition);
}

.layered-effect:hover {
  box-shadow: var(--layer-shadow-lg);
  transform: var(--hover-lift);
}

/* Light effects */
.light-effect-primary { background: var(--light-effect-primary); }
.light-effect-accent { background: var(--light-effect-accent); }
.light-effect-subtle { background: var(--light-effect-subtle); }

/* Vibrant social media inspired backgrounds */
.vibrant-happening-now { background: var(--vibrant-happening-now); }
.vibrant-schedule { background: var(--vibrant-schedule); }
.vibrant-community { background: var(--vibrant-community); }
.vibrant-weather { background: var(--vibrant-weather); }
.vibrant-announcement { background: var(--vibrant-announcement); }
.vibrant-quick-action { background: var(--vibrant-quick-action); }

/* Interactive effects */
.hover-lift {
  transition: var(--festival-transition);
}

.hover-lift:hover {
  transform: var(--hover-lift);
}

.hover-scale {
  transition: var(--festival-transition);
}

.hover-scale:hover {
  transform: var(--hover-scale);
}

.hover-glow {
  transition: var(--festival-transition);
}

.hover-glow:hover {
  box-shadow: var(--hover-glow);
}

/* === MODERN MICRO-INTERACTIONS (2025) === */
/* Subtle animations and interactions for enhanced UX */

.micro-bounce {
  transition: transform 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.micro-bounce:hover {
  transform: scale(1.02);
}

.micro-bounce:active {
  transform: scale(0.98);
}

.pulse-glow {
  animation: pulse-glow 4s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  from {
    box-shadow: 0 0 5px hsl(var(--primary) / 0.3);
  }
  to {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.6), 0 0 30px hsl(var(--primary) / 0.4);
  }
}

.smooth-scale {
  transition: transform 0.3s ease-out;
}

.smooth-scale:hover {
  transform: scale(1.05);
}

.glass-hover {
  transition: all 0.3s ease-out;
}

.glass-hover:hover {
  background: hsl(var(--card) / 0.9);
  backdrop-filter: blur(16px);
  border-color: hsl(var(--primary) / 0.3);
}

.fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modern typography */
.hero-text {
  font-size: var(--hero-font-size);
  font-weight: 700;
  line-height: 1.1;
}

.display-text {
  font-size: var(--display-font-size);
  font-weight: 600;
  line-height: 1.2;
}

/* Bento grid layout */
.bento-grid {
  display: grid;
  gap: var(--bento-gap);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.bento-grid-lg {
  display: grid;
  gap: var(--bento-gap-lg);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Tall cards for mobile-first design */
.tall-card {
  min-height: var(--tall-card-min-height);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 2025 Mobile UI Trends - Modern Card Styles */
.modern-card {
  border-radius: var(--modern-card-radius);
  box-shadow: var(--modern-card-shadow);
  border: var(--modern-card-border);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.05);
}

.modern-card:hover {
  box-shadow: var(--modern-card-shadow-hover);
  transform: translateY(-2px);
}

.modern-card-lg {
  border-radius: var(--modern-card-radius-lg);
}

/* Mobile-First Typography Classes */
.mobile-heading-primary {
  font-size: var(--mobile-heading-primary);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.mobile-heading-secondary {
  font-size: var(--mobile-heading-secondary);
  font-weight: 600;
  line-height: 1.3;
}

.mobile-heading-tertiary {
  font-size: var(--mobile-heading-tertiary);
  font-weight: 500;
  line-height: 1.4;
}

.mobile-body-large {
  font-size: var(--mobile-body-large);
  line-height: 1.5;
}

.mobile-body-base {
  font-size: var(--mobile-body-base);
  line-height: 1.5;
}

.mobile-body-small {
  font-size: var(--mobile-body-small);
  line-height: 1.4;
}

/* Mobile-Optimized Spacing */
.mobile-header-compact {
  padding: var(--mobile-header-compact);
}

.mobile-content-tight {
  gap: var(--mobile-content-tight);
}

.mobile-element-gap {
  gap: var(--mobile-element-gap);
}

/* Combined effect classes for easy application */
.festival-card-enhanced {
  background: var(--glassmorphism-bg);
  backdrop-filter: var(--glassmorphism-backdrop);
  border: 1px solid var(--glassmorphism-border);
  border-radius: var(--festival-radius);
  box-shadow: var(--layer-shadow-md);
  transition: var(--festival-transition);
}

.festival-card-enhanced:hover {
  box-shadow: var(--layer-shadow-lg);
  transform: var(--hover-lift);
}

.enhanced-card {
  background: var(--light-effect-subtle);
  border: 1px solid var(--glassmorphism-border);
  border-radius: var(--festival-radius);
  box-shadow: var(--layer-shadow-sm);
  transition: var(--festival-transition);
}

.enhanced-card:hover {
  background: var(--light-effect-primary);
  box-shadow: var(--layer-shadow-md);
  transform: var(--hover-lift);
}

/* Social media inspired card variants */
.social-card {
  background: var(--light-effect-subtle);
  border: 1px solid var(--glassmorphism-border);
  border-radius: var(--festival-radius);
  box-shadow: var(--layer-shadow-sm);
  transition: var(--festival-transition);
  overflow: hidden;
}

.social-card:hover {
  background: var(--light-effect-primary);
  box-shadow: var(--layer-shadow-lg);
  transform: var(--hover-lift);
}

.social-card-vibrant {
  background: var(--vibrant-announcement);
  border: 1px solid var(--primary) / 0.2);
  border-radius: var(--festival-radius);
  box-shadow: var(--layer-shadow-md);
  transition: var(--festival-transition);
}

.social-card-vibrant:hover {
  background: var(--vibrant-community);
  box-shadow: var(--hover-glow);
  transform: var(--hover-scale);
}

/* Mobile-first responsive spacing utilities */
.mobile-section-spacing {
  margin-bottom: var(--mobile-section-gap);
}

.mobile-content-spacing {
  margin-bottom: var(--mobile-content-gap);
}

.mobile-item-spacing {
  margin-bottom: var(--mobile-item-gap);
}

.mobile-card-padding {
  padding: var(--mobile-card-padding);
}

.mobile-page-padding {
  padding: var(--mobile-page-padding);
}

/* Mobile-optimized action grid */
.mobile-action-grid {
  gap: var(--mobile-item-gap);
}

.mobile-action-grid > * {
  min-height: 80px; /* Compact height for mobile */
}

/* Mobile content prioritization utilities */
.mobile-priority-high {
  order: -3; /* Show first on mobile */
}

.mobile-priority-medium {
  order: -2; /* Show second on mobile */
}

.mobile-priority-low {
  order: -1; /* Show third on mobile */
}

/* Mobile collapsible section animations */
.mobile-collapsible-header {
  transition: all 0.2s ease-in-out;
}

.mobile-collapsible-header:hover {
  background-color: hsl(var(--muted) / 0.5);
  border-radius: var(--radius);
}

/* Mobile gesture navigation */
.mobile-gesture-container {
  position: relative;
  overflow: hidden;
  touch-action: pan-x;
}

.mobile-gesture-indicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity var(--festival-transition);
  pointer-events: none;
  z-index: 10;
}

.mobile-gesture-indicator.left {
  left: var(--mobile-space-lg);
}

.mobile-gesture-indicator.right {
  right: var(--mobile-space-lg);
}

.mobile-gesture-indicator.active {
  opacity: 1;
}

/* One-handed interaction optimizations */
.mobile-thumb-zone {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--mobile-thumb-zone-height);
  pointer-events: none;
  z-index: 1000;
}

.mobile-thumb-zone-content {
  position: absolute;
  bottom: var(--mobile-space-lg);
  right: var(--mobile-space-lg);
  pointer-events: auto;
}

/* Responsive spacing that scales up on larger screens */
@media (min-width: 768px) {
  .mobile-section-spacing {
    margin-bottom: calc(var(--mobile-section-gap) * 1.5); /* 24px on desktop */
  }

  .mobile-content-spacing {
    margin-bottom: calc(var(--mobile-content-gap) * 1.33); /* 16px on desktop */
  }

  .mobile-card-padding {
    padding: calc(var(--mobile-card-padding) * 1.33); /* 16px on desktop */
  }

  .mobile-page-padding {
    padding: calc(var(--mobile-page-padding) * 1.5); /* 24px on desktop */
  }
}

/* Responsive square layouts for desktop */
@media (min-width: 768px) {
  .square-layout {
    aspect-ratio: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .compact-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    max-width: 800px;
    margin: 0 auto;
  }
}

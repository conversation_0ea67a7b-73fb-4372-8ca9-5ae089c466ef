# Test info

- Name: Festival Family Database Verification >> Database Connectivity Test
- Location: C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:9:3

# Error details

```
Error: expect(received).toContain(expected) // indexOf

Expected substring: "Database connection successful"
Received string:    "🔄 Testing connectivity..."
    at C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:32:20
```

# Page snapshot

```yaml
- heading "🔍 Festival Family Database Test" [level=1]
- paragraph: This page tests the database connectivity and table existence for Festival Family.
- heading "📊 Database Connectivity Test" [level=2]
- button "Test Connection"
- text: ✅ Database connection successful!
- heading "📋 Table Existence Check" [level=2]
- button "Check All Tables"
- heading "⚙️ Admin Functions Test" [level=2]
- button "Test Admin Functions"
- heading "🔄 CRUD Operations Test" [level=2]
- button "Test CRUD"
- heading "👤 Profile System Test" [level=2]
- button "Test Profile Updates"
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Festival Family Database Verification', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Set longer timeout for database operations
   6 |     test.setTimeout(60000);
   7 |   });
   8 |
   9 |   test('Database Connectivity Test', async ({ page }) => {
   10 |     // Navigate to our database test page
   11 |     await page.goto('file://' + process.cwd() + '/database-test.html');
   12 |     
   13 |     // Wait for page to load
   14 |     await page.waitForLoadState('networkidle');
   15 |     
   16 |     // Click the connectivity test button
   17 |     await page.click('button:has-text("Test Connection")');
   18 |     
   19 |     // Wait for result
   20 |     await page.waitForSelector('#connectivity-result', { timeout: 10000 });
   21 |     
   22 |     // Get the result text
   23 |     const result = await page.textContent('#connectivity-result');
   24 |     
   25 |     // Take screenshot for evidence
   26 |     await page.screenshot({ 
   27 |       path: 'test-results/database-connectivity.png',
   28 |       fullPage: true 
   29 |     });
   30 |     
   31 |     // Verify connection is successful
>  32 |     expect(result).toContain('Database connection successful');
      |                    ^ Error: expect(received).toContain(expected) // indexOf
   33 |     
   34 |     console.log('✅ Database Connectivity Result:', result);
   35 |   });
   36 |
   37 |   test('Table Existence Check', async ({ page }) => {
   38 |     await page.goto('file://' + process.cwd() + '/database-test.html');
   39 |     await page.waitForLoadState('networkidle');
   40 |     
   41 |     // Click the table check button
   42 |     await page.click('button:has-text("Check All Tables")');
   43 |     
   44 |     // Wait for results
   45 |     await page.waitForSelector('#tables-result', { timeout: 15000 });
   46 |     await page.waitForSelector('.table-item', { timeout: 15000 });
   47 |     
   48 |     // Get all table items
   49 |     const tableItems = await page.$$('.table-item');
   50 |     const tableResults = [];
   51 |     
   52 |     for (const item of tableItems) {
   53 |       const text = await item.textContent();
   54 |       const className = await item.getAttribute('class');
   55 |       tableResults.push({
   56 |         text: text?.trim(),
   57 |         exists: className?.includes('exists') || false
   58 |       });
   59 |     }
   60 |     
   61 |     // Take screenshot
   62 |     await page.screenshot({ 
   63 |       path: 'test-results/table-existence.png',
   64 |       fullPage: true 
   65 |     });
   66 |     
   67 |     // Log results
   68 |     console.log('📋 Table Existence Results:');
   69 |     tableResults.forEach(table => {
   70 |       console.log(`${table.exists ? '✅' : '❌'} ${table.text}`);
   71 |     });
   72 |     
   73 |     // Verify core tables exist
   74 |     const coreTablesExist = tableResults.filter(t => 
   75 |       t.text?.includes('profiles') || 
   76 |       t.text?.includes('festivals') || 
   77 |       t.text?.includes('activities')
   78 |     ).every(t => t.exists);
   79 |     
   80 |     expect(coreTablesExist).toBe(true);
   81 |     
   82 |     // Check for new tables
   83 |     const newTables = ['content_management', 'user_preferences', 'emergency_contacts', 'announcements'];
   84 |     const newTablesExist = newTables.map(tableName => {
   85 |       const tableResult = tableResults.find(t => t.text?.includes(tableName));
   86 |       return { name: tableName, exists: tableResult?.exists || false };
   87 |     });
   88 |     
   89 |     console.log('🆕 New Tables Status:');
   90 |     newTablesExist.forEach(table => {
   91 |       console.log(`${table.exists ? '✅' : '❌'} ${table.name}`);
   92 |     });
   93 |   });
   94 |
   95 |   test('Admin Functions Test', async ({ page }) => {
   96 |     await page.goto('file://' + process.cwd() + '/database-test.html');
   97 |     await page.waitForLoadState('networkidle');
   98 |     
   99 |     // Click admin functions test
  100 |     await page.click('button:has-text("Test Admin Functions")');
  101 |     
  102 |     // Wait for results
  103 |     await page.waitForSelector('#admin-result', { timeout: 10000 });
  104 |     
  105 |     const result = await page.textContent('#admin-result');
  106 |     
  107 |     // Take screenshot
  108 |     await page.screenshot({ 
  109 |       path: 'test-results/admin-functions.png',
  110 |       fullPage: true 
  111 |     });
  112 |     
  113 |     console.log('⚙️ Admin Functions Result:', result);
  114 |     
  115 |     // Verify at least some admin functions work
  116 |     expect(result).toMatch(/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/);
  117 |   });
  118 |
  119 |   test('CRUD Operations Test', async ({ page }) => {
  120 |     await page.goto('file://' + process.cwd() + '/database-test.html');
  121 |     await page.waitForLoadState('networkidle');
  122 |     
  123 |     // Click CRUD test
  124 |     await page.click('button:has-text("Test CRUD")');
  125 |     
  126 |     // Wait for results
  127 |     await page.waitForSelector('#crud-result', { timeout: 15000 });
  128 |     
  129 |     const result = await page.textContent('#crud-result');
  130 |     
  131 |     // Take screenshot
  132 |     await page.screenshot({ 
```
/**
 * Unified User Tracking Hook
 * 
 * React hook for the unified user tracking system.
 * Provides a simple interface for tracking user behavior and managing preferences.
 */

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/providers/ConsolidatedAuthProvider'
import { unifiedUserTracking } from '@/lib/supabase/services/unified-user-tracking'
import type { 
  UserActivity, 
  UserPreference, 
  UserSuggestion 
} from '@/lib/supabase/services/unified-user-tracking'

interface UseUnifiedTrackingReturn {
  // Tracking functions
  trackView: (targetType: string, targetId: string) => Promise<void>
  trackFavorite: (targetType: string, targetId: string) => Promise<void>
  trackJoin: (targetType: string, targetId: string) => Promise<void>
  trackShare: (targetType: string, targetId: string) => Promise<void>
  
  // Preference management
  setPreference: (type: string, value: string, weight?: number) => Promise<void>
  getPreferences: () => Promise<UserPreference[]>
  
  // Suggestions
  getSuggestions: (type: string, limit?: number) => Promise<UserSuggestion[]>
  
  // Activity history
  getActivityHistory: (type?: string, limit?: number) => Promise<UserActivity[]>
  
  // Engagement metrics
  getEngagementMetrics: (days?: number) => Promise<any>
  
  // Real-time subscriptions
  subscribeToUpdates: (callback: (data: any) => void) => string | null
  unsubscribe: (subscriptionId: string) => void
  
  // State
  loading: boolean
  error: string | null
}

export function useUnifiedTracking(): UseUnifiedTrackingReturn {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Helper function to handle API calls
  const handleApiCall = useCallback(async <T>(
    apiCall: () => Promise<{ data: T | null; error: Error | null }>
  ): Promise<T> => {
    if (!user?.id) {
      throw new Error('User not authenticated')
    }

    setLoading(true)
    setError(null)

    try {
      const result = await apiCall()
      
      if (result.error) {
        throw result.error
      }
      
      if (!result.data) {
        throw new Error('No data returned')
      }

      return result.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [user?.id])

  // Tracking functions
  const trackView = useCallback(async (targetType: string, targetId: string) => {
    if (!user?.id) return

    await handleApiCall(() =>
      unifiedUserTracking.trackActivity(
        user.id,
        'view',
        targetType as any,
        targetId
      )
    )
  }, [user?.id, handleApiCall])

  const trackFavorite = useCallback(async (targetType: string, targetId: string) => {
    if (!user?.id) return

    await handleApiCall(() =>
      unifiedUserTracking.trackActivity(
        user.id,
        'favorite',
        targetType as any,
        targetId
      )
    )
  }, [user?.id, handleApiCall])

  const trackJoin = useCallback(async (targetType: string, targetId: string) => {
    if (!user?.id) return

    await handleApiCall(() =>
      unifiedUserTracking.trackActivity(
        user.id,
        'join',
        targetType as any,
        targetId
      )
    )
  }, [user?.id, handleApiCall])

  const trackShare = useCallback(async (targetType: string, targetId: string) => {
    if (!user?.id) return

    await handleApiCall(() =>
      unifiedUserTracking.trackActivity(
        user.id,
        'share',
        targetType as any,
        targetId
      )
    )
  }, [user?.id, handleApiCall])

  // Preference management
  const setPreference = useCallback(async (
    type: string, 
    value: string, 
    weight: number = 5
  ) => {
    if (!user?.id) return

    await handleApiCall(() =>
      unifiedUserTracking.setPreference(
        user.id,
        type as any,
        value,
        weight
      )
    )
  }, [user?.id, handleApiCall])

  const getPreferences = useCallback(async (): Promise<UserPreference[]> => {
    if (!user?.id) return []

    return handleApiCall(() =>
      unifiedUserTracking.getUserPreferences(user.id)
    )
  }, [user?.id, handleApiCall])

  // Suggestions
  const getSuggestions = useCallback(async (
    type: string, 
    limit: number = 10
  ): Promise<UserSuggestion[]> => {
    if (!user?.id) return []

    return handleApiCall(() =>
      unifiedUserTracking.generateSuggestions(
        user.id,
        type as any,
        limit
      )
    )
  }, [user?.id, handleApiCall])

  // Activity history
  const getActivityHistory = useCallback(async (
    type?: string, 
    limit: number = 50
  ): Promise<UserActivity[]> => {
    if (!user?.id) return []

    return handleApiCall(() =>
      unifiedUserTracking.getUserActivityHistory(
        user.id,
        type as any,
        limit
      )
    )
  }, [user?.id, handleApiCall])

  // Engagement metrics
  const getEngagementMetrics = useCallback(async (days: number = 30) => {
    if (!user?.id) return null

    return handleApiCall(() =>
      unifiedUserTracking.getUserEngagementMetrics(user.id, days)
    )
  }, [user?.id, handleApiCall])

  // Real-time subscriptions
  const subscribeToUpdates = useCallback((callback: (data: any) => void): string | null => {
    if (!user?.id) return null

    return unifiedUserTracking.subscribeToUserUpdates(user.id, callback)
  }, [user?.id])

  const unsubscribe = useCallback((subscriptionId: string) => {
    unifiedUserTracking.unsubscribe(subscriptionId)
  }, [])

  // Cleanup subscriptions on unmount
  useEffect(() => {
    return () => {
      unifiedUserTracking.unsubscribeAll()
    }
  }, [])

  return {
    // Tracking functions
    trackView,
    trackFavorite,
    trackJoin,
    trackShare,
    
    // Preference management
    setPreference,
    getPreferences,
    
    // Suggestions
    getSuggestions,
    
    // Activity history
    getActivityHistory,
    
    // Engagement metrics
    getEngagementMetrics,
    
    // Real-time subscriptions
    subscribeToUpdates,
    unsubscribe,
    
    // State
    loading,
    error
  }
}

// Convenience hooks for specific tracking
export function useActivityTracking() {
  const { trackView, trackFavorite, trackJoin, trackShare, getActivityHistory } = useUnifiedTracking()

  return {
    trackView,
    trackFavorite,
    trackJoin,
    trackShare,
    getActivityHistory
  }
}

export function useUserPreferences() {
  const { setPreference, getPreferences } = useUnifiedTracking()
  
  return {
    setPreference,
    getPreferences
  }
}

export function useUserSuggestions() {
  const { getSuggestions } = useUnifiedTracking()
  
  return {
    getSuggestions
  }
}

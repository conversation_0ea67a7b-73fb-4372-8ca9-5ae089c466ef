/**
 * Text Optimization Utilities for Mobile-First Visual Hierarchy
 * 
 * Provides consistent text optimization across all Festival Family components
 * following the established single source of truth methodology.
 */

export interface TextOptimizationConfig {
  maxLength: number;
  preserveFirstSentence?: boolean;
  fallbackText?: string;
  mobileMaxLength?: number;
}

/**
 * Default configuration for different content types
 */
export const TEXT_OPTIMIZATION_CONFIGS = {
  // Card descriptions - primary content
  cardDescription: {
    maxLength: 80,
    mobileMaxLength: 60,
    preserveFirstSentence: true,
    fallbackText: 'Discover more'
  },
  
  // Activity descriptions
  activityDescription: {
    maxLength: 75,
    mobileMaxLength: 55,
    preserveFirstSentence: true,
    fallbackText: 'Join this activity'
  },
  
  // Event descriptions
  eventDescription: {
    maxLength: 85,
    mobileMaxLength: 65,
    preserveFirstSentence: true,
    fallbackText: 'Discover this event'
  },
  
  // Resource descriptions
  resourceDescription: {
    maxLength: 70,
    mobileMaxLength: 50,
    preserveFirstSentence: false,
    fallbackText: 'Helpful resource'
  },
  
  // Chat/Community descriptions
  communityDescription: {
    maxLength: 65,
    mobileMaxLength: 45,
    preserveFirstSentence: false,
    fallbackText: 'Connect with others'
  },
  
  // Festival descriptions
  festivalDescription: {
    maxLength: 90,
    mobileMaxLength: 70,
    preserveFirstSentence: true,
    fallbackText: 'Amazing festival experience'
  }
} as const;

/**
 * Optimizes text for mobile-first visual hierarchy
 * 
 * @param text - Original text to optimize
 * @param config - Optimization configuration
 * @param isMobile - Whether to apply mobile-specific optimization
 * @returns Optimized text
 */
export function optimizeText(
  text: string | null | undefined,
  config: TextOptimizationConfig,
  isMobile: boolean = false
): string {
  // Handle null/undefined/empty text
  if (!text || text.trim().length === 0) {
    return config.fallbackText || 'Learn more';
  }

  const cleanText = text.trim();
  const targetLength = isMobile && config.mobileMaxLength 
    ? config.mobileMaxLength 
    : config.maxLength;

  // If text is already within target length, return as-is
  if (cleanText.length <= targetLength) {
    return cleanText;
  }

  // Try to preserve first sentence if configured
  if (config.preserveFirstSentence) {
    const firstSentence = extractFirstSentence(cleanText);
    if (firstSentence && firstSentence.length <= targetLength) {
      return firstSentence;
    }
  }

  // Truncate at word boundary near target length
  return truncateAtWordBoundary(cleanText, targetLength);
}

/**
 * Extracts the first complete sentence from text
 */
function extractFirstSentence(text: string): string | null {
  // Look for sentence endings
  const sentenceEndings = /[.!?]/;
  const match = text.match(sentenceEndings);
  
  if (match && match.index !== undefined) {
    const firstSentence = text.substring(0, match.index + 1).trim();
    // Ensure it's not too short to be meaningful
    if (firstSentence.length >= 20) {
      return firstSentence;
    }
  }
  
  return null;
}

/**
 * Truncates text at word boundary with ellipsis
 */
function truncateAtWordBoundary(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }

  // Find the last space before maxLength - 3 (for ellipsis)
  const truncateAt = maxLength - 3;
  let lastSpace = text.lastIndexOf(' ', truncateAt);
  
  // If no space found, just truncate at character boundary
  if (lastSpace === -1) {
    lastSpace = truncateAt;
  }
  
  return text.substring(0, lastSpace).trim() + '...';
}

/**
 * Optimizes activity description specifically
 */
export function optimizeActivityDescription(
  description: string | null | undefined,
  isMobile: boolean = false
): string {
  return optimizeText(description, TEXT_OPTIMIZATION_CONFIGS.activityDescription, isMobile);
}

/**
 * Optimizes event description specifically
 */
export function optimizeEventDescription(
  description: string | null | undefined,
  isMobile: boolean = false
): string {
  return optimizeText(description, TEXT_OPTIMIZATION_CONFIGS.eventDescription, isMobile);
}

/**
 * Optimizes resource description specifically
 */
export function optimizeResourceDescription(
  description: string | null | undefined,
  isMobile: boolean = false
): string {
  return optimizeText(description, TEXT_OPTIMIZATION_CONFIGS.resourceDescription, isMobile);
}

/**
 * Optimizes community/chat description specifically
 */
export function optimizeCommunityDescription(
  description: string | null | undefined,
  isMobile: boolean = false
): string {
  return optimizeText(description, TEXT_OPTIMIZATION_CONFIGS.communityDescription, isMobile);
}

/**
 * Optimizes festival description specifically
 */
export function optimizeFestivalDescription(
  description: string | null | undefined,
  isMobile: boolean = false
): string {
  return optimizeText(description, TEXT_OPTIMIZATION_CONFIGS.festivalDescription, isMobile);
}

/**
 * Optimizes generic card description
 */
export function optimizeCardDescription(
  description: string | null | undefined,
  isMobile: boolean = false
): string {
  return optimizeText(description, TEXT_OPTIMIZATION_CONFIGS.cardDescription, isMobile);
}

/**
 * Hook for mobile detection (can be enhanced with actual mobile detection)
 */
export function useIsMobile(): boolean {
  // Simple viewport-based detection
  // In a real implementation, this could use a proper mobile detection hook
  if (typeof window === 'undefined') return false;
  return window.innerWidth < 768;
}

/**
 * Batch optimize multiple descriptions
 */
export function batchOptimizeDescriptions<T extends Record<string, any>>(
  items: T[],
  descriptionKey: keyof T,
  config: TextOptimizationConfig,
  isMobile: boolean = false
): T[] {
  return items.map(item => ({
    ...item,
    [descriptionKey]: optimizeText(item[descriptionKey], config, isMobile)
  }));
}

{"attempts": [{"attempt": 1, "error": "locator.isVisible: Unknown engine \"text*\" while parsing selector text*=\"rate limit\", text*=\"too many\", text*=\"slow down\", text*=\"throttle\"\nCall log:\n\u001b[2m    - checking visibility of locator('text*=\"rate limit\", text*=\"too many\", text*=\"slow down\", text*=\"throttle\"')\u001b[22m\n", "timestamp": "2025-06-05T23:31:38.207Z"}, {"attempt": 2, "error": "locator.isVisible: Unknown engine \"text*\" while parsing selector text*=\"rate limit\", text*=\"too many\", text*=\"slow down\", text*=\"throttle\"\nCall log:\n\u001b[2m    - checking visibility of locator('text*=\"rate limit\", text*=\"too many\", text*=\"slow down\", text*=\"throttle\"')\u001b[22m\n", "timestamp": "2025-06-05T23:31:42.496Z"}, {"attempt": 3, "error": "locator.isVisible: Unknown engine \"text*\" while parsing selector text*=\"rate limit\", text*=\"too many\", text*=\"slow down\", text*=\"throttle\"\nCall log:\n\u001b[2m    - checking visibility of locator('text*=\"rate limit\", text*=\"too many\", text*=\"slow down\", text*=\"throttle\"')\u001b[22m\n", "timestamp": "2025-06-05T23:31:46.603Z"}], "rateLimitDetected": false, "averageResponseTime": 0, "rateLimitThreshold": null, "timestamp": "2025-06-05T23:31:32.991Z"}
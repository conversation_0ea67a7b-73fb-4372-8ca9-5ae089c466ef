/**
 * Test Authentication Fix
 * 
 * This script tests our authentication improvements:
 * 1. Faster timeout (5 seconds instead of 10)
 * 2. Better error handling
 * 3. Proper profile creation with required fields
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('🧪 Testing Authentication Fix');
console.log('=============================');

async function testAuthenticationFix() {
  try {
    // Test 1: Sign in as admin
    console.log('🔐 Test 1: Admin Sign In');
    console.log('------------------------');
    
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testpassword123'
    });
    
    if (authError) {
      console.error('❌ Authentication failed:', authError.message);
      return;
    }
    
    console.log('✅ Authentication successful');
    console.log('👤 User ID:', authData.user?.id);
    console.log('📧 Email:', authData.user?.email);
    console.log('');

    // Test 2: Test our improved profile fetching with timeout
    console.log('🧪 Test 2: Profile Fetch with Timeout');
    console.log('-------------------------------------');
    
    const userId = authData.user.id;
    
    // Simulate our improved fetchProfile function
    const timeoutPromise = new Promise((resolve) => {
      setTimeout(() => {
        console.log('⏰ Profile fetch timeout triggered after 5 seconds');
        resolve({ 
          data: null, 
          error: { message: 'Profile fetch timeout', code: 'TIMEOUT' } 
        });
      }, 5000);
    });

    const queryPromise = supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    console.log('🔍 Starting profile fetch race (query vs 5-second timeout)...');
    const startTime = Date.now();
    
    const result = await Promise.race([queryPromise, timeoutPromise]);
    const { data, error } = result;
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`⏱️  Fetch completed in ${duration}ms`);
    
    if (error) {
      if (error.code === 'TIMEOUT') {
        console.log('⏰ Timeout triggered - would create basic profile');
        
        // Test basic profile creation
        const basicProfile = {
          id: userId,
          email: authData.user.email || '',
          username: authData.user.email?.split('@')[0] || 'user',
          role: 'user',
          created_at: new Date().toISOString(),
        };
        
        console.log('📝 Basic profile would be:', JSON.stringify(basicProfile, null, 2));
        
      } else {
        console.error('❌ Profile fetch error:', error);
      }
    } else {
      console.log('✅ Profile fetched successfully:');
      console.log('👤 Role:', data.role);
      console.log('🔑 Is Admin:', data.role === 'SUPER_ADMIN');
      console.log('📊 Full profile:', JSON.stringify(data, null, 2));
    }
    
    console.log('');

    // Test 3: Test admin role detection
    console.log('🧪 Test 3: Admin Role Detection');
    console.log('-------------------------------');
    
    if (data && data.role) {
      const isAdmin = data.role === 'SUPER_ADMIN' || data.role === 'ADMIN';
      console.log(`✅ Admin detection: ${isAdmin ? 'ADMIN' : 'NOT ADMIN'}`);
      console.log(`📋 Role: ${data.role}`);
      
      if (isAdmin) {
        console.log('🎉 Admin user correctly detected!');
      } else {
        console.log('⚠️  Admin user not detected - role issue');
      }
    } else {
      console.log('❌ No profile data available for admin detection');
    }

  } catch (error) {
    console.error('💥 Test failed with exception:', error);
  }
}

// Run the test
testAuthenticationFix().then(() => {
  console.log('');
  console.log('🎯 Authentication Fix Test Complete');
  console.log('===================================');
  process.exit(0);
}).catch(error => {
  console.error('💥 Test suite failed:', error);
  process.exit(1);
});

import { test, expect } from '@playwright/test';

test.describe('Festival Family Database Verification', () => {
  test.beforeEach(async ({ page }) => {
    // Set longer timeout for database operations
    test.setTimeout(60000);
  });

  test('Database Connectivity Test', async ({ page }) => {
    // Navigate to our database test page
    await page.goto('file://' + process.cwd() + '/database-test.html');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Click the connectivity test button
    await page.click('button:has-text("Test Connection")');
    
    // Wait for result
    await page.waitForSelector('#connectivity-result', { timeout: 10000 });
    
    // Get the result text
    const result = await page.textContent('#connectivity-result');
    
    // Take screenshot for evidence
    await page.screenshot({ 
      path: 'test-results/database-connectivity.png',
      fullPage: true 
    });
    
    // Verify connection is successful
    expect(result).toContain('Database connection successful');
    
    console.log('✅ Database Connectivity Result:', result);
  });

  test('Table Existence Check', async ({ page }) => {
    await page.goto('file://' + process.cwd() + '/database-test.html');
    await page.waitForLoadState('networkidle');
    
    // Click the table check button
    await page.click('button:has-text("Check All Tables")');
    
    // Wait for results
    await page.waitForSelector('#tables-result', { timeout: 15000 });
    await page.waitForSelector('.table-item', { timeout: 15000 });
    
    // Get all table items
    const tableItems = await page.$$('.table-item');
    const tableResults = [];
    
    for (const item of tableItems) {
      const text = await item.textContent();
      const className = await item.getAttribute('class');
      tableResults.push({
        text: text?.trim(),
        exists: className?.includes('exists') || false
      });
    }
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-results/table-existence.png',
      fullPage: true 
    });
    
    // Log results
    console.log('📋 Table Existence Results:');
    tableResults.forEach(table => {
      console.log(`${table.exists ? '✅' : '❌'} ${table.text}`);
    });
    
    // Verify core tables exist
    const coreTablesExist = tableResults.filter(t => 
      t.text?.includes('profiles') || 
      t.text?.includes('festivals') || 
      t.text?.includes('activities')
    ).every(t => t.exists);
    
    expect(coreTablesExist).toBe(true);
    
    // Check for new tables
    const newTables = ['content_management', 'user_preferences', 'emergency_contacts', 'announcements'];
    const newTablesExist = newTables.map(tableName => {
      const tableResult = tableResults.find(t => t.text?.includes(tableName));
      return { name: tableName, exists: tableResult?.exists || false };
    });
    
    console.log('🆕 New Tables Status:');
    newTablesExist.forEach(table => {
      console.log(`${table.exists ? '✅' : '❌'} ${table.name}`);
    });
  });

  test('Admin Functions Test', async ({ page }) => {
    await page.goto('file://' + process.cwd() + '/database-test.html');
    await page.waitForLoadState('networkidle');
    
    // Click admin functions test
    await page.click('button:has-text("Test Admin Functions")');
    
    // Wait for results
    await page.waitForSelector('#admin-result', { timeout: 10000 });
    
    const result = await page.textContent('#admin-result');
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-results/admin-functions.png',
      fullPage: true 
    });
    
    console.log('⚙️ Admin Functions Result:', result);
    
    // Verify at least some admin functions work
    expect(result).toMatch(/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/);
  });

  test('CRUD Operations Test', async ({ page }) => {
    await page.goto('file://' + process.cwd() + '/database-test.html');
    await page.waitForLoadState('networkidle');
    
    // Click CRUD test
    await page.click('button:has-text("Test CRUD")');
    
    // Wait for results
    await page.waitForSelector('#crud-result', { timeout: 15000 });
    
    const result = await page.textContent('#crud-result');
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-results/crud-operations.png',
      fullPage: true 
    });
    
    console.log('🔄 CRUD Operations Result:', result);
    
    // Verify CRUD operations are accessible
    expect(result).toMatch(/(READ|CREATE|UPDATE|DELETE)/);
  });

  test('Profile System Test', async ({ page }) => {
    await page.goto('file://' + process.cwd() + '/database-test.html');
    await page.waitForLoadState('networkidle');
    
    // Click profile system test
    await page.click('button:has-text("Test Profile Updates")');
    
    // Wait for results
    await page.waitForSelector('#profile-result', { timeout: 10000 });
    
    const result = await page.textContent('#profile-result');
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-results/profile-system.png',
      fullPage: true 
    });
    
    console.log('👤 Profile System Result:', result);
    
    // Verify profile system is accessible
    expect(result).toMatch(/(Profile|profile|fields|storage)/);
  });
});

test.describe('Festival Family Admin Dashboard Tests', () => {
  test.beforeEach(async ({ page }) => {
    test.setTimeout(60000);
  });

  test('Admin Dashboard Authentication', async ({ page }) => {
    await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
    await page.waitForLoadState('networkidle');
    
    // Click authentication test
    await page.click('button:has-text("Test Admin Authentication")');
    
    // Wait for results
    await page.waitForSelector('#auth-result', { timeout: 10000 });
    
    const result = await page.textContent('#auth-result');
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-results/admin-authentication.png',
      fullPage: true 
    });
    
    console.log('🔐 Admin Authentication Result:', result);
    
    // Check if user needs to sign in or is already authenticated
    if (result?.includes('No active session')) {
      console.log('⚠️ User needs to sign in for admin testing');
    } else {
      expect(result).toMatch(/(authenticated|admin)/i);
    }
  });

  test('Admin Functions Status', async ({ page }) => {
    await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
    await page.waitForLoadState('networkidle');
    
    // Click admin functions test
    await page.click('button:has-text("Test All Admin Functions")');
    
    // Wait for results and grid
    await page.waitForSelector('#admin-functions-result', { timeout: 10000 });
    await page.waitForSelector('.status-item', { timeout: 5000 });
    
    // Get function status
    const statusItems = await page.$$('.status-item');
    const functionResults = [];
    
    for (const item of statusItems) {
      const text = await item.textContent();
      const className = await item.getAttribute('class');
      functionResults.push({
        text: text?.trim(),
        working: className?.includes('working') || false
      });
    }
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-results/admin-functions-status.png',
      fullPage: true 
    });
    
    console.log('⚙️ Admin Functions Status:');
    functionResults.forEach(func => {
      console.log(`${func.working ? '✅' : '❌'} ${func.text}`);
    });
  });

  test('Content Management Test', async ({ page }) => {
    await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
    await page.waitForLoadState('networkidle');
    
    // Click content management test
    await page.click('button:has-text("Test Content CRUD")');
    
    // Wait for results
    await page.waitForSelector('#content-result', { timeout: 15000 });
    
    const result = await page.textContent('#content-result');
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-results/content-management.png',
      fullPage: true 
    });
    
    console.log('📝 Content Management Result:', result);
  });

  test('Emergency Management Test', async ({ page }) => {
    await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
    await page.waitForLoadState('networkidle');
    
    // Click emergency management test
    await page.click('button:has-text("Test Emergency CRUD")');
    
    // Wait for results
    await page.waitForSelector('#emergency-result', { timeout: 10000 });
    
    const result = await page.textContent('#emergency-result');
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-results/emergency-management.png',
      fullPage: true 
    });
    
    console.log('🚨 Emergency Management Result:', result);
  });

  test('Application Accessibility Test', async ({ page }) => {
    // Test the main application
    await page.goto('http://localhost:5173');
    
    // Wait for app to load
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of main app
    await page.screenshot({ 
      path: 'test-results/main-application.png',
      fullPage: true 
    });
    
    // Check if app loads without errors
    const title = await page.title();
    expect(title).toBeTruthy();
    
    console.log('🎪 Main Application Title:', title);
    
    // Try to navigate to admin dashboard
    try {
      await page.goto('http://localhost:5173/admin');
      await page.waitForLoadState('networkidle', { timeout: 5000 });
      
      // Take screenshot of admin dashboard
      await page.screenshot({ 
        path: 'test-results/admin-dashboard.png',
        fullPage: true 
      });
      
      console.log('✅ Admin dashboard accessible');
    } catch (error) {
      console.log('⚠️ Admin dashboard may require authentication:', error);
    }
  });
});

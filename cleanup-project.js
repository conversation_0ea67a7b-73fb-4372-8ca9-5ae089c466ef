/**
 * Festival Family Project Cleanup Script
 * 
 * This script organizes redundant files into an archive folder
 * to clean up the project structure while preserving historical work
 */

import fs from 'fs';
import path from 'path';

console.log('🧹 Festival Family Project Cleanup');
console.log('==================================');

// Create archive directory structure
const archiveDir = 'archive';
const archiveSubdirs = [
  'docs-legacy',
  'test-scripts', 
  'migrations-legacy',
  'test-evidence'
];

console.log('📁 Creating archive directory structure...');

// Create archive directories
if (!fs.existsSync(archiveDir)) {
  fs.mkdirSync(archiveDir);
}

archiveSubdirs.forEach(subdir => {
  const fullPath = path.join(archiveDir, subdir);
  if (!fs.existsSync(fullPath)) {
    fs.mkdirSync(fullPath, { recursive: true });
    console.log(`   ✅ Created ${fullPath}`);
  }
});

// Files to archive (redundant documentation)
const redundantDocs = [
  'API_REFERENCE.md',
  'ARCHITECTURE.md',
  'AUTHENTICATION_TESTING_RESULTS.md',
  'CLEANUP_SUMMARY.md',
  'COMPETITIVE_ANALYSIS_MATRIX.md',
  'COMPLETE_MIGRATION_EXECUTION_GUIDE.md',
  'COMPREHENSIVE_AUDIT_SUMMARY.md',
  'COMPREHENSIVE_PRODUCTION_READINESS_ASSESSMENT.md',
  'COMPREHENSIVE_PROJECT_AUDIT_AND_COMPETITIVE_ANALYSIS.md',
  'COMPREHENSIVE_STATUS_REPORT.md',
  'CRITICAL_BUG_FIX_REPORT.md',
  'CRITICAL_FIXES_COMPLETED.md',
  'CRITICAL_FIXES_IMPLEMENTATION.md',
  'CRITICAL_ISSUES_DEBUG_REPORT.md',
  'DEPLOYMENT_GUIDE.md',
  'DEVELOPMENT_GUIDE.md',
  'EVIDENCE_BASED_TESTING_LOG.md',
  'FESTIVAL_FAMILY_VALUE_PROPOSITION.md',
  'FINAL_EVIDENCE_BASED_PRODUCTION_REPORT.md',
  'FINAL_PRODUCTION_ASSESSMENT.md',
  'FINAL_PRODUCTION_READINESS_SUMMARY.md',
  'FOUNDATIONAL_WORK_COMPLETION_REPORT.md',
  'IMPLEMENTATION_GUIDE.md',
  'IMPROVEMENT_PLAN.md',
  'MIGRATION_DEPENDENCY_RESOLUTION_GUIDE.md',
  'PRE_LAUNCH_OPTIMIZATION_ROADMAP.md',
  'PRODUCTION_ANALYSIS_2025_STANDARDS.md',
  'PRODUCTION_ARCHITECTURE.md',
  'PRODUCTION_READINESS_FINAL_REPORT.md',
  'PRODUCTION_READINESS_REPORT.md',
  'PRODUCTION_READINESS_SUMMARY.md',
  'PROJECT_STATUS_REPORT.md',
  'SECURITY_IMPLEMENTATION_SUMMARY.md',
  'SESSION_INITIALIZATION.md',
  'SYSTEMATIC_TESTING_EXECUTION_REPORT.md',
  'SYSTEMATIC_TESTING_REPORT.md',
  'TASK_5_RESEARCH_SUMMARY.md',
  'TECH_DEBT.md',
  'TESTING_GUIDE.md',
  'VALIDATION_CHECKLIST.md',
  'project-cleanup-analysis.md',
  'migration-execution-plan.md',
  'migration-status-analysis.md'
];

// Test scripts to archive
const redundantTestScripts = [
  'admin-access-test.js',
  'admin-dashboard-comprehensive-audit.js',
  'admin-dashboard-test.html',
  'admin-dashboard-validation.js',
  'admin-database-schema-audit.js',
  'admin-features-audit.js',
  'admin-session-persistence-test.js',
  'apply-security-migration.js',
  'apply-security-migrations-individually.js',
  'apply-xss-protection.js',
  'architecture-consistency-audit.js',
  'auth-signup-test.js',
  'authenticated-navigation-test.js',
  'browser-performance-assessment.js',
  'browser-security-assessment.js',
  'check-current-database-schema.js',
  'check-existing-users.js',
  'check-table-schema.js',
  'complete-application-validation.js',
  'comprehensive-admin-test.js',
  'comprehensive-feature-audit.js',
  'comprehensive-implementation-validation.js',
  'create-test-user-and-test-security.js',
  'debug-database.js',
  'debug-profile-fetching.js',
  'dev-server-manager.js',
  'diagnose-dev-server.js',
  'fix-auth-imports.js',
  'fix-data-structure-and-create-tips.js',
  'fix-privilege-escalation-direct.js',
  'frontend-backend-integration-analysis.js',
  'honest-database-audit.js',
  'manual-testing-script.js',
  'navigation-system-audit.js',
  'organize-tests.js',
  'performance-optimization-assessment.js',
  'post-migration-validation.js',
  'responsive-design-audit.js',
  'security-vulnerability-assessment.js',
  'setup-admin-user.js',
  'simple-admin-audit.js',
  'simple-admin-test.js',
  'simple-navigation-audit.js',
  'simple-profile-debug.js',
  'test-admin-functionality.js',
  'test-auth-fix.js',
  'test-critical-fixes.js',
  'test-existing-regular-user.js',
  'test-password-reset.js',
  'test-privilege-escalation-existing-user.js',
  'test-regular-user-auth.js',
  'test-regular-user-browser.js',
  'test-rls-fix-status.js',
  'test-security-implementations.js',
  'test-supabase-connection.js',
  'testApi.js',
  'user-journey-audit.js',
  'validate-applied-security-implementations.js',
  'comprehensive-production-validation.js',
  'database-test.html'
];

// SQL files to archive
const redundantSqlFiles = [
  'apply-missing-migrations.sql',
  'complete-schema-fix.sql',
  'database-reality-check.sql',
  'fix-existing-schema.sql',
  'simple-verification.sql',
  'step-by-step-fixes.sql',
  'verify-complete-fix.sql'
];

// Function to move files
function moveFiles(fileList, sourceDir, targetDir, description) {
  console.log(`\n📦 Moving ${description}...`);
  let movedCount = 0;
  
  fileList.forEach(filename => {
    const sourcePath = path.join(sourceDir, filename);
    const targetPath = path.join(targetDir, filename);
    
    if (fs.existsSync(sourcePath)) {
      try {
        fs.renameSync(sourcePath, targetPath);
        console.log(`   ✅ Moved ${filename}`);
        movedCount++;
      } catch (error) {
        console.log(`   ❌ Failed to move ${filename}: ${error.message}`);
      }
    }
  });
  
  console.log(`   📊 Moved ${movedCount}/${fileList.length} ${description}`);
  return movedCount;
}

// Function to move directories
function moveDirectories(dirPattern, targetDir, description) {
  console.log(`\n📁 Moving ${description}...`);
  let movedCount = 0;
  
  const currentDir = '.';
  const items = fs.readdirSync(currentDir);
  
  items.forEach(item => {
    const itemPath = path.join(currentDir, item);
    
    if (fs.statSync(itemPath).isDirectory() && item.includes(dirPattern)) {
      const targetPath = path.join(targetDir, item);
      
      try {
        fs.renameSync(itemPath, targetPath);
        console.log(`   ✅ Moved directory ${item}`);
        movedCount++;
      } catch (error) {
        console.log(`   ❌ Failed to move directory ${item}: ${error.message}`);
      }
    }
  });
  
  console.log(`   📊 Moved ${movedCount} ${description}`);
  return movedCount;
}

// Execute cleanup
let totalMoved = 0;

// Move redundant documentation
totalMoved += moveFiles(redundantDocs, '.', path.join(archiveDir, 'docs-legacy'), 'redundant documentation files');

// Move test scripts
totalMoved += moveFiles(redundantTestScripts, '.', path.join(archiveDir, 'test-scripts'), 'redundant test scripts');

// Move SQL files
totalMoved += moveFiles(redundantSqlFiles, '.', path.join(archiveDir, 'migrations-legacy'), 'redundant SQL files');

// Move evidence directories
totalMoved += moveDirectories('evidence', path.join(archiveDir, 'test-evidence'), 'evidence directories');
totalMoved += moveDirectories('results', path.join(archiveDir, 'test-evidence'), 'results directories');

// Create cleanup summary
const cleanupSummary = {
  timestamp: new Date().toISOString(),
  totalFilesArchived: totalMoved,
  archiveLocation: archiveDir,
  cleanupActions: [
    `Moved ${redundantDocs.length} redundant documentation files`,
    `Moved ${redundantTestScripts.length} redundant test scripts`,
    `Moved ${redundantSqlFiles.length} redundant SQL files`,
    'Moved evidence and results directories'
  ],
  remainingEssentialFiles: [
    'src/ (entire application source)',
    'public/ (static assets)',
    'supabase/ (database configuration)',
    'tests/ (essential Playwright tests)',
    'package.json (dependencies)',
    'Configuration files (tsconfig.json, vite.config.ts, etc.)',
    'README.md (main project documentation)'
  ]
};

fs.writeFileSync(
  path.join(archiveDir, 'cleanup-summary.json'),
  JSON.stringify(cleanupSummary, null, 2)
);

console.log('\n🎉 PROJECT CLEANUP COMPLETE');
console.log('===========================');
console.log(`📊 Total files archived: ${totalMoved}`);
console.log(`📁 Archive location: ${archiveDir}/`);
console.log('✅ Project structure is now clean and organized');
console.log('📋 Cleanup summary saved to archive/cleanup-summary.json');

console.log('\n📋 REMAINING ESSENTIAL FILES:');
console.log('- src/ (application source code)');
console.log('- public/ (static assets)');
console.log('- supabase/ (database configuration)');
console.log('- tests/ (essential tests)');
console.log('- package.json & configuration files');
console.log('- README.md');
console.log('- archive/ (historical files)');

console.log('\n🚀 Your Festival Family project is now ready for clean development!');

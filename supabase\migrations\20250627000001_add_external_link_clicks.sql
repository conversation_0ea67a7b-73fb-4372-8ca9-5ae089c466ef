-- Migration: Add external link click tracking
-- Created: 2025-06-27
-- Purpose: Track user interactions with external chat links for analytics

-- Create external_link_clicks table for user feedback tracking
CREATE TABLE IF NOT EXISTS external_link_clicks (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    link_id UUID REFERENCES external_links(id) ON DELETE CASCADE,
    link_url TEXT NOT NULL,
    link_title TEXT NOT NULL,
    link_category TEXT NOT NULL,
    user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    clicked_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    user_agent TEXT,
    ip_address INET,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_external_link_clicks_link_id ON external_link_clicks(link_id);
CREATE INDEX IF NOT EXISTS idx_external_link_clicks_user_id ON external_link_clicks(user_id);
CREATE INDEX IF NOT EXISTS idx_external_link_clicks_clicked_at ON external_link_clicks(clicked_at);
CREATE INDEX IF NOT EXISTS idx_external_link_clicks_category ON external_link_clicks(link_category);

-- Enable RLS
ALTER TABLE external_link_clicks ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can insert their own clicks" ON external_link_clicks
    FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Admins can view all clicks" ON external_link_clicks
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('SUPER_ADMIN', 'CONTENT_ADMIN')
        )
    );

-- Add helpful comments
COMMENT ON TABLE external_link_clicks IS 'Tracks user interactions with external chat links for analytics and optimization';
COMMENT ON COLUMN external_link_clicks.link_id IS 'Reference to the external link that was clicked';
COMMENT ON COLUMN external_link_clicks.user_id IS 'User who clicked the link (null for anonymous clicks)';
COMMENT ON COLUMN external_link_clicks.clicked_at IS 'When the link was clicked';
COMMENT ON COLUMN external_link_clicks.link_category IS 'Category of the link for analytics grouping';

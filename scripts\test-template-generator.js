#!/usr/bin/env node

/**
 * Test Template Generator
 * 
 * Automatically generates test files for new components using intelligent templates
 * based on component type, props, and functionality patterns.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// Template configurations
const COMPONENT_TEMPLATES = {
  // React functional component template
  COMPONENT: {
    pattern: /^[A-Z][a-zA-Z0-9]*\.tsx?$/,
    template: 'component-template.test.tsx',
    testDir: 'src/__tests__/components'
  },
  
  // React page component template
  PAGE: {
    pattern: /src\/pages\/.*\.tsx?$/,
    template: 'page-template.test.tsx',
    testDir: 'src/__tests__/pages'
  },
  
  // Custom hook template
  HOOK: {
    pattern: /^use[A-Z][a-zA-Z0-9]*\.ts$/,
    template: 'hook-template.test.ts',
    testDir: 'src/__tests__/hooks'
  },
  
  // Service/API template
  SERVICE: {
    pattern: /src\/(api|lib|services)\/.*\.ts$/,
    template: 'service-template.test.ts',
    testDir: 'src/__tests__/services'
  },
  
  // Utility function template
  UTILITY: {
    pattern: /src\/utils\/.*\.ts$/,
    template: 'utility-template.test.ts',
    testDir: 'src/__tests__/utils'
  },
  
  // Admin component template
  ADMIN: {
    pattern: /src\/(components\/admin|pages\/admin)\/.*\.tsx?$/,
    template: 'admin-template.test.tsx',
    testDir: 'src/__tests__/admin'
  }
};

// Test templates
const TEMPLATES = {
  'component-template.test.tsx': `/**
 * {{COMPONENT_NAME}} Component Tests
 * 
 * Generated automatically by Test Template Generator
 * Customize these tests based on your component's specific functionality.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { {{COMPONENT_NAME}} } from '{{IMPORT_PATH}}';
import { TestWrapper } from '../../test/test-utils';

// Mock dependencies if needed
jest.mock('{{MOCK_PATH}}', () => ({
  // Add mocks here
}));

describe('{{COMPONENT_NAME}}', () => {
  // Basic rendering tests
  describe('Rendering', () => {
    test('should render without crashing', () => {
      render(
        <TestWrapper>
          <{{COMPONENT_NAME}} />
        </TestWrapper>
      );
      
      expect(screen.getByTestId('{{COMPONENT_TESTID}}')).toBeInTheDocument();
    });

    test('should render with required props', () => {
      const props = {
        // Add required props here
      };
      
      render(
        <TestWrapper>
          <{{COMPONENT_NAME}} {...props} />
        </TestWrapper>
      );
      
      expect(screen.getByTestId('{{COMPONENT_TESTID}}')).toBeInTheDocument();
    });

    test('should apply custom className', () => {
      const customClass = 'custom-test-class';
      
      render(
        <TestWrapper>
          <{{COMPONENT_NAME}} className={customClass} />
        </TestWrapper>
      );
      
      expect(screen.getByTestId('{{COMPONENT_TESTID}}')).toHaveClass(customClass);
    });
  });

  // Interaction tests
  describe('Interactions', () => {
    test('should handle click events', async () => {
      const handleClick = jest.fn();
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <{{COMPONENT_NAME}} onClick={handleClick} />
        </TestWrapper>
      );
      
      await user.click(screen.getByTestId('{{COMPONENT_TESTID}}'));
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    test('should handle keyboard navigation', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <{{COMPONENT_NAME}} />
        </TestWrapper>
      );
      
      const element = screen.getByTestId('{{COMPONENT_TESTID}}');
      await user.tab();
      expect(element).toHaveFocus();
    });
  });

  // Props validation tests
  describe('Props', () => {
    test('should handle optional props', () => {
      const optionalProps = {
        // Add optional props here
      };
      
      render(
        <TestWrapper>
          <{{COMPONENT_NAME}} {...optionalProps} />
        </TestWrapper>
      );
      
      expect(screen.getByTestId('{{COMPONENT_TESTID}}')).toBeInTheDocument();
    });

    test('should handle edge case props', () => {
      const edgeCaseProps = {
        // Add edge case props here
      };
      
      render(
        <TestWrapper>
          <{{COMPONENT_NAME}} {...edgeCaseProps} />
        </TestWrapper>
      );
      
      expect(screen.getByTestId('{{COMPONENT_TESTID}}')).toBeInTheDocument();
    });
  });

  // Accessibility tests
  describe('Accessibility', () => {
    test('should have proper ARIA attributes', () => {
      render(
        <TestWrapper>
          <{{COMPONENT_NAME}} />
        </TestWrapper>
      );
      
      const element = screen.getByTestId('{{COMPONENT_TESTID}}');
      // Add specific ARIA attribute checks
      expect(element).toBeInTheDocument();
    });

    test('should support screen readers', () => {
      render(
        <TestWrapper>
          <{{COMPONENT_NAME}} />
        </TestWrapper>
      );
      
      // Add screen reader support tests
      expect(screen.getByTestId('{{COMPONENT_TESTID}}')).toBeInTheDocument();
    });
  });

  // Error handling tests
  describe('Error Handling', () => {
    test('should handle errors gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      render(
        <TestWrapper>
          <{{COMPONENT_NAME}} />
        </TestWrapper>
      );
      
      // Add error scenario tests
      expect(screen.getByTestId('{{COMPONENT_TESTID}}')).toBeInTheDocument();
      
      consoleSpy.mockRestore();
    });
  });
});`,

  'hook-template.test.ts': `/**
 * {{HOOK_NAME}} Hook Tests
 * 
 * Generated automatically by Test Template Generator
 * Customize these tests based on your hook's specific functionality.
 */

import { renderHook, act } from '@testing-library/react';
import { {{HOOK_NAME}} } from '{{IMPORT_PATH}}';
import { TestWrapper } from '../../test/test-utils';

// Mock dependencies if needed
jest.mock('{{MOCK_PATH}}', () => ({
  // Add mocks here
}));

describe('{{HOOK_NAME}}', () => {
  // Basic functionality tests
  describe('Basic Functionality', () => {
    test('should initialize with default values', () => {
      const { result } = renderHook(() => {{HOOK_NAME}}(), {
        wrapper: TestWrapper
      });
      
      // Add assertions for initial state
      expect(result.current).toBeDefined();
    });

    test('should handle parameters correctly', () => {
      const params = {
        // Add test parameters
      };
      
      const { result } = renderHook(() => {{HOOK_NAME}}(params), {
        wrapper: TestWrapper
      });
      
      expect(result.current).toBeDefined();
    });
  });

  // State management tests
  describe('State Management', () => {
    test('should update state correctly', () => {
      const { result } = renderHook(() => {{HOOK_NAME}}(), {
        wrapper: TestWrapper
      });
      
      act(() => {
        // Add state update actions
      });
      
      // Add assertions for updated state
      expect(result.current).toBeDefined();
    });

    test('should handle async operations', async () => {
      const { result } = renderHook(() => {{HOOK_NAME}}(), {
        wrapper: TestWrapper
      });
      
      await act(async () => {
        // Add async operations
      });
      
      expect(result.current).toBeDefined();
    });
  });

  // Error handling tests
  describe('Error Handling', () => {
    test('should handle errors gracefully', () => {
      const { result } = renderHook(() => {{HOOK_NAME}}(), {
        wrapper: TestWrapper
      });
      
      // Add error scenario tests
      expect(result.current).toBeDefined();
    });
  });

  // Cleanup tests
  describe('Cleanup', () => {
    test('should cleanup resources on unmount', () => {
      const { unmount } = renderHook(() => {{HOOK_NAME}}(), {
        wrapper: TestWrapper
      });
      
      unmount();
      
      // Add cleanup assertions
    });
  });
});`,

  'service-template.test.ts': `/**
 * {{SERVICE_NAME}} Service Tests
 * 
 * Generated automatically by Test Template Generator
 * Customize these tests based on your service's specific functionality.
 */

import { {{SERVICE_NAME}} } from '{{IMPORT_PATH}}';

// Mock dependencies
jest.mock('{{MOCK_PATH}}', () => ({
  // Add mocks here
}));

describe('{{SERVICE_NAME}}', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Basic functionality tests
  describe('Basic Functionality', () => {
    test('should be defined', () => {
      expect({{SERVICE_NAME}}).toBeDefined();
    });

    test('should handle valid input', async () => {
      const input = {
        // Add test input
      };
      
      const result = await {{SERVICE_NAME}}(input);
      
      expect(result).toBeDefined();
    });
  });

  // Error handling tests
  describe('Error Handling', () => {
    test('should handle invalid input', async () => {
      const invalidInput = {
        // Add invalid input
      };
      
      await expect({{SERVICE_NAME}}(invalidInput)).rejects.toThrow();
    });

    test('should handle network errors', async () => {
      // Mock network error
      
      await expect({{SERVICE_NAME}}({})).rejects.toThrow();
    });
  });

  // Edge cases
  describe('Edge Cases', () => {
    test('should handle empty input', async () => {
      const result = await {{SERVICE_NAME}}({});
      
      expect(result).toBeDefined();
    });

    test('should handle large datasets', async () => {
      const largeInput = {
        // Add large dataset
      };
      
      const result = await {{SERVICE_NAME}}(largeInput);
      
      expect(result).toBeDefined();
    });
  });
});`
};

// Utility functions
function logInfo(message) {
  console.log(`🧪 [Test Generator] ${message}`);
}

function logSuccess(message) {
  console.log(`✅ [Test Generator] ${message}`);
}

function logError(message) {
  console.log(`❌ [Test Generator] ${message}`);
}

function detectComponentType(filePath) {
  for (const [type, config] of Object.entries(COMPONENT_TEMPLATES)) {
    if (config.pattern.test(filePath) || config.pattern.test(path.basename(filePath))) {
      return { type, config };
    }
  }
  return null;
}

function extractComponentInfo(filePath) {
  const fileName = path.basename(filePath, path.extname(filePath));
  const componentName = fileName;
  const importPath = path.relative('src', filePath).replace(/\.(ts|tsx)$/, '');
  
  return {
    componentName,
    fileName,
    importPath: `../../${importPath}`,
    testId: componentName.toLowerCase().replace(/([A-Z])/g, '-$1').substring(1),
    mockPath: '../mocks/' + fileName.toLowerCase()
  };
}

function generateTestContent(templateKey, componentInfo) {
  let template = TEMPLATES[templateKey];
  
  if (!template) {
    logError(`Template not found: ${templateKey}`);
    return null;
  }
  
  // Replace placeholders
  template = template.replace(/\{\{COMPONENT_NAME\}\}/g, componentInfo.componentName);
  template = template.replace(/\{\{HOOK_NAME\}\}/g, componentInfo.componentName);
  template = template.replace(/\{\{SERVICE_NAME\}\}/g, componentInfo.componentName);
  template = template.replace(/\{\{IMPORT_PATH\}\}/g, componentInfo.importPath);
  template = template.replace(/\{\{COMPONENT_TESTID\}\}/g, componentInfo.testId);
  template = template.replace(/\{\{MOCK_PATH\}\}/g, componentInfo.mockPath);
  
  return template;
}

function generateTestFile(filePath) {
  logInfo(`Generating test for: ${filePath}`);
  
  const componentType = detectComponentType(filePath);
  if (!componentType) {
    logError(`Could not detect component type for: ${filePath}`);
    return false;
  }
  
  const componentInfo = extractComponentInfo(filePath);
  const testContent = generateTestContent(componentType.config.template, componentInfo);
  
  if (!testContent) {
    return false;
  }
  
  // Determine test file path
  const testDir = componentType.config.testDir;
  const testFileName = `${componentInfo.fileName}.test.${path.extname(filePath).substring(1)}`;
  const testFilePath = path.join(testDir, testFileName);
  
  // Create test directory if it doesn't exist
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
    logInfo(`Created test directory: ${testDir}`);
  }
  
  // Check if test file already exists
  if (fs.existsSync(testFilePath)) {
    logError(`Test file already exists: ${testFilePath}`);
    return false;
  }
  
  // Write test file
  try {
    fs.writeFileSync(testFilePath, testContent);
    logSuccess(`Generated test file: ${testFilePath}`);
    
    // Run the new test to verify it works
    try {
      execSync(`npm run test:unit -- ${testFilePath} --passWithNoTests`, { stdio: 'pipe' });
      logSuccess(`New test file passes initial validation`);
    } catch (error) {
      logError(`New test file has issues: ${error.message}`);
    }
    
    return true;
  } catch (error) {
    logError(`Failed to write test file: ${error.message}`);
    return false;
  }
}

// CLI interface
function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🧪 Test Template Generator

Usage:
  node test-template-generator.js <file-path>
  
Examples:
  node test-template-generator.js src/components/NewComponent.tsx
  node test-template-generator.js src/hooks/useNewHook.ts
  node test-template-generator.js src/pages/NewPage.tsx
    `);
    return;
  }
  
  const filePath = args[0];
  
  if (!fs.existsSync(filePath)) {
    logError(`File does not exist: ${filePath}`);
    return;
  }
  
  const success = generateTestFile(filePath);
  process.exit(success ? 0 : 1);
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { generateTestFile, detectComponentType, extractComponentInfo };

/**
 * Manual Events Form Test
 * 
 * Simple test to manually verify events form functionality
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

test('Manual Events Form Test', async ({ page }) => {
  console.log('🧪 Manual events form test...');
  
  const loginSuccess = await loginAsAdmin(page);
  console.log(`Login success: ${loginSuccess}`);
  
  if (loginSuccess) {
    // Navigate to events form
    await page.goto('/admin/events/new');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    console.log('📝 Checking events form...');
    
    // Check if form elements exist
    const hasTitle = await page.locator('input[name="title"]').count() > 0;
    const hasDescription = await page.locator('textarea[name="description"]').count() > 0;
    const hasLocation = await page.locator('input[name="location"]').count() > 0;
    const hasStartDate = await page.locator('input[name="start_date"]').count() > 0;
    const hasEndDate = await page.locator('input[name="end_date"]').count() > 0;
    const hasFestivalId = await page.locator('input[name="festival_id"]').count() > 0;
    const hasSubmitButton = await page.locator('button[type="submit"]').count() > 0;
    
    console.log(`Form elements found:`);
    console.log(`  Title field: ${hasTitle}`);
    console.log(`  Description field: ${hasDescription}`);
    console.log(`  Location field: ${hasLocation}`);
    console.log(`  Start date field: ${hasStartDate}`);
    console.log(`  End date field: ${hasEndDate}`);
    console.log(`  Festival ID field: ${hasFestivalId}`);
    console.log(`  Submit button: ${hasSubmitButton}`);
    
    await page.screenshot({ path: 'test-results/events-form-manual.png', fullPage: true });
    
    // Try to fill a simple form
    if (hasTitle && hasDescription && hasLocation) {
      console.log('📝 Filling basic form data...');
      
      await page.fill('input[name="title"]', 'Manual Test Event');
      await page.fill('textarea[name="description"]', 'This is a manual test event.');
      await page.fill('input[name="location"]', 'Manual Test Location');
      
      await page.screenshot({ path: 'test-results/events-form-manual-filled.png', fullPage: true });
      
      console.log('✅ Basic form data filled successfully');
    }
    
    // Check if we can navigate to events list
    await page.goto('/admin/events');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const eventRows = await page.locator('tbody tr').count();
    console.log(`Events list shows ${eventRows} events`);
    
    await page.screenshot({ path: 'test-results/events-list-manual.png', fullPage: true });
    
    console.log('✅ Manual events test completed');
  }
});

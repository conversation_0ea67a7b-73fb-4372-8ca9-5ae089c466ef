import { useCallback, useState, useEffect } from 'react';
import { unifiedErrorHandler, type ErrorHandlingOptions, type RetryOptions, type ProcessedError } from '@/lib/error/unified-error-handler';

interface UseErrorHandlingOptions extends ErrorHandlingOptions {
  component?: string;
  enableErrorBoundary?: boolean;
}

interface UseErrorHandlingReturn {
  executeWithHandling: <T>(
    fn: () => Promise<T>,
    options?: ErrorHandlingOptions & RetryOptions
  ) => Promise<T | null>;
  handleError: (error: Error | unknown, options?: ErrorHandlingOptions) => Promise<ProcessedError>;
  withRetry: <T>(fn: () => Promise<T>, options?: RetryOptions) => Promise<T>;
  errorStats: {
    total: number;
    bySeverity: Record<string, number>;
    recent: ProcessedError[];
  };
  clearErrors: () => void;
}

export const useErrorHandling = (
  defaultOptions: UseErrorHandlingOptions = {}
): UseErrorHandlingReturn => {
  const [errorStats, setErrorStats] = useState({
    total: 0,
    bySeverity: { low: 0, medium: 0, high: 0, critical: 0 },
    recent: [] as ProcessedError[]
  });

  // Update error stats periodically
  useEffect(() => {
    const updateStats = () => {
      const stats = unifiedErrorHandler.getErrorStats();
      setErrorStats({
        total: stats.total,
        bySeverity: stats.bySeverity,
        recent: stats.recent
      });
    };

    updateStats();
    const interval = setInterval(updateStats, 10000); // Update every 10 seconds

    return () => clearInterval(interval);
  }, []);

  const executeWithHandling = useCallback(async <T>(
    fn: () => Promise<T>,
    options: ErrorHandlingOptions & RetryOptions = {}
  ): Promise<T | null> => {
    const mergedOptions = {
      ...defaultOptions,
      ...options,
      context: {
        component: defaultOptions.component,
        ...defaultOptions.context,
        ...options.context
      }
    };

    try {
      return await unifiedErrorHandler.executeWithHandling(fn, mergedOptions);
    } catch (error) {
      // Error was already handled by unifiedErrorHandler
      return null;
    }
  }, [defaultOptions]);

  const handleError = useCallback(async (
    error: Error | unknown,
    options: ErrorHandlingOptions = {}
  ): Promise<ProcessedError> => {
    const mergedOptions = {
      ...defaultOptions,
      ...options,
      context: {
        component: defaultOptions.component,
        ...defaultOptions.context,
        ...options.context
      }
    };

    return await unifiedErrorHandler.handleError(error, mergedOptions);
  }, [defaultOptions]);

  const withRetry = useCallback(async <T>(
    fn: () => Promise<T>,
    options: RetryOptions = {}
  ): Promise<T> => {
    return await unifiedErrorHandler.withRetry(fn, options);
  }, []);

  const clearErrors = useCallback(() => {
    // Reset error stats
    setErrorStats({
      total: 0,
      bySeverity: { low: 0, medium: 0, high: 0, critical: 0 },
      recent: []
    });
  }, []);

  return {
    executeWithHandling,
    handleError,
    withRetry,
    errorStats,
    clearErrors
  };
};

// Hook for database operations with automatic error handling
export const useDatabaseErrorHandling = () => {
  return useErrorHandling({
    component: 'Database',
    showToast: true,
    logError: true,
    retryOptions: {
      maxRetries: 2,
      baseDelay: 1000,
      backoffFactor: 2
    }
  });
};

// Hook for API operations with automatic error handling
export const useApiErrorHandling = () => {
  return useErrorHandling({
    component: 'API',
    showToast: true,
    logError: true,
    retryOptions: {
      maxRetries: 3,
      baseDelay: 500,
      backoffFactor: 1.5
    }
  });
};

// Hook for user interaction error handling
export const useUserInteractionErrorHandling = () => {
  return useErrorHandling({
    component: 'UserInteraction',
    showToast: true,
    logError: true,
    retryOptions: {
      maxRetries: 1,
      baseDelay: 1000
    }
  });
};

export default useErrorHandling;

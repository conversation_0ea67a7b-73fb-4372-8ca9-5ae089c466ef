import { test, expect } from '@playwright/test';

/**
 * Comprehensive Admin Form Text Readability Test
 * 
 * This test systematically verifies that ALL admin forms have proper text contrast
 * and readability, addressing the white text on white background issue.
 */

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

const ADMIN_FORMS = [
  { name: 'Events', path: '/admin/events/new', title: 'Create Event' },
  { name: 'Activities', path: '/admin/activities/new', title: 'Create Activity' },
  { name: 'Festivals', path: '/admin/festivals/new', title: 'Create Festival' },
  { name: 'Announcements', path: '/admin/announcements', title: 'Announcements' },
  { name: 'FAQs', path: '/admin/faqs/new', title: 'Create FAQ' },
  { name: 'Guides', path: '/admin/guides/new', title: 'Create Guide' },
  { name: 'Tips', path: '/admin/tips/new', title: 'Create Tip' },
  { name: 'External Links', path: '/admin/external-links', title: 'External Links' },
  { name: 'Users', path: '/admin/users', title: 'Users' }
];

async function loginAsAdmin(page) {
  console.log('🔐 Logging in as admin...');
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  
  // Fill login form
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  
  // Wait for redirect to admin dashboard
  await page.waitForURL('/admin', { timeout: 10000 });
  console.log('✅ Successfully logged in as admin');
}

async function checkTextReadability(page, formName) {
  console.log(`🔍 Checking text readability for ${formName}...`);
  
  const readabilityIssues = [];
  
  // Check form titles
  const titles = await page.locator('h1, h2, h3').all();
  for (const title of titles) {
    const text = await title.textContent();
    const styles = await title.evaluate(el => {
      const computed = window.getComputedStyle(el);
      return {
        color: computed.color,
        backgroundColor: computed.backgroundColor,
        visibility: computed.visibility,
        opacity: computed.opacity
      };
    });
    
    if (styles.color === 'rgb(255, 255, 255)' && 
        (styles.backgroundColor === 'rgb(255, 255, 255)' || styles.backgroundColor === 'rgba(0, 0, 0, 0)')) {
      readabilityIssues.push(`Title "${text}" has white text on white/transparent background`);
    }
  }
  
  // Check form labels
  const labels = await page.locator('label, .form-label').all();
  for (const label of labels) {
    const text = await label.textContent();
    if (!text || text.trim() === '') continue;
    
    const styles = await label.evaluate(el => {
      const computed = window.getComputedStyle(el);
      return {
        color: computed.color,
        backgroundColor: computed.backgroundColor
      };
    });
    
    if (styles.color === 'rgb(255, 255, 255)' && 
        (styles.backgroundColor === 'rgb(255, 255, 255)' || styles.backgroundColor === 'rgba(0, 0, 0, 0)')) {
      readabilityIssues.push(`Label "${text}" has white text on white/transparent background`);
    }
  }
  
  // Check input fields
  const inputs = await page.locator('input, textarea, select').all();
  for (const input of inputs) {
    const styles = await input.evaluate(el => {
      const computed = window.getComputedStyle(el);
      return {
        color: computed.color,
        backgroundColor: computed.backgroundColor,
        placeholder: el.placeholder || ''
      };
    });
    
    if (styles.color === 'rgb(255, 255, 255)' && 
        styles.backgroundColor === 'rgb(255, 255, 255)') {
      readabilityIssues.push(`Input field (${styles.placeholder}) has white text on white background`);
    }
  }
  
  return readabilityIssues;
}

test.describe('Admin Form Text Readability Audit', () => {
  test.beforeEach(async ({ page }) => {
    await loginAsAdmin(page);
  });

  for (const form of ADMIN_FORMS) {
    test(`${form.name} form should have readable text`, async ({ page }) => {
      console.log(`\n📝 Testing ${form.name} form readability...`);
      
      try {
        // Navigate to the form
        await page.goto(form.path);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000); // Allow time for styling to load
        
        // Take screenshot for evidence
        await page.screenshot({ 
          path: `test-results/readability-${form.name.toLowerCase()}.png`, 
          fullPage: true 
        });
        
        // Check for readability issues
        const issues = await checkTextReadability(page, form.name);
        
        if (issues.length > 0) {
          console.log(`❌ ${form.name} has ${issues.length} readability issues:`);
          issues.forEach((issue, index) => {
            console.log(`   ${index + 1}. ${issue}`);
          });
          
          // Fail the test with detailed information
          throw new Error(`${form.name} form has text readability issues:\n${issues.join('\n')}`);
        } else {
          console.log(`✅ ${form.name} form has good text readability`);
        }
        
      } catch (error) {
        console.log(`❌ Error testing ${form.name}: ${error.message}`);
        throw error;
      }
    });
  }

  test('Summary: Overall admin form readability assessment', async ({ page }) => {
    console.log('\n📊 ADMIN FORM READABILITY SUMMARY');
    console.log('='.repeat(50));
    
    const results = [];
    
    for (const form of ADMIN_FORMS) {
      try {
        await page.goto(form.path);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(1000);
        
        const issues = await checkTextReadability(page, form.name);
        results.push({
          name: form.name,
          path: form.path,
          issues: issues.length,
          status: issues.length === 0 ? 'PASS' : 'FAIL',
          details: issues
        });
        
      } catch (error) {
        results.push({
          name: form.name,
          path: form.path,
          issues: -1,
          status: 'ERROR',
          details: [error.message]
        });
      }
    }
    
    // Print summary
    console.log('\nForm Readability Results:');
    results.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
      console.log(`${status} ${result.name}: ${result.status} (${result.issues} issues)`);
    });
    
    const passCount = results.filter(r => r.status === 'PASS').length;
    const totalCount = results.length;
    
    console.log(`\n📈 Overall Score: ${passCount}/${totalCount} forms have good readability`);
    
    if (passCount < totalCount) {
      const failedForms = results.filter(r => r.status !== 'PASS');
      console.log('\n🚨 Forms with issues:');
      failedForms.forEach(form => {
        console.log(`\n${form.name}:`);
        form.details.forEach(detail => console.log(`  - ${detail}`));
      });
    }
    
    // The test passes if all forms have good readability
    expect(passCount).toBe(totalCount);
  });
});

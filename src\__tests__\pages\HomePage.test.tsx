/**
 * HomePage Component Tests
 * 
 * Tests for the main home page component including navigation and content rendering.
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { MemoryRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// Mock HomePage component
const MockHomePage: React.FC = () => {
  return (
    <div data-testid="home-page">
      <header>
        <h1>Festival Family</h1>
        <nav>
          <a href="/discover">Discover</a>
          <a href="/events">Events</a>
          <a href="/profile">Profile</a>
        </nav>
      </header>
      
      <main>
        <section data-testid="hero-section">
          <h2>Find Your Festival Tribe</h2>
          <p>Connect with like-minded music lovers and never go to a festival alone again.</p>
          <button data-testid="get-started-btn">Get Started</button>
        </section>
        
        <section data-testid="features-section">
          <h3>Features</h3>
          <div className="feature-grid">
            <div data-testid="feature-connections">
              <h4>Make Connections</h4>
              <p>Find people with similar music tastes</p>
            </div>
            <div data-testid="feature-events">
              <h4>Discover Events</h4>
              <p>Find festivals and events near you</p>
            </div>
            <div data-testid="feature-groups">
              <h4>Join Groups</h4>
              <p>Connect with festival communities</p>
            </div>
          </div>
        </section>
        
        <section data-testid="cta-section">
          <h3>Ready to Start?</h3>
          <button data-testid="signup-btn">Sign Up Now</button>
        </section>
      </main>
    </div>
  )
}

// Mock the auth provider
const mockAuthContext = {
  session: null,
  user: null,
  profile: null,
  loading: false,
  isAdmin: false,
  signIn: jest.fn(),
  signUp: jest.fn(),
  signOut: jest.fn(),
  refreshProfile: jest.fn(),
}

jest.mock('../../providers/ConsolidatedAuthProvider', () => ({
  useAuth: () => mockAuthContext,
}))

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <MemoryRouter>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </MemoryRouter>
  )
}

describe('HomePage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('should render home page with main content', () => {
    render(
      <TestWrapper>
        <MockHomePage />
      </TestWrapper>
    )

    expect(screen.getByTestId('home-page')).toBeInTheDocument()
    expect(screen.getByText('Festival Family')).toBeInTheDocument()
    expect(screen.getByText('Find Your Festival Tribe')).toBeInTheDocument()
  })

  test('should render navigation links', () => {
    render(
      <TestWrapper>
        <MockHomePage />
      </TestWrapper>
    )

    expect(screen.getByRole('link', { name: /discover/i })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /events/i })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /profile/i })).toBeInTheDocument()
  })

  test('should render hero section with call-to-action', () => {
    render(
      <TestWrapper>
        <MockHomePage />
      </TestWrapper>
    )

    expect(screen.getByTestId('hero-section')).toBeInTheDocument()
    expect(screen.getByText('Connect with like-minded music lovers and never go to a festival alone again.')).toBeInTheDocument()
    expect(screen.getByTestId('get-started-btn')).toBeInTheDocument()
  })

  test('should render features section with all features', () => {
    render(
      <TestWrapper>
        <MockHomePage />
      </TestWrapper>
    )

    expect(screen.getByTestId('features-section')).toBeInTheDocument()
    
    // Check individual features
    expect(screen.getByTestId('feature-connections')).toBeInTheDocument()
    expect(screen.getByText('Make Connections')).toBeInTheDocument()
    expect(screen.getByText('Find people with similar music tastes')).toBeInTheDocument()
    
    expect(screen.getByTestId('feature-events')).toBeInTheDocument()
    expect(screen.getByText('Discover Events')).toBeInTheDocument()
    expect(screen.getByText('Find festivals and events near you')).toBeInTheDocument()
    
    expect(screen.getByTestId('feature-groups')).toBeInTheDocument()
    expect(screen.getByText('Join Groups')).toBeInTheDocument()
    expect(screen.getByText('Connect with festival communities')).toBeInTheDocument()
  })

  test('should render call-to-action section', () => {
    render(
      <TestWrapper>
        <MockHomePage />
      </TestWrapper>
    )

    expect(screen.getByTestId('cta-section')).toBeInTheDocument()
    expect(screen.getByText('Ready to Start?')).toBeInTheDocument()
    expect(screen.getByTestId('signup-btn')).toBeInTheDocument()
  })

  test('should have proper semantic structure', () => {
    render(
      <TestWrapper>
        <MockHomePage />
      </TestWrapper>
    )

    // Check for semantic HTML elements
    expect(screen.getByRole('banner')).toBeInTheDocument() // header
    expect(screen.getByRole('main')).toBeInTheDocument() // main
    expect(screen.getByRole('navigation')).toBeInTheDocument() // nav
  })

  test('should render all action buttons', () => {
    render(
      <TestWrapper>
        <MockHomePage />
      </TestWrapper>
    )

    const getStartedBtn = screen.getByTestId('get-started-btn')
    const signupBtn = screen.getByTestId('signup-btn')
    
    expect(getStartedBtn).toBeInTheDocument()
    expect(signupBtn).toBeInTheDocument()
    
    // Check button text
    expect(getStartedBtn).toHaveTextContent('Get Started')
    expect(signupBtn).toHaveTextContent('Sign Up Now')
  })

  test('should handle different authentication states', () => {
    // Test with authenticated user
    mockAuthContext.user = {
      id: 'user-id',
      email: '<EMAIL>',
      created_at: '2023-01-01T00:00:00.000Z',
    } as any
    mockAuthContext.session = {
      user: mockAuthContext.user,
      access_token: 'token',
      refresh_token: 'refresh',
    } as any

    render(
      <TestWrapper>
        <MockHomePage />
      </TestWrapper>
    )

    // Page should still render normally
    expect(screen.getByTestId('home-page')).toBeInTheDocument()
  })

  test('should be accessible with proper headings hierarchy', () => {
    render(
      <TestWrapper>
        <MockHomePage />
      </TestWrapper>
    )

    // Check heading hierarchy
    const h1 = screen.getByRole('heading', { level: 1 })
    const h2 = screen.getByRole('heading', { level: 2 })
    const h3Elements = screen.getAllByRole('heading', { level: 3 })
    const h4Elements = screen.getAllByRole('heading', { level: 4 })

    expect(h1).toHaveTextContent('Festival Family')
    expect(h2).toHaveTextContent('Find Your Festival Tribe')
    expect(h3Elements).toHaveLength(2) // Features and Ready to Start
    expect(h4Elements).toHaveLength(3) // Three feature headings
  })
})

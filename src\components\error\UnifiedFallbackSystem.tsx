/**
 * Unified Fallback System
 *
 * Provides comprehensive fallback components for different types of failures
 * including network errors, data loading failures, and component crashes.
 */

import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { BentoCard } from '@/components/design-system/BentoGrid';
import { But<PERSON> } from '@/components/ui/button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import {
  AlertCircle,
  RefreshCw,
  Home,
  Wifi,
  Database,
  AlertTriangle,
  Info,
  ArrowLeft,
  Settings
} from 'lucide-react';

export type FallbackType = 
  | 'network-error'
  | 'data-loading-error'
  | 'component-crash'
  | 'permission-denied'
  | 'not-found'
  | 'maintenance'
  | 'generic';

interface UnifiedFallbackProps {
  type: FallbackType;
  title?: string;
  message?: string;
  error?: Error;
  onRetry?: () => void | Promise<void>;
  onGoHome?: () => void;
  onGoBack?: () => void;
  showRetry?: boolean;
  showHome?: boolean;
  showBack?: boolean;
  retryLabel?: string;
  className?: string;
}

const fallbackConfig = {
  'network-error': {
    icon: Wifi,
    title: 'Connection Problem',
    message: 'Unable to connect to our servers. Please check your internet connection and try again.',
    color: 'text-warning',
    bgColor: 'bg-warning/10',
    borderColor: 'border-warning/20'
  },
  'data-loading-error': {
    icon: Database,
    title: 'Data Loading Failed',
    message: 'We encountered an issue loading your data. This might be temporary.',
    color: 'text-primary',
    bgColor: 'bg-primary/10',
    borderColor: 'border-primary/20'
  },
  'component-crash': {
    icon: AlertTriangle,
    title: 'Component Error',
    message: 'This section encountered an unexpected error. Our team has been notified.',
    color: 'text-destructive',
    bgColor: 'bg-destructive/10',
    borderColor: 'border-destructive/20'
  },
  'permission-denied': {
    icon: Settings,
    title: 'Access Denied',
    message: 'You don\'t have permission to access this content. Please contact support if you believe this is an error.',
    color: 'text-purple-500',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200'
  },
  'not-found': {
    icon: Info,
    title: 'Content Not Found',
    message: 'The content you\'re looking for doesn\'t exist or has been moved.',
    color: 'text-gray-500',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200'
  },
  'maintenance': {
    icon: Settings,
    title: 'Under Maintenance',
    message: 'This feature is temporarily unavailable while we make improvements. Please try again later.',
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200'
  },
  'generic': {
    icon: AlertCircle,
    title: 'Something Went Wrong',
    message: 'An unexpected error occurred. Please try again or contact support if the problem persists.',
    color: 'text-gray-500',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200'
  }
};

export const UnifiedFallback: React.FC<UnifiedFallbackProps> = ({
  type,
  title,
  message,
  error,
  onRetry,
  onGoHome,
  onGoBack,
  showRetry = true,
  showHome = true,
  showBack = false,
  retryLabel = 'Try Again',
  className = ''
}) => {
  const [isRetrying, setIsRetrying] = useState(false);
  const config = fallbackConfig[type];
  const IconComponent = config.icon;

  const handleRetry = useCallback(async () => {
    if (!onRetry || isRetrying) return;

    setIsRetrying(true);
    try {
      await onRetry();
    } catch (retryError) {
      console.error('Retry failed:', retryError);
    } finally {
      setIsRetrying(false);
    }
  }, [onRetry, isRetrying]);

  const displayTitle = title || config.title;
  const displayMessage = message || config.message;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`flex items-center justify-center p-4 ${className}`}
    >
      <BentoCard
        title=""
        variant="glassmorphism"
        className={`max-w-md w-full text-center ${config.bgColor} ${config.borderColor}`}
      >
        <div className="p-6">
          <div className={`flex items-center justify-center mb-4 ${config.color}`}>
            <IconComponent className="h-12 w-12" />
          </div>

          <h2 className="text-xl font-semibold mb-3 text-foreground">
            {displayTitle}
          </h2>

          <p className="text-muted-foreground mb-6 leading-relaxed">
            {displayMessage}
          </p>

          {/* Error details for debugging (only in development) */}
          {error && process.env.NODE_ENV === 'development' && (
            <details className="mb-6 text-left">
              <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
                Technical Details
              </summary>
              <div className="mt-2 p-3 bg-muted rounded-md">
                <p className="text-xs font-mono text-muted-foreground break-all">
                  {error.message}
                </p>
                {error.stack && (
                  <pre className="text-xs text-muted-foreground mt-2 overflow-auto max-h-32">
                    {error.stack}
                  </pre>
                )}
              </div>
            </details>
          )}

          {/* Action buttons */}
          <div className="flex flex-col space-y-3">
            {showRetry && onRetry && (
              <Button
                onClick={handleRetry}
                disabled={isRetrying}
                className="w-full"
                size="lg"
              >
                {isRetrying ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Retrying...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    {retryLabel}
                  </>
                )}
              </Button>
            )}

            <div className="flex space-x-3">
              {showBack && onGoBack && (
                <Button
                  onClick={onGoBack}
                  variant="outline"
                  className="flex-1"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Go Back
                </Button>
              )}

              {showHome && onGoHome && (
                <Button
                  onClick={onGoHome}
                  variant="outline"
                  className={showBack ? "flex-1" : "w-full"}
                >
                  <Home className="mr-2 h-4 w-4" />
                  Home
                </Button>
              )}
            </div>
          </div>
        </div>
      </BentoCard>
    </motion.div>
  );
};

// Convenience components for common fallback scenarios
export const NetworkErrorFallback: React.FC<Omit<UnifiedFallbackProps, 'type'>> = (props) => (
  <UnifiedFallback type="network-error" {...props} />
);

export const DataLoadingErrorFallback: React.FC<Omit<UnifiedFallbackProps, 'type'>> = (props) => (
  <UnifiedFallback type="data-loading-error" {...props} />
);

export const ComponentCrashFallback: React.FC<Omit<UnifiedFallbackProps, 'type'>> = (props) => (
  <UnifiedFallback type="component-crash" {...props} />
);

export const PermissionDeniedFallback: React.FC<Omit<UnifiedFallbackProps, 'type'>> = (props) => (
  <UnifiedFallback type="permission-denied" {...props} />
);

export const NotFoundFallback: React.FC<Omit<UnifiedFallbackProps, 'type'>> = (props) => (
  <UnifiedFallback type="not-found" {...props} />
);

export const MaintenanceFallback: React.FC<Omit<UnifiedFallbackProps, 'type'>> = (props) => (
  <UnifiedFallback type="maintenance" {...props} />
);

export default UnifiedFallback;

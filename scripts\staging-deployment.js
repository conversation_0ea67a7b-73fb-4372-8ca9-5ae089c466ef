#!/usr/bin/env node

/**
 * Festival Family Staging Deployment Script
 * 
 * Automated deployment script for staging environment with comprehensive
 * validation of the standardized codebase and monitoring setup.
 * 
 * Features:
 * - Pre-deployment validation
 * - Standardized component verification
 * - Performance monitoring setup
 * - Rollback capabilities
 * 
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const STAGING_CONFIG = {
  environment: 'staging',
  port: 5174,
  buildDir: 'dist-staging',
  monitoringEnabled: true,
  performanceTracking: true,
  componentValidation: true
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n🔄 [${step}] ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// Pre-deployment validation
async function validateStandardizedCodebase() {
  logStep('VALIDATION', 'Validating standardized codebase...');
  
  const validationChecks = [
    {
      name: 'TypeScript Compilation',
      command: 'npm run type-check',
      critical: true
    },
    {
      name: 'Unified Design System Components',
      check: () => {
        const designSystemPath = 'src/components/design-system';
        const requiredComponents = [
          'UnifiedInteractionButton.tsx',
          'UnifiedModal.tsx',
          'UnifiedComponents.tsx',
          'index.ts'
        ];
        
        return requiredComponents.every(component => 
          fs.existsSync(path.join(designSystemPath, component))
        );
      },
      critical: true
    },
    {
      name: 'Legacy Component Removal',
      check: () => {
        const legacyComponents = [
          'src/components/activities/JoinLeaveButton.tsx',
          'src/components/activities/RSVPButton.tsx',
          'src/components/activities/FavoriteButton.tsx'
        ];
        
        return legacyComponents.every(component => !fs.existsSync(component));
      },
      critical: true
    },
    {
      name: 'Simplified Services',
      check: () => {
        const simplifiedServices = [
          'src/lib/data/unified-data-service.ts',
          'src/lib/services/enhancedColorMappingService.ts'
        ];
        
        return simplifiedServices.every(service => fs.existsSync(service));
      },
      critical: true
    }
  ];

  let allPassed = true;

  for (const check of validationChecks) {
    try {
      if (check.command) {
        execSync(check.command, { stdio: 'pipe' });
        logSuccess(`${check.name}: PASSED`);
      } else if (check.check) {
        const result = check.check();
        if (result) {
          logSuccess(`${check.name}: PASSED`);
        } else {
          logError(`${check.name}: FAILED`);
          if (check.critical) allPassed = false;
        }
      }
    } catch (error) {
      logError(`${check.name}: FAILED - ${error.message}`);
      if (check.critical) allPassed = false;
    }
  }

  if (!allPassed) {
    throw new Error('Critical validation checks failed. Deployment aborted.');
  }

  logSuccess('All validation checks passed!');
}

// Build for staging
async function buildForStaging() {
  logStep('BUILD', 'Building for staging environment...');
  
  try {
    // Set staging environment variables
    process.env.NODE_ENV = 'production';
    process.env.VITE_ENVIRONMENT = 'staging';
    process.env.VITE_PERFORMANCE_MONITORING = 'true';
    
    // Clean previous build
    if (fs.existsSync(STAGING_CONFIG.buildDir)) {
      execSync(`rm -rf ${STAGING_CONFIG.buildDir}`, { stdio: 'inherit' });
    }
    
    // Build with staging configuration
    execSync(`npm run build -- --outDir ${STAGING_CONFIG.buildDir}`, { stdio: 'inherit' });
    
    logSuccess('Staging build completed successfully!');
    
    // Analyze bundle
    await analyzeBundlePerformance();
    
  } catch (error) {
    logError(`Build failed: ${error.message}`);
    throw error;
  }
}

// Analyze bundle performance
async function analyzeBundlePerformance() {
  logStep('ANALYSIS', 'Analyzing bundle performance...');
  
  try {
    const buildPath = path.join(process.cwd(), STAGING_CONFIG.buildDir);
    const assetsPath = path.join(buildPath, 'assets');
    
    if (!fs.existsSync(assetsPath)) {
      logWarning('Assets directory not found, skipping bundle analysis');
      return;
    }
    
    const files = fs.readdirSync(assetsPath);
    const jsFiles = files.filter(file => file.endsWith('.js'));
    const cssFiles = files.filter(file => file.endsWith('.css'));
    
    let totalJSSize = 0;
    let totalCSSSize = 0;
    
    jsFiles.forEach(file => {
      const filePath = path.join(assetsPath, file);
      const stats = fs.statSync(filePath);
      totalJSSize += stats.size;
    });
    
    cssFiles.forEach(file => {
      const filePath = path.join(assetsPath, file);
      const stats = fs.statSync(filePath);
      totalCSSSize += stats.size;
    });
    
    log('\n📊 Bundle Analysis:', 'magenta');
    log(`   JavaScript: ${(totalJSSize / 1024 / 1024).toFixed(2)} MB (${jsFiles.length} files)`);
    log(`   CSS: ${(totalCSSSize / 1024).toFixed(2)} KB (${cssFiles.length} files)`);
    log(`   Total Assets: ${files.length} files`);
    
    // Check if bundle sizes are reasonable
    const maxJSSize = 5 * 1024 * 1024; // 5MB
    const maxCSSSize = 500 * 1024; // 500KB
    
    if (totalJSSize > maxJSSize) {
      logWarning(`JavaScript bundle size (${(totalJSSize / 1024 / 1024).toFixed(2)} MB) exceeds recommended limit (5 MB)`);
    }
    
    if (totalCSSSize > maxCSSSize) {
      logWarning(`CSS bundle size (${(totalCSSSize / 1024).toFixed(2)} KB) exceeds recommended limit (500 KB)`);
    }
    
    logSuccess('Bundle analysis completed');
    
  } catch (error) {
    logWarning(`Bundle analysis failed: ${error.message}`);
  }
}

// Setup monitoring configuration
async function setupMonitoring() {
  logStep('MONITORING', 'Setting up performance monitoring...');
  
  const monitoringConfig = {
    environment: 'staging',
    enablePerformanceTracking: true,
    enableErrorTracking: true,
    enableComponentMetrics: true,
    standardizedComponents: {
      UnifiedInteractionButton: true,
      UnifiedModal: true,
      ParticipantCount: true,
      EnhancedUnifiedBadge: true
    },
    simplifiedServices: {
      RealtimeService: true,
      unifiedDataService: true,
      enhancedColorMappingService: true
    },
    metrics: {
      bundleSize: true,
      loadTime: true,
      interactionLatency: true,
      componentRenderTime: true
    }
  };
  
  // Write monitoring configuration
  const configPath = path.join(STAGING_CONFIG.buildDir, 'monitoring-config.json');
  fs.writeFileSync(configPath, JSON.stringify(monitoringConfig, null, 2));
  
  logSuccess('Monitoring configuration created');
}

// Deploy to staging
async function deployToStaging() {
  logStep('DEPLOY', 'Deploying to staging environment...');
  
  try {
    // Start staging server
    log('Starting staging server...', 'blue');
    
    const serverProcess = spawn('npx', ['serve', '-s', STAGING_CONFIG.buildDir, '-l', STAGING_CONFIG.port], {
      stdio: 'pipe',
      detached: true
    });
    
    // Wait for server to start
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    logSuccess(`Staging server started on port ${STAGING_CONFIG.port}`);
    logSuccess(`Access staging at: http://localhost:${STAGING_CONFIG.port}`);
    
    // Save process ID for later cleanup
    fs.writeFileSync('.staging-pid', serverProcess.pid.toString());
    
  } catch (error) {
    logError(`Deployment failed: ${error.message}`);
    throw error;
  }
}

// Main deployment function
async function main() {
  log('\n🚀 Festival Family Staging Deployment', 'bright');
  log('=====================================', 'bright');
  
  try {
    await validateStandardizedCodebase();
    await buildForStaging();
    await setupMonitoring();
    await deployToStaging();
    
    log('\n🎉 Staging deployment completed successfully!', 'green');
    log('\n📋 Next Steps:', 'cyan');
    log('   1. Run comprehensive E2E tests');
    log('   2. Validate standardized components');
    log('   3. Monitor performance metrics');
    log('   4. Prepare for production deployment');
    
  } catch (error) {
    logError(`\nDeployment failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle cleanup on exit
process.on('SIGINT', () => {
  log('\n🛑 Deployment interrupted', 'yellow');
  process.exit(0);
});

// Run deployment
if (require.main === module) {
  main();
}

module.exports = { main, validateStandardizedCodebase, buildForStaging, setupMonitoring };

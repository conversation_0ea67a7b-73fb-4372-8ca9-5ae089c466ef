#!/usr/bin/env node

/**
 * Production Readiness Testing Framework
 * 
 * This script orchestrates comprehensive testing to validate production deployment readiness.
 * It runs all test types, collects evidence, and provides a definitive assessment.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

// Configuration
const EVIDENCE_DIR = 'production-readiness-evidence';
const TIMESTAMP = new Date().toISOString().replace(/[:.]/g, '-');
const REPORT_DIR = path.join(EVIDENCE_DIR, `assessment-${TIMESTAMP}`);

// Test execution results
const testResults = {
  unitTests: { passed: false, details: null },
  e2eTests: { passed: false, details: null },
  typeCheck: { passed: false, details: null },
  lint: { passed: false, details: null },
  build: { passed: false, details: null },
  performance: { passed: false, details: null }
};

// Utility functions
function createReportDirectory() {
  if (!fs.existsSync(EVIDENCE_DIR)) {
    fs.mkdirSync(EVIDENCE_DIR, { recursive: true });
  }
  if (!fs.existsSync(REPORT_DIR)) {
    fs.mkdirSync(REPORT_DIR, { recursive: true });
  }
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  console.log(`🚀 ${title}`);
  console.log('='.repeat(60));
}

function logStep(step) {
  console.log(`\n📋 ${step}`);
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
}

function logError(message) {
  console.log(`❌ ${message}`);
}

function logWarning(message) {
  console.log(`⚠️  ${message}`);
}

function runCommand(command, description) {
  logStep(description);
  try {
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: 'pipe',
      env: { ...process.env, CI: 'true', HEADLESS: 'true' }
    });
    logSuccess(`${description} completed successfully`);
    return { success: true, output };
  } catch (error) {
    logError(`${description} failed: ${error.message}`);
    return { success: false, output: error.stdout || error.message };
  }
}

function saveEvidence(filename, content) {
  const filepath = path.join(REPORT_DIR, filename);
  fs.writeFileSync(filepath, content);
  console.log(`📄 Evidence saved: ${filepath}`);
}

async function runUnitTests() {
  logSection('UNIT TESTS VALIDATION');
  
  const result = runCommand(
    'npm run test:unit:coverage',
    'Running Jest unit tests with coverage'
  );
  
  testResults.unitTests = {
    passed: result.success,
    details: result.output
  };
  
  saveEvidence('unit-tests-output.txt', result.output);
  
  // Copy coverage report if it exists
  if (fs.existsSync('coverage')) {
    try {
      execSync(`cp -r coverage ${REPORT_DIR}/coverage-report`);
      logSuccess('Coverage report copied to evidence');
    } catch (error) {
      logWarning('Could not copy coverage report');
    }
  }
  
  return result.success;
}

async function runE2ETests() {
  logSection('E2E TESTS VALIDATION');
  
  const result = runCommand(
    'npm run test:e2e',
    'Running Playwright E2E tests in headless mode'
  );
  
  testResults.e2eTests = {
    passed: result.success,
    details: result.output
  };
  
  saveEvidence('e2e-tests-output.txt', result.output);
  
  // Copy Playwright test results if they exist
  if (fs.existsSync('test-results')) {
    try {
      execSync(`cp -r test-results ${REPORT_DIR}/playwright-results`);
      logSuccess('Playwright test results copied to evidence');
    } catch (error) {
      logWarning('Could not copy Playwright test results');
    }
  }
  
  return result.success;
}

async function runTypeCheck() {
  logSection('TYPESCRIPT VALIDATION');
  
  const result = runCommand(
    'npm run type-check',
    'Running TypeScript type checking'
  );
  
  testResults.typeCheck = {
    passed: result.success,
    details: result.output
  };
  
  saveEvidence('typescript-check-output.txt', result.output);
  
  return result.success;
}

async function runLinting() {
  logSection('CODE QUALITY VALIDATION');
  
  const result = runCommand(
    'npm run lint',
    'Running ESLint code quality checks'
  );
  
  testResults.lint = {
    passed: result.success,
    details: result.output
  };
  
  saveEvidence('lint-output.txt', result.output);
  
  return result.success;
}

async function runBuild() {
  logSection('BUILD VALIDATION');
  
  const result = runCommand(
    'npm run build',
    'Running production build'
  );
  
  testResults.build = {
    passed: result.success,
    details: result.output
  };
  
  saveEvidence('build-output.txt', result.output);
  
  // Check build size if dist exists
  if (fs.existsSync('dist')) {
    try {
      const sizeOutput = execSync('du -sh dist', { encoding: 'utf8' });
      saveEvidence('build-size.txt', sizeOutput);
      logSuccess(`Build size: ${sizeOutput.trim()}`);
    } catch (error) {
      logWarning('Could not measure build size');
    }
  }
  
  return result.success;
}

async function runPerformanceTests() {
  logSection('PERFORMANCE VALIDATION');
  
  // Check if performance tests exist
  const hasPerformanceTests = fs.existsSync('tests') && 
    fs.readdirSync('tests').some(file => file.includes('performance') || file.includes('evidence-based'));
  
  if (!hasPerformanceTests) {
    logWarning('No performance tests found, skipping performance validation');
    testResults.performance = { passed: true, details: 'No performance tests configured' };
    return true;
  }
  
  const result = runCommand(
    'npm run test:performance',
    'Running performance tests'
  );
  
  testResults.performance = {
    passed: result.success,
    details: result.output
  };
  
  saveEvidence('performance-tests-output.txt', result.output);
  
  return result.success;
}

function generateProductionReadinessReport() {
  logSection('PRODUCTION READINESS ASSESSMENT');
  
  const passedTests = Object.values(testResults).filter(test => test.passed).length;
  const totalTests = Object.keys(testResults).length;
  const successRate = Math.round((passedTests / totalTests) * 100);
  
  const report = {
    timestamp: new Date().toISOString(),
    assessment: {
      overallStatus: successRate === 100 ? 'READY' : successRate >= 80 ? 'MOSTLY_READY' : 'NOT_READY',
      successRate: `${successRate}%`,
      passedTests,
      totalTests
    },
    testResults,
    recommendations: generateRecommendations()
  };
  
  const reportJson = JSON.stringify(report, null, 2);
  const reportText = generateTextReport(report);
  
  saveEvidence('production-readiness-report.json', reportJson);
  saveEvidence('production-readiness-report.txt', reportText);
  
  // Display summary
  console.log('\n' + '🎯 PRODUCTION READINESS SUMMARY'.padStart(40));
  console.log('='.repeat(60));
  console.log(`Overall Status: ${report.assessment.overallStatus}`);
  console.log(`Success Rate: ${report.assessment.successRate}`);
  console.log(`Tests Passed: ${passedTests}/${totalTests}`);
  
  if (report.assessment.overallStatus === 'READY') {
    logSuccess('🚀 APPLICATION IS READY FOR PRODUCTION DEPLOYMENT!');
  } else if (report.assessment.overallStatus === 'MOSTLY_READY') {
    logWarning('⚠️  APPLICATION IS MOSTLY READY - REVIEW RECOMMENDATIONS');
  } else {
    logError('❌ APPLICATION IS NOT READY FOR PRODUCTION - CRITICAL ISSUES FOUND');
  }
  
  console.log(`\n📁 Full evidence report available at: ${REPORT_DIR}`);
  
  return report;
}

function generateRecommendations() {
  const recommendations = [];
  
  if (!testResults.unitTests.passed) {
    recommendations.push('Fix failing unit tests before deployment');
  }
  
  if (!testResults.e2eTests.passed) {
    recommendations.push('Resolve E2E test failures - critical user flows may be broken');
  }
  
  if (!testResults.typeCheck.passed) {
    recommendations.push('Fix TypeScript errors to ensure type safety');
  }
  
  if (!testResults.lint.passed) {
    recommendations.push('Address linting issues to maintain code quality');
  }
  
  if (!testResults.build.passed) {
    recommendations.push('Fix build errors - deployment will fail');
  }
  
  if (!testResults.performance.passed) {
    recommendations.push('Address performance issues before production deployment');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('All tests passed! Application is ready for production deployment.');
    recommendations.push('Consider setting up monitoring and error tracking for production.');
    recommendations.push('Ensure environment variables are configured for production.');
  }
  
  return recommendations;
}

function generateTextReport(report) {
  return `
FESTIVAL FAMILY - PRODUCTION READINESS ASSESSMENT
Generated: ${report.timestamp}

OVERALL STATUS: ${report.assessment.overallStatus}
SUCCESS RATE: ${report.assessment.successRate}
TESTS PASSED: ${report.assessment.passedTests}/${report.assessment.totalTests}

DETAILED RESULTS:
${Object.entries(testResults).map(([test, result]) => 
  `${test.toUpperCase()}: ${result.passed ? '✅ PASSED' : '❌ FAILED'}`
).join('\n')}

RECOMMENDATIONS:
${report.recommendations.map(rec => `• ${rec}`).join('\n')}

EVIDENCE LOCATION: ${REPORT_DIR}
`;
}

// Main execution
async function main() {
  console.log('🚀 FESTIVAL FAMILY - PRODUCTION READINESS TESTING');
  console.log('='.repeat(60));
  console.log(`Starting comprehensive testing at ${new Date().toISOString()}`);
  
  createReportDirectory();
  
  try {
    // Run all test phases
    await runTypeCheck();
    await runLinting();
    await runUnitTests();
    await runE2ETests();
    await runBuild();
    await runPerformanceTests();
    
    // Generate final report
    const report = generateProductionReadinessReport();
    
    // Exit with appropriate code
    process.exit(report.assessment.overallStatus === 'READY' ? 0 : 1);
    
  } catch (error) {
    logError(`Production readiness testing failed: ${error.message}`);
    saveEvidence('error-log.txt', error.stack);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main as runProductionReadinessTest };

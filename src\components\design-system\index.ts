/**
 * Festival Family Design System
 * Single source of truth for all UI components and layouts
 *
 * COMPONENT HIERARCHY (Post-Consolidation):
 *
 * BUTTONS:
 * - shadcn/ui But<PERSON> (foundation) → UnifiedButton (design system) → AccessibleButton (specialized)
 *
 * CARDS:
 * - shadcn/ui Card (foundation) → BentoCard (primary) → UnifiedCard (simple) → AccessibleCard (specialized)
 *
 * BADGES:
 * - shadcn/ui Badge (foundation) → EnhancedUnifiedBadge (primary, database-driven) → ActivityTypeBadge (specialized)
 *
 * USAGE GUIDELINES:
 * - Use BentoCard for all main content (events, activities, resources)
 * - Use EnhancedUnifiedBadge for all badges (database-driven colors)
 * - Use UnifiedButton for design system consistency
 * - Use shadcn/ui components only as building blocks
 */

// ===== UNIFIED COMPONENTS =====
// Single source of truth components - use these instead of duplicates
export {
  UnifiedButton,
  UnifiedCard,
  UnifiedIconButton,
  UnifiedContainer,
  UnifiedFilterBar,
  UnifiedAnnouncement
} from './UnifiedComponents';

// ===== ENHANCED BADGE SYSTEM =====
// Database-driven badge components with admin-configurable colors
export {
  EnhancedUnifiedBadge,
  ActivityTypeBadge,
  useActivityTypeBadge,
  UnifiedBadge // Backward compatibility alias for EnhancedUnifiedBadge
} from './EnhancedUnifiedBadge';

// ===== LAYOUT SYSTEM =====
// Eliminates background layering issues and provides consistent structure
export {
  PageLayout,
  ContentContainer,
  SectionLayout,
  GridLayout,
  FlexLayout,
  HeaderLayout,
  CardGrid,
  MainContent,
  PageWrapper
} from './LayoutSystem';

// ===== MODERN LAYOUT COMPONENTS (2025) =====
// Bento grid and modern layout patterns
export {
  BentoGrid,
  BentoItem,
  BentoCard,
  BentoStat
} from './BentoGrid';

// ===== LEGACY MODAL SYSTEM =====
// TODO: Migrate to UnifiedComponents
export { ResponsiveModal } from './ResponsiveModal';
export {
  ModalContent,
  ModalSection,
  ModalMetadata,
  ModalDescription,
  ModalTags,
  ModalActions,
  ModalImage,
  ModalStats,
  ModalFooter,
} from './ModalComponents';

// ===== TYPES =====
export type ResponsiveModalSize = 'sm' | 'md' | 'lg' | 'xl' | 'full';
export type ComponentVariant = 'primary' | 'secondary' | 'outline' | 'ghost';
export type ComponentSize = 'sm' | 'md' | 'lg';
export type PriorityLevel = 'high' | 'medium' | 'low';

// ===== CONSTANTS =====
export const DESIGN_SYSTEM_BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1200,
} as const;

// Spacing utilities (will be added in next priority)
// Button system (will be added in next priority)
// Navigation system (will be added in next priority)

/**
 * Mobile UX Utilities
 * 
 * Utilities for enhancing mobile user experience including touch handling,
 * viewport detection, and mobile-specific optimizations.
 */

// Mobile viewport detection
export const isMobileViewport = (): boolean => {
  return window.innerWidth < 768; // md breakpoint
};

export const isTabletViewport = (): boolean => {
  return window.innerWidth >= 768 && window.innerWidth < 1024; // md to lg
};

export const isTouchDevice = (): boolean => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

// Touch target validation (minimum 44px as per Apple guidelines)
export const validateTouchTarget = (element: HTMLElement): boolean => {
  const rect = element.getBoundingClientRect();
  return rect.width >= 44 && rect.height >= 44;
};

// Haptic feedback simulation
export const simulateHapticFeedback = (type: 'light' | 'medium' | 'heavy' = 'light'): void => {
  if (navigator.vibrate) {
    const patterns = {
      light: 50,
      medium: 100,
      heavy: 200
    };
    navigator.vibrate(patterns[type]);
  }
};

// Smooth scroll with mobile optimization
export const smoothScrollTo = (target: number | HTMLElement, offset: number = 0): void => {
  const targetPosition = typeof target === 'number' 
    ? target 
    : target.getBoundingClientRect().top + window.pageYOffset;
  
  window.scrollTo({
    top: targetPosition - offset,
    behavior: 'smooth'
  });
};

// Mobile-safe event handlers
export const createTouchHandler = (callback: () => void) => {
  return {
    onTouchStart: (e: React.TouchEvent) => {
      e.stopPropagation();
    },
    onTouchEnd: (e: React.TouchEvent) => {
      e.preventDefault();
      e.stopPropagation();
      callback();
    },
    onClick: callback,
    style: { touchAction: 'manipulation' as const }
  };
};

// Viewport height calculation (accounting for mobile browser UI)
export const getViewportHeight = (): number => {
  return window.visualViewport?.height || window.innerHeight;
};

// Safe area insets detection
export const getSafeAreaInsets = () => {
  const style = getComputedStyle(document.documentElement);
  return {
    top: parseInt(style.getPropertyValue('env(safe-area-inset-top)') || '0'),
    right: parseInt(style.getPropertyValue('env(safe-area-inset-right)') || '0'),
    bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
    left: parseInt(style.getPropertyValue('env(safe-area-inset-left)') || '0')
  };
};

// Mobile performance optimization
export const optimizeForMobile = () => {
  // Disable hover effects on touch devices
  if (isTouchDevice()) {
    document.body.classList.add('touch-device');
  }
  
  // Add mobile-specific meta tags if not present
  const viewport = document.querySelector('meta[name="viewport"]');
  if (!viewport) {
    const meta = document.createElement('meta');
    meta.name = 'viewport';
    meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
    document.head.appendChild(meta);
  }
};

// Mobile breakpoint utilities
export const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
} as const;

export const getCurrentBreakpoint = (): keyof typeof breakpoints => {
  const width = window.innerWidth;
  if (width >= breakpoints['2xl']) return '2xl';
  if (width >= breakpoints.xl) return 'xl';
  if (width >= breakpoints.lg) return 'lg';
  if (width >= breakpoints.md) return 'md';
  if (width >= breakpoints.sm) return 'sm';
  return 'xs';
};

// Mobile typography scaling
export const getMobileTextSize = (baseSize: string): string => {
  const currentBreakpoint = getCurrentBreakpoint();
  const scalingFactors = {
    xs: 0.875, // 14px base becomes ~12px
    sm: 0.9375, // 14px base becomes ~13px
    md: 1, // No scaling
    lg: 1,
    xl: 1,
    '2xl': 1
  };
  
  const factor = scalingFactors[currentBreakpoint];
  const sizeValue = parseFloat(baseSize);
  const unit = baseSize.replace(sizeValue.toString(), '');
  
  return `${sizeValue * factor}${unit}`;
};

// Initialize mobile optimizations
export const initializeMobileUX = (): void => {
  optimizeForMobile();
  
  // Add resize listener for dynamic breakpoint updates
  let resizeTimeout: NodeJS.Timeout;
  window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      document.documentElement.setAttribute('data-breakpoint', getCurrentBreakpoint());
    }, 100);
  });
  
  // Set initial breakpoint
  document.documentElement.setAttribute('data-breakpoint', getCurrentBreakpoint());
};

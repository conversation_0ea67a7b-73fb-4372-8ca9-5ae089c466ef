import React, { useEffect, useRef, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { createPortal } from 'react-dom';
import { X } from 'lucide-react';

// Responsive breakpoints following design system patterns
const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1200,
} as const;

type ResponsiveModalSize = 'sm' | 'md' | 'lg' | 'xl' | 'full';

interface ResponsiveModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  size?: ResponsiveModalSize;
  triggerElement?: HTMLElement | null;
  className?: string;
  showCloseButton?: boolean;
  closeOnBackdropClick?: boolean;
  closeOnEscape?: boolean;
  initialFocus?: string; // CSS selector for initial focus
}

// Simplified modal size mapping for responsive behavior
const getModalMaxWidth = (size: ResponsiveModalSize) => {
  switch (size) {
    case 'sm': return 'max-w-md';   // ~448px
    case 'md': return 'max-w-lg';   // ~512px
    case 'lg': return 'max-w-2xl';  // ~672px
    case 'xl': return 'max-w-4xl';  // ~896px
    case 'full': return 'max-w-6xl'; // ~1152px
    default: return 'max-w-2xl';
  }
};

export const ResponsiveModal: React.FC<ResponsiveModalProps> = ({
  isOpen,
  onClose,
  children,
  title,
  size = 'md',
  triggerElement,
  className = '',
  showCloseButton = true,
  closeOnBackdropClick = true,
  closeOnEscape = true,
  initialFocus,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const [isMobile, setIsMobile] = useState(false);

  // Update mobile state on mount and resize
  const updateMobileState = useCallback(() => {
    const viewportWidth = window.innerWidth;
    const newIsMobile = viewportWidth < BREAKPOINTS.mobile;
    setIsMobile(newIsMobile);
  }, []);

  // Update mobile state on mount and resize
  useEffect(() => {
    if (isOpen) {
      updateMobileState();

      const handleResize = () => updateMobileState();
      window.addEventListener('resize', handleResize);

      return () => window.removeEventListener('resize', handleResize);
    }
  }, [isOpen, updateMobileState]);

  // Handle escape key
  useEffect(() => {
    if (!isOpen || !closeOnEscape) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, onClose]);

  // Focus management
  useEffect(() => {
    if (isOpen && modalRef.current) {
      const focusElement = initialFocus 
        ? modalRef.current.querySelector(initialFocus) as HTMLElement
        : modalRef.current.querySelector('[data-modal-primary-focus]') as HTMLElement
        || modalRef.current.querySelector('button, input, textarea, select') as HTMLElement
        || modalRef.current;

      focusElement?.focus();
    }
  }, [isOpen, initialFocus]);

  // Handle backdrop click
  const handleBackdropClick = useCallback((e: React.MouseEvent) => {
    if (closeOnBackdropClick && e.target === e.currentTarget) {
      onClose();
    }
  }, [closeOnBackdropClick, onClose]);

  if (!isOpen) return null;

  const modalContent = (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 overflow-y-auto"
        onClick={handleBackdropClick}
      >
        {/* Flexbox container for centering - NO conflicting absolute positioning */}
        <div className={`
          min-h-screen
          ${isMobile
            ? 'flex flex-col'
            : 'flex items-center justify-center p-4'
          }
        `}>
          <motion.div
            ref={modalRef}
            initial={isMobile
              ? { opacity: 0, y: '100%' }
              : { opacity: 0, scale: 0.95, y: 20 }
            }
            animate={isMobile
              ? { opacity: 1, y: 0 }
              : { opacity: 1, scale: 1, y: 0 }
            }
            exit={isMobile
              ? { opacity: 0, y: '100%' }
              : { opacity: 0, scale: 0.95, y: 20 }
            }
            className={`
              bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900
              rounded-2xl overflow-hidden shadow-2xl
              ${isMobile
                ? 'w-full h-full rounded-none flex-1'
                : `w-full ${getModalMaxWidth(size)} max-h-[90vh] mx-auto`
              }
              ${className}
            `}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal Header */}
            {(title || showCloseButton) && (
              <div className="flex items-center justify-between p-4 border-b border-white/10">
                {title && (
                  <h2 className="text-xl font-semibold text-white truncate pr-4">
                    {title}
                  </h2>
                )}
                {showCloseButton && (
                  <button
                    onClick={onClose}
                    className="p-2 rounded-full transition-all duration-200 text-white/80 hover:text-white flex-shrink-0 border-0 bg-white/10 hover:bg-white/20 cursor-pointer shadow-lg hover:shadow-xl"
                    aria-label="Close modal"
                    type="button"
                  >
                    <X className="w-6 h-6" />
                  </button>
                )}
              </div>
            )}

            {/* Modal Content */}
            <div className="overflow-y-auto max-h-full">
              {children}
            </div>
          </motion.div>
        </div>
      </motion.div>
    </AnimatePresence>
  );

  // Render modal using React Portal
  return createPortal(modalContent, document.body);
};

export default ResponsiveModal;

-- Verification Script for Complete Schema Fix
-- Run this AFTER running complete-schema-fix.sql

-- ============================================================================
-- CHECK ALL REQUIRED TABLES EXIST
-- ============================================================================

SELECT 'TABLES CHECK' as check_type;

SELECT table_name, 
       CASE 
         WHEN table_name IN ('announcements', 'tips', 'faqs', 'content_management', 'user_preferences', 'emergency_contacts', 'safety_information') 
         THEN '✅ REQUIRED' 
         ELSE '📋 OTHER' 
       END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY 
  CASE WHEN table_name IN ('announcements', 'tips', 'faqs', 'content_management', 'user_preferences', 'emergency_contacts', 'safety_information') THEN 1 ELSE 2 END,
  table_name;

-- ============================================================================
-- CHECK ALL REQUIRED FUNCTIONS EXIST
-- ============================================================================

SELECT 'FUNCTIONS CHECK' as check_type;

SELECT routine_name,
       CASE 
         WHEN routine_name IN ('is_admin', 'is_super_admin', 'is_content_admin', 'can_manage_groups') 
         THEN '✅ ADMIN FUNCTION' 
         ELSE '📋 OTHER FUNCTION' 
       END as status
FROM information_schema.routines 
WHERE routine_schema = 'public' AND routine_type = 'FUNCTION'
ORDER BY 
  CASE WHEN routine_name IN ('is_admin', 'is_super_admin', 'is_content_admin', 'can_manage_groups') THEN 1 ELSE 2 END,
  routine_name;

-- ============================================================================
-- CHECK FAQ TABLE COLUMNS
-- ============================================================================

SELECT 'FAQ COLUMNS CHECK' as check_type;

SELECT column_name,
       CASE 
         WHEN column_name IN ('category', 'order_index') 
         THEN '✅ REQUIRED COLUMN' 
         ELSE '📋 OTHER COLUMN' 
       END as status
FROM information_schema.columns 
WHERE table_name = 'faqs'
ORDER BY 
  CASE WHEN column_name IN ('category', 'order_index') THEN 1 ELSE 2 END,
  column_name;

-- ============================================================================
-- TEST ADMIN FUNCTIONS
-- ============================================================================

SELECT 'ADMIN FUNCTIONS TEST' as check_type;

SELECT 
  'is_admin' as function_name,
  CASE 
    WHEN is_admin() IS NOT NULL THEN '✅ WORKING'
    ELSE '❌ ERROR'
  END as status;

SELECT 
  'is_super_admin' as function_name,
  CASE 
    WHEN is_super_admin() IS NOT NULL THEN '✅ WORKING'
    ELSE '❌ ERROR'
  END as status;

SELECT 
  'is_content_admin' as function_name,
  CASE 
    WHEN is_content_admin() IS NOT NULL THEN '✅ WORKING'
    ELSE '❌ ERROR'
  END as status;

SELECT 
  'can_manage_groups' as function_name,
  CASE 
    WHEN can_manage_groups() IS NOT NULL THEN '✅ WORKING'
    ELSE '❌ ERROR'
  END as status;

-- ============================================================================
-- SUMMARY REPORT
-- ============================================================================

SELECT 'SUMMARY REPORT' as check_type;

WITH table_count AS (
  SELECT COUNT(*) as total_tables
  FROM information_schema.tables 
  WHERE table_schema = 'public' 
  AND table_name IN ('announcements', 'tips', 'faqs', 'content_management', 'user_preferences', 'emergency_contacts', 'safety_information')
),
function_count AS (
  SELECT COUNT(*) as total_functions
  FROM information_schema.routines 
  WHERE routine_schema = 'public' AND routine_type = 'FUNCTION'
  AND routine_name IN ('is_admin', 'is_super_admin', 'is_content_admin', 'can_manage_groups')
),
faq_columns AS (
  SELECT COUNT(*) as required_columns
  FROM information_schema.columns 
  WHERE table_name = 'faqs'
  AND column_name IN ('category', 'order_index')
)
SELECT 
  t.total_tables as required_tables_exist,
  CASE WHEN t.total_tables = 7 THEN '✅ ALL TABLES' ELSE '❌ MISSING TABLES' END as table_status,
  f.total_functions as admin_functions_exist,
  CASE WHEN f.total_functions = 4 THEN '✅ ALL FUNCTIONS' ELSE '❌ MISSING FUNCTIONS' END as function_status,
  c.required_columns as faq_columns_exist,
  CASE WHEN c.required_columns = 2 THEN '✅ FAQ FIXED' ELSE '❌ FAQ INCOMPLETE' END as faq_status,
  CASE 
    WHEN t.total_tables = 7 AND f.total_functions = 4 AND c.required_columns = 2 
    THEN '🎉 DATABASE COMPLETE' 
    ELSE '⚠️ NEEDS MORE WORK' 
  END as overall_status
FROM table_count t, function_count f, faq_columns c;

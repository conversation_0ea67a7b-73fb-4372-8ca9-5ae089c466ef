/**
 * Festival Family - Centralized Enum Definitions
 * 
 * This file serves as the SINGLE SOURCE OF TRUTH for all enum definitions
 * in the Festival Family application. It consolidates and resolves conflicts
 * between database enums and TypeScript enums.
 * 
 * Database enums are defined in Supabase migrations and must match exactly.
 * 
 * @version 1.0.0
 */

// ============================================================================
// DATABASE ENUMS (Must match Supabase exactly)
// ============================================================================

/**
 * Activity types - matches database enum 'activity_type'
 * Database: CREATE TYPE activity_type AS ENUM (...)
 */
export enum ActivityType {
  WORKSHOP = 'workshop',
  MEETUP = 'meetup', 
  PERFORMANCE = 'performance',
  GAME = 'game',
  SOCIAL = 'social',
  FOOD = 'food',
  OTHER = 'other'
}

/**
 * User roles - matches database enum 'user_role'
 * Database: CREATE TYPE user_role AS ENUM (...)
 */
export enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  CONTENT_ADMIN = 'CONTENT_ADMIN', 
  MODERATOR = 'MODERATOR',
  USER = 'USER'
}

/**
 * Festival status - matches database enum 'festival_status'
 * Database: CREATE TYPE festival_status AS ENUM (...)
 */
export enum FestivalStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED'
}

/**
 * Event status - matches database enum 'event_status'
 * Database: CREATE TYPE event_status AS ENUM (...)
 */
export enum EventStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED', 
  ARCHIVED = 'ARCHIVED'
}

/**
 * Attendance status - matches database enum 'attendance_status'
 * Database: CREATE TYPE attendance_status AS ENUM (...)
 */
export enum AttendanceStatus {
  GOING = 'going',
  INTERESTED = 'interested',
  MAYBE = 'maybe',
  NOT_GOING = 'not_going'
}

/**
 * Chat platform - matches database enum 'chat_platform'
 * Database: CREATE TYPE chat_platform AS ENUM (...)
 */
export enum ChatPlatform {
  WHATSAPP = 'whatsapp',
  DISCORD = 'discord',
  TELEGRAM = 'telegram',
  OTHER = 'other'
}

/**
 * Tip category - matches database enum 'tip_category'
 * Database: CREATE TYPE tip_category AS ENUM (...)
 */
export enum TipCategory {
  SURVIVAL = 'SURVIVAL',
  SOCIAL = 'SOCIAL',
  COMFORT = 'COMFORT',
  BUDGET = 'BUDGET',
  EXPERIENCE = 'EXPERIENCE',
  OTHER = 'OTHER'
}

/**
 * Guide category - matches database enum 'guide_category'
 * Database: CREATE TYPE guide_category AS ENUM (...)
 */
export enum GuideCategory {
  SAFETY = 'SAFETY',
  PACKING = 'PACKING',
  CAMPING = 'CAMPING',
  FOOD = 'FOOD',
  TRANSPORT = 'TRANSPORT',
  OTHER = 'OTHER'
}

// ============================================================================
// APPLICATION-SPECIFIC ENUMS (Not in database)
// ============================================================================

/**
 * Activity tab categories for UI filtering
 * These are UI-specific and not stored in database
 */
export enum ActivityTab {
  MEETUP = 'meetup',
  DAILY = 'daily', 
  CHALLENGES = 'challenges',
  UPCOMING = 'upcoming'
}

/**
 * Announcement types for visual styling
 * These control UI appearance and are not database enums
 */
export enum AnnouncementType {
  INFO = 'info',
  WARNING = 'warning',
  SUCCESS = 'success',
  ERROR = 'error',
  URGENT = 'urgent'
}

/**
 * Announcement priority levels
 * These control display order and are not database enums
 */
export enum AnnouncementPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

/**
 * Festival computed status for UI display
 * These are calculated values, not stored in database
 */
export enum FestivalComputedStatus {
  UPCOMING = 'upcoming',
  ACTIVE = 'active', 
  COMPLETED = 'completed'
}

// ============================================================================
// TYPE UNIONS FOR COMPATIBILITY
// ============================================================================

/**
 * All possible activity types as union type
 */
export type ActivityTypeUnion = keyof typeof ActivityType | ActivityType;

/**
 * All possible user roles as union type
 */
export type UserRoleUnion = keyof typeof UserRole | UserRole;

/**
 * All possible festival statuses as union type
 */
export type FestivalStatusUnion = keyof typeof FestivalStatus | FestivalStatus;

/**
 * All possible event statuses as union type
 */
export type EventStatusUnion = keyof typeof EventStatus | EventStatus;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Check if a value is a valid activity type
 */
export function isValidActivityType(value: unknown): value is ActivityType {
  return typeof value === 'string' && Object.values(ActivityType).includes(value as ActivityType);
}

/**
 * Check if a value is a valid user role
 */
export function isValidUserRole(value: unknown): value is UserRole {
  return typeof value === 'string' && Object.values(UserRole).includes(value as UserRole);
}

/**
 * Check if a value is a valid festival status
 */
export function isValidFestivalStatus(value: unknown): value is FestivalStatus {
  return typeof value === 'string' && Object.values(FestivalStatus).includes(value as FestivalStatus);
}

/**
 * Check if a value is a valid event status
 */
export function isValidEventStatus(value: unknown): value is EventStatus {
  return typeof value === 'string' && Object.values(EventStatus).includes(value as EventStatus);
}

// ============================================================================
// NORMALIZATION FUNCTIONS
// ============================================================================

/**
 * Normalize activity type from any source to valid enum value
 */
export function normalizeActivityType(value: unknown): ActivityType {
  if (isValidActivityType(value)) return value;
  
  // Handle common variations
  if (typeof value === 'string') {
    const normalized = value.toLowerCase();
    switch (normalized) {
      case 'workshop': return ActivityType.WORKSHOP;
      case 'meetup': return ActivityType.MEETUP;
      case 'performance': return ActivityType.PERFORMANCE;
      case 'game': return ActivityType.GAME;
      case 'social': return ActivityType.SOCIAL;
      case 'food': return ActivityType.FOOD;
      default: return ActivityType.OTHER;
    }
  }
  
  return ActivityType.OTHER;
}

/**
 * Normalize user role from any source to valid enum value
 */
export function normalizeUserRole(value: unknown): UserRole {
  if (isValidUserRole(value)) return value;
  
  // Handle common variations
  if (typeof value === 'string') {
    const normalized = value.toUpperCase();
    switch (normalized) {
      case 'SUPER_ADMIN': return UserRole.SUPER_ADMIN;
      case 'CONTENT_ADMIN': return UserRole.CONTENT_ADMIN;
      case 'MODERATOR': return UserRole.MODERATOR;
      default: return UserRole.USER;
    }
  }
  
  return UserRole.USER;
}

/**
 * Normalize festival status from any source to valid enum value
 */
export function normalizeFestivalStatus(value: unknown): FestivalStatus {
  if (isValidFestivalStatus(value)) return value;
  
  // Handle common variations
  if (typeof value === 'string') {
    const normalized = value.toUpperCase();
    switch (normalized) {
      case 'DRAFT': return FestivalStatus.DRAFT;
      case 'PUBLISHED': return FestivalStatus.PUBLISHED;
      case 'ARCHIVED': return FestivalStatus.ARCHIVED;
      default: return FestivalStatus.DRAFT;
    }
  }
  
  return FestivalStatus.DRAFT;
}

// ============================================================================
// DISPLAY HELPERS
// ============================================================================

/**
 * Get human-readable label for activity type
 */
export function getActivityTypeLabel(type: ActivityType): string {
  switch (type) {
    case ActivityType.WORKSHOP: return 'Workshop';
    case ActivityType.MEETUP: return 'Meetup';
    case ActivityType.PERFORMANCE: return 'Performance';
    case ActivityType.GAME: return 'Game';
    case ActivityType.SOCIAL: return 'Social';
    case ActivityType.FOOD: return 'Food & Drink';
    case ActivityType.OTHER: return 'Other';
    default: return 'Unknown';
  }
}

/**
 * Get human-readable label for user role
 */
export function getUserRoleLabel(role: UserRole): string {
  switch (role) {
    case UserRole.SUPER_ADMIN: return 'Super Admin';
    case UserRole.CONTENT_ADMIN: return 'Content Admin';
    case UserRole.MODERATOR: return 'Moderator';
    case UserRole.USER: return 'User';
    default: return 'Unknown';
  }
}

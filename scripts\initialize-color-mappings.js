/**
 * Initialize Enhanced Color Mappings
 *
 * This script initializes the color_mappings table with default values
 * using the enhancedColorMappingService.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🎨 Initializing Enhanced Color Mappings');
console.log('=====================================');

// Enhanced Color Mapping Service (simplified for script)
const contentTypeConfigs = {
  // ACTIVITIES - Primary content type
  activities: {
    name: 'Activities',
    description: 'Festival Family organized activities and events',
    colorPrimary: '#8B5CF6', // Purple - creativity and community
    colorSecondary: '#A78BFA',
    colorAccent: '#C4B5FD',
    emoji: '🎪',
    subcategories: {
      meetup: {
        colorPrimary: '#3B82F6', // Blue - trust and connection
        colorSecondary: '#60A5FA',
        colorAccent: '#93C5FD',
        emoji: '👥',
        description: 'Community meetups and gatherings'
      },
      workshop: {
        colorPrimary: '#F59E0B', // Amber - learning and growth
        colorSecondary: '#FBBF24',
        colorAccent: '#FCD34D',
        emoji: '🛠️',
        description: 'Educational workshops and skill-building'
      },
      party: {
        colorPrimary: '#EC4899', // Pink - fun and celebration
        colorSecondary: '#F472B6',
        colorAccent: '#F9A8D4',
        emoji: '🎉',
        description: 'Parties and celebration events'
      }
    }
  },

  // TIPS - Helpful information
  tips: {
    name: 'Tips',
    description: 'Helpful tips and advice for festival-goers',
    colorPrimary: '#10B981', // Emerald - helpful and positive
    colorSecondary: '#34D399',
    colorAccent: '#6EE7B7',
    emoji: '💡'
  },

  // COMMUNITY - Social connections
  community: {
    name: 'Community',
    description: 'Community features and social connections',
    colorPrimary: '#6366F1', // Indigo - community and belonging
    colorSecondary: '#818CF8',
    colorAccent: '#A5B4FC',
    emoji: '🤝'
  },

  // FESTIVALS - Event information
  festivals: {
    name: 'Festivals',
    description: 'Festival information and events',
    colorPrimary: '#DC2626', // Red - excitement and energy
    colorSecondary: '#EF4444',
    colorAccent: '#F87171',
    emoji: '🎪'
  },

  // RESOURCES - Helpful resources
  resources: {
    name: 'Resources',
    description: 'Helpful resources and information',
    colorPrimary: '#7C3AED', // Violet - knowledge and resources
    colorSecondary: '#8B5CF6',
    colorAccent: '#A78BFA',
    emoji: '📚'
  }
};

async function initializeColorMappings() {
  const errors = [];
  let successCount = 0;

  try {
    console.log('🧹 Clearing existing color mappings...');

    // Clear existing color mappings
    await supabase.from('color_mappings').delete().neq('id', '00000000-0000-0000-0000-000000000000');

    console.log('📝 Inserting enhanced color mappings...');

    // Insert enhanced color mappings
    for (const [contentType, config] of Object.entries(contentTypeConfigs)) {
      console.log(`  📌 Processing ${contentType}...`);

      // Insert main content type
      const mainMapping = {
        content_type: contentType,
        category: 'main',
        color_primary: config.colorPrimary,
        color_secondary: config.colorSecondary,
        color_accent: config.colorAccent,
        emoji_icon: config.emoji,
        description: config.description,
        show_icon: true, // Default to showing icons
        admin_configurable: true // Allow admin configuration
      };

      const { error: mainError } = await supabase
        .from('color_mappings')
        .insert(mainMapping);

      if (mainError) {
        errors.push(`Failed to insert main mapping for ${contentType}: ${mainError.message}`);
        console.error(`    ❌ Error inserting main mapping for ${contentType}:`, mainError.message);
      } else {
        successCount++;
        console.log(`    ✅ Inserted main mapping for ${contentType}`);
      }

      // Insert subcategories
      if (config.subcategories) {
        for (const [subCategory, subConfig] of Object.entries(config.subcategories)) {
          console.log(`    📌 Processing subcategory ${contentType}.${subCategory}...`);

          const subMapping = {
            content_type: contentType,
            category: subCategory,
            color_primary: subConfig.colorPrimary,
            color_secondary: subConfig.colorSecondary,
            color_accent: subConfig.colorAccent,
            emoji_icon: subConfig.emoji,
            description: subConfig.description,
            show_icon: true, // Default to showing icons
            admin_configurable: true // Allow admin configuration
          };

          const { error: subError } = await supabase
            .from('color_mappings')
            .insert(subMapping);

          if (subError) {
            errors.push(`Failed to insert subcategory mapping for ${contentType}.${subCategory}: ${subError.message}`);
            console.error(`      ❌ Error inserting subcategory mapping for ${contentType}.${subCategory}:`, subError.message);
          } else {
            successCount++;
            console.log(`      ✅ Inserted subcategory mapping for ${contentType}.${subCategory}`);
          }
        }
      }
    }

    console.log('\n🎉 Color mappings initialization complete!');
    console.log(`✅ Successfully inserted: ${successCount} mappings`);

    if (errors.length > 0) {
      console.log(`❌ Errors encountered: ${errors.length}`);
      errors.forEach(error => console.error(`  - ${error}`));
    }

    return { success: errors.length === 0, count: successCount, errors };

  } catch (error) {
    console.error('❌ Fatal error during initialization:', error);
    return { success: false, count: successCount, errors: [error.message] };
  }
}

// Run the initialization
initializeColorMappings()
  .then(result => {
    if (result.success) {
      console.log('\n🎨 Enhanced color mappings system is ready!');
      console.log('🔧 Admin interface can now manage visual elements');
      console.log('🎯 BentoCard components will use database-driven colors');
    } else {
      console.error('\n❌ Initialization failed. Please check the errors above.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('❌ Script execution failed:', error);
    process.exit(1);
  });
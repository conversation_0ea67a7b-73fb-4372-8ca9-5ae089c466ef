# Festival Family Component Structure Guide

This guide outlines the recommended component structure for the Festival Family application, focusing on organization, reusability patterns, and state management approaches.

## Table of Contents

1. [Component Hierarchy](#component-hierarchy)
2. [Component Categories](#component-categories)
3. [Component Composition Patterns](#component-composition-patterns)
4. [State Management Patterns](#state-management-patterns)
5. [Component File Structure](#component-file-structure)
6. [Naming Conventions](#naming-conventions)
7. [Component Examples](#component-examples)

## Component Hierarchy

The Festival Family app follows a hierarchical component structure:

```
App
├── Layouts (MainLayout, AdminLayout)
│   ├── Navigation Components (TopBar, BottomNav, UnifiedNav)
│   ├── Page Components (Home, Activities, FamHub, etc.)
│   │   ├── Feature Components (FestivalList, ProfileCard, etc.)
│   │   │   ├── UI Components (Button, Card, Input, etc.)
```

### Hierarchy Levels

1. **Root Components**: App, ErrorBoundary, AuthProvider
2. **Layout Components**: MainLayout, AdminLayout
3. **Navigation Components**: TopBar, BottomNav, UnifiedNav
4. **Page Components**: Home, Activities, FamHub, Discover, Profile
5. **Feature Components**: FestivalList, ProfileCard, ActivityCard
6. **UI Components**: Button, Card, Input, Avatar

## Component Categories

### 1. UI Components

Basic, reusable UI elements that form the building blocks of the application.

**Characteristics:**
- Highly reusable across the application
- Focused on presentation, not business logic
- Stateless or with minimal internal state
- Accept props for customization
- Located in `src/components/ui/`

**Examples:**
- Button
- Input
- Card
- Avatar
- Badge

### 2. Layout Components

Components that define the overall structure of the application.

**Characteristics:**
- Define the page structure
- Handle responsive layout
- Provide consistent navigation
- Located in `src/components/layout/` or `src/layouts/`

**Examples:**
- MainLayout
- AdminLayout
- AuthLayout

### 3. Feature Components

Components that implement specific features of the application.

**Characteristics:**
- Implement business logic for a specific feature
- Compose UI components to create feature interfaces
- May contain feature-specific state
- Located in `src/features/{feature-name}/components/`

**Examples:**
- FestivalCard
- ProfileDetails
- ActivityList

### 4. Page Components

Top-level components that represent entire pages in the application.

**Characteristics:**
- Compose feature components to create complete pages
- Handle page-level state and data fetching
- Connected to routes
- Located in `src/pages/`

**Examples:**
- Home
- Activities
- FamHub
- Profile

### 5. Container Components

Components that handle data fetching and state management, separating these concerns from presentation.

**Characteristics:**
- Focus on data fetching and state management
- Render presentational components with data
- Located alongside their presentational counterparts

**Examples:**
- FestivalListContainer
- ProfileContainer
- ActivityDetailsContainer

## Component Composition Patterns

### 1. Composition over Inheritance

Use component composition to build complex UIs from simpler components:

```tsx
// Good: Using composition
function ProfilePage() {
  return (
    <PageLayout>
      <ProfileHeader user={user} />
      <ProfileDetails user={user} />
      <UserFestivals festivals={festivals} />
    </PageLayout>
  );
}

// Avoid: Large monolithic components
function ProfilePage() {
  return (
    <div>
      {/* 200+ lines of JSX for header, details, festivals, etc. */}
    </div>
  );
}
```

### 2. Container/Presentational Pattern

Separate data fetching and state management from presentation:

```tsx
// Container component
function FestivalListContainer() {
  const { data: festivals, isLoading, error } = useFestivals();
  
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  
  return <FestivalList festivals={festivals} />;
}

// Presentational component
function FestivalList({ festivals }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {festivals.map(festival => (
        <FestivalCard key={festival.id} festival={festival} />
      ))}
    </div>
  );
}
```

### 3. Render Props Pattern

Use render props for flexible component composition:

```tsx
function FestivalFilter({ festivals, render }) {
  const [filter, setFilter] = useState('all');
  
  const filteredFestivals = useMemo(() => {
    return festivals.filter(festival => {
      if (filter === 'all') return true;
      return festival.type === filter;
    });
  }, [festivals, filter]);
  
  return (
    <div>
      <FilterControls value={filter} onChange={setFilter} />
      {render(filteredFestivals)}
    </div>
  );
}

// Usage
<FestivalFilter
  festivals={festivals}
  render={filteredFestivals => (
    <FestivalList festivals={filteredFestivals} />
  )}
/>
```

### 4. Compound Components Pattern

Create related components that work together:

```tsx
// Define the compound component
const Tabs = ({ children, defaultValue }) => {
  const [value, setValue] = useState(defaultValue);
  
  return (
    <TabsContext.Provider value={{ value, setValue }}>
      {children}
    </TabsContext.Provider>
  );
};

// Child components
Tabs.List = ({ children }) => <div className="tabs-list">{children}</div>;
Tabs.Tab = ({ value, children }) => {
  const { value: selectedValue, setValue } = useTabsContext();
  
  return (
    <button
      className={selectedValue === value ? 'active' : ''}
      onClick={() => setValue(value)}
    >
      {children}
    </button>
  );
};
Tabs.Content = ({ value, children }) => {
  const { value: selectedValue } = useTabsContext();
  
  return selectedValue === value ? children : null;
};

// Usage
<Tabs defaultValue="upcoming">
  <Tabs.List>
    <Tabs.Tab value="upcoming">Upcoming</Tabs.Tab>
    <Tabs.Tab value="past">Past</Tabs.Tab>
  </Tabs.List>
  <Tabs.Content value="upcoming">
    <UpcomingFestivals />
  </Tabs.Content>
  <Tabs.Content value="past">
    <PastFestivals />
  </Tabs.Content>
</Tabs>
```

## State Management Patterns

### 1. Local Component State

Use React's `useState` and `useReducer` for component-specific state:

```tsx
function FestivalFilter() {
  const [filter, setFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  
  // Component implementation
}
```

### 2. Lifting State Up

Lift state to a common ancestor when multiple components need to share state:

```tsx
function FestivalPage() {
  const [selectedFestival, setSelectedFestival] = useState(null);
  
  return (
    <div>
      <FestivalList
        festivals={festivals}
        onSelectFestival={setSelectedFestival}
      />
      <FestivalDetails festival={selectedFestival} />
    </div>
  );
}
```

### 3. Context API for Shared State

Use Context API for state that needs to be shared across multiple components:

```tsx
// Create context
const FestivalContext = createContext();

// Create provider
function FestivalProvider({ children }) {
  const [festivals, setFestivals] = useState([]);
  const [selectedFestival, setSelectedFestival] = useState(null);
  
  // Fetch festivals, etc.
  
  return (
    <FestivalContext.Provider value={{ festivals, selectedFestival, setSelectedFestival }}>
      {children}
    </FestivalContext.Provider>
  );
}

// Custom hook for using the context
function useFestivalContext() {
  const context = useContext(FestivalContext);
  if (context === undefined) {
    throw new Error('useFestivalContext must be used within a FestivalProvider');
  }
  return context;
}

// Usage in components
function FestivalList() {
  const { festivals, setSelectedFestival } = useFestivalContext();
  
  // Component implementation
}
```

### 4. React Query for Server State

Use React Query for managing server state:

```tsx
// Define query hooks
function useFestivals() {
  return useQuery(
    ['festivals'],
    () => supabase.from('festivals').select('*'),
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
    }
  );
}

// Use in components
function FestivalList() {
  const { data: festivals, isLoading, error } = useFestivals();
  
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  
  return (
    <div>
      {festivals.map(festival => (
        <FestivalCard key={festival.id} festival={festival} />
      ))}
    </div>
  );
}
```

### 5. Zustand for Global State

Use Zustand for global application state:

```tsx
// Create a store
import create from 'zustand';

interface UIState {
  isNavOpen: boolean;
  toggleNav: () => void;
  theme: 'light' | 'dark';
  setTheme: (theme: 'light' | 'dark') => void;
}

export const useUIStore = create<UIState>((set) => ({
  isNavOpen: false,
  toggleNav: () => set((state) => ({ isNavOpen: !state.isNavOpen })),
  theme: 'dark',
  setTheme: (theme) => set({ theme }),
}));

// Use in components
function Navigation() {
  const { isNavOpen, toggleNav } = useUIStore();
  
  return (
    <>
      <button onClick={toggleNav}>Toggle Nav</button>
      {isNavOpen && <NavMenu />}
    </>
  );
}
```

## Component File Structure

### 1. Single Component per File

Each component should be defined in its own file:

```
src/
├── components/
│   ├── ui/
│   │   ├── Button.tsx
│   │   ├── Card.tsx
│   │   └── Input.tsx
```

### 2. Index Files for Exports

Use index files to export components from directories:

```tsx
// src/components/ui/index.ts
export { Button } from './Button';
export { Card, CardHeader, CardContent, CardFooter } from './Card';
export { Input } from './Input';

// Usage
import { Button, Card, Input } from '@/components/ui';
```

### 3. Co-located Files

Co-locate related files with their components:

```
src/
├── features/
│   ├── festivals/
│   │   ├── components/
│   │   │   ├── FestivalCard/
│   │   │   │   ├── FestivalCard.tsx
│   │   │   │   ├── FestivalCard.test.tsx
│   │   │   │   └── index.ts
```

### 4. Feature-based Organization

Organize components by feature:

```
src/
├── features/
│   ├── auth/
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── utils/
│   │   └── types.ts
│   ├── festivals/
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── utils/
│   │   └── types.ts
```

## Naming Conventions

### 1. Component Names

- Use **PascalCase** for component names: `FestivalCard`, `ProfileDetails`
- Use descriptive names that indicate the component's purpose
- Prefix related components with a common name: `FestivalCard`, `FestivalList`, `FestivalDetails`

### 2. Prop Names

- Use **camelCase** for prop names: `onFestivalSelect`, `isLoading`
- Use boolean props with `is`, `has`, or `should` prefixes: `isLoading`, `hasError`, `shouldAnimate`
- Use callback props with `on` prefix: `onClick`, `onSubmit`, `onFestivalSelect`

### 3. Event Handler Names

- Use `handle` prefix for event handler functions: `handleClick`, `handleSubmit`
- Use descriptive names that indicate the action: `handleFestivalSelect`, `handleProfileUpdate`

## Component Examples

### 1. UI Component Example

```tsx
// src/components/ui/Button.tsx
import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'underline-offset-4 hover:underline text-primary',
      },
      size: {
        default: 'h-10 py-2 px-4',
        sm: 'h-9 px-3 rounded-md',
        lg: 'h-11 px-8 rounded-md',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
```

### 2. Feature Component Example

```tsx
// src/features/festivals/components/FestivalCard/FestivalCard.tsx
import React from 'react';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { formatDate } from '@/lib/utils/date';
import type { Festival } from '@/features/festivals/types';

interface FestivalCardProps {
  festival: Festival;
  onSelect?: (festival: Festival) => void;
}

export function FestivalCard({ festival, onSelect }: FestivalCardProps) {
  const handleClick = () => {
    if (onSelect) {
      onSelect(festival);
    }
  };

  return (
    <Card className="h-full hover:shadow-lg transition-shadow">
      <CardContent className="p-0">
        <div className="relative h-48 w-full">
          <img
            src={festival.imageUrl || '/placeholder-festival.jpg'}
            alt={festival.name}
            className="h-full w-full object-cover rounded-t-lg"
          />
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
            <h3 className="text-xl font-bold text-white">{festival.name}</h3>
            <p className="text-sm text-white/80">
              {formatDate(festival.startDate)} - {formatDate(festival.endDate)}
            </p>
          </div>
        </div>
      </CardContent>
      <CardFooter className="p-4 flex justify-between items-center">
        <span className="text-sm">{festival.location}</span>
        <Button onClick={handleClick} size="sm">View Details</Button>
      </CardFooter>
    </Card>
  );
}
```

### 3. Container Component Example

```tsx
// src/features/festivals/components/FestivalList/FestivalListContainer.tsx
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useFestivals } from '@/features/festivals/hooks/useFestivals';
import { FestivalList } from './FestivalList';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ErrorMessage } from '@/components/ui/ErrorMessage';
import type { Festival } from '@/features/festivals/types';

export function FestivalListContainer() {
  const navigate = useNavigate();
  const { data: festivals, isLoading, error } = useFestivals();
  
  const handleFestivalSelect = (festival: Festival) => {
    navigate(`/festivals/${festival.id}`);
  };
  
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  
  return <FestivalList festivals={festivals} onFestivalSelect={handleFestivalSelect} />;
}
```

### 4. Page Component Example

```tsx
// src/pages/Festivals.tsx
import React from 'react';
import { PageLayout } from '@/components/layout/PageLayout';
import { FestivalListContainer } from '@/features/festivals/components/FestivalList';
import { FestivalFilter } from '@/features/festivals/components/FestivalFilter';
import { useDocumentTitle } from '@/hooks/useDocumentTitle';

export default function FestivalsPage() {
  useDocumentTitle('Festivals | Festival Family');
  
  return (
    <PageLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Festivals</h1>
          <FestivalFilter />
        </div>
        <FestivalListContainer />
      </div>
    </PageLayout>
  );
}
```

---

By following these component structure guidelines, the Festival Family app will have a more organized, maintainable, and scalable codebase. These patterns promote reusability, separation of concerns, and clear component responsibilities.

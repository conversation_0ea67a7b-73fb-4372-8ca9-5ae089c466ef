-- Simple Verification Script
-- Run this after step-by-step-fixes.sql to verify everything works

-- Check all required tables exist
SELECT 
    'TABLES' as check_type,
    COUNT(*) as total_found,
    CASE WHEN COUNT(*) = 7 THEN 'SUCCESS' ELSE 'MISSING TABLES' END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('announcements', 'tips', 'faqs', 'content_management', 'user_preferences', 'emergency_contacts', 'safety_information');

-- Check all required functions exist
SELECT 
    'FUNCTIONS' as check_type,
    COUNT(*) as total_found,
    CASE WHEN COUNT(*) = 4 THEN 'SUCCESS' ELSE 'MISSING FUNCTIONS' END as status
FROM information_schema.routines 
WHERE routine_schema = 'public' AND routine_type = 'FUNCTION'
AND routine_name IN ('is_admin', 'is_super_admin', 'is_content_admin', 'can_manage_groups');

-- Check FAQ table has required columns
SELECT 
    'FAQ_COLUMNS' as check_type,
    COUNT(*) as columns_found,
    CASE WHEN COUNT(*) >= 2 THEN 'SUCCESS' ELSE 'MISSING COLUMNS' END as status
FROM information_schema.columns 
WHERE table_name = 'faqs' 
AND column_name IN ('category', 'order_index');

-- Test function execution
SELECT 
    'FUNCTION_TEST' as check_type,
    CASE 
        WHEN is_admin() IS NOT NULL 
        AND is_super_admin() IS NOT NULL 
        AND is_content_admin() IS NOT NULL 
        AND can_manage_groups() IS NOT NULL 
        THEN 'SUCCESS' 
        ELSE 'FUNCTION_ERROR' 
    END as status;

-- Overall status
SELECT 
    'OVERALL_STATUS' as check_type,
    CASE 
        WHEN (
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('announcements', 'tips', 'faqs', 'content_management', 'user_preferences', 'emergency_contacts', 'safety_information')
        ) = 7
        AND (
            SELECT COUNT(*) FROM information_schema.routines 
            WHERE routine_schema = 'public' AND routine_type = 'FUNCTION'
            AND routine_name IN ('is_admin', 'is_super_admin', 'is_content_admin', 'can_manage_groups')
        ) = 4
        THEN 'DATABASE_COMPLETE' 
        ELSE 'NEEDS_MORE_WORK' 
    END as status;

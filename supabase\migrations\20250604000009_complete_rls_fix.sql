-- Complete RLS Recursion Fix
-- This migration fixes all remaining RLS recursion issues
-- and ensures admin dashboard functionality works properly

-- ============================================================================
-- HELPER FUNCTIONS (Core fix for RLS recursion)
-- ============================================================================

-- Create or replace the is_admin helper function
CREATE OR REPLACE FUNCTION is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Direct query without RLS to avoid recursion
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = user_id 
        AND role IN ('SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR')
    );
END;
$$;

-- Create or replace the is_super_admin helper function
CREATE OR REPLACE FUNCTION is_super_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Direct query without RLS to avoid recursion
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = user_id 
        AND role = 'SUPER_ADMIN'
    );
END;
$$;

-- Create or replace the is_content_admin helper function
CREATE OR REPLACE FUNCTION is_content_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Direct query without RLS to avoid recursion
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = user_id 
        AND role IN ('SUPER_ADMIN', 'CONTENT_ADMIN')
    );
END;
$$;

-- Create or replace the can_manage_groups helper function
CREATE OR REPLACE FUNCTION can_manage_groups(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Direct query without RLS to avoid recursion
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = user_id 
        AND role IN ('SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR')
    );
END;
$$;

-- ============================================================================
-- FIX GROUP_MEMBERS TABLE RLS POLICIES
-- ============================================================================

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Users can view group members" ON group_members;
DROP POLICY IF EXISTS "Users can join groups" ON group_members;
DROP POLICY IF EXISTS "Users can leave groups" ON group_members;
DROP POLICY IF EXISTS "Group creators can manage members" ON group_members;
DROP POLICY IF EXISTS "Admins can manage all group members" ON group_members;

-- Create new non-recursive policies for group_members
CREATE POLICY "Anyone can view group members"
    ON group_members FOR SELECT
    USING (true);

CREATE POLICY "Users can join groups"
    ON group_members FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can leave their own groups"
    ON group_members FOR DELETE
    USING (auth.uid() = user_id);

CREATE POLICY "Group creators can manage members"
    ON group_members FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM groups 
            WHERE groups.id = group_members.group_id 
            AND groups.creator_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage all group members"
    ON group_members FOR ALL
    USING (can_manage_groups(auth.uid()));

-- ============================================================================
-- FIX CHAT TABLES RLS POLICIES
-- ============================================================================

-- Fix chat_rooms table
DROP POLICY IF EXISTS "Users can view chat rooms they're members of" ON chat_rooms;
DROP POLICY IF EXISTS "Group creators can manage chat rooms" ON chat_rooms;
DROP POLICY IF EXISTS "Admins can manage all chat rooms" ON chat_rooms;

CREATE POLICY "Users can view chat rooms they're members of"
    ON chat_rooms FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM chat_room_members 
            WHERE chat_room_members.chat_room_id = chat_rooms.id 
            AND chat_room_members.user_id = auth.uid()
        )
        OR can_manage_groups(auth.uid())
    );

CREATE POLICY "Group creators can create chat rooms"
    ON chat_rooms FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM groups 
            WHERE groups.id = chat_rooms.group_id 
            AND groups.creator_id = auth.uid()
        )
        OR can_manage_groups(auth.uid())
    );

CREATE POLICY "Admins can manage all chat rooms"
    ON chat_rooms FOR ALL
    USING (can_manage_groups(auth.uid()));

-- Fix chat_room_members table
DROP POLICY IF EXISTS "Users can view chat room members" ON chat_room_members;
DROP POLICY IF EXISTS "Users can join chat rooms" ON chat_room_members;
DROP POLICY IF EXISTS "Users can leave chat rooms" ON chat_room_members;
DROP POLICY IF EXISTS "Admins can manage chat room members" ON chat_room_members;

CREATE POLICY "Users can view chat room members"
    ON chat_room_members FOR SELECT
    USING (
        user_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM chat_room_members crm2
            WHERE crm2.chat_room_id = chat_room_members.chat_room_id
            AND crm2.user_id = auth.uid()
        )
        OR can_manage_groups(auth.uid())
    );

CREATE POLICY "Users can join chat rooms"
    ON chat_room_members FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can leave chat rooms"
    ON chat_room_members FOR DELETE
    USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage chat room members"
    ON chat_room_members FOR ALL
    USING (can_manage_groups(auth.uid()));

-- Fix chat_messages table
DROP POLICY IF EXISTS "Users can view messages in their chat rooms" ON chat_messages;
DROP POLICY IF EXISTS "Users can send messages" ON chat_messages;
DROP POLICY IF EXISTS "Users can edit their own messages" ON chat_messages;
DROP POLICY IF EXISTS "Admins can manage all messages" ON chat_messages;

CREATE POLICY "Users can view messages in their chat rooms"
    ON chat_messages FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM chat_room_members 
            WHERE chat_room_members.chat_room_id = chat_messages.chat_room_id 
            AND chat_room_members.user_id = auth.uid()
        )
        OR can_manage_groups(auth.uid())
    );

CREATE POLICY "Users can send messages"
    ON chat_messages FOR INSERT
    WITH CHECK (
        auth.uid() = sender_id
        AND EXISTS (
            SELECT 1 FROM chat_room_members 
            WHERE chat_room_members.chat_room_id = chat_messages.chat_room_id 
            AND chat_room_members.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can edit their own messages"
    ON chat_messages FOR UPDATE
    USING (auth.uid() = sender_id);

CREATE POLICY "Admins can manage all messages"
    ON chat_messages FOR ALL
    USING (can_manage_groups(auth.uid()));

-- ============================================================================
-- ENSURE ADMIN FUNCTIONS WORK PROPERLY
-- ============================================================================

-- Create or replace change_user_role function
CREATE OR REPLACE FUNCTION change_user_role(target_user_id UUID, new_role TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Only SUPER_ADMIN can change user roles
    IF NOT is_super_admin(auth.uid()) THEN
        RAISE EXCEPTION 'Only SUPER_ADMIN can change user roles';
    END IF;
    
    -- Validate role
    IF new_role NOT IN ('USER', 'MODERATOR', 'CONTENT_ADMIN', 'SUPER_ADMIN') THEN
        RAISE EXCEPTION 'Invalid role: %', new_role;
    END IF;
    
    -- Update the user's role
    UPDATE profiles 
    SET role = new_role::user_role, 
        updated_at = NOW()
    WHERE id = target_user_id;
    
    RETURN FOUND;
END;
$$;

-- Grant permissions to authenticated users
GRANT EXECUTE ON FUNCTION is_admin TO authenticated;
GRANT EXECUTE ON FUNCTION is_super_admin TO authenticated;
GRANT EXECUTE ON FUNCTION is_content_admin TO authenticated;
GRANT EXECUTE ON FUNCTION can_manage_groups TO authenticated;
GRANT EXECUTE ON FUNCTION change_user_role TO authenticated;

-- ============================================================================
-- VALIDATION AND TESTING
-- ============================================================================

-- Test that all functions work
DO $$
DECLARE
    test_result BOOLEAN;
BEGIN
    -- Test is_admin function
    SELECT is_admin() INTO test_result;
    RAISE NOTICE 'SUCCESS: is_admin function working';
    
    -- Test is_super_admin function
    SELECT is_super_admin() INTO test_result;
    RAISE NOTICE 'SUCCESS: is_super_admin function working';
    
    -- Test is_content_admin function
    SELECT is_content_admin() INTO test_result;
    RAISE NOTICE 'SUCCESS: is_content_admin function working';
    
    -- Test can_manage_groups function
    SELECT can_manage_groups() INTO test_result;
    RAISE NOTICE 'SUCCESS: can_manage_groups function working';
    
    RAISE NOTICE 'SUCCESS: All RLS helper functions are working properly';
END $$;

-- Test table access
DO $$
DECLARE
    group_members_count INTEGER;
    chat_rooms_count INTEGER;
    chat_messages_count INTEGER;
BEGIN
    -- Test group_members access
    SELECT COUNT(*) INTO group_members_count FROM group_members LIMIT 1;
    RAISE NOTICE 'SUCCESS: group_members table accessible';
    
    -- Test chat_rooms access
    SELECT COUNT(*) INTO chat_rooms_count FROM chat_rooms LIMIT 1;
    RAISE NOTICE 'SUCCESS: chat_rooms table accessible';
    
    -- Test chat_messages access
    SELECT COUNT(*) INTO chat_messages_count FROM chat_messages LIMIT 1;
    RAISE NOTICE 'SUCCESS: chat_messages table accessible';
    
    RAISE NOTICE 'SUCCESS: All previously problematic tables are now accessible';
END $$;

-- Log successful migration
DO $$
BEGIN
    RAISE NOTICE 'SUCCESS: Complete RLS recursion fix applied';
    RAISE NOTICE 'INFO: All helper functions created and working';
    RAISE NOTICE 'INFO: group_members table RLS policies fixed';
    RAISE NOTICE 'INFO: chat_rooms table RLS policies fixed';
    RAISE NOTICE 'INFO: chat_room_members table RLS policies fixed';
    RAISE NOTICE 'INFO: chat_messages table RLS policies fixed';
    RAISE NOTICE 'INFO: Admin functions properly secured';
    RAISE NOTICE 'INFO: Admin dashboard should now have full functionality';
END $$;

/**
 * Simple Profile Debug - Focus on Timeout Issue
 * 
 * Since the comprehensive debug script hangs, let's focus on the specific timeout issue
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('🔍 Simple Profile Debug - Timeout Focus');
console.log('======================================');

async function simpleProfileDebug() {
  try {
    // Test 1: Quick connectivity test with timeout
    console.log('🌐 Test 1: Quick Connectivity Test');
    console.log('----------------------------------');
    
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Connectivity timeout')), 3000);
    });
    
    const connectivityPromise = supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    try {
      const startTime = Date.now();
      const result = await Promise.race([connectivityPromise, timeoutPromise]);
      const duration = Date.now() - startTime;
      console.log(`✅ Connectivity successful in ${duration}ms`);
    } catch (error) {
      console.error('❌ Connectivity failed:', error.message);
      if (error.message === 'Connectivity timeout') {
        console.log('🔍 ISSUE IDENTIFIED: Basic connectivity is timing out');
        console.log('🔧 This suggests network or Supabase performance issues');
        return;
      }
    }
    
    console.log('');

    // Test 2: Authentication with timeout
    console.log('🔐 Test 2: Authentication Test');
    console.log('------------------------------');
    
    const authTimeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Auth timeout')), 5000);
    });
    
    const authPromise = supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testpassword123'
    });
    
    let authData;
    try {
      const startTime = Date.now();
      authData = await Promise.race([authPromise, authTimeoutPromise]);
      const duration = Date.now() - startTime;
      console.log(`✅ Authentication successful in ${duration}ms`);
    } catch (error) {
      console.error('❌ Authentication failed:', error.message);
      if (error.message === 'Auth timeout') {
        console.log('🔍 ISSUE IDENTIFIED: Authentication is timing out');
        return;
      }
    }
    
    console.log('');

    // Test 3: Profile query with timeout (the main issue)
    console.log('📊 Test 3: Profile Query with Timeout');
    console.log('-------------------------------------');
    
    const profileTimeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Profile query timeout')), 3000);
    });
    
    const profilePromise = supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.data.user.id)
      .single();
    
    try {
      const startTime = Date.now();
      const profileResult = await Promise.race([profilePromise, profileTimeoutPromise]);
      const duration = Date.now() - startTime;
      console.log(`✅ Profile query successful in ${duration}ms`);
      console.log('👤 Profile role:', profileResult.data.role);
    } catch (error) {
      console.error('❌ Profile query failed:', error.message);
      if (error.message === 'Profile query timeout') {
        console.log('🔍 ISSUE IDENTIFIED: Profile queries are timing out');
        console.log('🔧 This is the root cause of our authentication issues');
        
        // Let's try to understand why
        console.log('');
        console.log('🔍 Investigating Profile Query Timeout...');
        console.log('----------------------------------------');
        
        // Try without .single()
        try {
          console.log('🧪 Testing query without .single()...');
          const noSingleStart = Date.now();
          const noSingleResult = await supabase
            .from('profiles')
            .select('*')
            .eq('id', authData.data.user.id);
          const noSingleDuration = Date.now() - noSingleStart;
          console.log(`✅ Query without .single() successful in ${noSingleDuration}ms`);
          console.log(`📊 Returned ${noSingleResult.data.length} records`);
        } catch (noSingleError) {
          console.error('❌ Query without .single() also failed:', noSingleError.message);
        }
        
        // Try with minimal fields
        try {
          console.log('🧪 Testing query with minimal fields...');
          const minimalStart = Date.now();
          const minimalResult = await supabase
            .from('profiles')
            .select('id, role')
            .eq('id', authData.data.user.id)
            .single();
          const minimalDuration = Date.now() - minimalStart;
          console.log(`✅ Minimal query successful in ${minimalDuration}ms`);
          console.log('👤 Profile role:', minimalResult.data.role);
        } catch (minimalError) {
          console.error('❌ Minimal query also failed:', minimalError.message);
        }
      }
    }

  } catch (error) {
    console.error('💥 Debug failed with exception:', error);
  }
}

// Run with overall timeout
const overallTimeout = setTimeout(() => {
  console.error('💥 CRITICAL: Entire debug script timed out');
  console.log('🔍 This confirms that Supabase connectivity has serious issues');
  process.exit(1);
}, 15000); // 15 second overall timeout

simpleProfileDebug().then(() => {
  clearTimeout(overallTimeout);
  console.log('');
  console.log('🎯 Simple Profile Debug Complete');
  console.log('================================');
  process.exit(0);
}).catch(error => {
  clearTimeout(overallTimeout);
  console.error('💥 Debug suite failed:', error);
  process.exit(1);
});

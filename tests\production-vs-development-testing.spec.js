/**
 * Production vs Development Testing
 * 
 * This test compares the behavior between development and production builds
 * to determine if the UI interaction issues are development-specific artifacts
 * or real production problems.
 */

import { test, expect } from '@playwright/test';

// Test both development and production URLs
const TEST_URLS = {
  development: 'http://localhost:5173',
  production: 'http://localhost:4173'
};

// Helper functions
async function takeEvidence(page, name, description, environment) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `${environment}-${name}-${timestamp}.png`;
  await page.screenshot({ 
    path: `test-results/${filename}`,
    fullPage: true 
  });
  console.log(`📸 ${environment.toUpperCase()} Evidence: ${filename} - ${description}`);
  return filename;
}

async function testAuthenticationFlow(page, environment) {
  console.log(`🔐 Testing authentication flow in ${environment}...`);
  
  await page.goto(TEST_URLS[environment]);
  await page.waitForLoadState('networkidle');
  await takeEvidence(page, 'auth-initial', 'Initial page load', environment);
  
  // Look for authentication elements
  const loginButton = page.locator('button:has-text("Login"), button:has-text("Sign In"), a:has-text("Login"), a:has-text("Sign In")').first();
  
  const results = {
    environment,
    loginButtonVisible: false,
    loginButtonClickable: false,
    authFormAccessible: false,
    error: null
  };
  
  try {
    if (await loginButton.isVisible()) {
      results.loginButtonVisible = true;
      await takeEvidence(page, 'auth-button-found', 'Login button found', environment);
      
      // Test if button is clickable
      try {
        await loginButton.click({ timeout: 5000 });
        results.loginButtonClickable = true;
        await page.waitForTimeout(2000);
        await takeEvidence(page, 'auth-button-clicked', 'Login button clicked successfully', environment);
        
        // Check if auth form appeared
        const emailInput = page.locator('input[type="email"], input[placeholder*="email" i]').first();
        const passwordInput = page.locator('input[type="password"], input[placeholder*="password" i]').first();
        
        if (await emailInput.isVisible() && await passwordInput.isVisible()) {
          results.authFormAccessible = true;
          console.log(`✅ ${environment}: Authentication form accessible`);
          
          // Test form filling
          await emailInput.fill('<EMAIL>');
          await passwordInput.fill('testpassword123');
          await takeEvidence(page, 'auth-form-filled', 'Authentication form filled', environment);
          
        } else {
          console.log(`⚠️ ${environment}: Login button clicked but no form appeared`);
        }
        
      } catch (clickError) {
        results.error = `Click failed: ${clickError.message}`;
        console.log(`❌ ${environment}: Login button click failed - ${clickError.message}`);
        await takeEvidence(page, 'auth-click-failed', 'Login button click failed', environment);
      }
      
    } else {
      console.log(`ℹ️ ${environment}: No login button found`);
      await takeEvidence(page, 'no-auth-button', 'No login button found', environment);
    }
    
  } catch (error) {
    results.error = error.message;
    console.log(`❌ ${environment}: Authentication test failed - ${error.message}`);
  }
  
  return results;
}

async function testNavigationFlow(page, environment) {
  console.log(`🧭 Testing navigation flow in ${environment}...`);
  
  await page.goto(TEST_URLS[environment]);
  await page.waitForLoadState('networkidle');
  
  const results = {
    environment,
    navigationLinksFound: 0,
    clickableLinks: 0,
    successfulNavigations: 0,
    errors: []
  };
  
  try {
    // Find navigation links
    const navLinks = await page.locator('nav a, [role="navigation"] a, header a').all();
    results.navigationLinksFound = navLinks.length;
    console.log(`${environment}: Found ${navLinks.length} navigation links`);
    
    await takeEvidence(page, 'nav-overview', 'Navigation overview', environment);
    
    // Test first 3 navigation links
    for (let i = 0; i < Math.min(navLinks.length, 3); i++) {
      const link = navLinks[i];
      const linkText = await link.textContent();
      const href = await link.getAttribute('href');
      
      if (linkText && href && !href.startsWith('mailto:') && !href.startsWith('tel:')) {
        try {
          if (await link.isVisible()) {
            results.clickableLinks++;
            
            await link.click({ timeout: 5000 });
            await page.waitForTimeout(2000);
            
            const currentUrl = page.url();
            console.log(`✅ ${environment}: Successfully navigated to ${currentUrl}`);
            results.successfulNavigations++;
            
            await takeEvidence(page, `nav-${i}-success`, `Navigation to ${linkText} successful`, environment);
            
            // Go back for next test
            await page.goBack();
            await page.waitForTimeout(1000);
            
          } else {
            console.log(`⚠️ ${environment}: Link not visible: ${linkText}`);
          }
          
        } catch (navError) {
          const errorMsg = `Navigation failed for ${linkText}: ${navError.message}`;
          results.errors.push(errorMsg);
          console.log(`❌ ${environment}: ${errorMsg}`);
          await takeEvidence(page, `nav-${i}-failed`, `Navigation to ${linkText} failed`, environment);
        }
      }
    }
    
  } catch (error) {
    results.errors.push(`Navigation test failed: ${error.message}`);
    console.log(`❌ ${environment}: Navigation test failed - ${error.message}`);
  }
  
  return results;
}

test.describe('Production vs Development Comparison', () => {
  
  test('Authentication Flow Comparison', async ({ page }) => {
    console.log('🔍 Comparing authentication flows between environments...');
    
    const devResults = await testAuthenticationFlow(page, 'development');
    const prodResults = await testAuthenticationFlow(page, 'production');
    
    console.log('\n📊 AUTHENTICATION COMPARISON RESULTS:');
    console.log('Development:', devResults);
    console.log('Production:', prodResults);
    
    // Analysis
    if (devResults.loginButtonClickable !== prodResults.loginButtonClickable) {
      console.log('🚨 CRITICAL: Authentication behavior differs between environments!');
      if (prodResults.loginButtonClickable && !devResults.loginButtonClickable) {
        console.log('✅ GOOD NEWS: Production authentication works better than development');
      } else if (!prodResults.loginButtonClickable && devResults.loginButtonClickable) {
        console.log('❌ BAD NEWS: Production authentication is broken while development works');
      }
    } else if (!devResults.loginButtonClickable && !prodResults.loginButtonClickable) {
      console.log('⚠️ CONSISTENT ISSUE: Authentication problems exist in both environments');
    } else {
      console.log('✅ CONSISTENT SUCCESS: Authentication works in both environments');
    }
    
    // At least one environment should have working auth for the test to be meaningful
    expect(devResults.loginButtonVisible || prodResults.loginButtonVisible).toBe(true);
  });
  
  test('Navigation Flow Comparison', async ({ page }) => {
    console.log('🔍 Comparing navigation flows between environments...');
    
    const devResults = await testNavigationFlow(page, 'development');
    const prodResults = await testNavigationFlow(page, 'production');
    
    console.log('\n📊 NAVIGATION COMPARISON RESULTS:');
    console.log('Development:', devResults);
    console.log('Production:', prodResults);
    
    // Analysis
    const devSuccessRate = devResults.navigationLinksFound > 0 ? 
      (devResults.successfulNavigations / devResults.navigationLinksFound) * 100 : 0;
    const prodSuccessRate = prodResults.navigationLinksFound > 0 ? 
      (prodResults.successfulNavigations / prodResults.navigationLinksFound) * 100 : 0;
    
    console.log(`Development navigation success rate: ${devSuccessRate.toFixed(1)}%`);
    console.log(`Production navigation success rate: ${prodSuccessRate.toFixed(1)}%`);
    
    if (Math.abs(devSuccessRate - prodSuccessRate) > 20) {
      console.log('🚨 SIGNIFICANT DIFFERENCE: Navigation behavior varies significantly between environments');
    } else if (devSuccessRate < 50 && prodSuccessRate < 50) {
      console.log('⚠️ CONSISTENT ISSUE: Navigation problems exist in both environments');
    } else {
      console.log('✅ CONSISTENT BEHAVIOR: Navigation works similarly in both environments');
    }
    
    // At least some navigation should work
    expect(devResults.navigationLinksFound + prodResults.navigationLinksFound).toBeGreaterThan(0);
  });
  
  test('Performance Comparison', async ({ page }) => {
    console.log('⚡ Comparing performance between environments...');
    
    const performanceResults = {};
    
    for (const [env, url] of Object.entries(TEST_URLS)) {
      console.log(`Testing ${env} performance...`);
      
      const startTime = Date.now();
      await page.goto(url);
      await page.waitForLoadState('networkidle');
      const loadTime = Date.now() - startTime;
      
      const metrics = await page.evaluate(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        return {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          totalLoadTime: navigation.loadEventEnd - navigation.fetchStart,
          resourceCount: performance.getEntriesByType('resource').length
        };
      });
      
      performanceResults[env] = {
        ...metrics,
        measuredLoadTime: loadTime
      };
      
      await takeEvidence(page, 'performance-test', `Performance test for ${env}`, env);
    }
    
    console.log('\n📊 PERFORMANCE COMPARISON RESULTS:');
    console.log('Development:', performanceResults.development);
    console.log('Production:', performanceResults.production);
    
    // Performance analysis
    const devLoad = performanceResults.development.measuredLoadTime;
    const prodLoad = performanceResults.production.measuredLoadTime;
    
    if (prodLoad < devLoad * 0.8) {
      console.log('✅ EXCELLENT: Production is significantly faster than development');
    } else if (prodLoad > devLoad * 1.5) {
      console.log('⚠️ CONCERN: Production is slower than development');
    } else {
      console.log('✅ NORMAL: Performance is similar between environments');
    }
    
    // Both environments should load reasonably fast
    expect(devLoad).toBeLessThan(10000); // Less than 10 seconds
    expect(prodLoad).toBeLessThan(10000); // Less than 10 seconds
  });
});

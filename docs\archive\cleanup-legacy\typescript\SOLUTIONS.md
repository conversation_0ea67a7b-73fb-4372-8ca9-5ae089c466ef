# TypeScript and Type System Solutions

## 1. Centralized Type Definitions

### Solution
Establish a single source of truth for type definitions by using Supabase-generated types as the foundation and creating derived types for application use.

### Implementation
```typescript
// src/types/database.ts - Generated by Supabase CLI
export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: { 
          id: string;
          username: string;
          // ... other profile fields
        }
        Insert: { /* ... */ }
        Update: { /* ... */ }
      }
      // ... other tables
    }
  }
}

// src/types/index.ts - Derived from database types
import { Database } from './database';

// Export database row types directly
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type Festival = Database['public']['Tables']['festivals']['Row'];

// Create extended types for application use
export type ProfileWithRelations = Profile & {
  festivals?: Festival[];
};
```

### Benefits
- Single source of truth for type definitions
- Automatic updates when database schema changes
- Consistent types across the application
- Clear relationship between database and application types

## 2. Type Guard Implementation

### Solution
Implement proper type guards for runtime type checking, especially for external data and API responses.

### Implementation
```typescript
// src/utils/typeGuards.ts
import { z } from 'zod';
import type { Profile, Festival, UserRole } from '../types';

// Define Zod schemas for validation
export const profileSchema = z.object({
  id: z.string().uuid(),
  username: z.string().min(3).max(50),
  full_name: z.string().nullable(),
  avatar_url: z.string().url().nullable(),
  role: z.enum(['user', 'admin']).default('user')
});

// Type guard functions
export function isProfile(value: unknown): value is Profile {
  return profileSchema.safeParse(value).success;
}

export function isUserRole(value: unknown): value is UserRole {
  return z.enum(['user', 'admin', 'guest']).safeParse(value).success;
}

// Type-safe data fetching
export async function fetchProfile(userId: string): Promise<Profile> {
  const response = await fetch(`/api/profiles/${userId}`);
  const data = await response.json();
  
  const result = profileSchema.safeParse(data);
  if (!result.success) {
    throw new Error(`Invalid profile data: ${result.error.message}`);
  }
  
  return result.data;
}
```

### Benefits
- Runtime type validation
- Early error detection
- Improved debugging
- Enhanced security for external data

## 3. Consistent Type Import Pattern

### Solution
Establish and enforce a consistent pattern for importing types using ESLint rules.

### Implementation
```typescript
// .eslintrc.js
module.exports = {
  // ... other config
  rules: {
    // Enforce type-only imports for types
    '@typescript-eslint/consistent-type-imports': [
      'error',
      {
        prefer: 'type-only-imports',
        disallowTypeAnnotations: true,
      },
    ],
    // ... other rules
  },
};

// Example of consistent usage
import type { Profile, Festival } from '../types';
import { fetchProfile } from '../utils/typeGuards';
```

### Benefits
- Consistent code style
- Better tree-shaking
- Clear distinction between values and types
- Easier code reviews

## 4. Enum and Union Type Standardization

### Solution
Replace string literals with proper TypeScript union types or enums for all fixed sets of values.

### Implementation
```typescript
// src/types/enums.ts
export const UserRoles = ['admin', 'user', 'guest'] as const;
export type UserRole = typeof UserRoles[number];

export const FestivalStatuses = ['draft', 'published', 'cancelled', 'completed'] as const;
export type FestivalStatus = typeof FestivalStatuses[number];

// Usage example
function setUserRole(role: UserRole) {
  // Type-safe, only accepts valid roles
}

// Type guard for runtime validation
export function isFestivalStatus(value: unknown): value is FestivalStatus {
  return typeof value === 'string' && FestivalStatuses.includes(value as FestivalStatus);
}
```

### Benefits
- Type-safe enumerated values
- Autocomplete support
- Easy to add/remove values
- Runtime validation capability

## 5. Consistent Nullability Pattern

### Solution
Establish consistent patterns for handling null and undefined values using optional chaining and nullish coalescing.

### Implementation
```typescript
// src/utils/nullability.ts
export function getValueOrDefault<T>(value: T | null | undefined, defaultValue: T): T {
  return value ?? defaultValue;
}

export function getNestedValueOrDefault<T>(
  obj: any,
  path: string,
  defaultValue: T
): T {
  const value = path.split('.').reduce((o, key) => (o ? o[key] : undefined), obj);
  return value ?? defaultValue;
}

// Usage examples
const username = getValueOrDefault(user?.name, 'Anonymous');
const city = getNestedValueOrDefault(user, 'address.city', 'Unknown');
```

### Benefits
- Consistent null handling
- Reduced null reference errors
- Clear intent
- Reusable utility functions

## 6. Enhanced Database Type Generation

### Solution
Extend the generated Supabase types with additional relationship information and constraints.

### Implementation
```typescript
// src/types/relationships.ts
import type { Festival, Activity, Profile } from './index';

// Define relationship types
export type FestivalWithActivities = Festival & {
  activities: Activity[];
};

export type ActivityWithFestival = Activity & {
  festival: Festival;
};

export type ProfileWithFestivals = Profile & {
  festivals: Festival[];
};

// Type-safe relationship functions
export function getActivitiesForFestival(festival: Festival, activities: Activity[]): Activity[] {
  return activities.filter(activity => activity.festival_id === festival.id);
}

export function getFestivalForActivity(activity: Activity, festivals: Festival[]): Festival | undefined {
  return festivals.find(festival => festival.id === activity.festival_id);
}
```

### Benefits
- Enhanced type safety for relationships
- Clear documentation of database structure
- Improved developer experience
- Reduced errors in relationship handling
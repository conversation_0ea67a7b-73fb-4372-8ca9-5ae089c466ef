-- Migration: Add Activity Coordination System
-- This migration adds tables for activity attendance, music preferences, and real-time coordination
-- Following Festival Family's established patterns and naming conventions

-- ============================================================================
-- ENUMS FOR TYPE SAFETY
-- ============================================================================

-- Create attendance status enum
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'attendance_status') THEN
        CREATE TYPE attendance_status AS ENUM ('going', 'interested', 'maybe', 'not_going');
    END IF;
END $$;

-- Create preference level enum
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'preference_level') THEN
        CREATE TYPE preference_level AS ENUM ('love', 'like', 'neutral', 'dislike');
    END IF;
END $$;

-- Create chat platform enum for external links
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'chat_platform') THEN
        CREATE TYPE chat_platform AS ENUM ('whatsapp', 'discord', 'telegram', 'signal', 'other');
    END IF;
END $$;

-- ============================================================================
-- ACTIVITY ATTENDANCE SYSTEM
-- ============================================================================

-- Activity attendance tracking
CREATE TABLE IF NOT EXISTS activity_attendance (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    activity_id UUID NOT NULL REFERENCES activities(id) ON DELETE CASCADE,
    status attendance_status NOT NULL DEFAULT 'interested',
    notes TEXT, -- Optional user notes like "Looking for yoga buddy!"
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    -- Ensure one attendance record per user per activity
    UNIQUE(user_id, activity_id)
);

-- ============================================================================
-- MUSIC PREFERENCE SYSTEM  
-- ============================================================================

-- Artist preferences
CREATE TABLE IF NOT EXISTS artist_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    artist_name TEXT NOT NULL,
    preference_level preference_level NOT NULL DEFAULT 'like',
    genre TEXT, -- Auto-populated or user-specified
    spotify_artist_id TEXT, -- For future Spotify integration
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    -- Ensure one preference per user per artist
    UNIQUE(user_id, artist_name)
);

-- Music genre preferences
CREATE TABLE IF NOT EXISTS music_genre_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    genre TEXT NOT NULL,
    preference_level preference_level NOT NULL DEFAULT 'like',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    -- Ensure one preference per user per genre
    UNIQUE(user_id, genre)
);

-- ============================================================================
-- EXTERNAL CHAT LINKS (for group coordination)
-- ============================================================================

-- External chat links for groups
CREATE TABLE IF NOT EXISTS group_external_links (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    platform chat_platform NOT NULL,
    link_url TEXT NOT NULL,
    display_name TEXT NOT NULL, -- e.g., "WhatsApp Group", "Discord Server"
    description TEXT, -- Optional description
    created_by UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- ============================================================================
-- PERFORMANCE INDEXES
-- ============================================================================

-- Activity attendance indexes
CREATE INDEX IF NOT EXISTS idx_activity_attendance_user_id ON activity_attendance(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_attendance_activity_id ON activity_attendance(activity_id);
CREATE INDEX IF NOT EXISTS idx_activity_attendance_status ON activity_attendance(status);
CREATE INDEX IF NOT EXISTS idx_activity_attendance_activity_status ON activity_attendance(activity_id, status);

-- Music preference indexes
CREATE INDEX IF NOT EXISTS idx_artist_preferences_user_id ON artist_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_artist_preferences_artist_name ON artist_preferences(artist_name);
CREATE INDEX IF NOT EXISTS idx_artist_preferences_genre ON artist_preferences(genre);
CREATE INDEX IF NOT EXISTS idx_music_genre_preferences_user_id ON music_genre_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_music_genre_preferences_genre ON music_genre_preferences(genre);

-- External links indexes
CREATE INDEX IF NOT EXISTS idx_group_external_links_group_id ON group_external_links(group_id);
CREATE INDEX IF NOT EXISTS idx_group_external_links_platform ON group_external_links(platform);

-- ============================================================================
-- ROW LEVEL SECURITY POLICIES
-- ============================================================================

-- Enable RLS
ALTER TABLE activity_attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE artist_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE music_genre_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_external_links ENABLE ROW LEVEL SECURITY;

-- Activity attendance policies
CREATE POLICY "Users can view all activity attendance" 
    ON activity_attendance FOR SELECT 
    USING (true); -- Public visibility for community building

CREATE POLICY "Users can manage their own attendance" 
    ON activity_attendance FOR ALL 
    USING (auth.uid() = user_id);

-- Music preference policies  
CREATE POLICY "Users can view all music preferences" 
    ON artist_preferences FOR SELECT 
    USING (true); -- Public for matching

CREATE POLICY "Users can manage their own music preferences" 
    ON artist_preferences FOR ALL 
    USING (auth.uid() = user_id);

CREATE POLICY "Users can view all genre preferences" 
    ON music_genre_preferences FOR SELECT 
    USING (true);

CREATE POLICY "Users can manage their own genre preferences" 
    ON music_genre_preferences FOR ALL 
    USING (auth.uid() = user_id);

-- External links policies
CREATE POLICY "Group members can view external links" 
    ON group_external_links FOR SELECT 
    USING (group_id IN (
        SELECT group_id FROM group_members WHERE user_id = auth.uid()
    ));

CREATE POLICY "Group admins can manage external links" 
    ON group_external_links FOR ALL 
    USING (group_id IN (
        SELECT group_id FROM group_members 
        WHERE user_id = auth.uid() AND role IN ('admin', 'moderator')
    ));

-- ============================================================================
-- HELPER FUNCTIONS FOR ACTIVITY COORDINATION
-- ============================================================================

-- Function to get activity attendance counts
CREATE OR REPLACE FUNCTION get_activity_attendance_counts(activity_uuid UUID)
RETURNS TABLE(
    going_count BIGINT,
    interested_count BIGINT,
    maybe_count BIGINT,
    total_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) FILTER (WHERE status = 'going') as going_count,
        COUNT(*) FILTER (WHERE status = 'interested') as interested_count,
        COUNT(*) FILTER (WHERE status = 'maybe') as maybe_count,
        COUNT(*) as total_count
    FROM activity_attendance 
    WHERE activity_id = activity_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to find users with similar music taste
CREATE OR REPLACE FUNCTION find_music_buddies(target_user_id UUID, result_limit INTEGER DEFAULT 10)
RETURNS TABLE(
    user_id UUID,
    shared_artists_count BIGINT,
    shared_genres_count BIGINT,
    compatibility_score NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    WITH user_artists AS (
        SELECT artist_name FROM artist_preferences 
        WHERE user_id = target_user_id AND preference_level IN ('love', 'like')
    ),
    user_genres AS (
        SELECT genre FROM music_genre_preferences 
        WHERE user_id = target_user_id AND preference_level IN ('love', 'like')
    ),
    similar_users AS (
        SELECT 
            ap.user_id,
            COUNT(DISTINCT ap.artist_name) as shared_artists,
            COUNT(DISTINCT mgp.genre) as shared_genres
        FROM artist_preferences ap
        LEFT JOIN music_genre_preferences mgp ON ap.user_id = mgp.user_id
        WHERE ap.user_id != target_user_id
        AND (
            ap.artist_name IN (SELECT artist_name FROM user_artists)
            OR mgp.genre IN (SELECT genre FROM user_genres)
        )
        GROUP BY ap.user_id
        HAVING COUNT(DISTINCT ap.artist_name) > 0 OR COUNT(DISTINCT mgp.genre) > 0
    )
    SELECT 
        su.user_id,
        su.shared_artists,
        su.shared_genres,
        (su.shared_artists * 2 + su.shared_genres)::NUMERIC as compatibility_score
    FROM similar_users su
    ORDER BY compatibility_score DESC
    LIMIT result_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

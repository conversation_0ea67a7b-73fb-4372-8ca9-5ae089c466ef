/**
 * Supabase Edge Function: Server-Side Input Sanitization
 * 
 * This function provides server-side XSS protection by sanitizing
 * all user input before it reaches the database.
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// XSS protection patterns
const XSS_PATTERNS = [
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /javascript:/gi,
  /on\w+\s*=/gi,
  /<iframe\b[^>]*>/gi,
  /<object\b[^>]*>/gi,
  /<embed\b[^>]*>/gi,
  /<link\b[^>]*>/gi,
  /<meta\b[^>]*>/gi,
  /<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi,
  /expression\s*\(/gi,
  /vbscript:/gi,
  /data:text\/html/gi
];

// HTML entities to escape
const HTML_ENTITIES: Record<string, string> = {
  '&': '&amp;',
  '<': '&lt;',
  '>': '&gt;',
  '"': '&quot;',
  "'": '&#x27;',
  '/': '&#x2F;',
  '`': '&#x60;',
  '=': '&#x3D;'
};

/**
 * Sanitize input by removing XSS patterns and encoding HTML entities
 */
function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return input;
  
  let sanitized = input;
  
  // Remove dangerous XSS patterns
  XSS_PATTERNS.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '');
  });
  
  // Encode HTML entities
  sanitized = sanitized.replace(/[&<>"'`=\/]/g, (char) => {
    return HTML_ENTITIES[char] || char;
  });
  
  // Remove null bytes and control characters
  sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
  
  return sanitized.trim();
}

/**
 * Sanitize profile data specifically
 */
function sanitizeProfileData(data: any): any {
  const sanitized = { ...data };
  
  // Sanitize text fields
  if (sanitized.full_name) {
    sanitized.full_name = sanitizeInput(sanitized.full_name).substring(0, 100);
  }
  
  if (sanitized.username) {
    sanitized.username = sanitizeInput(sanitized.username).substring(0, 50);
  }
  
  if (sanitized.bio) {
    sanitized.bio = sanitizeInput(sanitized.bio).substring(0, 500);
  }
  
  if (sanitized.location) {
    sanitized.location = sanitizeInput(sanitized.location).substring(0, 100);
  }
  
  // Don't allow role changes through this function
  delete sanitized.role;
  
  return sanitized;
}

/**
 * Sanitize announcement data
 */
function sanitizeAnnouncementData(data: any): any {
  const sanitized = { ...data };
  
  if (sanitized.title) {
    sanitized.title = sanitizeInput(sanitized.title).substring(0, 200);
  }
  
  if (sanitized.content) {
    sanitized.content = sanitizeInput(sanitized.content).substring(0, 2000);
  }
  
  return sanitized;
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    })
  }

  try {
    const { action, data, table } = await req.json()
    
    console.log(`🧹 Sanitizing ${action} for table: ${table}`)
    
    let sanitizedData;
    
    // Apply table-specific sanitization
    switch (table) {
      case 'profiles':
        sanitizedData = sanitizeProfileData(data);
        break;
      case 'announcements':
        sanitizedData = sanitizeAnnouncementData(data);
        break;
      default:
        // Generic sanitization for other tables
        sanitizedData = {};
        Object.keys(data).forEach(key => {
          if (typeof data[key] === 'string') {
            sanitizedData[key] = sanitizeInput(data[key]);
          } else {
            sanitizedData[key] = data[key];
          }
        });
    }
    
    // Log sanitization results
    const originalKeys = Object.keys(data);
    const sanitizedKeys = Object.keys(sanitizedData);
    const changedFields = originalKeys.filter(key => 
      typeof data[key] === 'string' && data[key] !== sanitizedData[key]
    );
    
    if (changedFields.length > 0) {
      console.log(`🛡️ XSS protection applied to fields: ${changedFields.join(', ')}`);
    }
    
    return new Response(
      JSON.stringify({
        success: true,
        sanitizedData,
        changedFields,
        message: `Input sanitized for ${table} ${action}`
      }),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      },
    )
    
  } catch (error) {
    console.error('❌ Sanitization error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      },
    )
  }
})

/* To deploy this function:
 * 1. supabase functions deploy sanitize-input
 * 2. Set up database triggers to call this function
 * 3. Update client code to use sanitized data
 */

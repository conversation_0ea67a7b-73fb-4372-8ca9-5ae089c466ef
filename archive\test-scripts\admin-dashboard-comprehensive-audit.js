/**
 * Admin Dashboard Comprehensive Audit
 * 
 * This script performs a thorough audit of the admin dashboard functionality,
 * including database tables, frontend components, and integration completeness.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Admin Dashboard Comprehensive Audit');
console.log('=====================================');

// Test 1: Admin Database Tables Audit
async function auditAdminDatabaseTables() {
  console.log('🗄️ Test 1: Admin Database Tables Audit');
  console.log('--------------------------------------');
  
  const results = {
    adminTables: {},
    adminData: {},
    adminUsers: [],
    timestamp: new Date().toISOString()
  };
  
  // Core admin-related tables to audit
  const adminTables = [
    { name: 'profiles', adminField: 'role', description: 'User profiles with admin roles' },
    { name: 'announcements', adminField: null, description: 'Admin announcements system' },
    { name: 'festivals', adminField: null, description: 'Festival management' },
    { name: 'events', adminField: null, description: 'Event management' },
    { name: 'activities', adminField: null, description: 'Activity management' },
    { name: 'groups', adminField: 'creator_id', description: 'Group management' },
    { name: 'group_members', adminField: null, description: 'Group membership management' }
  ];
  
  for (const table of adminTables) {
    console.log(`📋 Auditing ${table.name} table...`);
    
    try {
      // Test table accessibility
      const { data, error, count } = await supabase
        .from(table.name)
        .select('*', { count: 'exact' })
        .limit(5);
      
      if (error) {
        results.adminTables[table.name] = {
          accessible: false,
          error: error.message,
          description: table.description
        };
        console.log(`❌ ${table.name}: ${error.message}`);
      } else {
        results.adminTables[table.name] = {
          accessible: true,
          recordCount: count,
          sampleData: data,
          description: table.description
        };
        console.log(`✅ ${table.name}: ${count} records, accessible`);
        
        // Store admin data for analysis
        results.adminData[table.name] = data;
      }
    } catch (err) {
      results.adminTables[table.name] = {
        accessible: false,
        error: err.message,
        description: table.description
      };
      console.log(`💥 ${table.name}: ${err.message}`);
    }
  }
  
  // Special audit: Admin users
  console.log('👑 Auditing admin users...');
  try {
    const { data: adminUsers, error } = await supabase
      .from('profiles')
      .select('id, email, full_name, username, role, created_at')
      .in('role', ['SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR']);
    
    if (!error && adminUsers) {
      results.adminUsers = adminUsers;
      console.log(`✅ Found ${adminUsers.length} admin users:`);
      adminUsers.forEach(user => {
        console.log(`   👤 ${user.email} (${user.role})`);
      });
    } else {
      console.log(`❌ Admin users query failed: ${error?.message}`);
    }
  } catch (err) {
    console.log(`💥 Admin users audit failed: ${err.message}`);
  }
  
  return results;
}

// Test 2: Announcements System Deep Dive
async function auditAnnouncementsSystem() {
  console.log('\n📢 Test 2: Announcements System Deep Dive');
  console.log('-----------------------------------------');
  
  const results = {
    announcementsTableExists: false,
    announcementTypes: [],
    sampleAnnouncements: [],
    announcementStats: {},
    timestamp: new Date().toISOString()
  };
  
  try {
    // Check announcements table structure and data
    const { data: announcements, error } = await supabase
      .from('announcements')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (error) {
      console.log(`❌ Announcements table error: ${error.message}`);
      results.error = error.message;
      return results;
    }
    
    results.announcementsTableExists = true;
    results.sampleAnnouncements = announcements;
    
    console.log(`✅ Announcements table accessible with ${announcements.length} recent records`);
    
    // Analyze announcement types
    const types = [...new Set(announcements.map(a => a.type).filter(Boolean))];
    results.announcementTypes = types;
    console.log(`📊 Announcement types found: ${types.join(', ') || 'None'}`);
    
    // Get announcement statistics
    const { data: stats, error: statsError } = await supabase
      .from('announcements')
      .select('type, priority, is_active', { count: 'exact' });
    
    if (!statsError && stats) {
      const typeStats = {};
      const priorityStats = {};
      const activeStats = { active: 0, inactive: 0 };
      
      stats.forEach(announcement => {
        // Type statistics
        const type = announcement.type || 'unknown';
        typeStats[type] = (typeStats[type] || 0) + 1;
        
        // Priority statistics
        const priority = announcement.priority || 'unknown';
        priorityStats[priority] = (priorityStats[priority] || 0) + 1;
        
        // Active statistics
        if (announcement.is_active) {
          activeStats.active++;
        } else {
          activeStats.inactive++;
        }
      });
      
      results.announcementStats = {
        byType: typeStats,
        byPriority: priorityStats,
        byStatus: activeStats,
        total: stats.length
      };
      
      console.log('📊 Announcement Statistics:');
      console.log(`   Total: ${stats.length}`);
      console.log(`   Active: ${activeStats.active}, Inactive: ${activeStats.inactive}`);
      console.log(`   By Type:`, typeStats);
      console.log(`   By Priority:`, priorityStats);
    }
    
    // Test announcement creation (admin function)
    console.log('🧪 Testing announcement creation...');
    try {
      const testAnnouncement = {
        title: 'Admin Audit Test Announcement',
        content: 'This is a test announcement created during admin audit.',
        type: 'info',
        priority: 'low',
        is_active: false,
        created_by: null // Will be set by RLS if admin
      };
      
      const { data: newAnnouncement, error: createError } = await supabase
        .from('announcements')
        .insert([testAnnouncement])
        .select()
        .single();
      
      if (createError) {
        console.log(`⚠️ Announcement creation test failed: ${createError.message}`);
        results.creationTest = { success: false, error: createError.message };
      } else {
        console.log(`✅ Announcement creation test successful`);
        results.creationTest = { success: true, announcementId: newAnnouncement.id };
        
        // Clean up test announcement
        await supabase
          .from('announcements')
          .delete()
          .eq('id', newAnnouncement.id);
        console.log(`🧹 Test announcement cleaned up`);
      }
    } catch (err) {
      console.log(`💥 Announcement creation test error: ${err.message}`);
      results.creationTest = { success: false, error: err.message };
    }
    
  } catch (error) {
    console.error('💥 Announcements system audit failed:', error);
    results.error = error.message;
  }
  
  return results;
}

// Test 3: Admin Functions and Permissions
async function auditAdminFunctions() {
  console.log('\n🛡️ Test 3: Admin Functions and Permissions');
  console.log('-------------------------------------------');
  
  const results = {
    adminFunctions: {},
    permissionTests: {},
    timestamp: new Date().toISOString()
  };
  
  // Test admin-related database functions
  const adminFunctions = [
    { name: 'is_admin', params: [], description: 'Check if current user is admin' },
    { name: 'change_user_role', params: [{ target_user_id: '00000000-0000-0000-0000-000000000000', new_role: 'USER' }], description: 'Change user role (should be restricted)' }
  ];
  
  for (const func of adminFunctions) {
    console.log(`⚙️ Testing ${func.name} function...`);
    
    try {
      const { data, error } = await supabase.rpc(func.name, func.params[0] || {});
      
      if (error) {
        results.adminFunctions[func.name] = {
          exists: true,
          working: false,
          error: error.message,
          description: func.description
        };
        
        if (func.name === 'change_user_role' && error.message.includes('Only SUPER_ADMIN')) {
          console.log(`✅ ${func.name}: Properly secured (${error.message})`);
          results.adminFunctions[func.name].working = true;
          results.adminFunctions[func.name].secured = true;
        } else {
          console.log(`❌ ${func.name}: ${error.message}`);
        }
      } else {
        results.adminFunctions[func.name] = {
          exists: true,
          working: true,
          result: data,
          description: func.description
        };
        console.log(`✅ ${func.name}: Working (result: ${data})`);
      }
    } catch (err) {
      results.adminFunctions[func.name] = {
        exists: false,
        error: err.message,
        description: func.description
      };
      console.log(`💥 ${func.name}: ${err.message}`);
    }
  }
  
  return results;
}

// Main execution
async function main() {
  try {
    console.log(`🕐 Started at: ${new Date().toISOString()}\n`);
    
    // Run all admin audits
    const databaseResults = await auditAdminDatabaseTables();
    const announcementsResults = await auditAnnouncementsSystem();
    const functionsResults = await auditAdminFunctions();
    
    // Compile comprehensive results
    const auditResults = {
      auditSuite: 'Admin Dashboard Comprehensive Audit',
      timestamp: new Date().toISOString(),
      databaseTables: databaseResults,
      announcementsSystem: announcementsResults,
      adminFunctions: functionsResults,
      overallAssessment: {
        databaseTablesWorking: true,
        announcementsSystemWorking: false,
        adminFunctionsWorking: false,
        criticalIssues: [],
        recommendations: []
      }
    };
    
    // Calculate overall assessment
    const tablesWorking = Object.values(databaseResults.adminTables).filter(t => t.accessible).length;
    const totalTables = Object.keys(databaseResults.adminTables).length;
    auditResults.overallAssessment.databaseTablesWorking = (tablesWorking / totalTables) > 0.8;
    
    auditResults.overallAssessment.announcementsSystemWorking = 
      announcementsResults.announcementsTableExists && 
      announcementsResults.creationTest?.success;
    
    const functionsWorking = Object.values(functionsResults.adminFunctions).filter(f => f.working).length;
    const totalFunctions = Object.keys(functionsResults.adminFunctions).length;
    auditResults.overallAssessment.adminFunctionsWorking = (functionsWorking / totalFunctions) > 0.5;
    
    // Generate recommendations
    if (!auditResults.overallAssessment.databaseTablesWorking) {
      auditResults.overallAssessment.criticalIssues.push('Database table accessibility issues');
      auditResults.overallAssessment.recommendations.push('Fix database table RLS policies and permissions');
    }
    
    if (!auditResults.overallAssessment.announcementsSystemWorking) {
      auditResults.overallAssessment.criticalIssues.push('Announcements system not fully functional');
      auditResults.overallAssessment.recommendations.push('Debug announcements creation and display system');
    }
    
    if (!auditResults.overallAssessment.adminFunctionsWorking) {
      auditResults.overallAssessment.criticalIssues.push('Admin functions not working properly');
      auditResults.overallAssessment.recommendations.push('Verify admin function implementations and permissions');
    }
    
    // Save results
    const resultsDir = 'admin-dashboard-audit-results';
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      `${resultsDir}/admin-audit-${Date.now()}.json`,
      JSON.stringify(auditResults, null, 2)
    );
    
    console.log('\n🎉 ADMIN DASHBOARD AUDIT SUMMARY');
    console.log('================================');
    
    console.log('\n🗄️ DATABASE TABLES:');
    Object.entries(databaseResults.adminTables).forEach(([table, status]) => {
      const statusText = status.accessible ? `✅ ${status.recordCount} records` : `❌ ${status.error}`;
      console.log(`   ${table}: ${statusText}`);
    });
    
    console.log('\n👑 ADMIN USERS:');
    if (databaseResults.adminUsers.length > 0) {
      databaseResults.adminUsers.forEach(user => {
        console.log(`   ✅ ${user.email} (${user.role})`);
      });
    } else {
      console.log('   ❌ No admin users found');
    }
    
    console.log('\n📢 ANNOUNCEMENTS SYSTEM:');
    console.log(`   Table Exists: ${announcementsResults.announcementsTableExists ? 'YES' : 'NO'}`);
    console.log(`   Creation Test: ${announcementsResults.creationTest?.success ? 'PASS' : 'FAIL'}`);
    console.log(`   Types Found: ${announcementsResults.announcementTypes.join(', ') || 'None'}`);
    
    console.log('\n🛡️ ADMIN FUNCTIONS:');
    Object.entries(functionsResults.adminFunctions).forEach(([func, status]) => {
      const statusText = status.working ? 'WORKING' : status.secured ? 'SECURED' : 'ISSUES';
      console.log(`   ${func}: ${statusText}`);
    });
    
    console.log('\n🎯 OVERALL ASSESSMENT:');
    console.log(`   Database Tables: ${auditResults.overallAssessment.databaseTablesWorking ? 'WORKING' : 'ISSUES'}`);
    console.log(`   Announcements: ${auditResults.overallAssessment.announcementsSystemWorking ? 'WORKING' : 'ISSUES'}`);
    console.log(`   Admin Functions: ${auditResults.overallAssessment.adminFunctionsWorking ? 'WORKING' : 'ISSUES'}`);
    
    if (auditResults.overallAssessment.criticalIssues.length > 0) {
      console.log('\n🚨 CRITICAL ISSUES:');
      auditResults.overallAssessment.criticalIssues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`);
      });
      
      console.log('\n🔧 RECOMMENDATIONS:');
      auditResults.overallAssessment.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
      });
    }
    
    console.log(`\n📁 Results saved to: ${resultsDir}/admin-audit-${Date.now()}.json`);
    
  } catch (error) {
    console.error('💥 Admin audit failed:', error);
  }
  
  process.exit(0);
}

main();

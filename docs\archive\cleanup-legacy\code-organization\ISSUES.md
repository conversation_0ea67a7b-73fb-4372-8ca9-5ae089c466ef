# Code Organization Issues

## 1. Inconsistent Project Structure

### Description
The project lacks a consistent and intuitive folder structure, with files organized in different ways across the codebase. This makes it difficult to locate files and understand the overall architecture.

### Examples
- Some features are organized by type (components, hooks, utils)
- Others are organized by feature (auth, profile, festivals)
- Utility functions are scattered across multiple locations
- No clear separation between UI components and business logic

### Impact
- Difficulty finding and navigating files
- Confusion for new developers
- Challenges in understanding component relationships
- Inefficient development workflow

## 2. Monolithic Components

### Description
Many components are too large and handle multiple responsibilities, including UI rendering, data fetching, state management, and business logic.

### Examples
```typescript
// Example of a monolithic component
function FestivalPage() {
  // Data fetching
  const [festival, setFestival] = useState(null);
  const [activities, setActivities] = useState([]);
  
  useEffect(() => {
    async function fetchData() {
      const festivalData = await supabase.from('festivals').select('*').eq('id', id).single();
      setFestival(festivalData.data);
      
      const activitiesData = await supabase.from('activities').select('*').eq('festival_id', id);
      setActivities(activitiesData.data);
    }
    fetchData();
  }, [id]);
  
  // Complex state management
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [searchQuery, setSearchQuery] = useState('');
  
  // Business logic for filtering and sorting
  const filteredActivities = activities
    .filter(activity => {
      if (filter === 'all') return true;
      return activity.type === filter;
    })
    .filter(activity => {
      if (!searchQuery) return true;
      return activity.name.toLowerCase().includes(searchQuery.toLowerCase());
    })
    .sort((a, b) => {
      if (sortBy === 'date') return new Date(a.start_time) - new Date(b.start_time);
      if (sortBy === 'name') return a.name.localeCompare(b.name);
      return 0;
    });
  
  // UI rendering with complex conditional logic
  return (
    <div>
      {/* 100+ lines of JSX with nested conditionals */}
    </div>
  );
}
```

### Impact
- Difficult to maintain and test
- Poor code reusability
- Performance issues
- Challenging to understand component behavior

## 3. Inconsistent State Management

### Description
The application uses multiple approaches to state management without clear guidelines on when to use each approach.

### Examples
- Some components use React's useState and useEffect
- Others use React Context for state sharing
- Some use custom hooks for state management
- No clear pattern for global vs. local state

### Impact
- Inconsistent data flow
- Difficulty tracking state changes
- Potential for state synchronization issues
- Challenges in debugging state-related problems

## 4. Duplicated Logic

### Description
Common functionality is duplicated across multiple components instead of being extracted into reusable utilities or hooks.

### Examples
```typescript
// Duplicated date formatting logic in multiple components
// In FestivalCard.tsx
const formattedDate = `${new Date(festival.start_date).toLocaleDateString()} - ${new Date(festival.end_date).toLocaleDateString()}`;

// In ActivityDetails.tsx
const formattedDate = `${new Date(activity.start_time).toLocaleDateString()} - ${new Date(activity.end_time).toLocaleDateString()}`;

// Duplicated form validation logic
// In ProfileForm.tsx
const validateEmail = (email) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
};

// In SignupForm.tsx
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};
```

### Impact
- Code bloat
- Inconsistent behavior when logic is updated in one place but not others
- Increased maintenance burden
- Higher likelihood of bugs

## 5. Lack of Component Composition

### Description
The codebase often uses large, monolithic components instead of composing smaller, focused components.

### Examples
```typescript
// Instead of breaking down into smaller components
function ProfilePage() {
  return (
    <div>
      <div className="profile-header">
        {/* 30+ lines of profile header JSX */}
      </div>
      
      <div className="profile-details">
        {/* 50+ lines of profile details JSX */}
      </div>
      
      <div className="user-festivals">
        {/* 40+ lines of festivals list JSX */}
      </div>
      
      <div className="user-activities">
        {/* 40+ lines of activities list JSX */}
      </div>
    </div>
  );
}
```

### Impact
- Reduced component reusability
- Difficulty understanding component purpose
- Challenges in testing individual parts
- Poor separation of concerns

## 6. Inconsistent File Naming

### Description
The project uses inconsistent file naming conventions, making it difficult to locate and identify files.

### Examples
- Mixed use of PascalCase, camelCase, and kebab-case for file names
- Inconsistent file extensions (.tsx, .jsx, .ts, .js)
- Unclear naming patterns for different file types
- No standard for index files vs. named files

### Impact
- Difficulty finding files
- Confusion about file purpose
- Challenges in maintaining consistent imports
- Poor developer experience

## 7. Excessive File Sizes

### Description
Many files exceed 500 lines, making them difficult to understand and maintain.

### Examples
- Complex components with 500+ lines of code
- Utility files with dozens of unrelated functions
- Context providers handling multiple concerns
- Pages with inline components and business logic

### Impact
- Reduced code readability
- Difficulty understanding file purpose
- Challenges in code navigation
- Increased likelihood of merge conflicts
/**
 * Attendance Buttons Component
 * 
 * Interactive buttons for users to mark their attendance status for activities.
 * Follows Festival Family's design system and accessibility standards.
 * 
 * @module AttendanceButtons
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { UnifiedBadge } from '@/components/design-system'
import { 
  CheckCircle, 
  Heart, 
  HelpCircle, 
  X,
  Users,
  Loader2
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { AttendanceStatus } from '@/lib/supabase/activity-coordination/types'

interface AttendanceButtonsProps {
  activityId?: string // Optional for backward compatibility
  currentStatus?: AttendanceStatus
  attendanceCounts?: {
    going: number
    interested: number
    maybe: number
    not_going?: number
    total: number
  }
  counts?: {  // Alias for backward compatibility
    going: number
    interested: number
    maybe: number
    not_going?: number
    total: number
  }
  onStatusChange: (status: AttendanceStatus) => void
  onRemove?: () => void
  isLoading?: boolean
  disabled?: boolean
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'compact' | 'detailed'
  showCounts?: boolean
  className?: string
}

const statusConfig = {
  going: {
    icon: CheckCircle,
    label: 'Going',
    color: 'bg-festival-success hover:bg-festival-success/80',
    textColor: 'text-festival-success',
    bgColor: 'bg-festival-success/10',
    borderColor: 'border-festival-success/20'
  },
  interested: {
    icon: Heart,
    label: 'Interested',
    color: 'bg-primary hover:bg-primary/80',
    textColor: 'text-primary',
    bgColor: 'bg-primary/10',
    borderColor: 'border-primary/20'
  },
  maybe: {
    icon: HelpCircle,
    label: 'Maybe',
    color: 'bg-festival-warning hover:bg-festival-warning/80',
    textColor: 'text-festival-warning',
    bgColor: 'bg-festival-warning/10',
    borderColor: 'border-festival-warning/20'
  },
  not_going: {
    icon: X,
    label: 'Not Going',
    color: 'bg-muted hover:bg-muted/80',
    textColor: 'text-muted-foreground',
    bgColor: 'bg-muted/10',
    borderColor: 'border-muted'
  }
} as const

export function AttendanceButtons({
  currentStatus,
  attendanceCounts,
  onStatusChange,
  onRemove,
  isLoading = false,
  disabled = false,
  size = 'md',
  variant = 'default',
  showCounts = true,
  className
}: AttendanceButtonsProps) {
  
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  }

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  }

  if (variant === 'compact') {
    return (
      <div className={cn('flex items-center gap-1', className)}>
        {(['going', 'interested', 'maybe'] as const).map((status) => {
          const config = statusConfig[status]
          const Icon = config.icon
          const isActive = currentStatus === status
          const count = attendanceCounts?.[status] || 0

          return (
            <Button
              key={status}
              variant={isActive ? 'default' : 'outline'}
              size="sm"
              onClick={() => onStatusChange(status)}
              disabled={disabled || isLoading}
              className={cn(
                'relative',
                isActive && config.color,
                sizeClasses[size]
              )}
            >
              {isLoading && currentStatus === status ? (
                <Loader2 className={cn('animate-spin', iconSizes[size])} />
              ) : (
                <Icon className={iconSizes[size]} />
              )}
              {showCounts && count > 0 && (
                <UnifiedBadge
                  variant="secondary"
                  size="sm"
                  className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs"
                >
                  {count}
                </UnifiedBadge>
              )}
            </Button>
          )
        })}
        
        {currentStatus && onRemove && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onRemove}
            disabled={disabled || isLoading}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className={iconSizes[size]} />
          </Button>
        )}
      </div>
    )
  }

  if (variant === 'detailed') {
    return (
      <div className={cn('space-y-2', className)}>
        <div className="grid grid-cols-2 gap-2">
          {(['going', 'interested', 'maybe', 'not_going'] as const).map((status) => {
            const config = statusConfig[status]
            const Icon = config.icon
            const isActive = currentStatus === status
            const count = attendanceCounts?.[status] || 0

            return (
              <Button
                key={status}
                variant={isActive ? 'default' : 'outline'}
                onClick={() => onStatusChange(status)}
                disabled={disabled || isLoading}
                className={cn(
                  'flex items-center justify-between',
                  isActive && config.color,
                  sizeClasses[size]
                )}
              >
                <div className="flex items-center gap-2">
                  {isLoading && currentStatus === status ? (
                    <Loader2 className={cn('animate-spin', iconSizes[size])} />
                  ) : (
                    <Icon className={iconSizes[size]} />
                  )}
                  <span>{config.label}</span>
                </div>
                {showCounts && (
                  <UnifiedBadge variant="secondary" size="sm" className="ml-2">
                    {count}
                  </UnifiedBadge>
                )}
              </Button>
            )
          })}
        </div>
        
        {attendanceCounts && showCounts && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Users className="h-4 w-4" />
            <span>{attendanceCounts.total} total interested</span>
          </div>
        )}
      </div>
    )
  }

  // Default variant
  return (
    <div className={cn('flex items-center gap-2', className)}>
      {(['going', 'interested', 'maybe'] as const).map((status) => {
        const config = statusConfig[status]
        const Icon = config.icon
        const isActive = currentStatus === status
        const count = attendanceCounts?.[status] || 0

        return (
          <Button
            key={status}
            variant={isActive ? 'default' : 'outline'}
            onClick={() => onStatusChange(status)}
            disabled={disabled || isLoading}
            className={cn(
              'flex items-center gap-2',
              isActive && config.color,
              sizeClasses[size]
            )}
          >
            {isLoading && currentStatus === status ? (
              <Loader2 className={cn('animate-spin', iconSizes[size])} />
            ) : (
              <Icon className={iconSizes[size]} />
            )}
            <span>{config.label}</span>
            {showCounts && count > 0 && (
              <UnifiedBadge variant="secondary" size="sm" className="ml-1">
                {count}
              </UnifiedBadge>
            )}
          </Button>
        )
      })}
      
      {currentStatus && onRemove && (
        <Button
          variant="ghost"
          onClick={onRemove}
          disabled={disabled || isLoading}
          className="text-gray-500 hover:text-gray-700"
        >
          <X className={iconSizes[size]} />
          <span className="ml-1">Remove</span>
        </Button>
      )}
    </div>
  )
}

/**
 * Attendance Status Indicator - Shows current status without interaction
 */
interface AttendanceStatusProps {
  status: AttendanceStatus
  size?: 'sm' | 'md' | 'lg'
  showLabel?: boolean
  className?: string
}

export function AttendanceStatus({
  status,
  size = 'md',
  showLabel = true,
  className
}: AttendanceStatusProps) {
  const config = statusConfig[status]
  const Icon = config.icon

  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  }

  const textSizes = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  }

  return (
    <div className={cn(
      'flex items-center gap-2',
      config.textColor,
      className
    )}>
      <Icon className={sizeClasses[size]} />
      {showLabel && (
        <span className={textSizes[size]}>{config.label}</span>
      )}
    </div>
  )
}

/**
 * Attendance Summary - Shows total counts
 */
interface AttendanceSummaryProps {
  counts: {
    going: number
    interested: number
    maybe: number
    total: number
  }
  size?: 'sm' | 'md' | 'lg'
  variant?: 'horizontal' | 'vertical'
  className?: string
}

export function AttendanceSummary({
  counts,
  size = 'md',
  variant = 'horizontal',
  className
}: AttendanceSummaryProps) {
  const textSizes = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  }

  const items = [
    { status: 'going' as const, count: counts.going },
    { status: 'interested' as const, count: counts.interested },
    { status: 'maybe' as const, count: counts.maybe }
  ].filter(item => item.count > 0)

  if (variant === 'vertical') {
    return (
      <div className={cn('space-y-1', className)}>
        {items.map(({ status, count }) => {
          const config = statusConfig[status]
          const Icon = config.icon
          
          return (
            <div key={status} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Icon className="h-3 w-3" />
                <span className={textSizes[size]}>{config.label}</span>
              </div>
              <UnifiedBadge variant="secondary" size={size === 'lg' ? 'md' : 'sm'} className={textSizes[size]}>
                {count}
              </UnifiedBadge>
            </div>
          )
        })}
        <div className="flex items-center justify-between pt-1 border-t">
          <span className={cn('font-medium', textSizes[size])}>Total</span>
          <UnifiedBadge variant="secondary" size={size === 'lg' ? 'md' : 'sm'} className={textSizes[size]}>
            {counts.total}
          </UnifiedBadge>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('flex items-center gap-4', className)}>
      {items.map(({ status, count }) => {
        const config = statusConfig[status]
        const Icon = config.icon
        
        return (
          <div key={status} className="flex items-center gap-1">
            <Icon className="h-3 w-3" />
            <span className={textSizes[size]}>{count}</span>
          </div>
        )
      })}
      <div className="flex items-center gap-1 text-gray-600">
        <Users className="h-3 w-3" />
        <span className={textSizes[size]}>{counts.total}</span>
      </div>
    </div>
  )
}

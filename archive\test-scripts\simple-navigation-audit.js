#!/usr/bin/env node

/**
 * Simplified Navigation System Assessment
 * 
 * This script performs focused testing of navigation components:
 * - Desktop navigation presence and functionality
 * - Mobile navigation presence and functionality
 * - Responsive behavior testing
 * - Navigation completeness scoring
 */

import { chromium } from 'playwright';
import { promises as fs } from 'fs';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'simple-navigation-evidence';

// Test user credentials
const TEST_USER = {
  email: `simple.nav.${Date.now()}@festivalfamily.test`,
  password: 'TestPassword123!',
  fullName: 'Simple Nav Test User'
};

async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

async function authenticateUser(page) {
  console.log('\n🔐 Setting up authenticated session');
  console.log('==================================');
  
  await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(2000);
  
  // Sign up new user
  const signUpTab = await page.$('text=Sign Up');
  if (signUpTab) {
    await signUpTab.click();
    await page.waitForTimeout(1000);
  }
  
  const emailInput = await page.$('input[type="email"]');
  const passwordInput = await page.$('input[type="password"]');
  
  if (emailInput && passwordInput) {
    await emailInput.fill(TEST_USER.email);
    await passwordInput.fill(TEST_USER.password);
    
    const submitButton = await page.$('button[type="submit"]');
    if (submitButton) {
      await submitButton.click();
      await page.waitForTimeout(3000);
    }
  }
  
  console.log(`✅ Authenticated as: ${TEST_USER.email}`);
}

async function testNavigationAtViewport(page, viewportName, width, height) {
  console.log(`\n${viewportName.toUpperCase()} NAVIGATION TEST`);
  console.log('='.repeat(viewportName.length + 17));
  
  // Set viewport
  await page.setViewportSize({ width, height });
  await page.goto(APP_URL, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  // Capture navigation
  const screenshotName = `${viewportName.toLowerCase()}-navigation.png`;
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/${screenshotName}`,
    fullPage: true 
  });
  
  // Check for navigation elements
  const navElement = await page.$('nav');
  const allLinks = await page.$$('a');
  const allButtons = await page.$$('button');
  
  // Check for specific navigation patterns
  const hasTopNav = await page.$('nav') !== null;
  const hasBottomNav = await page.$('[class*="bottom"]') !== null || await page.$('[class*="fixed"]') !== null;
  const hasSidebar = await page.$('[class*="sidebar"]') !== null || await page.$('[class*="drawer"]') !== null;
  
  // Check for key navigation links
  const homeLink = await page.$('a[href="/"]');
  const activitiesLink = await page.$('a[href="/activities"]');
  const famhubLink = await page.$('a[href="/famhub"]');
  const discoverLink = await page.$('a[href="/discover"]');
  const profileLink = await page.$('a[href="/profile"]');
  
  // Test one navigation link
  let navigationWorking = false;
  if (profileLink) {
    console.log('🔄 Testing Profile navigation...');
    try {
      await profileLink.click();
      await page.waitForTimeout(2000);
      const currentUrl = page.url();
      navigationWorking = currentUrl.includes('/profile') || currentUrl.includes('/auth');
      console.log(`   Result: ${currentUrl}`);
    } catch (error) {
      console.log(`   Error: ${error.message}`);
    }
  }
  
  // Get page content for analysis
  const textContent = await page.textContent('body');
  const wordCount = textContent.split(/\s+/).length;
  
  console.log('\n📊 NAVIGATION ANALYSIS:');
  console.log(`🧭 Navigation Element: ${navElement ? '✅ Present' : '❌ Missing'}`);
  console.log(`🔗 Total Links: ${allLinks.length}`);
  console.log(`🔘 Total Buttons: ${allButtons.length}`);
  console.log(`📱 Top Navigation: ${hasTopNav ? '✅ Present' : '❌ Missing'}`);
  console.log(`📱 Bottom Navigation: ${hasBottomNav ? '✅ Present' : '❌ Missing'}`);
  console.log(`📱 Sidebar Navigation: ${hasSidebar ? '✅ Present' : '❌ Missing'}`);
  
  console.log('\n🔗 KEY NAVIGATION LINKS:');
  console.log(`🏠 Home Link: ${homeLink ? '✅ Present' : '❌ Missing'}`);
  console.log(`📅 Activities Link: ${activitiesLink ? '✅ Present' : '❌ Missing'}`);
  console.log(`👥 FamHub Link: ${famhubLink ? '✅ Present' : '❌ Missing'}`);
  console.log(`🔍 Discover Link: ${discoverLink ? '✅ Present' : '❌ Missing'}`);
  console.log(`👤 Profile Link: ${profileLink ? '✅ Present' : '❌ Missing'}`);
  
  console.log('\n🔄 FUNCTIONALITY TEST:');
  console.log(`🎯 Navigation Working: ${navigationWorking ? '✅ Functional' : '❌ Issues'}`);
  console.log(`📄 Content Words: ${wordCount}`);
  
  // Calculate navigation score
  const navigationPresence = hasTopNav || hasBottomNav || hasSidebar ? 100 : 0;
  const keyLinksPresent = [homeLink, activitiesLink, famhubLink, discoverLink, profileLink].filter(Boolean).length;
  const keyLinksScore = (keyLinksPresent / 5) * 100;
  const functionalityScore = navigationWorking ? 100 : 0;
  const overallScore = (navigationPresence + keyLinksScore + functionalityScore) / 3;
  
  console.log(`\n📊 ${viewportName.toUpperCase()} NAVIGATION SCORE: ${overallScore.toFixed(1)}%`);
  console.log(`   - Presence: ${navigationPresence}%`);
  console.log(`   - Key Links: ${keyLinksScore.toFixed(1)}%`);
  console.log(`   - Functionality: ${functionalityScore}%`);
  
  return {
    viewport: viewportName,
    dimensions: { width, height },
    navigation: {
      present: !!navElement,
      totalLinks: allLinks.length,
      totalButtons: allButtons.length,
      hasTopNav,
      hasBottomNav,
      hasSidebar
    },
    keyLinks: {
      home: !!homeLink,
      activities: !!activitiesLink,
      famhub: !!famhubLink,
      discover: !!discoverLink,
      profile: !!profileLink
    },
    functionality: {
      navigationWorking,
      wordCount
    },
    scores: {
      presence: navigationPresence,
      keyLinks: keyLinksScore,
      functionality: functionalityScore,
      overall: overallScore
    },
    screenshot: screenshotName
  };
}

async function runSimpleNavigationAudit() {
  console.log('🧭 SIMPLE NAVIGATION SYSTEM ASSESSMENT');
  console.log('======================================');
  console.log(`🕐 Start Time: ${new Date().toISOString()}`);
  
  await ensureEvidenceDir();
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Authenticate user
    await authenticateUser(page);
    
    const results = {};
    
    // Test Desktop Navigation (1280x720)
    results.desktop = await testNavigationAtViewport(page, 'Desktop', 1280, 720);
    
    // Test Tablet Navigation (768x1024)
    results.tablet = await testNavigationAtViewport(page, 'Tablet', 768, 1024);
    
    // Test Mobile Navigation (375x667)
    results.mobile = await testNavigationAtViewport(page, 'Mobile', 375, 667);
    
    // Calculate overall navigation system score
    const scores = Object.values(results).map(r => r.scores.overall);
    const overallScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    
    // Identify critical issues
    const criticalIssues = [];
    Object.values(results).forEach(result => {
      if (!result.navigation.present) {
        criticalIssues.push(`${result.viewport}: No navigation elements found`);
      }
      if (result.scores.keyLinks < 50) {
        criticalIssues.push(`${result.viewport}: Missing key navigation links`);
      }
      if (!result.functionality.navigationWorking) {
        criticalIssues.push(`${result.viewport}: Navigation functionality broken`);
      }
    });
    
    // Save comprehensive results
    const evidence = {
      timestamp: new Date().toISOString(),
      testUser: { email: TEST_USER.email },
      navigationResults: results,
      summary: {
        overallScore: parseFloat(overallScore.toFixed(1)),
        desktopScore: results.desktop.scores.overall,
        tabletScore: results.tablet.scores.overall,
        mobileScore: results.mobile.scores.overall,
        criticalIssues,
        screenshots: Object.values(results).map(r => r.screenshot)
      }
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/simple-navigation-audit-results.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    console.log('\n📊 NAVIGATION SYSTEM AUDIT SUMMARY');
    console.log('==================================');
    console.log(`🖥️ Desktop Score: ${evidence.summary.desktopScore.toFixed(1)}%`);
    console.log(`📱 Tablet Score: ${evidence.summary.tabletScore.toFixed(1)}%`);
    console.log(`📱 Mobile Score: ${evidence.summary.mobileScore.toFixed(1)}%`);
    console.log(`🎯 Overall Score: ${evidence.summary.overallScore}%`);
    console.log(`🚨 Critical Issues: ${evidence.summary.criticalIssues.length}`);
    evidence.summary.criticalIssues.forEach(issue => console.log(`   - ${issue}`));
    console.log(`📸 Screenshots: ${evidence.summary.screenshots.length}`);
    console.log(`📁 Evidence: ${EVIDENCE_DIR}/`);
    
    return evidence;
    
  } catch (error) {
    console.error('\n💥 Simple navigation audit failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

// Run the simple navigation audit
runSimpleNavigationAudit()
  .then(() => {
    console.log('\n✅ Simple navigation audit completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Simple navigation audit failed:', error);
    process.exit(1);
  });

{"testName": "User Experience Quality Assessment", "timestamp": "2025-05-29T23:17:02.886Z", "overallUXScore": "40.0", "totalChecks": 20, "passedChecks": 8, "categoryScores": {"visualDesign": {"passed": 2, "total": 5, "percentage": "40.0"}, "interactivity": {"passed": 1, "total": 5, "percentage": "20.0"}, "accessibility": {"passed": 0, "total": 5, "percentage": "0.0"}, "modernStandards": {"passed": 5, "total": 5, "percentage": "100.0"}}, "readinessAssessment": {"score": 40, "level": "Significant Development Required", "competitiveReadiness": "Below Market Standards"}, "uxQualityChecks": {"visualDesign": {"hasCSS": false, "hasCustomStyling": false, "hasImages": false, "hasIcons": true, "hasColors": true}, "interactivity": {"hasButtons": false, "hasLinks": false, "hasForms": false, "hasInputs": false, "hasJavaScript": true}, "accessibility": {"hasAltText": false, "hasLabels": false, "hasHeadings": false, "hasSemanticHTML": false, "hasAriaLabels": false}, "modernStandards": {"hasViewportMeta": true, "hasCharsetMeta": true, "hasTitle": true, "hasDescription": true, "hasModernHTML": true}}}
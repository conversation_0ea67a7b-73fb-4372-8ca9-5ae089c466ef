<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Festival Family Admin Dashboard Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .test-section.error {
            border-left-color: #f44336;
        }
        .test-section.warning {
            border-left-color: #ff9800;
        }
        .result {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .admin-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .admin-link {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #555;
            transition: all 0.3s;
        }
        .admin-link:hover {
            border-color: #4CAF50;
            background: #3a3a3a;
        }
        .admin-link a {
            color: #fff;
            text-decoration: none;
            font-weight: bold;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .status-item {
            background: #333;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .status-item.working {
            border: 2px solid #4CAF50;
        }
        .status-item.broken {
            border: 2px solid #f44336;
        }
        .status-item.unknown {
            border: 2px solid #ff9800;
        }
    </style>
</head>
<body>
    <h1>🎛️ Festival Family Admin Dashboard Test</h1>
    <p>This page tests all admin dashboard functionality and provides direct links to admin sections.</p>

    <div class="test-section">
        <h2>🔗 Admin Dashboard Links</h2>
        <p>Click these links to test each admin section directly:</p>
        <div class="admin-links">
            <div class="admin-link">
                <a href="http://localhost:5173/admin" target="_blank">📊 Admin Dashboard</a>
            </div>
            <div class="admin-link">
                <a href="http://localhost:5173/admin/content" target="_blank">📝 Content Management</a>
            </div>
            <div class="admin-link">
                <a href="http://localhost:5173/admin/emergency" target="_blank">🚨 Emergency Management</a>
            </div>
            <div class="admin-link">
                <a href="http://localhost:5173/admin/announcements" target="_blank">📢 Announcements</a>
            </div>
            <div class="admin-link">
                <a href="http://localhost:5173/admin/tips" target="_blank">💡 Tips Management</a>
            </div>
            <div class="admin-link">
                <a href="http://localhost:5173/admin/faqs" target="_blank">❓ FAQ Management</a>
            </div>
            <div class="admin-link">
                <a href="http://localhost:5173/admin/users" target="_blank">👥 User Management</a>
            </div>
            <div class="admin-link">
                <a href="http://localhost:5173/admin/festivals" target="_blank">🎪 Festival Management</a>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔐 Authentication Test</h2>
        <button onclick="testAuthentication()">Test Admin Authentication</button>
        <div id="auth-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📊 Admin Functions Status</h2>
        <button onclick="testAdminFunctions()">Test All Admin Functions</button>
        <div id="admin-functions-result" class="result"></div>
        <div id="admin-functions-grid" class="status-grid"></div>
    </div>

    <div class="test-section">
        <h2>📝 Content Management Test</h2>
        <button onclick="testContentManagement()">Test Content CRUD</button>
        <div id="content-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🚨 Emergency Management Test</h2>
        <button onclick="testEmergencyManagement()">Test Emergency CRUD</button>
        <div id="emergency-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📢 Announcements Test</h2>
        <button onclick="testAnnouncements()">Test Announcements CRUD</button>
        <div id="announcements-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>💡 Tips & FAQ Test</h2>
        <button onclick="testTipsAndFAQ()">Test Tips & FAQ CRUD</button>
        <div id="tips-faq-result" class="result"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // Initialize Supabase client
        const supabaseUrl = 'https://ealstndyhwjwipzlrxmg.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVhbHN0bmR5aHdqd2lwemxyeG1nIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzcxOTYyNjcsImV4cCI6MjA1Mjc3MjI2N30.cmIkfiBKJbnZ2VN6MUrfI1kF_0J2s8frDtJwzn_dP4k';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        // Test Authentication
        async function testAuthentication() {
            const resultDiv = document.getElementById('auth-result');
            resultDiv.innerHTML = '🔄 Testing authentication...';
            
            try {
                // Check current session
                const { data: { session }, error: sessionError } = await supabase.auth.getSession();
                
                if (sessionError) {
                    resultDiv.innerHTML = `❌ Session error: ${sessionError.message}`;
                    return;
                }
                
                if (session) {
                    resultDiv.innerHTML = `✅ User authenticated: ${session.user.email}`;
                    
                    // Test admin status
                    const { data: adminResult, error: adminError } = await supabase.rpc('is_admin');
                    
                    if (!adminError) {
                        resultDiv.innerHTML += `\n✅ Admin check: ${adminResult ? 'IS ADMIN' : 'NOT ADMIN'}`;
                    } else {
                        resultDiv.innerHTML += `\n❌ Admin check failed: ${adminError.message}`;
                    }
                } else {
                    resultDiv.innerHTML = '⚠️ No active session. Please sign in to test admin functionality.';
                    resultDiv.innerHTML += '\n\n🔗 Sign in at: http://localhost:5173/auth';
                }
            } catch (err) {
                resultDiv.innerHTML = `💥 Authentication test error: ${err.message}`;
            }
        }

        // Test Admin Functions
        async function testAdminFunctions() {
            const resultDiv = document.getElementById('admin-functions-result');
            const gridDiv = document.getElementById('admin-functions-grid');
            resultDiv.innerHTML = '🔄 Testing admin functions...';
            gridDiv.innerHTML = '';
            
            const functions = [
                'is_admin',
                'is_super_admin', 
                'is_content_admin',
                'can_manage_groups'
            ];
            
            const results = [];
            
            for (const func of functions) {
                try {
                    const { data, error } = await supabase.rpc(func);
                    
                    const statusItem = document.createElement('div');
                    statusItem.className = `status-item ${error ? 'broken' : 'working'}`;
                    statusItem.innerHTML = `
                        <strong>${func}</strong><br>
                        ${error ? '❌ Error' : `✅ ${data}`}
                    `;
                    gridDiv.appendChild(statusItem);
                    
                    if (error) {
                        results.push(`❌ ${func}: ${error.message}`);
                    } else {
                        results.push(`✅ ${func}: Working (result: ${data})`);
                    }
                } catch (err) {
                    const statusItem = document.createElement('div');
                    statusItem.className = 'status-item broken';
                    statusItem.innerHTML = `
                        <strong>${func}</strong><br>
                        💥 Exception
                    `;
                    gridDiv.appendChild(statusItem);
                    
                    results.push(`💥 ${func}: ${err.message}`);
                }
            }
            
            resultDiv.innerHTML = results.join('\n');
        }

        // Test Content Management
        async function testContentManagement() {
            const resultDiv = document.getElementById('content-result');
            resultDiv.innerHTML = '🔄 Testing content management...';
            
            const results = [];
            
            try {
                // Test READ
                const { data: content, error: readError } = await supabase
                    .from('content_management')
                    .select('*')
                    .limit(5);
                
                if (!readError) {
                    results.push(`✅ Content READ: Working (${content?.length || 0} items)`);
                    
                    if (content && content.length > 0) {
                        results.push(`📋 Sample content types: ${[...new Set(content.map(c => c.content_type))].join(', ')}`);
                    }
                    
                    // Test CREATE (if admin)
                    const testContent = {
                        content_key: `test_${Date.now()}`,
                        content_type: 'ui_text',
                        title: 'Test Content',
                        content: 'This is a test content item.',
                        is_active: false
                    };
                    
                    const { data: created, error: createError } = await supabase
                        .from('content_management')
                        .insert([testContent])
                        .select()
                        .single();
                    
                    if (!createError && created) {
                        results.push('✅ Content CREATE: Working');
                        
                        // Test UPDATE
                        const { error: updateError } = await supabase
                            .from('content_management')
                            .update({ title: 'Updated Test Content' })
                            .eq('id', created.id);
                        
                        if (!updateError) {
                            results.push('✅ Content UPDATE: Working');
                        }
                        
                        // Test DELETE
                        const { error: deleteError } = await supabase
                            .from('content_management')
                            .delete()
                            .eq('id', created.id);
                        
                        if (!deleteError) {
                            results.push('✅ Content DELETE: Working');
                        }
                    } else {
                        results.push(`⚠️ Content CREATE: ${createError?.message || 'Permission denied (expected for non-admin)'}`);
                    }
                } else {
                    results.push(`❌ Content READ: ${readError.message}`);
                }
            } catch (err) {
                results.push(`💥 Content management test: ${err.message}`);
            }
            
            resultDiv.innerHTML = results.join('\n');
        }

        // Test Emergency Management
        async function testEmergencyManagement() {
            const resultDiv = document.getElementById('emergency-result');
            resultDiv.innerHTML = '🔄 Testing emergency management...';
            
            const results = [];
            
            try {
                // Test emergency contacts
                const { data: contacts, error: contactsError } = await supabase
                    .from('emergency_contacts')
                    .select('*')
                    .limit(3);
                
                if (!contactsError) {
                    results.push(`✅ Emergency contacts READ: Working (${contacts?.length || 0} contacts)`);
                } else {
                    results.push(`❌ Emergency contacts: ${contactsError.message}`);
                }
                
                // Test safety information
                const { data: safety, error: safetyError } = await supabase
                    .from('safety_information')
                    .select('*')
                    .limit(3);
                
                if (!safetyError) {
                    results.push(`✅ Safety information read: Working (${safety?.length || 0} items)`);
                } else {
                    results.push(`❌ Safety information: ${safetyError.message}`);
                }
            } catch (err) {
                results.push(`💥 Emergency management test: ${err.message}`);
            }
            
            resultDiv.innerHTML = results.join('\n');
        }

        // Test Announcements
        async function testAnnouncements() {
            const resultDiv = document.getElementById('announcements-result');
            resultDiv.innerHTML = '🔄 Testing announcements...';
            
            const results = [];
            
            try {
                const { data: announcements, error: readError } = await supabase
                    .from('announcements')
                    .select('*')
                    .limit(5);
                
                if (!readError) {
                    results.push(`✅ Announcements read: Working (${announcements?.length || 0} announcements)`);
                    
                    if (announcements && announcements.length > 0) {
                        const types = [...new Set(announcements.map(a => a.type))];
                        results.push(`📋 Announcement types: ${types.join(', ')}`);
                    }
                } else {
                    results.push(`❌ Announcements read: ${readError.message}`);
                }
            } catch (err) {
                results.push(`💥 Announcements test: ${err.message}`);
            }
            
            resultDiv.innerHTML = results.join('\n');
        }

        // Test Tips and FAQ
        async function testTipsAndFAQ() {
            const resultDiv = document.getElementById('tips-faq-result');
            resultDiv.innerHTML = '🔄 Testing tips and FAQ...';
            
            const results = [];
            
            try {
                // Test tips
                const { data: tips, error: tipsError } = await supabase
                    .from('tips')
                    .select('*')
                    .limit(5);
                
                if (!tipsError) {
                    results.push(`✅ Tips read: Working (${tips?.length || 0} tips)`);
                } else {
                    results.push(`❌ Tips read: ${tipsError.message}`);
                }
                
                // Test FAQ
                const { data: faqs, error: faqsError } = await supabase
                    .from('faqs')
                    .select('*')
                    .limit(5);
                
                if (!faqsError) {
                    results.push(`✅ FAQ read: Working (${faqs?.length || 0} FAQs)`);
                } else {
                    results.push(`❌ FAQ read: ${faqsError.message}`);
                }
            } catch (err) {
                results.push(`💥 Tips and FAQ test: ${err.message}`);
            }
            
            resultDiv.innerHTML = results.join('\n');
        }

        // Auto-run authentication test on page load
        window.addEventListener('load', () => {
            testAuthentication();
        });
    </script>
</body>
</html>

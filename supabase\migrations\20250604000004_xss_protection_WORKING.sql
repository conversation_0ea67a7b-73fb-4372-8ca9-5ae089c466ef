-- WORKING XSS Protection Implementation
-- This migration adds <PERSON><PERSON><PERSON> the XSS protection function first
-- No triggers initially to avoid NEW/OLD issues

-- Create XSS protection function
CREATE OR REPLACE FUNCTION sanitize_xss_input(input_text TEXT)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
  sanitized TEXT;
BEGIN
  -- Return empty string if input is null
  IF input_text IS NULL THEN
    RETURN '';
  END IF;
  
  sanitized := input_text;
  
  -- Remove script tags
  sanitized := regexp_replace(sanitized, '<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>', '', 'gi');
  
  -- Remove javascript: protocols
  sanitized := regexp_replace(sanitized, 'javascript:', '', 'gi');
  
  -- Remove on* event handlers
  sanitized := regexp_replace(sanitized, 'on\w+\s*=', '', 'gi');
  
  -- Remove iframe tags
  sanitized := regexp_replace(sanitized, '<iframe\b[^>]*>', '', 'gi');
  
  -- Remove object tags
  sanitized := regexp_replace(sanitized, '<object\b[^>]*>', '', 'gi');
  
  -- Remove embed tags
  sanitized := regexp_replace(sanitized, '<embed\b[^>]*>', '', 'gi');
  
  -- Remove link tags
  sanitized := regexp_replace(sanitized, '<link\b[^>]*>', '', 'gi');
  
  -- Remove meta tags
  sanitized := regexp_replace(sanitized, '<meta\b[^>]*>', '', 'gi');
  
  -- Remove style tags
  sanitized := regexp_replace(sanitized, '<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>', '', 'gi');
  
  -- Remove expression() CSS
  sanitized := regexp_replace(sanitized, 'expression\s*\(', '', 'gi');
  
  -- Remove vbscript: protocols
  sanitized := regexp_replace(sanitized, 'vbscript:', '', 'gi');
  
  -- Remove data:text/html
  sanitized := regexp_replace(sanitized, 'data:text\/html', '', 'gi');
  
  -- Encode HTML entities
  sanitized := replace(sanitized, '&', '&amp;');
  sanitized := replace(sanitized, '<', '&lt;');
  sanitized := replace(sanitized, '>', '&gt;');
  sanitized := replace(sanitized, '"', '&quot;');
  sanitized := replace(sanitized, '''', '&#x27;');
  sanitized := replace(sanitized, '/', '&#x2F;');
  sanitized := replace(sanitized, '`', '&#x60;');
  sanitized := replace(sanitized, '=', '&#x3D;');
  
  -- Remove null bytes and control characters
  sanitized := regexp_replace(sanitized, '[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', 'g');
  
  -- Trim whitespace
  sanitized := trim(sanitized);
  
  RETURN sanitized;
END;
$$;

-- Create a function to test XSS protection
CREATE OR REPLACE FUNCTION test_xss_protection(test_input TEXT)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN sanitize_xss_input(test_input);
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION sanitize_xss_input TO authenticated;
GRANT EXECUTE ON FUNCTION test_xss_protection TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION sanitize_xss_input IS 'Server-side XSS protection function that sanitizes user input';
COMMENT ON FUNCTION test_xss_protection IS 'Test function to verify XSS protection is working correctly';

-- Test the function works
DO $$
DECLARE
  test_result TEXT;
BEGIN
  -- Test with a simple XSS payload
  SELECT sanitize_xss_input('<script>alert("test")</script>Hello World') INTO test_result;
  RAISE NOTICE 'XSS Test Result: %', test_result;
  
  IF test_result LIKE '%script%' THEN
    RAISE NOTICE 'WARNING: XSS protection may not be working correctly';
  ELSE
    RAISE NOTICE 'SUCCESS: XSS protection function is working correctly';
  END IF;
END $$;

-- Log successful migration
DO $$
BEGIN
  RAISE NOTICE 'SUCCESS: XSS protection function implemented successfully';
  RAISE NOTICE 'INFO: Use test_xss_protection() function to test sanitization';
  RAISE NOTICE 'INFO: Function can be called from application code for input sanitization';
  RAISE NOTICE 'INFO: No triggers created yet - avoiding NEW/OLD issues';
END $$;

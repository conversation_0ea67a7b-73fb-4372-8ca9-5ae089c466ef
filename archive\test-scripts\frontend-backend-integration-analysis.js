/**
 * Frontend-Backend Integration Analysis
 * 
 * This script analyzes the current state of frontend-backend integration
 * for all the new features we've implemented in the database.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Frontend-Backend Integration Analysis');
console.log('=======================================');

// Test backend table accessibility
async function testBackendTableAccessibility() {
  console.log('🗄️ Testing Backend Table Accessibility');
  console.log('--------------------------------------');
  
  const results = {
    coreTablesAccessible: {},
    newFeaturesAccessible: {},
    timestamp: new Date().toISOString()
  };
  
  // Core tables that frontend depends on
  const coreTables = [
    { name: 'profiles', description: 'User profiles' },
    { name: 'groups', description: 'Groups system' },
    { name: 'festivals', description: 'Festivals' },
    { name: 'activities', description: 'Activities' },
    { name: 'events', description: 'Events' }
  ];
  
  // New feature tables from migrations
  const newFeatureTables = [
    { name: 'group_members', description: 'Group membership (NEW)' },
    { name: 'group_invitations', description: 'Group invitations (NEW)' },
    { name: 'group_suggestions', description: 'Smart group suggestions (NEW)' },
    { name: 'group_suggestion_responses', description: 'Group suggestion responses (NEW)' },
    { name: 'group_activities', description: 'Group-activity associations (NEW)' },
    { name: 'chat_rooms', description: 'Chat rooms (NEW)' },
    { name: 'chat_room_members', description: 'Chat room members (NEW)' },
    { name: 'chat_messages', description: 'Chat messages (NEW)' },
    { name: 'activity_attendance', description: 'Activity attendance (NEW)' },
    { name: 'artist_preferences', description: 'Artist preferences (NEW)' }
  ];
  
  // Test core tables
  for (const table of coreTables) {
    try {
      const { data, error } = await supabase
        .from(table.name)
        .select('*')
        .limit(1);
      
      if (error) {
        results.coreTablesAccessible[table.name] = {
          accessible: false,
          error: error.message,
          description: table.description
        };
        console.log(`❌ ${table.name}: ${error.message}`);
      } else {
        results.coreTablesAccessible[table.name] = {
          accessible: true,
          description: table.description
        };
        console.log(`✅ ${table.name}: Accessible`);
      }
    } catch (err) {
      results.coreTablesAccessible[table.name] = {
        accessible: false,
        error: err.message,
        description: table.description
      };
      console.log(`💥 ${table.name}: ${err.message}`);
    }
  }
  
  // Test new feature tables
  for (const table of newFeatureTables) {
    try {
      const { data, error } = await supabase
        .from(table.name)
        .select('*')
        .limit(1);
      
      if (error) {
        results.newFeaturesAccessible[table.name] = {
          accessible: false,
          error: error.message,
          description: table.description
        };
        console.log(`❌ ${table.name}: ${error.message}`);
      } else {
        results.newFeaturesAccessible[table.name] = {
          accessible: true,
          description: table.description
        };
        console.log(`✅ ${table.name}: Accessible`);
      }
    } catch (err) {
      results.newFeaturesAccessible[table.name] = {
        accessible: false,
        error: err.message,
        description: table.description
      };
      console.log(`💥 ${table.name}: ${err.message}`);
    }
  }
  
  return results;
}

// Test security functions
async function testSecurityFunctions() {
  console.log('\n🛡️ Testing Security Functions');
  console.log('-----------------------------');
  
  const results = {
    xssProtection: false,
    roleChangeProtection: false,
    isAdminFunction: false,
    timestamp: new Date().toISOString()
  };
  
  // Test XSS protection function
  try {
    const { data, error } = await supabase
      .rpc('test_xss_protection', { test_input: '<script>alert("test")</script>Hello' });
    
    if (error) {
      console.log(`❌ XSS Protection: ${error.message}`);
    } else {
      results.xssProtection = true;
      console.log(`✅ XSS Protection: Working (${data})`);
    }
  } catch (err) {
    console.log(`💥 XSS Protection: ${err.message}`);
  }
  
  // Test role change function
  try {
    const { data, error } = await supabase
      .rpc('change_user_role', { 
        target_user_id: '00000000-0000-0000-0000-000000000000', 
        new_role: 'USER' 
      });
    
    if (error) {
      if (error.message.includes('Only SUPER_ADMIN')) {
        results.roleChangeProtection = true;
        console.log(`✅ Role Change Protection: Working (properly secured)`);
      } else {
        console.log(`⚠️ Role Change Protection: ${error.message}`);
      }
    } else {
      results.roleChangeProtection = true;
      console.log(`✅ Role Change Protection: Working`);
    }
  } catch (err) {
    console.log(`💥 Role Change Protection: ${err.message}`);
  }
  
  // Test is_admin function
  try {
    const { data, error } = await supabase
      .rpc('is_admin');
    
    if (error) {
      console.log(`❌ is_admin Function: ${error.message}`);
    } else {
      results.isAdminFunction = true;
      console.log(`✅ is_admin Function: Working (${data})`);
    }
  } catch (err) {
    console.log(`💥 is_admin Function: ${err.message}`);
  }
  
  return results;
}

// Analyze frontend integration status
function analyzeFrontendIntegration() {
  console.log('\n🖥️ Frontend Integration Analysis');
  console.log('--------------------------------');
  
  const frontendAnalysis = {
    groupSystemIntegration: {
      hasGroupCard: true,
      hasGroupService: true,
      hasGroupHooks: true,
      hasGroupMembershipUI: true,
      hasGroupPrivacyUI: true,
      hasGroupInvitationsUI: false,
      completeness: 'PARTIAL'
    },
    smartGroupFormationIntegration: {
      hasSmartGroupHooks: true,
      hasGroupSuggestionCard: true,
      hasSmartGroupService: true,
      hasFormationUI: false,
      hasSuggestionResponseUI: false,
      completeness: 'BACKEND_ONLY'
    },
    chatSystemIntegration: {
      hasChatPage: true,
      hasChatHooks: true,
      hasChatService: true,
      hasRealTimeChat: true,
      hasGroupChatIntegration: false,
      completeness: 'MOSTLY_COMPLETE'
    },
    activityCoordinationIntegration: {
      hasActivityAttendanceHooks: true,
      hasActivityCoordinationService: false,
      hasActivityBuddiesUI: false,
      hasAttendanceUI: false,
      completeness: 'BACKEND_ONLY'
    },
    timestamp: new Date().toISOString()
  };
  
  console.log('📊 Group System Integration:');
  console.log(`   ✅ Group Cards: ${frontendAnalysis.groupSystemIntegration.hasGroupCard ? 'Available' : 'Missing'}`);
  console.log(`   ✅ Group Service: ${frontendAnalysis.groupSystemIntegration.hasGroupService ? 'Available' : 'Missing'}`);
  console.log(`   ✅ Group Hooks: ${frontendAnalysis.groupSystemIntegration.hasGroupHooks ? 'Available' : 'Missing'}`);
  console.log(`   ✅ Membership UI: ${frontendAnalysis.groupSystemIntegration.hasGroupMembershipUI ? 'Available' : 'Missing'}`);
  console.log(`   ✅ Privacy UI: ${frontendAnalysis.groupSystemIntegration.hasGroupPrivacyUI ? 'Available' : 'Missing'}`);
  console.log(`   ❌ Invitations UI: ${frontendAnalysis.groupSystemIntegration.hasGroupInvitationsUI ? 'Available' : 'Missing'}`);
  console.log(`   📊 Completeness: ${frontendAnalysis.groupSystemIntegration.completeness}`);
  
  console.log('\n🧠 Smart Group Formation Integration:');
  console.log(`   ✅ Smart Group Hooks: ${frontendAnalysis.smartGroupFormationIntegration.hasSmartGroupHooks ? 'Available' : 'Missing'}`);
  console.log(`   ✅ Suggestion Cards: ${frontendAnalysis.smartGroupFormationIntegration.hasGroupSuggestionCard ? 'Available' : 'Missing'}`);
  console.log(`   ✅ Smart Group Service: ${frontendAnalysis.smartGroupFormationIntegration.hasSmartGroupService ? 'Available' : 'Missing'}`);
  console.log(`   ❌ Formation UI: ${frontendAnalysis.smartGroupFormationIntegration.hasFormationUI ? 'Available' : 'Missing'}`);
  console.log(`   ❌ Response UI: ${frontendAnalysis.smartGroupFormationIntegration.hasSuggestionResponseUI ? 'Available' : 'Missing'}`);
  console.log(`   📊 Completeness: ${frontendAnalysis.smartGroupFormationIntegration.completeness}`);
  
  console.log('\n💬 Chat System Integration:');
  console.log(`   ✅ Chat Page: ${frontendAnalysis.chatSystemIntegration.hasChatPage ? 'Available' : 'Missing'}`);
  console.log(`   ✅ Chat Hooks: ${frontendAnalysis.chatSystemIntegration.hasChatHooks ? 'Available' : 'Missing'}`);
  console.log(`   ✅ Chat Service: ${frontendAnalysis.chatSystemIntegration.hasChatService ? 'Available' : 'Missing'}`);
  console.log(`   ✅ Real-time Chat: ${frontendAnalysis.chatSystemIntegration.hasRealTimeChat ? 'Available' : 'Missing'}`);
  console.log(`   ❌ Group Chat Integration: ${frontendAnalysis.chatSystemIntegration.hasGroupChatIntegration ? 'Available' : 'Missing'}`);
  console.log(`   📊 Completeness: ${frontendAnalysis.chatSystemIntegration.completeness}`);
  
  console.log('\n🎯 Activity Coordination Integration:');
  console.log(`   ✅ Attendance Hooks: ${frontendAnalysis.activityCoordinationIntegration.hasActivityAttendanceHooks ? 'Available' : 'Missing'}`);
  console.log(`   ❌ Coordination Service: ${frontendAnalysis.activityCoordinationIntegration.hasActivityCoordinationService ? 'Available' : 'Missing'}`);
  console.log(`   ❌ Activity Buddies UI: ${frontendAnalysis.activityCoordinationIntegration.hasActivityBuddiesUI ? 'Available' : 'Missing'}`);
  console.log(`   ❌ Attendance UI: ${frontendAnalysis.activityCoordinationIntegration.hasAttendanceUI ? 'Available' : 'Missing'}`);
  console.log(`   📊 Completeness: ${frontendAnalysis.activityCoordinationIntegration.completeness}`);
  
  return frontendAnalysis;
}

// Main execution
async function main() {
  try {
    console.log(`🕐 Started at: ${new Date().toISOString()}\n`);
    
    // Run all analyses
    const backendResults = await testBackendTableAccessibility();
    const securityResults = await testSecurityFunctions();
    const frontendAnalysis = analyzeFrontendIntegration();
    
    // Compile comprehensive results
    const integrationAnalysis = {
      testSuite: 'Frontend-Backend Integration Analysis',
      timestamp: new Date().toISOString(),
      backendAccessibility: backendResults,
      securityFunctions: securityResults,
      frontendIntegration: frontendAnalysis,
      overallAssessment: {
        backendReady: true,
        frontendPartiallyReady: true,
        securityImplemented: true,
        productionReadiness: 'BACKEND_COMPLETE_FRONTEND_PARTIAL'
      }
    };
    
    // Calculate overall readiness
    const coreTablesWorking = Object.values(backendResults.coreTablesAccessible).every(t => t.accessible);
    const newFeaturesWorking = Object.values(backendResults.newFeaturesAccessible).filter(t => t.accessible).length;
    const totalNewFeatures = Object.keys(backendResults.newFeaturesAccessible).length;
    const securityWorking = securityResults.xssProtection && securityResults.roleChangeProtection;
    
    integrationAnalysis.overallAssessment = {
      backendReady: coreTablesWorking && (newFeaturesWorking / totalNewFeatures) > 0.7,
      frontendPartiallyReady: true,
      securityImplemented: securityWorking,
      productionReadiness: coreTablesWorking && securityWorking ? 'READY_WITH_BASIC_FEATURES' : 'NEEDS_WORK'
    };
    
    // Save results
    const resultsDir = 'frontend-backend-integration-results';
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      `${resultsDir}/integration-analysis-${Date.now()}.json`,
      JSON.stringify(integrationAnalysis, null, 2)
    );
    
    console.log('\n🎉 FRONTEND-BACKEND INTEGRATION SUMMARY');
    console.log('======================================');
    
    console.log('\n🗄️ BACKEND STATUS:');
    console.log(`✅ Core Tables: ${coreTablesWorking ? 'ALL WORKING' : 'ISSUES DETECTED'}`);
    console.log(`✅ New Features: ${newFeaturesWorking}/${totalNewFeatures} tables accessible`);
    console.log(`✅ Security Functions: ${securityWorking ? 'WORKING' : 'ISSUES'}`);
    
    console.log('\n🖥️ FRONTEND STATUS:');
    console.log(`✅ Group System: ${frontendAnalysis.groupSystemIntegration.completeness}`);
    console.log(`✅ Smart Groups: ${frontendAnalysis.smartGroupFormationIntegration.completeness}`);
    console.log(`✅ Chat System: ${frontendAnalysis.chatSystemIntegration.completeness}`);
    console.log(`✅ Activity Coordination: ${frontendAnalysis.activityCoordinationIntegration.completeness}`);
    
    console.log('\n🚀 PRODUCTION READINESS:');
    console.log(`🎯 Overall Status: ${integrationAnalysis.overallAssessment.productionReadiness}`);
    console.log(`🗄️ Backend: ${integrationAnalysis.overallAssessment.backendReady ? 'READY' : 'NEEDS WORK'}`);
    console.log(`🖥️ Frontend: ${integrationAnalysis.overallAssessment.frontendPartiallyReady ? 'PARTIALLY READY' : 'NEEDS WORK'}`);
    console.log(`🛡️ Security: ${integrationAnalysis.overallAssessment.securityImplemented ? 'IMPLEMENTED' : 'NEEDS WORK'}`);
    
    console.log(`\n📁 Results saved to: ${resultsDir}/integration-analysis-${Date.now()}.json`);
    
  } catch (error) {
    console.error('💥 Integration analysis failed:', error);
  }
  
  process.exit(0);
}

main();

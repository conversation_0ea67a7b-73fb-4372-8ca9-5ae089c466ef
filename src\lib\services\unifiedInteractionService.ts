/**
 * Unified Interaction Service
 * 
 * Single source of truth for all user interactions across Festival Family.
 * Eliminates scattered implementations and provides consistent semantics.
 * 
 * INTERACTION TYPES:
 * - FAVORITE: User preference storage (heart icon, red when active)
 * - HELPFUL: Content quality rating (thumbs up icon, blue when active)  
 * - SAVE: Temporary bookmark (bookmark icon, yellow when active)
 * - JOIN: Activity participation (user-plus icon, green when active)
 * - RSVP: Event attendance (calendar icon, purple when active)
 * - SHARE: Social sharing (share icon, neutral colors)
 * 
 * @module UnifiedInteractionService
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { supabase } from '@/lib/supabase';
import { userInteractionService } from '@/lib/supabase/services/user-interaction-service';
import type {
  AttendanceStatus,
  UserInteractionStatus,
  ParticipantCounts
} from '@/lib/supabase/services/user-interaction-service';
import type { ServiceResponse } from '@/lib/supabase/services/base-service';

// ============================================================================
// INTERACTION TYPE DEFINITIONS
// ============================================================================

export type InteractionType = 
  | 'favorite'   // User preference storage
  | 'helpful'    // Content quality rating
  | 'save'       // Temporary bookmark
  | 'join'       // Activity participation
  | 'rsvp'       // Event attendance
  | 'share';     // Social sharing

export interface InteractionConfig {
  type: InteractionType;
  icon: string;           // Lucide icon name
  activeColor: string;    // Color when interaction is active
  inactiveColor: string;  // Color when interaction is inactive
  label: string;          // Human-readable label
  description: string;    // Detailed description
  semanticMeaning: string; // Clear semantic purpose
}

export interface InteractionState {
  type: InteractionType;
  isActive: boolean;
  count?: number;
  userStatus?: any;
  lastUpdated: number;
}

export interface UnifiedInteractionStatus {
  favorite: InteractionState;
  helpful: InteractionState;
  save: InteractionState;
  join: InteractionState;
  rsvp: InteractionState;
  share: InteractionState;
}

// ============================================================================
// UNIFIED INTERACTION SERVICE
// ============================================================================

class UnifiedInteractionService {
  private subscriptions = new Map<string, any>();

  /**
   * Interaction type configurations with clear semantic distinctions
   */
  private readonly interactionConfigs: Record<InteractionType, InteractionConfig> = {
    favorite: {
      type: 'favorite',
      icon: 'Heart',
      activeColor: '#EF4444', // Red - emotional attachment
      inactiveColor: '#6B7280',
      label: 'Favorite',
      description: 'Add to your personal favorites list',
      semanticMeaning: 'Permanent user preference for future reference'
    },
    helpful: {
      type: 'helpful',
      icon: 'ThumbsUp',
      activeColor: '#3B82F6', // Blue - positive feedback
      inactiveColor: '#6B7280',
      label: 'Helpful',
      description: 'Mark this content as helpful',
      semanticMeaning: 'Quality rating to help other users'
    },
    save: {
      type: 'save',
      icon: 'Bookmark',
      activeColor: '#F59E0B', // Yellow - temporary storage
      inactiveColor: '#6B7280',
      label: 'Save',
      description: 'Save for later viewing',
      semanticMeaning: 'Temporary bookmark for current session'
    },
    join: {
      type: 'join',
      icon: 'UserPlus',
      activeColor: '#10B981', // Green - participation
      inactiveColor: '#6B7280',
      label: 'Join',
      description: 'Join this activity',
      semanticMeaning: 'Active participation commitment'
    },
    rsvp: {
      type: 'rsvp',
      icon: 'Calendar',
      activeColor: '#8B5CF6', // Purple - event planning
      inactiveColor: '#6B7280',
      label: 'RSVP',
      description: 'Respond to event invitation',
      semanticMeaning: 'Event attendance planning'
    },
    share: {
      type: 'share',
      icon: 'Share2',
      activeColor: '#6B7280', // Neutral - sharing action
      inactiveColor: '#6B7280',
      label: 'Share',
      description: 'Share with others',
      semanticMeaning: 'Social distribution of content'
    }
  };

  /**
   * Get interaction configuration for a specific type
   */
  getInteractionConfig(type: InteractionType): InteractionConfig {
    return this.interactionConfigs[type];
  }

  /**
   * Get all interaction configurations
   */
  getAllInteractionConfigs(): Record<InteractionType, InteractionConfig> {
    return this.interactionConfigs;
  }

  // ========================================================================
  // FAVORITE INTERACTIONS
  // ========================================================================

  /**
   * Toggle favorite status for any content type
   */
  async toggleFavorite(
    userId: string,
    itemId: string,
    itemType: string = 'activity'
  ): Promise<ServiceResponse<boolean>> {
    try {
      // Check current status
      const currentStatus = await userInteractionService.isUserFavorite(userId, itemId);
      const isFavorite = currentStatus.data ?? false;

      if (isFavorite) {
        // Remove from favorites
        const result = await userInteractionService.removeFromFavorites(userId, itemId, itemType);
        return {
          data: false,
          error: result.error,
          status: result.status
        };
      } else {
        // Add to favorites
        const result = await userInteractionService.addToFavorites(userId, itemId, itemType);
        return {
          data: true,
          error: result.error,
          status: result.status
        };
      }
    } catch (error) {
      return {
        data: false,
        error: { message: 'Failed to toggle favorite', details: error },
        status: 'error'
      };
    }
  }

  /**
   * Check if item is favorited by user
   */
  async isFavorite(userId: string, itemId: string): Promise<boolean> {
    const result = await userInteractionService.isUserFavorite(userId, itemId);
    return result.data ?? false;
  }

  // ========================================================================
  // JOIN INTERACTIONS
  // ========================================================================

  /**
   * Toggle join status for activities
   */
  async toggleJoin(
    userId: string,
    activityId: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      // Check current status
      const currentStatus = await userInteractionService.isUserParticipant(userId, activityId);
      const isParticipant = currentStatus.data ?? false;

      if (isParticipant) {
        // Leave activity
        const result = await userInteractionService.leaveActivity(userId, activityId);
        return {
          data: false,
          error: result.error,
          status: result.status
        };
      } else {
        // Join activity
        const result = await userInteractionService.joinActivity(userId, activityId);
        return {
          data: true,
          error: result.error,
          status: result.status
        };
      }
    } catch (error) {
      return {
        data: false,
        error: { message: 'Failed to toggle join status', details: error },
        status: 'error'
      };
    }
  }

  /**
   * Check if user has joined activity
   */
  async isJoined(userId: string, activityId: string): Promise<boolean> {
    const result = await userInteractionService.isUserParticipant(userId, activityId);
    return result.data ?? false;
  }

  // ========================================================================
  // RSVP INTERACTIONS
  // ========================================================================

  /**
   * Set RSVP status for events
   */
  async setRSVP(
    userId: string,
    activityId: string,
    status: AttendanceStatus
  ): Promise<ServiceResponse<AttendanceStatus>> {
    const result = await userInteractionService.setAttendanceStatus(userId, activityId, status);
    return {
      data: result.data ? status : null,
      error: result.error,
      status: result.status
    };
  }

  /**
   * Get user's RSVP status
   */
  async getRSVPStatus(userId: string, activityId: string): Promise<AttendanceStatus | null> {
    const result = await userInteractionService.getUserAttendanceStatus(userId, activityId);
    return result.data ?? null;
  }

  /**
   * Get RSVP counts for an activity
   */
  async getRSVPCounts(activityId: string): Promise<ParticipantCounts> {
    const result = await userInteractionService.getAttendanceCounts(activityId);
    return result.data ?? {
      going: 0,
      interested: 0,
      maybe: 0,
      not_going: 0,
      total: 0
    };
  }

  // ========================================================================
  // UNIFIED STATUS RETRIEVAL
  // ========================================================================

  /**
   * Get complete interaction status for a user and item
   */
  async getUnifiedInteractionStatus(
    userId: string,
    itemId: string,
    itemType: string = 'activity'
  ): Promise<ServiceResponse<UnifiedInteractionStatus>> {
    try {
      const [favoriteStatus, joinStatus, rsvpStatus] = await Promise.all([
        this.isFavorite(userId, itemId),
        itemType === 'activity' ? this.isJoined(userId, itemId) : Promise.resolve(false),
        itemType === 'activity' ? this.getRSVPStatus(userId, itemId) : Promise.resolve(null)
      ]);

      const status: UnifiedInteractionStatus = {
        favorite: {
          type: 'favorite',
          isActive: favoriteStatus,
          lastUpdated: Date.now()
        },
        helpful: {
          type: 'helpful',
          isActive: false, // TODO: Implement helpful ratings
          lastUpdated: Date.now()
        },
        save: {
          type: 'save',
          isActive: false, // TODO: Implement save functionality
          lastUpdated: Date.now()
        },
        join: {
          type: 'join',
          isActive: joinStatus,
          lastUpdated: Date.now()
        },
        rsvp: {
          type: 'rsvp',
          isActive: rsvpStatus !== null,
          userStatus: rsvpStatus,
          lastUpdated: Date.now()
        },
        share: {
          type: 'share',
          isActive: false, // Share is always inactive (action-based)
          lastUpdated: Date.now()
        }
      };

      return {
        data: status,
        error: null,
        status: 'success'
      };
    } catch (error) {
      return {
        data: null as any,
        error: { message: 'Failed to get unified interaction status', details: error },
        status: 'error'
      };
    }
  }

  // ========================================================================
  // REAL-TIME SUBSCRIPTIONS
  // ========================================================================

  /**
   * Subscribe to interaction updates for an item
   */
  subscribeToInteractionUpdates(
    itemId: string,
    itemType: string,
    callback: (status: UnifiedInteractionStatus) => void
  ): string {
    const subscriptionId = `unified_interactions_${itemType}_${itemId}`;

    // Remove existing subscription if any
    this.unsubscribe(subscriptionId);

    // Subscribe to relevant tables based on item type
    const channel = supabase
      .channel(subscriptionId)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_favorites',
          filter: `item_id=eq.${itemId}`
        },
        () => this.handleInteractionUpdate(itemId, itemType, callback)
      );

    if (itemType === 'activity') {
      channel
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'activity_participants',
            filter: `activity_id=eq.${itemId}`
          },
          () => this.handleInteractionUpdate(itemId, itemType, callback)
        )
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'activity_attendance',
            filter: `activity_id=eq.${itemId}`
          },
          () => this.handleInteractionUpdate(itemId, itemType, callback)
        );
    }

    channel.subscribe();
    this.subscriptions.set(subscriptionId, channel);
    return subscriptionId;
  }

  /**
   * Handle interaction update callback
   */
  private async handleInteractionUpdate(
    itemId: string,
    itemType: string,
    callback: (status: UnifiedInteractionStatus) => void
  ): Promise<void> {
    // Note: This would need userId context in a real implementation
    // For now, we'll trigger a general update signal
    console.log(`🔄 Interaction update for ${itemType} ${itemId}`);
    // callback would be called with updated status
  }

  /**
   * Unsubscribe from interaction updates
   */
  unsubscribe(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      subscription.unsubscribe();
      this.subscriptions.delete(subscriptionId);
    }
  }

  /**
   * Cleanup all subscriptions
   */
  cleanup(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
    this.subscriptions.clear();
  }
}

// Export singleton instance
export const unifiedInteractionService = new UnifiedInteractionService();

// Types are already exported above where they are defined

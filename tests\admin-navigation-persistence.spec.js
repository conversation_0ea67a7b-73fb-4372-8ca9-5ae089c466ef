/**
 * Phase 1: Admin Navigation Persistence Validation
 * 
 * This test suite validates the enhanced admin navigation system and session persistence:
 * - Admin session persistence across navigation
 * - Enhanced admin navigation with shield icon + dropdown
 * - "View as User"/"Back to Admin" toggle functionality
 * - Promise.race implementation performance validation
 * - Cross-navigation persistence testing
 * 
 * Evidence-based testing with screenshots and performance metrics.
 */

import { test, expect } from '@playwright/test';
import { promises as fs } from 'fs';

// Test configuration
const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'admin-navigation-persistence-evidence';

// Admin credentials from session guide
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

// Admin navigation sections to test
const ADMIN_SECTIONS = [
  { name: 'Dashboard', path: '/admin', selector: 'text=Dashboard' },
  { name: 'Users', path: '/admin/users', selector: 'text=Users' },
  { name: 'Events', path: '/admin/events', selector: 'text=Events' },
  { name: 'Festivals', path: '/admin/festivals', selector: 'text=Festivals' },
  { name: 'Activities', path: '/admin/activities', selector: 'text=Activities' },
  { name: 'Announcements', path: '/admin/announcements', selector: 'text=Announcements' }
];

// Performance tracking
let performanceMetrics = {
  authFlow: {},
  navigation: {},
  viewToggle: {}
};

// Ensure evidence directory exists
test.beforeAll(async () => {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory created: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory already exists: ${EVIDENCE_DIR}`);
  }
});

test.describe('Phase 1: Admin Navigation Persistence Validation', () => {
  
  test('1.1 Admin Authentication and Session Persistence', async ({ page }) => {
    console.log('🔐 Testing admin authentication and session persistence...');
    
    const startTime = Date.now();
    
    // Navigate to app
    await page.goto(APP_URL);
    await page.waitForLoadState('networkidle');
    
    // Take initial screenshot
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/01-initial-page.png`,
      fullPage: true 
    });
    
    // Navigate to auth page - use more specific selector to avoid overlap
    // First try the navigation Sign In link
    try {
      await page.click('nav a[href="/auth"]', { timeout: 5000 });
    } catch (error) {
      console.log('Navigation Sign In not found, trying alternative...');
      // Fallback to direct navigation
      await page.goto(`${APP_URL}/auth`);
    }
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/02-auth-page.png`,
      fullPage: true 
    });
    
    // Fill admin credentials
    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/03-credentials-filled.png`,
      fullPage: true 
    });
    
    // Submit login and measure performance
    const authStartTime = Date.now();
    await page.click('button[type="submit"]');
    
    // Wait for authentication to complete
    await page.waitForURL(/\/(admin|dashboard|home)/, { timeout: 10000 });
    const authEndTime = Date.now();
    
    const authDuration = authEndTime - authStartTime;
    performanceMetrics.authFlow.duration = authDuration;
    performanceMetrics.authFlow.target = 686; // From session guide
    performanceMetrics.authFlow.status = authDuration <= 1000 ? 'EXCELLENT' : 'NEEDS_IMPROVEMENT';
    
    console.log(`⏱️ Auth flow completed in ${authDuration}ms (Target: <686ms)`);
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/04-post-login.png`,
      fullPage: true 
    });
    
    // Verify admin session is established
    const currentUrl = page.url();
    const hasAdminAccess = currentUrl.includes('/admin') || await page.locator('text=Admin').isVisible();
    
    expect(hasAdminAccess).toBeTruthy();
    
    // Test session persistence with page refresh
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    const postRefreshUrl = page.url();
    const sessionPersisted = !postRefreshUrl.includes('/auth');
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/05-post-refresh.png`,
      fullPage: true 
    });
    
    // Save authentication test results
    await fs.writeFile(
      `${EVIDENCE_DIR}/01-auth-test-results.json`,
      JSON.stringify({
        authDuration,
        target: 686,
        status: performanceMetrics.authFlow.status,
        sessionPersisted,
        currentUrl,
        postRefreshUrl,
        timestamp: new Date().toISOString()
      }, null, 2)
    );
    
    expect(sessionPersisted).toBeTruthy();
    console.log('✅ Admin session persistence validated');
  });

  test('1.2 Enhanced Admin Navigation Testing', async ({ page }) => {
    console.log('🧭 Testing enhanced admin navigation system...');
    
    // Login first
    await page.goto(APP_URL);
    // Navigate to auth page with robust selector
    try {
      await page.click('nav a[href="/auth"]', { timeout: 5000 });
    } catch (error) {
      await page.goto(`${APP_URL}/auth`);
    }
    await page.waitForLoadState('networkidle');

    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(admin|dashboard|home)/, { timeout: 10000 });
    
    // Look for admin navigation elements
    const hasShieldIcon = await page.locator('[data-testid="admin-shield"], .admin-dropdown-container, text=Admin').isVisible();
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/06-admin-navigation.png`,
      fullPage: true 
    });
    
    if (hasShieldIcon) {
      console.log('🛡️ Admin shield icon found - testing dropdown');
      
      // Click admin dropdown
      await page.click('text=Admin');
      await page.waitForTimeout(500);
      
      await page.screenshot({ 
        path: `${EVIDENCE_DIR}/07-admin-dropdown-open.png`,
        fullPage: true 
      });
      
      // Test admin quick access items
      const navigationResults = [];
      
      for (const section of ADMIN_SECTIONS) {
        try {
          const isVisible = await page.locator(section.selector).isVisible();
          navigationResults.push({
            section: section.name,
            visible: isVisible,
            path: section.path
          });
          
          if (isVisible) {
            console.log(`✅ ${section.name} navigation item found`);
          } else {
            console.log(`❌ ${section.name} navigation item not found`);
          }
        } catch (error) {
          console.log(`⚠️ Error checking ${section.name}: ${error.message}`);
          navigationResults.push({
            section: section.name,
            visible: false,
            error: error.message,
            path: section.path
          });
        }
      }
      
      // Save navigation test results
      await fs.writeFile(
        `${EVIDENCE_DIR}/02-navigation-test-results.json`,
        JSON.stringify({
          hasShieldIcon,
          navigationResults,
          timestamp: new Date().toISOString()
        }, null, 2)
      );
      
    } else {
      console.log('⚠️ Admin shield icon not found - checking alternative navigation');
      
      // Check for alternative admin navigation
      const hasAdminLink = await page.locator('a[href="/admin"], text=Dashboard').isVisible();
      
      await fs.writeFile(
        `${EVIDENCE_DIR}/02-navigation-test-results.json`,
        JSON.stringify({
          hasShieldIcon: false,
          hasAdminLink,
          note: 'Shield icon not found, checking alternative navigation',
          timestamp: new Date().toISOString()
        }, null, 2)
      );
    }
  });

  test('1.3 Admin Section Navigation Persistence', async ({ page }) => {
    console.log('🔄 Testing navigation between admin sections...');
    
    // Login and navigate to admin
    await page.goto(APP_URL);
    // Navigate to auth page with robust selector
    try {
      await page.click('nav a[href="/auth"]', { timeout: 5000 });
    } catch (error) {
      await page.goto(`${APP_URL}/auth`);
    }
    await page.waitForLoadState('networkidle');

    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(admin|dashboard|home)/, { timeout: 10000 });
    
    // Navigate to admin dashboard
    try {
      await page.goto(`${APP_URL}/admin`);
      await page.waitForLoadState('networkidle');
    } catch (error) {
      console.log('⚠️ Direct admin navigation failed, trying alternative approach');
    }
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/08-admin-dashboard.png`,
      fullPage: true 
    });
    
    const sectionNavigationResults = [];
    
    // Test navigation to each admin section
    for (let i = 0; i < ADMIN_SECTIONS.length; i++) {
      const section = ADMIN_SECTIONS[i];
      console.log(`🔄 Testing navigation to: ${section.name}`);
      
      const navStartTime = Date.now();
      
      try {
        await page.goto(`${APP_URL}${section.path}`);
        await page.waitForLoadState('networkidle');
        
        const navEndTime = Date.now();
        const navDuration = navEndTime - navStartTime;
        
        // Take screenshot of section
        await page.screenshot({ 
          path: `${EVIDENCE_DIR}/09-${section.name.toLowerCase()}-section.png`,
          fullPage: true 
        });
        
        // Verify admin session is maintained
        const currentUrl = page.url();
        const sessionMaintained = !currentUrl.includes('/auth');
        
        sectionNavigationResults.push({
          section: section.name,
          path: section.path,
          navigationDuration: navDuration,
          sessionMaintained,
          currentUrl,
          success: true
        });
        
        console.log(`✅ ${section.name}: ${navDuration}ms, Session: ${sessionMaintained ? 'Maintained' : 'Lost'}`);
        
      } catch (error) {
        console.log(`❌ ${section.name}: Navigation failed - ${error.message}`);
        sectionNavigationResults.push({
          section: section.name,
          path: section.path,
          success: false,
          error: error.message
        });
      }
      
      // Small delay between navigations
      await page.waitForTimeout(500);
    }
    
    // Save section navigation results
    await fs.writeFile(
      `${EVIDENCE_DIR}/03-section-navigation-results.json`,
      JSON.stringify({
        sectionNavigationResults,
        totalSections: ADMIN_SECTIONS.length,
        successfulNavigations: sectionNavigationResults.filter(r => r.success).length,
        timestamp: new Date().toISOString()
      }, null, 2)
    );
  });

  test('1.4 View Mode Toggle Testing', async ({ page }) => {
    console.log('👁️ Testing "View as User"/"Back to Admin" toggle functionality...');
    
    // Login and navigate to admin
    await page.goto(APP_URL);
    // Navigate to auth page with robust selector
    try {
      await page.click('nav a[href="/auth"]', { timeout: 5000 });
    } catch (error) {
      await page.goto(`${APP_URL}/auth`);
    }
    await page.waitForLoadState('networkidle');

    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(admin|dashboard|home)/, { timeout: 10000 });
    
    // Navigate to admin section
    await page.goto(`${APP_URL}/admin`);
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/10-admin-mode.png`,
      fullPage: true 
    });
    
    const viewToggleResults = {
      adminToUser: {},
      userToAdmin: {}
    };
    
    // Test "View as User" toggle
    try {
      const viewAsUserButton = page.locator('text=View as User');
      const hasViewToggle = await viewAsUserButton.isVisible();
      
      if (hasViewToggle) {
        console.log('👁️ "View as User" button found - testing toggle');
        
        const toggleStartTime = Date.now();
        await viewAsUserButton.click();
        await page.waitForLoadState('networkidle');
        const toggleEndTime = Date.now();
        
        viewToggleResults.adminToUser = {
          found: true,
          duration: toggleEndTime - toggleStartTime,
          urlAfterToggle: page.url(),
          success: true
        };
        
        await page.screenshot({ 
          path: `${EVIDENCE_DIR}/11-user-mode.png`,
          fullPage: true 
        });
        
        // Test "Back to Admin" toggle
        const backToAdminButton = page.locator('text=Back to Admin');
        const hasBackToggle = await backToAdminButton.isVisible();
        
        if (hasBackToggle) {
          console.log('🔙 "Back to Admin" button found - testing return');
          
          const backStartTime = Date.now();
          await backToAdminButton.click();
          await page.waitForLoadState('networkidle');
          const backEndTime = Date.now();
          
          viewToggleResults.userToAdmin = {
            found: true,
            duration: backEndTime - backStartTime,
            urlAfterToggle: page.url(),
            success: true
          };
          
          await page.screenshot({ 
            path: `${EVIDENCE_DIR}/12-back-to-admin.png`,
            fullPage: true 
          });
          
        } else {
          viewToggleResults.userToAdmin = {
            found: false,
            note: 'Back to Admin button not found'
          };
        }
        
      } else {
        viewToggleResults.adminToUser = {
          found: false,
          note: 'View as User button not found'
        };
      }
      
    } catch (error) {
      viewToggleResults.error = error.message;
      console.log(`❌ View toggle test failed: ${error.message}`);
    }
    
    // Save view toggle results
    await fs.writeFile(
      `${EVIDENCE_DIR}/04-view-toggle-results.json`,
      JSON.stringify({
        viewToggleResults,
        timestamp: new Date().toISOString()
      }, null, 2)
    );
  });

  test('1.5 Cross-Navigation Persistence Testing', async ({ page }) => {
    console.log('🌐 Testing cross-navigation persistence...');
    
    // Login
    await page.goto(APP_URL);
    // Navigate to auth page with robust selector
    try {
      await page.click('nav a[href="/auth"]', { timeout: 5000 });
    } catch (error) {
      await page.goto(`${APP_URL}/auth`);
    }
    await page.waitForLoadState('networkidle');

    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(admin|dashboard|home)/, { timeout: 10000 });
    
    const crossNavResults = [];
    
    // Test navigation to non-admin sections and back
    const testRoutes = [
      { name: 'Home', path: '/' },
      { name: 'Activities', path: '/activities' },
      { name: 'Profile', path: '/profile' },
      { name: 'Admin Dashboard', path: '/admin' }
    ];
    
    for (const route of testRoutes) {
      try {
        console.log(`🔄 Testing navigation to: ${route.name}`);
        
        await page.goto(`${APP_URL}${route.path}`);
        await page.waitForLoadState('networkidle');
        
        // Check if session is maintained
        const currentUrl = page.url();
        const sessionMaintained = !currentUrl.includes('/auth');
        
        crossNavResults.push({
          route: route.name,
          path: route.path,
          sessionMaintained,
          currentUrl,
          success: true
        });
        
        console.log(`✅ ${route.name}: Session ${sessionMaintained ? 'Maintained' : 'Lost'}`);
        
      } catch (error) {
        crossNavResults.push({
          route: route.name,
          path: route.path,
          success: false,
          error: error.message
        });
      }
    }
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/13-cross-navigation-final.png`,
      fullPage: true 
    });
    
    // Save cross-navigation results
    await fs.writeFile(
      `${EVIDENCE_DIR}/05-cross-navigation-results.json`,
      JSON.stringify({
        crossNavResults,
        totalRoutes: testRoutes.length,
        successfulNavigations: crossNavResults.filter(r => r.success).length,
        sessionMaintained: crossNavResults.filter(r => r.sessionMaintained).length,
        timestamp: new Date().toISOString()
      }, null, 2)
    );
  });

  // Generate comprehensive test report
  test.afterAll(async () => {
    console.log('📊 Generating comprehensive test report...');
    
    try {
      const finalReport = {
        testSuite: 'Phase 1: Admin Navigation Persistence Validation',
        timestamp: new Date().toISOString(),
        performanceMetrics,
        summary: {
          authFlowTarget: '686ms',
          authFlowActual: performanceMetrics.authFlow.duration || 'Not measured',
          authFlowStatus: performanceMetrics.authFlow.status || 'Unknown'
        },
        evidenceFiles: [
          '01-initial-page.png',
          '02-auth-page.png', 
          '03-credentials-filled.png',
          '04-post-login.png',
          '05-post-refresh.png',
          '06-admin-navigation.png',
          '07-admin-dropdown-open.png',
          '08-admin-dashboard.png',
          '09-*-section.png',
          '10-admin-mode.png',
          '11-user-mode.png',
          '12-back-to-admin.png',
          '13-cross-navigation-final.png'
        ],
        dataFiles: [
          '01-auth-test-results.json',
          '02-navigation-test-results.json',
          '03-section-navigation-results.json',
          '04-view-toggle-results.json',
          '05-cross-navigation-results.json'
        ]
      };
      
      await fs.writeFile(
        `${EVIDENCE_DIR}/phase-1-comprehensive-report.json`,
        JSON.stringify(finalReport, null, 2)
      );
      
      console.log('✅ Phase 1 testing completed with comprehensive evidence collection');
      console.log(`📁 Evidence saved to: ${EVIDENCE_DIR}/`);
      
    } catch (error) {
      console.error('❌ Error generating final report:', error);
    }
  });
});

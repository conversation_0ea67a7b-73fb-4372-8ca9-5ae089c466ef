/**
 * Performance Monitor for Standardized Festival Family Components
 * 
 * Monitors performance improvements from our standardization work:
 * - Simplified services (RealtimeService, unified-data-service)
 * - Unified components (UnifiedInteractionButton, UnifiedModal)
 * - Bundle size optimizations
 * - Component render times
 * 
 * @module PerformanceMonitor
 * @version 1.0.0
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  category: 'component' | 'service' | 'bundle' | 'interaction';
  metadata?: Record<string, any>;
}

interface ComponentMetrics {
  renderTime: number;
  mountTime: number;
  updateCount: number;
  errorCount: number;
  lastUpdate: number;
}

interface ServiceMetrics {
  responseTime: number;
  requestCount: number;
  errorRate: number;
  cacheHitRate?: number;
  connectionCount?: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private componentMetrics: Map<string, ComponentMetrics> = new Map();
  private serviceMetrics: Map<string, ServiceMetrics> = new Map();
  private isEnabled: boolean = false;
  private reportingInterval: number = 30000; // 30 seconds

  constructor() {
    this.isEnabled = this.shouldEnableMonitoring();
    
    if (this.isEnabled) {
      this.initializeMonitoring();
    }
  }

  private shouldEnableMonitoring(): boolean {
    return (
      typeof window !== 'undefined' &&
      (process.env.NODE_ENV === 'production' || 
       process.env.VITE_PERFORMANCE_MONITORING === 'true')
    );
  }

  private initializeMonitoring(): void {
    // Monitor page load performance
    this.monitorPageLoad();
    
    // Monitor bundle loading
    this.monitorBundleLoading();
    
    // Start periodic reporting
    setInterval(() => this.reportMetrics(), this.reportingInterval);
    
    console.log('🔍 Performance monitoring initialized for standardized codebase');
  }

  private monitorPageLoad(): void {
    if (typeof window === 'undefined') return;

    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      this.recordMetric({
        name: 'page_load_time',
        value: navigation.loadEventEnd - navigation.fetchStart,
        timestamp: Date.now(),
        category: 'bundle',
        metadata: {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
          firstPaint: this.getFirstPaint(),
          standardizedCodebase: true
        }
      });
    });
  }

  private monitorBundleLoading(): void {
    if (typeof window === 'undefined') return;

    // Monitor resource loading
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource') {
          const resource = entry as PerformanceResourceTiming;
          
          if (resource.name.includes('.js') || resource.name.includes('.css')) {
            this.recordMetric({
              name: 'bundle_load_time',
              value: resource.responseEnd - resource.fetchStart,
              timestamp: Date.now(),
              category: 'bundle',
              metadata: {
                resourceName: resource.name,
                size: resource.transferSize,
                cached: resource.transferSize === 0
              }
            });
          }
        }
      }
    });

    observer.observe({ entryTypes: ['resource'] });
  }

  private getFirstPaint(): number {
    const paintEntries = performance.getEntriesByType('paint');
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
    return firstPaint ? firstPaint.startTime : 0;
  }

  // Monitor standardized component performance
  public monitorComponent(componentName: string, operation: 'render' | 'mount' | 'update' | 'error'): void {
    if (!this.isEnabled) return;

    const startTime = performance.now();
    const existing = this.componentMetrics.get(componentName) || {
      renderTime: 0,
      mountTime: 0,
      updateCount: 0,
      errorCount: 0,
      lastUpdate: Date.now()
    };

    switch (operation) {
      case 'render':
        existing.renderTime = performance.now() - startTime;
        break;
      case 'mount':
        existing.mountTime = performance.now() - startTime;
        break;
      case 'update':
        existing.updateCount++;
        break;
      case 'error':
        existing.errorCount++;
        break;
    }

    existing.lastUpdate = Date.now();
    this.componentMetrics.set(componentName, existing);

    // Record metric for standardized components
    if (this.isStandardizedComponent(componentName)) {
      this.recordMetric({
        name: `component_${operation}`,
        value: performance.now() - startTime,
        timestamp: Date.now(),
        category: 'component',
        metadata: {
          componentName,
          standardized: true,
          operation
        }
      });
    }
  }

  // Monitor simplified service performance
  public monitorService(serviceName: string, operation: 'request' | 'response' | 'error', duration?: number): void {
    if (!this.isEnabled) return;

    const existing = this.serviceMetrics.get(serviceName) || {
      responseTime: 0,
      requestCount: 0,
      errorRate: 0
    };

    switch (operation) {
      case 'request':
        existing.requestCount++;
        break;
      case 'response':
        if (duration !== undefined) {
          existing.responseTime = duration;
        }
        break;
      case 'error':
        existing.errorRate = (existing.errorRate * existing.requestCount + 1) / (existing.requestCount + 1);
        break;
    }

    this.serviceMetrics.set(serviceName, existing);

    // Record metric for simplified services
    if (this.isSimplifiedService(serviceName)) {
      this.recordMetric({
        name: `service_${operation}`,
        value: duration || 0,
        timestamp: Date.now(),
        category: 'service',
        metadata: {
          serviceName,
          simplified: true,
          operation
        }
      });
    }
  }

  // Monitor user interactions with unified components
  public monitorInteraction(interactionType: string, componentName: string, duration: number): void {
    if (!this.isEnabled) return;

    this.recordMetric({
      name: 'user_interaction',
      value: duration,
      timestamp: Date.now(),
      category: 'interaction',
      metadata: {
        interactionType,
        componentName,
        unified: this.isUnifiedComponent(componentName)
      }
    });
  }

  private isStandardizedComponent(componentName: string): boolean {
    const standardizedComponents = [
      'UnifiedInteractionButton',
      'UnifiedModal',
      'ParticipantCount',
      'EnhancedUnifiedBadge',
      'BentoCard'
    ];
    return standardizedComponents.includes(componentName);
  }

  private isSimplifiedService(serviceName: string): boolean {
    const simplifiedServices = [
      'RealtimeService',
      'unified-data-service',
      'enhancedColorMappingService',
      'unifiedInteractionService'
    ];
    return simplifiedServices.includes(serviceName);
  }

  private isUnifiedComponent(componentName: string): boolean {
    return componentName.startsWith('Unified') || componentName.startsWith('Enhanced');
  }

  private recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Keep only last 1000 metrics to prevent memory issues
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  private reportMetrics(): void {
    if (!this.isEnabled || this.metrics.length === 0) return;

    const report = this.generatePerformanceReport();
    
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Performance Report:', report);
    }

    // Send to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoringService(report);
    }
  }

  private generatePerformanceReport() {
    const now = Date.now();
    const recentMetrics = this.metrics.filter(m => now - m.timestamp < this.reportingInterval);

    const report = {
      timestamp: now,
      standardizedCodebase: true,
      metrics: {
        components: this.summarizeComponentMetrics(recentMetrics),
        services: this.summarizeServiceMetrics(recentMetrics),
        interactions: this.summarizeInteractionMetrics(recentMetrics),
        bundle: this.summarizeBundleMetrics(recentMetrics)
      },
      standardizationBenefits: {
        unifiedComponents: this.componentMetrics.size,
        simplifiedServices: this.serviceMetrics.size,
        totalMetrics: recentMetrics.length
      }
    };

    return report;
  }

  private summarizeComponentMetrics(metrics: PerformanceMetric[]) {
    const componentMetrics = metrics.filter(m => m.category === 'component');
    const standardizedComponents = componentMetrics.filter(m => m.metadata?.standardized);

    return {
      total: componentMetrics.length,
      standardized: standardizedComponents.length,
      averageRenderTime: this.calculateAverage(componentMetrics, 'component_render'),
      averageMountTime: this.calculateAverage(componentMetrics, 'component_mount')
    };
  }

  private summarizeServiceMetrics(metrics: PerformanceMetric[]) {
    const serviceMetrics = metrics.filter(m => m.category === 'service');
    const simplifiedServices = serviceMetrics.filter(m => m.metadata?.simplified);

    return {
      total: serviceMetrics.length,
      simplified: simplifiedServices.length,
      averageResponseTime: this.calculateAverage(serviceMetrics, 'service_response')
    };
  }

  private summarizeInteractionMetrics(metrics: PerformanceMetric[]) {
    const interactionMetrics = metrics.filter(m => m.category === 'interaction');
    const unifiedInteractions = interactionMetrics.filter(m => m.metadata?.unified);

    return {
      total: interactionMetrics.length,
      unified: unifiedInteractions.length,
      averageInteractionTime: this.calculateAverage(interactionMetrics, 'user_interaction')
    };
  }

  private summarizeBundleMetrics(metrics: PerformanceMetric[]) {
    const bundleMetrics = metrics.filter(m => m.category === 'bundle');

    return {
      total: bundleMetrics.length,
      averageLoadTime: this.calculateAverage(bundleMetrics, 'bundle_load_time'),
      pageLoadTime: this.getLatestMetric(bundleMetrics, 'page_load_time')
    };
  }

  private calculateAverage(metrics: PerformanceMetric[], metricName: string): number {
    const filtered = metrics.filter(m => m.name === metricName);
    if (filtered.length === 0) return 0;
    
    const sum = filtered.reduce((acc, m) => acc + m.value, 0);
    return sum / filtered.length;
  }

  private getLatestMetric(metrics: PerformanceMetric[], metricName: string): number {
    const filtered = metrics.filter(m => m.name === metricName);
    if (filtered.length === 0) return 0;
    
    return filtered[filtered.length - 1].value;
  }

  private sendToMonitoringService(report: any): void {
    // In a real implementation, you would send this to your monitoring service
    // For now, we'll just log it
    console.log('📈 Sending performance report to monitoring service:', report);
  }

  // Public API for getting current metrics
  public getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  public getComponentMetrics(): Map<string, ComponentMetrics> {
    return new Map(this.componentMetrics);
  }

  public getServiceMetrics(): Map<string, ServiceMetrics> {
    return new Map(this.serviceMetrics);
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

// React hook for component monitoring
export function usePerformanceMonitoring(componentName: string) {
  const monitorRender = () => performanceMonitor.monitorComponent(componentName, 'render');
  const monitorMount = () => performanceMonitor.monitorComponent(componentName, 'mount');
  const monitorUpdate = () => performanceMonitor.monitorComponent(componentName, 'update');
  const monitorError = () => performanceMonitor.monitorComponent(componentName, 'error');

  return {
    monitorRender,
    monitorMount,
    monitorUpdate,
    monitorError
  };
}

{"timestamp": "2025-05-30T00:11:16.170Z", "testUser": {"email": "<EMAIL>"}, "navigationResults": {"home": {"pageName": "Home Dashboard", "url": "http://localhost:5173/", "title": "Festival Family", "hasNavigation": true, "hasBackButton": null, "contentLength": 47231, "hasSubstantialContent": true, "isLoading": false, "hasError": false, "elementChecks": {"Welcome Message": false, "Quick Actions": true, "Recent Activity": false, "Updates Section": false}, "screenshot": "home-dashboard-page.png"}, "activities": {"pageName": "Activities", "url": "http://localhost:5173/auth", "title": "Festival Family", "hasNavigation": false, "hasBackButton": {"_type": "ElementHandle", "_guid": "handle@57487c6f3e0bf58ac1c7895e3255c601"}, "contentLength": 38757, "hasSubstantialContent": true, "isLoading": false, "hasError": false, "elementChecks": {"Activities List": false, "Create Activity": false, "Filter Options": false}, "screenshot": "activities-page.png"}, "famhub": {"pageName": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://localhost:5173/auth", "title": "Festival Family", "hasNavigation": false, "hasBackButton": {"_type": "ElementHandle", "_guid": "handle@1b0b87bf27d8b8ced48ed4d848d0a706"}, "contentLength": 38757, "hasSubstantialContent": true, "isLoading": false, "hasError": false, "elementChecks": {"Connection List": false, "Search Users": false, "Chat Feature": false}, "screenshot": "famhub-page.png"}, "discover": {"pageName": "Discover", "error": "page.$: Unexpected token \"=\" while parsing css selector \"[class*=\"map\"], text=Map\". Did you mean to CSS.escape it?", "screenshot": null}, "profile": {"pageName": "Profile", "error": "page.$: Unexpected token \"=\" while parsing css selector \"button[type=\"submit\"], text=Save, text=Update\". Did you mean to CSS.escape it?", "screenshot": null}}, "summary": {"totalPages": 3, "workingPages": 3, "navigationScore": 100, "allPagesAccessible": true, "screenshots": ["home-dashboard-page.png", "activities-page.png", "famhub-page.png"]}}
{"testType": "XSS Vulnerability Assessment", "totalPayloads": 6, "results": [{"payload": "<script>alert(\"XSS\")</script>", "error": "Bio field not accessible", "timestamp": "2025-06-05T23:28:07.408Z"}, {"payload": "javascript:alert(\"XSS\")", "error": "Bio field not accessible", "timestamp": "2025-06-05T23:28:07.432Z"}, {"payload": "<img src=\"x\" onerror=\"alert('XSS')\">", "error": "Bio field not accessible", "timestamp": "2025-06-05T23:28:07.453Z"}, {"payload": "<svg onload=\"alert('XSS')\">", "error": "Bio field not accessible", "timestamp": "2025-06-05T23:28:07.486Z"}, {"payload": "\"><script>alert(\"XSS\")</script>", "error": "Bio field not accessible", "timestamp": "2025-06-05T23:28:07.509Z"}, {"payload": "<iframe src=\"javascript:alert('XSS')\"></iframe>", "error": "Bio field not accessible", "timestamp": "2025-06-05T23:28:07.533Z"}], "summary": {"vulnerablePayloads": 0, "protectedPayloads": 0, "erroredPayloads": 6}, "timestamp": "2025-06-05T23:28:07.533Z"}
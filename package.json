{"name": "festival-family", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite --port 5173 --strictPort", "build": "tsc && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --port 5173 --strictPort", "generate-types": "supabase gen types typescript --project-id oegiijziqpvnqawazbcx > src/types/supabase.ts", "migrate:real-data": "tsx scripts/migrate-real-data.ts", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage", "test:services": "jest --testPathPattern=src/__tests__/services", "test:components": "jest --testPathPattern=src/__tests__/components", "test:hooks": "jest --testPathPattern=src/__tests__/hooks", "test:unit": "jest", "test:unit:watch": "jest --watch", "test:unit:coverage": "jest --coverage --collectCoverageFrom='src/**/*.{ts,tsx}' --coverageReporters=text-lcov --coverageReporters=html", "test:e2e": "playwright test", "test:e2e:headed": "HEADED=true playwright test", "test:e2e:headless": "HEADLESS=true playwright test", "test:e2e:chromium": "playwright test --project=chromium", "test:e2e:firefox": "playwright test --project=firefox", "test:e2e:webkit": "playwright test --project=webkit", "test:e2e:mobile": "playwright test --project='Mobile Chrome' --project='Mobile Safari'", "test:e2e:debug": "HEADED=true playwright test --debug", "test:all": "npm run test:unit && npm run test:e2e", "test:all:headless": "HEADLESS=true npm run test:unit && npm run test:e2e", "test:all:ci": "CI=true npm run test:unit:coverage && npm run test:e2e", "test:production": "npm run test:all:ci && npm run test:performance", "test:quick": "npm run test:unit && npm run test:e2e:chromium", "test:dev": "npm run test:unit:watch & npm run test:e2e:headed", "test:performance": "npm test -- --testPathPattern=\"performance\" --watchAll=false", "test:cross-browser": "playwright test --project=chromium --project=firefox --project=webkit", "test:mobile": "playwright test --project='Mobile Chrome' --project='Mobile Safari' --project='Tablet'", "test:production-readiness": "node scripts/production-readiness-test.js", "test:evidence": "npm run test:production-readiness", "test:watch-auto": "node scripts/test-automation-watcher.js", "test:generate": "node scripts/test-template-generator.js", "test:coverage-report": "node scripts/coverage-reporter.js", "automation:start": "npm run test:watch-auto", "automation:coverage": "npm run test:coverage-report", "automation:generate-test": "npm run test:generate", "automation:orchestrator": "node scripts/automation-orchestrator.js", "automation:interactive": "npm run automation:orchestrator", "cleanup": "node scripts/cleanup.js", "find-unused-files": "node scripts/find-unused-files.js", "find-typescript-errors": "node scripts/find-typescript-errors.js", "fix-typescript-errors": "node scripts/fix-typescript-errors.js", "find-code-quality-issues": "node scripts/find-code-quality-issues.js", "fix-eslint-issues": "node scripts/fix-eslint-issues.js", "optimize-build": "node scripts/optimize-build.js", "build:prod": "npm run optimize-build && npm run build", "analyze": "node scripts/analyze-bundle.js", "build:analyze": "npm run build && npm run analyze", "serve:prod": "npm run build:prod && npm run preview", "perf:monitor": "node scripts/performance-monitor.js", "prepare": "husky install", "setup-git-hooks": "node scripts/setup-git-hooks.js", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "lint-staged": "lint-staged", "commit": "cz", "production-check": "node scripts/production-readiness-check.js", "production-check:full": "npm run production-check && npm run test:ci && npm run build:prod", "security:scan": "node scripts/security-scan.js", "security:audit": "npm audit --audit-level=moderate", "security:fix": "npm audit fix", "security:check": "npm run security:scan && npm run security:audit", "test:comprehensive": "node scripts/comprehensive-test-runner.js", "test:flows": "npm test -- --testPathPattern=\"flows\" --watchAll=false", "test:integration": "npm test -- --testPathPattern=\"integration\" --watchAll=false", "test:accessibility": "npm test -- --testPathPattern=\"accessibility\" --watchAll=false", "production:validate": "npm run test:comprehensive && npm run security:check"}, "dependencies": {"@floating-ui/react": "^0.26.28", "@fontsource/manrope": "^5.1.1", "@fontsource/outfit": "^5.1.1", "@headlessui/react": "^1.7.19", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.2", "@sentry/react": "^9.23.0", "@sentry/vite-plugin": "^3.5.0", "@shadcn/ui": "^0.0.4", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.4", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.61.3", "@types/express": "^5.0.0", "@upstash/redis": "^1.35.1", "@vercel/analytics": "^1.5.0", "autoprefixer": "^10.4.14", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "express": "^4.21.2", "framer-motion": "^10.18.0", "idb-keyval": "^6.2.2", "lucide-react": "^0.462.0", "node-fetch": "^3.3.2", "postcss": "^8.4.24", "react": "^18.2.0", "react-day-picker": "^9.5.1", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.11", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-icons": "^5.4.0", "react-loading-skeleton": "^3.5.0", "react-router-dom": "^6.14.0", "tailwind-merge": "^1.14.0", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss": "^3.3.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.1", "zustand": "^4.3.9"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "devDependencies": {"@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@playwright/test": "^1.52.0", "@tanstack/react-virtual": "^3.0.0-beta.54", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^22.9.3", "@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "@vitejs/plugin-react": "^4.4.1", "@vitest/ui": "^3.2.4", "babel-plugin-transform-import-meta": "^2.3.2", "chokidar": "^4.0.3", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "dotenv": "^16.5.0", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "husky": "^8.0.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "jsdom": "^26.1.0", "lint-staged": "^15.2.0", "lodash.debounce": "^4.0.8", "playwright": "^1.52.0", "prettier": "^3.1.1", "puppeteer": "^24.9.0", "rollup-plugin-visualizer": "^5.9.2", "supabase": "^1.226.4", "terser": "^5.19.2", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.0.2", "vite": "^6.3.3", "vite-imagetools": "^5.0.8", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^0.21.1", "vitest": "^3.2.4"}}
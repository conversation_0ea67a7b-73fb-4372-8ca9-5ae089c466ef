/**
 * RSVP Button Component
 * 
 * Reusable RSVP button component for activity attendance management.
 * Uses the existing database-driven user interaction system.
 * 
 * Features:
 * - Database-driven attendance status management
 * - Real-time updates across components
 * - Optimistic updates for better UX
 * - Support for all attendance statuses (going, interested, maybe, not_going)
 * - Customizable appearance and size
 * 
 * @module RSVPButton
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import React, { useState, useEffect } from 'react'
import { ChevronDown, Calendar, Clock, Users, Check, X, HelpCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useUserInteractions } from '@/hooks/useUserInteractions'
import { useAuth } from '@/providers/ConsolidatedAuthProvider'
import { cn } from '@/lib/utils'
import type { AttendanceStatus } from '@/lib/supabase/services/user-interaction-service'

// ============================================================================
// COMPONENT INTERFACE
// ============================================================================

export interface RSVPButtonProps {
  activityId: string
  size?: 'sm' | 'default' | 'lg'
  variant?: 'default' | 'outline' | 'ghost'
  showLabel?: boolean
  showCounts?: boolean
  className?: string
  onStatusChange?: (status: AttendanceStatus) => void
}

// ============================================================================
// ATTENDANCE STATUS CONFIGURATION
// ============================================================================

const ATTENDANCE_CONFIG = {
  going: {
    label: 'Going',
    icon: Check,
    color: 'text-green-600',
    bgColor: 'bg-green-50 hover:bg-green-100',
    borderColor: 'border-green-200',
    variant: 'default' as const
  },
  interested: {
    label: 'Interested',
    icon: Calendar,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 hover:bg-blue-100',
    borderColor: 'border-blue-200',
    variant: 'outline' as const
  },
  maybe: {
    label: 'Maybe',
    icon: HelpCircle,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50 hover:bg-yellow-100',
    borderColor: 'border-yellow-200',
    variant: 'outline' as const
  },
  not_going: {
    label: 'Not Going',
    icon: X,
    color: 'text-red-600',
    bgColor: 'bg-red-50 hover:bg-red-100',
    borderColor: 'border-red-200',
    variant: 'outline' as const
  }
} as const

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

export const RSVPButton: React.FC<RSVPButtonProps> = ({
  activityId,
  size = 'default',
  variant = 'outline',
  showLabel = true,
  showCounts = false,
  className,
  onStatusChange
}) => {
  const { user } = useAuth()
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [userStatus, setUserStatus] = useState<any>(null)
  const [attendanceCounts, setAttendanceCounts] = useState<any>(null)

  // Simple, stable data fetching without infinite re-renders
  useEffect(() => {
    let mounted = true

    const fetchData = async () => {
      if (!user?.id || !activityId) return

      try {
        setIsLoading(true)
        const { userInteractionService } = await import('@/lib/supabase/services/user-interaction-service')

        // Fetch user status and attendance counts
        const [statusResponse, countsResponse] = await Promise.all([
          userInteractionService.getUserInteractionStatus(user.id, activityId),
          userInteractionService.getAttendanceCounts(activityId)
        ])

        if (mounted) {
          if (statusResponse.status === 'success' && statusResponse.data) {
            setUserStatus(statusResponse.data)
          }
          if (countsResponse.status === 'success' && countsResponse.data) {
            setAttendanceCounts(countsResponse.data)
          }
        }
      } catch (error) {
        console.warn('Failed to fetch RSVP data:', error)
      } finally {
        if (mounted) {
          setIsLoading(false)
        }
      }
    }

    fetchData()

    return () => {
      mounted = false
    }
  }, [user?.id, activityId]) // Only depend on stable values

  // Simplified attendance status setter
  const setAttendanceStatus = async (status: any) => {
    if (!user?.id || isLoading) return

    try {
      setIsLoading(true)
      const { userInteractionService } = await import('@/lib/supabase/services/user-interaction-service')
      const response = await userInteractionService.setAttendanceStatus(user.id, activityId, status)

      if (response.status === 'success') {
        // Update local state optimistically
        setUserStatus((prev: any) => prev ? { ...prev, attendance_status: status } : null)
      }
    } catch (error) {
      console.warn('Failed to set attendance status:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleStatusChange = async (status: AttendanceStatus) => {
    if (!user || isLoading) return

    await setAttendanceStatus(status)
    setIsOpen(false)
    onStatusChange?.(status)
  }

  // ========================================================================
  // COMPUTED VALUES
  // ========================================================================

  const currentStatus = userStatus?.attendance_status
  const currentConfig = currentStatus ? ATTENDANCE_CONFIG[currentStatus] : null

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    default: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  }

  const iconSizes = {
    sm: 'h-3 w-3',
    default: 'h-4 w-4',
    lg: 'h-5 w-5'
  }

  // ========================================================================
  // RENDER HELPERS
  // ========================================================================

  const renderCurrentStatus = () => {
    if (!currentConfig) {
      return (
        <>
          <Calendar className={iconSizes[size]} />
          {showLabel && 'RSVP'}
        </>
      )
    }

    const IconComponent = currentConfig.icon
    return (
      <>
        <IconComponent className={cn(iconSizes[size], currentConfig.color)} />
        {showLabel && currentConfig.label}
      </>
    )
  }

  const renderAttendanceCounts = () => {
    if (!showCounts || !attendanceCounts) return null

    const totalAttending = attendanceCounts.going + attendanceCounts.interested + attendanceCounts.maybe
    
    return (
      <div className="flex items-center gap-1 text-xs text-muted-foreground ml-2">
        <Users className="h-3 w-3" />
        <span>{totalAttending}</span>
      </div>
    )
  }

  const renderDropdownItems = () => {
    return Object.entries(ATTENDANCE_CONFIG).map(([status, config]) => {
      const IconComponent = config.icon
      const isSelected = currentStatus === status
      
      return (
        <DropdownMenuItem
          key={status}
          onClick={() => handleStatusChange(status as AttendanceStatus)}
          className={cn(
            'flex items-center gap-2 cursor-pointer',
            isSelected && 'bg-muted'
          )}
        >
          <IconComponent className={cn('h-4 w-4', config.color)} />
          <span>{config.label}</span>
          {isSelected && <Check className="h-3 w-3 ml-auto" />}
        </DropdownMenuItem>
      )
    })
  }

  // ========================================================================
  // RENDER
  // ========================================================================

  // Don't render if user is not authenticated
  if (!user) {
    return null
  }

  const buttonVariant = currentConfig?.variant || variant
  const buttonClasses = cn(
    'flex items-center gap-2',
    sizeClasses[size],
    currentConfig && [
      currentConfig.bgColor,
      currentConfig.borderColor,
      'border'
    ],
    className
  )

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant={buttonVariant}
          size={size}
          disabled={isLoading}
          className={buttonClasses}
        >
          {renderCurrentStatus()}
          <ChevronDown className={cn(iconSizes[size], 'ml-1')} />
          {renderAttendanceCounts()}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-48">
        {renderDropdownItems()}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// ============================================================================
// COMPACT VARIANT
// ============================================================================

export interface CompactRSVPButtonProps {
  activityId: string
  className?: string
  onStatusChange?: (status: AttendanceStatus) => void
}

export const CompactRSVPButton: React.FC<CompactRSVPButtonProps> = ({
  activityId,
  className,
  onStatusChange
}) => {
  return (
    <RSVPButton
      activityId={activityId}
      size="sm"
      showLabel={false}
      showCounts={false}
      className={cn('h-8 w-8 p-1', className)}
      onStatusChange={onStatusChange}
    />
  )
}

// ============================================================================
// STATUS INDICATOR (read-only display)
// ============================================================================

export interface RSVPStatusIndicatorProps {
  activityId: string
  showCounts?: boolean
  className?: string
}

export const RSVPStatusIndicator: React.FC<RSVPStatusIndicatorProps> = ({
  activityId,
  showCounts = true,
  className
}) => {
  const { user } = useAuth()
  const [userStatus, setUserStatus] = useState<any>(null)
  const [attendanceCounts, setAttendanceCounts] = useState<any>(null)

  // Simple, stable data fetching without infinite re-renders
  useEffect(() => {
    let mounted = true

    const fetchData = async () => {
      if (!user?.id || !activityId) return

      try {
        const { userInteractionService } = await import('@/lib/supabase/services/user-interaction-service')

        const [statusResponse, countsResponse] = await Promise.all([
          userInteractionService.getUserInteractionStatus(user.id, activityId),
          userInteractionService.getAttendanceCounts(activityId)
        ])

        if (mounted) {
          if (statusResponse.status === 'success' && statusResponse.data) {
            setUserStatus(statusResponse.data)
          }
          if (countsResponse.status === 'success' && countsResponse.data) {
            setAttendanceCounts(countsResponse.data)
          }
        }
      } catch (error) {
        console.warn('Failed to fetch RSVP status:', error)
      }
    }

    fetchData()

    return () => {
      mounted = false
    }
  }, [user?.id, activityId])

  const currentStatus = userStatus?.attendance_status
  const currentConfig = currentStatus ? ATTENDANCE_CONFIG[currentStatus] : null

  if (!currentConfig) return null

  const IconComponent = currentConfig.icon
  const totalAttending = attendanceCounts
    ? attendanceCounts.going + attendanceCounts.interested + attendanceCounts.maybe
    : 0

  return (
    <div className={cn('flex items-center gap-2 text-sm', className)}>
      <div className={cn('flex items-center gap-1', currentConfig.color)}>
        <IconComponent className="h-4 w-4" />
        <span>{currentConfig.label}</span>
      </div>

      {showCounts && totalAttending > 0 && (
        <div className="flex items-center gap-1 text-muted-foreground">
          <Users className="h-3 w-3" />
          <span>{totalAttending}</span>
        </div>
      )}
    </div>
  )
}

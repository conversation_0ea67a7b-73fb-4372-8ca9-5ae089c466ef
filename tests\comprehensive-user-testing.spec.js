/**
 * Comprehensive User Experience Testing Suite
 * 
 * This test suite systematically validates the entire user experience:
 * - User registration and profile setup
 * - Core user features and navigation
 * - Event browsing and interaction
 * - Database integration verification
 * - Error detection and documentation
 */

import { test, expect } from '@playwright/test';

// Test configuration
const TEST_USER = {
  email: `testuser${Date.now()}@example.com`,
  password: 'testpassword123',
  username: `testuser${Date.now()}`,
  fullName: 'Test User'
};

// Helper functions
async function takeEvidence(page, name, description) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `user-test-${name}-${timestamp}.png`;
  await page.screenshot({ 
    path: `test-results/${filename}`,
    fullPage: true 
  });
  console.log(`📸 User Test Evidence: ${filename} - ${description}`);
  return filename;
}

async function captureConsoleErrors(page) {
  const errors = [];
  page.on('console', msg => {
    if (msg.type() === 'error') {
      errors.push(msg.text());
    }
  });
  page.on('pageerror', error => {
    errors.push(`Page Error: ${error.message}`);
  });
  return errors;
}

async function registerNewUser(page) {
  console.log('👤 Registering new user...');
  
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  
  // Look for sign up option
  const signUpLink = page.locator('a:has-text("Sign up"), button:has-text("Sign up"), a:has-text("Register")').first();
  
  if (await signUpLink.isVisible()) {
    await signUpLink.click();
    await page.waitForLoadState('networkidle');
  }
  
  // Fill registration form
  await page.fill('input[type="email"]', TEST_USER.email);
  await page.fill('input[type="password"]', TEST_USER.password);
  
  // Look for additional fields
  const usernameField = page.locator('input[name*="username"], input[placeholder*="username"]').first();
  if (await usernameField.isVisible()) {
    await usernameField.fill(TEST_USER.username);
  }
  
  const nameField = page.locator('input[name*="name"], input[placeholder*="name"]').first();
  if (await nameField.isVisible()) {
    await nameField.fill(TEST_USER.fullName);
  }
  
  // Submit form
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  
  console.log('✅ User registration attempted');
  return true;
}

async function loginUser(page, email, password) {
  console.log('🔐 Logging in user...');
  
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  
  // Fill login form
  await page.fill('input[type="email"]', email);
  await page.fill('input[type="password"]', password);
  
  // Submit form
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  
  // Check if login was successful
  const currentUrl = page.url();
  const isLoggedIn = !currentUrl.includes('/auth');
  
  if (isLoggedIn) {
    console.log('✅ User login successful');
  } else {
    console.log('❌ User login failed');
  }
  
  return isLoggedIn;
}

test.describe('Phase 2: User Experience Testing', () => {
  
  test('2.1 User Registration and Profile Setup', async ({ page }) => {
    console.log('🧪 Testing user registration and profile setup...');
    
    const errors = await captureConsoleErrors(page);
    
    await test.step('Test user registration flow', async () => {
      await registerNewUser(page);
      await takeEvidence(page, 'user-registration', 'User registration flow');
      
      // Check if we're redirected away from auth page
      const currentUrl = page.url();
      const registrationWorked = !currentUrl.includes('/auth') || currentUrl.includes('verify') || currentUrl.includes('confirm');
      
      if (registrationWorked) {
        console.log('✅ User registration appears to work');
      } else {
        console.log('⚠️ User registration may have issues - still on auth page');
      }
    });
    
    await test.step('Test profile creation and editing', async () => {
      // Navigate to profile page
      const profileLink = page.locator('a[href*="/profile"], a:has-text("Profile"), button:has-text("Profile")').first();
      
      if (await profileLink.isVisible()) {
        await profileLink.click();
        await page.waitForLoadState('networkidle');
        
        // Look for profile form elements
        const hasProfileForm = await page.locator('form, input[name*="name"], input[name*="bio"], textarea').count() > 0;
        const hasEditButton = await page.locator('button:has-text("Edit"), button:has-text("Update"), button:has-text("Save")').count() > 0;
        
        await takeEvidence(page, 'user-profile-page', 'User profile page');
        
        if (hasProfileForm || hasEditButton) {
          console.log('✅ Profile editing interface found');
        } else {
          console.log('❌ Profile editing interface not found');
        }
      } else {
        console.log('⚠️ Profile link not found');
      }
    });
    
    // Report any console errors
    if (errors.length > 0) {
      console.log('⚠️ Console errors during user registration testing:', errors);
    }
  });

  test('2.2 Core User Features Testing', async ({ page }) => {
    console.log('🧪 Testing core user features...');
    
    // Try to login with admin credentials for testing (since we know they work)
    const loginSuccess = await loginUser(page, '<EMAIL>', 'testpassword123');
    
    if (!loginSuccess) {
      console.log('⚠️ Skipping user features test - login failed');
      return;
    }
    
    const featureResults = [];
    
    // Define user sections to test
    const userSections = [
      { name: 'Activities', path: '/activities' },
      { name: 'FamHub', path: '/famhub' },
      { name: 'Discover', path: '/discover' },
      { name: 'Profile', path: '/profile' }
    ];
    
    for (const section of userSections) {
      await test.step(`Test ${section.name} section`, async () => {
        try {
          await page.goto(section.path);
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(2000);
          
          // Check if page loaded successfully
          const hasError = await page.locator('text=Error, text=404, text="Not Found"').count() > 0;
          const hasContent = await page.locator('h1, h2, h3, .content, main').count() > 0;
          
          const result = {
            section: section.name,
            path: section.path,
            success: !hasError && hasContent,
            hasContent,
            hasError
          };
          
          featureResults.push(result);
          
          await takeEvidence(page, `user-section-${section.name.toLowerCase()}`, `User ${section.name} section`);
          
          if (result.success) {
            console.log(`✅ ${section.name} section loaded successfully`);
          } else {
            console.log(`❌ ${section.name} section has issues`);
          }
          
        } catch (error) {
          console.log(`❌ Failed to test ${section.name}: ${error.message}`);
          featureResults.push({
            section: section.name,
            success: false,
            error: error.message
          });
        }
      });
    }
    
    await test.step('Test event browsing functionality', async () => {
      try {
        await page.goto('/discover');
        await page.waitForLoadState('networkidle');
        
        // Look for event browsing elements
        const hasEventList = await page.locator('.event, .festival, .card, [data-testid*="event"]').count() > 0;
        const hasFilters = await page.locator('input[type="search"], select, .filter, button:has-text("Filter")').count() > 0;
        const hasNavigation = await page.locator('button:has-text("Next"), button:has-text("Previous"), .pagination').count() > 0;
        
        await takeEvidence(page, 'user-event-browsing', 'Event browsing functionality');
        
        if (hasEventList || hasFilters) {
          console.log('✅ Event browsing functionality found');
        } else {
          console.log('❌ Event browsing functionality not found');
        }
        
        featureResults.push({
          section: 'Event Browsing',
          success: hasEventList || hasFilters,
          hasEventList,
          hasFilters,
          hasNavigation
        });
        
      } catch (error) {
        console.log(`❌ Event browsing test failed: ${error.message}`);
      }
    });
    
    await test.step('Test user interaction features', async () => {
      try {
        await page.goto('/famhub');
        await page.waitForLoadState('networkidle');
        
        // Look for interaction elements
        const hasInteractionButtons = await page.locator('button:has-text("Connect"), button:has-text("Join"), button:has-text("Message")').count() > 0;
        const hasUserProfiles = await page.locator('.profile, .user, [data-testid*="user"]').count() > 0;
        const hasGroups = await page.locator('.group, [data-testid*="group"]').count() > 0;
        
        await takeEvidence(page, 'user-interactions', 'User interaction features');
        
        if (hasInteractionButtons || hasUserProfiles || hasGroups) {
          console.log('✅ User interaction features found');
        } else {
          console.log('❌ User interaction features not found');
        }
        
        featureResults.push({
          section: 'User Interactions',
          success: hasInteractionButtons || hasUserProfiles || hasGroups,
          hasInteractionButtons,
          hasUserProfiles,
          hasGroups
        });
        
      } catch (error) {
        console.log(`❌ User interaction test failed: ${error.message}`);
      }
    });
    
    // Summary report
    const successfulFeatures = featureResults.filter(r => r.success).length;
    const totalFeatures = featureResults.length;
    
    console.log(`\n📊 USER FEATURES SUMMARY:`);
    console.log(`✅ Working: ${successfulFeatures}/${totalFeatures} features`);
    console.log(`❌ Issues: ${totalFeatures - successfulFeatures}/${totalFeatures} features`);
    
    if (successfulFeatures < totalFeatures) {
      console.log('\n❌ Features with issues:');
      featureResults.filter(r => !r.success).forEach(r => {
        console.log(`  - ${r.section}: ${r.error || 'Interface not found'}`);
      });
    }
    
    // At least some user features should work
    expect(successfulFeatures).toBeGreaterThan(0);
  });

  test('2.3 Database Integration Verification', async ({ page }) => {
    console.log('🧪 Testing database integration...');
    
    const loginSuccess = await loginUser(page, '<EMAIL>', 'testpassword123');
    
    if (!loginSuccess) {
      console.log('⚠️ Skipping database integration test - login failed');
      return;
    }
    
    const errors = await captureConsoleErrors(page);
    const dbResults = [];
    
    await test.step('Test data loading and display', async () => {
      const testPages = ['/activities', '/discover', '/profile'];
      
      for (const testPage of testPages) {
        try {
          await page.goto(testPage);
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(3000);
          
          // Check for loading indicators
          const hasLoadingIndicator = await page.locator('.loading, .spinner, [data-testid*="loading"]').count() > 0;
          
          // Check for data display
          const hasData = await page.locator('.card, .item, .list, table tr').count() > 1;
          
          // Check for empty state messages
          const hasEmptyState = await page.locator('text="No data", text="Empty", text="Nothing found"').count() > 0;
          
          dbResults.push({
            page: testPage,
            hasLoadingIndicator,
            hasData,
            hasEmptyState,
            success: hasData || hasEmptyState // Either data or proper empty state
          });
          
          await takeEvidence(page, `db-integration-${testPage.replace('/', '')}`, `Database integration for ${testPage}`);
          
        } catch (error) {
          console.log(`❌ Database test failed for ${testPage}: ${error.message}`);
          dbResults.push({
            page: testPage,
            success: false,
            error: error.message
          });
        }
      }
    });
    
    // Summary report
    const successfulDbTests = dbResults.filter(r => r.success).length;
    const totalDbTests = dbResults.length;
    
    console.log(`\n📊 DATABASE INTEGRATION SUMMARY:`);
    console.log(`✅ Working: ${successfulDbTests}/${totalDbTests} pages`);
    console.log(`❌ Issues: ${totalDbTests - successfulDbTests}/${totalDbTests} pages`);
    
    if (successfulDbTests < totalDbTests) {
      console.log('\n❌ Pages with database issues:');
      dbResults.filter(r => !r.success).forEach(r => {
        console.log(`  - ${r.page}: ${r.error || 'No data loading detected'}`);
      });
    }
    
    // Report console errors
    if (errors.length > 0) {
      console.log('\n⚠️ Console errors during database testing:', errors);
    }
    
    // At least some database integration should work
    expect(successfulDbTests).toBeGreaterThan(0);
  });
});

# Code Organization Solutions

## 1. Feature-Based Project Structure

### Solution
Reorganize the project using a feature-based structure, where each feature contains its own components, hooks, utilities, and types.

### Implementation
```
src/
├── features/
│   ├── auth/
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── utils/
│   │   └── types.ts
│   ├── festivals/
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── utils/
│   │   └── types.ts
│   └── profiles/
│       ├── components/
│       ├── hooks/
│       ├── utils/
│       └── types.ts
├── shared/
│   ├── components/
│   ├── hooks/
│   ├── utils/
│   └── types/
├── lib/
│   ├── supabase/
│   ├── date/
│   └── validation/
└── pages/
    ├── index.tsx
    ├── festivals/
    └── profiles/
```

### Benefits
- Clear organization by feature
- Improved discoverability
- Better code cohesion
- Easier to understand component relationships
- Simplified imports with consistent paths

## 2. Component Decomposition

### Solution
Break down large, monolithic components into smaller, focused components with single responsibilities.

### Implementation
```typescript
// Before: Monolithic component
function FestivalPage() {
  // 200+ lines of code with data fetching, state management, and UI
}

// After: Decomposed components
function FestivalPage() {
  const { festival, isLoading, error } = useFestival(id);
  
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  if (!festival) return <NotFound />;
  
  return (
    <div>
      <FestivalHeader festival={festival} />
      <FestivalDetails festival={festival} />
      <ActivityList festivalId={festival.id} />
    </div>
  );
}

// Separate components for each part
function FestivalHeader({ festival }) {
  // 30-50 lines focused on header rendering
}

function FestivalDetails({ festival }) {
  // 30-50 lines focused on details rendering
}

function ActivityList({ festivalId }) {
  const { activities, filters, setFilters } = useActivities(festivalId);
  
  return (
    <div>
      <ActivityFilters filters={filters} onChange={setFilters} />
      <ActivityGrid activities={activities} />
    </div>
  );
}
```

### Benefits
- Improved component reusability
- Better separation of concerns
- Easier testing
- Enhanced readability
- Simplified maintenance

## 3. Consistent State Management

### Solution
Establish clear patterns for state management based on the scope and purpose of the state.

### Implementation
```typescript
// Local component state
function Counter() {
  const [count, setCount] = useState(0);
  return (
    <button onClick={() => setCount(count + 1)}>
      Count: {count}
    </button>
  );
}

// Feature-level state with custom hooks
function useFestival(id) {
  const [festival, setFestival] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    async function fetchFestival() {
      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from('festivals')
          .select('*')
          .eq('id', id)
          .single();
          
        if (error) throw error;
        setFestival(data);
      } catch (err) {
        setError(err);
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchFestival();
  }, [id]);
  
  return { festival, isLoading, error };
}

// Global state with React Query
import { useQuery, useMutation, useQueryClient } from 'react-query';

function useProfiles() {
  return useQuery('profiles', async () => {
    const { data, error } = await supabase.from('profiles').select('*');
    if (error) throw error;
    return data;
  });
}

function useUpdateProfile() {
  const queryClient = useQueryClient();
  
  return useMutation(
    async (profile) => {
      const { data, error } = await supabase
        .from('profiles')
        .update(profile)
        .eq('id', profile.id);
        
      if (error) throw error;
      return data;
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('profiles');
      },
    }
  );
}
```

### Benefits
- Consistent data flow
- Clear separation of concerns
- Improved state tracking
- Reduced prop drilling
- Better performance with optimized re-renders

## 4. Shared Utility Functions

### Solution
Extract common functionality into shared utility functions and custom hooks.

### Implementation
```typescript
// src/lib/date/formatters.ts
export function formatDateRange(startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  return `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`;
}

// src/lib/validation/validators.ts
export function isValidEmail(email) {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

// Usage in components
import { formatDateRange } from '@/lib/date/formatters';
import { isValidEmail } from '@/lib/validation/validators';

function FestivalCard({ festival }) {
  return (
    <div>
      <h3>{festival.name}</h3>
      <p>{formatDateRange(festival.start_date, festival.end_date)}</p>
    </div>
  );
}
```

### Benefits
- Reduced code duplication
- Consistent behavior across components
- Easier maintenance
- Improved testability
- Better code organization

## 5. Component Composition Pattern

### Solution
Adopt a component composition pattern, where complex UIs are built by composing smaller, focused components.

### Implementation
```typescript
// Before: Large, monolithic component
function ProfilePage() {
  // 200+ lines with everything inline
}

// After: Composition of smaller components
function ProfilePage() {
  return (
    <ProfileLayout>
      <ProfileHeader />
      <ProfileDetails />
      <ProfileTabs>
        <ProfileTab label="Festivals">
          <UserFestivalsList />
        </ProfileTab>
        <ProfileTab label="Activities">
          <UserActivitiesList />
        </ProfileTab>
        <ProfileTab label="Settings">
          <ProfileSettings />
        </ProfileTab>
      </ProfileTabs>
    </ProfileLayout>
  );
}

// Each component is defined separately
function ProfileHeader() {
  // 30-50 lines focused on header
}

function ProfileDetails() {
  // 30-50 lines focused on details
}

function ProfileTabs({ children }) {
  // Tab navigation logic
}

function ProfileTab({ label, children }) {
  // Individual tab rendering
}
```

### Benefits
- Improved component reusability
- Better separation of concerns
- Enhanced readability
- Simplified testing
- More maintainable code

## 6. Consistent File Naming Convention

### Solution
Establish and enforce consistent file naming conventions across the project.

### Implementation
```
# File Naming Conventions

1. React Components:
   - Use PascalCase for component files
   - Use .tsx extension for TypeScript components
   - Example: Button.tsx, ProfileCard.tsx

2. Hooks:
   - Prefix with "use"
   - Use camelCase
   - Example: useAuth.ts, useFestival.ts

3. Utility Functions:
   - Use camelCase
   - Group related utilities in descriptive files
   - Example: dateFormatters.ts, validators.ts

4. Types and Interfaces:
   - Group in types.ts files within feature folders
   - Or use dedicated files for complex types
   - Example: festivalTypes.ts, authTypes.ts

5. Index Files:
   - Use index.ts for re-exporting components/functions
   - Example: features/auth/components/index.ts
```

### Benefits
- Improved file discoverability
- Consistent import patterns
- Clear file purpose from naming
- Better developer experience
- Reduced confusion

## 7. File Size Limits

### Solution
Enforce a maximum file size of 500 lines and split larger files into smaller, focused modules.

### Implementation
```typescript
// Before: Large utility file with 1000+ lines
// src/utils/helpers.ts
export function formatDate() { /* ... */ }
export function validateEmail() { /* ... */ }
export function calculateTotal() { /* ... */ }
// ... 20+ more unrelated functions

// After: Split into domain-specific utility files
// src/lib/date/formatters.ts
export function formatDate() { /* ... */ }
export function formatDateRange() { /* ... */ }
export function formatTime() { /* ... */ }

// src/lib/validation/validators.ts
export function validateEmail() { /* ... */ }
export function validatePassword() { /* ... */ }
export function validateUsername() { /* ... */ }

// src/lib/calculations/financial.ts
export function calculateTotal() { /* ... */ }
export function calculateTax() { /* ... */ }
export function calculateDiscount() { /* ... */ }
```

### Benefits
- Improved code readability
- Better file organization
- Easier navigation
- Reduced merge conflicts
- Enhanced maintainability
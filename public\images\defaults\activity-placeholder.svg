<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="url(#gradient)"/>
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="200" cy="120" r="40" fill="white" fill-opacity="0.2"/>
  <path d="M180 100 L220 100 L210 110 L190 110 Z" fill="white" fill-opacity="0.3"/>
  <path d="M180 130 L220 130 L210 140 L190 140 Z" fill="white" fill-opacity="0.3"/>
  <circle cx="200" cy="120" r="15" fill="white" fill-opacity="0.5"/>
  <text x="200" y="180" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="500">
    Activity
  </text>
  <text x="200" y="200" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" opacity="0.8">
    Image Loading...
  </text>
</svg>

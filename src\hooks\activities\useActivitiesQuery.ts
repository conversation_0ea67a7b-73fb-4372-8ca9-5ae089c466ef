/**
 * Unified Activities Query Hook - Single Source of Truth
 * 
 * This hook consolidates all activities data fetching using React Query
 * and the unified-data-service.ts. It replaces all duplicate fetching patterns
 * and provides proper caching, deduplication, and cache invalidation.
 * 
 * @module useActivitiesQuery
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useCallback } from 'react';
import { unifiedDataService, type Activity, type ActivityFilters } from '@/lib/data/unified-data-service';
import { useAuth } from '@/providers/ConsolidatedAuthProvider';
import { supabase } from '@/lib/supabase';
import { toast } from 'react-hot-toast';

// ============================================================================
// QUERY KEYS - Centralized for consistency
// ============================================================================

export const ACTIVITIES_QUERY_KEYS = {
  all: ['activities'] as const,
  lists: () => [...ACTIVITIES_QUERY_KEYS.all, 'list'] as const,
  list: (filters?: ActivityFilters) => [...ACTIVITIES_QUERY_KEYS.lists(), filters] as const,
  details: () => [...ACTIVITIES_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...ACTIVITIES_QUERY_KEYS.details(), id] as const,
  counts: () => [...ACTIVITIES_QUERY_KEYS.all, 'counts'] as const,
  participants: () => [...ACTIVITIES_QUERY_KEYS.all, 'participants'] as const,
  userActivities: (userId: string) => [...ACTIVITIES_QUERY_KEYS.all, 'user', userId] as const,
} as const;

// ============================================================================
// MAIN ACTIVITIES HOOK
// ============================================================================

/**
 * Unified hook for fetching activities with React Query caching
 * Replaces: useOptimizedQuery, useActivities, direct Supabase calls
 */
export function useActivitiesQuery(filters?: ActivityFilters) {
  return useQuery({
    queryKey: ACTIVITIES_QUERY_KEYS.list(filters),
    queryFn: async () => {
      console.log('🔄 useActivitiesQuery: Fetching activities with filters:', filters);
      const activities = await unifiedDataService.getActivities(filters);
      console.log('✅ useActivitiesQuery: Fetched', activities.length, 'activities');
      return activities;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
}

/**
 * Hook for fetching a single activity by ID
 */
export function useActivityQuery(id: string) {
  return useQuery({
    queryKey: ACTIVITIES_QUERY_KEYS.detail(id),
    queryFn: async () => {
      if (!id) throw new Error('Activity ID is required');
      console.log('🔄 useActivityQuery: Fetching activity:', id);
      const activity = await unifiedDataService.getActivityById(id);
      console.log('✅ useActivityQuery: Fetched activity:', activity?.title);
      return activity;
    },
    enabled: !!id,
    staleTime: 1000 * 60 * 10, // 10 minutes for individual activities
    gcTime: 1000 * 60 * 60, // 1 hour
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook for getting activity counts by type (for filter badges)
 * This is optimized to calculate counts from cached activities data
 */
export function useActivityCounts(filters?: ActivityFilters) {
  const { data: activities = [] } = useActivitiesQuery(filters);

  return useQuery({
    queryKey: [...ACTIVITIES_QUERY_KEYS.counts(), filters],
    queryFn: () => {
      console.log('🔄 useActivityCounts: Calculating counts from', activities.length, 'activities');
      
      const counts = {
        meetup: activities.filter(a => a.type === 'meetup').length,
        daily: activities.filter(a => ['workshop', 'social', 'food', 'other'].includes(a.type)).length,
        compete: activities.filter(a => ['game', 'performance'].includes(a.type)).length,
        later: activities.filter(a => a.start_date ? new Date(a.start_date) > new Date() : false).length,
        total: activities.length
      };

      console.log('✅ useActivityCounts: Calculated counts:', counts);
      return counts;
    },
    enabled: activities.length > 0,
    staleTime: 1000 * 60 * 2, // 2 minutes for counts
    gcTime: 1000 * 60 * 10, // 10 minutes
  });
}

/**
 * Hook for user's activities (joined/favorited)
 */
export function useUserActivitiesQuery() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ACTIVITIES_QUERY_KEYS.userActivities(user?.id || ''),
    queryFn: async () => {
      if (!user?.id) return [];
      console.log('🔄 useUserActivitiesQuery: Fetching user activities for:', user.id);
      // This would use a method from unifiedDataService for user-specific activities
      // For now, we'll use the general activities and filter client-side
      const activities = await unifiedDataService.getActivities();
      console.log('✅ useUserActivitiesQuery: Fetched user activities');
      return activities;
    },
    enabled: !!user?.id,
    staleTime: 1000 * 60 * 3, // 3 minutes
    gcTime: 1000 * 60 * 15, // 15 minutes
  });
}

/**
 * Batched hook for user interaction status across multiple activities
 * Optimizes performance by fetching all user interactions in a single query
 * Maintains compatibility with existing useUserInteractions pattern
 */
export function useBatchedUserInteractions(activityIds: string[]) {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['user-interactions-batch', user?.id, activityIds.sort().join(',')],
    queryFn: async () => {
      if (!user?.id || activityIds.length === 0) return {};

      console.log('🔄 useBatchedUserInteractions: Fetching interactions for', activityIds.length, 'activities');

      // Batch query for all user interactions
      const { data: participants, error: participantsError } = await supabase
        .from('activity_participants')
        .select('activity_id, status, attendance_status')
        .eq('user_id', user.id)
        .in('activity_id', activityIds);

      if (participantsError) {
        console.error('Error fetching batch participants:', participantsError);
        return {};
      }

      const { data: favorites, error: favoritesError } = await supabase
        .from('user_favorites')
        .select('activity_id')
        .eq('user_id', user.id)
        .in('activity_id', activityIds);

      if (favoritesError) {
        console.error('Error fetching batch favorites:', favoritesError);
        return {};
      }

      // Transform to lookup object for easy access
      const interactions: Record<string, {
        is_participant: boolean;
        is_favorite: boolean;
        attendance_status?: string;
      }> = {};

      // Initialize all activities
      activityIds.forEach(id => {
        interactions[id] = {
          is_participant: false,
          is_favorite: false
        };
      });

      // Populate participant data
      participants?.forEach(p => {
        if (interactions[p.activity_id]) {
          interactions[p.activity_id].is_participant = true;
          interactions[p.activity_id].attendance_status = p.attendance_status;
        }
      });

      // Populate favorite data
      favorites?.forEach(f => {
        if (interactions[f.activity_id]) {
          interactions[f.activity_id].is_favorite = true;
        }
      });

      console.log('✅ useBatchedUserInteractions: Fetched interactions for', Object.keys(interactions).length, 'activities');
      return interactions;
    },
    enabled: !!user?.id && activityIds.length > 0,
    staleTime: 1000 * 60 * 2, // 2 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
  });
}

// ============================================================================
// CACHE INVALIDATION UTILITIES
// ============================================================================

/**
 * Hook for cache invalidation utilities
 */
export function useActivitiesCacheUtils() {
  const queryClient = useQueryClient();

  const invalidateActivities = useCallback(async () => {
    console.log('🔄 Invalidating activities cache...');
    await queryClient.invalidateQueries({ queryKey: ACTIVITIES_QUERY_KEYS.all });
    console.log('✅ Activities cache invalidated');
  }, [queryClient]);

  const invalidateActivityCounts = useCallback(async () => {
    console.log('🔄 Invalidating activity counts cache...');
    await queryClient.invalidateQueries({ queryKey: ACTIVITIES_QUERY_KEYS.counts() });
    console.log('✅ Activity counts cache invalidated');
  }, [queryClient]);

  const refreshActivities = useCallback(async () => {
    console.log('🔄 Refreshing activities data...');
    await queryClient.refetchQueries({ queryKey: ACTIVITIES_QUERY_KEYS.all });
    console.log('✅ Activities data refreshed');
    toast.success('Activities refreshed!');
  }, [queryClient]);

  const clearActivitiesCache = useCallback(() => {
    console.log('🔄 Clearing activities cache...');
    queryClient.removeQueries({ queryKey: ACTIVITIES_QUERY_KEYS.all });
    console.log('✅ Activities cache cleared');
  }, [queryClient]);

  return {
    invalidateActivities,
    invalidateActivityCounts,
    refreshActivities,
    clearActivitiesCache,
  };
}

// ============================================================================
// MUTATION HOOKS (for future use)
// ============================================================================

/**
 * Hook for joining/leaving activities
 */
export function useActivityParticipationMutation() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async ({ activityId, action }: { activityId: string; action: 'join' | 'leave' }) => {
      if (!user?.id) throw new Error('User must be authenticated');
      
      console.log(`🔄 ${action}ing activity:`, activityId);
      // This would use unifiedDataService methods for participation
      // For now, this is a placeholder
      return { activityId, action, userId: user.id };
    },
    onSuccess: (data) => {
      console.log('✅ Activity participation updated:', data);
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ACTIVITIES_QUERY_KEYS.participants() });
      queryClient.invalidateQueries({ queryKey: ACTIVITIES_QUERY_KEYS.userActivities(user?.id || '') });
      toast.success(`Successfully ${data.action}ed activity!`);
    },
    onError: (error) => {
      console.error('❌ Activity participation error:', error);
      toast.error('Failed to update activity participation');
    },
  });
}

// ============================================================================
// LEGACY COMPATIBILITY
// ============================================================================

/**
 * Legacy compatibility hook that matches the old useActivities interface
 * This allows gradual migration from the old hook
 */
export function useActivitiesLegacy(filters?: ActivityFilters) {
  const query = useActivitiesQuery(filters);
  
  return {
    data: query.data || [],
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch,
    // Legacy properties for backward compatibility
    activities: query.data || [],
    activitiesWithDetails: query.data || [],
  };
}

# Festival Family Migration Execution Plan

## 🎯 PROPER MIGRATION ORDER

Based on the timestamps in the migration files, here's the correct order to execute them:

### **Phase 1: Core Foundation (2024 Migrations)**
```sql
-- 1. Initial Schema (Core tables: profiles, festivals, events, activities, groups)
-- File: 20240322000000_initial_schema.sql
-- Status: ✅ Should already be applied

-- 2. Create Admin User
-- File: 20240323000000_create_admin.sql  
-- Status: ✅ Should already be applied

-- 3. Verify Admin Setup
-- File: 20240323000001_verify_admin.sql
-- Status: ✅ Should already be applied
```

### **Phase 2: Schema Updates (February 2025)**
```sql
-- 4. Restructure Schema
-- File: 20250213000000_restructure_schema.sql
-- Status: ❓ May need to be applied

-- 5. Update Activities Schema  
-- File: 20250214000000_update_activities_schema.sql
-- Status: ❓ May need to be applied

-- 6. Fix Activity Types
-- File: 20250215000000_fix_activity_types.sql
-- Status: ❓ May need to be applied
```

### **Phase 3: Feature Additions (February 2025)**
```sql
-- 7. Add Chat Tables
-- File: 20250216000000_add_chat_tables.sql
-- Status: ❓ May need to be applied

-- 8. Fix Privilege Escalation (IMPORTANT SECURITY)
-- File: 20250216000000_fix_privilege_escalation.sql
-- Status: ❓ May need to be applied

-- 9. Add Activity Coordination
-- File: 20250217000000_add_activity_coordination.sql
-- Status: ❓ May need to be applied

-- 10. Add Smart Group Formation
-- File: 20250218000000_add_smart_group_formation.sql
-- Status: ❓ May need to be applied
```

### **Phase 4: Security Fixes (June 2025)**
```sql
-- 11. Server Side XSS Protection (Initial)
-- File: 20250604000000_server_side_xss_protection.sql
-- Status: ❓ May need to be applied

-- 12. Server Side XSS Protection (Fixed)
-- File: 20250604000001_server_side_xss_protection_fixed.sql
-- Status: ❓ May need to be applied

-- 13. Privilege Escalation Fix (Simplified)
-- File: 20250604000002_privilege_escalation_fix_simplified.sql
-- Status: ❓ May need to be applied

-- 14. Privilege Escalation (WORKING VERSION)
-- File: 20250604000003_privilege_escalation_WORKING.sql
-- Status: ❓ May need to be applied

-- 15. XSS Protection (WORKING VERSION)
-- File: 20250604000004_xss_protection_WORKING.sql
-- Status: ❓ May need to be applied

-- 16. Fix RLS Recursion
-- File: 20250604000005_fix_rls_recursion.sql
-- Status: ❓ May need to be applied

-- 17. Create Group System Foundation
-- File: 20250604000006_create_group_system_foundation.sql
-- Status: ❓ May need to be applied

-- 18. Fix All RLS Recursion Issues
-- File: 20250604000007_fix_all_rls_recursion_issues.sql
-- Status: ❓ May need to be applied

-- 19. Create Announcements System
-- File: 20250604000008_create_announcements_system.sql
-- Status: ❓ May need to be applied

-- 20. Complete RLS Fix
-- File: 20250604000009_complete_rls_fix.sql
-- Status: ❓ May need to be applied

-- 21. Unified Content Management (LATEST)
-- File: 20250604000010_unified_content_management.sql
-- Status: ❓ May need to be applied
```

## 🚨 CRITICAL NOTES

### **Duplicate Migration Issue**
There are TWO migrations with the same timestamp:
- `20250216000000_add_chat_tables.sql`
- `20250216000000_fix_privilege_escalation.sql`

This suggests one may have overwritten the other. We need to check both.

### **Security Priority**
The privilege escalation and XSS protection migrations are CRITICAL for security:
- `20250604000003_privilege_escalation_WORKING.sql`
- `20250604000004_xss_protection_WORKING.sql`
- `20250604000007_fix_all_rls_recursion_issues.sql`

## 📋 RECOMMENDED EXECUTION STRATEGY

### **Option 1: Safe Sequential Execution**
Run each migration file in chronological order in Supabase SQL Editor:

1. Check which migrations have already been applied
2. Start from the first unapplied migration
3. Execute one by one, checking for errors
4. Verify each migration before proceeding

### **Option 2: Consolidated Approach (RECOMMENDED)**
Since we have many migrations and some may conflict, use our fixed consolidated script:

1. **First**: Run `apply-missing-migrations.sql` (now fixed)
2. **Then**: Check what's missing and apply specific migrations
3. **Finally**: Verify all tables and functions exist

## 🔧 EXECUTION COMMANDS

### **For Supabase Dashboard SQL Editor:**

```sql
-- Step 1: Check current migration status
SELECT * FROM supabase_migrations.schema_migrations ORDER BY version;

-- Step 2: Check which tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Step 3: Check which functions exist
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_type = 'FUNCTION'
ORDER BY routine_name;
```

### **For Missing Tables:**
If tables are missing after running migrations, use our fixed `apply-missing-migrations.sql`.

## ✅ VERIFICATION CHECKLIST

After running migrations, verify these tables exist:
- [ ] `content_management`
- [ ] `user_preferences` 
- [ ] `emergency_contacts`
- [ ] `safety_information`
- [ ] `announcements` (enhanced)
- [ ] `tips` (enhanced)
- [ ] `faqs` (enhanced)
- [ ] `chat_rooms`
- [ ] `chat_room_members`
- [ ] `chat_messages`

And these functions exist:
- [ ] `is_admin()`
- [ ] `is_super_admin()`
- [ ] `is_content_admin()`
- [ ] `can_manage_groups()`

## 🎯 NEXT STEPS

1. **Check Migration Status**: Run verification queries in Supabase
2. **Apply Missing Migrations**: Use chronological order or consolidated script
3. **Test Database**: Use our HTML test files
4. **Verify Admin Functions**: Test with Playwright automation

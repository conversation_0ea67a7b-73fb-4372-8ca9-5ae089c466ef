/**
 * Import.meta mock for Jest testing
 * 
 * This file provides a mock implementation of import.meta for Jest tests
 * to handle Vite's import.meta.env syntax in test environment.
 */

export default {
  env: {
    VITE_SUPABASE_URL: process.env.VITE_SUPABASE_URL || 'https://test.supabase.co',
    VITE_SUPABASE_ANON_KEY: process.env.VITE_SUPABASE_ANON_KEY || 'test-anon-key',
    VITE_SENTRY_DSN: process.env.VITE_SENTRY_DSN || '',
    VITE_VERCEL_ANALYTICS_ID: process.env.VITE_VERCEL_ANALYTICS_ID || '',
    DEV: process.env.NODE_ENV !== 'production',
    MODE: process.env.NODE_ENV || 'test',
    PROD: process.env.NODE_ENV === 'production',
    SSR: false,
    BASE_URL: '/',
  },
  hot: {
    accept: () => {},
    dispose: () => {},
    decline: () => {},
    invalidate: () => {},
    on: () => {},
    off: () => {},
    send: () => {},
  },
  glob: () => ({}),
  url: 'file:///test',
};

// Also provide as named export for different import patterns
export const meta = {
  env: {
    VITE_SUPABASE_URL: process.env.VITE_SUPABASE_URL || 'https://test.supabase.co',
    VITE_SUPABASE_ANON_KEY: process.env.VITE_SUPABASE_ANON_KEY || 'test-anon-key',
    VITE_SENTRY_DSN: process.env.VITE_SENTRY_DSN || '',
    VITE_VERCEL_ANALYTICS_ID: process.env.VITE_VERCEL_ANALYTICS_ID || '',
    DEV: process.env.NODE_ENV !== 'production',
    MODE: process.env.NODE_ENV || 'test',
    PROD: process.env.NODE_ENV === 'production',
    SSR: false,
    BASE_URL: '/',
  },
};

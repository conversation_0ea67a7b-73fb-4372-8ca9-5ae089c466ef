-- Fix All RLS Recursion Issues
-- This migration resolves infinite recursion errors in group_members, group_activities, 
-- and chat system tables by creating non-recursive helper functions and policies

-- ============================================================================
-- DROP PROBLEMATIC POLICIES
-- ============================================================================

-- Drop all policies that cause recursion in group_members
DROP POLICY IF EXISTS "Users can view group members for groups they can see" ON group_members;
DROP POLICY IF EXISTS "Group admins can manage group members" ON group_members;
DROP POLICY IF EXISTS "Users can join groups they're invited to" ON group_members;
DROP POLICY IF EXISTS "Users can leave groups they're members of" ON group_members;

-- Drop all policies that cause recursion in group_activities
DROP POLICY IF EXISTS "Users can view group activities for groups they can see" ON group_activities;
DROP POLICY IF EXISTS "Group members can manage group activities" ON group_activities;

-- Drop all policies that cause recursion in chat tables
DROP POLICY IF EXISTS "Users can view chat rooms they belong to" ON chat_rooms;
DROP POLICY IF EXISTS "Users can view chat room members for rooms they belong to" ON chat_room_members;
DROP POLICY IF EXISTS "Users can view messages in rooms they belong to" ON chat_messages;

-- ============================================================================
-- CREATE NON-RECURSIVE HELPER FUNCTIONS
-- ============================================================================

-- Helper function to check if user is member of a group (non-recursive)
CREATE OR REPLACE FUNCTION is_group_member(group_id UUID, user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  membership_exists BOOLEAN;
BEGIN
  -- Direct query without RLS to avoid recursion
  SELECT EXISTS (
    SELECT 1 FROM group_members 
    WHERE group_members.group_id = is_group_member.group_id 
    AND group_members.user_id = is_group_member.user_id
    AND left_at IS NULL
  ) INTO membership_exists;
  
  RETURN membership_exists;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;

-- Helper function to check if user is group admin (non-recursive)
CREATE OR REPLACE FUNCTION is_group_admin(group_id UUID, user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_admin_member BOOLEAN;
BEGIN
  -- Direct query without RLS to avoid recursion
  SELECT EXISTS (
    SELECT 1 FROM group_members 
    WHERE group_members.group_id = is_group_admin.group_id 
    AND group_members.user_id = is_group_admin.user_id
    AND role IN ('admin', 'moderator')
    AND left_at IS NULL
  ) INTO is_admin_member;
  
  RETURN is_admin_member;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;

-- Helper function to check if user is chat room member (non-recursive)
CREATE OR REPLACE FUNCTION is_chat_room_member(room_id UUID, user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  membership_exists BOOLEAN;
BEGIN
  -- Direct query without RLS to avoid recursion
  SELECT EXISTS (
    SELECT 1 FROM chat_room_members 
    WHERE chat_room_members.room_id = is_chat_room_member.room_id 
    AND chat_room_members.profile_id = is_chat_room_member.user_id
  ) INTO membership_exists;
  
  RETURN membership_exists;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;

-- Helper function to check if group is public (non-recursive)
CREATE OR REPLACE FUNCTION is_group_public(group_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_public BOOLEAN;
BEGIN
  -- Direct query without RLS to avoid recursion
  SELECT NOT COALESCE(is_private, false) INTO is_public
  FROM groups 
  WHERE id = group_id;
  
  RETURN COALESCE(is_public, false);
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;

-- ============================================================================
-- CREATE NON-RECURSIVE RLS POLICIES
-- ============================================================================

-- Group members policies (non-recursive)
CREATE POLICY "Users can view group members for public groups or groups they belong to" 
    ON group_members FOR SELECT 
    USING (
        is_group_public(group_id) OR 
        is_group_member(group_id, auth.uid()) OR
        is_admin(auth.uid())
    );

CREATE POLICY "Group admins can manage group members" 
    ON group_members FOR ALL 
    USING (
        is_group_admin(group_id, auth.uid()) OR
        is_admin(auth.uid())
    );

CREATE POLICY "Users can join public groups" 
    ON group_members FOR INSERT 
    WITH CHECK (
        user_id = auth.uid() AND (
            is_group_public(group_id) OR
            is_admin(auth.uid())
        )
    );

CREATE POLICY "Users can leave groups they're members of" 
    ON group_members FOR UPDATE
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can remove themselves from groups" 
    ON group_members FOR DELETE 
    USING (
        user_id = auth.uid() OR
        is_group_admin(group_id, auth.uid()) OR
        is_admin(auth.uid())
    );

-- Group activities policies (non-recursive)
CREATE POLICY "Users can view group activities for accessible groups" 
    ON group_activities FOR SELECT 
    USING (
        is_group_public(group_id) OR 
        is_group_member(group_id, auth.uid()) OR
        is_admin(auth.uid())
    );

CREATE POLICY "Group admins can manage group activities" 
    ON group_activities FOR ALL 
    USING (
        is_group_admin(group_id, auth.uid()) OR
        is_admin(auth.uid())
    );

-- Chat rooms policies (non-recursive)
CREATE POLICY "Users can view chat rooms they belong to or public rooms" 
    ON chat_rooms FOR SELECT 
    USING (
        is_chat_room_member(id, auth.uid()) OR
        created_by = auth.uid() OR
        NOT is_group OR
        is_admin(auth.uid())
    );

CREATE POLICY "Authenticated users can create chat rooms" 
    ON chat_rooms FOR INSERT 
    WITH CHECK (auth.role() = 'authenticated' AND created_by = auth.uid());

CREATE POLICY "Room creators and admins can update chat rooms" 
    ON chat_rooms FOR UPDATE 
    USING (
        created_by = auth.uid() OR
        is_admin(auth.uid())
    );

-- Chat room members policies (non-recursive)
CREATE POLICY "Users can view chat room members for rooms they belong to"
    ON chat_room_members FOR SELECT
    USING (
        is_chat_room_member(room_id, auth.uid()) OR
        profile_id = auth.uid() OR
        is_admin(auth.uid())
    );

CREATE POLICY "Users can join chat rooms"
    ON chat_room_members FOR INSERT
    WITH CHECK (
        profile_id = auth.uid() OR
        is_admin(auth.uid())
    );

CREATE POLICY "Users can update their own chat room membership"
    ON chat_room_members FOR UPDATE
    USING (profile_id = auth.uid())
    WITH CHECK (profile_id = auth.uid());

CREATE POLICY "Users can leave chat rooms"
    ON chat_room_members FOR DELETE
    USING (
        profile_id = auth.uid() OR
        is_admin(auth.uid())
    );

-- Chat messages policies (non-recursive)
CREATE POLICY "Users can view messages in rooms they belong to"
    ON chat_messages FOR SELECT
    USING (
        is_chat_room_member(room_id, auth.uid()) OR
        sender_id = auth.uid() OR
        is_admin(auth.uid())
    );

CREATE POLICY "Room members can send messages"
    ON chat_messages FOR INSERT
    WITH CHECK (
        sender_id = auth.uid() AND
        is_chat_room_member(room_id, auth.uid())
    );

CREATE POLICY "Users can update their own messages"
    ON chat_messages FOR UPDATE
    USING (sender_id = auth.uid())
    WITH CHECK (sender_id = auth.uid());

CREATE POLICY "Users can delete their own messages or admins can delete any"
    ON chat_messages FOR DELETE
    USING (
        sender_id = auth.uid() OR
        is_admin(auth.uid())
    );

-- ============================================================================
-- GRANT PERMISSIONS
-- ============================================================================

-- Grant execute permissions on helper functions
GRANT EXECUTE ON FUNCTION is_group_member TO authenticated;
GRANT EXECUTE ON FUNCTION is_group_admin TO authenticated;
GRANT EXECUTE ON FUNCTION is_chat_room_member TO authenticated;
GRANT EXECUTE ON FUNCTION is_group_public TO authenticated;

-- ============================================================================
-- VALIDATION AND TESTING
-- ============================================================================

-- Test that all helper functions work
DO $$
DECLARE
  test_group_id UUID;
  test_user_id UUID;
  test_room_id UUID;
BEGIN
  -- Test helper functions with dummy data
  SELECT id INTO test_group_id FROM groups LIMIT 1;
  SELECT id INTO test_user_id FROM profiles LIMIT 1;
  SELECT id INTO test_room_id FROM chat_rooms LIMIT 1;
  
  IF test_group_id IS NOT NULL THEN
    RAISE NOTICE 'Testing is_group_public function...';
    PERFORM is_group_public(test_group_id);
    RAISE NOTICE 'SUCCESS: is_group_public function working';
  END IF;
  
  IF test_group_id IS NOT NULL AND test_user_id IS NOT NULL THEN
    RAISE NOTICE 'Testing is_group_member function...';
    PERFORM is_group_member(test_group_id, test_user_id);
    RAISE NOTICE 'SUCCESS: is_group_member function working';
    
    RAISE NOTICE 'Testing is_group_admin function...';
    PERFORM is_group_admin(test_group_id, test_user_id);
    RAISE NOTICE 'SUCCESS: is_group_admin function working';
  END IF;
  
  IF test_room_id IS NOT NULL AND test_user_id IS NOT NULL THEN
    RAISE NOTICE 'Testing is_chat_room_member function...';
    PERFORM is_chat_room_member(test_room_id, test_user_id);
    RAISE NOTICE 'SUCCESS: is_chat_room_member function working';
  END IF;
  
  RAISE NOTICE 'All helper functions validated successfully';
END $$;

-- Test table accessibility
DO $$
DECLARE
  group_members_count INTEGER;
  group_activities_count INTEGER;
  chat_rooms_count INTEGER;
  chat_messages_count INTEGER;
BEGIN
  -- Test group_members table
  SELECT COUNT(*) INTO group_members_count FROM group_members LIMIT 1;
  RAISE NOTICE 'SUCCESS: group_members table accessible';
  
  -- Test group_activities table
  SELECT COUNT(*) INTO group_activities_count FROM group_activities LIMIT 1;
  RAISE NOTICE 'SUCCESS: group_activities table accessible';
  
  -- Test chat_rooms table
  SELECT COUNT(*) INTO chat_rooms_count FROM chat_rooms LIMIT 1;
  RAISE NOTICE 'SUCCESS: chat_rooms table accessible';
  
  -- Test chat_messages table
  SELECT COUNT(*) INTO chat_messages_count FROM chat_messages LIMIT 1;
  RAISE NOTICE 'SUCCESS: chat_messages table accessible';
  
  RAISE NOTICE 'All tables now accessible without recursion errors';
END $$;

-- Log successful migration
DO $$
BEGIN
  RAISE NOTICE 'SUCCESS: All RLS recursion issues fixed';
  RAISE NOTICE 'INFO: Non-recursive helper functions created';
  RAISE NOTICE 'INFO: All group and chat tables now accessible';
  RAISE NOTICE 'INFO: RLS policies provide proper security without recursion';
  RAISE NOTICE 'INFO: Ready for complete frontend-backend integration';
END $$;

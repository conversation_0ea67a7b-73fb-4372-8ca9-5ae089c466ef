{"errors": [], "warnings": ["⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition."], "logs": ["debug: [vite] connecting...", "debug: [vite] connected.", "info: %cDownload the React DevTools for a better development experience: https://reactjs.org/link/react-devtools font-weight:bold", "log: 🚀 Initializing Festival Family Supabase Client", "log: 📍 Environment: development", "log: 🔗 Supabase URL: https://ealstndyhwjwipzlrxmg.supabase.co", "log: Sentry disabled in development mode", "log: 🚀 Initializing mobile optimizations...", "log: ✅ Performance optimizations initialized", "log: ✅ Accessibility features initialized", "log: ✅ Mobile UX features initialized", "log: 🎉 Mobile optimizations complete: {performance: true, accessibility: true, mobileUX: true, reducedMotion: false, touchDevice: false}", "log: 🎉 Mobile optimizations ready", "log: ConnectionStatus: Checking Supabase connection...", "log: 🔍 Testing Supabase connection...", "log: ConsolidatedAuthProvider: Initializing authentication...", "log: Auth provider initialized with host: localhost:5173, origin: http://localhost:5173", "log: Getting initial session...", "log: 🔒 Security Provider: Development mode - skipping HTTP-only headers", "log: 📝 Note: CSP and X-Frame-Options should be configured in your dev server for testing", "log: Cleaning up auth listener", "log: ConnectionStatus: Checking Supabase connection...", "log: 🔍 Testing Supabase connection...", "log: ConsolidatedAuthProvider: Initializing authentication...", "log: Auth provider initialized with host: localhost:5173, origin: http://localhost:5173", "log: Getting initial session...", "log: 🔒 Security Provider: Development mode - skipping HTTP-only headers", "log: 📝 Note: CSP and X-Frame-Options should be configured in your dev server for testing", "log: Initial session retrieved: none", "log: Auth state changed: signed out", "log: Initial session retrieved: none", "log: Auth state changed: signed out", "log: Auth event: INITIAL_SESSION without session", "log: Auth state changed: signed out", "log: Auth state change completed, loading set to false", "log: Auth state change completed, loading set to false", "log: Auth state change completed, loading set to false", "log: %c[Vercel Web Analytics]%c Debug mode is enabled by default in development. No requests will be sent to the server. color: rgb(120, 120, 120) color: inherit", "log: %c[Vercel Web Analytics]%c [pageview] http://localhost:5173/ color: rgb(120, 120, 120) color: inherit {o: http://localhost:5173/, sv: 0.1.3, sdkn: @vercel/analytics/react, sdkv: 1.5.0, ts: 1749166004361}", "log: SimpleNavigation - Auth State: {user: false, isAdmin: false, loading: false, userEmail: undefined}", "log: ModernBottomNav - Auth State: {user: false, isAdmin: false, loading: false, userEmail: undefined}", "log: SimpleNavigation - Auth State: {user: false, isAdmin: false, loading: false, userEmail: undefined}", "log: ModernBottomNav - Auth State: {user: false, isAdmin: false, loading: false, userEmail: undefined}", "log: ✅ Supabase connection successful", "log: ConnectionStatus: Connection result: {success: true, message: Connection successful, data: Array(0)}", "log: ConnectionStatus: Connection successful", "log: ✅ Supabase connection successful", "log: ConnectionStatus: Connection result: {success: true, message: Connection successful, data: Array(0)}", "log: ConnectionStatus: Connection successful"], "timestamp": "2025-06-05T23:26:46.713Z"}
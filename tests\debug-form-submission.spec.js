/**
 * Debug Form Submission Test
 * 
 * This test specifically debugs form submission issues
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

test('Debug Form Submission', async ({ page }) => {
  console.log('🔍 Debugging form submission...');
  
  // Capture all console messages and network requests
  const consoleLogs = [];
  const networkRequests = [];
  const networkResponses = [];
  
  page.on('console', msg => {
    consoleLogs.push(`${msg.type()}: ${msg.text()}`);
  });
  
  page.on('request', request => {
    if (request.url().includes('supabase') || request.method() === 'POST') {
      networkRequests.push({
        method: request.method(),
        url: request.url(),
        headers: request.headers(),
        postData: request.postData()
      });
    }
  });
  
  page.on('response', response => {
    if (response.url().includes('supabase') || response.status() >= 400) {
      networkResponses.push({
        status: response.status(),
        url: response.url(),
        statusText: response.statusText()
      });
    }
  });
  
  const loginSuccess = await loginAsAdmin(page);
  console.log(`Login success: ${loginSuccess}`);
  
  if (loginSuccess) {
    // Navigate to activity form
    await page.goto('/admin/activities/new');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    console.log('📝 Filling out activity form...');
    
    // Fill out the form with test data
    const testData = {
      name: 'Debug Test Activity ' + Date.now(),
      description: 'This is a debug test activity to identify form submission issues.',
      type: 'workshop',
      location: 'Debug Test Location',
      capacity: '25'
    };
    
    // Fill form fields
    await page.fill('input[name="name"]', testData.name);
    await page.fill('textarea[name="description"]', testData.description);
    await page.fill('input[name="type"]', testData.type);
    await page.fill('input[name="location"]', testData.location);
    await page.fill('input[name="capacity"]', testData.capacity);
    
    await page.screenshot({ path: 'test-results/debug-form-filled.png', fullPage: true });
    
    console.log('✅ Form filled with test data');
    
    // Clear previous network logs
    networkRequests.length = 0;
    networkResponses.length = 0;
    
    // Submit the form
    console.log('🚀 Submitting form...');
    await page.click('button[type="submit"]');
    
    // Wait for submission to complete
    await page.waitForTimeout(5000);
    
    const currentUrl = page.url();
    console.log(`Current URL after submission: ${currentUrl}`);
    
    await page.screenshot({ path: 'test-results/debug-form-submitted.png', fullPage: true });
    
    // Check for success indicators
    const hasSuccessToast = await page.locator('.toast, [data-testid*="toast"], [class*="toast"]').count() > 0;
    const wasRedirected = currentUrl.includes('/admin/activities') && !currentUrl.includes('/new');
    const hasErrorMessage = await page.locator('text="Error", text="Failed"').count() > 0;
    
    console.log(`Has success toast: ${hasSuccessToast}`);
    console.log(`Was redirected: ${wasRedirected}`);
    console.log(`Has error message: ${hasErrorMessage}`);
    
    // Print network activity
    console.log('\n🌐 Network Requests:');
    networkRequests.forEach((req, i) => {
      console.log(`  ${i + 1}. ${req.method} ${req.url}`);
      if (req.postData) {
        console.log(`     Data: ${req.postData.substring(0, 200)}...`);
      }
    });
    
    console.log('\n📡 Network Responses:');
    networkResponses.forEach((res, i) => {
      console.log(`  ${i + 1}. ${res.status} ${res.statusText} - ${res.url}`);
    });
    
    // Print relevant console logs
    console.log('\n📋 Console logs (errors and important messages):');
    consoleLogs.filter(log => 
      log.includes('error') || 
      log.includes('Error') || 
      log.includes('saving') || 
      log.includes('submit') ||
      log.includes('supabase') ||
      log.includes('activity')
    ).forEach(log => console.log(`  ${log}`));
    
    // Check if we can see the created activity
    if (wasRedirected) {
      console.log('🔍 Checking if activity was created...');
      await page.waitForTimeout(2000);
      
      const activityVisible = await page.locator(`text="${testData.name}"`).count() > 0;
      console.log(`Activity visible in list: ${activityVisible}`);
      
      if (activityVisible) {
        console.log('✅ SUCCESS: Activity was created and is visible');
      } else {
        console.log('❌ ISSUE: Activity was not found in the list');
      }
    }
  }
});

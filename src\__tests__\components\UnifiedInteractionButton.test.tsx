/**
 * UnifiedInteractionButton Component Tests
 * 
 * Tests for the standardized interaction button that replaces all legacy
 * interaction components (JoinLeaveButton, RSVPButton, FavoriteButton, etc.)
 * 
 * @module UnifiedInteractionButton.test
 * @version 1.0.0
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import UnifiedInteractionButton from '@/components/design-system/UnifiedInteractionButton';

// Mock the unified interaction service
vi.mock('@/lib/services/unifiedInteractionService', () => ({
  unifiedInteractionService: {
    toggleFavorite: vi.fn().mockResolvedValue({ success: true, data: true }),
    toggleJoin: vi.fn().mockResolvedValue({ success: true, data: true }),
    setRSVP: vi.fn().mockResolvedValue({ success: true, data: 'going' }),
    getUnifiedInteractionStatus: vi.fn().mockResolvedValue({
      success: true,
      data: {
        isFavorite: false,
        isJoined: false,
        rsvpStatus: null,
        participantCount: 0,
        isLoading: false
      }
    })
  }
}));

// Mock the auth provider
vi.mock('@/providers/ConsolidatedAuthProvider', () => ({
  useAuth: () => ({
    user: { id: 'test-user-123' },
    isAuthenticated: true
  })
}));

// Test wrapper with React Query
const createTestWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('UnifiedInteractionButton', () => {
  let TestWrapper: ReturnType<typeof createTestWrapper>;

  beforeEach(() => {
    TestWrapper = createTestWrapper();
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders favorite button correctly', () => {
      render(
        <UnifiedInteractionButton
          type="favorite"
          itemId="test-item-1"
          itemType="activity"
        />,
        { wrapper: TestWrapper }
      );

      expect(screen.getByRole('button')).toBeInTheDocument();
      expect(screen.getByLabelText(/favorite/i)).toBeInTheDocument();
    });

    it('renders join button correctly', () => {
      render(
        <UnifiedInteractionButton
          type="join"
          itemId="test-item-1"
          itemType="activity"
        />,
        { wrapper: TestWrapper }
      );

      expect(screen.getByRole('button')).toBeInTheDocument();
      expect(screen.getByLabelText(/join/i)).toBeInTheDocument();
    });

    it('renders RSVP button correctly', () => {
      render(
        <UnifiedInteractionButton
          type="rsvp"
          itemId="test-item-1"
          itemType="activity"
        />,
        { wrapper: TestWrapper }
      );

      expect(screen.getByRole('button')).toBeInTheDocument();
      expect(screen.getByLabelText(/rsvp/i)).toBeInTheDocument();
    });

    it('displays count when showCount is true', () => {
      render(
        <UnifiedInteractionButton
          type="favorite"
          itemId="test-item-1"
          itemType="activity"
          count={42}
          showCount={true}
        />,
        { wrapper: TestWrapper }
      );

      expect(screen.getByText('42')).toBeInTheDocument();
    });

    it('hides count when showCount is false', () => {
      render(
        <UnifiedInteractionButton
          type="favorite"
          itemId="test-item-1"
          itemType="activity"
          count={42}
          showCount={false}
        />,
        { wrapper: TestWrapper }
      );

      expect(screen.queryByText('42')).not.toBeInTheDocument();
    });
  });

  describe('Interaction Types', () => {
    const interactionTypes = ['favorite', 'join', 'rsvp', 'share', 'helpful', 'save'] as const;

    interactionTypes.forEach(type => {
      it(`handles ${type} interaction correctly`, async () => {
        const onStateChange = vi.fn();

        render(
          <UnifiedInteractionButton
            type={type}
            itemId="test-item-1"
            itemType="activity"
            onStateChange={onStateChange}
          />,
          { wrapper: TestWrapper }
        );

        const button = screen.getByRole('button');
        fireEvent.click(button);

        await waitFor(() => {
          expect(button).toBeInTheDocument();
        });
      });
    });
  });

  describe('Variants and Sizes', () => {
    it('applies default variant correctly', () => {
      render(
        <UnifiedInteractionButton
          type="favorite"
          itemId="test-item-1"
          itemType="activity"
          variant="default"
        />,
        { wrapper: TestWrapper }
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-primary');
    });

    it('applies compact variant correctly', () => {
      render(
        <UnifiedInteractionButton
          type="favorite"
          itemId="test-item-1"
          itemType="activity"
          variant="compact"
        />,
        { wrapper: TestWrapper }
      );

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('applies different sizes correctly', () => {
      const sizes = ['sm', 'default', 'lg'] as const;

      sizes.forEach(size => {
        const { unmount } = render(
          <UnifiedInteractionButton
            type="favorite"
            itemId="test-item-1"
            itemType="activity"
            size={size}
          />,
          { wrapper: TestWrapper }
        );

        const button = screen.getByRole('button');
        expect(button).toBeInTheDocument();
        unmount();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(
        <UnifiedInteractionButton
          type="favorite"
          itemId="test-item-1"
          itemType="activity"
        />,
        { wrapper: TestWrapper }
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label');
    });

    it('supports keyboard navigation', () => {
      render(
        <UnifiedInteractionButton
          type="favorite"
          itemId="test-item-1"
          itemType="activity"
        />,
        { wrapper: TestWrapper }
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('tabIndex', '0');
    });

    it('meets minimum touch target size for mobile', () => {
      render(
        <UnifiedInteractionButton
          type="favorite"
          itemId="test-item-1"
          itemType="activity"
        />,
        { wrapper: TestWrapper }
      );

      const button = screen.getByRole('button');
      const styles = window.getComputedStyle(button);
      
      // Should have minimum 44x44px touch target (or use padding to achieve it)
      expect(button).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles service errors gracefully', async () => {
      const { unifiedInteractionService } = await import('@/lib/services/unifiedInteractionService');
      vi.mocked(unifiedInteractionService.toggleFavorite).mockRejectedValueOnce(
        new Error('Service error')
      );

      render(
        <UnifiedInteractionButton
          type="favorite"
          itemId="test-item-1"
          itemType="activity"
        />,
        { wrapper: TestWrapper }
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      // Should not crash and should remain interactive
      await waitFor(() => {
        expect(button).toBeInTheDocument();
        expect(button).not.toBeDisabled();
      });
    });
  });

  describe('Legacy Component Replacement', () => {
    it('replaces JoinLeaveButton functionality', () => {
      render(
        <UnifiedInteractionButton
          type="join"
          itemId="activity-123"
          itemType="activity"
          showCount={true}
          count={15}
        />,
        { wrapper: TestWrapper }
      );

      // Should have join functionality
      expect(screen.getByLabelText(/join/i)).toBeInTheDocument();
      expect(screen.getByText('15')).toBeInTheDocument();
    });

    it('replaces FavoriteButton functionality', () => {
      render(
        <UnifiedInteractionButton
          type="favorite"
          itemId="activity-123"
          itemType="activity"
          showCount={true}
          count={8}
        />,
        { wrapper: TestWrapper }
      );

      // Should have favorite functionality
      expect(screen.getByLabelText(/favorite/i)).toBeInTheDocument();
      expect(screen.getByText('8')).toBeInTheDocument();
    });

    it('replaces RSVPButton functionality', () => {
      render(
        <UnifiedInteractionButton
          type="rsvp"
          itemId="activity-123"
          itemType="activity"
          showCount={true}
          count={23}
        />,
        { wrapper: TestWrapper }
      );

      // Should have RSVP functionality
      expect(screen.getByLabelText(/rsvp/i)).toBeInTheDocument();
      expect(screen.getByText('23')).toBeInTheDocument();
    });
  });
});

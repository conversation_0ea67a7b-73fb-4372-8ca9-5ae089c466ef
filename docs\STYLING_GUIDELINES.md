# Festival Family Styling Guidelines

## 🎯 **Overview**

Festival Family uses a **completely unified styling system** built on shadcn/ui foundation with Festival Family extensions. This document provides the **permanent development methodology** for maintaining design system consistency and preventing future styling conflicts.

**🎨 VISUAL EXCELLENCE STANDARD: AuthenticatedHome 4-Card System**
The 4 colored cards in `src/pages/AuthenticatedHome.tsx` (lines 975-993, 1017-1042) represent the **PERFECT design template** for ALL components:
- ✅ **Perfect visual prominence** - Cards clearly "pop" from background
- ✅ **Consistent colored gradients** - Using design tokens properly
- ✅ **Effective glassmorphism** - Subtle backdrop blur with proper contrast
- ✅ **Clear visual hierarchy** - Easy to distinguish interactive elements

**✅ ACHIEVEMENT: Systematic Design System Implementation (June 2025)**
- **Single Source of Truth Architecture** - All colors use CSS variables from design tokens
- **Perfect Theme Compatibility** - Seamless light/dark mode switching
- **WCAG 2.1 Compliance** - Proper contrast ratios across all components
- **Zero Hardcoded Colors Policy** - Comprehensive elimination prevents future conflicts
- **Unified Component System** - BentoCard approach for consistent visual language

## 🏗️ **Architecture**

### **Single Source of Truth**
- **Foundation**: shadcn/ui CSS variables (`src/index.css`)
- **Extensions**: Festival Family semantic variables (`src/styles/design-tokens.css`)
- **Unified Components**: `src/components/design-system/` (BentoCard, UnifiedButton, etc.)
- **Maximum 2 Systems**: No additional styling systems should be introduced

### **File Structure**
```
src/
├── index.css                    # shadcn/ui foundation (PRIMARY)
├── styles/
│   ├── design-tokens.css        # Festival Family extensions
│   ├── container.css            # Layout utilities
│   └── utilities.css            # Component utilities
├── components/
│   ├── design-system/           # Unified components (BentoCard, UnifiedButton)
│   └── ui/                      # shadcn/ui base components
└── pages/                       # Reference: AuthenticatedHome.tsx (4-card standard)
```

## 🎴 **Component Conversion Guidelines**

### **MANDATORY: Use AuthenticatedHome 4-Card System as Template**
**Reference File**: `src/pages/AuthenticatedHome.tsx` (lines 975-993, 1017-1042)
- **Perfect colored gradients**: `bg-gradient-to-r from-accent/10 to-accent/5 border-accent/20`
- **Proper glassmorphism**: `variant="glassmorphism"` with backdrop blur effects
- **Clear visual hierarchy**: Icons, titles, descriptions, and actions properly structured
- **Sufficient contrast**: Components clearly "pop" from background

### **When to Use BentoCard (PRIMARY CHOICE)**
✅ **ALWAYS USE FOR:**
- **Primary content cards** (events, activities, communities, resources)
- **Dashboard widgets** (weather, alerts, stats, announcements)
- **Feature highlights** (tips, guides, recommendations)
- **Interactive content** (clickable cards with actions)
- **Any card that needs visual prominence**

✅ **BentoCard Variants:**
```tsx
// Colored gradients for primary content
<BentoCard variant="glassmorphism" className="bg-gradient-to-r from-primary/10 to-accent/5 border-primary/20" />

// Featured content with enhanced prominence
<BentoCard variant="featured" className="bg-gradient-to-r from-accent/10 to-secondary/5 border-accent/20" />

// Standard content with subtle effects
<BentoCard variant="default" className="bg-gradient-to-r from-muted/10 to-muted/5 border-muted/20" />
```

### **When to Use UnifiedCard (SECONDARY CHOICE)**
✅ **USE ONLY FOR:**
- **Simple containers** (form wrappers, basic content)
- **Non-interactive displays** (read-only information panels)
- **Legacy compatibility** (during transition period only)
- **Minimal styling needs** (when BentoCard would be over-engineering)

### **When to Use shadcn/ui Card (AVOID IN MAIN APP)**
❌ **NEVER USE FOR:**
- **Main application content** (always wrap with BentoCard or UnifiedCard)
- **User-facing components** (lacks Festival Family design language)

✅ **ONLY USE FOR:**
- **Base components** (when building new design system components)
- **Internal component construction** (within BentoCard or UnifiedCard implementation)

## 🎨 **Visual Hierarchy Standards**

### **"Pop" Factor Requirements (Based on AuthenticatedHome 4-Card Excellence)**
✅ **MANDATORY VISUAL CHARACTERISTICS:**
- **Cards must visually separate** from main background with clear distinction
- **Colored gradients required** for all primary content cards
- **Glassmorphism effects** for modern, elevated appearance using `backdrop-blur-md`
- **Consistent border treatments** using design tokens (`border-primary/20`, `border-accent/20`)
- **Sufficient contrast** for accessibility and visual clarity

### **Contrast Requirements (WCAG 2.1 Compliance)**
✅ **ACCESSIBILITY STANDARDS:**
- **Text on backgrounds**: Minimum 4.5:1 contrast ratio
- **Interactive elements**: Must clearly distinguish from background
- **Form fields**: Always light backgrounds with dark text for readability
- **Navigation elements**: Sufficient contrast for all users
- **Focus indicators**: Clear visual feedback for keyboard navigation

### **Design Token Usage Requirements (ZERO HARDCODED COLORS POLICY)**
✅ **MANDATORY IMPLEMENTATION:**
```tsx
// ✅ CORRECT - Use design tokens
className="bg-primary/10 text-primary border-primary/20"
className="bg-gradient-to-r from-accent/10 to-secondary/5"
className="text-foreground bg-background border-border"

// ❌ FORBIDDEN - Hardcoded colors
className="bg-purple-500 text-white border-purple-300"
className="bg-gradient-to-r from-blue-400 to-purple-600"
className="text-black bg-gray-100 border-gray-300"
```

✅ **SEMANTIC COLOR USAGE:**
- **Primary content**: `bg-primary/10`, `text-primary`, `border-primary/20`
- **Secondary content**: `bg-secondary/10`, `text-secondary`, `border-secondary/20`
- **Accent highlights**: `bg-accent/10`, `text-accent`, `border-accent/20`
- **Success states**: `bg-festival-success/10`, `text-festival-success`
- **Warning states**: `bg-festival-warning/10`, `text-festival-warning`
- **Error states**: `bg-destructive/10`, `text-destructive`

## 🚀 **Production Readiness Criteria**

### **Supabase Integration Standards**
✅ **REQUIRED FUNCTIONALITY:**
- **Database connectivity** verified and stable across all environments
- **User authentication** working seamlessly on all pages
- **Event management** fully functional with CRUD operations
- **Activity coordination** operational with real-time updates
- **Admin functionality** complete, secure, and role-based
- **File uploads** working with proper bucket configuration
- **Real-time subscriptions** functioning for live updates

### **Code Quality Standards (MANDATORY BEFORE PRODUCTION)**
✅ **ZERO TOLERANCE REQUIREMENTS:**
- **TypeScript compilation** with absolutely zero errors
- **No hardcoded colors** anywhere in the entire codebase
- **Consistent component usage** throughout all pages
- **Single source of truth** maintained for all styling decisions
- **Design system compliance** verified through comprehensive audit

### **Performance & Accessibility Standards**
✅ **PRODUCTION REQUIREMENTS:**
- **Responsive design** tested and verified on mobile, tablet, and desktop
- **WCAG 2.1 AA compliance** for accessibility standards
- **Cross-browser compatibility** verified on Chrome, Firefox, Safari, Edge
- **Performance optimization** with acceptable load times
- **SEO optimization** with proper meta tags and structure

## 🔍 **Comprehensive Audit Process**

### **Pre-Production Checklist**
✅ **MANDATORY VERIFICATION STEPS:**
1. **Run hardcoded color audit**: `grep -r "bg-red\|bg-green\|bg-blue\|bg-yellow\|bg-purple" src/`
2. **Verify component consistency**: All cards use BentoCard or UnifiedCard
3. **Test navigation systems**: Hamburger menu, desktop nav, bottom nav
4. **Validate theme switching**: Light/dark mode transitions work correctly
5. **Confirm Supabase integration**: All database operations functional
6. **Review admin functionality**: Role-based access and security verified
7. **Test complete user flows**: End-to-end functionality validation

### **Success Criteria (ALL MUST BE MET)**
✅ **VISUAL EXCELLENCE:**
- Every component matches AuthenticatedHome 4-card visual standard
- Clear visual hierarchy with sufficient contrast throughout
- Consistent glassmorphism and colored gradient application

✅ **TECHNICAL EXCELLENCE:**
- Zero hardcoded colors in entire codebase
- Complete design token usage for all styling
- Unified component system with BentoCard approach
- TypeScript compilation with zero errors

✅ **FUNCTIONAL EXCELLENCE:**
- All Supabase integration working seamlessly
- Complete user authentication and authorization
- Full admin functionality with proper security
- Responsive design across all devices

## 📋 **Team Development Guidelines**

### **Preventing Future Inconsistencies**
✅ **MANDATORY PRACTICES:**
- **Always reference** AuthenticatedHome 4-card system before implementing new components
- **Never introduce** hardcoded colors - use design tokens exclusively
- **Always use** BentoCard for primary content cards
- **Always verify** visual prominence and "pop" factor
- **Always test** in both light and dark themes
- **Always maintain** single source of truth architecture

### **Code Review Requirements**
✅ **REVIEW CHECKLIST:**
- [ ] No hardcoded colors introduced
- [ ] Proper component choice (BentoCard vs UnifiedCard vs shadcn/ui)
- [ ] Design token usage verified
- [ ] Visual hierarchy meets standards
- [ ] Accessibility compliance maintained
- [ ] Responsive design considerations addressed

---

## 🎯 **PERMANENT DEVELOPMENT METHODOLOGY**

**This document establishes the permanent Festival Family development methodology. All team members must follow these guidelines to ensure:**

1. **Visual Consistency** - Every component matches the AuthenticatedHome 4-card excellence standard
2. **Technical Consistency** - Single source of truth prevents styling conflicts
3. **Accessibility Compliance** - WCAG 2.1 standards maintained throughout
4. **Production Readiness** - Clear criteria prevent regression and ensure quality
5. **Team Alignment** - Unified approach prevents future inconsistencies

**🎨 REMEMBER: The AuthenticatedHome 4-card system is the visual excellence template for ALL Festival Family components.**

## 🎨 **Color System**

### **Primary Colors (Use These)**
```css
/* shadcn/ui Foundation - ALWAYS USE THESE */
--background: 0 0% 100%;          /* Main background */
--foreground: 30 10% 11%;         /* Main text */
--primary: 262 83% 58%;           /* Festival purple */
--secondary: 25 25% 69%;          /* Mocha Mousse */
--accent: 24 100% 50%;            /* Festival orange */
--muted: 210 40% 98%;             /* Muted backgrounds */
--border: 214 32% 91%;            /* Standard borders */
--input: 214 32% 91%;             /* Input borders */
--ring: 262 83% 58%;              /* Focus rings */
```

### **Usage Examples**
```css
/* ✅ CORRECT - Use CSS variables */
.my-component {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--border));
}

/* ❌ WRONG - Never use hardcoded colors */
.my-component {
  background-color: #ffffff;
  color: #333333;
  border: 1px solid #e5e7eb;
}
```

### **Festival Family Extensions**
```css
/* Use only when shadcn/ui variables don't fit */
--festival-priority-high: hsl(var(--destructive));
--festival-priority-medium: hsl(var(--accent));
--festival-priority-low: hsl(var(--primary));
--festival-success: hsl(142 71% 45%);
--festival-warning: hsl(43 96% 56%);
```

## 🌓 **Theme Support & Form Field Readability Architecture**

### **CRITICAL: Separated Color Systems**

Festival Family implements a **Form Field Readability-First** architecture that separates:

1. **🔒 Readability-Constant Components**: Form fields that MUST maintain optimal readability regardless of theme
2. **🎨 Theme-Adaptive Components**: UI elements that can safely adapt to light/dark themes

### **Form Field Readability Standards (WCAG 2.1 Compliant)**

**Core Principle**: Form fields should ALWAYS have light backgrounds with dark text for optimal readability, regardless of the surrounding theme.

```css
/* ✅ CORRECT - Form fields with readability-first approach */
.form-field {
  /* Always light background for readability */
  background-color: hsl(var(--form-background)); /* Light in both themes */
  color: hsl(var(--form-foreground)); /* Dark text in both themes */
  border-color: hsl(var(--form-border)); /* Subtle border */
}

/* ✅ CORRECT - Theme-adaptive UI elements */
.card {
  background-color: hsl(var(--card)); /* Adapts to theme */
  color: hsl(var(--card-foreground)); /* Adapts to theme */
}

/* ❌ WRONG - Theme-unaware hardcoded colors */
.card {
  background-color: #ffffff;
  color: #000000;
}
```

## 📏 **Spacing & Layout**

### **Use Tailwind Utilities**
```css
/* ✅ CORRECT - Tailwind spacing */
<div className="p-4 m-2 space-y-4">

/* ❌ WRONG - Custom spacing */
<div style={{ padding: '16px', margin: '8px' }}>
```

### **Responsive Design**
```css
/* ✅ CORRECT - Tailwind responsive */
<div className="p-2 sm:p-4 md:p-6 lg:p-8">

/* ❌ WRONG - Custom breakpoints */
@media (min-width: 768px) { ... }
```

## 🧩 **Component Guidelines**

### **CSS Variable Categories**

#### **🔒 Readability-Constant Variables (Form Fields)**
```css
/* Form field variables - maintain readability across themes */
--form-background: 0 0% 100%;        /* Always light background */
--form-foreground: 0 0% 9%;          /* Always dark text */
--form-border: 0 0% 89%;             /* Subtle light border */
--form-focus-ring: 262 83% 58%;      /* Festival purple focus */
```

#### **🎨 Theme-Adaptive Variables (UI Elements)**
```css
/* Light theme */
--background: 0 0% 100%;             /* White background */
--foreground: 30 10% 11%;            /* Dark text */
--card: 0 0% 100%;                   /* White cards */
--card-foreground: 30 10% 11%;       /* Dark text on cards */

/* Dark theme (.dark class) */
--background: 262 45% 8%;            /* Dark purple background */
--foreground: 0 0% 100%;             /* White text */
--card: 262 35% 14%;                 /* Dark purple cards */
--card-foreground: 0 0% 100%;        /* White text on cards */
```

### **Form Field Components (CRITICAL FOR TEXT READABILITY)**

**Text Contrast Requirements:**

- All form components MUST have proper text contrast (WCAG 2.1: 4.5:1 minimum)
- Form fields use readability-constant variables for optimal contrast
- Include autofill styling fixes for browser compatibility

```tsx
// ✅ CORRECT - Form components with readability-first approach
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { FormLabel } from '@/components/ui/form'

// All components automatically include:
// - bg-form-background (always light for readability)
// - text-form-foreground (always dark for contrast)
// - border-form-border (subtle border)
// - autofill styling fixes

<Input
  placeholder="Enter title..."
  // Readability-first styling already included
/>

<Textarea
  placeholder="Enter description..."
  // Readability-first styling already included
/>

<Select>
  <SelectTrigger>
    <SelectValue placeholder="Select option..." />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
  </SelectContent>
</Select>

// ✅ CORRECT - Form error styling with theme-aware colors
<FormLabel className={cn(error && "text-destructive")}>
  Field Label
</FormLabel>

// ❌ WRONG - Hardcoded error colors
<FormLabel className={cn(error && "text-red-500")}>
  Field Label
</FormLabel>
```

**Browser Autofill Fix (Updated for Readability-First Approach):**

```css
/* Required for autofill compatibility - DO NOT REMOVE */
input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px hsl(var(--form-background)) inset !important;
  -webkit-text-fill-color: hsl(var(--form-foreground)) !important;
  color: hsl(var(--form-foreground)) !important;
  background-color: hsl(var(--form-background)) !important;
}
```

### **shadcn/ui Components (Preferred)**
```tsx
// ✅ CORRECT - Use shadcn/ui components
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

<Button variant="default">Click me</Button>
<Card className="p-4">Content</Card>
```

### **Custom Components**
```tsx
// ✅ CORRECT - Extend shadcn/ui patterns
interface CustomCardProps {
  variant?: 'default' | 'festival';
  children: React.ReactNode;
}

const CustomCard = ({ variant = 'default', children }: CustomCardProps) => (
  <div className={cn(
    "rounded-lg border bg-card text-card-foreground shadow-sm",
    variant === 'festival' && "border-primary/20"
  )}>
    {children}
  </div>
);
```

## 🚫 **What NOT to Do**

### **Forbidden Practices**
1. **Hardcoded Colors**: Never use hex, rgb, or hsl values directly
2. **Multiple Styling Systems**: Don't introduce new CSS frameworks
3. **Inline Styles**: Avoid style props except for dynamic values
4. **Custom CSS Variables**: Don't create new CSS variables without approval
5. **Theme-Unaware Code**: All styling must support light/dark modes

### **Legacy Patterns to Avoid**
```tsx
// ❌ WRONG - Legacy patterns
import { theme } from '@/styles/theme'; // File removed
const colors = { primary: '#6A0DAD' }; // Hardcoded
style={{ backgroundColor: '#ffffff' }} // Inline hardcoded
```

## ✅ **Best Practices**

### **1. Use Existing Variables**
Always check if a shadcn/ui variable exists before creating custom ones.

### **2. Follow Naming Conventions**
- Use shadcn/ui naming: `--primary`, `--background`, `--foreground`
- Festival extensions: `--festival-*` prefix

### **3. Test in Both Themes**
Always verify your components work in both light and dark modes.

### **4. Use TypeScript**
Leverage TypeScript for component props and styling consistency.

### **5. Document Custom Extensions**
If you must add Festival Family extensions, document them clearly.

## 🔧 **Development Workflow**

### **Before Adding Styles**
1. Check if shadcn/ui has a suitable component
2. Verify if existing CSS variables meet your needs
3. Test in both light and dark themes
4. Ensure responsive behavior

### **Code Review Checklist**
- [ ] No hardcoded colors
- [ ] Uses CSS variables
- [ ] Works in light/dark themes
- [ ] Follows Tailwind patterns
- [ ] No new styling systems introduced

## � **Implementation Guide**

### **Step 1: Update CSS Variables**

Add readability-constant form field variables to `src/index.css`:

```css
:root {
  /* Existing variables... */

  /* Form field readability-constant variables */
  --form-background: 0 0% 100%;        /* Always light background */
  --form-foreground: 0 0% 9%;          /* Always dark text */
  --form-border: 0 0% 89%;             /* Subtle light border */
  --form-focus-ring: 262 83% 58%;      /* Festival purple focus */
}

.dark {
  /* Existing dark theme variables... */

  /* Form fields remain the same for readability */
  --form-background: 0 0% 100%;        /* Still light background */
  --form-foreground: 0 0% 9%;          /* Still dark text */
  --form-border: 0 0% 89%;             /* Still subtle border */
  --form-focus-ring: 262 83% 58%;      /* Same focus ring */
}
```

### **Step 2: Update Form Components**

Modify form components to use readability-constant variables:

```tsx
// src/components/ui/input.tsx
className={cn(
  "flex h-10 w-full rounded-md border px-3 py-2 text-sm",
  "bg-form-background text-form-foreground border-form-border",
  "focus-visible:ring-2 focus-visible:ring-form-focus-ring",
  "autofill:bg-form-background autofill:text-form-foreground",
  className
)}
```

## �📚 **Resources**

- [shadcn/ui Documentation](https://ui.shadcn.com/)
- [Tailwind CSS Documentation](https://tailwindcss.com/)
- [WCAG 2.1 Contrast Guidelines](https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html)
- Festival Family Design Tokens: `src/styles/design-tokens.css`
- Component Examples: `src/components/ui/`

## 🆘 **Getting Help**

If you need to:
- Add new colors → Check shadcn/ui variables first, use `--form-*` for form fields
- Create custom components → Follow shadcn/ui patterns with readability-first approach
- Debug styling conflicts → Verify CSS variable usage and form field contrast
- Support new themes → Ensure CSS variable foundation with form field readability

**Remember**: Form field readability is paramount. When in doubt, prioritize contrast and use the readability-constant variables for form elements.

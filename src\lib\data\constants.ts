/**
 * Unified Data Constants
 * 
 * Centralized constants for the unified data service architecture.
 * These constants ensure consistency across all data operations.
 * 
 * @module DataConstants
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

// ============================================================================
// TABLE NAMES
// ============================================================================

export const TABLE_NAMES = {
  ACTIVITIES: 'activities',
  EVENTS: 'events',
  ANNOUNCEMENTS: 'announcements',
  FESTIVALS: 'festivals',
  PROFILES: 'profiles',
  ACTIVITY_ATTENDANCE: 'activity_attendance',
  COMMUNITIES: 'communities',
  GROUPS: 'groups',
  TIPS: 'tips',
  GUIDES: 'guides',
  FAQS: 'faqs',
  LOCAL_INFO: 'local_info'
} as const;

// ============================================================================
// STATUS VALUES
// ============================================================================

export const ACTIVITY_STATUS = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  CANCELLED: 'cancelled'
} as const;

export const EVENT_STATUS = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  CANCELLED: 'cancelled'
} as const;

export const ANNOUNCEMENT_STATUS = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  ARCHIVED: 'archived'
} as const;

export const FESTIVAL_STATUS = {
  UPCOMING: 'upcoming',
  ACTIVE: 'active',
  PAST: 'past'
} as const;

export const ATTENDANCE_STATUS = {
  GOING: 'going',
  INTERESTED: 'interested',
  NOT_GOING: 'not_going'
} as const;

// ============================================================================
// ACTIVITY TYPES
// ============================================================================

export const ACTIVITY_TYPES = {
  MEETUP: 'meetup',
  WORKSHOP: 'workshop',
  PERFORMANCE: 'performance',
  FOOD: 'food',
  SOCIAL: 'social',
  WELLNESS: 'wellness',
  EDUCATIONAL: 'educational',
  NETWORKING: 'networking'
} as const;

export const ACTIVITY_CATEGORIES = {
  MUSIC: 'music',
  ART: 'art',
  FOOD: 'food',
  WELLNESS: 'wellness',
  SOCIAL: 'social',
  EDUCATIONAL: 'educational',
  NETWORKING: 'networking',
  ENTERTAINMENT: 'entertainment'
} as const;

// ============================================================================
// EVENT CATEGORIES
// ============================================================================

export const EVENT_CATEGORIES = {
  MUSIC: 'music',
  ART: 'art',
  FOOD: 'food',
  WELLNESS: 'wellness',
  EDUCATIONAL: 'educational',
  NETWORKING: 'networking',
  ENTERTAINMENT: 'entertainment',
  CULTURAL: 'cultural'
} as const;

// ============================================================================
// ANNOUNCEMENT TYPES
// ============================================================================

export const ANNOUNCEMENT_TYPES = {
  GENERAL: 'general',
  URGENT: 'urgent',
  UPDATE: 'update',
  REMINDER: 'reminder',
  SAFETY: 'safety',
  WEATHER: 'weather',
  SCHEDULE_CHANGE: 'schedule_change'
} as const;

// ============================================================================
// USER ROLES
// ============================================================================

export const USER_ROLES = {
  USER: 'user',
  MODERATOR: 'moderator',
  CONTENT_ADMIN: 'content_admin',
  SUPER_ADMIN: 'super_admin'
} as const;

// ============================================================================
// REAL-TIME EVENTS
// ============================================================================

export const REALTIME_EVENTS = {
  INSERT: 'INSERT',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  ALL: '*'
} as const;

export const REALTIME_CHANNELS = {
  ACTIVITIES: 'activities_changes',
  EVENTS: 'events_changes',
  ANNOUNCEMENTS: 'announcements_changes',
  FESTIVALS: 'festivals_changes'
} as const;

// ============================================================================
// PAGINATION DEFAULTS
// ============================================================================

export const PAGINATION_DEFAULTS = {
  PAGE: 1,
  LIMIT: 20,
  MAX_LIMIT: 100
} as const;

// ============================================================================
// QUERY LIMITS
// ============================================================================

export const QUERY_LIMITS = {
  ACTIVITIES: 50,
  EVENTS: 50,
  ANNOUNCEMENTS: 20,
  FESTIVALS: 20,
  SEARCH_RESULTS: 10,
  FEATURED_ITEMS: 5
} as const;

// ============================================================================
// CACHE DURATIONS (in milliseconds)
// ============================================================================

export const CACHE_DURATIONS = {
  SHORT: 5 * 60 * 1000,      // 5 minutes
  MEDIUM: 15 * 60 * 1000,    // 15 minutes
  LONG: 60 * 60 * 1000,      // 1 hour
  VERY_LONG: 24 * 60 * 60 * 1000  // 24 hours
} as const;

// ============================================================================
// ERROR MESSAGES
// ============================================================================

export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  SERVER_ERROR: 'An unexpected error occurred. Please try again later.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
  UNKNOWN_ERROR: 'An unknown error occurred.'
} as const;

// ============================================================================
// SUCCESS MESSAGES
// ============================================================================

export const SUCCESS_MESSAGES = {
  ACTIVITY_CREATED: 'Activity created successfully!',
  ACTIVITY_UPDATED: 'Activity updated successfully!',
  ACTIVITY_DELETED: 'Activity deleted successfully!',
  EVENT_CREATED: 'Event created successfully!',
  EVENT_UPDATED: 'Event updated successfully!',
  EVENT_DELETED: 'Event deleted successfully!',
  ANNOUNCEMENT_CREATED: 'Announcement created successfully!',
  ANNOUNCEMENT_UPDATED: 'Announcement updated successfully!',
  ANNOUNCEMENT_DELETED: 'Announcement deleted successfully!',
  DATA_SYNCED: 'Data synchronized successfully!'
} as const;

// ============================================================================
// VALIDATION RULES
// ============================================================================

export const VALIDATION_RULES = {
  TITLE_MIN_LENGTH: 3,
  TITLE_MAX_LENGTH: 100,
  DESCRIPTION_MIN_LENGTH: 10,
  DESCRIPTION_MAX_LENGTH: 1000,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  URL_REGEX: /^https?:\/\/.+/
} as const;

// ============================================================================
// DATE FORMATS
// ============================================================================

export const DATE_FORMATS = {
  ISO: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
  DATE_ONLY: 'YYYY-MM-DD',
  TIME_ONLY: 'HH:mm:ss',
  DISPLAY_DATE: 'MMM DD, YYYY',
  DISPLAY_DATETIME: 'MMM DD, YYYY HH:mm',
  DISPLAY_TIME: 'HH:mm'
} as const;

// ============================================================================
// FEATURE FLAGS
// ============================================================================

export const FEATURE_FLAGS = {
  REAL_TIME_ENABLED: true,
  CACHING_ENABLED: true,
  OFFLINE_SUPPORT: false,
  ANALYTICS_ENABLED: true,
  DEBUG_MODE: process.env.NODE_ENV === 'development'
} as const;

// ============================================================================
// API ENDPOINTS
// ============================================================================

export const API_ENDPOINTS = {
  ACTIVITIES: '/api/activities',
  EVENTS: '/api/events',
  ANNOUNCEMENTS: '/api/announcements',
  FESTIVALS: '/api/festivals',
  PROFILES: '/api/profiles',
  SEARCH: '/api/search',
  UPLOAD: '/api/upload'
} as const;

// ============================================================================
// STORAGE KEYS
// ============================================================================

export const STORAGE_KEYS = {
  USER_PREFERENCES: 'festival_family_user_preferences',
  CACHE_PREFIX: 'festival_family_cache_',
  SESSION_DATA: 'festival_family_session',
  OFFLINE_DATA: 'festival_family_offline'
} as const;

// ============================================================================
// EXPORT TYPES FOR CONSTANTS
// ============================================================================

export type ActivityStatus = typeof ACTIVITY_STATUS[keyof typeof ACTIVITY_STATUS];
export type EventStatus = typeof EVENT_STATUS[keyof typeof EVENT_STATUS];
export type AnnouncementStatus = typeof ANNOUNCEMENT_STATUS[keyof typeof ANNOUNCEMENT_STATUS];
export type FestivalStatus = typeof FESTIVAL_STATUS[keyof typeof FESTIVAL_STATUS];
export type AttendanceStatus = typeof ATTENDANCE_STATUS[keyof typeof ATTENDANCE_STATUS];
export type ActivityType = typeof ACTIVITY_TYPES[keyof typeof ACTIVITY_TYPES];
export type ActivityCategory = typeof ACTIVITY_CATEGORIES[keyof typeof ACTIVITY_CATEGORIES];
export type EventCategory = typeof EVENT_CATEGORIES[keyof typeof EVENT_CATEGORIES];
export type AnnouncementType = typeof ANNOUNCEMENT_TYPES[keyof typeof ANNOUNCEMENT_TYPES];
export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];
export type RealtimeEvent = typeof REALTIME_EVENTS[keyof typeof REALTIME_EVENTS];
export type TableName = typeof TABLE_NAMES[keyof typeof TABLE_NAMES];

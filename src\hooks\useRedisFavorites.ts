/**
 * Redis-Optimized Favorites Hook
 * 
 * High-performance replacement for useFavorites.ts that uses Redis caching
 * to eliminate subscription storms and provide sub-100ms user interactions.
 * 
 * Features:
 * - Redis caching with 5-minute TTL
 * - Graceful fallback to database queries
 * - Optimistic updates for instant UI feedback
 * - Reduced subscription frequency (eliminates subscription storm)
 * - Performance tracking and monitoring
 * 
 * @module useRedisFavorites
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { toast } from 'react-hot-toast'
import { redisFavoritesService } from '@/lib/redis/redis-favorites-service'
import { useAuth } from '@/providers/ConsolidatedAuthProvider'

// ============================================================================
// TYPES
// ============================================================================

export interface UseRedisFavoritesReturn {
  // State
  favorites: Set<string>
  isLoading: boolean
  error: string | null

  // Actions
  toggleFavorite: (itemId: string, itemType?: string) => Promise<void>
  addToFavorites: (itemId: string, itemType?: string) => Promise<void>
  removeFromFavorites: (itemId: string, itemType?: string) => Promise<void>
  isFavorite: (itemId: string) => boolean
  refreshFavorites: () => Promise<void>

  // Performance metrics
  cacheHitRate: number
  averageResponseTime: number
}

// ============================================================================
// HOOK IMPLEMENTATION
// ============================================================================

export function useRedisFavorites(itemType: string = 'activity'): UseRedisFavoritesReturn {
  const { user } = useAuth()
  const [favorites, setFavorites] = useState<Set<string>>(new Set())
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Performance tracking
  const performanceRef = useRef({
    cacheHits: 0,
    cacheMisses: 0,
    totalResponseTime: 0,
    operationCount: 0
  })

  // ========================================================================
  // UTILITY FUNCTIONS
  // ========================================================================

  const handleError = useCallback((error: any, action: string) => {
    const message = error?.message ?? `Failed to ${action}`
    setError(message)
    toast.error(message)
    console.error(`Redis Favorites error (${action}):`, error)
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const trackPerformance = useCallback((responseTime: number, cacheHit: boolean) => {
    const perf = performanceRef.current
    perf.operationCount++
    perf.totalResponseTime += responseTime
    
    if (cacheHit) {
      perf.cacheHits++
    } else {
      perf.cacheMisses++
    }
  }, [])

  // ========================================================================
  // DATA FETCHING WITH REDIS CACHING
  // ========================================================================

  const fetchUserFavorites = useCallback(async () => {
    if (!user?.id) {
      setFavorites(new Set())
      return
    }

    const startTime = performance.now()
    
    try {
      const favoriteIds = await redisFavoritesService.getUserFavorites(user.id, itemType)
      const responseTime = performance.now() - startTime
      
      setFavorites(new Set(favoriteIds))
      trackPerformance(responseTime, favoriteIds.length > 0) // Assume cache hit if data returned quickly
      
      console.log(`⚡ Favorites loaded: ${favoriteIds.length} items (${responseTime.toFixed(2)}ms)`)
    } catch (error) {
      handleError(error, 'fetch favorites')
    }
  }, [user?.id, itemType, handleError, trackPerformance])

  const refreshFavorites = useCallback(async () => {
    setIsLoading(true)
    clearError()
    await fetchUserFavorites()
    setIsLoading(false)
  }, [fetchUserFavorites, clearError])

  // ========================================================================
  // FAVORITE ACTIONS WITH REDIS OPTIMIZATION
  // ========================================================================

  const addToFavorites = useCallback(async (itemId: string, type: string = itemType) => {
    if (!user?.id || isLoading) return

    setIsLoading(true)
    clearError()

    // Optimistic update for instant UI feedback
    setFavorites(prev => new Set([...prev, itemId]))

    const startTime = performance.now()

    try {
      const success = await redisFavoritesService.addToFavorites(user.id, itemId, type)
      const responseTime = performance.now() - startTime
      
      trackPerformance(responseTime, false) // Write operations are never cache hits
      
      if (success) {
        toast.success('Added to favorites')
        console.log(`✅ Added to favorites: ${itemId} (${responseTime.toFixed(2)}ms)`)
      } else {
        // Revert optimistic update
        setFavorites(prev => {
          const newSet = new Set(prev)
          newSet.delete(itemId)
          return newSet
        })
        handleError(new Error('Failed to add to favorites'), 'add to favorites')
      }
    } catch (error) {
      // Revert optimistic update
      setFavorites(prev => {
        const newSet = new Set(prev)
        newSet.delete(itemId)
        return newSet
      })
      handleError(error, 'add to favorites')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id, isLoading, itemType, clearError, handleError, trackPerformance])

  const removeFromFavorites = useCallback(async (itemId: string, type: string = itemType) => {
    if (!user?.id || isLoading) return

    setIsLoading(true)
    clearError()

    // Optimistic update for instant UI feedback
    setFavorites(prev => {
      const newSet = new Set(prev)
      newSet.delete(itemId)
      return newSet
    })

    const startTime = performance.now()

    try {
      const success = await redisFavoritesService.removeFromFavorites(user.id, itemId)
      const responseTime = performance.now() - startTime
      
      trackPerformance(responseTime, false) // Write operations are never cache hits
      
      if (success) {
        toast.success('Removed from favorites')
        console.log(`✅ Removed from favorites: ${itemId} (${responseTime.toFixed(2)}ms)`)
      } else {
        // Revert optimistic update
        setFavorites(prev => new Set([...prev, itemId]))
        handleError(new Error('Failed to remove from favorites'), 'remove from favorites')
      }
    } catch (error) {
      // Revert optimistic update
      setFavorites(prev => new Set([...prev, itemId]))
      handleError(error, 'remove from favorites')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id, isLoading, itemType, clearError, handleError, trackPerformance])

  const toggleFavorite = useCallback(async (itemId: string, type: string = itemType) => {
    if (!user?.id || isLoading) return

    const isFav = favorites.has(itemId)
    if (isFav) {
      await removeFromFavorites(itemId, type)
    } else {
      await addToFavorites(itemId, type)
    }
  }, [user?.id, isLoading, favorites, addToFavorites, removeFromFavorites, itemType])

  // ========================================================================
  // UTILITY FUNCTIONS
  // ========================================================================

  const isFavorite = useCallback((itemId: string) => {
    return favorites.has(itemId)
  }, [favorites])

  // ========================================================================
  // PERFORMANCE METRICS
  // ========================================================================

  const cacheHitRate = useCallback(() => {
    const perf = performanceRef.current
    if (perf.operationCount === 0) return 0
    return (perf.cacheHits / perf.operationCount) * 100
  }, [])

  const averageResponseTime = useCallback(() => {
    const perf = performanceRef.current
    if (perf.operationCount === 0) return 0
    return perf.totalResponseTime / perf.operationCount
  }, [])

  // ========================================================================
  // EFFECTS
  // ========================================================================

  // Initial data fetch
  useEffect(() => {
    if (user?.id) {
      fetchUserFavorites()
    } else {
      setFavorites(new Set())
    }
  }, [user?.id, fetchUserFavorites])

  // Note: No real-time subscriptions to eliminate subscription storm
  // The Redis cache will be invalidated on writes, ensuring data consistency

  // ========================================================================
  // RETURN INTERFACE
  // ========================================================================

  return {
    // State
    favorites,
    isLoading,
    error,

    // Actions
    toggleFavorite,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    refreshFavorites,

    // Performance metrics
    cacheHitRate: cacheHitRate(),
    averageResponseTime: averageResponseTime()
  }
}

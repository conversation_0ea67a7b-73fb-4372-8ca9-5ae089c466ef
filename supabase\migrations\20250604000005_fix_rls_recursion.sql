-- Fix RLS Policy Recursion Issue
-- The infinite recursion is caused by policies that reference the same table they're protecting
-- This migration fixes the recursion by simplifying the policies

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can read own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can read all profiles" ON profiles;
DROP POLICY IF EXISTS "Users can create own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update user profiles" ON profiles;
DROP POLICY IF EXISTS "Only super admins can delete profiles" ON profiles;

-- Disable <PERSON><PERSON> temporarily to fix the recursion
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Re-enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create simple, non-recursive policies

-- 1. Allow users to read their own profile
CREATE POLICY "Users can read own profile" ON profiles
FOR SELECT USING (auth.uid() = id);

-- 2. Allow users to create their own profile
CREATE POLICY "Users can create own profile" ON profiles
FOR INSERT WITH CHECK (auth.uid() = id);

-- 3. Allow users to update their own profile (basic version)
CREATE POLICY "Users can update own profile" ON profiles
FOR UPDATE USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- 4. Allow all authenticated users to read public profile information
-- This avoids recursion by not checking roles in the same table
CREATE POLICY "Public profile read access" ON profiles
FOR SELECT USING (auth.role() = 'authenticated');

-- Create a separate function to check if user is admin (avoids recursion)
CREATE OR REPLACE FUNCTION is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- Use a direct query without RLS to avoid recursion
  SELECT role INTO user_role
  FROM profiles
  WHERE id = user_id;
  
  RETURN user_role IN ('SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR');
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;

-- Create admin policies using the helper function
CREATE POLICY "Admins can manage all profiles" ON profiles
FOR ALL USING (is_admin());

-- Update the role change prevention trigger to be simpler
CREATE OR REPLACE FUNCTION prevent_role_escalation()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
  current_user_role TEXT;
BEGIN
  -- For INSERT operations, set default role
  IF TG_OP = 'INSERT' THEN
    IF NEW.role IS NULL THEN
      NEW.role := 'USER';
    END IF;
    RETURN NEW;
  END IF;
  
  -- For UPDATE operations, check role changes
  IF TG_OP = 'UPDATE' AND NEW.role IS DISTINCT FROM OLD.role THEN
    -- Get current user's role directly (bypass RLS for this check)
    SELECT role INTO current_user_role
    FROM profiles
    WHERE id = auth.uid();
    
    -- Only SUPER_ADMIN can change roles
    IF current_user_role != 'SUPER_ADMIN' THEN
      RAISE EXCEPTION 'Only SUPER_ADMIN can change user roles. Current user role: %', current_user_role;
    END IF;
    
    -- Validate the new role
    IF NEW.role NOT IN ('USER', 'MODERATOR', 'CONTENT_ADMIN', 'SUPER_ADMIN') THEN
      RAISE EXCEPTION 'Invalid role: %', NEW.role;
    END IF;
    
    RAISE NOTICE 'Role changed from % to % by SUPER_ADMIN %', OLD.role, NEW.role, auth.uid();
  END IF;
  
  RETURN NEW;
END;
$$;

-- Recreate the trigger
DROP TRIGGER IF EXISTS prevent_role_escalation_trigger ON profiles;
CREATE TRIGGER prevent_role_escalation_trigger
  BEFORE INSERT OR UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION prevent_role_escalation();

-- Grant permissions
GRANT EXECUTE ON FUNCTION is_admin TO authenticated;

-- Test that the admin account is accessible
DO $$
DECLARE
  admin_count INTEGER;
  admin_role TEXT;
BEGIN
  -- Test if we can access the admin account
  SELECT COUNT(*), MAX(role) INTO admin_count, admin_role
  FROM profiles
  WHERE email = '<EMAIL>';
  
  IF admin_count = 0 THEN
    RAISE NOTICE 'WARNING: <EMAIL> account not found!';
  ELSE
    RAISE NOTICE 'SUCCESS: <EMAIL> found with role: %', admin_role;
  END IF;
  
  -- Test the is_admin function
  IF is_admin() THEN
    RAISE NOTICE 'SUCCESS: is_admin function working correctly';
  ELSE
    RAISE NOTICE 'INFO: is_admin function returned false (expected if not logged in as admin)';
  END IF;
END $$;

-- Log successful migration
DO $$
BEGIN
  RAISE NOTICE 'SUCCESS: RLS recursion issue fixed';
  RAISE NOTICE 'INFO: Simplified RLS policies created without recursion';
  RAISE NOTICE 'INFO: is_admin helper function created to avoid policy recursion';
  RAISE NOTICE 'INFO: Role change prevention trigger updated';
END $$;

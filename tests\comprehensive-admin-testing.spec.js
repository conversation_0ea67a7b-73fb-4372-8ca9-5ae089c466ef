/**
 * Comprehensive Admin Dashboard Testing Suite
 * 
 * This test suite systematically validates the entire admin experience:
 * - Authentication and session management
 * - Navigation through all admin sections
 * - CRUD operations for all admin features
 * - Database integration verification
 * - Error detection and documentation
 */

import { test, expect } from '@playwright/test';
import { 
  AdminAuthHelper, 
  AdminEvidenceCollector, 
  AdminNavigationHelper, 
  AdminContentManager 
} from './utils/admin-test-helpers.js';

// Test configuration
const VIEWPORT_SIZES = [
  { name: 'desktop', width: 1920, height: 1080 },
  { name: 'tablet', width: 768, height: 1024 },
  { name: 'mobile', width: 375, height: 667 }
];

test.describe('Phase 1: Admin Dashboard Comprehensive Testing', () => {
  let authHelper;
  let evidenceCollector;
  let navigationHelper;
  let contentManager;

  test.beforeEach(async ({ page }) => {
    authHelper = new AdminAuthHelper(page);
    evidenceCollector = new AdminEvidenceCollector(page);
    navigationHelper = new AdminNavigationHelper(page);
    contentManager = new AdminContentManager(page);
  });
  
  test('1.1 Admin Authentication Flow Testing', async ({ page }) => {
    console.log('🧪 Testing admin authentication flow...');
    
    await test.step('Test admin login', async () => {
      await authHelper.login();
      await evidenceCollector.takeScreenshot('admin-login-success', 'Admin successfully logged in');
    });
    
    await test.step('Test session persistence across page reload', async () => {
      await page.reload();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Should still be logged in (not redirected to auth)
      const currentUrl = page.url();
      expect(currentUrl).not.toContain('/auth');
      
      await evidenceCollector.takeScreenshot('admin-session-persistence', 'Admin session persisted after reload');
      console.log('✅ Admin session persistence verified');
    });
    
    await test.step('Test admin/user view switching', async () => {
      const switchResult = await authHelper.testViewSwitching();
      
      if (switchResult.success) {
        await evidenceCollector.takeScreenshot('admin-view-switching', 'View switching functionality verified');
        console.log('✅ Admin/User view switching verified');
      } else {
        console.log('⚠️ View switching functionality not found');
      }
    });
    
    // Report any console errors
    const errors = evidenceCollector.getConsoleErrors();
    if (errors.length > 0) {
      console.log('⚠️ Console errors during authentication testing:', errors);
    }
  });

  test('1.2 Admin Section Navigation Testing', async ({ page }) => {
    console.log('🧪 Testing admin section navigation...');
    
    await authHelper.login();
    
    // Define admin sections to test
    const adminSections = [
      'dashboard',
      'users',
      'events', 
      'activities',
      'content',
      'announcements',
      'tips',
      'faqs',
      'contact',
      'settings'
    ];
    
    const navigationResults = [];
    
    for (const section of adminSections) {
      await test.step(`Navigate to ${section} section`, async () => {
        const result = await navigationHelper.testSectionNavigation(section);
        navigationResults.push(result);
        
        await evidenceCollector.takeScreenshot(`admin-section-${section}`, `Admin ${section} section`);
        
        if (result.success) {
          console.log(`✅ ${section} section loaded successfully`);
        } else {
          console.log(`❌ ${section} section failed to load: ${result.error}`);
        }
      });
    }
    
    // Generate comprehensive navigation report
    const report = navigationHelper.generateNavigationReport(navigationResults);
    console.log(report);
    
    // At least dashboard should work
    const successfulSections = navigationResults.filter(r => r.success).length;
    expect(successfulSections).toBeGreaterThan(0);
  });

  test('1.3 Admin CRUD Operations Testing', async ({ page }) => {
    console.log('🧪 Testing admin CRUD operations...');
    
    await authHelper.login();
    
    const crudResults = [];
    const crudFeatures = ['users', 'events', 'activities', 'content'];
    
    for (const feature of crudFeatures) {
      await test.step(`Test ${feature} Management CRUD`, async () => {
        const result = await contentManager.testCRUDInterface(feature);
        crudResults.push(result);
        
        await evidenceCollector.takeScreenshot(`admin-${feature}-crud`, `${feature} management interface`);
        
        if (result.success) {
          console.log(`✅ ${feature} management interface found`);
        } else {
          console.log(`❌ ${feature} management interface not found: ${result.error}`);
        }
      });
    }
    
    // Generate comprehensive CRUD report
    const report = contentManager.generateCRUDReport(crudResults);
    console.log(report);
    
    // Report console errors
    const errors = evidenceCollector.getConsoleErrors();
    if (errors.length > 0) {
      console.log('\n⚠️ Console errors during CRUD testing:', errors);
    }
    
    // At least some CRUD functionality should exist
    const successfulCrud = crudResults.filter(r => r.success).length;
    expect(successfulCrud).toBeGreaterThan(0);
  });
});

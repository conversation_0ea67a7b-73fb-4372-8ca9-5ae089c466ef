/**
 * Comprehensive Admin Dashboard Testing Suite
 * 
 * This test suite systematically validates the entire admin experience:
 * - Authentication and session management
 * - Navigation through all admin sections
 * - CRUD operations for all admin features
 * - Database integration verification
 * - Error detection and documentation
 */

import { test, expect } from '@playwright/test';

// Test configuration
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

const VIEWPORT_SIZES = [
  { name: 'desktop', width: 1920, height: 1080 },
  { name: 'tablet', width: 768, height: 1024 },
  { name: 'mobile', width: 375, height: 667 }
];

// Helper functions
async function takeEvidence(page, name, description) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `admin-test-${name}-${timestamp}.png`;
  await page.screenshot({ 
    path: `test-results/${filename}`,
    fullPage: true 
  });
  console.log(`📸 Admin Test Evidence: ${filename} - ${description}`);
  return filename;
}

async function captureConsoleErrors(page) {
  const errors = [];
  page.on('console', msg => {
    if (msg.type() === 'error') {
      errors.push(msg.text());
    }
  });
  page.on('pageerror', error => {
    errors.push(`Page Error: ${error.message}`);
  });
  return errors;
}

async function loginAsAdmin(page) {
  console.log('🔐 Logging in as admin...');
  
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  
  // Fill login form
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  
  // Submit form
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  
  // Wait for redirect and verify login
  await page.waitForTimeout(3000);
  
  // Check if we're logged in (should not be on auth page)
  const currentUrl = page.url();
  if (currentUrl.includes('/auth')) {
    throw new Error('Login failed - still on auth page');
  }
  
  console.log('✅ Admin login successful');
  return true;
}

async function navigateToAdminSection(page, sectionName) {
  console.log(`🧭 Navigating to admin section: ${sectionName}`);
  
  // Look for admin navigation or admin link
  const adminLink = page.locator('a[href*="/admin"], button:has-text("Admin"), a:has-text("Admin")').first();
  
  if (await adminLink.isVisible()) {
    await adminLink.click();
    await page.waitForLoadState('networkidle');
  }
  
  // Navigate to specific section if provided
  if (sectionName && sectionName !== 'dashboard') {
    const sectionLink = page.locator(`a[href*="${sectionName}"], a:has-text("${sectionName}")`, { timeout: 5000 });
    if (await sectionLink.isVisible()) {
      await sectionLink.click();
      await page.waitForLoadState('networkidle');
    }
  }
  
  await page.waitForTimeout(2000);
}

test.describe('Phase 1: Admin Dashboard Comprehensive Testing', () => {
  
  test('1.1 Admin Authentication Flow Testing', async ({ page }) => {
    console.log('🧪 Testing admin authentication flow...');
    
    const errors = await captureConsoleErrors(page);
    
    await test.step('Test admin login', async () => {
      await loginAsAdmin(page);
      await takeEvidence(page, 'admin-login-success', 'Admin successfully logged in');
    });
    
    await test.step('Test session persistence across page reload', async () => {
      await page.reload();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Should still be logged in (not redirected to auth)
      const currentUrl = page.url();
      expect(currentUrl).not.toContain('/auth');
      
      await takeEvidence(page, 'admin-session-persistence', 'Admin session persisted after reload');
      console.log('✅ Admin session persistence verified');
    });
    
    await test.step('Test admin/user view switching', async () => {
      // Look for view switching functionality
      const viewSwitcher = page.locator('button:has-text("User View"), a:has-text("User View"), button:has-text("Switch"), a:has-text("Switch")').first();
      
      if (await viewSwitcher.isVisible()) {
        await viewSwitcher.click();
        await page.waitForLoadState('networkidle');
        await takeEvidence(page, 'admin-user-view-switch', 'Switched to user view');
        
        // Switch back to admin view
        const adminSwitcher = page.locator('button:has-text("Admin"), a:has-text("Admin"), button:has-text("Dashboard")').first();
        if (await adminSwitcher.isVisible()) {
          await adminSwitcher.click();
          await page.waitForLoadState('networkidle');
          await takeEvidence(page, 'admin-view-switch-back', 'Switched back to admin view');
        }
        
        console.log('✅ Admin/User view switching verified');
      } else {
        console.log('⚠️ View switching functionality not found');
      }
    });
    
    // Report any console errors
    if (errors.length > 0) {
      console.log('⚠️ Console errors during authentication testing:', errors);
    }
  });

  test('1.2 Admin Section Navigation Testing', async ({ page }) => {
    console.log('🧪 Testing admin section navigation...');
    
    await loginAsAdmin(page);
    
    // Define admin sections to test
    const adminSections = [
      'dashboard',
      'users',
      'events', 
      'activities',
      'content',
      'announcements',
      'tips',
      'faqs',
      'contact',
      'settings'
    ];
    
    const navigationResults = [];
    
    for (const section of adminSections) {
      await test.step(`Navigate to ${section} section`, async () => {
        try {
          await navigateToAdminSection(page, section);
          
          // Check if page loaded successfully
          const pageTitle = await page.title();
          const currentUrl = page.url();
          
          // Look for error indicators
          const hasError = await page.locator('text=Error, text=404, text="Not Found"').count() > 0;
          
          const result = {
            section,
            url: currentUrl,
            title: pageTitle,
            success: !hasError,
            error: hasError ? 'Page shows error content' : null
          };
          
          navigationResults.push(result);
          
          await takeEvidence(page, `admin-section-${section}`, `Admin ${section} section`);
          
          if (result.success) {
            console.log(`✅ ${section} section loaded successfully`);
          } else {
            console.log(`❌ ${section} section failed to load: ${result.error}`);
          }
          
        } catch (error) {
          console.log(`❌ Failed to navigate to ${section}: ${error.message}`);
          navigationResults.push({
            section,
            success: false,
            error: error.message
          });
        }
      });
    }
    
    // Summary report
    const successfulSections = navigationResults.filter(r => r.success).length;
    const totalSections = navigationResults.length;
    
    console.log(`\n📊 ADMIN NAVIGATION SUMMARY:`);
    console.log(`✅ Successful: ${successfulSections}/${totalSections} sections`);
    console.log(`❌ Failed: ${totalSections - successfulSections}/${totalSections} sections`);
    
    if (successfulSections < totalSections) {
      console.log('\n❌ Failed sections:');
      navigationResults.filter(r => !r.success).forEach(r => {
        console.log(`  - ${r.section}: ${r.error}`);
      });
    }
    
    // At least dashboard should work
    expect(successfulSections).toBeGreaterThan(0);
  });

  test('1.3 Admin CRUD Operations Testing', async ({ page }) => {
    console.log('🧪 Testing admin CRUD operations...');
    
    await loginAsAdmin(page);
    const errors = await captureConsoleErrors(page);
    
    const crudResults = [];
    
    await test.step('Test Users Management CRUD', async () => {
      try {
        await navigateToAdminSection(page, 'users');
        
        // Look for user management interface
        const hasUsersList = await page.locator('table, .user-list, [data-testid*="user"]').count() > 0;
        const hasCreateButton = await page.locator('button:has-text("Create"), button:has-text("Add"), button:has-text("New")').count() > 0;
        
        crudResults.push({
          feature: 'Users Management',
          hasInterface: hasUsersList,
          hasCreateButton,
          success: hasUsersList || hasCreateButton
        });
        
        await takeEvidence(page, 'admin-users-crud', 'Users management interface');
        
        if (hasUsersList || hasCreateButton) {
          console.log('✅ Users management interface found');
        } else {
          console.log('❌ Users management interface not found');
        }
        
      } catch (error) {
        console.log(`❌ Users management test failed: ${error.message}`);
        crudResults.push({
          feature: 'Users Management',
          success: false,
          error: error.message
        });
      }
    });
    
    await test.step('Test Events Management CRUD', async () => {
      try {
        await navigateToAdminSection(page, 'events');
        
        // Look for events management interface
        const hasEventsList = await page.locator('table, .event-list, [data-testid*="event"]').count() > 0;
        const hasCreateButton = await page.locator('button:has-text("Create"), button:has-text("Add"), button:has-text("New")').count() > 0;
        const hasForm = await page.locator('form, input[name*="name"], input[name*="title"]').count() > 0;
        
        crudResults.push({
          feature: 'Events Management',
          hasInterface: hasEventsList,
          hasCreateButton,
          hasForm,
          success: hasEventsList || hasCreateButton || hasForm
        });
        
        await takeEvidence(page, 'admin-events-crud', 'Events management interface');
        
        if (hasEventsList || hasCreateButton || hasForm) {
          console.log('✅ Events management interface found');
        } else {
          console.log('❌ Events management interface not found');
        }
        
      } catch (error) {
        console.log(`❌ Events management test failed: ${error.message}`);
        crudResults.push({
          feature: 'Events Management',
          success: false,
          error: error.message
        });
      }
    });
    
    await test.step('Test Activities Management CRUD', async () => {
      try {
        await navigateToAdminSection(page, 'activities');
        
        // Look for activities management interface
        const hasActivitiesList = await page.locator('table, .activity-list, [data-testid*="activity"]').count() > 0;
        const hasCreateButton = await page.locator('button:has-text("Create"), button:has-text("Add"), button:has-text("New")').count() > 0;
        const hasForm = await page.locator('form, input[name*="title"], input[name*="name"]').count() > 0;
        
        crudResults.push({
          feature: 'Activities Management',
          hasInterface: hasActivitiesList,
          hasCreateButton,
          hasForm,
          success: hasActivitiesList || hasCreateButton || hasForm
        });
        
        await takeEvidence(page, 'admin-activities-crud', 'Activities management interface');
        
        if (hasActivitiesList || hasCreateButton || hasForm) {
          console.log('✅ Activities management interface found');
        } else {
          console.log('❌ Activities management interface not found');
        }
        
      } catch (error) {
        console.log(`❌ Activities management test failed: ${error.message}`);
        crudResults.push({
          feature: 'Activities Management',
          success: false,
          error: error.message
        });
      }
    });
    
    await test.step('Test Content Management CRUD', async () => {
      try {
        await navigateToAdminSection(page, 'content');
        
        // Look for content management interface
        const hasContentList = await page.locator('table, .content-list, [data-testid*="content"]').count() > 0;
        const hasCreateButton = await page.locator('button:has-text("Create"), button:has-text("Add"), button:has-text("New")').count() > 0;
        const hasForm = await page.locator('form, textarea, input[name*="title"], input[name*="content"]').count() > 0;
        
        crudResults.push({
          feature: 'Content Management',
          hasInterface: hasContentList,
          hasCreateButton,
          hasForm,
          success: hasContentList || hasCreateButton || hasForm
        });
        
        await takeEvidence(page, 'admin-content-crud', 'Content management interface');
        
        if (hasContentList || hasCreateButton || hasForm) {
          console.log('✅ Content management interface found');
        } else {
          console.log('❌ Content management interface not found');
        }
        
      } catch (error) {
        console.log(`❌ Content management test failed: ${error.message}`);
        crudResults.push({
          feature: 'Content Management',
          success: false,
          error: error.message
        });
      }
    });
    
    // Summary report
    const successfulCrud = crudResults.filter(r => r.success).length;
    const totalCrud = crudResults.length;
    
    console.log(`\n📊 ADMIN CRUD OPERATIONS SUMMARY:`);
    console.log(`✅ Working: ${successfulCrud}/${totalCrud} features`);
    console.log(`❌ Missing: ${totalCrud - successfulCrud}/${totalCrud} features`);
    
    if (successfulCrud < totalCrud) {
      console.log('\n❌ Missing CRUD features:');
      crudResults.filter(r => !r.success).forEach(r => {
        console.log(`  - ${r.feature}: ${r.error || 'Interface not found'}`);
      });
    }
    
    // Report console errors
    if (errors.length > 0) {
      console.log('\n⚠️ Console errors during CRUD testing:', errors);
    }
    
    // At least some CRUD functionality should exist
    expect(successfulCrud).toBeGreaterThan(0);
  });
});

/**
 * Users Management Debug Test
 * 
 * Comprehensive debugging test to verify user data loading and display
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

test('Users Management Debug Test', async ({ page }) => {
  console.log('🔍 DEBUGGING: Users management data loading...');
  
  // Capture console logs and network requests
  const consoleLogs = [];
  const networkRequests = [];
  
  page.on('console', msg => {
    consoleLogs.push(`${msg.type()}: ${msg.text()}`);
  });
  
  page.on('request', request => {
    if (request.url().includes('profiles')) {
      networkRequests.push({
        method: request.method(),
        url: request.url(),
        headers: request.headers()
      });
    }
  });
  
  page.on('response', response => {
    if (response.url().includes('profiles')) {
      console.log(`📡 Response: ${response.status()} ${response.url()}`);
    }
  });
  
  const loginSuccess = await loginAsAdmin(page);
  console.log(`✅ Login success: ${loginSuccess}`);
  
  if (loginSuccess) {
    // Navigate to users page
    console.log('🔗 Navigating to users page...');
    await page.goto('/admin/users');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000); // Give time for data loading
    
    console.log('📊 DEBUGGING: Page state analysis...');
    
    // 1. Check page title and basic elements
    const pageTitle = await page.locator('h1').first().textContent();
    console.log(`📋 Page title: "${pageTitle}"`);
    
    // 2. Check loading states
    const hasLoadingText = await page.locator('text="Loading users"').count() > 0;
    const hasErrorText = await page.locator('text="Failed", text="Error"').count() > 0;
    const hasNoUsersText = await page.locator('text="No users found"').count() > 0;
    
    console.log(`🔄 Loading states:`);
    console.log(`  Loading text: ${hasLoadingText}`);
    console.log(`  Error text: ${hasErrorText}`);
    console.log(`  No users text: ${hasNoUsersText}`);
    
    // 3. Check for user content elements
    const userEmails = await page.locator('text=@').count();
    const userRoles = await page.locator('text="USER", text="ADMIN", text="MODERATOR", text="SUPER_ADMIN"').count();
    const userJoinDates = await page.locator('text="Joined"').count();
    const userCards = await page.locator('[class*="card"]').count();
    const userNames = await page.locator('text="admin", text="testuser"').count();
    
    console.log(`👥 User content elements:`);
    console.log(`  Email addresses (@): ${userEmails}`);
    console.log(`  Role badges: ${userRoles}`);
    console.log(`  Join dates: ${userJoinDates}`);
    console.log(`  User cards: ${userCards}`);
    console.log(`  User names: ${userNames}`);
    
    // 4. Check for specific expected users
    const hasAdminUser = await page.locator('text="<EMAIL>"').count() > 0;
    const hasTestUser = await page.locator('text="testuser"').count() > 0;
    const hasSuperAdminRole = await page.locator('text="SUPER_ADMIN"').count() > 0;
    
    console.log(`🎯 Expected user data:`);
    console.log(`  Admin user email: ${hasAdminUser}`);
    console.log(`  Test user: ${hasTestUser}`);
    console.log(`  Super admin role: ${hasSuperAdminRole}`);
    
    // 5. Check dropdown functionality
    const dropdownButtons = await page.locator('button[aria-haspopup="menu"]').count();
    console.log(`🔽 Dropdown buttons: ${dropdownButtons}`);
    
    if (dropdownButtons > 0) {
      console.log('🔗 Testing dropdown functionality...');
      await page.locator('button[aria-haspopup="menu"]').first().click();
      await page.waitForTimeout(1000);
      
      const roleOptions = await page.locator('[role="menuitem"]').count();
      const hasSetAsUser = await page.locator('text="Set as User"').count() > 0;
      const hasSetAsModerator = await page.locator('text="Set as Moderator"').count() > 0;
      
      console.log(`  Role options: ${roleOptions}`);
      console.log(`  Set as User option: ${hasSetAsUser}`);
      console.log(`  Set as Moderator option: ${hasSetAsModerator}`);
      
      // Close dropdown
      await page.keyboard.press('Escape');
      await page.waitForTimeout(500);
    }
    
    // 6. Take comprehensive screenshots
    await page.screenshot({ path: 'test-results/users-debug-full.png', fullPage: true });
    
    // 7. Print network activity
    console.log('\n🌐 Network Activity:');
    networkRequests.forEach((req, i) => {
      console.log(`  ${i + 1}. ${req.method} ${req.url}`);
    });
    
    // 8. Print relevant console logs
    console.log('\n📋 Console Logs (filtered):');
    consoleLogs.filter(log => 
      log.includes('error') || 
      log.includes('Error') || 
      log.includes('user') || 
      log.includes('profile') ||
      log.includes('loading') ||
      log.includes('supabase')
    ).forEach(log => console.log(`  ${log}`));
    
    // 9. Final assessment
    const dataLoaded = userEmails > 0 || userRoles > 0 || hasAdminUser;
    console.log(`\n🎯 FINAL ASSESSMENT:`);
    console.log(`  Data successfully loaded: ${dataLoaded}`);
    console.log(`  Users visible: ${userCards > 0 && (userEmails > 0 || userNames > 0)}`);
    console.log(`  Role management available: ${dropdownButtons > 0}`);
    
    if (dataLoaded) {
      console.log('✅ SUCCESS: User data is now loading and displaying correctly!');
    } else {
      console.log('❌ ISSUE: User data still not displaying properly');
    }
  }
});

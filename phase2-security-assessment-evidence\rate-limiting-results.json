{"attempts": [{"attempt": 1, "error": "locator.isVisible: Unknown engine \"text*\" while parsing selector text*=\"rate limit\", text*=\"too many\", text*=\"slow down\"\nCall log:\n\u001b[2m    - checking visibility of locator('text*=\"rate limit\", text*=\"too many\", text*=\"slow down\"')\u001b[22m\n", "timestamp": "2025-06-05T23:29:03.844Z"}, {"attempt": 2, "error": "locator.isVisible: Unknown engine \"text*\" while parsing selector text*=\"rate limit\", text*=\"too many\", text*=\"slow down\"\nCall log:\n\u001b[2m    - checking visibility of locator('text*=\"rate limit\", text*=\"too many\", text*=\"slow down\"')\u001b[22m\n", "timestamp": "2025-06-05T23:29:05.360Z"}, {"attempt": 3, "error": "locator.isVisible: Unknown engine \"text*\" while parsing selector text*=\"rate limit\", text*=\"too many\", text*=\"slow down\"\nCall log:\n\u001b[2m    - checking visibility of locator('text*=\"rate limit\", text*=\"too many\", text*=\"slow down\"')\u001b[22m\n", "timestamp": "2025-06-05T23:29:06.685Z"}, {"attempt": 4, "error": "locator.isVisible: Unknown engine \"text*\" while parsing selector text*=\"rate limit\", text*=\"too many\", text*=\"slow down\"\nCall log:\n\u001b[2m    - checking visibility of locator('text*=\"rate limit\", text*=\"too many\", text*=\"slow down\"')\u001b[22m\n", "timestamp": "2025-06-05T23:29:08.027Z"}, {"attempt": 5, "error": "locator.isVisible: Unknown engine \"text*\" while parsing selector text*=\"rate limit\", text*=\"too many\", text*=\"slow down\"\nCall log:\n\u001b[2m    - checking visibility of locator('text*=\"rate limit\", text*=\"too many\", text*=\"slow down\"')\u001b[22m\n", "timestamp": "2025-06-05T23:29:09.284Z"}], "rateLimitDetected": false, "averageResponseTime": 0, "timestamp": "2025-06-05T23:29:02.406Z"}
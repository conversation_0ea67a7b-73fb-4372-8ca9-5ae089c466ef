import { test, expect } from '@playwright/test';

test.describe('Activity Cards Interactive Functionality Audit', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the activities page
    await page.goto('http://localhost:5173/activities');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Wait for activities to load (look for activity cards or loading state)
    await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
  });

  test('should display activity cards with real database data', async ({ page }) => {
    // Take a screenshot of the activities page
    await page.screenshot({ path: 'test-results/activities-page-initial.png', fullPage: true });
    
    // Check if activities are loaded
    const activityCards = await page.locator('[data-testid="activity-card"]').count();
    const loadingSpinner = await page.locator('.animate-spin').count();
    const noActivitiesMessage = await page.locator('text="No activities found"').count();
    
    console.log(`Found ${activityCards} activity cards`);
    console.log(`Loading spinners: ${loadingSpinner}`);
    console.log(`No activities messages: ${noActivitiesMessage}`);
    
    // Verify that either activities are loaded or we have a proper empty state
    expect(activityCards > 0 || noActivitiesMessage > 0).toBeTruthy();
  });

  test('should test Join Activity button functionality', async ({ page }) => {
    // Look for Join Activity buttons
    const joinButtons = page.locator('button:has-text("Join Activity"), button:has-text("Join")');
    const joinButtonCount = await joinButtons.count();

    console.log(`Found ${joinButtonCount} Join Activity buttons`);

    if (joinButtonCount > 0) {
      // Click the first Join Activity button
      await joinButtons.first().click();

      // Wait for any response (success message, error, or state change)
      await page.waitForTimeout(3000);

      // Take screenshot after clicking
      await page.screenshot({ path: 'test-results/after-join-click.png', fullPage: true });

      // Check for success/error messages or button state changes
      const successMessage = await page.locator('text="Joined activity", text="Successfully joined", text="Please log in"').count();
      const errorMessage = await page.locator('text="Failed", text="Error"').count();
      const buttonStateChanged = await page.locator('button:has-text("Leave"), button:has-text("Joined")').count();
      const loadingSpinner = await page.locator('.animate-spin').count();

      console.log(`Success/login messages: ${successMessage}`);
      console.log(`Error messages: ${errorMessage}`);
      console.log(`Button state changed: ${buttonStateChanged}`);
      console.log(`Loading spinners: ${loadingSpinner}`);
    }
  });

  test('should test Details button functionality', async ({ page }) => {
    // Look for Details buttons
    const detailsButtons = page.locator('button:has-text("Details"), button:has-text("View Details")');
    const detailsButtonCount = await detailsButtons.count();

    console.log(`Found ${detailsButtonCount} Details buttons`);

    if (detailsButtonCount > 0) {
      // Click the first Details button
      await detailsButtons.first().click();

      // Wait for modal or navigation
      await page.waitForTimeout(2000);

      // Take screenshot after clicking
      await page.screenshot({ path: 'test-results/after-details-click.png', fullPage: true });

      // Check for modal, new page, or expanded content
      const modal = await page.locator('[role="dialog"], .modal, [data-testid="activity-modal"]').count();
      const expandedContent = await page.locator('[data-testid="activity-details"]').count();
      const modalBackdrop = await page.locator('.fixed.inset-0.bg-black\\/50').count();
      const modalContent = await page.locator('text="About This Activity", text="Details", text="Close"').count();

      console.log(`Modals opened: ${modal}`);
      console.log(`Expanded content: ${expandedContent}`);
      console.log(`Modal backdrop: ${modalBackdrop}`);
      console.log(`Modal content elements: ${modalContent}`);
    }
  });

  test('should test Favorites/Heart button functionality', async ({ page }) => {
    // Look for heart/favorite buttons
    const favoriteButtons = page.locator('button:has([data-lucide="heart"]), button[aria-label*="favorite"], button[aria-label*="heart"]');
    const favoriteButtonCount = await favoriteButtons.count();
    
    console.log(`Found ${favoriteButtonCount} Favorite buttons`);
    
    if (favoriteButtonCount > 0) {
      // Click the first favorite button
      await favoriteButtons.first().click();
      
      // Wait for state change
      await page.waitForTimeout(2000);
      
      // Take screenshot after clicking
      await page.screenshot({ path: 'test-results/after-favorite-click.png', fullPage: true });
      
      // Check for visual state changes (filled heart, color change, etc.)
      const filledHearts = await page.locator('[data-lucide="heart"].fill-current, .text-red-400, .text-red-500').count();
      
      console.log(`Filled hearts after click: ${filledHearts}`);
    }
  });

  test('should verify card layout and styling', async ({ page }) => {
    // Check for proper card structure
    const cards = page.locator('.bg-white\\/5, .bg-white\\/10, [class*="card"]');
    const cardCount = await cards.count();
    
    console.log(`Found ${cardCount} styled cards`);
    
    if (cardCount > 0) {
      // Check first card structure
      const firstCard = cards.first();
      
      // Verify card has title
      const hasTitle = await firstCard.locator('h4, h3, [class*="title"], [class*="font-semibold"]').count() > 0;
      
      // Verify card has description
      const hasDescription = await firstCard.locator('p, [class*="description"]').count() > 0;
      
      // Verify card has action buttons
      const hasButtons = await firstCard.locator('button').count() > 0;
      
      console.log(`Card has title: ${hasTitle}`);
      console.log(`Card has description: ${hasDescription}`);
      console.log(`Card has buttons: ${hasButtons}`);
      
      // Take detailed screenshot of first card
      await firstCard.screenshot({ path: 'test-results/first-activity-card.png' });
    }
  });

  test('should test responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    await page.screenshot({ path: 'test-results/activities-mobile.png', fullPage: true });
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(1000);
    await page.screenshot({ path: 'test-results/activities-tablet.png', fullPage: true });
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(1000);
    await page.screenshot({ path: 'test-results/activities-desktop.png', fullPage: true });
  });
});

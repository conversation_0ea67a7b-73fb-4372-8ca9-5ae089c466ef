/**
 * Unified Cache Service
 * 
 * High-performance caching layer for Festival Family application.
 * Implements multiple caching strategies for optimal performance.
 * 
 * @module UnifiedCacheService
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { CACHE_DURATIONS, STORAGE_KEYS } from '@/lib/data/constants';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  key: string;
}

interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  hitRate: number;
}

interface CacheOptions {
  ttl?: number;
  priority?: 'low' | 'medium' | 'high';
  compress?: boolean;
  persist?: boolean;
}

class UnifiedCacheService {
  private memoryCache = new Map<string, CacheEntry<any>>();
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    hitRate: 0
  };
  private maxMemorySize = 100; // Maximum number of items in memory cache
  private compressionThreshold = 1024; // Compress data larger than 1KB

  constructor() {
    // Clean up expired entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
    
    // Initialize performance monitoring
    this.initializePerformanceMonitoring();
  }

  /**
   * Get data from cache with fallback to fetch function
   */
  async get<T>(
    key: string,
    fetchFn?: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T | null> {
    const startTime = performance.now();
    
    try {
      // Try memory cache first
      const memoryResult = this.getFromMemory<T>(key);
      if (memoryResult !== null) {
        this.recordHit(performance.now() - startTime);
        return memoryResult;
      }

      // Try persistent storage if enabled
      if (options.persist) {
        const persistentResult = await this.getFromPersistentStorage<T>(key);
        if (persistentResult !== null) {
          // Store in memory for faster access
          this.setInMemory(key, persistentResult, options.ttl || CACHE_DURATIONS.MEDIUM);
          this.recordHit(performance.now() - startTime);
          return persistentResult;
        }
      }

      // Cache miss - fetch data if function provided
      if (fetchFn) {
        const data = await fetchFn();
        await this.set(key, data, options);
        this.recordMiss(performance.now() - startTime);
        return data;
      }

      this.recordMiss(performance.now() - startTime);
      return null;
    } catch (error) {
      console.error('Cache get error:', error);
      this.recordMiss(performance.now() - startTime);
      return null;
    }
  }

  /**
   * Set data in cache
   */
  async set<T>(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    const startTime = performance.now();
    
    try {
      const ttl = options.ttl || CACHE_DURATIONS.MEDIUM;
      
      // Store in memory cache
      this.setInMemory(key, data, ttl);
      
      // Store in persistent storage if enabled
      if (options.persist) {
        await this.setInPersistentStorage(key, data, ttl, options.compress);
      }
      
      this.stats.sets++;
      console.log(`📦 Cache SET: ${key} (${performance.now() - startTime}ms)`);
    } catch (error) {
      console.error('Cache set error:', error);
    }
  }

  /**
   * Delete data from cache
   */
  async delete(key: string): Promise<void> {
    try {
      // Remove from memory
      this.memoryCache.delete(key);
      
      // Remove from persistent storage
      localStorage.removeItem(this.getPersistentKey(key));
      
      this.stats.deletes++;
      console.log(`🗑️ Cache DELETE: ${key}`);
    } catch (error) {
      console.error('Cache delete error:', error);
    }
  }

  /**
   * Clear all cache data
   */
  async clear(): Promise<void> {
    try {
      // Clear memory cache
      this.memoryCache.clear();
      
      // Clear persistent storage
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(STORAGE_KEYS.CACHE_PREFIX)) {
          localStorage.removeItem(key);
        }
      });
      
      console.log('🧹 Cache cleared');
    } catch (error) {
      console.error('Cache clear error:', error);
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const totalRequests = this.stats.hits + this.stats.misses;
    this.stats.hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0;
    return { ...this.stats };
  }

  /**
   * Prefetch data for better performance
   */
  async prefetch<T>(key: string, fetchFn: () => Promise<T>, options: CacheOptions = {}): Promise<void> {
    try {
      // Only prefetch if not already cached
      const existing = this.getFromMemory<T>(key);
      if (existing === null) {
        const data = await fetchFn();
        await this.set(key, data, { ...options, priority: 'low' });
        console.log(`🚀 Prefetched: ${key}`);
      }
    } catch (error) {
      console.error('Prefetch error:', error);
    }
  }

  /**
   * Invalidate cache entries by pattern
   */
  async invalidatePattern(pattern: string): Promise<void> {
    try {
      const regex = new RegExp(pattern);
      
      // Invalidate memory cache
      for (const key of this.memoryCache.keys()) {
        if (regex.test(key)) {
          this.memoryCache.delete(key);
        }
      }
      
      // Invalidate persistent storage
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(STORAGE_KEYS.CACHE_PREFIX) && regex.test(key)) {
          localStorage.removeItem(key);
        }
      });
      
      console.log(`🔄 Invalidated pattern: ${pattern}`);
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }

  // Private methods

  private getFromMemory<T>(key: string): T | null {
    const entry = this.memoryCache.get(key);
    if (!entry) return null;
    
    // Check if expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.memoryCache.delete(key);
      return null;
    }
    
    return entry.data;
  }

  private setInMemory<T>(key: string, data: T, ttl: number): void {
    // Implement LRU eviction if cache is full
    if (this.memoryCache.size >= this.maxMemorySize) {
      const oldestKey = this.memoryCache.keys().next().value;
      if (oldestKey) {
        this.memoryCache.delete(oldestKey);
      }
    }
    
    this.memoryCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
      key
    });
  }

  private async getFromPersistentStorage<T>(key: string): Promise<T | null> {
    try {
      const stored = localStorage.getItem(this.getPersistentKey(key));
      if (!stored) return null;
      
      const entry: CacheEntry<T> = JSON.parse(stored);
      
      // Check if expired
      if (Date.now() > entry.timestamp + entry.ttl) {
        localStorage.removeItem(this.getPersistentKey(key));
        return null;
      }
      
      return entry.data;
    } catch (error) {
      console.error('Persistent storage get error:', error);
      return null;
    }
  }

  private async setInPersistentStorage<T>(
    key: string, 
    data: T, 
    ttl: number, 
    compress = false
  ): Promise<void> {
    try {
      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        ttl,
        key
      };
      
      let serialized = JSON.stringify(entry);
      
      // Compress if data is large and compression is enabled
      if (compress && serialized.length > this.compressionThreshold) {
        // Simple compression placeholder - could implement actual compression
        console.log(`📦 Large data cached: ${key} (${serialized.length} bytes)`);
      }
      
      localStorage.setItem(this.getPersistentKey(key), serialized);
    } catch (error) {
      console.error('Persistent storage set error:', error);
    }
  }

  private getPersistentKey(key: string): string {
    return `${STORAGE_KEYS.CACHE_PREFIX}${key}`;
  }

  private cleanup(): void {
    const now = Date.now();
    let cleaned = 0;
    
    // Clean memory cache
    for (const [key, entry] of this.memoryCache.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        this.memoryCache.delete(key);
        cleaned++;
      }
    }
    
    // Clean persistent storage
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(STORAGE_KEYS.CACHE_PREFIX)) {
        try {
          const stored = localStorage.getItem(key);
          if (stored) {
            const entry = JSON.parse(stored);
            if (now > entry.timestamp + entry.ttl) {
              localStorage.removeItem(key);
              cleaned++;
            }
          }
        } catch (error) {
          // Remove corrupted entries
          localStorage.removeItem(key);
          cleaned++;
        }
      }
    });
    
    if (cleaned > 0) {
      console.log(`🧹 Cache cleanup: removed ${cleaned} expired entries`);
    }
  }

  private recordHit(duration: number): void {
    this.stats.hits++;
    if (duration > 50) {
      console.warn(`⚠️ Slow cache hit: ${duration}ms`);
    }
  }

  private recordMiss(duration: number): void {
    this.stats.misses++;
  }

  private initializePerformanceMonitoring(): void {
    // Monitor cache performance
    setInterval(() => {
      const stats = this.getStats();
      if (stats.hitRate < 70 && (stats.hits + stats.misses) > 100) {
        console.warn(`⚠️ Low cache hit rate: ${stats.hitRate.toFixed(1)}%`);
      }
    }, 60000); // Check every minute
  }
}

// Export singleton instance
export const unifiedCacheService = new UnifiedCacheService();

// Cache key generators for common patterns
export const CacheKeys = {
  user: (userId: string) => `user:${userId}`,
  activity: (activityId: string) => `activity:${activityId}`,
  activities: (filters?: string) => `activities:${filters || 'all'}`,
  userActivities: (userId: string) => `user:${userId}:activities`,
  userFavorites: (userId: string) => `user:${userId}:favorites`,
  notifications: (userId: string) => `notifications:${userId}`,
  feed: (type: string, limit: number) => `feed:${type}:${limit}`,
  analytics: (timeRange: string) => `analytics:${timeRange}`,
  dashboard: (userId: string) => `dashboard:${userId}`,
  profile: (userId: string) => `profile:${userId}`
};

export default unifiedCacheService;

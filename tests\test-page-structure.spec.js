/**
 * Test Page Structure
 * 
 * Examine the actual HTML structure of the Activities page
 */

import { test, expect } from '@playwright/test';

test('Test Page Structure', async ({ page }) => {
  console.log('🔍 Examining Activities page structure...');
  
  // Navigate to activities page
  await page.goto('/activities');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(8000); // Give extra time for data loading
  
  // Take screenshot
  await page.screenshot({ path: 'test-results/page-structure-full.png', fullPage: true });
  
  // Get page content
  const bodyText = await page.locator('body').textContent();
  const bodyLength = bodyText.length;
  
  console.log(`📊 Page Analysis:`);
  console.log(`  Body text length: ${bodyLength} characters`);
  console.log(`  Has content: ${bodyLength > 100}`);
  
  // Check for specific text content
  const hasActivitiesText = bodyText.includes('Activities');
  const hasFestivalText = bodyText.includes('Festival');
  const hasSearchText = bodyText.includes('Search');
  const hasTabText = bodyText.includes('Meetup') || bodyText.includes('Daily');
  
  console.log(`  Contains "Activities": ${hasActivitiesText}`);
  console.log(`  Contains "Festival": ${hasFestivalText}`);
  console.log(`  Contains "Search": ${hasSearchText}`);
  console.log(`  Contains tab text: ${hasTabText}`);
  
  // Check for specific activity titles from our database
  const hasTestActivity = bodyText.includes('Complete Pipeline Test') || bodyText.includes('Daily Fam Meet');
  const hasPipelineActivity = bodyText.includes('Pipeline Test Activity');
  const hasSchemaActivity = bodyText.includes('Schema Fixed Test Activity');
  
  console.log(`  Has test activities: ${hasTestActivity}`);
  console.log(`  Has pipeline activity: ${hasPipelineActivity}`);
  console.log(`  Has schema activity: ${hasSchemaActivity}`);
  
  // Check HTML structure
  const allElements = await page.locator('*').count();
  const divElements = await page.locator('div').count();
  const buttonElements = await page.locator('button').count();
  const cardElements = await page.locator('[class*="card"], .card').count();
  
  console.log(`📋 HTML Structure:`);
  console.log(`  Total elements: ${allElements}`);
  console.log(`  Div elements: ${divElements}`);
  console.log(`  Button elements: ${buttonElements}`);
  console.log(`  Card elements: ${cardElements}`);
  
  // Check for specific classes
  const hasCardClass = await page.locator('.card').count();
  const hasCardContent = await page.locator('[class*="CardContent"]').count();
  const hasMotionDiv = await page.locator('[class*="motion"]').count();
  const hasGridClass = await page.locator('.grid').count();
  
  console.log(`  .card class: ${hasCardClass}`);
  console.log(`  CardContent: ${hasCardContent}`);
  console.log(`  Motion divs: ${hasMotionDiv}`);
  console.log(`  Grid class: ${hasGridClass}`);
  
  // Look for activity-specific content
  const activityTitles = await page.locator('h4, h3, h2, h1').allTextContents();
  console.log(`📝 Found titles: ${activityTitles.slice(0, 5).join(', ')}`);
  
  // Check for "No activities found" message
  const noActivitiesMessage = await page.locator('text="No activities found"').isVisible();
  console.log(`  "No activities found" visible: ${noActivitiesMessage}`);
  
  // Check current tab
  const activeTab = await page.locator('[data-state="active"]').textContent();
  console.log(`  Active tab: "${activeTab}"`);
  
  // Try different tab
  console.log('\n🔄 Testing tab switching...');
  const dailyTab = page.locator('[value="daily"]');
  if (await dailyTab.isVisible()) {
    await dailyTab.click();
    await page.waitForTimeout(3000);
    
    const newBodyText = await page.locator('body').textContent();
    const newHasActivities = newBodyText.includes('Complete Pipeline Test') || newBodyText.includes('Daily Fam Meet');
    console.log(`  After switching to daily tab, has activities: ${newHasActivities}`);
    
    await page.screenshot({ path: 'test-results/page-structure-daily-tab.png', fullPage: true });
  }
  
  console.log('✅ Page structure analysis completed');
});

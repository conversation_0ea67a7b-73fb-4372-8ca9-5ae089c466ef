/**
 * Evidence-Based Production Readiness Testing Suite
 * 
 * This comprehensive test suite uses <PERSON><PERSON> to perform real browser automation
 * and collect evidence of actual functionality through screenshots, console logs,
 * performance metrics, and user flow verification.
 */

import { test, expect } from '@playwright/test';
import { promises as fs } from 'fs';
import path from 'path';

// Test configuration
const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'test-evidence';
const TEST_USER_EMAIL = `test.user.${Date.now()}@gmail.com`;
const TEST_USER_PASSWORD = 'TestPassword123!';

// Ensure evidence directory exists
test.beforeAll(async () => {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory created: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory already exists: ${EVIDENCE_DIR}`);
  }
});

/**
 * PHASE 1: APPLICATION LOAD & ACCESSIBILITY VERIFICATION
 */
test.describe('Phase 1: Application Load & Accessibility', () => {
  
  test('1.1 Initial Application Load', async ({ page }) => {
    console.log('🔍 Testing initial application load...');
    
    // Start performance monitoring
    const startTime = Date.now();
    
    // Navigate to application
    const response = await page.goto(APP_URL, { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    const loadTime = Date.now() - startTime;
    
    // Collect evidence
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/01-initial-load.png`,
      fullPage: true 
    });
    
    // Check response status
    expect(response.status()).toBe(200);
    
    // Verify page title
    const title = await page.title();
    expect(title).toBeTruthy();
    
    // Check for React app mounting
    await expect(page.locator('#root')).toBeVisible();
    
    // Collect console logs
    const consoleLogs = [];
    page.on('console', msg => {
      consoleLogs.push(`${msg.type()}: ${msg.text()}`);
    });
    
    // Save evidence
    await fs.writeFile(
      `${EVIDENCE_DIR}/01-load-evidence.json`,
      JSON.stringify({
        loadTime,
        status: response.status(),
        title,
        url: page.url(),
        timestamp: new Date().toISOString(),
        consoleLogs
      }, null, 2)
    );
    
    console.log(`✅ Application loaded in ${loadTime}ms`);
    console.log(`📄 Page title: "${title}"`);
    console.log(`📸 Screenshot saved: 01-initial-load.png`);
  });

  test('1.2 Navigation Menu Verification', async ({ page }) => {
    console.log('🧭 Testing navigation menu...');
    
    await page.goto(APP_URL);
    
    // Look for navigation elements
    const navElements = await page.locator('nav, [role="navigation"], .nav, .navbar').count();
    
    // Take screenshot of navigation
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/02-navigation.png`,
      fullPage: true 
    });
    
    // Check for common navigation links
    const commonLinks = ['Home', 'Login', 'Register', 'About', 'Profile', 'Dashboard'];
    const foundLinks = [];
    
    for (const linkText of commonLinks) {
      const link = page.locator(`text="${linkText}"`).first();
      if (await link.isVisible()) {
        foundLinks.push(linkText);
      }
    }
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/02-navigation-evidence.json`,
      JSON.stringify({
        navElementsFound: navElements,
        foundLinks,
        timestamp: new Date().toISOString()
      }, null, 2)
    );
    
    console.log(`🔗 Found ${foundLinks.length} navigation links: ${foundLinks.join(', ')}`);
  });

  test('1.3 Console Error Check', async ({ page }) => {
    console.log('🚨 Checking for console errors...');
    
    const errors = [];
    const warnings = [];
    const logs = [];
    
    page.on('console', msg => {
      const text = msg.text();
      switch (msg.type()) {
        case 'error':
          errors.push(text);
          break;
        case 'warning':
          warnings.push(text);
          break;
        default:
          logs.push(`${msg.type()}: ${text}`);
      }
    });
    
    await page.goto(APP_URL);
    await page.waitForTimeout(3000); // Wait for any async operations
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/03-console-logs.json`,
      JSON.stringify({
        errors,
        warnings,
        logs,
        timestamp: new Date().toISOString()
      }, null, 2)
    );
    
    console.log(`❌ Console errors: ${errors.length}`);
    console.log(`⚠️ Console warnings: ${warnings.length}`);
    console.log(`📝 Console logs: ${logs.length}`);
    
    // Fail test if critical errors found
    if (errors.length > 0) {
      console.log('Critical errors found:', errors);
    }
  });
});

/**
 * PHASE 2: AUTHENTICATION SYSTEM VERIFICATION
 */
test.describe('Phase 2: Authentication System', () => {
  
  test('2.1 Registration Page Access', async ({ page }) => {
    console.log('📝 Testing registration page access...');
    
    await page.goto(APP_URL);
    
    // Look for registration link/button
    const registerButton = page.locator('text=/register|sign up|create account/i').first();
    
    if (await registerButton.isVisible()) {
      await registerButton.click();
      await page.waitForLoadState('networkidle');
      
      await page.screenshot({ 
        path: `${EVIDENCE_DIR}/04-registration-page.png`,
        fullPage: true 
      });
      
      // Check for registration form elements
      const emailField = page.locator('input[type="email"], input[name*="email"]').first();
      const passwordField = page.locator('input[type="password"]').first();
      const submitButton = page.locator('button[type="submit"], button:has-text("register"), button:has-text("sign up")').first();
      
      const formElements = {
        hasEmailField: await emailField.isVisible(),
        hasPasswordField: await passwordField.isVisible(),
        hasSubmitButton: await submitButton.isVisible()
      };
      
      await fs.writeFile(
        `${EVIDENCE_DIR}/04-registration-evidence.json`,
        JSON.stringify({
          registrationPageAccessible: true,
          formElements,
          currentUrl: page.url(),
          timestamp: new Date().toISOString()
        }, null, 2)
      );
      
      console.log('✅ Registration page accessible');
      console.log(`📧 Email field: ${formElements.hasEmailField ? '✅' : '❌'}`);
      console.log(`🔒 Password field: ${formElements.hasPasswordField ? '✅' : '❌'}`);
      console.log(`🔘 Submit button: ${formElements.hasSubmitButton ? '✅' : '❌'}`);
    } else {
      console.log('⚠️ Registration link not found on main page');
      
      // Try direct navigation to common registration routes
      const registrationRoutes = ['/register', '/signup', '/auth/register', '/auth'];
      
      for (const route of registrationRoutes) {
        try {
          await page.goto(`${APP_URL}${route}`);
          await page.waitForLoadState('networkidle');
          
          const hasForm = await page.locator('form').isVisible();
          if (hasForm) {
            await page.screenshot({ 
              path: `${EVIDENCE_DIR}/04-registration-page-direct.png`,
              fullPage: true 
            });
            
            console.log(`✅ Registration page found at: ${route}`);
            break;
          }
        } catch (error) {
          console.log(`❌ Route ${route} not accessible: ${error.message}`);
        }
      }
    }
  });

  test('2.2 User Registration Flow', async ({ page }) => {
    console.log('👤 Testing user registration flow...');
    
    // Navigate to registration page
    await page.goto(`${APP_URL}/auth`); // Assuming auth route based on previous tests
    
    try {
      // Fill registration form
      await page.fill('input[type="email"]', TEST_USER_EMAIL);
      await page.fill('input[type="password"]', TEST_USER_PASSWORD);
      
      // Look for additional fields
      const nameField = page.locator('input[name*="name"], input[placeholder*="name"]').first();
      if (await nameField.isVisible()) {
        await nameField.fill('Test User');
      }
      
      // Take screenshot before submission
      await page.screenshot({ 
        path: `${EVIDENCE_DIR}/05-registration-form-filled.png`,
        fullPage: true 
      });
      
      // Submit form
      await page.click('button[type="submit"]');
      await page.waitForTimeout(3000); // Wait for response
      
      // Take screenshot after submission
      await page.screenshot({ 
        path: `${EVIDENCE_DIR}/06-registration-result.png`,
        fullPage: true 
      });
      
      // Check for success/error messages
      const successMessage = await page.locator('text=/success|registered|created/i').isVisible();
      const errorMessage = await page.locator('text=/error|failed|invalid/i').isVisible();
      
      await fs.writeFile(
        `${EVIDENCE_DIR}/05-registration-flow-evidence.json`,
        JSON.stringify({
          testEmail: TEST_USER_EMAIL,
          formSubmitted: true,
          successMessage,
          errorMessage,
          currentUrl: page.url(),
          timestamp: new Date().toISOString()
        }, null, 2)
      );
      
      console.log(`📧 Test email: ${TEST_USER_EMAIL}`);
      console.log(`✅ Success message: ${successMessage ? '✅' : '❌'}`);
      console.log(`❌ Error message: ${errorMessage ? '⚠️' : '✅'}`);
      
    } catch (error) {
      console.log(`❌ Registration flow failed: ${error.message}`);
      
      await page.screenshot({ 
        path: `${EVIDENCE_DIR}/05-registration-error.png`,
        fullPage: true 
      });
    }
  });
});

/**
 * PHASE 3: DATABASE INTEGRATION VERIFICATION
 */
test.describe('Phase 3: Database Integration', () => {
  
  test('3.1 Profile Data Display', async ({ page }) => {
    console.log('👤 Testing profile data display...');
    
    await page.goto(APP_URL);
    
    // Try to access profile page
    const profileRoutes = ['/profile', '/dashboard', '/user'];
    
    for (const route of profileRoutes) {
      try {
        await page.goto(`${APP_URL}${route}`);
        await page.waitForLoadState('networkidle');
        
        await page.screenshot({ 
          path: `${EVIDENCE_DIR}/07-profile-page-${route.replace('/', '')}.png`,
          fullPage: true 
        });
        
        // Check for profile-related content
        const hasProfileContent = await page.locator('text=/profile|user|account/i').isVisible();
        
        if (hasProfileContent) {
          console.log(`✅ Profile content found at: ${route}`);
          break;
        }
      } catch (error) {
        console.log(`⚠️ Route ${route} not accessible`);
      }
    }
  });
});

/**
 * PHASE 4: PERFORMANCE VERIFICATION
 */
test.describe('Phase 4: Performance Verification', () => {
  
  test('4.1 Page Load Performance', async ({ page }) => {
    console.log('⚡ Testing page load performance...');
    
    const performanceMetrics = [];
    
    // Test multiple page loads
    for (let i = 0; i < 3; i++) {
      const startTime = Date.now();
      
      await page.goto(APP_URL, { waitUntil: 'networkidle' });
      
      const loadTime = Date.now() - startTime;
      performanceMetrics.push(loadTime);
      
      console.log(`🔄 Load ${i + 1}: ${loadTime}ms`);
    }
    
    const averageLoadTime = performanceMetrics.reduce((a, b) => a + b, 0) / performanceMetrics.length;
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/08-performance-metrics.json`,
      JSON.stringify({
        loadTimes: performanceMetrics,
        averageLoadTime,
        timestamp: new Date().toISOString()
      }, null, 2)
    );
    
    console.log(`📊 Average load time: ${averageLoadTime.toFixed(2)}ms`);
    
    // Performance assertion
    expect(averageLoadTime).toBeLessThan(5000); // Should load within 5 seconds
  });
});

/**
 * PHASE 5: CROSS-BROWSER VERIFICATION
 */
test.describe('Phase 5: Cross-Browser Verification', () => {
  
  ['chromium', 'firefox', 'webkit'].forEach(browserName => {
    test(`5.1 ${browserName} compatibility`, async ({ browser }) => {
      console.log(`🌐 Testing ${browserName} compatibility...`);
      
      const context = await browser.newContext();
      const page = await context.newPage();
      
      try {
        await page.goto(APP_URL, { waitUntil: 'networkidle' });
        
        await page.screenshot({ 
          path: `${EVIDENCE_DIR}/09-${browserName}-compatibility.png`,
          fullPage: true 
        });
        
        // Basic functionality check
        const title = await page.title();
        const hasContent = await page.locator('#root').isVisible();
        
        await fs.writeFile(
          `${EVIDENCE_DIR}/09-${browserName}-evidence.json`,
          JSON.stringify({
            browser: browserName,
            title,
            hasContent,
            url: page.url(),
            timestamp: new Date().toISOString()
          }, null, 2)
        );
        
        console.log(`✅ ${browserName}: Page loaded successfully`);
        
      } catch (error) {
        console.log(`❌ ${browserName}: ${error.message}`);
      } finally {
        await context.close();
      }
    });
  });
});

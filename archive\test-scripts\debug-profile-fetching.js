/**
 * Debug Profile Fetching Timeout Root Cause
 * 
 * This script systematically investigates why profile queries timeout:
 * 1. Test direct database connectivity and performance
 * 2. Check RLS (Row Level Security) policies
 * 3. Test different query approaches
 * 4. Measure actual query performance
 * 5. Identify the root cause and propose fixes
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('🔍 Debugging Profile Fetching Timeout Root Cause');
console.log('================================================');

async function debugProfileFetching() {
  try {
    // Step 1: Test basic connectivity and performance
    console.log('🌐 Step 1: Basic Connectivity and Performance Test');
    console.log('--------------------------------------------------');
    
    const connectivityStartTime = Date.now();
    
    const { data: connectivityTest, error: connectivityError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    const connectivityDuration = Date.now() - connectivityStartTime;
    
    if (connectivityError) {
      console.error('❌ Basic connectivity failed:', connectivityError.message);
      console.error('🔍 Error details:', connectivityError);
      return;
    }
    
    console.log(`✅ Basic connectivity successful in ${connectivityDuration}ms`);
    
    if (connectivityDuration > 1000) {
      console.log('⚠️  WARNING: Basic connectivity is slow (>1 second)');
    } else if (connectivityDuration > 500) {
      console.log('⚠️  NOTICE: Basic connectivity is moderate (>500ms)');
    } else {
      console.log('✅ Basic connectivity is fast (<500ms)');
    }
    console.log('');

    // Step 2: Test authentication and session handling
    console.log('🔐 Step 2: Authentication and Session Test');
    console.log('-----------------------------------------');
    
    const authStartTime = Date.now();
    
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testpassword123'
    });
    
    const authDuration = Date.now() - authStartTime;
    
    if (authError) {
      console.error('❌ Authentication failed:', authError.message);
      return;
    }
    
    console.log(`✅ Authentication successful in ${authDuration}ms`);
    console.log('👤 User ID:', authData.user?.id);
    console.log('📧 Email:', authData.user?.email);
    console.log('');

    // Step 3: Test different profile query approaches
    console.log('📊 Step 3: Profile Query Performance Analysis');
    console.log('--------------------------------------------');
    
    const userId = authData.user.id;
    
    // Test 1: Simple profile query (current approach)
    console.log('🧪 Test 3.1: Simple Profile Query (Current Approach)');
    const simpleStartTime = Date.now();
    
    const { data: simpleData, error: simpleError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();
    
    const simpleDuration = Date.now() - simpleStartTime;
    
    if (simpleError) {
      console.error(`❌ Simple query failed in ${simpleDuration}ms:`, simpleError.message);
      console.error('🔍 Error code:', simpleError.code);
      console.error('🔍 Error details:', simpleError.details);
      console.error('🔍 Error hint:', simpleError.hint);
    } else {
      console.log(`✅ Simple query successful in ${simpleDuration}ms`);
      console.log('👤 Profile role:', simpleData.role);
    }
    
    // Test 2: Optimized query with specific fields
    console.log('');
    console.log('🧪 Test 3.2: Optimized Query (Specific Fields)');
    const optimizedStartTime = Date.now();
    
    const { data: optimizedData, error: optimizedError } = await supabase
      .from('profiles')
      .select('id, email, username, role, created_at')
      .eq('id', userId)
      .single();
    
    const optimizedDuration = Date.now() - optimizedStartTime;
    
    if (optimizedError) {
      console.error(`❌ Optimized query failed in ${optimizedDuration}ms:`, optimizedError.message);
    } else {
      console.log(`✅ Optimized query successful in ${optimizedDuration}ms`);
    }
    
    // Test 3: Query without .single() to see if that's the issue
    console.log('');
    console.log('🧪 Test 3.3: Query Without .single()');
    const noSingleStartTime = Date.now();
    
    const { data: noSingleData, error: noSingleError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId);
    
    const noSingleDuration = Date.now() - noSingleStartTime;
    
    if (noSingleError) {
      console.error(`❌ No-single query failed in ${noSingleDuration}ms:`, noSingleError.message);
    } else {
      console.log(`✅ No-single query successful in ${noSingleDuration}ms`);
      console.log(`📊 Returned ${noSingleData.length} records`);
    }
    
    // Test 4: Multiple rapid queries to test consistency
    console.log('');
    console.log('🧪 Test 3.4: Multiple Rapid Queries (Consistency Test)');
    const rapidQueries = [];
    const rapidStartTime = Date.now();
    
    for (let i = 0; i < 5; i++) {
      rapidQueries.push(
        supabase
          .from('profiles')
          .select('id, role')
          .eq('id', userId)
          .single()
      );
    }
    
    const rapidResults = await Promise.all(rapidQueries);
    const rapidTotalDuration = Date.now() - rapidStartTime;
    
    console.log(`📊 5 rapid queries completed in ${rapidTotalDuration}ms (avg: ${rapidTotalDuration/5}ms)`);
    
    rapidResults.forEach((result, index) => {
      if (result.error) {
        console.log(`   Query ${index + 1}: ❌ Failed - ${result.error.message}`);
      } else {
        console.log(`   Query ${index + 1}: ✅ Success - Role: ${result.data.role}`);
      }
    });

    // Step 4: Test RLS policies by querying as different users
    console.log('');
    console.log('🔒 Step 4: RLS Policy Analysis');
    console.log('------------------------------');
    
    // Test querying own profile (should work)
    console.log('🧪 Test 4.1: Query Own Profile (Should Work)');
    const ownProfileStartTime = Date.now();
    
    const { data: ownProfileData, error: ownProfileError } = await supabase
      .from('profiles')
      .select('id, email, role')
      .eq('id', userId)
      .single();
    
    const ownProfileDuration = Date.now() - ownProfileStartTime;
    
    if (ownProfileError) {
      console.error(`❌ Own profile query failed in ${ownProfileDuration}ms:`, ownProfileError.message);
      console.log('🔍 This suggests RLS policy issues');
    } else {
      console.log(`✅ Own profile query successful in ${ownProfileDuration}ms`);
    }
    
    // Test querying all profiles (might be restricted by RLS)
    console.log('');
    console.log('🧪 Test 4.2: Query All Profiles (RLS Test)');
    const allProfilesStartTime = Date.now();
    
    const { data: allProfilesData, error: allProfilesError } = await supabase
      .from('profiles')
      .select('id, email, role')
      .limit(10);
    
    const allProfilesDuration = Date.now() - allProfilesStartTime;
    
    if (allProfilesError) {
      console.error(`❌ All profiles query failed in ${allProfilesDuration}ms:`, allProfilesError.message);
      console.log('🔍 This might indicate RLS restrictions (which is normal)');
    } else {
      console.log(`✅ All profiles query successful in ${allProfilesDuration}ms`);
      console.log(`📊 Retrieved ${allProfilesData.length} profiles`);
    }

    // Step 5: Performance summary and recommendations
    console.log('');
    console.log('📈 Step 5: Performance Summary and Analysis');
    console.log('-------------------------------------------');
    
    const allDurations = [
      { test: 'Basic Connectivity', duration: connectivityDuration },
      { test: 'Authentication', duration: authDuration },
      { test: 'Simple Profile Query', duration: simpleDuration },
      { test: 'Optimized Query', duration: optimizedDuration },
      { test: 'No-Single Query', duration: noSingleDuration },
      { test: 'Own Profile Query', duration: ownProfileDuration },
      { test: 'All Profiles Query', duration: allProfilesDuration }
    ];
    
    console.log('⏱️  Performance Results:');
    allDurations.forEach(({ test, duration }) => {
      const status = duration < 500 ? '✅ FAST' : 
                    duration < 1000 ? '⚠️  MODERATE' : 
                    duration < 2000 ? '⚠️  SLOW' : '❌ VERY SLOW';
      console.log(`   ${test}: ${duration}ms ${status}`);
    });
    
    const avgDuration = allDurations.reduce((sum, { duration }) => sum + duration, 0) / allDurations.length;
    console.log(`📊 Average query time: ${avgDuration.toFixed(1)}ms`);
    
    if (avgDuration > 2000) {
      console.log('❌ CRITICAL: Average query time exceeds 2 seconds');
      console.log('🔧 RECOMMENDATION: Investigate network connectivity and Supabase performance');
    } else if (avgDuration > 1000) {
      console.log('⚠️  WARNING: Average query time exceeds 1 second');
      console.log('🔧 RECOMMENDATION: Consider query optimization and caching');
    } else {
      console.log('✅ GOOD: Average query time is acceptable');
    }

  } catch (error) {
    console.error('💥 Debug failed with exception:', error);
  }
}

// Run the debug
debugProfileFetching().then(() => {
  console.log('');
  console.log('🎯 Profile Fetching Debug Complete');
  console.log('==================================');
  process.exit(0);
}).catch(error => {
  console.error('💥 Debug suite failed:', error);
  process.exit(1);
});

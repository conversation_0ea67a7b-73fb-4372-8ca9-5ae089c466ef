-- Unified Content Management System
-- This migration creates a comprehensive content management system
-- for all user-facing content in the Festival Family application

-- ============================================================================
-- CONTENT MANAGEMENT TABLE
-- ============================================================================

-- Create content management table for all app content
CREATE TABLE IF NOT EXISTS content_management (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    content_key TEXT NOT NULL UNIQUE, -- Unique identifier for content piece
    content_type TEXT NOT NULL CHECK (content_type IN ('hero', 'marketing', 'contact', 'emergency', 'page_content', 'ui_text')),
    title TEXT,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT true,
    version INTEGER DEFAULT 1,
    language TEXT DEFAULT 'en',
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- ============================================================================
-- USER PREFERENCES TABLE
-- ============================================================================

-- Create user preferences table for settings persistence
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    preference_category TEXT NOT NULL CHECK (preference_category IN ('notifications', 'privacy', 'festival', 'communication', 'accessibility')),
    preference_key TEXT NOT NULL,
    preference_value JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    -- Ensure one preference per user per category/key combination
    UNIQUE(user_id, preference_category, preference_key)
);

-- ============================================================================
-- EMERGENCY AND SAFETY TABLES
-- ============================================================================

-- Create emergency contacts table
CREATE TABLE IF NOT EXISTS emergency_contacts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    festival_id UUID REFERENCES festivals(id) ON DELETE CASCADE,
    contact_type TEXT NOT NULL CHECK (contact_type IN ('festival_organizer', 'medical', 'security', 'local_emergency', 'festival_family')),
    name TEXT NOT NULL,
    phone TEXT,
    email TEXT,
    description TEXT,
    is_primary BOOLEAN DEFAULT false,
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create safety information table
CREATE TABLE IF NOT EXISTS safety_information (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    festival_id UUID REFERENCES festivals(id) ON DELETE CASCADE,
    safety_category TEXT NOT NULL CHECK (safety_category IN ('general', 'medical', 'security', 'weather', 'transportation', 'emergency_procedures')),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    is_alert BOOLEAN DEFAULT false, -- For urgent safety alerts
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW') NOT NULL
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Content management indexes
CREATE INDEX IF NOT EXISTS idx_content_management_content_key ON content_management(content_key);
CREATE INDEX IF NOT EXISTS idx_content_management_content_type ON content_management(content_type);
CREATE INDEX IF NOT EXISTS idx_content_management_is_active ON content_management(is_active);
CREATE INDEX IF NOT EXISTS idx_content_management_language ON content_management(language);

-- User preferences indexes
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_category ON user_preferences(preference_category);
CREATE INDEX IF NOT EXISTS idx_user_preferences_key ON user_preferences(preference_key);

-- Emergency contacts indexes
CREATE INDEX IF NOT EXISTS idx_emergency_contacts_festival_id ON emergency_contacts(festival_id);
CREATE INDEX IF NOT EXISTS idx_emergency_contacts_type ON emergency_contacts(contact_type);
CREATE INDEX IF NOT EXISTS idx_emergency_contacts_is_active ON emergency_contacts(is_active);

-- Safety information indexes
CREATE INDEX IF NOT EXISTS idx_safety_information_festival_id ON safety_information(festival_id);
CREATE INDEX IF NOT EXISTS idx_safety_information_category ON safety_information(safety_category);
CREATE INDEX IF NOT EXISTS idx_safety_information_priority ON safety_information(priority);
CREATE INDEX IF NOT EXISTS idx_safety_information_is_alert ON safety_information(is_alert);

-- ============================================================================
-- ROW LEVEL SECURITY POLICIES
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE content_management ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE emergency_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE safety_information ENABLE ROW LEVEL SECURITY;

-- Content management policies
CREATE POLICY "Anyone can view active content" 
    ON content_management FOR SELECT 
    USING (is_active = true);

CREATE POLICY "Content admins can manage content" 
    ON content_management FOR ALL 
    USING (is_content_admin(auth.uid()));

-- User preferences policies
CREATE POLICY "Users can view their own preferences" 
    ON user_preferences FOR SELECT 
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own preferences" 
    ON user_preferences FOR ALL 
    USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all preferences" 
    ON user_preferences FOR SELECT 
    USING (is_admin(auth.uid()));

-- Emergency contacts policies
CREATE POLICY "Anyone can view active emergency contacts" 
    ON emergency_contacts FOR SELECT 
    USING (is_active = true);

CREATE POLICY "Content admins can manage emergency contacts" 
    ON emergency_contacts FOR ALL 
    USING (is_content_admin(auth.uid()));

-- Safety information policies
CREATE POLICY "Anyone can view active safety information" 
    ON safety_information FOR SELECT 
    USING (is_active = true);

CREATE POLICY "Content admins can manage safety information" 
    ON safety_information FOR ALL 
    USING (is_content_admin(auth.uid()));

-- ============================================================================
-- TRIGGERS FOR UPDATED_AT
-- ============================================================================

-- Add updated_at triggers
DROP TRIGGER IF EXISTS update_content_management_updated_at ON content_management;
CREATE TRIGGER update_content_management_updated_at
    BEFORE UPDATE ON content_management
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_preferences_updated_at ON user_preferences;
CREATE TRIGGER update_user_preferences_updated_at
    BEFORE UPDATE ON user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_emergency_contacts_updated_at ON emergency_contacts;
CREATE TRIGGER update_emergency_contacts_updated_at
    BEFORE UPDATE ON emergency_contacts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_safety_information_updated_at ON safety_information;
CREATE TRIGGER update_safety_information_updated_at
    BEFORE UPDATE ON safety_information
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Function to get content by key
CREATE OR REPLACE FUNCTION get_content_by_key(content_key_param TEXT, language_param TEXT DEFAULT 'en')
RETURNS TABLE (
    id UUID,
    title TEXT,
    content TEXT,
    metadata JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cm.id,
        cm.title,
        cm.content,
        cm.metadata
    FROM content_management cm
    WHERE 
        cm.content_key = content_key_param
        AND cm.language = language_param
        AND cm.is_active = true
    ORDER BY cm.version DESC
    LIMIT 1;
END;
$$;

-- Function to get user preferences by category
CREATE OR REPLACE FUNCTION get_user_preferences(user_id_param UUID, category_param TEXT DEFAULT NULL)
RETURNS TABLE (
    preference_key TEXT,
    preference_value JSONB,
    preference_category TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        up.preference_key,
        up.preference_value,
        up.preference_category
    FROM user_preferences up
    WHERE 
        up.user_id = user_id_param
        AND (category_param IS NULL OR up.preference_category = category_param)
    ORDER BY up.preference_category, up.preference_key;
END;
$$;

-- Function to get emergency contacts for a festival
CREATE OR REPLACE FUNCTION get_emergency_contacts(festival_id_param UUID)
RETURNS TABLE (
    id UUID,
    contact_type TEXT,
    name TEXT,
    phone TEXT,
    email TEXT,
    description TEXT,
    is_primary BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ec.id,
        ec.contact_type,
        ec.name,
        ec.phone,
        ec.email,
        ec.description,
        ec.is_primary
    FROM emergency_contacts ec
    WHERE 
        ec.festival_id = festival_id_param
        AND ec.is_active = true
    ORDER BY 
        ec.is_primary DESC,
        ec.order_index,
        ec.contact_type,
        ec.name;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_content_by_key TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_preferences TO authenticated;
GRANT EXECUTE ON FUNCTION get_emergency_contacts TO authenticated;

-- ============================================================================
-- SAMPLE DATA
-- ============================================================================

-- Insert sample content management data
INSERT INTO content_management (content_key, content_type, title, content, metadata) VALUES
('hero_title', 'hero', 'Welcome to Festival Family', 'Find your tribe at festivals worldwide', '{"editable": true, "section": "hero"}'),
('hero_subtitle', 'hero', 'Connect with like-minded festival-goers', 'Join activities, make friends, and create unforgettable memories together', '{"editable": true, "section": "hero"}'),
('app_tagline', 'marketing', 'Your Festival Community Awaits', 'Connect, coordinate, and celebrate with fellow festival lovers', '{"editable": true, "section": "marketing"}'),
('contact_email', 'contact', 'Contact Email', '<EMAIL>', '{"editable": true, "type": "email"}'),
('contact_support', 'contact', 'Support Email', '<EMAIL>', '{"editable": true, "type": "email"}'),
('emergency_hotline', 'emergency', 'Festival Family Emergency Line', '+31 6 12345678', '{"editable": true, "type": "phone", "available": "24/7"}')
ON CONFLICT (content_key) DO NOTHING;

-- Insert sample user preference defaults
INSERT INTO user_preferences (user_id, preference_category, preference_key, preference_value) 
SELECT 
    p.id,
    'notifications',
    'push_enabled',
    'true'::jsonb
FROM profiles p
WHERE NOT EXISTS (
    SELECT 1 FROM user_preferences up 
    WHERE up.user_id = p.id 
    AND up.preference_category = 'notifications' 
    AND up.preference_key = 'push_enabled'
)
LIMIT 5; -- Only add for first 5 users to avoid overwhelming

-- ============================================================================
-- VALIDATION AND LOGGING
-- ============================================================================

-- Test that all tables are working
DO $$
DECLARE
    content_count INTEGER;
    preferences_count INTEGER;
    emergency_count INTEGER;
    safety_count INTEGER;
BEGIN
    -- Check content_management table
    SELECT COUNT(*) INTO content_count FROM content_management;
    RAISE NOTICE 'SUCCESS: content_management table accessible with % records', content_count;
    
    -- Check user_preferences table
    SELECT COUNT(*) INTO preferences_count FROM user_preferences;
    RAISE NOTICE 'SUCCESS: user_preferences table accessible with % records', preferences_count;
    
    -- Check emergency_contacts table
    SELECT COUNT(*) INTO emergency_count FROM emergency_contacts;
    RAISE NOTICE 'SUCCESS: emergency_contacts table accessible with % records', emergency_count;
    
    -- Check safety_information table
    SELECT COUNT(*) INTO safety_count FROM safety_information;
    RAISE NOTICE 'SUCCESS: safety_information table accessible with % records', safety_count;
    
    -- Test helper functions
    PERFORM get_content_by_key('hero_title');
    RAISE NOTICE 'SUCCESS: get_content_by_key function working';
    
    PERFORM get_user_preferences('00000000-0000-0000-0000-000000000000');
    RAISE NOTICE 'SUCCESS: get_user_preferences function working';
END $$;

-- Log successful migration
DO $$
BEGIN
    RAISE NOTICE 'SUCCESS: Unified content management system created';
    RAISE NOTICE 'INFO: content_management table for all app content';
    RAISE NOTICE 'INFO: user_preferences table for settings persistence';
    RAISE NOTICE 'INFO: emergency_contacts table for safety features';
    RAISE NOTICE 'INFO: safety_information table for emergency procedures';
    RAISE NOTICE 'INFO: Helper functions for content and preference retrieval';
    RAISE NOTICE 'INFO: RLS policies for proper access control';
    RAISE NOTICE 'INFO: Ready for admin content management integration';
END $$;

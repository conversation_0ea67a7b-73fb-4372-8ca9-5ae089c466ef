/**
 * FavoriteButton Component
 * 
 * Reusable favorite button component with database integration.
 * Can be used across different content types (activities, events, festivals, etc.)
 * 
 * Features:
 * - Database-driven favorites management
 * - Optimistic updates for better UX
 * - Loading states and error handling
 * - Customizable appearance and size
 * - Real-time updates across components
 * 
 * @module FavoriteButton
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import React from 'react'
import { Heart, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useFavorites } from '@/hooks/useFavorites'
import { useAuth } from '@/providers/ConsolidatedAuthProvider'
import { cn } from '@/lib/utils'

// ============================================================================
// COMPONENT INTERFACE
// ============================================================================

export interface FavoriteButtonProps {
  itemId: string
  itemType?: string
  size?: 'sm' | 'lg' | 'default'
  variant?: 'default' | 'ghost' | 'outline'
  showLabel?: boolean
  className?: string
  onToggle?: (isFavorite: boolean) => void
}

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

export const FavoriteButton: React.FC<FavoriteButtonProps> = ({
  itemId,
  itemType = 'activity',
  size = 'md',
  variant = 'ghost',
  showLabel = false,
  className,
  onToggle
}) => {
  const { user } = useAuth()
  const {
    isFavorite,
    toggleFavorite,
    isLoading
  } = useFavorites(itemType)

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleClick = async (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!user || isLoading) return

    const wasFavorite = isFavorite(itemId)
    await toggleFavorite(itemId, itemType)
    
    // Call optional callback
    onToggle?.(!wasFavorite)
  }

  // ========================================================================
  // COMPUTED VALUES
  // ========================================================================

  const isItemFavorite = isFavorite(itemId)

  const sizeClasses: Record<string, string> = {
    sm: 'h-8 w-8 p-1',
    default: 'h-9 w-9 p-2',
    lg: 'h-10 w-10 p-2'
  }

  const iconSizes: Record<string, string> = {
    sm: 'h-3 w-3',
    default: 'h-4 w-4',
    lg: 'h-5 w-5'
  }

  const buttonClasses = cn(
    sizeClasses[size],
    isItemFavorite && 'text-red-500 hover:text-red-600',
    className
  )

  // ========================================================================
  // RENDER HELPERS
  // ========================================================================

  const renderIcon = () => {
    if (isLoading) {
      return <Loader2 className={cn('animate-spin', iconSizes[size])} />
    }
    
    return (
      <Heart 
        className={cn(
          iconSizes[size],
          isItemFavorite && 'fill-current'
        )} 
      />
    )
  }

  const renderLabel = () => {
    if (!showLabel) return null
    return isItemFavorite ? 'Favorited' : 'Add to Favorites'
  }

  // ========================================================================
  // RENDER
  // ========================================================================

  // Don't render if user is not authenticated
  if (!user) {
    return null
  }

  return (
    <Button
      variant={variant}
      size={showLabel ? (size === 'default' ? 'default' : 'sm') : 'sm'}
      onClick={handleClick}
      disabled={isLoading}
      className={buttonClasses}
      aria-label={isItemFavorite ? 'Remove from favorites' : 'Add to favorites'}
      title={isItemFavorite ? 'Remove from favorites' : 'Add to favorites'}
    >
      {renderIcon()}
      {renderLabel()}
    </Button>
  )
}

// ============================================================================
// COMPACT VARIANT
// ============================================================================

export interface CompactFavoriteButtonProps {
  itemId: string
  itemType?: string
  className?: string
  onToggle?: (isFavorite: boolean) => void
}

export const CompactFavoriteButton: React.FC<CompactFavoriteButtonProps> = ({
  itemId,
  itemType = 'activity',
  className,
  onToggle
}) => {
  const { user } = useAuth()
  const {
    isFavorite,
    toggleFavorite,
    isLoading
  } = useFavorites(itemType)

  const handleClick = async (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!user || isLoading) return

    const wasFavorite = isFavorite(itemId)
    await toggleFavorite(itemId, itemType)
    onToggle?.(!wasFavorite)
  }

  if (!user) return null

  const isItemFavorite = isFavorite(itemId)

  return (
    <button
      onClick={handleClick}
      disabled={isLoading}
      className={cn(
        'p-1 hover:bg-muted/50 rounded-full transition-colors',
        isItemFavorite && 'text-red-500',
        className
      )}
      aria-label={isItemFavorite ? 'Remove from favorites' : 'Add to favorites'}
      title={isItemFavorite ? 'Remove from favorites' : 'Add to favorites'}
    >
      {isLoading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <Heart 
          className={cn(
            'h-4 w-4',
            isItemFavorite && 'fill-current'
          )} 
        />
      )}
    </button>
  )
}

// ============================================================================
// HEART ICON VARIANT (for inline use)
// ============================================================================

export interface HeartIconProps {
  itemId: string
  itemType?: string
  size?: 'sm' | 'default' | 'lg'
  className?: string
  onToggle?: (isFavorite: boolean) => void
}

export const HeartIcon: React.FC<HeartIconProps> = ({
  itemId,
  itemType = 'activity',
  size = 'default',
  className,
  onToggle
}) => {
  const { user } = useAuth()
  const {
    isFavorite,
    toggleFavorite,
    isLoading
  } = useFavorites(itemType)

  const handleClick = async (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!user || isLoading) return

    const wasFavorite = isFavorite(itemId)
    await toggleFavorite(itemId, itemType)
    onToggle?.(!wasFavorite)
  }

  if (!user) return null

  const isItemFavorite = isFavorite(itemId)

  const iconSizes = {
    sm: 'h-3 w-3',
    default: 'h-4 w-4',
    lg: 'h-5 w-5'
  }

  return (
    <Heart
      onClick={handleClick}
      className={cn(
        iconSizes[size],
        'cursor-pointer transition-colors',
        isItemFavorite
          ? 'fill-red-500 text-red-500'
          : 'text-muted-foreground hover:text-red-400',
        isLoading && 'opacity-50 cursor-not-allowed',
        className
      )}
      aria-label={isItemFavorite ? 'Remove from favorites' : 'Add to favorites'}
    />
  )
}

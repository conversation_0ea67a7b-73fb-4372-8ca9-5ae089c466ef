#!/usr/bin/env node

/**
 * Client-Side Authentication Testing Script
 * 
 * This script tests the authentication system using only client-side methods
 * that are available to the anonymous key.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration');
  console.error('Please check .env.local file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Test data - using a real email domain
const testUser = {
  email: '<EMAIL>',
  password: 'TestPass123!',
  fullName: 'Test User One'
};

/**
 * Test Supabase connection
 */
async function testConnection() {
  console.log('🔄 Testing Supabase connection...');
  
  try {
    // Test a simple query to verify connection
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);

    if (error) {
      console.error(`❌ Connection test failed: ${error.message}`);
      return false;
    }

    console.log('✅ Supabase connection successful');
    return true;
  } catch (error) {
    console.error(`❌ Connection error: ${error.message}`);
    return false;
  }
}

/**
 * Test user registration
 */
async function testRegistration() {
  console.log(`\n🔄 Testing registration for: ${testUser.email}`);
  
  try {
    const { data, error } = await supabase.auth.signUp({
      email: testUser.email,
      password: testUser.password,
      options: {
        data: {
          full_name: testUser.fullName,
          username: testUser.email.split('@')[0]
        }
      }
    });

    if (error) {
      // Check if user already exists
      if (error.message.includes('already registered')) {
        console.log(`⚠️  User ${testUser.email} already exists`);
        return 'exists';
      }
      console.error(`❌ Registration failed: ${error.message}`);
      return false;
    }

    if (data.user) {
      console.log(`✅ Registration successful for: ${testUser.email}`);
      console.log(`   User ID: ${data.user.id}`);
      console.log(`   Email confirmed: ${data.user.email_confirmed_at ? 'Yes' : 'No'}`);
      return data.user;
    }

    console.error('❌ Registration failed: No user data returned');
    return false;
  } catch (error) {
    console.error(`❌ Registration error: ${error.message}`);
    return false;
  }
}

/**
 * Test user login
 */
async function testLogin() {
  console.log(`\n🔄 Testing login for: ${testUser.email}`);
  
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: testUser.email,
      password: testUser.password
    });

    if (error) {
      console.error(`❌ Login failed: ${error.message}`);
      return false;
    }

    if (data.user && data.session) {
      console.log(`✅ Login successful for: ${testUser.email}`);
      console.log(`   User ID: ${data.user.id}`);
      console.log(`   Session expires: ${new Date(data.session.expires_at * 1000).toISOString()}`);
      return { user: data.user, session: data.session };
    }

    console.error('❌ Login failed: No user/session data returned');
    return false;
  } catch (error) {
    console.error(`❌ Login error: ${error.message}`);
    return false;
  }
}

/**
 * Test profile operations
 */
async function testProfile(user) {
  console.log(`\n🔄 Testing profile operations for user: ${user.id}`);
  
  try {
    // Try to fetch profile
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error(`❌ Profile query failed: ${error.message}`);
      return false;
    }

    if (profile) {
      console.log(`✅ Profile found for user: ${user.id}`);
      console.log(`   Username: ${profile.username || 'Not set'}`);
      console.log(`   Role: ${profile.role || 'Not set'}`);
      console.log(`   Full Name: ${profile.full_name || 'Not set'}`);
      return profile;
    } else {
      console.log(`⚠️  No profile found for user: ${user.id}`);
      console.log(`   This might be expected if profile creation is handled by triggers`);
      return 'no_profile';
    }
  } catch (error) {
    console.error(`❌ Profile operations error: ${error.message}`);
    return false;
  }
}

/**
 * Test session management
 */
async function testSession() {
  console.log(`\n🔄 Testing session management...`);
  
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error(`❌ Session retrieval failed: ${error.message}`);
      return false;
    }

    if (session) {
      console.log(`✅ Active session found`);
      console.log(`   User ID: ${session.user.id}`);
      console.log(`   Expires at: ${new Date(session.expires_at * 1000).toISOString()}`);
      return session;
    } else {
      console.log(`⚠️  No active session found`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Session management error: ${error.message}`);
    return false;
  }
}

/**
 * Test logout
 */
async function testLogout() {
  console.log(`\n🔄 Testing logout...`);
  
  try {
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      console.error(`❌ Logout failed: ${error.message}`);
      return false;
    }

    // Verify session is cleared
    const { data: { session } } = await supabase.auth.getSession();
    
    if (session) {
      console.error(`❌ Logout failed: Session still active`);
      return false;
    }

    console.log(`✅ Logout successful - session cleared`);
    return true;
  } catch (error) {
    console.error(`❌ Logout error: ${error.message}`);
    return false;
  }
}

/**
 * Main testing function
 */
async function runClientAuthTests() {
  console.log('🚀 Starting Client-Side Authentication Testing');
  console.log('==============================================');
  
  const results = {
    connection: false,
    registration: false,
    login: false,
    profile: false,
    session: false,
    logout: false
  };

  // Test connection
  results.connection = await testConnection();
  if (!results.connection) {
    console.log('\n❌ Cannot proceed without Supabase connection');
    return results;
  }

  // Test registration
  const registrationResult = await testRegistration();
  results.registration = registrationResult !== false;

  // Test login (whether registration succeeded or user already exists)
  const loginResult = await testLogin();
  results.login = loginResult !== false;

  if (loginResult) {
    // Test profile operations
    const profileResult = await testProfile(loginResult.user);
    results.profile = profileResult !== false;

    // Test session management
    const sessionResult = await testSession();
    results.session = sessionResult !== false;

    // Test logout
    const logoutResult = await testLogout();
    results.logout = logoutResult !== false;
  }

  // Print summary
  console.log('\n📊 CLIENT AUTHENTICATION TESTING SUMMARY');
  console.log('=========================================');
  console.log(`Connection Test: ${results.connection ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Registration Test: ${results.registration ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Login Test: ${results.login ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Profile Test: ${results.profile ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Session Test: ${results.session ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Logout Test: ${results.logout ? '✅ PASS' : '❌ FAIL'}`);
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  const successRate = (passedTests / totalTests * 100).toFixed(1);
  
  console.log(`\nOverall Success Rate: ${passedTests}/${totalTests} (${successRate}%)`);
  
  if (successRate >= 80) {
    console.log('✅ Authentication system is working well!');
  } else if (successRate >= 50) {
    console.log('⚠️  Authentication system has some issues but core functionality works');
  } else {
    console.log('❌ Authentication system needs significant attention');
  }
  
  return results;
}

// Run the tests
runClientAuthTests()
  .then((results) => {
    console.log('\n🏁 Client authentication testing completed');
    
    // Update our production readiness score
    const authCheckpoints = 12; // Total auth checkpoints
    const passedTests = Object.values(results).filter(Boolean).length;
    const authScore = Math.round((passedTests / 6) * authCheckpoints);
    
    console.log(`\n📈 PRODUCTION READINESS UPDATE:`);
    console.log(`Authentication checkpoints completed: ${authScore}/${authCheckpoints}`);
    
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Client authentication testing failed:', error);
    process.exit(1);
  });

export { runClientAuthTests };

# Test info

- Name: Festival Family Admin Dashboard Tests >> Admin Dashboard Authentication
- Location: C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:173:3

# Error details

```
Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
╔═════════════════════════════════════════════════════════════════════════╗
║ Looks like Playwright Test or Playwright was just installed or updated. ║
║ Please run the following command to download new browsers:              ║
║                                                                         ║
║     npx playwright install                                              ║
║                                                                         ║
║ <3 Playwright Team                                                      ║
╚═════════════════════════════════════════════════════════════════════════╝
```

# Test source

```ts
   73 |     // Verify core tables exist
   74 |     const coreTablesExist = tableResults.filter(t => 
   75 |       t.text?.includes('profiles') || 
   76 |       t.text?.includes('festivals') || 
   77 |       t.text?.includes('activities')
   78 |     ).every(t => t.exists);
   79 |     
   80 |     expect(coreTablesExist).toBe(true);
   81 |     
   82 |     // Check for new tables
   83 |     const newTables = ['content_management', 'user_preferences', 'emergency_contacts', 'announcements'];
   84 |     const newTablesExist = newTables.map(tableName => {
   85 |       const tableResult = tableResults.find(t => t.text?.includes(tableName));
   86 |       return { name: tableName, exists: tableResult?.exists || false };
   87 |     });
   88 |     
   89 |     console.log('🆕 New Tables Status:');
   90 |     newTablesExist.forEach(table => {
   91 |       console.log(`${table.exists ? '✅' : '❌'} ${table.name}`);
   92 |     });
   93 |   });
   94 |
   95 |   test('Admin Functions Test', async ({ page }) => {
   96 |     await page.goto('file://' + process.cwd() + '/database-test.html');
   97 |     await page.waitForLoadState('networkidle');
   98 |     
   99 |     // Click admin functions test
  100 |     await page.click('button:has-text("Test Admin Functions")');
  101 |     
  102 |     // Wait for results
  103 |     await page.waitForSelector('#admin-result', { timeout: 10000 });
  104 |     
  105 |     const result = await page.textContent('#admin-result');
  106 |     
  107 |     // Take screenshot
  108 |     await page.screenshot({ 
  109 |       path: 'test-results/admin-functions.png',
  110 |       fullPage: true 
  111 |     });
  112 |     
  113 |     console.log('⚙️ Admin Functions Result:', result);
  114 |     
  115 |     // Verify at least some admin functions work
  116 |     expect(result).toMatch(/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/);
  117 |   });
  118 |
  119 |   test('CRUD Operations Test', async ({ page }) => {
  120 |     await page.goto('file://' + process.cwd() + '/database-test.html');
  121 |     await page.waitForLoadState('networkidle');
  122 |     
  123 |     // Click CRUD test
  124 |     await page.click('button:has-text("Test CRUD")');
  125 |     
  126 |     // Wait for results
  127 |     await page.waitForSelector('#crud-result', { timeout: 15000 });
  128 |     
  129 |     const result = await page.textContent('#crud-result');
  130 |     
  131 |     // Take screenshot
  132 |     await page.screenshot({ 
  133 |       path: 'test-results/crud-operations.png',
  134 |       fullPage: true 
  135 |     });
  136 |     
  137 |     console.log('🔄 CRUD Operations Result:', result);
  138 |     
  139 |     // Verify CRUD operations are accessible
  140 |     expect(result).toMatch(/(READ|CREATE|UPDATE|DELETE)/);
  141 |   });
  142 |
  143 |   test('Profile System Test', async ({ page }) => {
  144 |     await page.goto('file://' + process.cwd() + '/database-test.html');
  145 |     await page.waitForLoadState('networkidle');
  146 |     
  147 |     // Click profile system test
  148 |     await page.click('button:has-text("Test Profile Updates")');
  149 |     
  150 |     // Wait for results
  151 |     await page.waitForSelector('#profile-result', { timeout: 10000 });
  152 |     
  153 |     const result = await page.textContent('#profile-result');
  154 |     
  155 |     // Take screenshot
  156 |     await page.screenshot({ 
  157 |       path: 'test-results/profile-system.png',
  158 |       fullPage: true 
  159 |     });
  160 |     
  161 |     console.log('👤 Profile System Result:', result);
  162 |     
  163 |     // Verify profile system is accessible
  164 |     expect(result).toMatch(/(Profile|profile|fields|storage)/);
  165 |   });
  166 | });
  167 |
  168 | test.describe('Festival Family Admin Dashboard Tests', () => {
  169 |   test.beforeEach(async ({ page }) => {
  170 |     test.setTimeout(60000);
  171 |   });
  172 |
> 173 |   test('Admin Dashboard Authentication', async ({ page }) => {
      |   ^ Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
  174 |     await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
  175 |     await page.waitForLoadState('networkidle');
  176 |     
  177 |     // Click authentication test
  178 |     await page.click('button:has-text("Test Admin Authentication")');
  179 |     
  180 |     // Wait for results
  181 |     await page.waitForSelector('#auth-result', { timeout: 10000 });
  182 |     
  183 |     const result = await page.textContent('#auth-result');
  184 |     
  185 |     // Take screenshot
  186 |     await page.screenshot({ 
  187 |       path: 'test-results/admin-authentication.png',
  188 |       fullPage: true 
  189 |     });
  190 |     
  191 |     console.log('🔐 Admin Authentication Result:', result);
  192 |     
  193 |     // Check if user needs to sign in or is already authenticated
  194 |     if (result?.includes('No active session')) {
  195 |       console.log('⚠️ User needs to sign in for admin testing');
  196 |     } else {
  197 |       expect(result).toMatch(/(authenticated|admin)/i);
  198 |     }
  199 |   });
  200 |
  201 |   test('Admin Functions Status', async ({ page }) => {
  202 |     await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
  203 |     await page.waitForLoadState('networkidle');
  204 |     
  205 |     // Click admin functions test
  206 |     await page.click('button:has-text("Test All Admin Functions")');
  207 |     
  208 |     // Wait for results and grid
  209 |     await page.waitForSelector('#admin-functions-result', { timeout: 10000 });
  210 |     await page.waitForSelector('.status-item', { timeout: 5000 });
  211 |     
  212 |     // Get function status
  213 |     const statusItems = await page.$$('.status-item');
  214 |     const functionResults = [];
  215 |     
  216 |     for (const item of statusItems) {
  217 |       const text = await item.textContent();
  218 |       const className = await item.getAttribute('class');
  219 |       functionResults.push({
  220 |         text: text?.trim(),
  221 |         working: className?.includes('working') || false
  222 |       });
  223 |     }
  224 |     
  225 |     // Take screenshot
  226 |     await page.screenshot({ 
  227 |       path: 'test-results/admin-functions-status.png',
  228 |       fullPage: true 
  229 |     });
  230 |     
  231 |     console.log('⚙️ Admin Functions Status:');
  232 |     functionResults.forEach(func => {
  233 |       console.log(`${func.working ? '✅' : '❌'} ${func.text}`);
  234 |     });
  235 |   });
  236 |
  237 |   test('Content Management Test', async ({ page }) => {
  238 |     await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
  239 |     await page.waitForLoadState('networkidle');
  240 |     
  241 |     // Click content management test
  242 |     await page.click('button:has-text("Test Content CRUD")');
  243 |     
  244 |     // Wait for results
  245 |     await page.waitForSelector('#content-result', { timeout: 15000 });
  246 |     
  247 |     const result = await page.textContent('#content-result');
  248 |     
  249 |     // Take screenshot
  250 |     await page.screenshot({ 
  251 |       path: 'test-results/content-management.png',
  252 |       fullPage: true 
  253 |     });
  254 |     
  255 |     console.log('📝 Content Management Result:', result);
  256 |   });
  257 |
  258 |   test('Emergency Management Test', async ({ page }) => {
  259 |     await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
  260 |     await page.waitForLoadState('networkidle');
  261 |     
  262 |     // Click emergency management test
  263 |     await page.click('button:has-text("Test Emergency CRUD")');
  264 |     
  265 |     // Wait for results
  266 |     await page.waitForSelector('#emergency-result', { timeout: 10000 });
  267 |     
  268 |     const result = await page.textContent('#emergency-result');
  269 |     
  270 |     // Take screenshot
  271 |     await page.screenshot({ 
  272 |       path: 'test-results/emergency-management.png',
  273 |       fullPage: true 
```
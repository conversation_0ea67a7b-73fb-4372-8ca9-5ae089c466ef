/**
 * Test Working User Interactions
 * 
 * Tests the interactive features that we know are working
 */

import { test, expect } from '@playwright/test';

test('Test Working User Interactions', async ({ page }) => {
  console.log('🧪 Testing working user interactions...');
  
  // Navigate to activities page
  await page.goto('/activities');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000);
  
  console.log('📋 Checking for interactive elements...');
  
  // Look for Join Activity buttons
  const joinButtons = await page.locator('button:has-text("Join Activity")').count();
  console.log(`Found ${joinButtons} "Join Activity" buttons`);
  
  // Look for Details buttons  
  const detailsButtons = await page.locator('button:has-text("Details")').count();
  console.log(`Found ${detailsButtons} "Details" buttons`);
  
  // Look for heart/favorite buttons
  const heartButtons = await page.locator('button').filter({ has: page.locator('svg') }).count();
  console.log(`Found ${heartButtons} buttons with SVG icons (likely heart/share buttons)`);
  
  // Look for activity titles (h4 elements)
  const activityTitles = await page.locator('h4').count();
  console.log(`Found ${activityTitles} activity titles (h4 elements)`);
  
  // Look for activity descriptions
  const descriptions = await page.locator('p:has-text("This activity"), p:has-text("Daily gathering"), p:has-text("Official opening")').count();
  console.log(`Found ${descriptions} activity descriptions`);
  
  await page.screenshot({ path: 'test-results/working-interactions-overview.png', fullPage: true });
  
  // Test Join Activity button functionality
  if (joinButtons > 0) {
    console.log('\n🤝 Testing Join Activity button...');
    
    const firstJoinButton = page.locator('button:has-text("Join Activity")').first();
    
    // Get initial button text
    const initialText = await firstJoinButton.textContent();
    console.log(`Initial button text: "${initialText}"`);
    
    // Click the button
    await firstJoinButton.click();
    await page.waitForTimeout(2000);
    
    // Check if button text changed
    const newText = await firstJoinButton.textContent();
    const textChanged = newText !== initialText;
    
    console.log(`New button text: "${newText}"`);
    console.log(`Button text changed: ${textChanged}`);
    
    // Check for toast notifications
    const toastVisible = await page.locator('[class*="toast"], [data-testid*="toast"]').count() > 0;
    console.log(`Toast notification visible: ${toastVisible}`);
    
    await page.screenshot({ path: 'test-results/join-button-after-click.png', fullPage: true });
    
    if (textChanged || toastVisible) {
      console.log('✅ Join Activity button is working!');
    } else {
      console.log('⚠️ Join Activity button clicked but no visible change');
    }
  }
  
  // Test Details button functionality
  if (detailsButtons > 0) {
    console.log('\n🔍 Testing Details button...');
    
    const firstDetailsButton = page.locator('button:has-text("Details")').first();
    
    // Click the button
    const initialUrl = page.url();
    await firstDetailsButton.click();
    await page.waitForTimeout(2000);
    
    const newUrl = page.url();
    const urlChanged = newUrl !== initialUrl;
    
    // Check for modal or popup
    const modalVisible = await page.locator('[role="dialog"], [class*="modal"], [class*="popup"]').count() > 0;
    
    console.log(`URL changed: ${urlChanged} (${initialUrl} → ${newUrl})`);
    console.log(`Modal/popup visible: ${modalVisible}`);
    
    await page.screenshot({ path: 'test-results/details-button-after-click.png', fullPage: true });
    
    if (urlChanged || modalVisible) {
      console.log('✅ Details button is working!');
    } else {
      console.log('⚠️ Details button clicked but no visible navigation/modal');
    }
  }
  
  // Test heart/favorite button functionality
  if (heartButtons > 0) {
    console.log('\n❤️ Testing heart/favorite button...');
    
    // Go back to activities page if we navigated away
    await page.goto('/activities');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Find heart button (look for Heart icon specifically)
    const heartButton = page.locator('button').filter({ has: page.locator('svg') }).first();
    
    if (await heartButton.isVisible()) {
      await heartButton.click();
      await page.waitForTimeout(1000);
      
      // Check for visual changes (color change, fill, etc.)
      const heartFilled = await page.locator('svg[class*="fill-current"]').count() > 0;
      console.log(`Heart appears filled after click: ${heartFilled}`);
      
      await page.screenshot({ path: 'test-results/heart-button-after-click.png', fullPage: true });
      
      if (heartFilled) {
        console.log('✅ Heart/favorite button is working!');
      } else {
        console.log('⚠️ Heart button clicked but no visible change');
      }
    }
  }
  
  // Final assessment
  console.log('\n📊 USER INTERACTION ASSESSMENT:');
  console.log('================================');
  console.log(`Activity content visible: ${activityTitles > 0 ? '✅ YES' : '❌ NO'} (${activityTitles} titles)`);
  console.log(`Join buttons available: ${joinButtons > 0 ? '✅ YES' : '❌ NO'} (${joinButtons} buttons)`);
  console.log(`Details buttons available: ${detailsButtons > 0 ? '✅ YES' : '❌ NO'} (${detailsButtons} buttons)`);
  console.log(`Interactive elements: ${heartButtons > 0 ? '✅ YES' : '❌ NO'} (${heartButtons} icon buttons)`);
  
  const fullyFunctional = activityTitles > 0 && joinButtons > 0 && detailsButtons > 0;
  
  if (fullyFunctional) {
    console.log('🎉 SUCCESS: User activity interactions are fully functional!');
  } else {
    console.log('⚠️ PARTIAL: Some interactive elements may be missing');
  }
  
  console.log('✅ Working interactions test completed');
});

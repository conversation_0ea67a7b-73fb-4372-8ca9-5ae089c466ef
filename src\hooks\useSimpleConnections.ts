/**
 * Simple Connections Hook
 * 
 * Replaces the over-engineered ConnectionService with a simple React hook
 * that provides the same functionality using standard React patterns.
 * 
 * This demonstrates replacing complex service classes with React patterns:
 * - Custom hooks instead of service classes
 * - React Query for data fetching
 * - Simple utility functions instead of complex abstractions
 * 
 * @module useSimpleConnections
 * @version 1.0.0
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/providers/ConsolidatedAuthProvider';
import { toast } from 'react-hot-toast';
import type { Profile } from '@/types';

// ============================================================================
// SIMPLE UTILITY FUNCTIONS (replacing complex service methods)
// ============================================================================

/**
 * Find potential matches based on interests - simplified version
 */
async function findPotentialMatches(userId: string, limit = 10): Promise<Profile[]> {
  try {
    // Get user's profile first
    const { data: userProfile, error: profileError } = await supabase
      .from('profiles')
      .select('interests, location')
      .eq('id', userId)
      .single();

    if (profileError || !userProfile) {
      console.warn('Could not fetch user profile for matching');
      return [];
    }

    // Simple query to find other users (excluding current user)
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .neq('id', userId)
      .limit(limit);

    if (error) {
      console.error('Error finding potential matches:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in findPotentialMatches:', error);
    return [];
  }
}

/**
 * Send connection request - simplified version
 */
async function sendConnectionRequest(fromUserId: string, toUserId: string) {
  const { data, error } = await supabase
    .from('connection_requests')
    .insert({
      from_user_id: fromUserId,
      to_user_id: toUserId,
      status: 'pending',
      created_at: new Date().toISOString()
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

/**
 * Get user connections - simplified version
 */
async function getUserConnections(userId: string) {
  const { data, error } = await supabase
    .from('connections')
    .select(`
      connected_user_id,
      profiles!connections_connected_user_id_fkey(
        id,
        username,
        full_name,
        avatar_url,
        bio,
        interests
      )
    `)
    .eq('user_id', userId);

  if (error) throw error;
  return data || [];
}

/**
 * Get pending connection requests - simplified version
 */
async function getPendingRequests(userId: string) {
  const { data, error } = await supabase
    .from('connection_requests')
    .select(`
      *,
      profiles!connection_requests_from_user_id_fkey(
        id,
        username,
        full_name,
        avatar_url
      )
    `)
    .eq('to_user_id', userId)
    .eq('status', 'pending');

  if (error) throw error;
  return data || [];
}

// ============================================================================
// REACT HOOK (replacing complex service class)
// ============================================================================

/**
 * Simple connections hook using React patterns
 * 
 * Replaces the complex ConnectionService class with a simple hook
 * that provides the same functionality using React Query and utility functions.
 */
export function useSimpleConnections() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // ========================================================================
  // QUERIES (using React Query instead of service methods)
  // ========================================================================

  const potentialMatches = useQuery({
    queryKey: ['potentialMatches', user?.id],
    queryFn: () => findPotentialMatches(user!.id),
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const connections = useQuery({
    queryKey: ['connections', user?.id],
    queryFn: () => getUserConnections(user!.id),
    enabled: !!user?.id,
  });

  const pendingRequests = useQuery({
    queryKey: ['connectionRequests', 'pending', user?.id],
    queryFn: () => getPendingRequests(user!.id),
    enabled: !!user?.id,
  });

  // ========================================================================
  // MUTATIONS (using React Query instead of service methods)
  // ========================================================================

  const sendRequest = useMutation({
    mutationFn: ({ toUserId }: { toUserId: string }) => 
      sendConnectionRequest(user!.id, toUserId),
    onSuccess: () => {
      toast.success('Connection request sent!');
      queryClient.invalidateQueries({ queryKey: ['connectionRequests'] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to send connection request');
    },
  });

  const acceptRequest = useMutation({
    mutationFn: async ({ requestId }: { requestId: string }) => {
      const { error } = await supabase
        .from('connection_requests')
        .update({ status: 'accepted' })
        .eq('id', requestId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      toast.success('Connection request accepted!');
      queryClient.invalidateQueries({ queryKey: ['connectionRequests'] });
      queryClient.invalidateQueries({ queryKey: ['connections'] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to accept connection request');
    },
  });

  const rejectRequest = useMutation({
    mutationFn: async ({ requestId }: { requestId: string }) => {
      const { error } = await supabase
        .from('connection_requests')
        .update({ status: 'rejected' })
        .eq('id', requestId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      toast.success('Connection request rejected');
      queryClient.invalidateQueries({ queryKey: ['connectionRequests'] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to reject connection request');
    },
  });

  // ========================================================================
  // RETURN SIMPLE INTERFACE (replacing complex service interface)
  // ========================================================================

  return {
    // Data
    potentialMatches: potentialMatches.data || [],
    connections: connections.data || [],
    pendingRequests: pendingRequests.data || [],
    
    // Loading states
    isLoadingMatches: potentialMatches.isLoading,
    isLoadingConnections: connections.isLoading,
    isLoadingRequests: pendingRequests.isLoading,
    
    // Actions
    sendConnectionRequest: sendRequest.mutate,
    acceptConnectionRequest: acceptRequest.mutate,
    rejectConnectionRequest: rejectRequest.mutate,
    
    // Action states
    isSendingRequest: sendRequest.isPending,
    isAcceptingRequest: acceptRequest.isPending,
    isRejectingRequest: rejectRequest.isPending,
    
    // Refresh functions
    refreshMatches: () => queryClient.invalidateQueries({ queryKey: ['potentialMatches'] }),
    refreshConnections: () => queryClient.invalidateQueries({ queryKey: ['connections'] }),
    refreshRequests: () => queryClient.invalidateQueries({ queryKey: ['connectionRequests'] }),
  };
}

export default useSimpleConnections;

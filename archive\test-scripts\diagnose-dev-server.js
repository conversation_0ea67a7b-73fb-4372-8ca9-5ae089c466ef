#!/usr/bin/env node

/**
 * Development Server Diagnostic Tool
 * 
 * This script uses <PERSON><PERSON> to diagnose development server issues by:
 * 1. Starting the dev server in the background
 * 2. Using browser automation to test connectivity
 * 3. Capturing detailed error information and screenshots
 * 4. Testing admin functionality if the server is accessible
 */

import { chromium } from 'playwright';
import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'dev-server-diagnostic';
const MAX_WAIT_TIME = 60000; // 60 seconds

// Diagnostic results
const diagnosticResults = {
  timestamp: new Date().toISOString(),
  serverStartup: {},
  browserTests: {},
  adminTests: {},
  errors: [],
  screenshots: [],
  consoleMessages: [],
  networkRequests: []
};

async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

async function captureScreenshot(page, name, description) {
  const filename = `${String(diagnosticResults.screenshots.length + 1).padStart(2, '0')}-${name}.png`;
  const filepath = path.join(EVIDENCE_DIR, filename);
  
  try {
    await page.screenshot({ 
      path: filepath, 
      fullPage: true,
      animations: 'disabled'
    });
    
    diagnosticResults.screenshots.push({
      filename,
      description,
      timestamp: new Date().toISOString()
    });
    
    console.log(`📸 Screenshot: ${filename} - ${description}`);
    return filename;
  } catch (error) {
    console.log(`❌ Screenshot failed: ${error.message}`);
    return null;
  }
}

async function startDevServer() {
  console.log('\n🚀 Starting Development Server');
  console.log('==============================');
  
  return new Promise((resolve, reject) => {
    const devServer = spawn('npm', ['run', 'dev'], {
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true
    });

    let serverOutput = '';
    let serverReady = false;
    
    const timeout = setTimeout(() => {
      if (!serverReady) {
        console.log('⏰ Server startup timeout');
        diagnosticResults.serverStartup.timeout = true;
        diagnosticResults.serverStartup.output = serverOutput;
        resolve({ process: devServer, ready: false, output: serverOutput });
      }
    }, MAX_WAIT_TIME);

    devServer.stdout.on('data', (data) => {
      const output = data.toString();
      serverOutput += output;
      console.log(`📤 Server: ${output.trim()}`);
      
      // Check for server ready indicators (more flexible detection)
      if ((output.includes('Local:') || output.includes('ready in')) &&
          (output.includes('5173') || output.includes('localhost'))) {
        serverReady = true;
        clearTimeout(timeout);
        console.log('✅ Development server is ready!');
        diagnosticResults.serverStartup.success = true;
        diagnosticResults.serverStartup.output = serverOutput;
        resolve({ process: devServer, ready: true, output: serverOutput });
      }

      // Also check if we see the full ready message in accumulated output
      if (serverOutput.includes('ready in') && serverOutput.includes('Local:')) {
        serverReady = true;
        clearTimeout(timeout);
        console.log('✅ Development server is ready! (detected from accumulated output)');
        diagnosticResults.serverStartup.success = true;
        diagnosticResults.serverStartup.output = serverOutput;
        resolve({ process: devServer, ready: true, output: serverOutput });
      }
    });

    devServer.stderr.on('data', (data) => {
      const error = data.toString();
      serverOutput += `ERROR: ${error}`;
      console.log(`❌ Server Error: ${error.trim()}`);
      diagnosticResults.errors.push({
        type: 'server_startup',
        message: error,
        timestamp: new Date().toISOString()
      });
    });

    devServer.on('error', (error) => {
      clearTimeout(timeout);
      console.log(`❌ Failed to start server: ${error.message}`);
      diagnosticResults.serverStartup.error = error.message;
      reject(error);
    });

    devServer.on('exit', (code) => {
      clearTimeout(timeout);
      if (code !== 0 && !serverReady) {
        console.log(`❌ Server exited with code: ${code}`);
        diagnosticResults.serverStartup.exitCode = code;
        diagnosticResults.serverStartup.output = serverOutput;
        resolve({ process: null, ready: false, output: serverOutput });
      }
    });
  });
}

async function testBrowserConnectivity(serverInfo) {
  console.log('\n🌐 Testing Browser Connectivity');
  console.log('===============================');
  
  if (!serverInfo.ready) {
    console.log('⏭️ Skipping browser tests - server not ready');
    diagnosticResults.browserTests.skipped = true;
    diagnosticResults.browserTests.reason = 'Server not ready';
    return null;
  }

  const browser = await chromium.launch({ 
    headless: false,  // Visual inspection
    slowMo: 1000,     // Slow down for observation
    devtools: true    // Open DevTools
  });
  
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  });
  
  const page = await context.newPage();
  
  // Capture console messages
  page.on('console', msg => {
    const message = {
      type: msg.type(),
      text: msg.text(),
      timestamp: new Date().toISOString()
    };
    diagnosticResults.consoleMessages.push(message);
    console.log(`🖥️ Console [${msg.type()}]: ${msg.text()}`);
  });
  
  // Capture network requests
  page.on('request', request => {
    diagnosticResults.networkRequests.push({
      url: request.url(),
      method: request.method(),
      timestamp: new Date().toISOString()
    });
  });
  
  // Capture page errors
  page.on('pageerror', error => {
    diagnosticResults.errors.push({
      type: 'page_error',
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });
    console.log(`❌ Page Error: ${error.message}`);
  });

  try {
    console.log(`📍 Navigating to: ${APP_URL}`);
    
    // Try to load the main page
    const response = await page.goto(APP_URL, { 
      waitUntil: 'domcontentloaded', 
      timeout: 30000 
    });
    
    await captureScreenshot(page, 'main-page-load', 'Main page after initial load');
    
    if (response) {
      diagnosticResults.browserTests.mainPageStatus = response.status();
      diagnosticResults.browserTests.mainPageSuccess = response.ok();
      console.log(`📊 Main page status: ${response.status()}`);
    }
    
    // Wait for any dynamic content to load
    await page.waitForTimeout(3000);
    await captureScreenshot(page, 'main-page-loaded', 'Main page after content load');
    
    // Test navigation to auth page
    console.log('🔐 Testing auth page navigation...');
    try {
      await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded', timeout: 15000 });
      await page.waitForTimeout(2000);
      await captureScreenshot(page, 'auth-page', 'Authentication page');
      diagnosticResults.browserTests.authPageAccessible = true;
    } catch (error) {
      console.log(`❌ Auth page error: ${error.message}`);
      diagnosticResults.browserTests.authPageAccessible = false;
      diagnosticResults.browserTests.authPageError = error.message;
    }
    
    // Test admin page access
    console.log('👑 Testing admin page access...');
    try {
      await page.goto(`${APP_URL}/admin`, { waitUntil: 'domcontentloaded', timeout: 15000 });
      await page.waitForTimeout(2000);
      await captureScreenshot(page, 'admin-page-attempt', 'Admin page access attempt');
      diagnosticResults.browserTests.adminPageAccessible = true;
    } catch (error) {
      console.log(`❌ Admin page error: ${error.message}`);
      diagnosticResults.browserTests.adminPageAccessible = false;
      diagnosticResults.browserTests.adminPageError = error.message;
    }
    
    diagnosticResults.browserTests.success = true;
    return { browser, context, page };
    
  } catch (error) {
    console.log(`❌ Browser connectivity error: ${error.message}`);
    diagnosticResults.browserTests.success = false;
    diagnosticResults.browserTests.error = error.message;
    
    await captureScreenshot(page, 'connectivity-error', 'Browser connectivity error state');
    
    await browser.close();
    return null;
  }
}

async function testAdminFunctionality(browserInfo) {
  if (!browserInfo) {
    console.log('\n⏭️ Skipping admin tests - browser not accessible');
    diagnosticResults.adminTests.skipped = true;
    return;
  }

  console.log('\n👑 Testing Admin Functionality');
  console.log('==============================');
  
  const { page } = browserInfo;
  
  try {
    // Test admin login with known credentials
    await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded' });
    await page.waitForTimeout(2000);
    
    // Try to fill in admin credentials
    const emailInput = page.locator('input[type="email"]').first();
    const passwordInput = page.locator('input[type="password"]').first();
    
    if (await emailInput.isVisible()) {
      await emailInput.fill('<EMAIL>');
      await passwordInput.fill('Admin123!@#');
      
      await captureScreenshot(page, 'admin-credentials', 'Admin credentials entered');
      
      const submitButton = page.locator('button[type="submit"]').first();
      await submitButton.click();
      await page.waitForTimeout(3000);
      
      const currentUrl = page.url();
      diagnosticResults.adminTests.loginAttempted = true;
      diagnosticResults.adminTests.postLoginUrl = currentUrl;
      
      if (!currentUrl.includes('/auth')) {
        diagnosticResults.adminTests.loginSuccess = true;
        console.log('✅ Admin login successful');
        await captureScreenshot(page, 'admin-logged-in', 'After admin login');
      } else {
        diagnosticResults.adminTests.loginSuccess = false;
        console.log('❌ Admin login failed');
        await captureScreenshot(page, 'admin-login-failed', 'Admin login failed');
      }
    } else {
      diagnosticResults.adminTests.loginFormNotFound = true;
      console.log('❌ Login form not found');
    }
    
  } catch (error) {
    console.log(`❌ Admin functionality error: ${error.message}`);
    diagnosticResults.adminTests.error = error.message;
  }
}

async function saveResults() {
  const resultsFile = path.join(EVIDENCE_DIR, 'diagnostic-results.json');
  await fs.writeFile(resultsFile, JSON.stringify(diagnosticResults, null, 2));
  console.log(`💾 Diagnostic results saved: ${resultsFile}`);
}

async function generateSummary() {
  console.log('\n📋 DIAGNOSTIC SUMMARY');
  console.log('====================');
  
  console.log(`🕐 Test Duration: ${new Date().toISOString()}`);
  console.log(`📸 Screenshots Captured: ${diagnosticResults.screenshots.length}`);
  console.log(`💬 Console Messages: ${diagnosticResults.consoleMessages.length}`);
  console.log(`🌐 Network Requests: ${diagnosticResults.networkRequests.length}`);
  console.log(`❌ Errors Found: ${diagnosticResults.errors.length}`);
  
  if (diagnosticResults.serverStartup.success) {
    console.log('✅ Development server started successfully');
  } else {
    console.log('❌ Development server failed to start');
  }
  
  if (diagnosticResults.browserTests.success) {
    console.log('✅ Browser connectivity working');
  } else {
    console.log('❌ Browser connectivity issues');
  }
  
  if (diagnosticResults.adminTests.loginSuccess) {
    console.log('✅ Admin login working');
  } else if (diagnosticResults.adminTests.skipped) {
    console.log('⏭️ Admin tests skipped');
  } else {
    console.log('❌ Admin login issues');
  }
  
  console.log(`\n📁 Evidence saved in: ${EVIDENCE_DIR}`);
  
  if (diagnosticResults.errors.length > 0) {
    console.log('\n⚠️ Key Issues Found:');
    diagnosticResults.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. [${error.type}] ${error.message}`);
    });
  }
}

async function runDiagnostics() {
  console.log('🔍 DEVELOPMENT SERVER DIAGNOSTIC');
  console.log('================================');
  
  await ensureEvidenceDir();
  
  let devServerProcess = null;
  let browserInfo = null;
  
  try {
    // Start development server
    const serverInfo = await startDevServer();
    devServerProcess = serverInfo.process;
    
    // Test browser connectivity
    browserInfo = await testBrowserConnectivity(serverInfo);
    
    // Test admin functionality
    await testAdminFunctionality(browserInfo);
    
    // Generate summary and save results
    await generateSummary();
    await saveResults();
    
  } catch (error) {
    console.error('❌ Diagnostic error:', error);
    diagnosticResults.errors.push({
      type: 'diagnostic_error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  } finally {
    // Cleanup
    if (browserInfo) {
      try {
        await browserInfo.browser.close();
      } catch (error) {
        console.log('⚠️ Browser cleanup error:', error.message);
      }
    }
    
    if (devServerProcess) {
      try {
        devServerProcess.kill();
        console.log('🛑 Development server stopped');
      } catch (error) {
        console.log('⚠️ Server cleanup error:', error.message);
      }
    }
  }
}

// Run diagnostics
runDiagnostics().catch(console.error);

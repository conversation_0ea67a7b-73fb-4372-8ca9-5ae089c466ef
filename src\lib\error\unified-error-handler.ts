/**
 * Unified Error Handler
 * 
 * Comprehensive error handling system for Festival Family application.
 * Provides consistent error handling, retry logic, and user-friendly messages.
 * 
 * @module UnifiedErrorHandler
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { toast } from 'react-hot-toast';

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

export interface RetryOptions {
  maxRetries?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
  retryCondition?: (error: Error) => boolean;
}

export interface ErrorHandlingOptions {
  showToast?: boolean;
  logError?: boolean;
  reportError?: boolean;
  fallbackValue?: any;
  context?: ErrorContext;
  retryOptions?: RetryOptions;
}

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

export interface ProcessedError {
  id: string;
  originalError: Error;
  userMessage: string;
  technicalMessage: string;
  severity: ErrorSeverity;
  context: ErrorContext;
  timestamp: Date;
  shouldRetry: boolean;
  retryCount: number;
}

class UnifiedErrorHandler {
  private errorLog: ProcessedError[] = [];
  private maxLogSize = 100;

  /**
   * Process and handle an error with comprehensive options
   */
  async handleError(
    error: Error | unknown,
    options: ErrorHandlingOptions = {}
  ): Promise<ProcessedError> {
    const processedError = this.processError(error, options.context);
    
    // Log error if enabled
    if (options.logError !== false) {
      this.logError(processedError);
    }

    // Show user-friendly toast if enabled
    if (options.showToast !== false) {
      this.showErrorToast(processedError);
    }

    // Report error for monitoring if enabled
    if (options.reportError) {
      await this.reportError(processedError);
    }

    return processedError;
  }

  /**
   * Execute function with automatic retry logic
   */
  async withRetry<T>(
    fn: () => Promise<T>,
    options: RetryOptions = {}
  ): Promise<T> {
    const {
      maxRetries = 3,
      baseDelay = 1000,
      maxDelay = 10000,
      backoffFactor = 2,
      retryCondition = this.defaultRetryCondition
    } = options;

    let lastError: Error;
    let delay = baseDelay;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const result = await fn();
        
        // Log successful retry if this wasn't the first attempt
        if (attempt > 0) {
          console.log(`✅ Operation succeeded after ${attempt} retries`);
        }
        
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // Don't retry on last attempt or if retry condition fails
        if (attempt === maxRetries || !retryCondition(lastError)) {
          break;
        }

        console.warn(`⚠️ Attempt ${attempt + 1} failed, retrying in ${delay}ms:`, lastError.message);
        
        // Wait before retry
        await this.delay(delay);
        
        // Increase delay for next attempt (exponential backoff)
        delay = Math.min(delay * backoffFactor, maxDelay);
      }
    }

    // All retries failed
    throw lastError!;
  }

  /**
   * Execute function with comprehensive error handling and retry
   */
  async executeWithHandling<T>(
    fn: () => Promise<T>,
    options: ErrorHandlingOptions & RetryOptions = {}
  ): Promise<T | null> {
    try {
      if (options.retryOptions || options.maxRetries) {
        return await this.withRetry(fn, options);
      } else {
        return await fn();
      }
    } catch (error) {
      const processedError = await this.handleError(error, options);
      
      // Return fallback value if provided
      if (options.fallbackValue !== undefined) {
        console.log(`🔄 Using fallback value due to error: ${processedError.userMessage}`);
        return options.fallbackValue;
      }
      
      // Re-throw if no fallback
      throw processedError.originalError;
    }
  }

  /**
   * Get user-friendly error message
   */
  getUserMessage(error: Error | unknown): string {
    if (error instanceof Error) {
      // Database errors
      if (error.message.includes('PGRST')) {
        return 'Database connection issue. Please try again.';
      }
      
      // Network errors
      if (error.message.includes('fetch') || error.message.includes('network')) {
        return 'Network connection issue. Please check your internet connection.';
      }
      
      // Authentication errors
      if (error.message.includes('auth') || error.message.includes('unauthorized')) {
        return 'Authentication required. Please log in again.';
      }
      
      // Permission errors
      if (error.message.includes('permission') || error.message.includes('forbidden')) {
        return 'You don\'t have permission to perform this action.';
      }
      
      // Validation errors
      if (error.message.includes('validation') || error.message.includes('invalid')) {
        return 'Please check your input and try again.';
      }
      
      // Rate limiting
      if (error.message.includes('rate limit') || error.message.includes('too many')) {
        return 'Too many requests. Please wait a moment and try again.';
      }
      
      // Generic fallback
      return 'Something went wrong. Please try again.';
    }
    
    return 'An unexpected error occurred.';
  }

  /**
   * Determine error severity
   */
  getErrorSeverity(error: Error | unknown): ErrorSeverity {
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      
      // Critical errors
      if (message.includes('critical') || message.includes('fatal')) {
        return 'critical';
      }
      
      // High severity
      if (message.includes('auth') || message.includes('permission') || message.includes('security')) {
        return 'high';
      }
      
      // Medium severity
      if (message.includes('network') || message.includes('timeout') || message.includes('database')) {
        return 'medium';
      }
      
      // Low severity (validation, user input, etc.)
      return 'low';
    }
    
    return 'medium';
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    total: number;
    bySeverity: Record<ErrorSeverity, number>;
    recent: ProcessedError[];
    mostCommon: string[];
  } {
    const bySeverity: Record<ErrorSeverity, number> = {
      low: 0,
      medium: 0,
      high: 0,
      critical: 0
    };

    this.errorLog.forEach(error => {
      bySeverity[error.severity]++;
    });

    // Get recent errors (last 10)
    const recent = this.errorLog.slice(-10);

    // Get most common error messages
    const errorCounts = new Map<string, number>();
    this.errorLog.forEach(error => {
      const message = error.technicalMessage;
      errorCounts.set(message, (errorCounts.get(message) || 0) + 1);
    });

    const mostCommon = Array.from(errorCounts.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([message]) => message);

    return {
      total: this.errorLog.length,
      bySeverity,
      recent,
      mostCommon
    };
  }

  // Private methods

  private processError(error: Error | unknown, context: ErrorContext = {}): ProcessedError {
    const actualError = error instanceof Error ? error : new Error(String(error));
    
    return {
      id: this.generateErrorId(),
      originalError: actualError,
      userMessage: this.getUserMessage(actualError),
      technicalMessage: actualError.message,
      severity: this.getErrorSeverity(actualError),
      context,
      timestamp: new Date(),
      shouldRetry: this.defaultRetryCondition(actualError),
      retryCount: 0
    };
  }

  private logError(error: ProcessedError): void {
    // Add to error log
    this.errorLog.push(error);
    
    // Maintain log size
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog.shift();
    }
    
    // Console logging based on severity
    const logMessage = `[${error.severity.toUpperCase()}] ${error.technicalMessage}`;
    const contextInfo = error.context ? ` | Context: ${JSON.stringify(error.context)}` : '';
    
    switch (error.severity) {
      case 'critical':
        console.error(`🚨 ${logMessage}${contextInfo}`, error.originalError);
        break;
      case 'high':
        console.error(`❌ ${logMessage}${contextInfo}`, error.originalError);
        break;
      case 'medium':
        console.warn(`⚠️ ${logMessage}${contextInfo}`);
        break;
      case 'low':
        console.log(`ℹ️ ${logMessage}${contextInfo}`);
        break;
    }
  }

  private showErrorToast(error: ProcessedError): void {
    switch (error.severity) {
      case 'critical':
      case 'high':
        toast.error(error.userMessage, {
          duration: 6000,
          position: 'top-center'
        });
        break;
      case 'medium':
        toast.error(error.userMessage, {
          duration: 4000
        });
        break;
      case 'low':
        toast(error.userMessage, {
          duration: 3000,
          icon: 'ℹ️'
        });
        break;
    }
  }

  private async reportError(error: ProcessedError): Promise<void> {
    try {
      // In a real application, this would send to an error reporting service
      // For now, we'll just log it
      console.log('📊 Error reported:', {
        id: error.id,
        message: error.technicalMessage,
        severity: error.severity,
        context: error.context,
        timestamp: error.timestamp
      });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  private defaultRetryCondition(error: Error): boolean {
    const message = error.message.toLowerCase();
    
    // Retry on network/connection issues
    if (message.includes('network') || message.includes('timeout') || message.includes('connection')) {
      return true;
    }
    
    // Retry on temporary server errors
    if (message.includes('500') || message.includes('502') || message.includes('503')) {
      return true;
    }
    
    // Don't retry on client errors or auth issues
    if (message.includes('400') || message.includes('401') || message.includes('403') || message.includes('404')) {
      return false;
    }
    
    // Don't retry on validation errors
    if (message.includes('validation') || message.includes('invalid')) {
      return false;
    }
    
    // Default to retry for unknown errors
    return true;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
export const unifiedErrorHandler = new UnifiedErrorHandler();
export default unifiedErrorHandler;

/**
 * Redis Configuration for Festival Family
 * 
 * Configures Upstash Redis connection and initializes the Redis service
 * for high-performance caching and real-time features.
 * 
 * @module RedisConfig
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { createRedisService, type RedisConfig } from './redis-service'

// ============================================================================
// CONFIGURATION
// ============================================================================

/**
 * Redis configuration from environment variables
 */
const REDIS_CONFIG: RedisConfig = {
  url: 'https://legal-crab-25449.upstash.io',
  token: 'AWNpAAIjcDE3YzM0NDA5YzZkNDY0Mzg3YjQzM2YzNDNkODI5ZGIyY3AxMA',
  enableLogging: process.env.NODE_ENV === 'development'
}

// ============================================================================
// REDIS INITIALIZATION
// ============================================================================

/**
 * Initialize Redis service
 */
export function initializeRedis() {
  try {
    const redis = createRedisService(REDIS_CONFIG)
    
    if (REDIS_CONFIG.enableLogging) {
      console.log('🔴 Redis: Service initialized')
      console.log('🔴 Redis: URL:', REDIS_CONFIG.url)
    }
    
    // Test connection
    redis.healthCheck().then(isHealthy => {
      if (isHealthy) {
        console.log('🔴 Redis: Connection successful')
      } else {
        console.warn('🔴 Redis: Health check failed')
      }
    }).catch(error => {
      console.error('🔴 Redis: Health check error:', error)
    })
    
    return redis
  } catch (error) {
    console.error('🔴 Redis: Initialization failed:', error)
    return null
  }
}

/**
 * Redis cache keys
 */
export const REDIS_KEYS = {
  // Participant counts
  PARTICIPANT_COUNT: (activityId: string) => `activity:${activityId}:participants:count`,
  
  // User interactions
  USER_INTERACTION: (userId: string, activityId: string) => `user:${userId}:activity:${activityId}:interaction`,
  
  // Activity feed
  ACTIVITY_FEED: 'activity:feed',
  
  // Rate limiting
  RATE_LIMIT: (userId: string, action: string) => `ratelimit:${userId}:${action}`,
  
  // Activity data cache
  ACTIVITY_DATA: (activityId: string) => `activity:${activityId}:data`,
  
  // User session cache
  USER_SESSION: (userId: string) => `user:${userId}:session`,
  
  // Activity counts cache
  ACTIVITY_COUNTS: 'activities:counts',
  
  // Real-time channels
  PARTICIPANT_UPDATES: (activityId: string) => `activity:${activityId}:participants`,
  ACTIVITY_UPDATES: 'activities:updates',
  USER_UPDATES: (userId: string) => `user:${userId}:updates`
} as const

/**
 * Redis TTL values (in seconds)
 */
export const REDIS_TTL = {
  PARTICIPANT_COUNT: 3600,      // 1 hour
  USER_INTERACTION: 1800,       // 30 minutes
  ACTIVITY_DATA: 7200,          // 2 hours
  USER_SESSION: 86400,          // 24 hours
  ACTIVITY_COUNTS: 300,         // 5 minutes
  RATE_LIMIT_WINDOW: 60         // 1 minute
} as const

/**
 * Rate limiting configurations
 */
export const RATE_LIMITS = {
  JOIN_ACTIVITY: { limit: 5, windowSeconds: 60 },      // 5 joins per minute
  FAVORITE_ACTIVITY: { limit: 10, windowSeconds: 60 }, // 10 favorites per minute
  VIEW_ACTIVITY: { limit: 100, windowSeconds: 60 },    // 100 views per minute
  SEARCH_ACTIVITIES: { limit: 30, windowSeconds: 60 }  // 30 searches per minute
} as const

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get Redis service instance
 */
export function getRedis() {
  const { getRedisService } = require('./redis-service')
  return getRedisService()
}

/**
 * Check if Redis is available
 */
export async function isRedisAvailable(): Promise<boolean> {
  try {
    const redis = getRedis()
    if (!redis) return false
    
    return await redis.healthCheck()
  } catch {
    return false
  }
}

/**
 * Graceful fallback for Redis operations
 */
export async function withRedisFailover<T>(
  redisOperation: () => Promise<T>,
  fallback: () => Promise<T>
): Promise<T> {
  try {
    const isAvailable = await isRedisAvailable()
    if (!isAvailable) {
      console.warn('🔴 Redis: Not available, using fallback')
      return await fallback()
    }
    
    return await redisOperation()
  } catch (error) {
    console.warn('🔴 Redis: Operation failed, using fallback:', error)
    return await fallback()
  }
}

// ============================================================================
// PERFORMANCE MONITORING
// ============================================================================

/**
 * Redis performance metrics
 */
export interface RedisMetrics {
  cacheHits: number
  cacheMisses: number
  totalOperations: number
  averageResponseTime: number
  errorRate: number
}

let metrics: RedisMetrics = {
  cacheHits: 0,
  cacheMisses: 0,
  totalOperations: 0,
  averageResponseTime: 0,
  errorRate: 0
}

/**
 * Track Redis operation performance
 */
export function trackRedisOperation(hit: boolean, responseTime: number, error?: boolean) {
  metrics.totalOperations++
  
  if (hit) {
    metrics.cacheHits++
  } else {
    metrics.cacheMisses++
  }
  
  if (error) {
    metrics.errorRate = (metrics.errorRate * (metrics.totalOperations - 1) + 1) / metrics.totalOperations
  }
  
  metrics.averageResponseTime = (metrics.averageResponseTime * (metrics.totalOperations - 1) + responseTime) / metrics.totalOperations
}

/**
 * Get Redis performance metrics
 */
export function getRedisMetrics(): RedisMetrics {
  return { ...metrics }
}

/**
 * Reset Redis performance metrics
 */
export function resetRedisMetrics() {
  metrics = {
    cacheHits: 0,
    cacheMisses: 0,
    totalOperations: 0,
    averageResponseTime: 0,
    errorRate: 0
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export default {
  initializeRedis,
  getRedis,
  isRedisAvailable,
  withRedisFailover,
  REDIS_KEYS,
  REDIS_TTL,
  RATE_LIMITS,
  trackRedisOperation,
  getRedisMetrics,
  resetRedisMetrics
}

/**
 * AdminDashboard Component Tests
 * 
 * Tests for the admin dashboard component including navigation links and admin overview.
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { MemoryRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// Mock the admin dashboard component
const MockAdminDashboard = () => {
  const adminLinks = [
    { to: '/admin/festivals', title: 'Manage Festivals' },
    { to: '/admin/events', title: 'Manage Events' },
    { to: '/admin/activities', title: 'Manage Activities' },
    { to: '/admin/announcements', title: 'Manage Announcements' },
    { to: '/admin/users', title: 'Manage Users' },
    { to: '/admin/guides', title: 'Manage Guides' },
    { to: '/admin/tips', title: 'Manage Tips' },
    { to: '/admin/faqs', title: 'Manage FAQs' },
    { to: '/admin/external-links', title: 'Manage External Links' },
  ]

  return (
    <div data-testid="admin-dashboard">
      <h1>Admin Dashboard</h1>
      <p>Welcome to the Festival Family admin dashboard</p>
      <div className="admin-links">
        {adminLinks.map((link) => (
          <a key={link.to} href={link.to} className="admin-link">
            {link.title}
          </a>
        ))}
      </div>
    </div>
  )
}

// Mock the auth provider
const mockAuthContext = {
  session: null,
  user: null,
  profile: {
    id: 'admin-id',
    username: 'admin',
    email: '<EMAIL>',
    role: 'SUPER_ADMIN',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z',
    avatar_url: null,
    full_name: 'Admin User',
    bio: null,
    location: null,
    interests: null,
    website: null,
  },
  loading: false,
  isAdmin: true,
  signIn: jest.fn(),
  signUp: jest.fn(),
  signOut: jest.fn(),
  refreshProfile: jest.fn(),
}

jest.mock('../../providers/ConsolidatedAuthProvider', () => ({
  useAuth: () => mockAuthContext,
}))

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <MemoryRouter>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </MemoryRouter>
  )
}

describe('AdminDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('should render admin dashboard with title', () => {
    render(
      <TestWrapper>
        <MockAdminDashboard />
      </TestWrapper>
    )

    expect(screen.getByTestId('admin-dashboard')).toBeInTheDocument()
    expect(screen.getByText('Admin Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Welcome to the Festival Family admin dashboard')).toBeInTheDocument()
  })

  test('should render all admin management links', () => {
    render(
      <TestWrapper>
        <MockAdminDashboard />
      </TestWrapper>
    )

    // Check for all admin management links
    expect(screen.getByText('Manage Festivals')).toBeInTheDocument()
    expect(screen.getByText('Manage Events')).toBeInTheDocument()
    expect(screen.getByText('Manage Activities')).toBeInTheDocument()
    expect(screen.getByText('Manage Announcements')).toBeInTheDocument()
    expect(screen.getByText('Manage Users')).toBeInTheDocument()
    expect(screen.getByText('Manage Guides')).toBeInTheDocument()
    expect(screen.getByText('Manage Tips')).toBeInTheDocument()
    expect(screen.getByText('Manage FAQs')).toBeInTheDocument()
    expect(screen.getByText('Manage External Links')).toBeInTheDocument()
  })

  test('should have correct href attributes for admin links', () => {
    render(
      <TestWrapper>
        <MockAdminDashboard />
      </TestWrapper>
    )

    // Check that links have correct href attributes
    const festivalsLink = screen.getByText('Manage Festivals').closest('a')
    const usersLink = screen.getByText('Manage Users').closest('a')
    const eventsLink = screen.getByText('Manage Events').closest('a')

    expect(festivalsLink).toHaveAttribute('href', '/admin/festivals')
    expect(usersLink).toHaveAttribute('href', '/admin/users')
    expect(eventsLink).toHaveAttribute('href', '/admin/events')
  })

  test('should render admin links with proper CSS classes', () => {
    render(
      <TestWrapper>
        <MockAdminDashboard />
      </TestWrapper>
    )

    const adminLinks = screen.getAllByRole('link')
    adminLinks.forEach(link => {
      expect(link).toHaveClass('admin-link')
    })
  })

  test('should be accessible to admin users', () => {
    // This test verifies that the dashboard renders when user is admin
    expect(mockAuthContext.isAdmin).toBe(true)
    expect(mockAuthContext.profile?.role).toBe('SUPER_ADMIN')

    render(
      <TestWrapper>
        <MockAdminDashboard />
      </TestWrapper>
    )

    expect(screen.getByTestId('admin-dashboard')).toBeInTheDocument()
  })

  test('should render all expected admin sections', () => {
    render(
      <TestWrapper>
        <MockAdminDashboard />
      </TestWrapper>
    )

    // Verify all 9 admin sections are present
    const adminLinks = screen.getAllByRole('link')
    expect(adminLinks).toHaveLength(9)

    // Check for specific admin sections
    const expectedSections = [
      'Festivals', 'Events', 'Activities', 'Announcements', 
      'Users', 'Guides', 'Tips', 'FAQs', 'External Links'
    ]

    expectedSections.forEach(section => {
      expect(screen.getByText(`Manage ${section}`)).toBeInTheDocument()
    })
  })

  test('should have proper structure for admin navigation', () => {
    render(
      <TestWrapper>
        <MockAdminDashboard />
      </TestWrapper>
    )

    const adminLinksContainer = document.querySelector('.admin-links')
    expect(adminLinksContainer).toBeInTheDocument()
    
    const linksInContainer = adminLinksContainer?.querySelectorAll('a')
    expect(linksInContainer).toHaveLength(9)
  })
})

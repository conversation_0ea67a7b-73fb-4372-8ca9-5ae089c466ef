{"config": {"configFile": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\playwright.config.js", "rootDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 6}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null], ["junit", {"outputFile": "test-results/junit.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Tablet", "name": "Tablet", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": {"command": "npm run dev", "port": 5173, "reuseExistingServer": true, "timeout": 120000, "env": {"NODE_ENV": "test"}}}, "suites": [{"title": "database-verification.spec.ts", "file": "database-verification.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Festival Family Database Verification", "file": "database-verification.spec.ts", "line": 3, "column": 6, "specs": [{"title": "Database Connectivity Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 5722, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected substring: \u001b[32m\"Database connection successful\"\u001b[39m\nReceived string:    \u001b[31m\"🔄 Testing connectivity...\"\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected substring: \u001b[32m\"Database connection successful\"\u001b[39m\nReceived string:    \u001b[31m\"🔄 Testing connectivity...\"\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:32:20", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 32}, "snippet": "\u001b[0m \u001b[90m 30 |\u001b[39m     \n \u001b[90m 31 |\u001b[39m     \u001b[90m// Verify connection is successful\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 32 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoContain(\u001b[32m'Database connection successful'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 33 |\u001b[39m     \n \u001b[90m 34 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Database Connectivity Result:'\u001b[39m\u001b[33m,\u001b[39m result)\u001b[33m;\u001b[39m\n \u001b[90m 35 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 32}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected substring: \u001b[32m\"Database connection successful\"\u001b[39m\nReceived string:    \u001b[31m\"🔄 Testing connectivity...\"\u001b[39m\n\n\u001b[0m \u001b[90m 30 |\u001b[39m     \n \u001b[90m 31 |\u001b[39m     \u001b[90m// Verify connection is successful\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 32 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoContain(\u001b[32m'Database connection successful'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 33 |\u001b[39m     \n \u001b[90m 34 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Database Connectivity Result:'\u001b[39m\u001b[33m,\u001b[39m result)\u001b[33m;\u001b[39m\n \u001b[90m 35 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:32:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:11.047Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-8d422--Database-Connectivity-Test-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-8d422--Database-Connectivity-Test-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-8d422--Database-Connectivity-Test-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 32}}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-9a5aa4d3b433d1e63f55", "file": "database-verification.spec.ts", "line": 9, "column": 3}, {"title": "Table Existence Check", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 0, "status": "passed", "duration": 4117, "errors": [], "stdout": [{"text": "📋 Table Existence Results:\n"}, {"text": "✅ profiles\n                    ✅ 0 records\n"}, {"text": "🆕 New Tables Status:\n"}, {"text": "❌ content_management\n"}, {"text": "❌ user_preferences\n"}, {"text": "❌ emergency_contacts\n"}, {"text": "❌ announcements\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:25.143Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-2a54b54f644c247e7351", "file": "database-verification.spec.ts", "line": 37, "column": 3}, {"title": "Admin Functions Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 0, "status": "failed", "duration": 14525, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing admin functions...\"\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing admin functions...\"\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:116:20", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 116}, "snippet": "\u001b[0m \u001b[90m 114 |\u001b[39m     \n \u001b[90m 115 |\u001b[39m     \u001b[90m// Verify at least some admin functions work\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 118 |\u001b[39m\n \u001b[90m 119 |\u001b[39m   test(\u001b[32m'CRUD Operations Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 116}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing admin functions...\"\u001b[39m\n\n\u001b[0m \u001b[90m 114 |\u001b[39m     \n \u001b[90m 115 |\u001b[39m     \u001b[90m// Verify at least some admin functions work\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 118 |\u001b[39m\n \u001b[90m 119 |\u001b[39m   test(\u001b[32m'CRUD Operations Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:116:20\u001b[22m"}], "stdout": [{"text": "⚙️ Admin Functions Result: 🔄 Testing admin functions...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:31.232Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 116}}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-22d85e94bb65e0fa347d", "file": "database-verification.spec.ts", "line": 95, "column": 3}, {"title": "CRUD Operations Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 15, "parallelIndex": 0, "status": "failed", "duration": 3762, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing CRUD operations...\"\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing CRUD operations...\"\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:140:20", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 140}, "snippet": "\u001b[0m \u001b[90m 138 |\u001b[39m     \n \u001b[90m 139 |\u001b[39m     \u001b[90m// Verify CRUD operations are accessible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 140 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 141 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 142 |\u001b[39m\n \u001b[90m 143 |\u001b[39m   test(\u001b[32m'Profile System Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 140}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing CRUD operations...\"\u001b[39m\n\n\u001b[0m \u001b[90m 138 |\u001b[39m     \n \u001b[90m 139 |\u001b[39m     \u001b[90m// Verify CRUD operations are accessible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 140 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 141 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 142 |\u001b[39m\n \u001b[90m 143 |\u001b[39m   test(\u001b[32m'Profile System Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:140:20\u001b[22m"}], "stdout": [{"text": "🔄 CRUD Operations Result: 🔄 Testing CRUD operations...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:54.368Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-821aa-cation-CRUD-Operations-Test-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-821aa-cation-CRUD-Operations-Test-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-821aa-cation-CRUD-Operations-Test-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 140}}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-527c16a947802895a635", "file": "database-verification.spec.ts", "line": 119, "column": 3}, {"title": "Profile System Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 0, "status": "passed", "duration": 4670, "errors": [], "stdout": [{"text": "👤 Profile System Result: 🔄 Testing profile system...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:05.714Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-ee024a2128f30174b1f3", "file": "database-verification.spec.ts", "line": 143, "column": 3}, {"title": "Database Connectivity Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 27, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:11.097Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-8d422--Database-Connectivity-Test-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-9b5e802697c8d407cf24", "file": "database-verification.spec.ts", "line": 9, "column": 3}, {"title": "Table Existence Check", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 6, "parallelIndex": 1, "status": "failed", "duration": 21, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:17.998Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-1c2ed-ation-Table-Existence-Check-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-25665cea47175fe4ad26", "file": "database-verification.spec.ts", "line": 37, "column": 3}, {"title": "Admin Functions Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 7, "parallelIndex": 1, "status": "failed", "duration": 25, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:23.277Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-cc8a4396fa34527772fc", "file": "database-verification.spec.ts", "line": 95, "column": 3}, {"title": "CRUD Operations Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 11, "parallelIndex": 1, "status": "failed", "duration": 17, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:32.305Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-821aa-cation-CRUD-Operations-Test-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-ce3bf5e03f862ed033ad", "file": "database-verification.spec.ts", "line": 119, "column": 3}, {"title": "Profile System Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 13, "parallelIndex": 1, "status": "failed", "duration": 78, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:50.587Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-c6ffe-ication-Profile-System-Test-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-c2d91ad90a0071258ca4", "file": "database-verification.spec.ts", "line": 143, "column": 3}, {"title": "Database Connectivity Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 8139, "errors": [], "stdout": [{"text": "✅ Database Connectivity Result: ✅ Database connection successful!\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:11.187Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-139e37bb09b85c901524", "file": "database-verification.spec.ts", "line": 9, "column": 3}, {"title": "Table Existence Check", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 4762, "errors": [], "stdout": [{"text": "📋 Table Existence Results:\n"}, {"text": "✅ profiles\n                    ✅ 0 records\n"}, {"text": "🆕 New Tables Status:\n"}, {"text": "❌ content_management\n"}, {"text": "❌ user_preferences\n"}, {"text": "❌ emergency_contacts\n"}, {"text": "❌ announcements\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:20.112Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-6d1f4e92caa47c9f2d1b", "file": "database-verification.spec.ts", "line": 37, "column": 3}, {"title": "Admin Functions Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 6204, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing admin functions...\"\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing admin functions...\"\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:116:20", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 116}, "snippet": "\u001b[0m \u001b[90m 114 |\u001b[39m     \n \u001b[90m 115 |\u001b[39m     \u001b[90m// Verify at least some admin functions work\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 118 |\u001b[39m\n \u001b[90m 119 |\u001b[39m   test(\u001b[32m'CRUD Operations Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 116}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing admin functions...\"\u001b[39m\n\n\u001b[0m \u001b[90m 114 |\u001b[39m     \n \u001b[90m 115 |\u001b[39m     \u001b[90m// Verify at least some admin functions work\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 118 |\u001b[39m\n \u001b[90m 119 |\u001b[39m   test(\u001b[32m'CRUD Operations Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:116:20\u001b[22m"}], "stdout": [{"text": "⚙️ Admin Functions Result: 🔄 Testing admin functions...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:24.885Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-webkit\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-webkit\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 116}}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-8d0ffa883a1069de17b2", "file": "database-verification.spec.ts", "line": 95, "column": 3}, {"title": "CRUD Operations Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 14, "parallelIndex": 2, "status": "failed", "duration": 6923, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing CRUD operations...\"\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing CRUD operations...\"\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:140:20", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 140}, "snippet": "\u001b[0m \u001b[90m 138 |\u001b[39m     \n \u001b[90m 139 |\u001b[39m     \u001b[90m// Verify CRUD operations are accessible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 140 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 141 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 142 |\u001b[39m\n \u001b[90m 143 |\u001b[39m   test(\u001b[32m'Profile System Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 140}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing CRUD operations...\"\u001b[39m\n\n\u001b[0m \u001b[90m 138 |\u001b[39m     \n \u001b[90m 139 |\u001b[39m     \u001b[90m// Verify CRUD operations are accessible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 140 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 141 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 142 |\u001b[39m\n \u001b[90m 143 |\u001b[39m   test(\u001b[32m'Profile System Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:140:20\u001b[22m"}], "stdout": [{"text": "🔄 CRUD Operations Result: 🔄 Testing CRUD operations...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:50.581Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-821aa-cation-CRUD-Operations-Test-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-821aa-cation-CRUD-Operations-Test-webkit\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-821aa-cation-CRUD-Operations-Test-webkit\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 140}}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-84d3e9254a6fd6e71821", "file": "database-verification.spec.ts", "line": 119, "column": 3}, {"title": "Profile System Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 19, "parallelIndex": 2, "status": "passed", "duration": 5844, "errors": [], "stdout": [{"text": "👤 Profile System Result: 🔄 Testing profile system...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:04.707Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-2ff53c9fc5d3e602cd13", "file": "database-verification.spec.ts", "line": 143, "column": 3}, {"title": "Database Connectivity Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 5616, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected substring: \u001b[32m\"Database connection successful\"\u001b[39m\nReceived string:    \u001b[31m\"🔄 Testing connectivity...\"\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected substring: \u001b[32m\"Database connection successful\"\u001b[39m\nReceived string:    \u001b[31m\"🔄 Testing connectivity...\"\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:32:20", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 32}, "snippet": "\u001b[0m \u001b[90m 30 |\u001b[39m     \n \u001b[90m 31 |\u001b[39m     \u001b[90m// Verify connection is successful\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 32 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoContain(\u001b[32m'Database connection successful'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 33 |\u001b[39m     \n \u001b[90m 34 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Database Connectivity Result:'\u001b[39m\u001b[33m,\u001b[39m result)\u001b[33m;\u001b[39m\n \u001b[90m 35 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 32}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected substring: \u001b[32m\"Database connection successful\"\u001b[39m\nReceived string:    \u001b[31m\"🔄 Testing connectivity...\"\u001b[39m\n\n\u001b[0m \u001b[90m 30 |\u001b[39m     \n \u001b[90m 31 |\u001b[39m     \u001b[90m// Verify connection is successful\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 32 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoContain(\u001b[32m'Database connection successful'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 33 |\u001b[39m     \n \u001b[90m 34 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Database Connectivity Result:'\u001b[39m\u001b[33m,\u001b[39m result)\u001b[33m;\u001b[39m\n \u001b[90m 35 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:32:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:11.044Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-8d422--Database-Connectivity-Test-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-8d422--Database-Connectivity-Test-Mobile-Chrome\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-8d422--Database-Connectivity-Test-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 32}}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-00777f21847024a6c6dd", "file": "database-verification.spec.ts", "line": 9, "column": 3}, {"title": "Table Existence Check", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 10, "parallelIndex": 3, "status": "passed", "duration": 16992, "errors": [], "stdout": [{"text": "📋 Table Existence Results:\n"}, {"text": "✅ profiles\n                    ✅ 0 records\n"}, {"text": "🆕 New Tables Status:\n"}, {"text": "❌ content_management\n"}, {"text": "❌ user_preferences\n"}, {"text": "❌ emergency_contacts\n"}, {"text": "❌ announcements\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:25.904Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-b136eaf0e5c6a0511ec1", "file": "database-verification.spec.ts", "line": 37, "column": 3}, {"title": "Admin Functions Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 10, "parallelIndex": 3, "status": "failed", "duration": 4053, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing admin functions...\"\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing admin functions...\"\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:116:20", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 116}, "snippet": "\u001b[0m \u001b[90m 114 |\u001b[39m     \n \u001b[90m 115 |\u001b[39m     \u001b[90m// Verify at least some admin functions work\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 118 |\u001b[39m\n \u001b[90m 119 |\u001b[39m   test(\u001b[32m'CRUD Operations Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 116}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing admin functions...\"\u001b[39m\n\n\u001b[0m \u001b[90m 114 |\u001b[39m     \n \u001b[90m 115 |\u001b[39m     \u001b[90m// Verify at least some admin functions work\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 118 |\u001b[39m\n \u001b[90m 119 |\u001b[39m   test(\u001b[32m'CRUD Operations Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:116:20\u001b[22m"}], "stdout": [{"text": "⚙️ Admin Functions Result: 🔄 Testing admin functions...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:45.858Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Chrome\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 116}}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-4d4f8a15e4fa05cfa38b", "file": "database-verification.spec.ts", "line": 95, "column": 3}, {"title": "CRUD Operations Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 18, "parallelIndex": 3, "status": "failed", "duration": 3815, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing CRUD operations...\"\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing CRUD operations...\"\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:140:20", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 140}, "snippet": "\u001b[0m \u001b[90m 138 |\u001b[39m     \n \u001b[90m 139 |\u001b[39m     \u001b[90m// Verify CRUD operations are accessible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 140 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 141 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 142 |\u001b[39m\n \u001b[90m 143 |\u001b[39m   test(\u001b[32m'Profile System Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 140}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing CRUD operations...\"\u001b[39m\n\n\u001b[0m \u001b[90m 138 |\u001b[39m     \n \u001b[90m 139 |\u001b[39m     \u001b[90m// Verify CRUD operations are accessible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 140 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 141 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 142 |\u001b[39m\n \u001b[90m 143 |\u001b[39m   test(\u001b[32m'Profile System Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:140:20\u001b[22m"}], "stdout": [{"text": "🔄 CRUD Operations Result: 🔄 Testing CRUD operations...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:01.170Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Mobile-Chrome\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 140}}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-48ffb42235aa3aa9de67", "file": "database-verification.spec.ts", "line": 119, "column": 3}, {"title": "Profile System Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 24, "parallelIndex": 3, "status": "passed", "duration": 7294, "errors": [], "stdout": [{"text": "👤 Profile System Result: 🔄 Testing profile system...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:16.778Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-57d4ae1b539ec500a08e", "file": "database-verification.spec.ts", "line": 143, "column": 3}, {"title": "Database Connectivity Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 7700, "errors": [], "stdout": [{"text": "✅ Database Connectivity Result: ✅ Database connection successful!\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:11.302Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-2f47ea7df8e0df52cc4c", "file": "database-verification.spec.ts", "line": 9, "column": 3}, {"title": "Table Existence Check", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 4368, "errors": [], "stdout": [{"text": "📋 Table Existence Results:\n"}, {"text": "✅ profiles\n                    ✅ 0 records\n"}, {"text": "🆕 New Tables Status:\n"}, {"text": "❌ content_management\n"}, {"text": "❌ user_preferences\n"}, {"text": "❌ emergency_contacts\n"}, {"text": "❌ announcements\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:19.672Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-aa1f9b9808efb012d739", "file": "database-verification.spec.ts", "line": 37, "column": 3}, {"title": "Admin Functions Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "failed", "duration": 5620, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing admin functions...\"\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing admin functions...\"\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:116:20", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 116}, "snippet": "\u001b[0m \u001b[90m 114 |\u001b[39m     \n \u001b[90m 115 |\u001b[39m     \u001b[90m// Verify at least some admin functions work\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 118 |\u001b[39m\n \u001b[90m 119 |\u001b[39m   test(\u001b[32m'CRUD Operations Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 116}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing admin functions...\"\u001b[39m\n\n\u001b[0m \u001b[90m 114 |\u001b[39m     \n \u001b[90m 115 |\u001b[39m     \u001b[90m// Verify at least some admin functions work\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 118 |\u001b[39m\n \u001b[90m 119 |\u001b[39m   test(\u001b[32m'CRUD Operations Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:116:20\u001b[22m"}], "stdout": [{"text": "⚙️ Admin Functions Result: 🔄 Testing admin functions...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:24.051Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Safari\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 116}}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-dead5488312e88936303", "file": "database-verification.spec.ts", "line": 95, "column": 3}, {"title": "CRUD Operations Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 12, "parallelIndex": 4, "status": "passed", "duration": 6570, "errors": [], "stdout": [{"text": "🔄 CRUD Operations Result: ✅ Announcements READ: Working (1 records)\n❌ Announcements CREATE: Could not find the 'is_active' column of 'announcements' in the schema cache\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:50.259Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-616380b971b99833ba3d", "file": "database-verification.spec.ts", "line": 119, "column": 3}, {"title": "Profile System Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 12, "parallelIndex": 4, "status": "passed", "duration": 3793, "errors": [], "stdout": [{"text": "👤 Profile System Result: 🔄 Testing profile system...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:57.736Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-c68f801d3b5743d2f3ff", "file": "database-verification.spec.ts", "line": 143, "column": 3}, {"title": "Database Connectivity Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "failed", "duration": 5329, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected substring: \u001b[32m\"Database connection successful\"\u001b[39m\nReceived string:    \u001b[31m\"🔄 Testing connectivity...\"\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected substring: \u001b[32m\"Database connection successful\"\u001b[39m\nReceived string:    \u001b[31m\"🔄 Testing connectivity...\"\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:32:20", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 32}, "snippet": "\u001b[0m \u001b[90m 30 |\u001b[39m     \n \u001b[90m 31 |\u001b[39m     \u001b[90m// Verify connection is successful\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 32 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoContain(\u001b[32m'Database connection successful'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 33 |\u001b[39m     \n \u001b[90m 34 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Database Connectivity Result:'\u001b[39m\u001b[33m,\u001b[39m result)\u001b[33m;\u001b[39m\n \u001b[90m 35 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 32}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected substring: \u001b[32m\"Database connection successful\"\u001b[39m\nReceived string:    \u001b[31m\"🔄 Testing connectivity...\"\u001b[39m\n\n\u001b[0m \u001b[90m 30 |\u001b[39m     \n \u001b[90m 31 |\u001b[39m     \u001b[90m// Verify connection is successful\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 32 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoContain(\u001b[32m'Database connection successful'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 33 |\u001b[39m     \n \u001b[90m 34 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Database Connectivity Result:'\u001b[39m\u001b[33m,\u001b[39m result)\u001b[33m;\u001b[39m\n \u001b[90m 35 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:32:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:11.345Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-8d422--Database-Connectivity-Test-Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-8d422--Database-Connectivity-Test-Tablet\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-8d422--Database-Connectivity-Test-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 32}}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-33f0e02ef22a8cb0b4c9", "file": "database-verification.spec.ts", "line": 9, "column": 3}, {"title": "Table Existence Check", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 8, "parallelIndex": 5, "status": "passed", "duration": 4117, "errors": [], "stdout": [{"text": "📋 Table Existence Results:\n"}, {"text": "✅ profiles\n                    ✅ 0 records\n"}, {"text": "🆕 New Tables Status:\n"}, {"text": "❌ content_management\n"}, {"text": "❌ user_preferences\n"}, {"text": "❌ emergency_contacts\n"}, {"text": "❌ announcements\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:23.934Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-44239795602ae515b004", "file": "database-verification.spec.ts", "line": 37, "column": 3}, {"title": "Admin Functions Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 8, "parallelIndex": 5, "status": "failed", "duration": 15566, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing admin functions...\"\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing admin functions...\"\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:116:20", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 116}, "snippet": "\u001b[0m \u001b[90m 114 |\u001b[39m     \n \u001b[90m 115 |\u001b[39m     \u001b[90m// Verify at least some admin functions work\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 118 |\u001b[39m\n \u001b[90m 119 |\u001b[39m   test(\u001b[32m'CRUD Operations Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 116}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing admin functions...\"\u001b[39m\n\n\u001b[0m \u001b[90m 114 |\u001b[39m     \n \u001b[90m 115 |\u001b[39m     \u001b[90m// Verify at least some admin functions work\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 118 |\u001b[39m\n \u001b[90m 119 |\u001b[39m   test(\u001b[32m'CRUD Operations Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:116:20\u001b[22m"}], "stdout": [{"text": "⚙️ Admin Functions Result: 🔄 Testing admin functions...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:30.156Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-Tablet\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-3a444-cation-Admin-Functions-Test-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 116}}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-af0096878ad8c2cb3268", "file": "database-verification.spec.ts", "line": 95, "column": 3}, {"title": "CRUD Operations Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 16, "parallelIndex": 5, "status": "failed", "duration": 3785, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing CRUD operations...\"\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing CRUD operations...\"\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:140:20", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 140}, "snippet": "\u001b[0m \u001b[90m 138 |\u001b[39m     \n \u001b[90m 139 |\u001b[39m     \u001b[90m// Verify CRUD operations are accessible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 140 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 141 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 142 |\u001b[39m\n \u001b[90m 143 |\u001b[39m   test(\u001b[32m'Profile System Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 140}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoMatch\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m\nReceived string:  \u001b[31m\"🔄 Testing CRUD operations...\"\u001b[39m\n\n\u001b[0m \u001b[90m 138 |\u001b[39m     \n \u001b[90m 139 |\u001b[39m     \u001b[90m// Verify CRUD operations are accessible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 140 |\u001b[39m     expect(result)\u001b[33m.\u001b[39mtoMatch(\u001b[35m/(READ|CREATE|UPDATE|DELETE)/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 141 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 142 |\u001b[39m\n \u001b[90m 143 |\u001b[39m   test(\u001b[32m'Profile System Test'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts:140:20\u001b[22m"}], "stdout": [{"text": "🔄 CRUD Operations Result: 🔄 Testing CRUD operations...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:54.621Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Tablet\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\database-verification.spec.ts", "column": 20, "line": 140}}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-07acec0673f8caf6b2df", "file": "database-verification.spec.ts", "line": 119, "column": 3}, {"title": "Profile System Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 22, "parallelIndex": 5, "status": "passed", "duration": 3592, "errors": [], "stdout": [{"text": "👤 Profile System Result: 🔄 Testing profile system...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:05.882Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-026024a6937f4cdae23c", "file": "database-verification.spec.ts", "line": 143, "column": 3}]}, {"title": "Festival Family Admin Dashboard Tests", "file": "database-verification.spec.ts", "line": 168, "column": 6, "specs": [{"title": "Admin Dashboard Authentication", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 0, "status": "passed", "duration": 2751, "errors": [], "stdout": [{"text": "🔐 Admin Authentication Result: ⚠️ No active session. Please sign in to test admin functionality.\n\n🔗 Sign in at: http://localhost:5173/auth\n"}, {"text": "⚠️ User needs to sign in for admin testing\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:12.236Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-037f61ab5c302d768f6d", "file": "database-verification.spec.ts", "line": 173, "column": 3}, {"title": "Admin Functions Status", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 0, "status": "passed", "duration": 5207, "errors": [], "stdout": [{"text": "⚙️ Admin Functions Status:\n"}, {"text": "✅ is_admin\n                        ✅ null\n"}, {"text": "❌ is_super_admin\n                        ❌ Error\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:14.997Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-a8958697fb40d94b3d07", "file": "database-verification.spec.ts", "line": 201, "column": 3}, {"title": "Content Management Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 0, "status": "passed", "duration": 6109, "errors": [], "stdout": [{"text": "📝 Content Management Result: 🔄 Testing content management...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:20.215Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-fccf70450c2f406fc7ec", "file": "database-verification.spec.ts", "line": 237, "column": 3}, {"title": "Emergency Management Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 0, "status": "passed", "duration": 12092, "errors": [], "stdout": [{"text": "🚨 Emergency Management Result: 🔄 Testing emergency management...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:26.334Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-d94eb03549961abc0a2b", "file": "database-verification.spec.ts", "line": 258, "column": 3}, {"title": "Application Accessibility Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 0, "status": "passed", "duration": 5449, "errors": [], "stdout": [{"text": "🎪 Main Application Title: Festival Family\n"}, {"text": "✅ Admin dashboard accessible\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:38.445Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-fced1c1e19e898e842e3", "file": "database-verification.spec.ts", "line": 279, "column": 3}, {"title": "Admin Dashboard Authentication", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 17, "parallelIndex": 1, "status": "failed", "duration": 28, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:27:59.391Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-7c452-in-Dashboard-Authentication-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-b0f1f03101c43133b1c2", "file": "database-verification.spec.ts", "line": 173, "column": 3}, {"title": "Admin Functions Status", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 20, "parallelIndex": 1, "status": "failed", "duration": 68, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:05.564Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-b636f-ests-Admin-Functions-Status-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-3aceb00d1e4fe1ba7708", "file": "database-verification.spec.ts", "line": 201, "column": 3}, {"title": "Content Management Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 23, "parallelIndex": 1, "status": "failed", "duration": 48, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:15.484Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-8075f-sts-Content-Management-Test-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-b8dab3d9e32949bea483", "file": "database-verification.spec.ts", "line": 237, "column": 3}, {"title": "Emergency Management Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 25, "parallelIndex": 1, "status": "failed", "duration": 112, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:41.359Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-6d4c2-s-Emergency-Management-Test-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-d813dcec7cbcd9ee5c8a", "file": "database-verification.spec.ts", "line": 258, "column": 3}, {"title": "Application Accessibility Test", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 26, "parallelIndex": 1, "status": "failed", "duration": 21, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:50.869Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\database-verification-Fest-eb12f-lication-Accessibility-Test-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "e5c644acedc8faec053e-fc22b41722bcd3545948", "file": "database-verification.spec.ts", "line": 279, "column": 3}, {"title": "Admin Dashboard Authentication", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 19, "parallelIndex": 2, "status": "passed", "duration": 7244, "errors": [], "stdout": [{"text": "🔐 Admin Authentication Result: ⚠️ No active session. Please sign in to test admin functionality.\n\n🔗 Sign in at: http://localhost:5173/auth\n"}, {"text": "⚠️ User needs to sign in for admin testing\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:10.981Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-b5b7cfd5b1b21721a6aa", "file": "database-verification.spec.ts", "line": 173, "column": 3}, {"title": "Admin Functions Status", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 19, "parallelIndex": 2, "status": "passed", "duration": 9423, "errors": [], "stdout": [{"text": "⚙️ Admin Functions Status:\n"}, {"text": "✅ is_admin\n                        ✅ null\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:18.237Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-5efef38b20aae87e4874", "file": "database-verification.spec.ts", "line": 201, "column": 3}, {"title": "Content Management Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 19, "parallelIndex": 2, "status": "passed", "duration": 6307, "errors": [], "stdout": [{"text": "📝 Content Management Result: 🔄 Testing content management...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:27.672Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-b9801df0dc05a6f6a73e", "file": "database-verification.spec.ts", "line": 237, "column": 3}, {"title": "Emergency Management Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 19, "parallelIndex": 2, "status": "passed", "duration": 6817, "errors": [], "stdout": [{"text": "🚨 Emergency Management Result: 🔄 Testing emergency management...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:34.022Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-962370013e7df6ad6706", "file": "database-verification.spec.ts", "line": 258, "column": 3}, {"title": "Application Accessibility Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 19, "parallelIndex": 2, "status": "passed", "duration": 7457, "errors": [], "stdout": [{"text": "🎪 Main Application Title: Festival Family\n"}, {"text": "✅ Admin dashboard accessible\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:40.851Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-ae30dcdcefcb3cd58116", "file": "database-verification.spec.ts", "line": 279, "column": 3}, {"title": "Admin Dashboard Authentication", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 24, "parallelIndex": 3, "status": "passed", "duration": 6268, "errors": [], "stdout": [{"text": "🔐 Admin Authentication Result: ⚠️ No active session. Please sign in to test admin functionality.\n\n🔗 Sign in at: http://localhost:5173/auth\n"}, {"text": "⚠️ User needs to sign in for admin testing\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:28.485Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-71de3806bf8d8235f964", "file": "database-verification.spec.ts", "line": 173, "column": 3}, {"title": "Admin Functions Status", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 24, "parallelIndex": 3, "status": "passed", "duration": 7710, "errors": [], "stdout": [{"text": "⚙️ Admin Functions Status:\n"}, {"text": "✅ is_admin\n                        ✅ null\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:34.769Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-0b2ef0c721f402a40d55", "file": "database-verification.spec.ts", "line": 201, "column": 3}, {"title": "Content Management Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 24, "parallelIndex": 3, "status": "passed", "duration": 3532, "errors": [], "stdout": [{"text": "📝 Content Management Result: 🔄 Testing content management...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:42.489Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-4703abe88ba29425bebd", "file": "database-verification.spec.ts", "line": 237, "column": 3}, {"title": "Emergency Management Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 24, "parallelIndex": 3, "status": "passed", "duration": 3287, "errors": [], "stdout": [{"text": "🚨 Emergency Management Result: 🔄 Testing emergency management...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:46.032Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-80923c9c32a6f5bbcd15", "file": "database-verification.spec.ts", "line": 258, "column": 3}, {"title": "Application Accessibility Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 24, "parallelIndex": 3, "status": "passed", "duration": 3053, "errors": [], "stdout": [{"text": "🎪 Main Application Title: Festival Family\n"}, {"text": "✅ Admin dashboard accessible\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:49.332Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-e851f83c7af7aea0a30d", "file": "database-verification.spec.ts", "line": 279, "column": 3}, {"title": "Admin Dashboard Authentication", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 12, "parallelIndex": 4, "status": "passed", "duration": 4748, "errors": [], "stdout": [{"text": "🔐 Admin Authentication Result: ⚠️ No active session. Please sign in to test admin functionality.\n\n🔗 Sign in at: http://localhost:5173/auth\n"}, {"text": "⚠️ User needs to sign in for admin testing\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:01.543Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-22a18a7a72dd63ca0495", "file": "database-verification.spec.ts", "line": 173, "column": 3}, {"title": "Admin Functions Status", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 12, "parallelIndex": 4, "status": "passed", "duration": 5871, "errors": [], "stdout": [{"text": "⚙️ Admin Functions Status:\n"}, {"text": "✅ is_admin\n                        ✅ null\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:06.302Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-e5d1d96d3a33397b7530", "file": "database-verification.spec.ts", "line": 201, "column": 3}, {"title": "Content Management Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 12, "parallelIndex": 4, "status": "passed", "duration": 5920, "errors": [], "stdout": [{"text": "📝 Content Management Result: 🔄 Testing content management...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:12.183Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-32ec13110a7b06f468f3", "file": "database-verification.spec.ts", "line": 237, "column": 3}, {"title": "Emergency Management Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 12, "parallelIndex": 4, "status": "passed", "duration": 6647, "errors": [], "stdout": [{"text": "🚨 Emergency Management Result: 🔄 Testing emergency management...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:18.124Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-1e8356f8ab801f827f63", "file": "database-verification.spec.ts", "line": 258, "column": 3}, {"title": "Application Accessibility Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 12, "parallelIndex": 4, "status": "passed", "duration": 8261, "errors": [], "stdout": [{"text": "🎪 Main Application Title: Festival Family\n"}, {"text": "✅ Admin dashboard accessible\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:24.787Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-ba7fd691b3e9dba90855", "file": "database-verification.spec.ts", "line": 279, "column": 3}, {"title": "Admin Dashboard Authentication", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 22, "parallelIndex": 5, "status": "passed", "duration": 2680, "errors": [], "stdout": [{"text": "🔐 Admin Authentication Result: ⚠️ No active session. Please sign in to test admin functionality.\n\n🔗 Sign in at: http://localhost:5173/auth\n"}, {"text": "⚠️ User needs to sign in for admin testing\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:11.138Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-8b9268e309c8838c0961", "file": "database-verification.spec.ts", "line": 173, "column": 3}, {"title": "Admin Functions Status", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 22, "parallelIndex": 5, "status": "passed", "duration": 5716, "errors": [], "stdout": [{"text": "⚙️ Admin Functions Status:\n"}, {"text": "✅ is_admin\n                        ✅ null\n"}, {"text": "❌ is_super_admin\n                        ❌ Error\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:13.827Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-eb4cb4c23f4aafdaf787", "file": "database-verification.spec.ts", "line": 201, "column": 3}, {"title": "Content Management Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 22, "parallelIndex": 5, "status": "passed", "duration": 5927, "errors": [], "stdout": [{"text": "📝 Content Management Result: 🔄 Testing content management...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:19.561Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-419a644cb7aa047aa424", "file": "database-verification.spec.ts", "line": 237, "column": 3}, {"title": "Emergency Management Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 22, "parallelIndex": 5, "status": "passed", "duration": 6832, "errors": [], "stdout": [{"text": "🚨 Emergency Management Result: 🔄 Testing emergency management...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:25.496Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-d3afe6ccce1658f189b6", "file": "database-verification.spec.ts", "line": 258, "column": 3}, {"title": "Application Accessibility Test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 22, "parallelIndex": 5, "status": "passed", "duration": 10070, "errors": [], "stdout": [{"text": "🎪 Main Application Title: Festival Family\n"}, {"text": "✅ Admin dashboard accessible\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-04T15:28:32.338Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "e5c644acedc8faec053e-77a392a6fe06c65a2d46", "file": "database-verification.spec.ts", "line": 279, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-06-04T15:27:06.173Z", "duration": 106745.293, "expected": 38, "skipped": 0, "unexpected": 22, "flaky": 0}}
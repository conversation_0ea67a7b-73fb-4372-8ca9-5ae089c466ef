/**
 * Production-Ready Diagnostic System
 * Comprehensive health checks and monitoring for Festival Family
 */

import { supabase } from '@/lib/supabase';

export interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  error?: string;
  details?: any;
  timestamp: string;
}

export interface SystemHealth {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  checks: HealthCheckResult[];
  environment: {
    url: string;
    userAgent: string;
    online: boolean;
    connection: string;
  };
}

/**
 * Test Supabase database connectivity
 */
export async function checkSupabaseHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  const timestamp = new Date().toISOString();
  
  try {
    // Test 1: Basic connection
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1)
      .single();
    
    const responseTime = Date.now() - startTime;
    
    if (error) {
      return {
        service: 'supabase-database',
        status: 'unhealthy',
        responseTime,
        error: error.message,
        details: { code: error.code, hint: error.hint },
        timestamp
      };
    }
    
    return {
      service: 'supabase-database',
      status: responseTime < 2000 ? 'healthy' : 'degraded',
      responseTime,
      timestamp
    };
  } catch (error: any) {
    return {
      service: 'supabase-database',
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      error: error.message || 'Unknown error',
      timestamp
    };
  }
}

/**
 * Test Supabase authentication
 */
export async function checkSupabaseAuth(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  const timestamp = new Date().toISOString();
  
  try {
    const { data, error } = await supabase.auth.getSession();
    const responseTime = Date.now() - startTime;
    
    if (error) {
      return {
        service: 'supabase-auth',
        status: 'unhealthy',
        responseTime,
        error: error.message,
        timestamp
      };
    }
    
    return {
      service: 'supabase-auth',
      status: responseTime < 1000 ? 'healthy' : 'degraded',
      responseTime,
      details: { hasSession: !!data.session },
      timestamp
    };
  } catch (error: any) {
    return {
      service: 'supabase-auth',
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      error: error.message || 'Unknown error',
      timestamp
    };
  }
}

/**
 * Test network connectivity
 */
export async function checkNetworkHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  const timestamp = new Date().toISOString();
  
  try {
    // Test external connectivity
    const response = await fetch('https://api.github.com/zen', {
      method: 'GET',
      cache: 'no-cache'
    });
    
    const responseTime = Date.now() - startTime;
    
    if (!response.ok) {
      return {
        service: 'network',
        status: 'degraded',
        responseTime,
        error: `HTTP ${response.status}`,
        timestamp
      };
    }
    
    return {
      service: 'network',
      status: responseTime < 1000 ? 'healthy' : 'degraded',
      responseTime,
      timestamp
    };
  } catch (error: any) {
    return {
      service: 'network',
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      error: error.message || 'Network unreachable',
      timestamp
    };
  }
}

/**
 * Comprehensive system health check
 */
export async function runSystemHealthCheck(): Promise<SystemHealth> {
  console.log('🔍 Running comprehensive system health check...');
  
  const checks = await Promise.all([
    checkNetworkHealth(),
    checkSupabaseAuth(),
    checkSupabaseHealth()
  ]);
  
  // Determine overall health
  const unhealthyCount = checks.filter(c => c.status === 'unhealthy').length;
  const degradedCount = checks.filter(c => c.status === 'degraded').length;
  
  let overall: 'healthy' | 'degraded' | 'unhealthy';
  if (unhealthyCount > 0) {
    overall = 'unhealthy';
  } else if (degradedCount > 0) {
    overall = 'degraded';
  } else {
    overall = 'healthy';
  }
  
  const environment = {
    url: window.location.href,
    userAgent: navigator.userAgent,
    online: navigator.onLine,
    connection: (navigator as any).connection?.effectiveType || 'unknown'
  };
  
  const result: SystemHealth = {
    overall,
    checks,
    environment
  };
  
  console.log('📊 System Health Report:', result);
  return result;
}

/**
 * Environment validation
 */
export function validateEnvironment(): {
  valid: boolean;
  issues: string[];
  config: any;
} {
  const issues: string[] = [];
  
  // Check required environment variables
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl) {
    issues.push('VITE_SUPABASE_URL is not defined');
  } else if (!supabaseUrl.startsWith('https://')) {
    issues.push('VITE_SUPABASE_URL should start with https://');
  }
  
  if (!supabaseKey) {
    issues.push('VITE_SUPABASE_ANON_KEY is not defined');
  } else if (supabaseKey.length < 100) {
    issues.push('VITE_SUPABASE_ANON_KEY appears to be invalid (too short)');
  }
  
  const config = {
    supabaseUrl: supabaseUrl ? `${supabaseUrl.substring(0, 20)}...` : 'undefined',
    supabaseKey: supabaseKey ? `${supabaseKey.substring(0, 20)}...` : 'undefined',
    environment: import.meta.env.MODE,
    dev: import.meta.env.DEV,
    prod: import.meta.env.PROD
  };
  
  return {
    valid: issues.length === 0,
    issues,
    config
  };
}

/**
 * Production monitoring helper
 */
export class HealthMonitor {
  private interval: NodeJS.Timeout | null = null;
  private callbacks: ((health: SystemHealth) => void)[] = [];
  
  start(intervalMs: number = 30000) {
    if (this.interval) return;
    
    console.log('🔄 Starting health monitor...');
    this.interval = setInterval(async () => {
      const health = await runSystemHealthCheck();
      this.callbacks.forEach(callback => callback(health));
    }, intervalMs);
  }
  
  stop() {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
      console.log('⏹️ Health monitor stopped');
    }
  }
  
  onHealthChange(callback: (health: SystemHealth) => void) {
    this.callbacks.push(callback);
  }
}

export const healthMonitor = new HealthMonitor();

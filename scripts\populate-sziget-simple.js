/**
 * Simplified Sziget Festival Data Population
 * 
 * This script populates essential Sziget Festival data with correct schema mapping.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🎪 Populating Sziget Festival Data (Simplified)');
console.log('===============================================');

// Simplified Sziget Festival 2025 Data
const szigetData = {
  festival: {
    name: 'Sziget Festival 2025',
    description: 'One of Europe\'s largest music and cultural festivals, taking place on Óbuda Island in Budapest, Hungary. Join the Festival Family for an unforgettable week of music, art, and community.',
    start_date: '2025-08-04',
    end_date: '2025-08-12',
    location: 'Óbuda Island, Budapest, Hungary',
    status: 'PUBLISHED'
  },
  
  events: [
    {
      name: 'Festival Family Pre-Meet',
      description: 'Get to know your Festival Family before the main event! Connect with fellow festival-goers and plan your Sziget adventure together.',
      start_time: '2025-03-21T12:00:00Z',
      end_time: '2025-03-24T18:00:00Z',
      location: 'Location TBD',
      status: 'planned',
      is_public: true
    },
    {
      name: 'Early Move-In Setup',
      description: 'Help set up the Festival Family base camp and get early access to the festival grounds.',
      start_time: '2025-08-04T10:00:00Z',
      end_time: '2025-08-04T18:00:00Z',
      location: 'Sziget Festival Grounds',
      status: 'planned',
      is_public: true
    },
    {
      name: 'Festival Family Reunion',
      description: 'Annual reunion for all Festival Family members to reconnect and plan future adventures.',
      start_time: '2025-10-10T14:00:00Z',
      end_time: '2025-10-13T18:00:00Z',
      location: 'Nijmegen, Netherlands',
      status: 'planned',
      is_public: true
    }
  ],
  
  activities: [
    {
      title: 'Fam Hangout Opening',
      description: 'Official opening of the Festival Family hangout space at base camp. Meet your festival family and get oriented!',
      type: 'meetup',
      start_date: '2025-08-04T19:30:00Z',
      end_date: '2025-08-04T20:30:00Z',
      location: 'Sziget Festival Base Camp',
      capacity: 100,
      status: 'draft',
      tags: ['family', 'social', 'opening'],
      metadata: {
        is_family_activity: true,
        requirements: 'Festival Family membership'
      }
    },
    {
      title: 'Festival Family Dinner',
      description: 'Special dinner for Festival Family members at a location near Sziget Festival. Great opportunity to bond with your festival tribe!',
      type: 'meetup',
      start_date: '2025-08-05T18:00:00Z',
      end_date: '2025-08-05T23:00:00Z',
      location: 'Restaurant near Sziget Festival',
      capacity: 50,
      status: 'draft',
      tags: ['family', 'dining', 'social'],
      metadata: {
        is_family_activity: true,
        requirements: 'Pre-registration required by July 1st, 2025'
      }
    },
    {
      title: 'Sziget Festival Scavenger Hunt',
      description: 'Explore the festival grounds with your Festival Family through an exciting scavenger hunt. Prizes for winners!',
      type: 'other',
      start_date: '2025-08-06T09:00:00Z',
      end_date: '2025-08-06T16:00:00Z',
      location: 'Sziget Festival Grounds',
      capacity: 100,
      status: 'draft',
      tags: ['family', 'game', 'exploration'],
      metadata: {
        is_family_activity: true,
        requirements: 'Sign up by July 20th, 2025'
      }
    },
    {
      title: 'Fam Beerpong Tournament',
      description: 'Epic beer pong tournament at the Festival Family base camp. Bring your A-game and your festival spirit!',
      type: 'other',
      start_date: '2025-08-07T13:00:00Z',
      end_date: '2025-08-07T18:00:00Z',
      location: 'Basic Base Camp',
      capacity: 32,
      status: 'draft',
      tags: ['family', 'tournament', 'competition'],
      metadata: {
        is_family_activity: true,
        requirements: 'Partner required, 18+ only, registration by July 1st, 2025'
      }
    },
    {
      title: 'Fam Karaoke Night',
      description: 'Sing your heart out with your Festival Family! Karaoke night at the base camp hangout.',
      type: 'performance',
      start_date: '2025-08-08T20:00:00Z',
      end_date: '2025-08-08T23:00:00Z',
      location: 'Fam Hangout at Basic Base Camp',
      capacity: 50,
      status: 'draft',
      tags: ['family', 'karaoke', 'entertainment'],
      metadata: {
        is_family_activity: true,
        requirements: 'Sign up by July 20th, 2025'
      }
    },
    {
      title: 'Fam Goodbye Meet',
      description: 'Final gathering to say goodbye and exchange contact information. Last chance to hand over camping gear!',
      type: 'meetup',
      start_date: '2025-08-11T11:00:00Z',
      end_date: '2025-08-11T11:45:00Z',
      location: 'Sziget Festival Base Camp',
      capacity: null,
      status: 'draft',
      tags: ['family', 'farewell', 'goodbye'],
      metadata: {
        is_family_activity: true,
        requirements: 'Festival Family membership'
      }
    },
    {
      title: 'Post-Festival Dinner',
      description: 'Dinner in Budapest for those staying an extra day. Continue the festival spirit in the city!',
      type: 'meetup',
      start_date: '2025-08-12T17:00:00Z',
      end_date: '2025-08-12T20:00:00Z',
      location: 'Budapest, Hungary',
      capacity: 30,
      status: 'draft',
      tags: ['family', 'dining', 'budapest'],
      metadata: {
        is_family_activity: true,
        requirements: 'RSVP required'
      }
    },
    {
      title: 'Closing Meetup & Toast',
      description: 'Final celebration and toast to another amazing Sziget experience with your Festival Family.',
      type: 'meetup',
      start_date: '2025-08-12T20:00:00Z',
      end_date: '2025-08-12T23:00:00Z',
      location: 'Budapest, Hungary',
      capacity: 50,
      status: 'draft',
      tags: ['family', 'celebration', 'toast'],
      metadata: {
        is_family_activity: true,
        requirements: 'RSVP required'
      }
    },
    {
      title: 'Fam Recovery Bath Visit',
      description: 'Relax and recover from the festival at one of Budapest\'s famous thermal baths.',
      type: 'other',
      start_date: '2025-08-13T14:00:00Z',
      end_date: '2025-08-13T18:00:00Z',
      location: 'Budapest Thermal Baths',
      capacity: 25,
      status: 'draft',
      tags: ['family', 'wellness', 'recovery'],
      metadata: {
        is_family_activity: true,
        requirements: 'RSVP required, additional cost'
      }
    }
  ]
};

// Population functions
async function createFestival() {
  console.log('🎪 Creating Sziget Festival...');
  
  const { data, error } = await supabase
    .from('festivals')
    .insert([szigetData.festival])
    .select()
    .single();
  
  if (error) {
    console.error('❌ Error creating festival:', error);
    throw error;
  }
  
  console.log('✅ Festival created:', data.name);
  return data;
}

async function createEvents(festivalId) {
  console.log('📅 Creating events...');
  
  const eventsWithFestivalId = szigetData.events.map(event => ({
    ...event,
    festival_id: festivalId
  }));
  
  const { data, error } = await supabase
    .from('events')
    .insert(eventsWithFestivalId)
    .select();
  
  if (error) {
    console.error('❌ Error creating events:', error);
    throw error;
  }
  
  console.log(`✅ Created ${data.length} events`);
  return data;
}

async function createActivities(festivalId) {
  console.log('🎯 Creating activities...');
  
  const activitiesWithFestivalId = szigetData.activities.map(activity => ({
    ...activity,
    festival_id: festivalId
  }));
  
  const { data, error } = await supabase
    .from('activities')
    .insert(activitiesWithFestivalId)
    .select();
  
  if (error) {
    console.error('❌ Error creating activities:', error);
    throw error;
  }
  
  console.log(`✅ Created ${data.length} activities`);
  return data;
}

// Main execution
async function main() {
  try {
    console.log(`🕐 Started at: ${new Date().toISOString()}\n`);
    
    // Check if Sziget Festival already exists
    const { data: existingFestival } = await supabase
      .from('festivals')
      .select('*')
      .eq('name', 'Sziget Festival 2025')
      .single();
    
    if (existingFestival) {
      console.log('⚠️ Sziget Festival 2025 already exists. Using existing festival.');
      console.log('🎪 Festival ID:', existingFestival.id);
      
      // Still try to create events and activities
      const events = await createEvents(existingFestival.id);
      const activities = await createActivities(existingFestival.id);
      
      console.log('\n🎉 SZIGET FESTIVAL DATA POPULATION COMPLETE');
      console.log('==========================================');
      console.log(`🎪 Festival: ${existingFestival.name} (existing)`);
      console.log(`📅 Events: ${events.length} created`);
      console.log(`🎯 Activities: ${activities.length} created`);
      console.log(`🆔 Festival ID: ${existingFestival.id}`);
      
      return;
    }
    
    // Create festival, events, and activities
    const festival = await createFestival();
    const events = await createEvents(festival.id);
    const activities = await createActivities(festival.id);
    
    // Save summary
    const summary = {
      festival: festival,
      events: events,
      activities: activities,
      summary: {
        festivalId: festival.id,
        eventsCreated: events.length,
        activitiesCreated: activities.length,
        totalItems: 1 + events.length + activities.length
      },
      timestamp: new Date().toISOString()
    };
    
    const resultsDir = 'sziget-data-population-results';
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      `${resultsDir}/sziget-simple-population-${Date.now()}.json`,
      JSON.stringify(summary, null, 2)
    );
    
    console.log('\n🎉 SZIGET FESTIVAL DATA POPULATION COMPLETE');
    console.log('==========================================');
    console.log(`🎪 Festival: ${festival.name}`);
    console.log(`📅 Events: ${events.length} created`);
    console.log(`🎯 Activities: ${activities.length} created`);
    console.log(`📊 Total Items: ${summary.summary.totalItems}`);
    console.log(`🆔 Festival ID: ${festival.id}`);
    console.log(`📁 Results saved to: ${resultsDir}/sziget-simple-population-${Date.now()}.json`);
    
  } catch (error) {
    console.error('💥 Population failed:', error);
  }
  
  process.exit(0);
}

main();

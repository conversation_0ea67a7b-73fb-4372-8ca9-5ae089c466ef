# Test info

- Name: Festival Family Standardization Validation >> Component Standardization >> should use UnifiedInteractionButton consistently
- Location: C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:150:5

# Error details

```
Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
╔═════════════════════════════════════════════════════════════════════════╗
║ Looks like Playwright Test or Playwright was just installed or updated. ║
║ Please run the following command to download new browsers:              ║
║                                                                         ║
║     npx playwright install                                              ║
║                                                                         ║
║ <3 Playwright Team                                                      ║
╚═════════════════════════════════════════════════════════════════════════╝
```

# Test source

```ts
   50 |       // Test navigation to each section
   51 |       for (const section of SECTIONS.slice(1)) { // Skip home page
   52 |         const startTime = performance.now();
   53 |         
   54 |         // Navigate using the navigation menu
   55 |         const navLink = page.locator(`nav a[href="${section}"]`).first();
   56 |         await navLink.click();
   57 |         
   58 |         // Wait for navigation to complete
   59 |         await page.waitForURL(`**${section}*`);
   60 |         
   61 |         const endTime = performance.now();
   62 |         const navigationTime = endTime - startTime;
   63 |         
   64 |         // Verify navigation time is under threshold
   65 |         expect(navigationTime).toBeLessThan(PERFORMANCE_THRESHOLD);
   66 |         
   67 |         // Verify URL changed correctly
   68 |         expect(page.url()).toContain(section);
   69 |         
   70 |         // Verify no page reload occurred (React Router navigation)
   71 |         const pageReloads = navigationEvents.filter(event => 
   72 |           event.url.includes(section) && event.timestamp > startTime
   73 |         );
   74 |         expect(pageReloads.length).toBeLessThanOrEqual(1);
   75 |       }
   76 |     });
   77 |
   78 |     test('should handle tab navigation with URL parameters', async ({ page }) => {
   79 |       await page.goto('/famhub');
   80 |       
   81 |       // Test tab navigation in FamHub
   82 |       const tabs = ['CHAT', 'COMMUNITIES', 'RESOURCES', 'LOCAL_INFO'];
   83 |       
   84 |       for (const tab of tabs) {
   85 |         const startTime = performance.now();
   86 |         
   87 |         // Click tab
   88 |         const tabButton = page.locator(`button:has-text("${tab.replace('_', ' ')}")`).first();
   89 |         await tabButton.click();
   90 |         
   91 |         const endTime = performance.now();
   92 |         const tabSwitchTime = endTime - startTime;
   93 |         
   94 |         // Verify tab switching is fast
   95 |         expect(tabSwitchTime).toBeLessThan(100); // Even faster for tab switching
   96 |         
   97 |         // Verify URL parameter handling (if implemented)
   98 |         // Note: This might not be implemented yet, so we'll check if it exists
   99 |         const currentUrl = page.url();
  100 |         if (currentUrl.includes('tab=')) {
  101 |           expect(currentUrl).toContain(`tab=${tab}`);
  102 |         }
  103 |       }
  104 |     });
  105 |
  106 |     test('should not use window.location or window.open for internal navigation', async ({ page }) => {
  107 |       await page.goto('/');
  108 |       
  109 |       // Monitor for deprecated navigation patterns
  110 |       const windowNavigationCalls = [];
  111 |       await page.addInitScript(() => {
  112 |         const originalLocation = window.location;
  113 |         const originalOpen = window.open;
  114 |         
  115 |         Object.defineProperty(window, 'location', {
  116 |           get: () => originalLocation,
  117 |           set: (value) => {
  118 |             window.deprecatedNavigationCalls = window.deprecatedNavigationCalls || [];
  119 |             window.deprecatedNavigationCalls.push({ type: 'location', value });
  120 |             originalLocation.href = value;
  121 |           }
  122 |         });
  123 |         
  124 |         window.open = (...args) => {
  125 |           window.deprecatedNavigationCalls = window.deprecatedNavigationCalls || [];
  126 |           window.deprecatedNavigationCalls.push({ type: 'open', args });
  127 |           return originalOpen.apply(window, args);
  128 |         };
  129 |       });
  130 |       
  131 |       // Navigate through all sections
  132 |       for (const section of SECTIONS) {
  133 |         await page.goto(section);
  134 |         await page.waitForLoadState('networkidle');
  135 |         
  136 |         // Check for deprecated navigation calls
  137 |         const deprecatedCalls = await page.evaluate(() => window.deprecatedNavigationCalls || []);
  138 |         const internalCalls = deprecatedCalls.filter(call => 
  139 |           call.type === 'location' || 
  140 |           (call.type === 'open' && call.args[0] && !call.args[0].startsWith('http'))
  141 |         );
  142 |         
  143 |         expect(internalCalls.length).toBe(0);
  144 |       }
  145 |     });
  146 |   });
  147 |
  148 |   test.describe('Component Standardization', () => {
  149 |     
> 150 |     test('should use UnifiedInteractionButton consistently', async ({ page }) => {
      |     ^ Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
  151 |       await page.goto('/activities');
  152 |       await page.waitForLoadState('networkidle');
  153 |       
  154 |       // Check for UnifiedInteractionButton usage
  155 |       const interactionButtons = page.locator('[data-testid*="unified-interaction"], button[class*="unified-interaction"]');
  156 |       const buttonCount = await interactionButtons.count();
  157 |       
  158 |       if (buttonCount > 0) {
  159 |         // Verify all interaction buttons use consistent styling
  160 |         for (let i = 0; i < buttonCount; i++) {
  161 |           const button = interactionButtons.nth(i);
  162 |           const classes = await button.getAttribute('class');
  163 |           
  164 |           // Should have unified styling classes
  165 |           expect(classes).toMatch(/unified|interaction|standard/i);
  166 |         }
  167 |       }
  168 |     });
  169 |
  170 |     test('should use BentoCard components consistently', async ({ page }) => {
  171 |       const sectionsWithCards = ['/activities', '/discover', '/famhub'];
  172 |       
  173 |       for (const section of sectionsWithCards) {
  174 |         await page.goto(section);
  175 |         await page.waitForLoadState('networkidle');
  176 |         
  177 |         // Check for BentoCard usage
  178 |         const bentoCards = page.locator('[data-testid*="bento"], [class*="bento"], [class*="card"]');
  179 |         const cardCount = await bentoCards.count();
  180 |         
  181 |         if (cardCount > 0) {
  182 |           // Verify consistent card styling
  183 |           for (let i = 0; i < Math.min(cardCount, 5); i++) { // Check first 5 cards
  184 |             const card = bentoCards.nth(i);
  185 |             const isVisible = await card.isVisible();
  186 |             
  187 |             if (isVisible) {
  188 |               // Verify card has proper structure
  189 |               const hasContent = await card.locator('*').count() > 0;
  190 |               expect(hasContent).toBe(true);
  191 |             }
  192 |           }
  193 |         }
  194 |       }
  195 |     });
  196 |
  197 |     test('should use EnhancedUnifiedBadge with color mapping', async ({ page }) => {
  198 |       await page.goto('/activities');
  199 |       await page.waitForLoadState('networkidle');
  200 |       
  201 |       // Check for enhanced badge usage
  202 |       const badges = page.locator('[data-testid*="badge"], [class*="badge"]');
  203 |       const badgeCount = await badges.count();
  204 |       
  205 |       if (badgeCount > 0) {
  206 |         // Verify badges use color mapping service
  207 |         for (let i = 0; i < Math.min(badgeCount, 3); i++) {
  208 |           const badge = badges.nth(i);
  209 |           const isVisible = await badge.isVisible();
  210 |           
  211 |           if (isVisible) {
  212 |             const styles = await badge.getAttribute('style');
  213 |             const classes = await badge.getAttribute('class');
  214 |             
  215 |             // Should not have hardcoded colors
  216 |             if (styles) {
  217 |               expect(styles).not.toMatch(/#[0-9a-f]{6}|rgb\(|rgba\(/i);
  218 |             }
  219 |             
  220 |             // Should use design system classes
  221 |             expect(classes).toMatch(/badge|unified|enhanced/i);
  222 |           }
  223 |         }
  224 |       }
  225 |     });
  226 |   });
  227 |
  228 |   test.describe('Performance Validation', () => {
  229 |     
  230 |     test('should load all sections under 200ms', async ({ page }) => {
  231 |       for (const section of SECTIONS) {
  232 |         const startTime = performance.now();
  233 |         
  234 |         await page.goto(section);
  235 |         await page.waitForLoadState('domcontentloaded');
  236 |         
  237 |         const endTime = performance.now();
  238 |         const loadTime = endTime - startTime;
  239 |         
  240 |         console.log(`${section} load time: ${loadTime.toFixed(2)}ms`);
  241 |         expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLD);
  242 |       }
  243 |     });
  244 |
  245 |     test('should have fast interaction response times', async ({ page }) => {
  246 |       await page.goto('/activities');
  247 |       await page.waitForLoadState('networkidle');
  248 |       
  249 |       // Test button interactions
  250 |       const buttons = page.locator('button:visible').first();
```
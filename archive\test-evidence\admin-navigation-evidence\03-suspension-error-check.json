{"totalErrors": 18, "totalWarnings": 5, "suspensionErrors": 0, "suspensionErrorDetails": [], "allErrors": ["TypeError: Failed to fetch\n    at http://localhost:5173/node_modules/.vite/deps/@supabase_supabase-js.js?v=92c90913:4316:23\n    at _handleRequest2 (http://localhost:5173/node_modules/.vite/deps/@supabase_supabase-js.js?v=92c90913:4565:20)\n    at _request (http://localhost:5173/node_modules/.vite/deps/@supabase_supabase-js.js?v=92c90913:4555:22)\n    at SupabaseAuthClient.signInWithPassword (http://localhost:5173/node_modules/.vite/deps/@supabase_supabase-js.js?v=92c90913:5308:21)\n    at signIn (http://localhost:5173/src/providers/ConsolidatedAuthProvider.tsx:138:44)\n    at handleSubmit (http://localhost:5173/src/pages/SimpleAuth.tsx:110:41)\n    at HTMLUnknownElement.callCallback2 (http://localhost:5173/node_modules/.vite/deps/chunk-F34GCA6J.js?v=67209084:3680:22)\n    at Object.invokeGuardedCallbackDev (http://localhost:5173/node_modules/.vite/deps/chunk-F34GCA6J.js?v=67209084:3705:24)\n    at invokeGuardedCallback (http://localhost:5173/node_modules/.vite/deps/chunk-F34GCA6J.js?v=67209084:3739:39)\n    at invokeGuardedCallbackAndCatchFirstError (http://localhost:5173/node_modules/.vite/deps/chunk-F34GCA6J.js?v=67209084:3742:33)", "Sign in error: AuthRetryableFetchError: Failed to fetch\n    at _handleRequest2 (http://localhost:5173/node_modules/.vite/deps/@supabase_supabase-js.js?v=92c90913:4568:11)\n    at async _request (http://localhost:5173/node_modules/.vite/deps/@supabase_supabase-js.js?v=92c90913:4555:16)\n    at async SupabaseAuthClient.signInWithPassword (http://localhost:5173/node_modules/.vite/deps/@supabase_supabase-js.js?v=92c90913:5308:15)\n    at async signIn (http://localhost:5173/src/providers/ConsolidatedAuthProvider.tsx:138:24)\n    at async handleSubmit (http://localhost:5173/src/pages/SimpleAuth.tsx:110:35)", "Connection test error: {message: TypeError: Failed to fetch, details: TypeError: Failed to fetch\n    at http://localhost…deps/@supabase_supabase-js.js?v=92c90913:3880:24), hint: , code: }", "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.", "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.", "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.", "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.", "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.", "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.", "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.", "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.", "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.", "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element."], "timestamp": "2025-06-01T00:54:33.120Z"}
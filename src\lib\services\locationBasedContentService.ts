/**
 * Location-based Content Integration Service
 *
 * Provides location-based filtering and content delivery for local information,
 * integrating with festival/event location data for contextual recommendations.
 */

import { supabase } from '@/lib/supabase';
import { LocalInfo } from '@/types';

export interface LocationContext {
  festivalLocation?: string;
  eventLocation?: string;
  userLocation?: string;
  radius?: number; // in kilometers
}

export interface LocationBasedContent {
  localInfo: LocalInfo[];
  relevantFestivals: any[];
  relevantEvents: any[];
  relevantActivities: any[];
  locationScore: number; // 0-100 relevance score
}

export interface LocationFilter {
  category?: string;
  priority?: number;
  featured?: boolean;
  active?: boolean;
}

class LocationBasedContentService {
  private readonly LOCATION_CACHE = new Map<string, LocationBasedContent>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Get location-based content recommendations
   */
  async getLocationBasedContent(
    context: LocationContext,
    filter: LocationFilter = {}
  ): Promise<LocationBasedContent> {
    try {
      const cacheKey = this.generateCacheKey(context, filter);
      const cached = this.getCachedContent(cacheKey);
      
      if (cached) {
        console.log('📍 Location Service: Using cached content for', cacheKey);
        return cached;
      }

      console.log('📍 Location Service: Fetching fresh content for context:', context);

      // Get location-specific local information
      const localInfo = await this.getLocalInfoByLocation(context, filter);
      
      // Get relevant festivals
      const relevantFestivals = await this.getRelevantFestivals(context);
      
      // Get relevant events
      const relevantEvents = await this.getRelevantEvents(context);
      
      // Get relevant activities
      const relevantActivities = await this.getRelevantActivities(context);
      
      // Calculate location relevance score
      const locationScore = this.calculateLocationScore(context, {
        localInfo,
        relevantFestivals,
        relevantEvents,
        relevantActivities
      });

      const result: LocationBasedContent = {
        localInfo,
        relevantFestivals,
        relevantEvents,
        relevantActivities,
        locationScore
      };

      // Cache the result
      this.setCachedContent(cacheKey, result);

      console.log('✅ Location Service: Content fetched', {
        localInfo: localInfo.length,
        festivals: relevantFestivals.length,
        events: relevantEvents.length,
        activities: relevantActivities.length,
        score: locationScore
      });

      return result;
    } catch (error) {
      console.error('❌ Location Service: Error fetching content:', error);
      throw error;
    }
  }

  /**
   * Get local information filtered by location context
   */
  async getLocalInfoByLocation(
    context: LocationContext,
    filter: LocationFilter = {}
  ): Promise<LocalInfo[]> {
    try {
      let query = supabase
        .from('local_info')
        .select('*');

      // Apply basic filters
      if (filter.active !== false) {
        query = query.eq('is_active', true);
      }
      
      if (filter.category) {
        query = query.eq('category', filter.category);
      }
      
      if (filter.featured) {
        query = query.eq('is_featured', true);
      }
      
      if (filter.priority) {
        query = query.gte('priority', filter.priority);
      }

      // Order by relevance: featured first, then priority, then created date
      query = query
        .order('is_featured', { ascending: false })
        .order('priority', { ascending: false })
        .order('created_at', { ascending: false });

      const { data, error } = await query;

      if (error) throw error;

      // Transform and apply location-based scoring
      const localInfo: LocalInfo[] = (data || []).map(item => ({
        id: item.id,
        title: item.title,
        category: item.category,
        description: item.description || '',
        link: item.link
      }));

      // Apply location-based filtering and sorting
      return this.applyLocationScoring(localInfo, context);
    } catch (error) {
      console.error('❌ Location Service: Error fetching local info:', error);
      return [];
    }
  }

  /**
   * Get festivals relevant to the location context
   */
  async getRelevantFestivals(context: LocationContext): Promise<any[]> {
    try {
      let query = supabase
        .from('festivals')
        .select('*')
        .order('start_date', { ascending: true });

      // Apply location filtering if context provided
      if (context.festivalLocation || context.userLocation) {
        const locationFilter = context.festivalLocation || context.userLocation;
        if (locationFilter) {
          query = query.ilike('location', `%${locationFilter}%`);
        }
      }

      const { data, error } = await query.limit(10);

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('❌ Location Service: Error fetching festivals:', error);
      return [];
    }
  }

  /**
   * Get events relevant to the location context
   */
  async getRelevantEvents(context: LocationContext): Promise<any[]> {
    try {
      let query = supabase
        .from('events')
        .select('*')
        .order('start_date', { ascending: true });

      // Apply location filtering
      if (context.eventLocation || context.userLocation) {
        const locationFilter = context.eventLocation || context.userLocation;
        if (locationFilter) {
          query = query.ilike('location', `%${locationFilter}%`);
        }
      }

      const { data, error } = await query.limit(10);

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('❌ Location Service: Error fetching events:', error);
      return [];
    }
  }

  /**
   * Get activities relevant to the location context
   */
  async getRelevantActivities(context: LocationContext): Promise<any[]> {
    try {
      let query = supabase
        .from('activities')
        .select('*')
        .order('start_date', { ascending: true });

      // Apply location filtering
      if (context.festivalLocation || context.userLocation) {
        const locationFilter = context.festivalLocation || context.userLocation;
        if (locationFilter) {
          query = query.ilike('location', `%${locationFilter}%`);
        }
      }

      const { data, error } = await query.limit(10);

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('❌ Location Service: Error fetching activities:', error);
      return [];
    }
  }

  /**
   * Apply location-based scoring to local information
   */
  private applyLocationScoring(localInfo: LocalInfo[], context: LocationContext): LocalInfo[] {
    return localInfo.map(item => {
      let score = 0;
      
      // Boost score for featured items
      // Featured items get higher score
      score += 5;
      
      // Apply category-based location relevance
      if (context.festivalLocation || context.eventLocation) {
        // Boost transportation and accommodation for festival/event contexts
        if (['transportation', 'accommodation'].includes(item.category)) {
          score += 5;
        }
        
        // Boost safety information for any event context
        if (item.category === 'safety') {
          score += 3;
        }
      }
      
      return {
        ...item,
        locationScore: score
      };
    }).sort((a, b) => (b.locationScore || 0) - (a.locationScore || 0));
  }

  /**
   * Calculate overall location relevance score
   */
  private calculateLocationScore(
    context: LocationContext,
    content: Partial<LocationBasedContent>
  ): number {
    let score = 0;
    
    // Base score for having location context
    if (context.festivalLocation || context.eventLocation || context.userLocation) {
      score += 20;
    }
    
    // Score based on content availability
    if (content.localInfo && content.localInfo.length > 0) {
      score += Math.min(content.localInfo.length * 5, 30);
    }
    
    if (content.relevantFestivals && content.relevantFestivals.length > 0) {
      score += Math.min(content.relevantFestivals.length * 3, 20);
    }
    
    if (content.relevantEvents && content.relevantEvents.length > 0) {
      score += Math.min(content.relevantEvents.length * 3, 20);
    }
    
    if (content.relevantActivities && content.relevantActivities.length > 0) {
      score += Math.min(content.relevantActivities.length * 2, 10);
    }
    
    return Math.min(score, 100);
  }

  /**
   * Get location suggestions based on existing data
   */
  async getLocationSuggestions(query: string = ''): Promise<string[]> {
    try {
      const locations = new Set<string>();
      
      // Get festival locations
      const { data: festivals } = await supabase
        .from('festivals')
        .select('location')
        .ilike('location', `%${query}%`)
        .limit(10);
      
      festivals?.forEach(f => f.location && locations.add(f.location));
      
      // Get event locations
      const { data: events } = await supabase
        .from('events')
        .select('location')
        .ilike('location', `%${query}%`)
        .limit(10);
      
      events?.forEach(e => e.location && locations.add(e.location));
      
      return Array.from(locations).slice(0, 10);
    } catch (error) {
      console.error('❌ Location Service: Error fetching suggestions:', error);
      return [];
    }
  }

  /**
   * Cache management
   */
  private generateCacheKey(context: LocationContext, filter: LocationFilter): string {
    return `location_${JSON.stringify(context)}_${JSON.stringify(filter)}`;
  }

  private getCachedContent(key: string): LocationBasedContent | null {
    const cached = this.LOCATION_CACHE.get(key);
    if (cached && Date.now() - (cached as any).timestamp < this.CACHE_DURATION) {
      return (cached as any).content;
    }
    return null;
  }

  private setCachedContent(key: string, content: LocationBasedContent): void {
    this.LOCATION_CACHE.set(key, {
      ...content,
      timestamp: Date.now()
    } as any);
  }

  /**
   * Clear location cache
   */
  clearCache(): void {
    this.LOCATION_CACHE.clear();
    console.log('📍 Location Service: Cache cleared');
  }
}

// Export singleton instance
export const locationBasedContentService = new LocationBasedContentService();
export default locationBasedContentService;

/**
 * Phase 1: Admin Navigation Persistence Validation - Streamlined
 * 
 * Focused testing of admin navigation system with evidence collection
 */

import { test, expect } from '@playwright/test';
import { promises as fs } from 'fs';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'phase1-admin-validation-evidence';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

// Ensure evidence directory exists
test.beforeAll(async () => {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory created: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory already exists: ${EVIDENCE_DIR}`);
  }
});

test.describe('Phase 1: Admin Navigation Persistence Validation', () => {
  
  test('Admin Authentication Flow Validation', async ({ page }) => {
    console.log('🔐 Testing admin authentication flow...');
    
    const startTime = Date.now();
    
    // Step 1: Navigate to app
    await page.goto(APP_URL);
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/01-home-page.png`,
      fullPage: true 
    });
    
    // Step 2: Navigate to auth page
    await page.goto(`${APP_URL}/auth`);
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/02-auth-page.png`,
      fullPage: true 
    });
    
    // Step 3: Fill credentials
    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/03-credentials-filled.png`,
      fullPage: true 
    });
    
    // Step 4: Submit and measure performance
    const authStartTime = Date.now();
    await page.click('button[type="submit"]');
    
    // Wait for any navigation or state change
    await page.waitForTimeout(3000);
    
    const authEndTime = Date.now();
    const authDuration = authEndTime - authStartTime;
    
    console.log(`⏱️ Auth flow took ${authDuration}ms`);
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/04-post-auth.png`,
      fullPage: true 
    });
    
    // Step 5: Check current state
    const currentUrl = page.url();
    const pageContent = await page.content();
    
    // Look for admin indicators
    const hasAdminText = await page.locator('text=Admin').isVisible();
    const hasAdminLink = await page.locator('a[href*="/admin"]').isVisible();
    const hasShieldIcon = await page.locator('[data-testid="admin-shield"]').isVisible();
    
    // Check for authentication success indicators
    const isAuthenticated = !currentUrl.includes('/auth') || 
                           hasAdminText || 
                           hasAdminLink || 
                           await page.locator('text=Dashboard').isVisible() ||
                           await page.locator('text=Welcome').isVisible();
    
    // Save detailed results
    const authResults = {
      authDuration,
      target: 686,
      status: authDuration <= 1000 ? 'EXCELLENT' : 'NEEDS_IMPROVEMENT',
      currentUrl,
      isAuthenticated,
      hasAdminText,
      hasAdminLink,
      hasShieldIcon,
      timestamp: new Date().toISOString()
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/auth-test-results.json`,
      JSON.stringify(authResults, null, 2)
    );
    
    console.log(`✅ Authentication test completed`);
    console.log(`📊 Results: ${JSON.stringify(authResults, null, 2)}`);
  });

  test('Admin Navigation Discovery', async ({ page }) => {
    console.log('🧭 Discovering admin navigation elements...');
    
    // Login first
    await page.goto(`${APP_URL}/auth`);
    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/05-post-login-state.png`,
      fullPage: true 
    });
    
    // Try to navigate to admin dashboard
    try {
      await page.goto(`${APP_URL}/admin`);
      await page.waitForLoadState('networkidle');
      
      await page.screenshot({ 
        path: `${EVIDENCE_DIR}/06-admin-dashboard.png`,
        fullPage: true 
      });
      
      console.log('✅ Admin dashboard accessible');
    } catch (error) {
      console.log(`⚠️ Admin dashboard navigation failed: ${error.message}`);
    }
    
    // Look for admin navigation elements
    const navigationElements = {
      adminText: await page.locator('text=Admin').isVisible(),
      adminDropdown: await page.locator('.admin-dropdown-container').isVisible(),
      shieldIcon: await page.locator('[data-testid="admin-shield"]').isVisible(),
      dashboardLink: await page.locator('a[href="/admin"]').isVisible(),
      usersLink: await page.locator('a[href="/admin/users"]').isVisible(),
      eventsLink: await page.locator('a[href="/admin/events"]').isVisible(),
      festivalsLink: await page.locator('a[href="/admin/festivals"]').isVisible(),
      activitiesLink: await page.locator('a[href="/admin/activities"]').isVisible(),
      announcementsLink: await page.locator('a[href="/admin/announcements"]').isVisible()
    };
    
    // Test view mode toggle
    const viewToggleElements = {
      viewAsUserButton: await page.locator('text=View as User').isVisible(),
      backToAdminButton: await page.locator('text=Back to Admin').isVisible()
    };
    
    const discoveryResults = {
      currentUrl: page.url(),
      navigationElements,
      viewToggleElements,
      timestamp: new Date().toISOString()
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/navigation-discovery-results.json`,
      JSON.stringify(discoveryResults, null, 2)
    );
    
    console.log(`🔍 Navigation discovery completed`);
    console.log(`📊 Found elements: ${JSON.stringify(navigationElements, null, 2)}`);
  });

  test('Session Persistence Validation', async ({ page }) => {
    console.log('🔄 Testing session persistence...');
    
    // Login
    await page.goto(`${APP_URL}/auth`);
    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    const urlBeforeRefresh = page.url();
    
    // Test page refresh
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    const urlAfterRefresh = page.url();
    const sessionPersisted = !urlAfterRefresh.includes('/auth');
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/07-after-refresh.png`,
      fullPage: true 
    });
    
    // Test navigation to different sections
    const testRoutes = [
      { name: 'Home', path: '/' },
      { name: 'Activities', path: '/activities' },
      { name: 'Profile', path: '/profile' }
    ];
    
    const navigationResults = [];
    
    for (const route of testRoutes) {
      try {
        await page.goto(`${APP_URL}${route.path}`);
        await page.waitForLoadState('networkidle');
        
        const currentUrl = page.url();
        const sessionMaintained = !currentUrl.includes('/auth');
        
        navigationResults.push({
          route: route.name,
          path: route.path,
          sessionMaintained,
          currentUrl,
          success: true
        });
        
        console.log(`✅ ${route.name}: Session ${sessionMaintained ? 'Maintained' : 'Lost'}`);
        
      } catch (error) {
        navigationResults.push({
          route: route.name,
          path: route.path,
          success: false,
          error: error.message
        });
      }
    }
    
    const persistenceResults = {
      urlBeforeRefresh,
      urlAfterRefresh,
      sessionPersisted,
      navigationResults,
      timestamp: new Date().toISOString()
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/session-persistence-results.json`,
      JSON.stringify(persistenceResults, null, 2)
    );
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/08-final-state.png`,
      fullPage: true 
    });
    
    console.log(`🔄 Session persistence test completed`);
    console.log(`📊 Results: Session persisted: ${sessionPersisted}`);
  });

  // Generate final report
  test.afterAll(async () => {
    console.log('📊 Generating Phase 1 validation report...');
    
    const finalReport = {
      testSuite: 'Phase 1: Admin Navigation Persistence Validation - Streamlined',
      timestamp: new Date().toISOString(),
      evidenceFiles: [
        '01-home-page.png',
        '02-auth-page.png',
        '03-credentials-filled.png',
        '04-post-auth.png',
        '05-post-login-state.png',
        '06-admin-dashboard.png',
        '07-after-refresh.png',
        '08-final-state.png'
      ],
      dataFiles: [
        'auth-test-results.json',
        'navigation-discovery-results.json',
        'session-persistence-results.json'
      ],
      summary: 'Streamlined Phase 1 testing with focused admin validation'
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/phase1-validation-report.json`,
      JSON.stringify(finalReport, null, 2)
    );
    
    console.log('✅ Phase 1 validation completed with evidence collection');
    console.log(`📁 Evidence saved to: ${EVIDENCE_DIR}/`);
  });
});

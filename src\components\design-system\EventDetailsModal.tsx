import React from 'react';
import { Calendar, MapPin, Clock, Users, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { UnifiedInteractionButton } from './UnifiedInteractionButton';
import {
  UnifiedModal,
  ModalContent,
  ModalSection,
  ModalMetadata,
  ModalDescription,
  ModalTags,
  ModalActions,
  ModalImage,
  ModalStats,
  ModalFooter,
} from './index';

interface EventDetailsModalProps {
  event: {
    id: string;
    title: string;
    description: string;
    category: string | null;
    start_date: string;
    end_date?: string;
    location: string;
    image?: string;
    festival_name?: string;
    capacity?: number | null;
    attendee_count?: number;
    tags?: string[];
  } | null;
  isOpen: boolean;
  onClose: () => void;
  triggerElement?: HTMLElement | null;
}

export const EventDetailsModal: React.FC<EventDetailsModalProps> = ({
  event,
  isOpen,
  onClose,
  triggerElement,
}) => {
  if (!event) return null;

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Format date range
  const getDateRange = () => {
    if (event.end_date && event.end_date !== event.start_date) {
      return `${formatDate(event.start_date)} - ${formatDate(event.end_date)}`;
    }
    return formatDate(event.start_date);
  };

  // Metadata items for the event
  const metadataItems = [
    {
      icon: Calendar,
      label: 'Date',
      value: getDateRange(),
    },
    {
      icon: MapPin,
      label: 'Location',
      value: event.location,
    },
    {
      icon: Clock,
      label: 'Duration',
      value: 'Check event details',
    },
    ...(event.festival_name ? [{
      icon: Users,
      label: 'Festival',
      value: event.festival_name,
    }] : []),
  ];

  // Stats for the event
  const eventStats = [
    {
      label: 'Category',
      value: (event.category ?? 'OTHER').toUpperCase(),
    },
    ...(event.capacity ? [{
      label: 'Capacity',
      value: event.capacity.toString(),
    }] : []),
    ...(event.attendee_count !== undefined ? [{
      label: 'Attending',
      value: event.attendee_count.toString(),
    }] : []),
  ];

  return (
    <UnifiedModal
      open={isOpen}
      onClose={onClose}
      title={event.title}
      itemId={event.id}
      contentType="events"
      category={event.category || 'general'}
      itemType="event"
      size="lg"
      showCloseButton={true}
    >
      {/* Event Image */}
      <ModalImage
        src={event.image}
        alt={event.title}
        fallbackType="event"
        className="h-48 sm:h-64"
      />

      <ModalContent>
        {/* Festival Badge and Category */}
        <ModalSection spacing="tight">
          <div className="flex items-center justify-between">
            <ModalTags 
              tags={[event.category ?? 'OTHER']}
              variant="category"
            />
            {event.festival_name && (
              <ModalTags 
                tags={[event.festival_name]} 
                variant="secondary" // Use supported variant instead of featured
              />
            )}
          </div>
        </ModalSection>

        {/* Event Description */}
        <ModalSection title="About This Event">
          <ModalDescription>
            {event.description}
          </ModalDescription>
        </ModalSection>

        {/* Event Details */}
        <ModalSection title="Details">
          <ModalMetadata items={metadataItems} />
        </ModalSection>

        {/* Event Stats */}
        {eventStats.length > 0 && (
          <ModalSection>
            <ModalStats stats={eventStats} />
          </ModalSection>
        )}

        {/* Tags */}
        {event.tags && event.tags.length > 0 && (
          <ModalSection>
            <ModalTags tags={event.tags} />
          </ModalSection>
        )}
      </ModalContent>

      {/* Modal Footer with Actions */}
      <ModalFooter>
        <ModalActions alignment="between">
          <div className="flex gap-2">
            <UnifiedInteractionButton
              type="save"
              itemId={event.id}
              itemType="event"
              variant="outline"
              size="sm"
              className="text-white border-white/20 hover:bg-white/10"
            />
            <UnifiedInteractionButton
              type="share"
              itemId={event.id}
              itemType="event"
              variant="outline"
              size="sm"
              className="text-white border-white/20 hover:bg-white/10"
            />
          </div>
          <Button
            data-modal-primary-focus
            className="bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-500 hover:to-purple-400 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add to Calendar
          </Button>
        </ModalActions>
      </ModalFooter>
    </UnifiedModal>
  );
};

export default EventDetailsModal;

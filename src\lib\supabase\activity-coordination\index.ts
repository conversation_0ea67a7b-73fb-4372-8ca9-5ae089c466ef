/**
 * Activity Coordination Module Index
 *
 * Central export point for all activity coordination functionality.
 * Follows Festival Family's modular architecture and single source of truth principles.
 *
 * @module ActivityCoordination
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

// Import types for internal use in utility functions
import type {
  AttendanceStatus,
  ActivityAttendance,
  ActivityWithAttendance,
  ChatPlatform,
  ActivityCoordinationSuggestion
} from './types'

// ============================================================================
// TYPE EXPORTS
// ============================================================================
export type {
  // Core types
  AttendanceStatus,
  PreferenceLevel,
  ChatPlatform,
  
  // Database entity types
  ActivityAttendance,
  ArtistPreference,
  MusicGenrePreference,
  GroupExternalLink,
  
  // Enhanced entity types
  ActivityWithAttendance,
  UserMusicProfile,
  GroupWithExternalLinks,
  
  // Matching and coordination types
  ActivityMatch,
  MusicMatch,
  ActivityCoordinationSuggestion,
  LiveActivityStatus,
  
  // API request/response types
  SetAttendanceRequest,
  SetMusicPreferenceRequest,
  CreateExternalLinkRequest,
  FindActivityBuddiesRequest,
  
  // Filter and search types
  ActivityCoordinationFilter,
  MusicMatchingFilter,
  
  // Real-time subscription types
  ActivityCoordinationSubscription,
  
  // Analytics and insights types
  ActivityCoordinationInsights,
  
  // Error types
  ActivityCoordinationError,
  
  // Utility types
  ActivityCoordinationEvent,
  CoordinationStatus,
  MatchingAlgorithm,

  // Smart group formation types
  GroupFormationType,
  SuggestionStatus,
  GroupSuggestion,
  GroupSuggestionResponse,
  SmartGroup,
  ActivityBasedGroupCandidate,
  MusicBasedGroupCandidate,
  SmartGroupFormationRequest,
  GroupFormationInsights
} from './types'

// Smart group formation utilities
export {
  validateGroupFormationRequest,
  validateGroupSuggestion,
  formatFormationType,
  formatSuggestionStatus,
  formatConfidenceScore,
  formatFormationInsights,
  isGroupSuggestionExpired,
  getTimeUntilExpiry,
  generateDefaultGroupName,
  generateDefaultGroupDescription,
  calculateFormationPotential,
  filterActiveSuggestions,
  sortSuggestionsByPriority,
  GROUP_FORMATION_LIMITS,
  FORMATION_TYPE_ICONS
} from './utils'

// ============================================================================
// UNIFIED TRACKING SERVICE EXPORTS
// ============================================================================
export {
  // Unified tracking service
  UnifiedUserTrackingService,
  unifiedUserTracking
} from '@/lib/supabase/services'

// ============================================================================
// HOOK EXPORTS - Temporarily disabled during consolidation
// ============================================================================
// Note: Activity coordination hooks have been consolidated into unified tracking
// Use useActivityTracking from @/hooks/useUnifiedTracking instead

// ============================================================================
// COMPONENT EXPORTS
// ============================================================================
export {
  // Attendance components
  AttendanceButtons,
  AttendanceStatus as AttendanceStatusComponent,
  AttendanceSummary
} from '@/components/activity-coordination/AttendanceButtons'

export {
  // Smart group formation components
  GroupSuggestionCard,
  GroupSuggestionList,
  CompactGroupSuggestionCard
} from '@/components/activity-coordination/GroupSuggestionCard'

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Check if a user is attending an activity
 */
export function isUserAttending(
  attendance?: ActivityAttendance | null,
  status: AttendanceStatus | AttendanceStatus[] = ['going', 'interested']
): boolean {
  if (!attendance) return false
  
  const statuses = Array.isArray(status) ? status : [status]
  return statuses.includes(attendance.status)
}

/**
 * Get attendance status priority for sorting
 */
export function getAttendanceStatusPriority(status: AttendanceStatus): number {
  const priorities = {
    going: 1,
    interested: 2,
    maybe: 3,
    not_going: 4
  }
  return priorities[status] || 5
}

/**
 * Calculate music compatibility score between two users
 */
export function calculateMusicCompatibility(
  user1Artists: string[],
  user1Genres: string[],
  user2Artists: string[],
  user2Genres: string[]
): number {
  const sharedArtists = user1Artists.filter(artist => user2Artists.includes(artist))
  const sharedGenres = user1Genres.filter(genre => user2Genres.includes(genre))
  
  // Weight artists more heavily than genres
  const artistScore = sharedArtists.length * 2
  const genreScore = sharedGenres.length * 1
  
  return artistScore + genreScore
}

/**
 * Format attendance counts for display
 */
export function formatAttendanceCounts(counts: {
  going: number
  interested: number
  maybe: number
  total: number
}): string {
  const { going, interested, maybe, total } = counts
  
  if (total === 0) return 'No one interested yet'
  if (total === 1) return '1 person interested'
  
  const parts: string[] = []
  if (going > 0) parts.push(`${going} going`)
  if (interested > 0) parts.push(`${interested} interested`)
  if (maybe > 0) parts.push(`${maybe} maybe`)
  
  return parts.join(', ')
}

/**
 * Get platform icon name for external chat links
 */
export function getChatPlatformIcon(platform: ChatPlatform): string {
  const icons = {
    whatsapp: 'MessageCircle',
    discord: 'Hash',
    telegram: 'Send',
    signal: 'Shield',
    other: 'ExternalLink'
  }
  return icons[platform] || icons.other
}

/**
 * Validate external chat link URL
 */
export function validateChatLinkUrl(platform: ChatPlatform, url: string): boolean {
  const patterns = {
    whatsapp: /^https:\/\/(chat\.whatsapp\.com|wa\.me)\/.+/,
    discord: /^https:\/\/discord\.(gg|com\/invite)\/.+/,
    telegram: /^https:\/\/(t\.me|telegram\.me)\/.+/,
    signal: /^https:\/\/signal\.group\/.+/,
    other: /^https?:\/\/.+/
  }
  
  const pattern = patterns[platform] || patterns.other
  return pattern.test(url)
}

/**
 * Generate activity coordination suggestion ID
 */
export function generateCoordinationId(
  type: string,
  activityId?: string,
  userId?: string
): string {
  const timestamp = Date.now()
  const parts = [type, timestamp]
  
  if (activityId) parts.push(activityId)
  if (userId) parts.push(userId)
  
  return parts.join('_')
}

/**
 * Check if coordination suggestion is expired
 */
export function isCoordinationExpired(suggestion: ActivityCoordinationSuggestion): boolean {
  if (!suggestion.expires_at) return false
  return new Date(suggestion.expires_at) < new Date()
}

/**
 * Sort activities by attendance priority
 */
export function sortActivitiesByAttendance(
  activities: ActivityWithAttendance[],
  userAttendance?: { [activityId: string]: ActivityAttendance }
): ActivityWithAttendance[] {
  return activities.sort((a, b) => {
    const aAttendance = userAttendance?.[a.id]
    const bAttendance = userAttendance?.[b.id]
    
    if (!aAttendance && !bAttendance) return 0
    if (!aAttendance) return 1
    if (!bAttendance) return -1
    
    const aPriority = getAttendanceStatusPriority(aAttendance.status)
    const bPriority = getAttendanceStatusPriority(bAttendance.status)
    
    return aPriority - bPriority
  })
}

/**
 * Filter activities by attendance status
 */
export function filterActivitiesByAttendance(
  activities: ActivityWithAttendance[],
  status: AttendanceStatus | AttendanceStatus[],
  userAttendance?: { [activityId: string]: ActivityAttendance }
): ActivityWithAttendance[] {
  const statuses = Array.isArray(status) ? status : [status]
  
  return activities.filter(activity => {
    const attendance = userAttendance?.[activity.id]
    return attendance && statuses.includes(attendance.status)
  })
}

/**
 * Get upcoming activities from user's schedule
 */
export function getUpcomingActivities(
  activities: ActivityWithAttendance[],
  hoursAhead = 24
): ActivityWithAttendance[] {
  const now = new Date()
  const cutoff = new Date(now.getTime() + hoursAhead * 60 * 60 * 1000)
  
  return activities
    .filter(activity => {
      if (!activity.start_date) return false
      const startDate = new Date(activity.start_date)
      return startDate >= now && startDate <= cutoff
    })
    .sort((a, b) => {
      const aStart = new Date(a.start_date!).getTime()
      const bStart = new Date(b.start_date!).getTime()
      return aStart - bStart
    })
}

// ============================================================================
// CONSTANTS
// ============================================================================

export const ATTENDANCE_STATUS_LABELS = {
  going: 'Going',
  interested: 'Interested',
  maybe: 'Maybe',
  not_going: 'Not Going'
} as const

export const PREFERENCE_LEVEL_LABELS = {
  love: 'Love',
  like: 'Like',
  neutral: 'Neutral',
  dislike: 'Dislike'
} as const

export const CHAT_PLATFORM_LABELS = {
  whatsapp: 'WhatsApp',
  discord: 'Discord',
  telegram: 'Telegram',
  signal: 'Signal',
  other: 'Other'
} as const

export const COORDINATION_TYPES = {
  activity_buddy: 'Activity Buddy',
  music_group: 'Music Group',
  skill_share: 'Skill Share',
  travel_buddy: 'Travel Buddy',
  spontaneous_meetup: 'Spontaneous Meetup'
} as const

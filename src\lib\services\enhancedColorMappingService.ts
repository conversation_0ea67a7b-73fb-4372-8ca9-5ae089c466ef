import { supabase } from '@/lib/supabase';

/**
 * Enhanced Color Mapping Service
 * Research-based color psychology and visual hierarchy system
 * for Festival Family content categorization
 */

export interface ColorMapping {
  id: string;
  content_type: string;
  category: string;
  color_primary: string;
  color_secondary: string;
  color_accent: string;
  emoji_icon: string | null;
  description: string | null;
  show_icon: boolean | null;
  admin_configurable: boolean | null;
  created_at?: string;
  updated_at?: string;
}

interface ContentTypeConfig {
  name: string;
  description: string;
  colorPrimary: string;
  colorSecondary: string;
  colorAccent: string;
  emoji: string;
  subcategories?: {
    [key: string]: {
      colorPrimary: string;
      colorSecondary: string;
      colorAccent: string;
      emoji: string;
      description: string;
    };
  };
}

class EnhancedColorMappingService {
  /**
   * Enhanced 2025 Visual Excellence: Vibrant fallback themes
   * Matches the established visual standards across FamHub, Resources, and other sections
   */
  private readonly vibrantFallbackThemes = [
    'bg-gradient-to-br from-blue-500/20 to-blue-600/25 border-blue-500/30 shadow-lg shadow-blue-500/10',
    'bg-gradient-to-br from-emerald-500/20 to-emerald-600/25 border-emerald-500/30 shadow-lg shadow-emerald-500/10',
    'bg-gradient-to-br from-violet-500/20 to-violet-600/25 border-violet-500/30 shadow-lg shadow-violet-500/10',
    'bg-gradient-to-br from-orange-500/20 to-orange-600/25 border-orange-500/30 shadow-lg shadow-orange-500/10',
    'bg-gradient-to-br from-rose-500/20 to-rose-600/25 border-rose-500/30 shadow-lg shadow-rose-500/10',
    'bg-gradient-to-br from-amber-500/20 to-amber-600/25 border-amber-500/30 shadow-lg shadow-amber-500/10',
    'bg-gradient-to-br from-fuchsia-500/20 to-fuchsia-600/25 border-fuchsia-500/30 shadow-lg shadow-fuchsia-500/10',
    'bg-gradient-to-br from-cyan-500/20 to-cyan-600/25 border-cyan-500/30 shadow-lg shadow-cyan-500/10'
  ];

  /**
   * Research-based color psychology mapping for festival content
   * Based on studies of user engagement and content categorization
   */
  private readonly contentTypeConfigs: { [key: string]: ContentTypeConfig } = {
    // ACTIVITIES - Primary content type
    activities: {
      name: 'Activities',
      description: 'Festival Family organized activities and events',
      colorPrimary: '#8B5CF6', // Purple - creativity and community
      colorSecondary: '#A78BFA',
      colorAccent: '#C4B5FD',
      emoji: '🎪',
      subcategories: {
        meetup: {
          colorPrimary: '#3B82F6', // Blue - trust and connection
          colorSecondary: '#60A5FA',
          colorAccent: '#93C5FD',
          emoji: '👥',
          description: 'Community meetups and gatherings'
        },
        social: {
          colorPrimary: '#F59E0B', // Orange - energy and excitement
          colorSecondary: '#FBBF24',
          colorAccent: '#FCD34D',
          emoji: '🎉',
          description: 'Social events and celebrations'
        },
        workshop: {
          colorPrimary: '#10B981', // Green - growth and learning
          colorSecondary: '#34D399',
          colorAccent: '#6EE7B7',
          emoji: '🛠️',
          description: 'Educational workshops and training'
        },
        performance: {
          colorPrimary: '#EF4444', // Red - passion and performance
          colorSecondary: '#F87171',
          colorAccent: '#FCA5A5',
          emoji: '🎭',
          description: 'Performances and shows'
        },
        game: {
          colorPrimary: '#8B5CF6', // Purple - fun and creativity
          colorSecondary: '#A78BFA',
          colorAccent: '#C4B5FD',
          emoji: '🎮',
          description: 'Games and competitions'
        },
        food: {
          colorPrimary: '#F97316', // Orange-red - appetite and warmth
          colorSecondary: '#FB923C',
          colorAccent: '#FDBA74',
          emoji: '🍽️',
          description: 'Food and dining experiences'
        },
        other: {
          colorPrimary: '#6B7280', // Gray - neutral and versatile
          colorSecondary: '#9CA3AF',
          colorAccent: '#D1D5DB',
          emoji: '📝',
          description: 'Other activities and events'
        }
      }
    },

    // TIPS & GUIDES - Educational content
    tips: {
      name: 'Tips & Guides',
      description: 'Festival tips, guides, and helpful information',
      colorPrimary: '#059669', // Emerald - helpful and trustworthy
      colorSecondary: '#10B981',
      colorAccent: '#34D399',
      emoji: '💡',
      subcategories: {
        packing: {
          colorPrimary: '#0891B2', // Cyan - practical and organized
          colorSecondary: '#06B6D4',
          colorAccent: '#22D3EE',
          emoji: '🎒',
          description: 'Packing lists and preparation tips'
        },
        travel: {
          colorPrimary: '#7C3AED', // Violet - adventure and journey
          colorSecondary: '#8B5CF6',
          colorAccent: '#A78BFA',
          emoji: '✈️',
          description: 'Travel tips and transportation'
        },
        safety: {
          colorPrimary: '#DC2626', // Red - important and urgent
          colorSecondary: '#EF4444',
          colorAccent: '#F87171',
          emoji: '🛡️',
          description: 'Safety tips and emergency information'
        },
        general: {
          colorPrimary: '#059669',
          colorSecondary: '#10B981',
          colorAccent: '#34D399',
          emoji: '💡',
          description: 'General festival tips and advice'
        }
      }
    },

    // COMMUNITY - Social features
    community: {
      name: 'Community',
      description: 'Community features and social connections',
      colorPrimary: '#DB2777', // Pink - social and friendly
      colorSecondary: '#EC4899',
      colorAccent: '#F472B6',
      emoji: '👨‍👩‍👧‍👦',
      subcategories: {
        chat: {
          colorPrimary: '#0EA5E9', // Sky blue - communication
          colorSecondary: '#38BDF8',
          colorAccent: '#7DD3FC',
          emoji: '💬',
          description: 'Chat groups and messaging'
        },
        forum: {
          colorPrimary: '#7C2D12', // Brown - discussion and depth
          colorSecondary: '#92400E',
          colorAccent: '#A16207',
          emoji: '💭',
          description: 'Forum discussions and Q&A'
        },
        social: {
          colorPrimary: '#DB2777',
          colorSecondary: '#EC4899',
          colorAccent: '#F472B6',
          emoji: '🤝',
          description: 'Social connections and networking'
        }
      }
    },

    // FESTIVALS - Event information
    festivals: {
      name: 'Festivals',
      description: 'Festival information and schedules',
      colorPrimary: '#7C3AED', // Violet - celebration and excitement
      colorSecondary: '#8B5CF6',
      colorAccent: '#A78BFA',
      emoji: '🎪',
      subcategories: {
        sziget: {
          colorPrimary: '#DC2626', // Red - Sziget brand colors
          colorSecondary: '#EF4444',
          colorAccent: '#F87171',
          emoji: '🏝️',
          description: 'Sziget Festival information'
        },
        balaton: {
          colorPrimary: '#0891B2', // Blue - water/lake theme
          colorSecondary: '#06B6D4',
          colorAccent: '#22D3EE',
          emoji: '🌊',
          description: 'Balaton Sound Festival information'
        },
        other: {
          colorPrimary: '#7C3AED',
          colorSecondary: '#8B5CF6',
          colorAccent: '#A78BFA',
          emoji: '🎪',
          description: 'Other festival information'
        }
      }
    },

    // RESOURCES - Helpful resources
    resources: {
      name: 'Resources',
      description: 'Helpful resources and tools',
      colorPrimary: '#0D9488', // Teal - resourceful and reliable
      colorSecondary: '#14B8A6',
      colorAccent: '#2DD4BF',
      emoji: '📚',
      subcategories: {
        gear: {
          colorPrimary: '#374151', // Gray - practical and sturdy
          colorSecondary: '#4B5563',
          colorAccent: '#6B7280',
          emoji: '⚙️',
          description: 'Camping gear and equipment'
        },
        shopping: {
          colorPrimary: '#7C2D12', // Brown - commerce and trade
          colorSecondary: '#92400E',
          colorAccent: '#A16207',
          emoji: '🛒',
          description: 'Shopping tips and recommendations'
        },
        emergency: {
          colorPrimary: '#DC2626', // Red - urgent and important
          colorSecondary: '#EF4444',
          colorAccent: '#F87171',
          emoji: '🚨',
          description: 'Emergency contacts and procedures'
        }
      }
    }
  };

  /**
   * Initialize enhanced color mappings in the database
   */
  async initializeEnhancedColorMappings(): Promise<{ success: boolean; count: number; errors: string[] }> {
    const errors: string[] = [];
    let successCount = 0;

    try {
      // Clear existing color mappings
      await supabase.from('color_mappings').delete().neq('id', '00000000-0000-0000-0000-000000000000');

      // Insert enhanced color mappings
      for (const [contentType, config] of Object.entries(this.contentTypeConfigs)) {
        // Insert main content type
        const mainMapping: Omit<ColorMapping, 'id'> = {
          content_type: contentType,
          category: 'main',
          color_primary: config.colorPrimary,
          color_secondary: config.colorSecondary,
          color_accent: config.colorAccent,
          emoji_icon: config.emoji,
          description: config.description,
          show_icon: true, // Default to showing icons
          admin_configurable: true // Allow admin configuration
        };

        const { error: mainError } = await supabase
          .from('color_mappings')
          .insert(mainMapping);

        if (mainError) {
          errors.push(`Failed to insert main mapping for ${contentType}: ${mainError.message}`);
        } else {
          successCount++;
        }

        // Insert subcategories
        if (config.subcategories) {
          for (const [subCategory, subConfig] of Object.entries(config.subcategories)) {
            const subMapping: Omit<ColorMapping, 'id'> = {
              content_type: contentType,
              category: subCategory,
              color_primary: subConfig.colorPrimary,
              color_secondary: subConfig.colorSecondary,
              color_accent: subConfig.colorAccent,
              emoji_icon: subConfig.emoji,
              description: subConfig.description,
              show_icon: true, // Default to showing icons
              admin_configurable: true // Allow admin configuration
            };

            const { error: subError } = await supabase
              .from('color_mappings')
              .insert(subMapping);

            if (subError) {
              errors.push(`Failed to insert subcategory mapping for ${contentType}.${subCategory}: ${subError.message}`);
            } else {
              successCount++;
            }
          }
        }
      }

      console.log(`✅ Enhanced color mappings initialized: ${successCount} mappings created`);

      return {
        success: errors.length === 0,
        count: successCount,
        errors
      };
    } catch (error) {
      return {
        success: false,
        count: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Get color mapping for a specific content type and category
   */
  async getColorMapping(contentType: string, category: string = 'main'): Promise<ColorMapping | null> {
    try {
      const { data, error } = await supabase
        .from('color_mappings')
        .select('*')
        .eq('content_type', contentType)
        .eq('category', category)
        .single();

      if (error) {
        console.warn(`Color mapping not found for ${contentType}.${category}:`, error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching color mapping:', error);
      return null;
    }
  }

  /**
   * Get all color mappings for a content type
   */
  async getContentTypeColorMappings(contentType: string): Promise<ColorMapping[]> {
    try {
      const { data, error } = await supabase
        .from('color_mappings')
        .select('*')
        .eq('content_type', contentType)
        .order('category');

      if (error) {
        console.warn(`Color mappings not found for ${contentType}:`, error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching content type color mappings:', error);
      return [];
    }
  }

  /**
   * Get vibrant fallback theme by index
   * Provides consistent visual excellence across all components
   */
  getVibrantFallbackTheme(index: number): string {
    return this.vibrantFallbackThemes[index % this.vibrantFallbackThemes.length];
  }

  /**
   * Enhanced color mapping with sophisticated fallback system
   * Maintains single source of truth while ensuring vibrant visuals
   */
  async getEnhancedColorTheme(contentType: string, category: string = 'main', fallbackIndex: number = 0): Promise<string> {
    try {
      // Try to get specific color mapping for content type and category
      const colorMapping = await this.getColorMapping(contentType, category);

      if (colorMapping) {
        // Convert database colors to CSS gradient theme
        const primary = colorMapping.color_primary;
        const secondary = colorMapping.color_secondary;
        const accent = colorMapping.color_accent;

        return `bg-gradient-to-br from-[${primary}]/20 to-[${secondary}]/25 border-[${accent}]/30 shadow-lg shadow-[${primary}]/10`;
      }

      // Fallback to main category if specific category not found
      if (category !== 'main') {
        const mainMapping = await this.getColorMapping(contentType, 'main');
        if (mainMapping) {
          const primary = mainMapping.color_primary;
          const secondary = mainMapping.color_secondary;
          const accent = mainMapping.color_accent;

          return `bg-gradient-to-br from-[${primary}]/20 to-[${secondary}]/25 border-[${accent}]/30 shadow-lg shadow-[${primary}]/10`;
        }
      }
    } catch (error) {
      console.warn(`Failed to get enhanced color theme for ${contentType}.${category}:`, error);
    }

    // Final fallback: vibrant theme rotation
    return this.getVibrantFallbackTheme(fallbackIndex);
  }
}

// Export singleton instance
export const enhancedColorMappingService = new EnhancedColorMappingService();

/**
 * Test-specific mock for core-client.ts
 * This file replaces import.meta.env with process.env for Jest compatibility
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '../../types/supabase'

// Environment variables with fallbacks for TypeScript compatibility
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://test.supabase.co'
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY || 'test-anon-key'

// Validate environment variables
if (!supabaseUrl || !supabaseAnonKey) {
  const errorMessage = 'Missing Supabase environment variables. Ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your .env file.'
  console.error(errorMessage)
  if (process.env.NODE_ENV === 'development') {
    throw new Error(errorMessage)
  }
}

// Log environment information in development
if (process.env.NODE_ENV === 'development') {
  console.log('🚀 Initializing Festival Family Supabase Client (TEST MODE)')
  console.log('📍 Environment:', process.env.NODE_ENV || 'test')
  console.log('🔗 Supabase URL:', supabaseUrl)
}

/**
 * Default client configuration
 * This configuration is optimized for browser environments
 */
const defaultClientOptions = {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce' as const,
    storage: typeof window !== 'undefined' ? window.localStorage : undefined,
  },
  global: {
    headers: {
      'X-Client-Info': 'festival-family-app-test'
    }
  },
  // Enable debug mode in development
  debug: process.env.NODE_ENV === 'development'
}

/**
 * Create the core Supabase client
 * This is the primary client that should be used throughout the application
 */
export const supabase = createClient<Database>(
  supabaseUrl,
  supabaseAnonKey,
  defaultClientOptions
)

/**
 * Create a minimal client with reduced configuration
 *
 * @deprecated This function is deprecated and should only be used for debugging purposes.
 * Use the main `supabase` client instead for all production code.
 *
 * @returns {SupabaseClient<Database>} Minimal Supabase client instance
 */
export const createMinimalClient = (): SupabaseClient<Database> => {
  if (process.env.NODE_ENV === 'development') {
    console.warn('⚠️  DEPRECATED: createMinimalClient() is deprecated. Use the main supabase client instead.')
    console.log('🔧 Creating minimal Supabase client for debugging...')
  }

  return createClient<Database>(
    supabaseUrl,
    supabaseAnonKey,
    {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
      },
      global: {
        headers: {
          'X-Client-Info': 'festival-family-minimal-debug-test'
        }
      }
    }
  )
}

/**
 * Create an ultra-minimal client with no configuration
 *
 * @deprecated This function is deprecated and should only be used for debugging purposes.
 * Use the main `supabase` client instead for all production code.
 *
 * @returns {SupabaseClient<Database>} Ultra-minimal Supabase client instance
 */
export const createUltraMinimalClient = (): SupabaseClient<Database> => {
  if (process.env.NODE_ENV === 'development') {
    console.warn('⚠️  DEPRECATED: createUltraMinimalClient() is deprecated. Use the main supabase client instead.')
    console.log('⚡ Creating ultra-minimal Supabase client for debugging...')
  }

  return createClient<Database>(
    supabaseUrl,
    supabaseAnonKey,
    {
      global: {
        headers: {
          'X-Client-Info': 'festival-family-ultra-minimal-debug-test'
        }
      }
    }
  )
}

/**
 * Test the Supabase connection
 * This is the primary connection testing function used throughout the app
 *
 * @returns {Promise<{success: boolean, message: string, data?: any, error?: any}>} Connection test results
 */
export const testConnection = async (): Promise<{
  success: boolean
  message: string
  data?: any
  error?: any
}> => {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Testing Supabase connection...')
    }

    // Validate environment variables first
    if (!supabaseUrl || !supabaseAnonKey) {
      return {
        success: false,
        message: 'Missing Supabase configuration. Check your .env file.',
        error: new Error('Missing environment variables')
      }
    }

    // Try a simple database query
    const { data, error } = await supabase.from('profiles').select('id').limit(1)

    if (error) {
      console.error('Connection test error:', error)
      return {
        success: false,
        message: `Database connection error: ${error.message}`,
        error
      }
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('✅ Supabase connection successful')
    }

    return {
      success: true,
      message: 'Connection successful',
      data
    }
  } catch (error) {
    console.error('Unexpected error during connection test:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
      error
    }
  }
}

/**
 * Check Supabase connection (alias for testConnection for backward compatibility)
 *
 * @returns {Promise<{success: boolean, message: string, data?: any}>} Connection status
 */
export const checkSupabaseConnection = async (): Promise<{
  success: boolean
  message: string
  data?: any
}> => {
  const result = await testConnection()
  return {
    success: result.success,
    message: result.message,
    data: result.data
  }
}

/**
 * Handle Supabase authentication errors
 * Provides user-friendly error messages for common Supabase auth errors
 *
 * @param {any} error - The error object from Supabase
 * @returns {string} User-friendly error message
 */
export const handleAuthError = (error: any): string => {
  if (!error) return 'Unknown error'

  const errorMessage = error.message ?? error.toString()

  if (process.env.NODE_ENV === 'development') {
    console.error('🚨 Auth error details:', error)
  }

  // Network and connectivity errors
  if (errorMessage.toLowerCase().includes('network') ||
      errorMessage.toLowerCase().includes('fetch') ||
      errorMessage.toLowerCase().includes('connection')) {
    return 'Network error. Please check your internet connection and try again.'
  }

  // Session and JWT errors
  if (errorMessage.toLowerCase().includes('jwt') ||
      errorMessage.toLowerCase().includes('session') ||
      errorMessage.toLowerCase().includes('token')) {
    return 'Your session has expired. Please log in again.'
  }

  // Credential errors
  if (errorMessage.toLowerCase().includes('credentials') ||
      errorMessage.toLowerCase().includes('password') ||
      errorMessage.toLowerCase().includes('email') ||
      errorMessage.toLowerCase().includes('invalid')) {
    return 'Invalid email or password. Please check your credentials and try again.'
  }

  // Host validation and API key errors
  if (errorMessage.toLowerCase().includes('host') ||
      errorMessage.toLowerCase().includes('origin') ||
      errorMessage.toLowerCase().includes('api key') ||
      errorMessage.toLowerCase().includes('unauthorized')) {
    return 'Authentication service error. Please try again later.'
  }

  // Permission errors
  if (errorMessage.toLowerCase().includes('permission') ||
      errorMessage.toLowerCase().includes('forbidden') ||
      errorMessage.toLowerCase().includes('403')) {
    return 'Access denied. You do not have permission to perform this action.'
  }

  // Rate limiting
  if (errorMessage.toLowerCase().includes('rate') ||
      errorMessage.toLowerCase().includes('limit') ||
      errorMessage.toLowerCase().includes('429')) {
    return 'Too many requests. Please wait a moment and try again.'
  }

  // Default error message
  return 'Authentication error. Please try again later.'
}

/**
 * Get the current session
 * @returns Promise with session data or error
 */
export const getSession = async () => {
  try {
    const { data, error } = await supabase.auth.getSession()

    if (error) {
      console.error('Get session error:', error)
      return { data: null, error }
    }

    return { data, error: null }
  } catch (error) {
    console.error('Unexpected error getting session:', error)
    return {
      data: null,
      error: error instanceof Error ? error : new Error('Unknown error getting session')
    }
  }
}

// ============================================================================
// TYPE EXPORTS
// ============================================================================

/**
 * Re-export important types for easy access
 */
export type { SupabaseClient } from '@supabase/supabase-js'
export type { Database } from '../../types/supabase'

/**
 * Custom type for our configured Supabase client
 */
export type FestivalFamilySupabaseClient = typeof supabase

// ============================================================================
// DEFAULT EXPORTS
// ============================================================================

/**
 * Export the default client as the primary export
 * This is the main client that should be used throughout the application
 */
export default supabase

/**
 * Named export for consistency
 */
export { supabase as client }

#!/usr/bin/env node

/**
 * Critical Fixes Verification Script
 * Tests the mobile navigation and authentication state fixes
 */

import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = './evidence/critical-fixes';

// Ensure evidence directory exists
if (!fs.existsSync(EVIDENCE_DIR)) {
  fs.mkdirSync(EVIDENCE_DIR, { recursive: true });
}

async function testCriticalFixes() {
  console.log('🚀 CRITICAL FIXES VERIFICATION');
  console.log('==============================');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    // Test 1: Mobile Navigation Touch Interface
    console.log('\n📱 Testing Mobile Navigation Touch Interface...');
    const mobilePage = await browser.newPage();
    await mobilePage.setViewportSize({ width: 375, height: 667 });
    await mobilePage.goto(APP_URL, { waitUntil: 'domcontentloaded', timeout: 15000 });
    await mobilePage.waitForTimeout(3000);
    
    // Capture mobile navigation
    await mobilePage.screenshot({ 
      path: `${EVIDENCE_DIR}/01-mobile-navigation-enhanced.png`,
      fullPage: true 
    });
    
    // Test touch navigation
    const mobileNavButtons = await mobilePage.$$('nav button');
    console.log(`📱 Found ${mobileNavButtons.length} mobile navigation buttons`);
    
    if (mobileNavButtons.length > 0) {
      console.log('🔄 Testing mobile navigation touch...');
      await mobileNavButtons[0].tap();
      await mobilePage.waitForTimeout(1000);
      
      await mobilePage.screenshot({ 
        path: `${EVIDENCE_DIR}/02-mobile-navigation-after-touch.png`,
        fullPage: true 
      });
      
      console.log('✅ Mobile navigation touch test completed');
    }
    
    // Test 2: Authentication State Display
    console.log('\n🔐 Testing Authentication State Display...');
    const desktopPage = await browser.newPage();
    await desktopPage.setViewportSize({ width: 1920, height: 1080 });
    await desktopPage.goto(APP_URL, { waitUntil: 'domcontentloaded', timeout: 15000 });
    await desktopPage.waitForTimeout(3000);
    
    // Capture desktop authentication state
    await desktopPage.screenshot({ 
      path: `${EVIDENCE_DIR}/03-desktop-auth-state.png`,
      fullPage: true 
    });
    
    // Check for authentication elements
    const authElements = await desktopPage.$$('[class*="auth"], [class*="sign"], button:has-text("Sign")');
    console.log(`🔐 Found ${authElements.length} authentication elements`);
    
    // Test 3: Toast Positioning
    console.log('\n🍞 Testing Toast Positioning...');
    
    // Trigger a toast (if possible)
    try {
      await desktopPage.evaluate(() => {
        // Try to trigger a toast notification
        if (window.toast) {
          window.toast.success('Test toast for positioning');
        }
      });
      
      await desktopPage.waitForTimeout(2000);
      
      await desktopPage.screenshot({ 
        path: `${EVIDENCE_DIR}/04-toast-positioning.png`,
        fullPage: true 
      });
      
      console.log('✅ Toast positioning test completed');
    } catch (error) {
      console.log('⚠️ Toast test skipped (no toast system available)');
    }
    
    // Test 4: Console Logs for Debug Information
    console.log('\n🐛 Checking Debug Console Logs...');
    
    const consoleLogs = [];
    desktopPage.on('console', msg => {
      if (msg.text().includes('Auth State') || msg.text().includes('navigation')) {
        consoleLogs.push(msg.text());
      }
    });
    
    // Reload to capture auth state logs
    await desktopPage.reload({ waitUntil: 'domcontentloaded' });
    await desktopPage.waitForTimeout(3000);
    
    console.log('📋 Debug logs captured:');
    consoleLogs.forEach(log => console.log(`  - ${log}`));
    
    // Generate test report
    const report = {
      timestamp: new Date().toISOString(),
      tests: {
        mobileNavigation: {
          buttonsFound: mobileNavButtons.length,
          touchTestCompleted: mobileNavButtons.length > 0,
          status: mobileNavButtons.length > 0 ? 'PASS' : 'FAIL'
        },
        authenticationState: {
          elementsFound: authElements.length,
          status: authElements.length > 0 ? 'PASS' : 'FAIL'
        },
        debugLogs: {
          captured: consoleLogs.length,
          logs: consoleLogs,
          status: consoleLogs.length > 0 ? 'PASS' : 'PARTIAL'
        }
      },
      screenshots: [
        '01-mobile-navigation-enhanced.png',
        '02-mobile-navigation-after-touch.png',
        '03-desktop-auth-state.png',
        '04-toast-positioning.png'
      ]
    };
    
    // Save report
    fs.writeFileSync(
      `${EVIDENCE_DIR}/critical-fixes-report.json`,
      JSON.stringify(report, null, 2)
    );
    
    console.log('\n📊 CRITICAL FIXES TEST SUMMARY:');
    console.log('================================');
    console.log(`📱 Mobile Navigation: ${report.tests.mobileNavigation.status}`);
    console.log(`🔐 Authentication State: ${report.tests.authenticationState.status}`);
    console.log(`🐛 Debug Logs: ${report.tests.debugLogs.status}`);
    console.log(`📸 Screenshots: ${report.screenshots.length} captured`);
    console.log(`📄 Report saved: ${EVIDENCE_DIR}/critical-fixes-report.json`);
    
    const overallStatus = Object.values(report.tests).every(test => test.status === 'PASS') ? 'PASS' : 'PARTIAL';
    console.log(`\n🎯 OVERALL STATUS: ${overallStatus}`);
    
    if (overallStatus === 'PASS') {
      console.log('✅ All critical fixes are working correctly!');
    } else {
      console.log('⚠️ Some fixes need additional work. Check the evidence and logs.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
  }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
  testCriticalFixes().catch(console.error);
}

export { testCriticalFixes };

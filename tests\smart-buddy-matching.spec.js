/**
 * Smart Buddy Matching Feature Tests
 * 
 * Comprehensive testing for the AI-powered buddy matching system
 * including component rendering, functionality, and performance.
 * 
 * @module SmartBuddyMatchingTests
 * @version 1.0.0
 */

import { test, expect } from '@playwright/test';

test.describe('Smart Buddy Matching Feature', () => {
  
  test.beforeEach(async ({ page }) => {
    // Navigate to FamHub and wait for it to load
    await page.goto('/famhub');
    await page.waitForLoadState('networkidle');
  });

  test.describe('Feature Integration', () => {
    
    test('should display Smart Matching tab in FamHub', async ({ page }) => {
      // Check if Smart Matching tab is present
      const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
      await expect(smartMatchingTab).toBeVisible();
      
      // Verify tab has the correct icon
      const heartIcon = smartMatchingTab.locator('svg');
      await expect(heartIcon).toBeVisible();
    });

    test('should navigate to Smart Matching section when tab is clicked', async ({ page }) => {
      // Click on Smart Matching tab
      const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
      await smartMatchingTab.click();
      
      // Wait for content to load
      await page.waitForTimeout(1000);
      
      // Check if Smart Buddy Matching component is displayed
      const buddyMatchingComponent = page.locator('text=Smart Buddy Matching');
      await expect(buddyMatchingComponent).toBeVisible();
      
      // Check for AI-Powered badge
      const aiPoweredBadge = page.locator('text=AI-Powered');
      await expect(aiPoweredBadge).toBeVisible();
    });
  });

  test.describe('Component Functionality', () => {
    
    test.beforeEach(async ({ page }) => {
      // Navigate to Smart Matching tab
      const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
      await smartMatchingTab.click();
      await page.waitForTimeout(1000);
    });

    test('should display filters button and functionality', async ({ page }) => {
      // Check if Filters button is present
      const filtersButton = page.locator('button:has-text("Filters")');
      await expect(filtersButton).toBeVisible();
      
      // Click filters button to open filters panel
      await filtersButton.click();
      
      // Check if filters panel is displayed
      const filtersPanel = page.locator('text=Matching Preferences');
      await expect(filtersPanel).toBeVisible();
      
      // Check for distance slider
      const distanceSlider = page.locator('text=Maximum Distance');
      await expect(distanceSlider).toBeVisible();
      
      // Check for compatibility sliders
      const musicCompatibility = page.locator('text=Min Music Compatibility');
      await expect(musicCompatibility).toBeVisible();
      
      const activityCompatibility = page.locator('text=Min Activity Compatibility');
      await expect(activityCompatibility).toBeVisible();
    });

    test('should display Find Matches button', async ({ page }) => {
      // Check if Find Matches button is present
      const findMatchesButton = page.locator('button:has-text("Find Matches")');
      await expect(findMatchesButton).toBeVisible();
      
      // Button should have search icon
      const searchIcon = findMatchesButton.locator('text=🔍');
      await expect(searchIcon).toBeVisible();
    });

    test('should handle authentication state correctly', async ({ page }) => {
      // If user is not authenticated, should show sign-in message
      const signInMessage = page.locator('text=Sign In Required');
      const buddyMatchingContent = page.locator('text=Find your perfect festival companions');
      
      // Either sign-in message or buddy matching content should be visible
      const hasSignIn = await signInMessage.isVisible();
      const hasContent = await buddyMatchingContent.isVisible();
      
      expect(hasSignIn || hasContent).toBe(true);
      
      if (hasSignIn) {
        // Check sign-in message content
        const signInDescription = page.locator('text=Please sign in to find your festival buddies!');
        await expect(signInDescription).toBeVisible();
      }
    });
  });

  test.describe('Standardized Components Usage', () => {
    
    test.beforeEach(async ({ page }) => {
      // Navigate to Smart Matching tab
      const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
      await smartMatchingTab.click();
      await page.waitForTimeout(1000);
    });

    test('should use BentoCard components consistently', async ({ page }) => {
      // Check for BentoCard usage in the component
      const bentoCards = page.locator('[class*="bento"], [class*="card"]');
      const cardCount = await bentoCards.count();
      
      expect(cardCount).toBeGreaterThan(0);
      
      // Verify cards have proper styling
      if (cardCount > 0) {
        const firstCard = bentoCards.first();
        const cardClasses = await firstCard.getAttribute('class');
        expect(cardClasses).toMatch(/card|bento|rounded|shadow/i);
      }
    });

    test('should use EnhancedUnifiedBadge for AI-Powered indicator', async ({ page }) => {
      // Check for AI-Powered badge
      const aiPoweredBadge = page.locator('text=AI-Powered');
      
      if (await aiPoweredBadge.isVisible()) {
        // Verify badge styling
        const badgeElement = aiPoweredBadge.locator('..');
        const badgeClasses = await badgeElement.getAttribute('class');
        expect(badgeClasses).toMatch(/badge|unified|enhanced/i);
      }
    });

    test('should use UnifiedInteractionButton for user actions', async ({ page }) => {
      // Look for interaction buttons (these would appear when matches are found)
      const interactionButtons = page.locator('[data-testid*="unified-interaction"], button[class*="unified-interaction"]');
      
      // Note: These might not be visible if no matches are found or user is not authenticated
      // This test validates the component structure when buttons are present
      const buttonCount = await interactionButtons.count();
      
      if (buttonCount > 0) {
        // Verify buttons use unified styling
        for (let i = 0; i < Math.min(buttonCount, 3); i++) {
          const button = interactionButtons.nth(i);
          const classes = await button.getAttribute('class');
          expect(classes).toMatch(/unified|interaction|button/i);
        }
      }
    });
  });

  test.describe('Performance Validation', () => {
    
    test('should load Smart Matching tab quickly', async ({ page }) => {
      const startTime = performance.now();
      
      // Click Smart Matching tab
      const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
      await smartMatchingTab.click();
      
      // Wait for main content to appear
      await page.waitForSelector('text=Smart Buddy Matching', { timeout: 5000 });
      
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      console.log(`Smart Matching tab load time: ${Math.round(loadTime)}ms`);
      
      // Should load within 2 seconds
      expect(loadTime).toBeLessThan(2000);
    });

    test('should handle filter interactions smoothly', async ({ page }) => {
      // Navigate to Smart Matching
      const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
      await smartMatchingTab.click();
      await page.waitForTimeout(500);
      
      // Open filters
      const filtersButton = page.locator('button:has-text("Filters")');
      
      if (await filtersButton.isVisible()) {
        const startTime = performance.now();
        
        await filtersButton.click();
        
        // Wait for filters panel to appear
        await page.waitForSelector('text=Matching Preferences', { timeout: 3000 });
        
        const endTime = performance.now();
        const filterLoadTime = endTime - startTime;
        
        console.log(`Filters panel load time: ${Math.round(filterLoadTime)}ms`);
        
        // Should open filters quickly
        expect(filterLoadTime).toBeLessThan(1000);
      }
    });
  });

  test.describe('Responsive Design', () => {
    
    test('should work on mobile viewport', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Navigate to Smart Matching
      const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
      await smartMatchingTab.click();
      await page.waitForTimeout(1000);
      
      // Check if component is visible and properly sized
      const buddyMatchingComponent = page.locator('text=Smart Buddy Matching');
      await expect(buddyMatchingComponent).toBeVisible();
      
      // Check if filters button is accessible on mobile
      const filtersButton = page.locator('button:has-text("Filters")');
      await expect(filtersButton).toBeVisible();
      
      // Verify no horizontal scrolling
      const bodyWidth = await page.evaluate(() => document.body.scrollWidth);
      const viewportWidth = await page.evaluate(() => window.innerWidth);
      
      expect(bodyWidth).toBeLessThanOrEqual(viewportWidth + 10); // Allow small margin
    });

    test('should work on tablet viewport', async ({ page }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      
      // Navigate to Smart Matching
      const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
      await smartMatchingTab.click();
      await page.waitForTimeout(1000);
      
      // Check if component layout adapts to tablet size
      const buddyMatchingComponent = page.locator('text=Smart Buddy Matching');
      await expect(buddyMatchingComponent).toBeVisible();
      
      // Check if filters panel works on tablet
      const filtersButton = page.locator('button:has-text("Filters")');
      
      if (await filtersButton.isVisible()) {
        await filtersButton.click();
        
        const filtersPanel = page.locator('text=Matching Preferences');
        await expect(filtersPanel).toBeVisible();
      }
    });
  });

  test.describe('Error Handling', () => {
    
    test('should handle component loading gracefully', async ({ page }) => {
      // Navigate to Smart Matching
      const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
      await smartMatchingTab.click();
      
      // Wait for component to load or error state
      await page.waitForTimeout(2000);
      
      // Check that either content loads or error is handled gracefully
      const hasContent = await page.locator('text=Smart Buddy Matching').isVisible();
      const hasError = await page.locator('text=Error').isVisible();
      const hasSignIn = await page.locator('text=Sign In Required').isVisible();
      
      // One of these states should be present
      expect(hasContent || hasError || hasSignIn).toBe(true);
      
      // Should not have unhandled JavaScript errors
      const consoleErrors = [];
      page.on('console', msg => {
        if (msg.type() === 'error') {
          consoleErrors.push(msg.text());
        }
      });
      
      // Wait a bit more to catch any delayed errors
      await page.waitForTimeout(1000);
      
      // Filter out known development warnings
      const criticalErrors = consoleErrors.filter(error => 
        !error.includes('Warning:') && 
        !error.includes('DevTools') &&
        !error.includes('favicon')
      );
      
      expect(criticalErrors.length).toBe(0);
    });
  });
});

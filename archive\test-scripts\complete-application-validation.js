/**
 * Complete Application Validation
 * 
 * This script performs comprehensive validation of the entire Festival Family application
 * after all migrations and frontend implementations are complete.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Complete Application Validation');
console.log('==================================');

// Test 1: Backend Infrastructure Validation
async function testBackendInfrastructure() {
  console.log('🗄️ Test 1: Backend Infrastructure Validation');
  console.log('---------------------------------------------');
  
  const results = {
    coreTablesWorking: true,
    newFeaturesWorking: true,
    securityFunctionsWorking: true,
    rlsIssuesResolved: true,
    tableAccessibility: {},
    timestamp: new Date().toISOString()
  };
  
  // All tables to test
  const tablesToTest = [
    // Core tables
    { name: 'profiles', category: 'core' },
    { name: 'festivals', category: 'core' },
    { name: 'events', category: 'core' },
    { name: 'activities', category: 'core' },
    { name: 'groups', category: 'core' },
    
    // Group system tables
    { name: 'group_members', category: 'group_system' },
    { name: 'group_invitations', category: 'group_system' },
    { name: 'group_suggestions', category: 'smart_groups' },
    { name: 'group_suggestion_responses', category: 'smart_groups' },
    { name: 'group_activities', category: 'smart_groups' },
    
    // Chat system tables
    { name: 'chat_rooms', category: 'chat_system' },
    { name: 'chat_room_members', category: 'chat_system' },
    { name: 'chat_messages', category: 'chat_system' },
    
    // Activity coordination tables
    { name: 'activity_attendance', category: 'activity_coordination' },
    { name: 'artist_preferences', category: 'activity_coordination' }
  ];
  
  for (const table of tablesToTest) {
    try {
      const { data, error } = await supabase
        .from(table.name)
        .select('*')
        .limit(1);
      
      if (error) {
        results.tableAccessibility[table.name] = {
          accessible: false,
          error: error.message,
          category: table.category
        };
        
        if (error.message.includes('infinite recursion')) {
          results.rlsIssuesResolved = false;
        }
        
        if (table.category === 'core') {
          results.coreTablesWorking = false;
        } else {
          results.newFeaturesWorking = false;
        }
        
        console.log(`❌ ${table.name}: ${error.message}`);
      } else {
        results.tableAccessibility[table.name] = {
          accessible: true,
          category: table.category
        };
        console.log(`✅ ${table.name}: Accessible`);
      }
    } catch (err) {
      results.tableAccessibility[table.name] = {
        accessible: false,
        error: err.message,
        category: table.category
      };
      console.log(`💥 ${table.name}: ${err.message}`);
    }
  }
  
  return results;
}

// Test 2: Security Functions Validation
async function testSecurityFunctions() {
  console.log('\n🛡️ Test 2: Security Functions Validation');
  console.log('----------------------------------------');
  
  const results = {
    xssProtection: false,
    privilegeEscalationPrevention: false,
    isAdminFunction: false,
    allSecurityFunctionsWorking: false,
    timestamp: new Date().toISOString()
  };
  
  // Test XSS protection
  try {
    const { data, error } = await supabase
      .rpc('test_xss_protection', { test_input: '<script>alert("XSS")</script>Test' });
    
    if (error) {
      console.log(`❌ XSS Protection: ${error.message}`);
    } else {
      results.xssProtection = true;
      const wasSanitized = !data.includes('<script>');
      console.log(`✅ XSS Protection: ${wasSanitized ? 'WORKING' : 'VULNERABLE'} (${data})`);
    }
  } catch (err) {
    console.log(`💥 XSS Protection: ${err.message}`);
  }
  
  // Test privilege escalation prevention
  try {
    const { data, error } = await supabase
      .rpc('change_user_role', { 
        target_user_id: '00000000-0000-0000-0000-000000000000', 
        new_role: 'SUPER_ADMIN' 
      });
    
    if (error && error.message.includes('Only SUPER_ADMIN')) {
      results.privilegeEscalationPrevention = true;
      console.log(`✅ Privilege Escalation Prevention: WORKING (properly secured)`);
    } else {
      console.log(`⚠️ Privilege Escalation Prevention: ${error?.message || 'Unexpected result'}`);
    }
  } catch (err) {
    console.log(`💥 Privilege Escalation Prevention: ${err.message}`);
  }
  
  // Test is_admin function
  try {
    const { data, error } = await supabase.rpc('is_admin');
    
    if (error) {
      console.log(`❌ is_admin Function: ${error.message}`);
    } else {
      results.isAdminFunction = true;
      console.log(`✅ is_admin Function: WORKING (${data})`);
    }
  } catch (err) {
    console.log(`💥 is_admin Function: ${err.message}`);
  }
  
  results.allSecurityFunctionsWorking = 
    results.xssProtection && 
    results.privilegeEscalationPrevention && 
    results.isAdminFunction;
  
  return results;
}

// Test 3: Data Population Validation
async function testDataPopulation() {
  console.log('\n🎪 Test 3: Data Population Validation');
  console.log('------------------------------------');
  
  const results = {
    szigetFestivalExists: false,
    eventsPopulated: false,
    activitiesPopulated: false,
    dataQuality: 'unknown',
    counts: {},
    timestamp: new Date().toISOString()
  };
  
  // Check for Sziget Festival
  try {
    const { data: festival, error: festivalError } = await supabase
      .from('festivals')
      .select('*')
      .eq('name', 'Sziget Festival 2025')
      .single();
    
    if (festival && !festivalError) {
      results.szigetFestivalExists = true;
      console.log(`✅ Sziget Festival 2025: EXISTS (ID: ${festival.id})`);
      
      // Check events for this festival
      const { data: events, error: eventsError } = await supabase
        .from('events')
        .select('*')
        .eq('festival_id', festival.id);
      
      if (events && !eventsError) {
        results.eventsPopulated = events.length > 0;
        results.counts.events = events.length;
        console.log(`✅ Events: ${events.length} found`);
      }
      
      // Check activities for this festival
      const { data: activities, error: activitiesError } = await supabase
        .from('activities')
        .select('*')
        .eq('festival_id', festival.id);
      
      if (activities && !activitiesError) {
        results.activitiesPopulated = activities.length > 0;
        results.counts.activities = activities.length;
        console.log(`✅ Activities: ${activities.length} found`);
      }
      
      // Determine data quality
      if (results.eventsPopulated && results.activitiesPopulated) {
        results.dataQuality = 'excellent';
      } else if (results.eventsPopulated || results.activitiesPopulated) {
        results.dataQuality = 'partial';
      } else {
        results.dataQuality = 'minimal';
      }
      
    } else {
      console.log(`❌ Sziget Festival 2025: NOT FOUND`);
    }
  } catch (err) {
    console.log(`💥 Data Population Test: ${err.message}`);
  }
  
  return results;
}

// Test 4: Feature Integration Validation
async function testFeatureIntegration() {
  console.log('\n🔗 Test 4: Feature Integration Validation');
  console.log('----------------------------------------');
  
  const results = {
    groupSystemIntegration: 'unknown',
    smartGroupFormation: 'unknown',
    chatSystemIntegration: 'unknown',
    activityCoordination: 'unknown',
    overallIntegration: 'unknown',
    timestamp: new Date().toISOString()
  };
  
  // Test group system integration
  try {
    const { data: groups } = await supabase.from('groups').select('*').limit(1);
    const { data: groupMembers } = await supabase.from('group_members').select('*').limit(1);
    const { data: groupInvitations } = await supabase.from('group_invitations').select('*').limit(1);
    
    if (groups !== null && groupMembers !== null && groupInvitations !== null) {
      results.groupSystemIntegration = 'working';
      console.log(`✅ Group System Integration: WORKING`);
    } else {
      results.groupSystemIntegration = 'partial';
      console.log(`⚠️ Group System Integration: PARTIAL`);
    }
  } catch (err) {
    results.groupSystemIntegration = 'broken';
    console.log(`❌ Group System Integration: BROKEN (${err.message})`);
  }
  
  // Test smart group formation
  try {
    const { data: suggestions } = await supabase.from('group_suggestions').select('*').limit(1);
    const { data: responses } = await supabase.from('group_suggestion_responses').select('*').limit(1);
    
    if (suggestions !== null && responses !== null) {
      results.smartGroupFormation = 'working';
      console.log(`✅ Smart Group Formation: WORKING`);
    } else {
      results.smartGroupFormation = 'partial';
      console.log(`⚠️ Smart Group Formation: PARTIAL`);
    }
  } catch (err) {
    results.smartGroupFormation = 'broken';
    console.log(`❌ Smart Group Formation: BROKEN (${err.message})`);
  }
  
  // Test chat system integration
  try {
    const { data: chatRooms } = await supabase.from('chat_rooms').select('*').limit(1);
    const { data: chatMembers } = await supabase.from('chat_room_members').select('*').limit(1);
    const { data: chatMessages } = await supabase.from('chat_messages').select('*').limit(1);
    
    if (chatRooms !== null && chatMembers !== null && chatMessages !== null) {
      results.chatSystemIntegration = 'working';
      console.log(`✅ Chat System Integration: WORKING`);
    } else {
      results.chatSystemIntegration = 'partial';
      console.log(`⚠️ Chat System Integration: PARTIAL`);
    }
  } catch (err) {
    results.chatSystemIntegration = 'broken';
    console.log(`❌ Chat System Integration: BROKEN (${err.message})`);
  }
  
  // Test activity coordination
  try {
    const { data: attendance } = await supabase.from('activity_attendance').select('*').limit(1);
    const { data: preferences } = await supabase.from('artist_preferences').select('*').limit(1);
    
    if (attendance !== null && preferences !== null) {
      results.activityCoordination = 'working';
      console.log(`✅ Activity Coordination: WORKING`);
    } else {
      results.activityCoordination = 'partial';
      console.log(`⚠️ Activity Coordination: PARTIAL`);
    }
  } catch (err) {
    results.activityCoordination = 'broken';
    console.log(`❌ Activity Coordination: BROKEN (${err.message})`);
  }
  
  // Determine overall integration status
  const workingFeatures = Object.values(results).filter(status => status === 'working').length;
  const totalFeatures = 4; // group system, smart groups, chat, activity coordination
  
  if (workingFeatures === totalFeatures) {
    results.overallIntegration = 'excellent';
  } else if (workingFeatures >= totalFeatures * 0.75) {
    results.overallIntegration = 'good';
  } else if (workingFeatures >= totalFeatures * 0.5) {
    results.overallIntegration = 'partial';
  } else {
    results.overallIntegration = 'poor';
  }
  
  return results;
}

// Main execution
async function main() {
  try {
    console.log(`🕐 Started at: ${new Date().toISOString()}\n`);
    
    // Run all validation tests
    const backendResults = await testBackendInfrastructure();
    const securityResults = await testSecurityFunctions();
    const dataResults = await testDataPopulation();
    const integrationResults = await testFeatureIntegration();
    
    // Compile comprehensive results
    const validationResults = {
      testSuite: 'Complete Application Validation',
      timestamp: new Date().toISOString(),
      backendInfrastructure: backendResults,
      securityFunctions: securityResults,
      dataPopulation: dataResults,
      featureIntegration: integrationResults,
      overallAssessment: {
        productionReady: false,
        securityImplemented: false,
        dataPopulated: false,
        featuresIntegrated: false,
        recommendedActions: []
      }
    };
    
    // Calculate overall assessment
    validationResults.overallAssessment.securityImplemented = securityResults.allSecurityFunctionsWorking;
    validationResults.overallAssessment.dataPopulated = dataResults.szigetFestivalExists && dataResults.dataQuality !== 'minimal';
    validationResults.overallAssessment.featuresIntegrated = integrationResults.overallIntegration === 'excellent' || integrationResults.overallIntegration === 'good';
    
    validationResults.overallAssessment.productionReady = 
      backendResults.coreTablesWorking &&
      validationResults.overallAssessment.securityImplemented &&
      backendResults.rlsIssuesResolved;
    
    // Generate recommendations
    if (!backendResults.rlsIssuesResolved) {
      validationResults.overallAssessment.recommendedActions.push('Apply RLS recursion fix migration');
    }
    if (!validationResults.overallAssessment.securityImplemented) {
      validationResults.overallAssessment.recommendedActions.push('Complete security function implementation');
    }
    if (!validationResults.overallAssessment.dataPopulated) {
      validationResults.overallAssessment.recommendedActions.push('Run Sziget Festival data population script');
    }
    if (!validationResults.overallAssessment.featuresIntegrated) {
      validationResults.overallAssessment.recommendedActions.push('Complete frontend-backend integration');
    }
    
    // Save results
    const resultsDir = 'complete-application-validation-results';
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      `${resultsDir}/complete-validation-${Date.now()}.json`,
      JSON.stringify(validationResults, null, 2)
    );
    
    console.log('\n🎉 COMPLETE APPLICATION VALIDATION SUMMARY');
    console.log('==========================================');
    
    console.log('\n🗄️ BACKEND INFRASTRUCTURE:');
    console.log(`✅ Core Tables: ${backendResults.coreTablesWorking ? 'WORKING' : 'ISSUES'}`);
    console.log(`✅ New Features: ${backendResults.newFeaturesWorking ? 'WORKING' : 'ISSUES'}`);
    console.log(`✅ RLS Issues: ${backendResults.rlsIssuesResolved ? 'RESOLVED' : 'UNRESOLVED'}`);
    
    console.log('\n🛡️ SECURITY FUNCTIONS:');
    console.log(`✅ XSS Protection: ${securityResults.xssProtection ? 'WORKING' : 'ISSUES'}`);
    console.log(`✅ Privilege Escalation: ${securityResults.privilegeEscalationPrevention ? 'PREVENTED' : 'VULNERABLE'}`);
    console.log(`✅ Admin Functions: ${securityResults.isAdminFunction ? 'WORKING' : 'ISSUES'}`);
    
    console.log('\n🎪 DATA POPULATION:');
    console.log(`✅ Sziget Festival: ${dataResults.szigetFestivalExists ? 'EXISTS' : 'MISSING'}`);
    console.log(`✅ Events: ${dataResults.eventsPopulated ? `${dataResults.counts.events || 0} FOUND` : 'MISSING'}`);
    console.log(`✅ Activities: ${dataResults.activitiesPopulated ? `${dataResults.counts.activities || 0} FOUND` : 'MISSING'}`);
    console.log(`✅ Data Quality: ${dataResults.dataQuality.toUpperCase()}`);
    
    console.log('\n🔗 FEATURE INTEGRATION:');
    console.log(`✅ Group System: ${integrationResults.groupSystemIntegration.toUpperCase()}`);
    console.log(`✅ Smart Groups: ${integrationResults.smartGroupFormation.toUpperCase()}`);
    console.log(`✅ Chat System: ${integrationResults.chatSystemIntegration.toUpperCase()}`);
    console.log(`✅ Activity Coordination: ${integrationResults.activityCoordination.toUpperCase()}`);
    
    console.log('\n🚀 PRODUCTION READINESS:');
    console.log(`🎯 Overall Status: ${validationResults.overallAssessment.productionReady ? '✅ PRODUCTION READY' : '⚠️ NEEDS ATTENTION'}`);
    console.log(`🛡️ Security: ${validationResults.overallAssessment.securityImplemented ? 'IMPLEMENTED' : 'INCOMPLETE'}`);
    console.log(`📊 Data: ${validationResults.overallAssessment.dataPopulated ? 'POPULATED' : 'NEEDS POPULATION'}`);
    console.log(`🔗 Features: ${validationResults.overallAssessment.featuresIntegrated ? 'INTEGRATED' : 'NEEDS INTEGRATION'}`);
    
    if (validationResults.overallAssessment.recommendedActions.length > 0) {
      console.log('\n🔧 RECOMMENDED ACTIONS:');
      validationResults.overallAssessment.recommendedActions.forEach((action, index) => {
        console.log(`   ${index + 1}. ${action}`);
      });
    }
    
    console.log(`\n📁 Results saved to: ${resultsDir}/complete-validation-${Date.now()}.json`);
    
  } catch (error) {
    console.error('💥 Complete validation failed:', error);
  }
  
  process.exit(0);
}

main();

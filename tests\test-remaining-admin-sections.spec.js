/**
 * Test Remaining Admin Sections: Tips, Guides, FAQs
 * 
 * Tests the remaining admin sections we haven't fully validated
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

async function testAdminSection(page, sectionName, sectionUrl) {
  console.log(`\n🧪 Testing ${sectionName} section...`);
  
  await page.goto(sectionUrl);
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  
  // Check if section loads
  const pageLoaded = !page.url().includes('404') && !page.url().includes('error');
  const hasTitle = await page.locator('h1, h2').count() > 0;
  const hasCreateButton = await page.locator('button:has-text("Create"), button:has-text("New"), button:has-text("Add")').count() > 0;
  const hasListItems = await page.locator('table, .item, [data-testid*="item"], tbody tr').count() > 0;
  const hasForm = await page.locator('form, input, textarea').count() > 0;
  
  console.log(`${sectionName} assessment:`);
  console.log(`  Page loaded: ${pageLoaded}`);
  console.log(`  Has title: ${hasTitle}`);
  console.log(`  Has create button: ${hasCreateButton}`);
  console.log(`  Has list items: ${hasListItems}`);
  console.log(`  Has form elements: ${hasForm}`);
  
  await page.screenshot({ path: `test-results/${sectionName.toLowerCase()}-section.png`, fullPage: true });
  
  // Test create functionality if available
  if (hasCreateButton) {
    console.log(`🔗 Testing ${sectionName} create functionality...`);
    
    await page.click('button:has-text("Create"), button:has-text("New"), button:has-text("Add")');
    await page.waitForTimeout(2000);
    
    const hasCreateForm = await page.locator('form').count() > 0;
    const hasTitleField = await page.locator('input[name*="title"], input[placeholder*="title"]').count() > 0;
    const hasContentField = await page.locator('textarea, input[name*="content"]').count() > 0;
    
    console.log(`  Create form: ${hasCreateForm}`);
    console.log(`  Title field: ${hasTitleField}`);
    console.log(`  Content field: ${hasContentField}`);
    
    await page.screenshot({ path: `test-results/${sectionName.toLowerCase()}-create-form.png`, fullPage: true });
  }
  
  return {
    sectionName,
    pageLoaded,
    hasTitle,
    hasCreateButton,
    hasListItems,
    hasForm,
    functional: pageLoaded && (hasCreateButton || hasListItems || hasForm)
  };
}

test('Test Remaining Admin Sections', async ({ page }) => {
  console.log('🧪 Testing remaining admin sections...');
  
  const loginSuccess = await loginAsAdmin(page);
  console.log(`✅ Login success: ${loginSuccess}`);
  
  if (loginSuccess) {
    const results = [];
    
    // Test Tips section
    results.push(await testAdminSection(page, 'Tips', '/admin/tips'));
    
    // Test Guides section
    results.push(await testAdminSection(page, 'Guides', '/admin/guides'));
    
    // Test FAQs section
    results.push(await testAdminSection(page, 'FAQs', '/admin/faqs'));
    
    // Test External Links section
    results.push(await testAdminSection(page, 'External Links', '/admin/external-links'));
    
    // Test Emergency Management section
    results.push(await testAdminSection(page, 'Emergency', '/admin/emergency'));
    
    console.log('\n📊 SUMMARY OF ADMIN SECTIONS:');
    console.log('=====================================');
    
    results.forEach(result => {
      const status = result.functional ? '✅ WORKING' : '❌ NEEDS WORK';
      console.log(`${result.sectionName}: ${status}`);
      console.log(`  Page loads: ${result.pageLoaded}`);
      console.log(`  Has interface: ${result.hasTitle || result.hasForm}`);
      console.log(`  Has functionality: ${result.hasCreateButton || result.hasListItems}`);
    });
    
    const workingSections = results.filter(r => r.functional).length;
    const totalSections = results.length;
    
    console.log(`\n🎯 OVERALL ADMIN COVERAGE: ${workingSections}/${totalSections} sections working`);
    
    console.log('✅ Remaining admin sections test completed');
  }
});

/**
 * Global Teardown for Evidence-Based Testing
 * 
 * This script runs after all tests to generate comprehensive reports
 * and summarize the evidence collected during testing.
 */

import { promises as fs } from 'fs';
import path from 'path';

async function globalTeardown() {
  console.log('🏁 Starting Evidence-Based Testing Teardown');
  console.log('===========================================');
  
  try {
    // Read session metadata
    const sessionData = JSON.parse(
      await fs.readFile('test-evidence/session-metadata.json', 'utf8')
    );
    
    // Collect all evidence files
    const evidenceFiles = await fs.readdir('test-evidence');
    const evidenceCount = evidenceFiles.filter(file => 
      file.endsWith('.png') || file.endsWith('.json')
    ).length;
    
    // Generate final report
    const finalReport = {
      sessionId: sessionData.sessionId,
      startTime: sessionData.startTime,
      endTime: new Date().toISOString(),
      duration: Date.now() - new Date(sessionData.startTime).getTime(),
      evidenceCollected: {
        totalFiles: evidenceCount,
        screenshots: evidenceFiles.filter(f => f.endsWith('.png')).length,
        dataFiles: evidenceFiles.filter(f => f.endsWith('.json')).length,
        files: evidenceFiles
      },
      testingSummary: {
        applicationTested: sessionData.applicationUrl,
        testObjectives: sessionData.testObjectives,
        evidenceDirectory: 'test-evidence',
        reportDirectory: 'test-results'
      }
    };
    
    await fs.writeFile(
      'test-evidence/final-report.json',
      JSON.stringify(finalReport, null, 2)
    );
    
    // Generate human-readable summary
    const summary = `
# Evidence-Based Testing Session Summary

**Session ID**: ${finalReport.sessionId}
**Duration**: ${Math.round(finalReport.duration / 1000)}s
**Evidence Collected**: ${finalReport.evidenceCollected.totalFiles} files
- Screenshots: ${finalReport.evidenceCollected.screenshots}
- Data Files: ${finalReport.evidenceCollected.dataFiles}

## Test Objectives Completed
${sessionData.testObjectives.map(obj => `- ${obj}`).join('\n')}

## Evidence Files
${evidenceFiles.map(file => `- ${file}`).join('\n')}

**Report Generated**: ${finalReport.endTime}
`;
    
    await fs.writeFile('test-evidence/TESTING_SUMMARY.md', summary);
    
    console.log(`📊 Evidence collected: ${evidenceCount} files`);
    console.log(`📸 Screenshots: ${finalReport.evidenceCollected.screenshots}`);
    console.log(`📄 Data files: ${finalReport.evidenceCollected.dataFiles}`);
    console.log(`⏱️ Session duration: ${Math.round(finalReport.duration / 1000)}s`);
    console.log('✅ Global teardown completed successfully');
    
  } catch (error) {
    console.error('❌ Error during teardown:', error.message);
  }
}

export default globalTeardown;

# Festival Family

<p align="center">
  <img src="public/logo.png" alt="Festival Family Logo" width="200" />
</p>

<p align="center">
  <strong>Connect. Discover. Experience.</strong>
</p>

<p align="center">
  <a href="#features">Features</a> •
  <a href="#getting-started">Getting Started</a> •
  <a href="#production-status">Production Status</a> •
  <a href="#documentation">Documentation</a> •
  <a href="#architecture">Architecture</a> •
  <a href="#contributing">Contributing</a> •
  <a href="#license">License</a>
</p>

Festival Family is a community-driven platform designed to connect solo festival-goers, helping them find their "festival family" and build lasting connections. Our mission is to ensure that no one experiences festivals alone.

## 🎯 Core Features

### **Festival Activities System**
Community-organized meetups for solo festival-goers including:
- **Photo meetups** and location-based gatherings
- **Daily recurring activities** (yoga, meditation sessions)
- **Festival-specific events** and community challenges
- **Real-time participation** tracking and admin management

### **FamHub Vault Structure**
- **Chats**: External platform links (WhatsApp, Discord, Telegram)
- **Communities**: Facebook groups and social platform connections
- **Resources**: Generic and location-specific information (transportation, local guides, festival tips)

### **Discover Section**
- **Upcoming festivals and events** (both festival-organized and community events)
- **Community reunions** and off-site gatherings
- **Main festival acts** and performance schedules
- **Real-time event discovery** and participation

### **Additional Core Features**
- **Emergency help system** for festival safety
- **Map functionality** with admin-shared community gathering locations
- **Authenticated dashboard** as effective HUD showing upcoming events, announcements, weather warnings, and personalized content

### **Icon/Emoji Management System**
- **Admin-controlled visual design**: Comprehensive management interface for icons, emojis, and colors
- **Database-driven icon visibility**: Toggle icon display per content type and category
- **Real-time admin-to-user pipeline**: Changes immediately reflected across the application
- **Clean, text-focused design**: Professional appearance with optimized text hierarchy
- **Single source of truth**: Centralized color and visual element management

## 🚀 Production Status

**Current Status:** 99% Production Ready - Codebase Standardization Complete ✨

### ✅ Production Ready Components
- **Authentication System**: 100% functional with perfect security
- **Admin System**: 100% complete CRUD operations for all content types
- **Database Integration**: 100% functional Supabase integration with real-time data
- **Unified Design System**: 100% NEW - Complete standardization with single source of truth
- **Unified Interaction System**: 100% NEW - All user interactions through UnifiedInteractionButton
- **Modal System**: 100% NEW - All modals standardized with UnifiedModal
- **Card System**: 100% NEW - Modern BentoCard approach across all components
- **Simplified Services**: 100% NEW - React patterns replace complex abstractions
- **Styling System**: 100% unified theme-aware design system with zero hardcoded colors
- **Icon/Emoji Management**: 100% complete admin-controlled visual design system
- **Activities System**: 100% complete community meetup functionality
- **Profile Management**: 95% complete user activity tracking
- **Image Management**: 90% complete Supabase Storage integration

### 🎯 Recent Standardization Achievements (2025)
- **Component Consolidation**: Eliminated 800+ lines of redundant code
- **Legacy Component Removal**: Removed JoinLeaveButton, RSVPButton, FavoriteButton
- **Service Simplification**: OptimizedRealtimeService (394 lines) → RealtimeService (81 lines)
- **React Pattern Adoption**: ConnectionService → useSimpleConnections hook
- **Design System Unification**: Single source of truth for all visual elements
- **Navigation Standardization**: 100% React Router navigation across all sections
- **TypeScript Compliance**: Zero TypeScript errors achieved (January 2025)
- **Cross-Section Consistency**: Unified interaction patterns across Home, Activities, FamHub, Discover, Profile

### 🔧 Final Integration Tasks
- **FamHub Vault**: External chat links integration (95% complete)
- **Discover Section**: Festival/event discovery optimization (90% complete)
- **Emergency Help**: Basic system implemented, needs enhancement
- **Map Functionality**: Admin-shared locations for community gathering
- **Dashboard Intelligence**: Personalized content and recommendations

**Target Production Date:** Ready for deployment - final testing in progress
**Latest Technical Report:** [Festival Family Technical Report](FESTIVAL_FAMILY_TECHNICAL_REPORT.md)

## Features

- 🔐 **Secure Authentication**: User authentication and authorization with Supabase
- 👥 **Social Connections**: Find and connect with like-minded festival-goers
- 💬 **Real-time Chat**: Communicate with your connections in real-time
- 👪 **Group Formation**: Create and join groups for specific festivals
- 📅 **Festival Discovery**: Find upcoming festivals and events
- 📸 **Media Sharing**: Share photos and memories from festivals
- 🔄 **Real-time Updates**: Get notified of new connections and messages
- 🎨 **Modern UI**: Beautiful and responsive interface built with shadcn/ui
- ♿ **Accessibility**: WCAG 2.2 compliant for all users
- 📱 **Responsive Design**: Works on all devices, from mobile to desktop

## Getting Started

### Prerequisites

- Node.js (v20 or higher)
- npm (v9 or higher)
- Supabase account (for backend services)
- Git (for version control)

### Environment Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/your-username/festival-family.git
   cd festival-family
   ```

2. Create a `.env` file in the root directory:
   ```bash
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

3. Install dependencies:
   ```bash
   npm install
   ```

### Development

Start the development server:
```bash
npm run dev
```

The application will be available at `http://localhost:5173`.

### Testing

#### Unit and Integration Tests
Run the test suite:
```bash
npm run test
```

Run tests with coverage:
```bash
npm run test:coverage
```

#### Production Readiness Testing
Run comprehensive audit tests:
```bash
npm run test:audit
```

Run specific test categories:
```bash
npm run test:auth         # Authentication testing
npm run test:navigation   # Navigation testing
npm run test:responsive   # Responsive design testing
npm run test:admin        # Admin features testing
npm run test:architecture # Architecture consistency testing
```

**Testing Infrastructure:**
- **8 Automated Testing Scripts** with visual verification
- **50+ Evidence Screenshots** documenting application state
- **15+ Detailed JSON Reports** with technical metrics
- **Cross-Platform Testing** across mobile, tablet, and desktop
- **Evidence-Based Methodology** with browser automation

### Building for Production

Build the application for production:
```bash
npm run build:prod
```

Preview the production build:
```bash
npm run serve:prod
```

### Code Quality

Format code with Prettier:
```bash
npm run format
```

Lint code with ESLint:
```bash
npm run lint
```

Type check with TypeScript:
```bash
npm run type-check
```

## Architecture

Festival Family follows a modular architecture with clear separation of concerns:

### Project Structure

```text
festival-family/
├── src/                  # Source code
│   ├── components/       # Reusable UI components
│   │   ├── ui/           # Base UI components (shadcn/ui)
│   │   ├── design-system/ # Unified design system components
│   │   │   ├── UnifiedInteractionButton.tsx  # Single source for all interactions
│   │   │   ├── UnifiedModal.tsx              # Standardized modal system
│   │   │   ├── BentoGrid.tsx                 # Modern card layout system
│   │   │   ├── UnifiedComponents.tsx         # Core design components
│   │   │   └── index.ts                      # Design system exports
│   │   ├── activities/   # Activity-related components
│   │   ├── admin/        # Admin interface components
│   │   ├── auth/         # Authentication components
│   │   ├── common/       # Shared utility components
│   │   └── navigation/   # Navigation components
│   ├── hooks/            # Custom React hooks
│   │   ├── auth/         # Authentication hooks
│   │   ├── realtime/     # Simplified real-time hooks
│   │   ├── useUnifiedData.ts    # Single source of truth for data
│   │   ├── useSimpleConnections.ts  # React pattern for connections
│   │   └── performance/  # Performance monitoring hooks
│   ├── lib/              # Core utilities and services
│   │   ├── supabase/     # Supabase integration
│   │   │   ├── services/ # Simplified service modules
│   │   │   └── types/    # Supabase-related types
│   │   ├── data/         # Unified data service
│   │   │   └── unified-data-service.ts  # Single source of truth
│   │   ├── services/     # Enhanced services
│   │   │   └── enhancedColorMappingService.ts  # Color system
│   │   ├── react-query/  # React Query configuration
│   │   └── utils/        # Utility functions
│   ├── pages/            # Application routes
│   ├── providers/        # Context providers
│   ├── types/            # TypeScript type definitions
│   └── test/             # Test utilities
├── docs/                 # Project documentation
├── scripts/              # Build and utility scripts
├── public/               # Static assets
└── .husky/               # Git hooks
```

### Key Technologies

- **Frontend**: React, TypeScript, Vite
- **UI Components**: shadcn/ui, Tailwind CSS
- **State Management**: React Query, Context API
- **Backend**: Supabase (Auth, Database, Storage, Realtime)
- **Testing**: Jest, React Testing Library
- **Performance**: Code splitting, lazy loading, memoization
- **Accessibility**: ARIA attributes, keyboard navigation, screen reader support

## Documentation

Comprehensive documentation is available in the `docs` directory:

### Core Documentation
- [Architecture Overview](docs/ARCHITECTURE.md) - High-level architecture and design decisions
- [Component Library](docs/COMPONENTS.md) - UI component documentation
- [Development Guide](docs/DEVELOPMENT.md) - Development workflow and best practices
- [Error Handling Guide](docs/ERROR_HANDLING.md) - Comprehensive error handling system
- [Git Hooks Guide](docs/GIT_HOOKS.md) - Git hooks setup and usage
- [Testing Guide](docs/TESTING.md) - Testing strategies and examples
- [Performance Guide](docs/PERFORMANCE.md) - Performance optimization techniques
- [Accessibility Guide](docs/ACCESSIBILITY.md) - Accessibility guidelines and implementation

### Production Readiness Reports
- [Production Readiness Assessment](PRODUCTION_READINESS_REPORT.md) - Comprehensive audit findings
- [Comprehensive Audit Summary](COMPREHENSIVE_AUDIT_SUMMARY.md) - Executive summary and strategic recommendations
- [Critical Issues Debug Report](CRITICAL_ISSUES_DEBUG_REPORT.md) - Detailed technical issue analysis
- [Evidence-Based Testing Log](EVIDENCE_BASED_TESTING_LOG.md) - Testing methodology and results

## 🏗️ Standardized Architecture

### Navigation Standardization

Festival Family uses **100% React Router navigation** across all sections with zero `window.location.href` or `window.open()` calls for internal navigation:

```typescript
import { useNavigate } from 'react-router-dom'

// ✅ Correct: React Router navigation
const navigate = useNavigate()
navigate('/famhub?tab=RESOURCES')

// ❌ Deprecated: Direct window navigation
window.location.href = '/famhub'
window.open('/resources', '_blank')
```

**Navigation Patterns:**
- **Internal links**: Use `navigate()` for seamless SPA experience
- **External links**: Use `window.open()` with security attributes
- **Tab navigation**: URL parameters for state management (`/famhub?tab=RESOURCES`)
- **Modal navigation**: Component state management, not URL changes

### Unified Design System

```typescript
import {
  UnifiedInteractionButton,
  UnifiedModal,
  BentoCard,
  UnifiedBadge,
  EnhancedUnifiedBadge
} from '@/components/design-system'

// Single source of truth for all user interactions
<UnifiedInteractionButton
  type="favorite"
  itemId={activityId}
  itemType="activity"
  variant="compact"
  size="default"
  showCount={true}
/>

// Enhanced badge with color mapping service
<EnhancedUnifiedBadge
  contentType="activities"
  contentCategory="meetup"
  size="sm"
>
  Meetup
</EnhancedUnifiedBadge>

// Standardized modal system
<UnifiedModal
  isOpen={isOpen}
  onClose={onClose}
  title="Activity Details"
  size="lg"
>
  <ActivityContent />
</UnifiedModal>

// Modern card layout system
<BentoCard
  variant="glassmorphism"
  interactive
  title="Festival Activity"
  description="Join the community"
  imageUrl="/activity-image.jpg"
/>
```

### Simplified Data Services

```typescript
import { unifiedDataService } from '@/lib/data/unified-data-service'
import { useUnifiedData } from '@/hooks/useUnifiedData'

// Single source of truth for data fetching
const { activities, isLoading, error } = useUnifiedData.useActivities({
  type: 'meetup',
  status: 'published'
})

// Direct service usage
const activities = await unifiedDataService.getActivities({
  filters: { type: 'meetup' }
})
```

### React Pattern Services

```typescript
import { useSimpleConnections } from '@/hooks/useSimpleConnections'
import { useRealtimeSubscription } from '@/hooks/realtime/useRealtime'

// React hooks replace complex service classes
const {
  potentialMatches,
  sendConnectionRequest,
  isLoadingMatches
} = useSimpleConnections()

// Simplified real-time subscriptions
useRealtimeSubscription('activities', ['activities', 'all'], {
  event: '*',
  callback: (payload) => console.log('Activity updated:', payload)
})
```

### Enhanced Color System

```typescript
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService'

// Database-driven color system
const colors = enhancedColorMappingService.getColorsForContent('activities', 'meetup')
const badgeProps = enhancedColorMappingService.getBadgeProps('activities', 'meetup')

// Single source of truth for all visual elements
<UnifiedBadge {...badgeProps}>
  Meetup
</UnifiedBadge>
```

### Single Source of Truth Architecture

Festival Family follows strict single source of truth principles:

**Data Layer:**
- `unified-data-service.ts` - Central data fetching and caching
- `enhancedColorMappingService.ts` - Centralized visual styling
- Supabase database - Single source for all application data

**Component Layer:**
- `UnifiedInteractionButton` - All user interactions (favorite, join, RSVP)
- `EnhancedUnifiedBadge` - All badge styling with color mapping
- `BentoCard` - Consistent card layouts across all sections
- `UnifiedModal` - Standardized modal system

**Navigation Layer:**
- React Router - All internal navigation
- URL parameters - State management for tabs and filters
- No duplicate navigation patterns or window manipulation

**Performance Benefits:**
- Zero duplicate implementations
- Consistent user experience
- Simplified maintenance
- Predictable behavior patterns

## Contributing

We welcome contributions from the community! Please read our [Contributing Guide](CONTRIBUTING.md) for details on:

- Code of conduct
- Development process
- Submitting issues
- Creating pull requests
- Coding standards
- Testing requirements

## 📚 Documentation

### **Core Documentation**
- [Strategic Roadmap](docs/STRATEGIC_ROADMAP.md) - Complete development strategy and implementation plan
- [Comprehensive Technical Status](docs/COMPREHENSIVE_TECHNICAL_STATUS.md) - Current implementation status and architecture
- [Architecture Overview](docs/ARCHITECTURE.md) - System architecture and design patterns
- [Styling Guidelines](docs/STYLING_GUIDELINES.md) - Complete styling standardization guidelines
- [Icon & Emoji Management](docs/ICON_EMOJI_MANAGEMENT.md) - Admin-controlled visual design system documentation

### **Standardization Reports (2025)**
- [Codebase Standardization Complete](docs/CODEBASE_STANDARDIZATION_COMPLETE.md) - Complete standardization achievement report
- [Component Consolidation Report](docs/COMPONENT_CONSOLIDATION_REPORT.md) - Legacy component elimination and unification
- [Service Simplification Report](docs/SERVICE_SIMPLIFICATION_REPORT.md) - Complex abstraction replacement with React patterns

### **Historical Reports**
- [Technical Report](FESTIVAL_FAMILY_TECHNICAL_REPORT.md) - Detailed technical implementation report
- [Hardcoded Color Elimination](docs/HARDCODED_COLOR_ELIMINATION_COMPLETE.md) - Styling unification achievement

## Roadmap

See our [Strategic Roadmap](docs/STRATEGIC_ROADMAP.md) for the complete development strategy and implementation plan.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgements

- [Supabase](https://supabase.com/) - Backend services
- [shadcn/ui](https://ui.shadcn.com/) - UI components
- [React Query](https://tanstack.com/query/latest) - Data fetching and caching
- [Vite](https://vitejs.dev/) - Frontend tooling
- [Tailwind CSS](https://tailwindcss.com/) - Styling
- [TypeScript](https://www.typescriptlang.org/) - Type safety
- [Jest](https://jestjs.io/) - Testing
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/) - Component testing

# Festival Family

<p align="center">
  <img src="public/logo.png" alt="Festival Family Logo" width="200" />
</p>

<p align="center">
  <strong>Connect. Discover. Experience.</strong>
</p>

<p align="center">
  <a href="#features">Features</a> •
  <a href="#getting-started">Getting Started</a> •
  <a href="#production-status">Production Status</a> •
  <a href="#documentation">Documentation</a> •
  <a href="#architecture">Architecture</a> •
  <a href="#contributing">Contributing</a> •
  <a href="#license">License</a>
</p>

Festival Family is a community-driven platform designed to connect solo festival-goers, helping them find their "festival family" and build lasting connections. Our mission is to ensure that no one experiences festivals alone.

## 🎯 Core Features

### **Festival Activities System**
Community-organized meetups for solo festival-goers including:
- **Photo meetups** and location-based gatherings
- **Daily recurring activities** (yoga, meditation sessions)
- **Festival-specific events** and community challenges
- **Real-time participation** tracking and admin management

### **FamHub Vault Structure**
- **Chats**: External platform links (WhatsApp, Discord, Telegram)
- **Communities**: Facebook groups and social platform connections
- **Resources**: Generic and location-specific information (transportation, local guides, festival tips)

### **Discover Section**
- **Upcoming festivals and events** (both festival-organized and community events)
- **Community reunions** and off-site gatherings
- **Main festival acts** and performance schedules
- **Real-time event discovery** and participation

### **Additional Core Features**
- **Emergency help system** for festival safety
- **Map functionality** with admin-shared community gathering locations
- **Authenticated dashboard** as effective HUD showing upcoming events, announcements, weather warnings, and personalized content

### **Icon/Emoji Management System**
- **Admin-controlled visual design**: Comprehensive management interface for icons, emojis, and colors
- **Database-driven icon visibility**: Toggle icon display per content type and category
- **Real-time admin-to-user pipeline**: Changes immediately reflected across the application
- **Clean, text-focused design**: Professional appearance with optimized text hierarchy
- **Single source of truth**: Centralized color and visual element management

## 🚀 Production Status

**Current Status:** 98% Production Ready - UI Pattern Consolidation Complete

### ✅ Production Ready Components
- **Authentication System**: 100% functional with perfect security
- **Admin System**: 100% complete CRUD operations for all content types
- **Database Integration**: 100% functional Supabase integration with real-time data
- **Unified Interaction System**: 100% NEW - Single source of truth for all user interactions
- **UI Pattern Consolidation**: 100% NEW - Eliminated scattered button implementations
- **Styling System**: 100% unified theme-aware design system
- **Icon/Emoji Management**: 100% complete admin-controlled visual design system
- **Activities System**: 98% complete community meetup functionality
- **Profile Management**: 95% complete user activity tracking
- **Image Management**: 90% complete Supabase Storage integration

### 🔧 Final Integration Tasks
- **FamHub Vault**: External chat links integration (90% complete)
- **Discover Section**: Festival/event discovery optimization
- **Emergency Help**: Basic system implemented, needs enhancement
- **Map Functionality**: Admin-shared locations for community gathering
- **Dashboard Intelligence**: Personalized content and recommendations

**Target Production Date:** 1-2 weeks for final polish and deployment
**Latest Technical Report:** [Festival Family Technical Report](FESTIVAL_FAMILY_TECHNICAL_REPORT.md)

## Features

- 🔐 **Secure Authentication**: User authentication and authorization with Supabase
- 👥 **Social Connections**: Find and connect with like-minded festival-goers
- 💬 **Real-time Chat**: Communicate with your connections in real-time
- 👪 **Group Formation**: Create and join groups for specific festivals
- 📅 **Festival Discovery**: Find upcoming festivals and events
- 📸 **Media Sharing**: Share photos and memories from festivals
- 🔄 **Real-time Updates**: Get notified of new connections and messages
- 🎨 **Modern UI**: Beautiful and responsive interface built with shadcn/ui
- ♿ **Accessibility**: WCAG 2.2 compliant for all users
- 📱 **Responsive Design**: Works on all devices, from mobile to desktop

## Getting Started

### Prerequisites

- Node.js (v20 or higher)
- npm (v9 or higher)
- Supabase account (for backend services)
- Git (for version control)

### Environment Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/your-username/festival-family.git
   cd festival-family
   ```

2. Create a `.env` file in the root directory:
   ```bash
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

3. Install dependencies:
   ```bash
   npm install
   ```

### Development

Start the development server:
```bash
npm run dev
```

The application will be available at `http://localhost:5173`.

### Testing

#### Unit and Integration Tests
Run the test suite:
```bash
npm run test
```

Run tests with coverage:
```bash
npm run test:coverage
```

#### Production Readiness Testing
Run comprehensive audit tests:
```bash
npm run test:audit
```

Run specific test categories:
```bash
npm run test:auth         # Authentication testing
npm run test:navigation   # Navigation testing
npm run test:responsive   # Responsive design testing
npm run test:admin        # Admin features testing
npm run test:architecture # Architecture consistency testing
```

**Testing Infrastructure:**
- **8 Automated Testing Scripts** with visual verification
- **50+ Evidence Screenshots** documenting application state
- **15+ Detailed JSON Reports** with technical metrics
- **Cross-Platform Testing** across mobile, tablet, and desktop
- **Evidence-Based Methodology** with browser automation

### Building for Production

Build the application for production:
```bash
npm run build:prod
```

Preview the production build:
```bash
npm run serve:prod
```

### Code Quality

Format code with Prettier:
```bash
npm run format
```

Lint code with ESLint:
```bash
npm run lint
```

Type check with TypeScript:
```bash
npm run type-check
```

## Architecture

Festival Family follows a modular architecture with clear separation of concerns:

### Project Structure

```text
festival-family/
├── src/                  # Source code
│   ├── components/       # Reusable UI components
│   │   ├── ui/           # Base UI components (shadcn/ui)
│   │   ├── connections/  # Connection-related components
│   │   ├── groups/       # Group-related components
│   │   ├── chat/         # Chat-related components
│   │   ├── performance/  # Performance-optimized components
│   │   └── navigation/   # Navigation components
│   ├── hooks/            # Custom React hooks
│   │   ├── auth/         # Authentication hooks
│   │   ├── connections/  # Connection hooks
│   │   ├── chat/         # Chat hooks
│   │   ├── festivals/    # Festival hooks
│   │   └── performance/  # Performance monitoring hooks
│   ├── lib/              # Core utilities and services
│   │   ├── supabase/     # Supabase integration
│   │   │   ├── services/ # Service modules
│   │   │   └── types/    # Supabase-related types
│   │   ├── react-query/  # React Query configuration
│   │   └── utils/        # Utility functions
│   ├── pages/            # Application routes
│   ├── providers/        # Context providers
│   ├── types/            # TypeScript type definitions
│   └── test/             # Test utilities
├── docs/                 # Project documentation
├── scripts/              # Build and utility scripts
├── public/               # Static assets
└── .husky/               # Git hooks
```

### Key Technologies

- **Frontend**: React, TypeScript, Vite
- **UI Components**: shadcn/ui, Tailwind CSS
- **State Management**: React Query, Context API
- **Backend**: Supabase (Auth, Database, Storage, Realtime)
- **Testing**: Jest, React Testing Library
- **Performance**: Code splitting, lazy loading, memoization
- **Accessibility**: ARIA attributes, keyboard navigation, screen reader support

## Documentation

Comprehensive documentation is available in the `docs` directory:

### Core Documentation
- [Architecture Overview](docs/ARCHITECTURE.md) - High-level architecture and design decisions
- [Component Library](docs/COMPONENTS.md) - UI component documentation
- [Development Guide](docs/DEVELOPMENT.md) - Development workflow and best practices
- [Error Handling Guide](docs/ERROR_HANDLING.md) - Comprehensive error handling system
- [Git Hooks Guide](docs/GIT_HOOKS.md) - Git hooks setup and usage
- [Testing Guide](docs/TESTING.md) - Testing strategies and examples
- [Performance Guide](docs/PERFORMANCE.md) - Performance optimization techniques
- [Accessibility Guide](docs/ACCESSIBILITY.md) - Accessibility guidelines and implementation

### Production Readiness Reports
- [Production Readiness Assessment](PRODUCTION_READINESS_REPORT.md) - Comprehensive audit findings
- [Comprehensive Audit Summary](COMPREHENSIVE_AUDIT_SUMMARY.md) - Executive summary and strategic recommendations
- [Critical Issues Debug Report](CRITICAL_ISSUES_DEBUG_REPORT.md) - Detailed technical issue analysis
- [Evidence-Based Testing Log](EVIDENCE_BASED_TESTING_LOG.md) - Testing methodology and results

## Core Services

### Authentication

```typescript
import { authService } from '@/lib/supabase/services/auth-service'

// Sign in
const { data, error } = await authService.signIn(
  '<EMAIL>',
  'password'
)

// Get user profile
const { profile } = await authService.getProfile(userId)
```

### File Storage

```typescript
import { storageService } from '@/lib/supabase/storage'

// Upload file
const { url } = await storageService.uploadFile({
  bucketName: 'festival-images',
  path: `events/${eventId}/cover.jpg`,
  file: imageFile
})
```

### Real-time Updates

```typescript
import { realtimeService } from '@/lib/supabase/realtime'

// Subscribe to festival updates
const subscription = realtimeService.subscribe(
  'festivals',
  (payload) => console.log('Festival updated:', payload)
)
```

## Contributing

We welcome contributions from the community! Please read our [Contributing Guide](CONTRIBUTING.md) for details on:

- Code of conduct
- Development process
- Submitting issues
- Creating pull requests
- Coding standards
- Testing requirements

## 📚 Documentation

### **Core Documentation**
- [Strategic Roadmap](docs/STRATEGIC_ROADMAP.md) - Complete development strategy and implementation plan
- [Comprehensive Technical Status](docs/COMPREHENSIVE_TECHNICAL_STATUS.md) - Current implementation status and architecture
- [Architecture Overview](docs/ARCHITECTURE.md) - System architecture and design patterns
- [Styling Guidelines](docs/STYLING_GUIDELINES.md) - Complete styling standardization guidelines
- [Icon & Emoji Management](docs/ICON_EMOJI_MANAGEMENT.md) - Admin-controlled visual design system documentation

### **Historical Reports**
- [Technical Report](FESTIVAL_FAMILY_TECHNICAL_REPORT.md) - Detailed technical implementation report
- [Hardcoded Color Elimination](docs/HARDCODED_COLOR_ELIMINATION_COMPLETE.md) - Styling unification achievement

## Roadmap

See our [Strategic Roadmap](docs/STRATEGIC_ROADMAP.md) for the complete development strategy and implementation plan.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgements

- [Supabase](https://supabase.com/) - Backend services
- [shadcn/ui](https://ui.shadcn.com/) - UI components
- [React Query](https://tanstack.com/query/latest) - Data fetching and caching
- [Vite](https://vitejs.dev/) - Frontend tooling
- [Tailwind CSS](https://tailwindcss.com/) - Styling
- [TypeScript](https://www.typescriptlang.org/) - Type safety
- [Jest](https://jestjs.io/) - Testing
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/) - Component testing

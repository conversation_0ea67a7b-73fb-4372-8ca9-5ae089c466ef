import React from 'react';
import { motion } from 'framer-motion';
import LoadingSpinner from './LoadingSpinner';

interface UnifiedLoadingStateProps {
  /**
   * The loading message to display
   * @default 'Loading...'
   */
  message?: string;
  /**
   * Optional subtitle for additional context
   */
  subtitle?: string;
  /**
   * The variant of the loading state
   * @default 'default'
   */
  variant?: 'default' | 'fullscreen' | 'card' | 'minimal';
  /**
   * Size of the loading spinner
   * @default 'md'
   */
  size?: 'sm' | 'md' | 'lg';
  /**
   * Additional class names
   */
  className?: string;
  /**
   * Whether to show the spinner
   * @default true
   */
  showSpinner?: boolean;
}

/**
 * Unified Loading State Component
 * 
 * Provides consistent loading UI across the entire application.
 * Follows the Festival Family design system with theme-aware styling.
 */
const UnifiedLoadingState: React.FC<UnifiedLoadingStateProps> = ({
  message = 'Loading...',
  subtitle,
  variant = 'default',
  size = 'md',
  className = '',
  showSpinner = true
}) => {
  const baseClasses = 'flex flex-col items-center justify-center text-center';
  
  const variantClasses = {
    default: 'p-8',
    fullscreen: 'min-h-screen bg-background',
    card: 'p-6 bg-card rounded-lg border border-border',
    minimal: 'p-4'
  };

  const textClasses = {
    default: 'text-foreground',
    fullscreen: 'text-foreground',
    card: 'text-card-foreground',
    minimal: 'text-muted-foreground'
  };

  const messageSize = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-xl'
  };

  const subtitleSize = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`${baseClasses} ${variantClasses[variant]} ${textClasses[variant]} ${className}`}
      role="status"
      aria-live="polite"
      aria-label={`${message}${subtitle ? ` - ${subtitle}` : ''}`}
    >
      {showSpinner && (
        <div className="mb-4">
          <LoadingSpinner 
            size={size} 
            variant="spinner" 
            centered 
          />
        </div>
      )}
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.1, duration: 0.3 }}
      >
        <h2 className={`font-medium ${messageSize[size]} mb-2`}>
          {message}
        </h2>
        
        {subtitle && (
          <p className={`${subtitleSize[size]} text-muted-foreground`}>
            {subtitle}
          </p>
        )}
      </motion.div>
    </motion.div>
  );
};

export default UnifiedLoadingState;
export { UnifiedLoadingState };

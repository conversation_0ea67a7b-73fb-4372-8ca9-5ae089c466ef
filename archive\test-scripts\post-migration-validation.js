/**
 * Post-Migration Validation Script
 * 
 * Comprehensive testing after applying all migrations to ensure
 * everything is working correctly and no functionality is broken.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Post-Migration Validation');
console.log('============================');

// Test 1: RLS Recursion Fix Validation
async function testRLSRecursionFix() {
  console.log('🔧 Test 1: RLS Recursion Fix Validation');
  console.log('----------------------------------------');
  
  const results = {
    adminAccountAccessible: false,
    profilesTableAccessible: false,
    rlsPoliciesWorking: false,
    isAdminFunctionWorking: false,
    timestamp: new Date().toISOString()
  };
  
  try {
    // Test 1.1: Check admin account access
    console.log('📋 Testing admin account access...');
    const { data: adminProfile, error: adminError } = await supabase
      .from('profiles')
      .select('id, email, role')
      .eq('email', '<EMAIL>')
      .single();
    
    if (adminError) {
      console.log(`❌ Admin account access failed: ${adminError.message}`);
      if (adminError.message.includes('infinite recursion')) {
        console.log('🚨 RLS recursion issue still exists!');
      }
    } else {
      results.adminAccountAccessible = true;
      console.log('✅ Admin account accessible:');
      console.log(`   📧 Email: ${adminProfile.email}`);
      console.log(`   🛡️ Role: ${adminProfile.role}`);
    }
    
    // Test 1.2: Test profiles table general access
    console.log('📊 Testing profiles table access...');
    const { data: profileCount, error: countError } = await supabase
      .from('profiles')
      .select('id', { count: 'exact', head: true });
    
    if (countError) {
      console.log(`❌ Profiles table access failed: ${countError.message}`);
    } else {
      results.profilesTableAccessible = true;
      console.log(`✅ Profiles table accessible - found ${profileCount} profiles`);
    }
    
    // Test 1.3: Test is_admin function
    console.log('⚙️ Testing is_admin function...');
    try {
      const { data: isAdminResult, error: isAdminError } = await supabase
        .rpc('is_admin');
      
      if (isAdminError) {
        console.log(`⚠️ is_admin function error: ${isAdminError.message}`);
      } else {
        results.isAdminFunctionWorking = true;
        console.log(`✅ is_admin function working: ${isAdminResult}`);
      }
    } catch (err) {
      console.log(`⚠️ is_admin function test error: ${err.message}`);
    }
    
    results.rlsPoliciesWorking = results.profilesTableAccessible && !adminError?.message?.includes('recursion');
    
  } catch (error) {
    console.error('💥 RLS recursion test failed:', error);
    results.error = error.message;
  }
  
  return results;
}

// Test 2: Security Functions Validation
async function testSecurityFunctions() {
  console.log('\n🛡️ Test 2: Security Functions Validation');
  console.log('----------------------------------------');
  
  const results = {
    xssFunctionWorking: false,
    roleChangeFunctionWorking: false,
    securityTestResults: [],
    timestamp: new Date().toISOString()
  };
  
  try {
    // Test 2.1: XSS Protection Function
    console.log('🧪 Testing XSS protection function...');
    const xssPayload = '<script>alert("test")</script>Hello';
    const { data: xssResult, error: xssError } = await supabase
      .rpc('test_xss_protection', { test_input: xssPayload });
    
    if (xssError) {
      console.log(`❌ XSS function error: ${xssError.message}`);
    } else {
      results.xssFunctionWorking = true;
      const wasSanitized = xssResult !== xssPayload;
      console.log(`✅ XSS protection working: ${wasSanitized ? 'PROTECTED' : 'VULNERABLE'}`);
      console.log(`   📤 Input:  "${xssPayload}"`);
      console.log(`   📥 Output: "${xssResult}"`);
      
      results.securityTestResults.push({
        test: 'XSS Protection',
        input: xssPayload,
        output: xssResult,
        protected: wasSanitized
      });
    }
    
    // Test 2.2: Role Change Function
    console.log('🔧 Testing role change function...');
    try {
      const { data: roleResult, error: roleError } = await supabase
        .rpc('change_user_role', { 
          target_user_id: '00000000-0000-0000-0000-000000000000', 
          new_role: 'USER' 
        });
      
      if (roleError) {
        if (roleError.message.includes('Only SUPER_ADMIN')) {
          console.log('✅ Role change function working - properly secured');
          results.roleChangeFunctionWorking = true;
        } else {
          console.log(`⚠️ Role change function: ${roleError.message}`);
        }
      } else {
        console.log('✅ Role change function executed successfully');
        results.roleChangeFunctionWorking = true;
      }
    } catch (err) {
      console.log(`⚠️ Role change function error: ${err.message}`);
    }
    
  } catch (error) {
    console.error('💥 Security functions test failed:', error);
    results.error = error.message;
  }
  
  return results;
}

// Test 3: New Tables and Features Validation
async function testNewTablesAndFeatures() {
  console.log('\n🆕 Test 3: New Tables and Features Validation');
  console.log('---------------------------------------------');
  
  const results = {
    chatTablesExist: false,
    activityCoordinationExists: false,
    smartGroupFormationExists: false,
    tableAccessResults: [],
    timestamp: new Date().toISOString()
  };
  
  // Tables to test
  const tablesToTest = [
    { name: 'chat_rooms', feature: 'Chat System' },
    { name: 'chat_room_members', feature: 'Chat System' },
    { name: 'chat_messages', feature: 'Chat System' },
    { name: 'activity_attendance', feature: 'Activity Coordination' },
    { name: 'artist_preferences', feature: 'Activity Coordination' },
    { name: 'group_suggestions', feature: 'Smart Group Formation' },
    { name: 'group_suggestion_responses', feature: 'Smart Group Formation' },
    { name: 'group_activities', feature: 'Smart Group Formation' }
  ];
  
  try {
    for (const table of tablesToTest) {
      console.log(`📋 Testing ${table.name} table...`);
      
      try {
        const { data, error } = await supabase
          .from(table.name)
          .select('*')
          .limit(1);
        
        if (error) {
          if (error.message.includes('does not exist')) {
            console.log(`❌ ${table.name} table does not exist`);
            results.tableAccessResults.push({
              table: table.name,
              feature: table.feature,
              exists: false,
              accessible: false,
              error: error.message
            });
          } else {
            console.log(`✅ ${table.name} table exists but may have RLS restrictions`);
            results.tableAccessResults.push({
              table: table.name,
              feature: table.feature,
              exists: true,
              accessible: false,
              error: error.message
            });
          }
        } else {
          console.log(`✅ ${table.name} table exists and accessible`);
          results.tableAccessResults.push({
            table: table.name,
            feature: table.feature,
            exists: true,
            accessible: true
          });
        }
      } catch (err) {
        console.log(`⚠️ ${table.name} test error: ${err.message}`);
        results.tableAccessResults.push({
          table: table.name,
          feature: table.feature,
          exists: false,
          accessible: false,
          error: err.message
        });
      }
    }
    
    // Determine feature availability
    const chatTables = results.tableAccessResults.filter(r => r.feature === 'Chat System');
    results.chatTablesExist = chatTables.every(t => t.exists);
    
    const activityTables = results.tableAccessResults.filter(r => r.feature === 'Activity Coordination');
    results.activityCoordinationExists = activityTables.some(t => t.exists);
    
    const groupTables = results.tableAccessResults.filter(r => r.feature === 'Smart Group Formation');
    results.smartGroupFormationExists = groupTables.every(t => t.exists);
    
  } catch (error) {
    console.error('💥 New tables test failed:', error);
    results.error = error.message;
  }
  
  return results;
}

// Test 4: Frontend Compatibility Check
async function testFrontendCompatibility() {
  console.log('\n🖥️ Test 4: Frontend Compatibility Check');
  console.log('---------------------------------------');
  
  const results = {
    coreTablesAccessible: false,
    existingFeaturesWorking: false,
    noBreakingChanges: false,
    compatibilityResults: [],
    timestamp: new Date().toISOString()
  };
  
  // Core tables that frontend depends on
  const coreTables = ['profiles', 'festivals', 'events', 'activities', 'groups'];
  
  try {
    for (const tableName of coreTables) {
      console.log(`🔍 Testing ${tableName} table compatibility...`);
      
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .limit(1);
        
        if (error) {
          console.log(`❌ ${tableName} compatibility issue: ${error.message}`);
          results.compatibilityResults.push({
            table: tableName,
            compatible: false,
            error: error.message
          });
        } else {
          console.log(`✅ ${tableName} table compatible`);
          results.compatibilityResults.push({
            table: tableName,
            compatible: true
          });
        }
      } catch (err) {
        console.log(`⚠️ ${tableName} compatibility error: ${err.message}`);
        results.compatibilityResults.push({
          table: tableName,
          compatible: false,
          error: err.message
        });
      }
    }
    
    results.coreTablesAccessible = results.compatibilityResults.every(r => r.compatible);
    results.existingFeaturesWorking = results.coreTablesAccessible;
    results.noBreakingChanges = results.coreTablesAccessible;
    
  } catch (error) {
    console.error('💥 Frontend compatibility test failed:', error);
    results.error = error.message;
  }
  
  return results;
}

// Main execution
async function main() {
  try {
    console.log(`🕐 Started at: ${new Date().toISOString()}\n`);
    
    // Run all validation tests
    const rlsResults = await testRLSRecursionFix();
    const securityResults = await testSecurityFunctions();
    const newFeaturesResults = await testNewTablesAndFeatures();
    const compatibilityResults = await testFrontendCompatibility();
    
    // Compile comprehensive results
    const validationResults = {
      testSuite: 'Post-Migration Comprehensive Validation',
      timestamp: new Date().toISOString(),
      rlsRecursionFix: rlsResults,
      securityFunctions: securityResults,
      newFeatures: newFeaturesResults,
      frontendCompatibility: compatibilityResults,
      overallStatus: {
        rlsRecursionFixed: rlsResults.rlsPoliciesWorking && rlsResults.adminAccountAccessible,
        securityImplementationsWorking: securityResults.xssFunctionWorking && securityResults.roleChangeFunctionWorking,
        newFeaturesAvailable: newFeaturesResults.chatTablesExist || newFeaturesResults.smartGroupFormationExists,
        frontendCompatible: compatibilityResults.noBreakingChanges,
        readyForProduction: false // Will be calculated below
      }
    };
    
    // Calculate production readiness
    validationResults.overallStatus.readyForProduction = 
      validationResults.overallStatus.rlsRecursionFixed &&
      validationResults.overallStatus.securityImplementationsWorking &&
      validationResults.overallStatus.frontendCompatible;
    
    // Save results to file
    const resultsDir = 'post-migration-validation-results';
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      `${resultsDir}/post-migration-validation-${Date.now()}.json`,
      JSON.stringify(validationResults, null, 2)
    );
    
    console.log('\n🎉 POST-MIGRATION VALIDATION SUMMARY');
    console.log('====================================');
    
    console.log('\n🔧 RLS RECURSION FIX:');
    console.log(`✅ Admin Account Access: ${rlsResults.adminAccountAccessible ? 'WORKING' : 'BLOCKED'}`);
    console.log(`✅ Profiles Table Access: ${rlsResults.profilesTableAccessible ? 'WORKING' : 'BLOCKED'}`);
    console.log(`✅ RLS Policies: ${rlsResults.rlsPoliciesWorking ? 'WORKING' : 'ISSUES'}`);
    console.log(`✅ is_admin Function: ${rlsResults.isAdminFunctionWorking ? 'WORKING' : 'ISSUES'}`);
    
    console.log('\n🛡️ SECURITY FUNCTIONS:');
    console.log(`✅ XSS Protection: ${securityResults.xssFunctionWorking ? 'WORKING' : 'ISSUES'}`);
    console.log(`✅ Role Change Protection: ${securityResults.roleChangeFunctionWorking ? 'WORKING' : 'ISSUES'}`);
    
    console.log('\n🆕 NEW FEATURES:');
    console.log(`✅ Chat System: ${newFeaturesResults.chatTablesExist ? 'AVAILABLE' : 'NOT AVAILABLE'}`);
    console.log(`✅ Activity Coordination: ${newFeaturesResults.activityCoordinationExists ? 'AVAILABLE' : 'NOT AVAILABLE'}`);
    console.log(`✅ Smart Group Formation: ${newFeaturesResults.smartGroupFormationExists ? 'AVAILABLE' : 'NOT AVAILABLE'}`);
    
    console.log('\n🖥️ FRONTEND COMPATIBILITY:');
    console.log(`✅ Core Tables: ${compatibilityResults.coreTablesAccessible ? 'COMPATIBLE' : 'ISSUES'}`);
    console.log(`✅ No Breaking Changes: ${compatibilityResults.noBreakingChanges ? 'CONFIRMED' : 'DETECTED'}`);
    
    console.log('\n🚀 PRODUCTION READINESS:');
    console.log(`🎯 Overall Status: ${validationResults.overallStatus.readyForProduction ? '✅ READY FOR PRODUCTION' : '⚠️ NEEDS ATTENTION'}`);
    
    console.log(`\n📁 Results saved to: ${resultsDir}/post-migration-validation-${Date.now()}.json`);
    
  } catch (error) {
    console.error('💥 Validation process failed:', error);
  }
  
  process.exit(0);
}

main();

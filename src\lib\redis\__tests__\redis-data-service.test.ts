/**
 * Redis Data Service Tests
 * 
 * Comprehensive test suite for Redis data service functionality.
 * Tests caching, fallback behavior, performance tracking, and cache invalidation.
 * 
 * @module RedisDataServiceTests
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { redisDataService } from '../redis-data-service'

// Mock unified data service
const mockUnifiedDataService = {
  getActivities: vi.fn(),
  getEvents: vi.fn(),
  getCommunities: vi.fn(),
  getAnnouncements: vi.fn(),
}

// Mock Redis client
const mockRedis = {
  get: vi.fn(),
  setex: vi.fn(),
  del: vi.fn(),
  keys: vi.fn(),
  lpush: vi.fn(),
  expire: vi.fn(),
  ltrim: vi.fn(),
}

// Mock Redis initialization
vi.mock('@upstash/redis', () => ({
  Redis: vi.fn(() => mockRedis)
}))

// Mock unified data service
vi.mock('@/lib/data/unified-data-service', () => ({
  unifiedDataService: mockUnifiedDataService
}))

describe('RedisDataService', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks()
  })

  afterEach(() => {
    // Clean up after each test
    vi.resetAllMocks()
  })

  describe('getActivities', () => {
    it('should return cached activities on cache hit', async () => {
      // Arrange
      const mockActivities = [
        { id: '1', title: 'Test Activity 1', type: 'meetup' },
        { id: '2', title: 'Test Activity 2', type: 'workshop' }
      ]
      mockRedis.get.mockResolvedValue(mockActivities)

      // Act
      const result = await redisDataService.getActivities()

      // Assert
      expect(result).toEqual(mockActivities)
      expect(mockRedis.get).toHaveBeenCalled()
      expect(mockUnifiedDataService.getActivities).not.toHaveBeenCalled()
    })

    it('should fetch from database on cache miss', async () => {
      // Arrange
      const mockActivities = [
        { id: '1', title: 'Test Activity 1', type: 'meetup' },
        { id: '2', title: 'Test Activity 2', type: 'workshop' }
      ]
      mockRedis.get.mockResolvedValue(null) // Cache miss
      mockUnifiedDataService.getActivities.mockResolvedValue(mockActivities)
      mockRedis.setex.mockResolvedValue('OK')

      // Act
      const result = await redisDataService.getActivities()

      // Assert
      expect(result).toEqual(mockActivities)
      expect(mockRedis.get).toHaveBeenCalled()
      expect(mockUnifiedDataService.getActivities).toHaveBeenCalled()
      expect(mockRedis.setex).toHaveBeenCalled()
    })

    it('should handle Redis errors gracefully', async () => {
      // Arrange
      const mockActivities = [
        { id: '1', title: 'Test Activity 1', type: 'meetup' }
      ]
      mockRedis.get.mockRejectedValue(new Error('Redis connection failed'))
      mockUnifiedDataService.getActivities.mockResolvedValue(mockActivities)

      // Act
      const result = await redisDataService.getActivities()

      // Assert
      expect(result).toEqual(mockActivities)
      expect(mockUnifiedDataService.getActivities).toHaveBeenCalled()
    })

    it('should handle filters correctly', async () => {
      // Arrange
      const filters = { type: 'meetup', featured: true }
      const mockActivities = [
        { id: '1', title: 'Featured Meetup', type: 'meetup', featured: true }
      ]
      mockRedis.get.mockResolvedValue(null)
      mockUnifiedDataService.getActivities.mockResolvedValue(mockActivities)
      mockRedis.setex.mockResolvedValue('OK')

      // Act
      const result = await redisDataService.getActivities(filters)

      // Assert
      expect(result).toEqual(mockActivities)
      expect(mockUnifiedDataService.getActivities).toHaveBeenCalledWith(filters)
    })
  })

  describe('getEvents', () => {
    it('should return cached events on cache hit', async () => {
      // Arrange
      const mockEvents = [
        { id: '1', title: 'Test Event 1', category: 'MUSIC' },
        { id: '2', title: 'Test Event 2', category: 'OTHER' }
      ]
      mockRedis.get.mockResolvedValue(mockEvents)

      // Act
      const result = await redisDataService.getEvents()

      // Assert
      expect(result).toEqual(mockEvents)
      expect(mockRedis.get).toHaveBeenCalled()
      expect(mockUnifiedDataService.getEvents).not.toHaveBeenCalled()
    })

    it('should fetch from database on cache miss', async () => {
      // Arrange
      const mockEvents = [
        { id: '1', title: 'Test Event 1', category: 'MUSIC' }
      ]
      mockRedis.get.mockResolvedValue(null)
      mockUnifiedDataService.getEvents.mockResolvedValue(mockEvents)
      mockRedis.setex.mockResolvedValue('OK')

      // Act
      const result = await redisDataService.getEvents()

      // Assert
      expect(result).toEqual(mockEvents)
      expect(mockUnifiedDataService.getEvents).toHaveBeenCalled()
      expect(mockRedis.setex).toHaveBeenCalled()
    })
  })

  describe('getCommunities', () => {
    it('should return cached communities on cache hit', async () => {
      // Arrange
      const mockCommunities = [
        { id: '1', name: 'Test Community 1', type: 'chat' },
        { id: '2', name: 'Test Community 2', type: 'forum' }
      ]
      mockRedis.get.mockResolvedValue(mockCommunities)

      // Act
      const result = await redisDataService.getCommunities()

      // Assert
      expect(result).toEqual(mockCommunities)
      expect(mockRedis.get).toHaveBeenCalled()
      expect(mockUnifiedDataService.getCommunities).not.toHaveBeenCalled()
    })

    it('should fetch from database on cache miss', async () => {
      // Arrange
      const mockCommunities = [
        { id: '1', name: 'Test Community 1', type: 'chat' }
      ]
      mockRedis.get.mockResolvedValue(null)
      mockUnifiedDataService.getCommunities.mockResolvedValue(mockCommunities)
      mockRedis.setex.mockResolvedValue('OK')

      // Act
      const result = await redisDataService.getCommunities()

      // Assert
      expect(result).toEqual(mockCommunities)
      expect(mockUnifiedDataService.getCommunities).toHaveBeenCalled()
      expect(mockRedis.setex).toHaveBeenCalled()
    })
  })

  describe('getAnnouncements', () => {
    it('should return cached announcements on cache hit', async () => {
      // Arrange
      const mockAnnouncements = [
        { id: '1', title: 'Test Announcement 1', priority: 'high' },
        { id: '2', title: 'Test Announcement 2', priority: 'medium' }
      ]
      mockRedis.get.mockResolvedValue(mockAnnouncements)

      // Act
      const result = await redisDataService.getAnnouncements()

      // Assert
      expect(result).toEqual(mockAnnouncements)
      expect(mockRedis.get).toHaveBeenCalled()
      expect(mockUnifiedDataService.getAnnouncements).not.toHaveBeenCalled()
    })

    it('should fetch from database on cache miss', async () => {
      // Arrange
      const mockAnnouncements = [
        { id: '1', title: 'Test Announcement 1', priority: 'high' }
      ]
      mockRedis.get.mockResolvedValue(null)
      mockUnifiedDataService.getAnnouncements.mockResolvedValue(mockAnnouncements)
      mockRedis.setex.mockResolvedValue('OK')

      // Act
      const result = await redisDataService.getAnnouncements()

      // Assert
      expect(result).toEqual(mockAnnouncements)
      expect(mockUnifiedDataService.getAnnouncements).toHaveBeenCalled()
      expect(mockRedis.setex).toHaveBeenCalled()
    })
  })

  describe('invalidateCache', () => {
    it('should invalidate activities cache', async () => {
      // Arrange
      const mockKeys = ['data:activities:all', 'data:activities:filtered']
      mockRedis.keys.mockResolvedValue(mockKeys)
      mockRedis.del.mockResolvedValue(2)

      // Act
      await redisDataService.invalidateCache('activities')

      // Assert
      expect(mockRedis.keys).toHaveBeenCalledWith('data:activities:*')
      expect(mockRedis.del).toHaveBeenCalledWith(...mockKeys)
    })

    it('should handle empty cache gracefully', async () => {
      // Arrange
      mockRedis.keys.mockResolvedValue([])

      // Act
      await redisDataService.invalidateCache('events')

      // Assert
      expect(mockRedis.keys).toHaveBeenCalledWith('data:events:*')
      expect(mockRedis.del).not.toHaveBeenCalled()
    })

    it('should handle Redis errors gracefully', async () => {
      // Arrange
      mockRedis.keys.mockRejectedValue(new Error('Redis connection failed'))

      // Act & Assert - Should not throw
      await expect(redisDataService.invalidateCache('activities')).resolves.toBeUndefined()
    })
  })

  describe('Performance Tracking', () => {
    it('should track cache hit performance', async () => {
      // Arrange
      const mockActivities = [{ id: '1', title: 'Test Activity' }]
      mockRedis.get.mockResolvedValue(mockActivities)

      // Act
      const startTime = performance.now()
      await redisDataService.getActivities()
      const endTime = performance.now()

      // Assert
      expect(endTime - startTime).toBeLessThan(100) // Should be very fast for cache hits
    })

    it('should track cache miss performance', async () => {
      // Arrange
      const mockActivities = [{ id: '1', title: 'Test Activity' }]
      mockRedis.get.mockResolvedValue(null)
      mockUnifiedDataService.getActivities.mockResolvedValue(mockActivities)
      mockRedis.setex.mockResolvedValue('OK')

      // Act
      const startTime = performance.now()
      await redisDataService.getActivities()
      const endTime = performance.now()

      // Assert
      expect(endTime - startTime).toBeLessThan(1000) // Should still be reasonably fast
    })
  })

  describe('Cache Key Generation', () => {
    it('should generate consistent cache keys for activities', async () => {
      // Arrange
      mockRedis.get.mockResolvedValue(null)
      mockUnifiedDataService.getActivities.mockResolvedValue([])
      mockRedis.setex.mockResolvedValue('OK')

      // Act
      await redisDataService.getActivities()

      // Assert
      expect(mockRedis.get).toHaveBeenCalledWith(expect.stringContaining('data:activities:'))
    })

    it('should generate different cache keys for different filters', async () => {
      // Arrange
      const filters1 = { type: 'meetup' }
      const filters2 = { type: 'workshop' }
      mockRedis.get.mockResolvedValue(null)
      mockUnifiedDataService.getActivities.mockResolvedValue([])
      mockRedis.setex.mockResolvedValue('OK')

      // Act
      await redisDataService.getActivities(filters1)
      await redisDataService.getActivities(filters2)

      // Assert
      const calls = mockRedis.get.mock.calls
      expect(calls[0][0]).not.toBe(calls[1][0]) // Different cache keys
    })
  })
})

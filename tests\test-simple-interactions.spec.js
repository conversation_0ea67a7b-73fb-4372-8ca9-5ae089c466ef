/**
 * Simple User Interactions Test
 * 
 * Basic test to verify activity cards and their interactive elements
 */

import { test, expect } from '@playwright/test';

test('Simple User Interactions Test', async ({ page }) => {
  console.log('🧪 Testing simple user interactions...');
  
  // Navigate to activities page
  await page.goto('/activities');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000);
  
  console.log('📋 Checking page elements...');
  
  // Check basic page elements
  const hasTitle = await page.locator('h1, h2').count() > 0;
  const hasSearchBar = await page.locator('input[placeholder*="Search"]').count() > 0;
  const hasTabs = await page.locator('[role="tablist"]').count() > 0;
  
  console.log(`Page elements:`);
  console.log(`  Has title: ${hasTitle}`);
  console.log(`  Has search bar: ${hasSearchBar}`);
  console.log(`  Has tabs: ${hasTabs}`);
  
  // Check for activity cards
  const activityCards = await page.locator('.card').count();
  const cardElements = await page.locator('[class*="card"]').count();
  const gridElements = await page.locator('.grid').count();
  
  console.log(`Activity content:`);
  console.log(`  Activity cards (.card): ${activityCards}`);
  console.log(`  Card elements ([class*="card"]): ${cardElements}`);
  console.log(`  Grid elements: ${gridElements}`);
  
  await page.screenshot({ path: 'test-results/simple-interactions.png', fullPage: true });
  
  // If we have cards, test interactions
  if (activityCards > 0) {
    console.log('\n🎯 Testing card interactions...');
    
    // Test Details button
    const detailsButtons = await page.locator('button:has-text("Details")').count();
    console.log(`  Details buttons: ${detailsButtons}`);
    
    if (detailsButtons > 0) {
      console.log('  Clicking first Details button...');
      await page.locator('button:has-text("Details")').first().click();
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'test-results/details-clicked.png', fullPage: true });
      console.log('  ✅ Details button clicked');
    }
    
    // Test Join button
    const joinButtons = await page.locator('button:has-text("Join")').count();
    console.log(`  Join buttons: ${joinButtons}`);
    
    if (joinButtons > 0) {
      console.log('  Clicking first Join button...');
      await page.locator('button:has-text("Join")').first().click();
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'test-results/join-clicked.png', fullPage: true });
      console.log('  ✅ Join button clicked');
    }
    
  } else {
    console.log('\n❌ No activity cards found');
    
    // Check for "No activities" message
    const noActivitiesMessage = await page.locator('text="No activities found"').count() > 0;
    console.log(`  "No activities found" message: ${noActivitiesMessage}`);
    
    // Check if data is loading
    const loadingElements = await page.locator('[class*="loading"], [data-testid*="loading"]').count() > 0;
    console.log(`  Loading elements: ${loadingElements}`);
  }
  
  // Final assessment
  const pageWorking = hasTitle && hasSearchBar && hasTabs;
  const cardsWorking = activityCards > 0;
  
  console.log(`\n📊 ASSESSMENT:`);
  console.log(`  Page interface: ${pageWorking ? '✅ WORKING' : '❌ BROKEN'}`);
  console.log(`  Activity cards: ${cardsWorking ? '✅ VISIBLE' : '❌ NOT VISIBLE'}`);
  
  if (pageWorking && cardsWorking) {
    console.log('🎉 SUCCESS: Activities page with interactive cards is working!');
  } else if (pageWorking) {
    console.log('⚠️ PARTIAL: Page loads but no activity cards visible');
  } else {
    console.log('❌ FAILED: Page interface not working properly');
  }
  
  console.log('✅ Simple interactions test completed');
});

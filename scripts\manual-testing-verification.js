#!/usr/bin/env node

/**
 * Manual Testing Verification Script
 * 
 * This script performs actual HTTP requests to verify the application
 * is running and accessible, simulating real browser interactions.
 */

import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const appUrl = 'http://localhost:5173';

console.log('🔍 EVIDENCE-BASED MANUAL TESTING VERIFICATION');
console.log('==============================================');
console.log(`Testing Application: ${appUrl}`);
console.log(`Testing Time: ${new Date().toISOString()}`);

/**
 * Test 1: Application Accessibility
 */
async function testApplicationAccessibility() {
  console.log('\n📱 Test 1: Application Accessibility');
  console.log('====================================');
  
  try {
    console.log(`🔗 Attempting to connect to: ${appUrl}`);
    const startTime = Date.now();
    
    const response = await fetch(appUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    const loadTime = Date.now() - startTime;
    
    console.log(`✅ HTTP Response Status: ${response.status} ${response.statusText}`);
    console.log(`⚡ Load Time: ${loadTime}ms`);
    console.log(`📊 Response Headers:`);
    console.log(`   - Content-Type: ${response.headers.get('content-type')}`);
    console.log(`   - Content-Length: ${response.headers.get('content-length') || 'Not specified'}`);
    
    if (response.ok) {
      const html = await response.text();
      
      // Check for key elements in the HTML
      const hasTitle = html.includes('<title>');
      const hasReact = html.includes('react') || html.includes('React');
      const hasVite = html.includes('vite') || html.includes('Vite');
      const hasSupabase = html.includes('supabase') || html.includes('Supabase');
      
      console.log(`\n🔍 HTML Content Analysis:`);
      console.log(`   - Has Title Tag: ${hasTitle ? '✅' : '❌'}`);
      console.log(`   - React References: ${hasReact ? '✅' : '❌'}`);
      console.log(`   - Vite References: ${hasVite ? '✅' : '❌'}`);
      console.log(`   - Supabase References: ${hasSupabase ? '✅' : '❌'}`);
      console.log(`   - HTML Size: ${html.length} characters`);
      
      // Extract title if present
      const titleMatch = html.match(/<title>(.*?)<\/title>/);
      if (titleMatch) {
        console.log(`   - Page Title: "${titleMatch[1]}"`);
      }
      
      return {
        accessible: true,
        loadTime,
        status: response.status,
        hasTitle,
        hasReact,
        hasVite,
        hasSupabase,
        htmlSize: html.length
      };
    } else {
      console.log(`❌ Application not accessible: ${response.status}`);
      return { accessible: false, status: response.status };
    }
  } catch (error) {
    console.log(`❌ Connection failed: ${error.message}`);
    return { accessible: false, error: error.message };
  }
}

/**
 * Test 2: Database Connectivity
 */
async function testDatabaseConnectivity() {
  console.log('\n🗄️ Test 2: Database Connectivity');
  console.log('=================================');
  
  if (!supabaseUrl || !supabaseKey) {
    console.log('❌ Supabase configuration missing');
    return { connected: false, error: 'Missing configuration' };
  }
  
  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    console.log(`🔗 Connecting to: ${supabaseUrl}`);
    
    const startTime = Date.now();
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    const queryTime = Date.now() - startTime;
    
    if (!error) {
      console.log(`✅ Database connection successful`);
      console.log(`⚡ Query Time: ${queryTime}ms`);
      console.log(`📊 Connection verified through profiles table`);
      
      // Test additional tables
      const tables = ['festivals', 'events', 'activities'];
      const tableResults = {};
      
      for (const table of tables) {
        try {
          const { data: tableData, error: tableError } = await supabase
            .from(table)
            .select('count')
            .limit(1);
          
          tableResults[table] = !tableError;
          console.log(`   - ${table} table: ${!tableError ? '✅' : '❌'}`);
        } catch (err) {
          tableResults[table] = false;
          console.log(`   - ${table} table: ❌ (${err.message})`);
        }
      }
      
      return {
        connected: true,
        queryTime,
        tablesAccessible: tableResults
      };
    } else {
      console.log(`❌ Database connection failed: ${error.message}`);
      return { connected: false, error: error.message };
    }
  } catch (error) {
    console.log(`❌ Database test failed: ${error.message}`);
    return { connected: false, error: error.message };
  }
}

/**
 * Test 3: API Endpoints Accessibility
 */
async function testApiEndpoints() {
  console.log('\n🔌 Test 3: API Endpoints Accessibility');
  console.log('======================================');
  
  const endpoints = [
    { name: 'Supabase REST API', url: `${supabaseUrl}/rest/v1/` },
    { name: 'Supabase Auth API', url: `${supabaseUrl}/auth/v1/` }
  ];
  
  const results = {};
  
  for (const endpoint of endpoints) {
    try {
      console.log(`🔗 Testing: ${endpoint.name}`);
      const startTime = Date.now();
      
      const response = await fetch(endpoint.url, {
        headers: {
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`
        }
      });
      
      const responseTime = Date.now() - startTime;
      
      console.log(`   - Status: ${response.status} ${response.statusText}`);
      console.log(`   - Response Time: ${responseTime}ms`);
      
      results[endpoint.name] = {
        accessible: response.status < 500, // 4xx is expected for some endpoints
        status: response.status,
        responseTime
      };
      
    } catch (error) {
      console.log(`   - Error: ${error.message}`);
      results[endpoint.name] = {
        accessible: false,
        error: error.message
      };
    }
  }
  
  return results;
}

/**
 * Test 4: Authentication System Verification
 */
async function testAuthenticationSystem() {
  console.log('\n🔐 Test 4: Authentication System Verification');
  console.log('=============================================');
  
  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test 1: Check current session
    console.log('🔍 Checking current session...');
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    if (!sessionError) {
      console.log(`✅ Session check successful`);
      console.log(`   - Active session: ${sessionData.session ? 'Yes' : 'No'}`);
    } else {
      console.log(`⚠️ Session check: ${sessionError.message}`);
    }
    
    // Test 2: Test user registration (with test email)
    console.log('\n🔍 Testing user registration capability...');
    const testEmail = `test.${Date.now()}@example.com`;
    
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: testEmail,
      password: 'TestPassword123!',
      options: {
        data: {
          full_name: 'Test User',
          username: `testuser${Date.now()}`
        }
      }
    });
    
    if (!signUpError) {
      console.log(`✅ Registration system working`);
      console.log(`   - Test user created: ${signUpData.user?.id || 'ID not available'}`);
      console.log(`   - Email confirmation required: ${!signUpData.user?.email_confirmed_at}`);
    } else {
      console.log(`⚠️ Registration test: ${signUpError.message}`);
    }
    
    return {
      sessionCheck: !sessionError,
      registrationWorking: !signUpError,
      testUserId: signUpData?.user?.id
    };
    
  } catch (error) {
    console.log(`❌ Authentication test failed: ${error.message}`);
    return { error: error.message };
  }
}

/**
 * Generate comprehensive verification report
 */
function generateVerificationReport(appTest, dbTest, apiTest, authTest) {
  console.log('\n📊 EVIDENCE-BASED VERIFICATION REPORT');
  console.log('=====================================');
  
  const results = {
    applicationAccessible: appTest.accessible,
    databaseConnected: dbTest.connected,
    apiEndpointsWorking: Object.values(apiTest).some(result => result.accessible),
    authenticationFunctional: authTest.sessionCheck && authTest.registrationWorking
  };
  
  console.log('\n🎯 VERIFICATION SUMMARY:');
  console.log(`Application Accessible: ${results.applicationAccessible ? '✅' : '❌'}`);
  console.log(`Database Connected: ${results.databaseConnected ? '✅' : '❌'}`);
  console.log(`API Endpoints Working: ${results.apiEndpointsWorking ? '✅' : '❌'}`);
  console.log(`Authentication Functional: ${results.authenticationFunctional ? '✅' : '❌'}`);
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  const successRate = (passedTests / totalTests * 100).toFixed(1);
  
  console.log(`\n📈 VERIFICATION SCORE: ${passedTests}/${totalTests} (${successRate}%)`);
  
  if (successRate >= 75) {
    console.log('✅ Application appears to be functional for manual testing');
  } else {
    console.log('⚠️ Application has issues that need to be resolved');
  }
  
  console.log('\n🔍 DETAILED FINDINGS:');
  if (appTest.accessible) {
    console.log(`   - App Load Time: ${appTest.loadTime}ms`);
    console.log(`   - HTML Content: ${appTest.htmlSize} characters`);
  }
  if (dbTest.connected) {
    console.log(`   - DB Query Time: ${dbTest.queryTime}ms`);
  }
  if (authTest.testUserId) {
    console.log(`   - Test User Created: ${authTest.testUserId}`);
  }
  
  return {
    score: successRate,
    passedTests,
    totalTests,
    details: { appTest, dbTest, apiTest, authTest }
  };
}

/**
 * Run comprehensive verification
 */
async function runVerification() {
  console.log('🚀 Starting Evidence-Based Verification');
  console.log('=======================================');
  
  try {
    const appTest = await testApplicationAccessibility();
    const dbTest = await testDatabaseConnectivity();
    const apiTest = await testApiEndpoints();
    const authTest = await testAuthenticationSystem();
    
    const report = generateVerificationReport(appTest, dbTest, apiTest, authTest);
    
    console.log('\n🏁 Verification completed');
    console.log(`Final Score: ${report.score}%`);
    
    return report;
  } catch (error) {
    console.error('\n💥 Verification failed:', error);
    throw error;
  }
}

// Run the verification
runVerification()
  .then(() => {
    console.log('\n✅ Evidence-based verification completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Verification failed:', error);
    process.exit(1);
  });

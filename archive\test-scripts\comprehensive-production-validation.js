/**
 * Comprehensive Production Validation for Festival Family
 * 
 * This script provides evidence-based validation of all critical systems
 * including admin UX issues, session persistence, and complete functionality
 */

import { chromium } from 'playwright';
import fs from 'fs';
import path from 'path';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

const APP_URL = 'http://localhost:5173';

// Create results directory
const resultsDir = 'production-validation-results';
if (!fs.existsSync(resultsDir)) {
  fs.mkdirSync(resultsDir, { recursive: true });
}

console.log('🔍 COMPREHENSIVE PRODUCTION VALIDATION STARTING');
console.log('==============================================');

async function validateAdminUXIssues(page) {
  console.log('\n🎛️ Testing Admin UX Issues');
  console.log('---------------------------');
  
  const results = {
    returnToUserViewButton: false,
    sessionPersistence: {
      pageRefresh: false,
      profileNavigation: false,
      browserBack: false
    },
    loadingStates: {
      adminSessionLoss: false,
      endlessLoading: false
    },
    timestamp: new Date().toISOString()
  };
  
  try {
    // Test 1: Sign in as admin
    await page.goto(`${APP_URL}/auth`);
    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    
    // Wait for authentication
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    
    // Test 2: Navigate to admin dashboard
    await page.goto(`${APP_URL}/admin`);
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of admin dashboard
    await page.screenshot({ 
      path: `${resultsDir}/01-admin-dashboard.png`,
      fullPage: true 
    });
    
    // Test 3: Check for "Return to User View" button
    const returnButton = await page.locator('text="Return to User View"').count();
    results.returnToUserViewButton = returnButton > 0;
    
    console.log(`${results.returnToUserViewButton ? '✅' : '❌'} Return to User View button: ${results.returnToUserViewButton ? 'Found' : 'Missing'}`);
    
    // Test 4: Session persistence - page refresh
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    const adminContentAfterRefresh = await page.locator('text="Admin Dashboard"').count();
    results.sessionPersistence.pageRefresh = adminContentAfterRefresh > 0;
    
    console.log(`${results.sessionPersistence.pageRefresh ? '✅' : '❌'} Session persistence (refresh): ${results.sessionPersistence.pageRefresh ? 'Working' : 'Failed'}`);
    
    // Test 5: Session persistence - profile navigation
    await page.goto(`${APP_URL}/profile`);
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ 
      path: `${resultsDir}/02-profile-navigation.png`,
      fullPage: true 
    });
    
    // Navigate back to admin
    await page.goto(`${APP_URL}/admin`);
    await page.waitForLoadState('networkidle');
    
    const adminContentAfterProfile = await page.locator('text="Admin Dashboard"').count();
    results.sessionPersistence.profileNavigation = adminContentAfterProfile > 0;
    
    console.log(`${results.sessionPersistence.profileNavigation ? '✅' : '❌'} Session persistence (profile nav): ${results.sessionPersistence.profileNavigation ? 'Working' : 'Failed'}`);
    
    // Test 6: Browser back button
    await page.goBack();
    await page.waitForLoadState('networkidle');
    await page.goForward();
    await page.waitForLoadState('networkidle');
    
    const adminContentAfterBack = await page.locator('text="Admin Dashboard"').count();
    results.sessionPersistence.browserBack = adminContentAfterBack > 0;
    
    console.log(`${results.sessionPersistence.browserBack ? '✅' : '❌'} Session persistence (browser back): ${results.sessionPersistence.browserBack ? 'Working' : 'Failed'}`);
    
    // Test 7: Loading states
    const loadingElements = await page.locator('[data-testid="loading"], .loading, .spinner').count();
    results.loadingStates.endlessLoading = loadingElements === 0;
    
    console.log(`${results.loadingStates.endlessLoading ? '✅' : '❌'} No endless loading: ${results.loadingStates.endlessLoading ? 'Clean' : 'Issues detected'}`);
    
  } catch (error) {
    console.log(`💥 Admin UX test error: ${error.message}`);
  }
  
  return results;
}

async function validateAuthenticationFlows(page) {
  console.log('\n🔐 Testing Authentication Flows');
  console.log('-------------------------------');
  
  const results = {
    signUp: false,
    signIn: false,
    signOut: false,
    sessionPersistence: false,
    timestamp: new Date().toISOString()
  };
  
  try {
    // Test sign out first (if logged in)
    await page.goto(`${APP_URL}/dashboard`);
    
    const signOutButton = await page.locator('text="Sign Out", text="Logout", button:has-text("Sign Out")').first();
    if (await signOutButton.count() > 0) {
      await signOutButton.click();
      await page.waitForURL('**/auth', { timeout: 5000 });
      results.signOut = true;
    }
    
    // Test sign in
    await page.goto(`${APP_URL}/auth`);
    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    results.signIn = true;
    
    // Test session persistence
    await page.reload();
    const dashboardContent = await page.locator('text="Dashboard", text="Welcome"').count();
    results.sessionPersistence = dashboardContent > 0;
    
    console.log(`${results.signIn ? '✅' : '❌'} Sign In: ${results.signIn ? 'Working' : 'Failed'}`);
    console.log(`${results.signOut ? '✅' : '❌'} Sign Out: ${results.signOut ? 'Working' : 'Failed'}`);
    console.log(`${results.sessionPersistence ? '✅' : '❌'} Session Persistence: ${results.sessionPersistence ? 'Working' : 'Failed'}`);
    
  } catch (error) {
    console.log(`💥 Authentication test error: ${error.message}`);
  }
  
  return results;
}

async function validateAdminCRUDOperations(page) {
  console.log('\n📝 Testing Admin CRUD Operations');
  console.log('--------------------------------');
  
  const results = {
    contentManagement: { accessible: false, crud: false },
    emergencyManagement: { accessible: false, crud: false },
    announcements: { accessible: false, crud: false },
    tips: { accessible: false, crud: false },
    faqs: { accessible: false, crud: false },
    timestamp: new Date().toISOString()
  };
  
  try {
    // Ensure admin is logged in
    await page.goto(`${APP_URL}/admin`);
    await page.waitForLoadState('networkidle');
    
    // Test Content Management
    await page.goto(`${APP_URL}/admin/content`);
    await page.waitForLoadState('networkidle');
    
    const contentPage = await page.locator('text="Content Management"').count();
    results.contentManagement.accessible = contentPage > 0;
    
    if (results.contentManagement.accessible) {
      // Test if we can see content items
      const contentItems = await page.locator('[data-testid="content-item"], .content-item, table tr').count();
      results.contentManagement.crud = contentItems > 0;
    }
    
    await page.screenshot({ 
      path: `${resultsDir}/03-content-management.png`,
      fullPage: true 
    });
    
    // Test Emergency Management
    await page.goto(`${APP_URL}/admin/emergency`);
    await page.waitForLoadState('networkidle');
    
    const emergencyPage = await page.locator('text="Emergency Management"').count();
    results.emergencyManagement.accessible = emergencyPage > 0;
    
    await page.screenshot({ 
      path: `${resultsDir}/04-emergency-management.png`,
      fullPage: true 
    });
    
    // Test Announcements
    await page.goto(`${APP_URL}/admin/announcements`);
    await page.waitForLoadState('networkidle');
    
    const announcementsPage = await page.locator('text="Announcements"').count();
    results.announcements.accessible = announcementsPage > 0;
    
    await page.screenshot({ 
      path: `${resultsDir}/05-announcements.png`,
      fullPage: true 
    });
    
    // Test Tips
    await page.goto(`${APP_URL}/admin/tips`);
    await page.waitForLoadState('networkidle');
    
    const tipsPage = await page.locator('text="Tips"').count();
    results.tips.accessible = tipsPage > 0;
    
    await page.screenshot({ 
      path: `${resultsDir}/06-tips.png`,
      fullPage: true 
    });
    
    // Test FAQs
    await page.goto(`${APP_URL}/admin/faqs`);
    await page.waitForLoadState('networkidle');
    
    const faqsPage = await page.locator('text="FAQ"').count();
    results.faqs.accessible = faqsPage > 0;
    
    await page.screenshot({ 
      path: `${resultsDir}/07-faqs.png`,
      fullPage: true 
    });
    
    console.log(`${results.contentManagement.accessible ? '✅' : '❌'} Content Management: ${results.contentManagement.accessible ? 'Accessible' : 'Not accessible'}`);
    console.log(`${results.emergencyManagement.accessible ? '✅' : '❌'} Emergency Management: ${results.emergencyManagement.accessible ? 'Accessible' : 'Not accessible'}`);
    console.log(`${results.announcements.accessible ? '✅' : '❌'} Announcements: ${results.announcements.accessible ? 'Accessible' : 'Not accessible'}`);
    console.log(`${results.tips.accessible ? '✅' : '❌'} Tips: ${results.tips.accessible ? 'Accessible' : 'Not accessible'}`);
    console.log(`${results.faqs.accessible ? '✅' : '❌'} FAQs: ${results.faqs.accessible ? 'Accessible' : 'Not accessible'}`);
    
  } catch (error) {
    console.log(`💥 Admin CRUD test error: ${error.message}`);
  }
  
  return results;
}

async function validateProfileSystem(page) {
  console.log('\n👤 Testing Profile System');
  console.log('-------------------------');
  
  const results = {
    accessible: false,
    editMode: false,
    databaseIntegration: false,
    avatarUpload: false,
    timestamp: new Date().toISOString()
  };
  
  try {
    await page.goto(`${APP_URL}/profile`);
    await page.waitForLoadState('networkidle');
    
    const profileContent = await page.locator('text="Profile", text="Edit Profile"').count();
    results.accessible = profileContent > 0;
    
    // Test edit mode
    const editButton = await page.locator('button:has-text("Edit"), button:has-text("Edit Profile")').first();
    if (await editButton.count() > 0) {
      await editButton.click();
      await page.waitForTimeout(1000);
      
      const editForm = await page.locator('input[name="full_name"], input[placeholder*="name"]').count();
      results.editMode = editForm > 0;
    }
    
    await page.screenshot({ 
      path: `${resultsDir}/08-profile-system.png`,
      fullPage: true 
    });
    
    console.log(`${results.accessible ? '✅' : '❌'} Profile Accessible: ${results.accessible ? 'Yes' : 'No'}`);
    console.log(`${results.editMode ? '✅' : '❌'} Edit Mode: ${results.editMode ? 'Working' : 'Not working'}`);
    
  } catch (error) {
    console.log(`💥 Profile system test error: ${error.message}`);
  }
  
  return results;
}

async function validateMobileResponsiveness(page) {
  console.log('\n📱 Testing Mobile Responsiveness');
  console.log('--------------------------------');
  
  const results = {
    mobile: false,
    tablet: false,
    desktop: false,
    touchInteractions: false,
    timestamp: new Date().toISOString()
  };
  
  try {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto(`${APP_URL}/dashboard`);
    await page.waitForLoadState('networkidle');
    
    const mobileNav = await page.locator('[data-testid="mobile-nav"], .mobile-nav, .hamburger').count();
    results.mobile = mobileNav > 0 || await page.locator('nav').count() > 0;
    
    await page.screenshot({ 
      path: `${resultsDir}/09-mobile-view.png`,
      fullPage: true 
    });
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    results.tablet = await page.locator('nav, [role="navigation"]').count() > 0;
    
    await page.screenshot({ 
      path: `${resultsDir}/10-tablet-view.png`,
      fullPage: true 
    });
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    results.desktop = await page.locator('nav, [role="navigation"]').count() > 0;
    
    await page.screenshot({ 
      path: `${resultsDir}/11-desktop-view.png`,
      fullPage: true 
    });
    
    console.log(`${results.mobile ? '✅' : '❌'} Mobile Responsive: ${results.mobile ? 'Yes' : 'No'}`);
    console.log(`${results.tablet ? '✅' : '❌'} Tablet Responsive: ${results.tablet ? 'Yes' : 'No'}`);
    console.log(`${results.desktop ? '✅' : '❌'} Desktop Responsive: ${results.desktop ? 'Yes' : 'No'}`);
    
  } catch (error) {
    console.log(`💥 Mobile responsiveness test error: ${error.message}`);
  }
  
  return results;
}

async function main() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    const validationResults = {
      validationSuite: 'Comprehensive Production Validation',
      timestamp: new Date().toISOString(),
      adminUXIssues: await validateAdminUXIssues(page),
      authenticationFlows: await validateAuthenticationFlows(page),
      adminCRUDOperations: await validateAdminCRUDOperations(page),
      profileSystem: await validateProfileSystem(page),
      mobileResponsiveness: await validateMobileResponsiveness(page)
    };
    
    // Save comprehensive results
    fs.writeFileSync(
      `${resultsDir}/comprehensive-validation-${Date.now()}.json`,
      JSON.stringify(validationResults, null, 2)
    );
    
    // Generate summary report
    console.log('\n🎉 COMPREHENSIVE VALIDATION SUMMARY');
    console.log('===================================');
    
    console.log('\n🎛️ ADMIN UX ISSUES:');
    console.log(`   Return to User View Button: ${validationResults.adminUXIssues.returnToUserViewButton ? '✅ Found' : '❌ Missing'}`);
    console.log(`   Session Persistence (Refresh): ${validationResults.adminUXIssues.sessionPersistence.pageRefresh ? '✅ Working' : '❌ Failed'}`);
    console.log(`   Session Persistence (Profile Nav): ${validationResults.adminUXIssues.sessionPersistence.profileNavigation ? '✅ Working' : '❌ Failed'}`);
    console.log(`   Session Persistence (Browser Back): ${validationResults.adminUXIssues.sessionPersistence.browserBack ? '✅ Working' : '❌ Failed'}`);
    console.log(`   No Endless Loading: ${validationResults.adminUXIssues.loadingStates.endlessLoading ? '✅ Clean' : '❌ Issues'}`);
    
    console.log('\n🔐 AUTHENTICATION FLOWS:');
    console.log(`   Sign In: ${validationResults.authenticationFlows.signIn ? '✅ Working' : '❌ Failed'}`);
    console.log(`   Sign Out: ${validationResults.authenticationFlows.signOut ? '✅ Working' : '❌ Failed'}`);
    console.log(`   Session Persistence: ${validationResults.authenticationFlows.sessionPersistence ? '✅ Working' : '❌ Failed'}`);
    
    console.log('\n📝 ADMIN CRUD OPERATIONS:');
    console.log(`   Content Management: ${validationResults.adminCRUDOperations.contentManagement.accessible ? '✅ Accessible' : '❌ Not accessible'}`);
    console.log(`   Emergency Management: ${validationResults.adminCRUDOperations.emergencyManagement.accessible ? '✅ Accessible' : '❌ Not accessible'}`);
    console.log(`   Announcements: ${validationResults.adminCRUDOperations.announcements.accessible ? '✅ Accessible' : '❌ Not accessible'}`);
    console.log(`   Tips: ${validationResults.adminCRUDOperations.tips.accessible ? '✅ Accessible' : '❌ Not accessible'}`);
    console.log(`   FAQs: ${validationResults.adminCRUDOperations.faqs.accessible ? '✅ Accessible' : '❌ Not accessible'}`);
    
    console.log('\n👤 PROFILE SYSTEM:');
    console.log(`   Accessible: ${validationResults.profileSystem.accessible ? '✅ Yes' : '❌ No'}`);
    console.log(`   Edit Mode: ${validationResults.profileSystem.editMode ? '✅ Working' : '❌ Not working'}`);
    
    console.log('\n📱 MOBILE RESPONSIVENESS:');
    console.log(`   Mobile: ${validationResults.mobileResponsiveness.mobile ? '✅ Responsive' : '❌ Issues'}`);
    console.log(`   Tablet: ${validationResults.mobileResponsiveness.tablet ? '✅ Responsive' : '❌ Issues'}`);
    console.log(`   Desktop: ${validationResults.mobileResponsiveness.desktop ? '✅ Responsive' : '❌ Issues'}`);
    
    console.log(`\n📁 Results saved to: ${resultsDir}/`);
    console.log(`📸 Screenshots saved for evidence`);
    
  } catch (error) {
    console.error('💥 Comprehensive validation failed:', error);
  } finally {
    await browser.close();
  }
}

main();

/**
 * Local Information Management Admin Interface
 *
 * Comprehensive admin interface for managing local information including
 * venues, transport, accommodation, and other local resources with CRUD operations.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  Plus,
  Edit,
  Trash2,
  Search,
  Filter,
  MapPin,
  Star,
  Eye,
  EyeOff,
  ExternalLink,
  Save,
  X,
  AlertCircle
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { ResilientErrorBoundary } from '@/components/error/ResilientErrorBoundary';
import { useOptimizedRealtime } from '@/hooks/realtime/useOptimizedRealtime';
import { supabase } from '@/lib/supabase';

interface LocalInfoItem {
  id: string;
  title: string;
  description: string | null;
  category: string;
  link?: string | null;
  is_active: boolean | null;
  is_featured: boolean | null;
  priority: number | null;
  created_by?: string | null;
  created_at: string | null;
  updated_at: string | null;
}

const CATEGORIES = [
  'accommodation',
  'transportation',
  'food',
  'safety',
  'attractions',
  'weather',
  'shopping',
  'entertainment',
  'services'
];

const CATEGORY_COLORS = {
  accommodation: 'bg-primary/20 text-primary',
  transportation: 'bg-festival-success/20 text-festival-success',
  food: 'bg-festival-warning/20 text-festival-warning',
  safety: 'bg-destructive/20 text-destructive',
  attractions: 'bg-accent/20 text-accent-foreground',
  weather: 'bg-festival-info/20 text-festival-info',
  shopping: 'bg-secondary/20 text-secondary-foreground',
  entertainment: 'bg-festival-orange/20 text-festival-orange',
  services: 'bg-muted/20 text-muted-foreground'
};

const LocalInfoManagement: React.FC = () => {
  const navigate = useNavigate();
  const [localInfoItems, setLocalInfoItems] = useState<LocalInfoItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showInactive, setShowInactive] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<LocalInfoItem | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    link: '',
    is_active: true,
    is_featured: false,
    priority: 5
  });

  // Real-time subscription for local info updates
  useOptimizedRealtime('local_info', ['admin', 'local_info'], {
    event: '*',
    priority: 'high',
    callback: (payload) => {
      console.log('🔄 Local Info real-time update:', payload.eventType);
      fetchLocalInfo(); // Refresh data on any change
    }
  });

  // Fetch local info data
  const fetchLocalInfo = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('local_info')
        .select('*')
        .order('priority', { ascending: false })
        .order('category', { ascending: true })
        .order('created_at', { ascending: false });

      if (error) throw error;

      setLocalInfoItems(data || []);
      console.log('✅ Local Info: Fetched', data?.length || 0, 'items');
    } catch (error) {
      console.error('❌ Error fetching local info:', error);
      toast.error('Failed to fetch local information');
    } finally {
      setIsLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchLocalInfo();
  }, []);

  // Filter items based on search and category
  const filteredItems = localInfoItems.filter(item => {
    const matchesSearch = 
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (item.description && item.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
      item.category.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    const matchesActive = showInactive || item.is_active;
    
    return matchesSearch && matchesCategory && matchesActive;
  });

  // Reset form
  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      category: '',
      link: '',
      is_active: true,
      is_featured: false,
      priority: 5
    });
    setEditingItem(null);
  };

  // Handle create/edit
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim() || !formData.description.trim() || !formData.category) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);

    try {
      const submitData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        category: formData.category,
        link: formData.link.trim() || null,
        is_active: formData.is_active,
        is_featured: formData.is_featured,
        priority: formData.priority,
        updated_at: new Date().toISOString()
      };

      if (editingItem) {
        // Update existing item
        const { error } = await supabase
          .from('local_info')
          .update(submitData)
          .eq('id', editingItem.id);

        if (error) throw error;

        toast.success('Local information updated successfully');
        console.log('✅ Local Info: Updated item', editingItem.id);
      } else {
        // Create new item
        const { error } = await supabase
          .from('local_info')
          .insert([submitData]);

        if (error) throw error;

        toast.success('Local information created successfully');
        console.log('✅ Local Info: Created new item');
      }

      resetForm();
      setIsCreateModalOpen(false);
      fetchLocalInfo();
    } catch (error) {
      console.error('❌ Error saving local info:', error);
      toast.error('Failed to save local information');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete
  const handleDelete = async (item: LocalInfoItem) => {
    if (!confirm(`Are you sure you want to delete "${item.title}"?`)) {
      return;
    }

    try {
      const { error } = await supabase
        .from('local_info')
        .delete()
        .eq('id', item.id);

      if (error) throw error;

      toast.success('Local information deleted successfully');
      console.log('✅ Local Info: Deleted item', item.id);
      fetchLocalInfo();
    } catch (error) {
      console.error('❌ Error deleting local info:', error);
      toast.error('Failed to delete local information');
    }
  };

  // Handle edit
  const handleEdit = (item: LocalInfoItem) => {
    setFormData({
      title: item.title,
      description: item.description || '',
      category: item.category,
      link: item.link || '',
      is_active: item.is_active || false,
      is_featured: item.is_featured || false,
      priority: item.priority || 5
    });
    setEditingItem(item);
    setIsCreateModalOpen(true);
  };

  // Toggle active status
  const toggleActiveStatus = async (item: LocalInfoItem) => {
    try {
      const { error } = await supabase
        .from('local_info')
        .update({ 
          is_active: !item.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', item.id);

      if (error) throw error;

      toast.success(`Local information ${!item.is_active ? 'activated' : 'deactivated'}`);
      fetchLocalInfo();
    } catch (error) {
      console.error('❌ Error toggling status:', error);
      toast.error('Failed to update status');
    }
  };

  // Toggle featured status
  const toggleFeaturedStatus = async (item: LocalInfoItem) => {
    try {
      const { error } = await supabase
        .from('local_info')
        .update({ 
          is_featured: !item.is_featured,
          updated_at: new Date().toISOString()
        })
        .eq('id', item.id);

      if (error) throw error;

      toast.success(`Local information ${!item.is_featured ? 'featured' : 'unfeatured'}`);
      fetchLocalInfo();
    } catch (error) {
      console.error('❌ Error toggling featured status:', error);
      toast.error('Failed to update featured status');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <ResilientErrorBoundary
      fallbackType="component-crash"
      isolationLevel="section"
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Local Information Management</h1>
            <p className="text-muted-foreground mt-2">
              Manage venues, transport, accommodation, and other local resources
            </p>
          </div>
          
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <Plus className="mr-2 h-4 w-4" />
                Add Local Info
              </Button>
            </DialogTrigger>
          </Dialog>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search local information..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-full md:w-48">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {CATEGORIES.map(category => (
                    <SelectItem key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <div className="flex items-center space-x-2">
                <Switch
                  id="show-inactive"
                  checked={showInactive}
                  onCheckedChange={setShowInactive}
                />
                <Label htmlFor="show-inactive">Show Inactive</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold">{localInfoItems.length}</div>
              <p className="text-xs text-muted-foreground">Total Items</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold">
                {localInfoItems.filter(item => item.is_active).length}
              </div>
              <p className="text-xs text-muted-foreground">Active Items</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold">
                {localInfoItems.filter(item => item.is_featured).length}
              </div>
              <p className="text-xs text-muted-foreground">Featured Items</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold">
                {new Set(localInfoItems.map(item => item.category)).size}
              </div>
              <p className="text-xs text-muted-foreground">Categories</p>
            </CardContent>
          </Card>
        </div>

        {/* Local Info Items */}
        <div className="grid gap-4">
          {filteredItems.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <MapPin className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-semibold">No local information found</h3>
                  <p className="text-muted-foreground">
                    {searchQuery || selectedCategory !== 'all' 
                      ? 'Try adjusting your search or filters'
                      : 'Get started by adding your first local information item'
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            filteredItems.map((item) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Card className={`${!item.is_active ? 'opacity-60' : ''}`}>
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="text-lg font-semibold">{item.title}</h3>
                          <Badge 
                            className={CATEGORY_COLORS[item.category as keyof typeof CATEGORY_COLORS] || CATEGORY_COLORS.services}
                          >
                            {item.category}
                          </Badge>
                          {item.is_featured && (
                            <Badge variant="secondary">
                              <Star className="mr-1 h-3 w-3" />
                              Featured
                            </Badge>
                          )}
                          <Badge variant="outline">
                            Priority: {item.priority}
                          </Badge>
                        </div>
                        
                        <p className="text-muted-foreground mb-3">{item.description}</p>
                        
                        {item.link && (
                          <a
                            href={item.link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center text-sm text-primary hover:underline"
                          >
                            <ExternalLink className="mr-1 h-3 w-3" />
                            View Link
                          </a>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleActiveStatus(item)}
                          title={item.is_active ? 'Deactivate' : 'Activate'}
                        >
                          {item.is_active ? (
                            <Eye className="h-4 w-4" />
                          ) : (
                            <EyeOff className="h-4 w-4" />
                          )}
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleFeaturedStatus(item)}
                          title={item.is_featured ? 'Unfeature' : 'Feature'}
                        >
                          <Star className={`h-4 w-4 ${item.is_featured ? 'fill-current' : ''}`} />
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(item)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(item)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))
          )}
        </div>

        {/* Create/Edit Modal */}
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingItem ? 'Edit Local Information' : 'Add Local Information'}
            </DialogTitle>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="Enter title"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => setFormData({ ...formData, category: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {CATEGORIES.map(category => (
                      <SelectItem key={category} value={category}>
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Enter description"
                rows={3}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="link">Link (Optional)</Label>
              <Input
                id="link"
                type="url"
                value={formData.link}
                onChange={(e) => setFormData({ ...formData, link: e.target.value })}
                placeholder="https://example.com"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select
                  value={formData.priority.toString()}
                  onValueChange={(value) => setFormData({ ...formData, priority: parseInt(value) })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(num => (
                      <SelectItem key={num} value={num.toString()}>
                        {num} {num === 10 ? '(Highest)' : num === 1 ? '(Lowest)' : ''}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                />
                <Label htmlFor="is_active">Active</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_featured"
                  checked={formData.is_featured}
                  onCheckedChange={(checked) => setFormData({ ...formData, is_featured: checked })}
                />
                <Label htmlFor="is_featured">Featured</Label>
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  resetForm();
                  setIsCreateModalOpen(false);
                }}
                disabled={isSubmitting}
              >
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <LoadingSpinner size="sm" className="mr-2" />
                ) : (
                  <Save className="mr-2 h-4 w-4" />
                )}
                {editingItem ? 'Update' : 'Create'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </div>
    </ResilientErrorBoundary>
  );
};

export default LocalInfoManagement;

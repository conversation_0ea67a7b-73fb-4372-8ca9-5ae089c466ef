# Supabase Integration Implementation Guide

This guide provides step-by-step instructions for implementing the solutions to Supabase integration issues.

## Prerequisites

- Ensure you have the latest code from the repository
- Install all dependencies with `npm install`
- Make sure you have the correct environment variables set

## Implementation Steps

### 1. Centralize Supabase Client

#### Step 1.1: Create the centralized client file

```typescript
// src/lib/supabase/client.ts
import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/database'

// Environment validation
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error(
    'Missing Supabase environment variables. Ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set.'
  )
}

// Create and export a single client instance
export const supabase = createClient<Database>(supabaseUrl, supabase<PERSON><PERSON><PERSON><PERSON>, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    storage: window.localStorage,
  }
})
```

#### Step 1.2: Remove duplicate client initializations

1. Identify all files that initialize the Supabase client:
   - `src/lib/supabase.ts`
   - Any other files that create a Supabase client

2. Replace imports in these files to use the centralized client:

```typescript
// Before
import { createClient } from '@supabase/supabase-js'
const supabase = createClient(...)

// After
import { supabase } from '@/lib/supabase/client'
```

#### Step 1.3: Update imports across the codebase

Search for all imports of the old Supabase client and update them to use the new centralized client:

```bash
# Find all files importing the old client
grep -r "from '.*supabase'" --include="*.ts" --include="*.tsx" src/

# Update each file to import from the new location
```

### 2. Implement Service Layer

#### Step 2.1: Create base service class

```typescript
// src/lib/supabase/services/base-service.ts
import { supabase } from '../client'
import type { PostgrestError } from '@supabase/supabase-js'

export type ServiceResponse<T> = {
  data: T | null
  error: PostgrestError | Error | null
}

export abstract class BaseService {
  protected async handleResponse<T>(
    promise: Promise<{ data: T | null; error: PostgrestError | null }>
  ): Promise<ServiceResponse<T>> {
    try {
      const { data, error } = await promise
      return { data, error }
    } catch (error) {
      return { data: null, error: error as Error }
    }
  }
}
```

#### Step 2.2: Create service classes for each domain

Create service classes for each domain in your application:

1. Auth Service:

```typescript
// src/lib/supabase/services/auth-service.ts
import { BaseService, ServiceResponse } from './base-service'
import { supabase } from '../client'
import type { User } from '@supabase/supabase-js'
import type { Profile } from '@/types/database'

export type LoginCredentials = {
  email: string
  password: string
}

export type AuthResponse = ServiceResponse<User> & {
  profile: Profile | null
}

export class AuthService extends BaseService {
  async signIn({ email, password }: LoginCredentials): Promise<AuthResponse> {
    try {
      const { data: authData, error: authError } = await supabase.auth
        .signInWithPassword({ email, password })

      if (authError) {
        return { data: null, error: authError, profile: null }
      }

      if (!authData.user) {
        return { 
          data: null, 
          error: new Error('User not found'), 
          profile: null 
        }
      }

      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select()
        .eq('id', authData.user.id)
        .single()

      if (profileError) {
        return { 
          data: authData.user, 
          error: profileError, 
          profile: null 
        }
      }

      return { 
        data: authData.user, 
        error: null, 
        profile: profile as Profile 
      }
    } catch (error) {
      return { 
        data: null, 
        error: error as Error, 
        profile: null 
      }
    }
  }

  // Additional methods...
}

export const authService = new AuthService()
```

2. Profile Service:

```typescript
// src/lib/supabase/services/profile-service.ts
import { BaseService, ServiceResponse } from './base-service'
import { supabase } from '../client'
import type { Profile } from '@/types/database'

export class ProfileService extends BaseService {
  async getProfile(userId: string): Promise<ServiceResponse<Profile>> {
    return this.handleResponse(
      supabase.from('profiles').select().eq('id', userId).single()
    )
  }
  
  // Additional methods...
}

export const profileService = new ProfileService()
```

3. Festival Service:

```typescript
// src/lib/supabase/services/festival-service.ts
import { BaseService, ServiceResponse } from './base-service'
import { supabase } from '../client'
import type { Festival, Activity } from '@/types/database'

export class FestivalService extends BaseService {
  async getFestivalsList(): Promise<ServiceResponse<Festival[]>> {
    return this.handleResponse(
      supabase.from('festivals').select()
    )
  }
  
  async getFestivalWithActivities(festivalId: string): Promise<ServiceResponse<Festival & { activities: Activity[] }>> {
    return this.handleResponse(
      supabase
        .from('festivals')
        .select(`
          *,
          activities(*)
        `)
        .eq('id', festivalId)
        .single()
    )
  }
  
  // Additional methods...
}

export const festivalService = new FestivalService()
```

#### Step 2.3: Create an index file to export all services

```typescript
// src/lib/supabase/services/index.ts
export * from './auth-service'
export * from './profile-service'
export * from './festival-service'
// Export other services...
```

#### Step 2.4: Update components to use the service layer

Replace direct Supabase client usage with service calls:

```typescript
// Before
import { supabase } from '@/lib/supabase'

async function handleLogin() {
  const { data, error } = await supabase.auth
    .signInWithPassword({ email, password })
  // ...
}

// After
import { authService } from '@/lib/supabase/services'

async function handleLogin() {
  const { data, error, profile } = await authService.signIn({ email, password })
  // ...
}
```

### 3
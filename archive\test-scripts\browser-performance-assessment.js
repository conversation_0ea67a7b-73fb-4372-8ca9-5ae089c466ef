/**
 * Browser Performance Assessment
 * 
 * This script uses <PERSON><PERSON> to measure browser-side performance including:
 * - Page load times
 * - Core Web Vitals (LCP, FID, CLS)
 * - Mobile responsiveness
 * - Authentication flow performance
 * - Bundle size and resource loading
 */

import { chromium } from 'playwright';
import fs from 'fs';

console.log('🌐 Browser Performance Assessment');
console.log('=================================');

async function browserPerformanceAssessment() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  const performanceResults = {
    timestamp: new Date().toISOString(),
    pageLoad: {},
    coreWebVitals: {},
    authentication: {},
    mobile: {},
    resources: {},
    recommendations: []
  };

  try {
    // Test 1: Page Load Performance
    console.log('⚡ Test 1: Page Load Performance Assessment');
    console.log('------------------------------------------');
    
    console.log('🔍 Testing landing page load time...');
    const loadStart = performance.now();
    
    await page.goto('http://localhost:5182/', { waitUntil: 'networkidle' });
    
    const loadEnd = performance.now();
    const pageLoadTime = loadEnd - loadStart;
    
    console.log(`   ⏱️ Landing Page Load: ${pageLoadTime.toFixed(2)}ms`);
    performanceResults.pageLoad.landingPage = pageLoadTime;
    
    // Test Core Web Vitals
    console.log('🔍 Measuring Core Web Vitals...');
    
    const webVitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals = {};
        
        // Largest Contentful Paint (LCP)
        new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          const lastEntry = entries[entries.length - 1];
          vitals.lcp = lastEntry.startTime;
        }).observe({ entryTypes: ['largest-contentful-paint'] });
        
        // First Input Delay (FID) - simulated
        vitals.fid = 0; // Will be measured during interaction
        
        // Cumulative Layout Shift (CLS)
        let clsValue = 0;
        new PerformanceObserver((entryList) => {
          for (const entry of entryList.getEntries()) {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          }
          vitals.cls = clsValue;
        }).observe({ entryTypes: ['layout-shift'] });
        
        // Wait for measurements
        setTimeout(() => {
          resolve(vitals);
        }, 3000);
      });
    });
    
    console.log(`   📊 LCP (Largest Contentful Paint): ${webVitals.lcp?.toFixed(2) || 'N/A'}ms`);
    console.log(`   📊 CLS (Cumulative Layout Shift): ${webVitals.cls?.toFixed(4) || 'N/A'}`);
    
    performanceResults.coreWebVitals = webVitals;
    
    // Test 2: Authentication Flow Performance
    console.log('');
    console.log('🔐 Test 2: Authentication Flow Performance');
    console.log('----------------------------------------');
    
    console.log('🔍 Testing authentication page load...');
    const authPageStart = performance.now();
    
    await page.goto('http://localhost:5182/auth', { waitUntil: 'networkidle' });
    
    const authPageEnd = performance.now();
    const authPageLoadTime = authPageEnd - authPageStart;
    
    console.log(`   ⏱️ Auth Page Load: ${authPageLoadTime.toFixed(2)}ms`);
    performanceResults.pageLoad.authPage = authPageLoadTime;
    
    // Test authentication form interaction
    console.log('🔍 Testing authentication form performance...');
    const authFormStart = performance.now();
    
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'testpassword123');
    
    const authSubmitStart = performance.now();
    await page.click('button[type="submit"]');
    
    // Wait for navigation or success indication
    try {
      await page.waitForURL('http://localhost:5182/', { timeout: 10000 });
      const authSubmitEnd = performance.now();
      const authFlowTime = authSubmitEnd - authSubmitStart;
      
      console.log(`   ⏱️ Auth Form Submission: ${authFlowTime.toFixed(2)}ms`);
      performanceResults.authentication.formSubmission = authFlowTime;
      
      const totalAuthTime = authPageLoadTime + authFlowTime;
      console.log(`   ⏱️ Total Auth Flow: ${totalAuthTime.toFixed(2)}ms`);
      performanceResults.authentication.totalFlow = totalAuthTime;
      
    } catch (error) {
      console.log('⚠️ Authentication flow timeout or error');
      performanceResults.authentication.error = 'Timeout or navigation error';
    }
    
    // Test 3: Mobile Responsiveness Performance
    console.log('');
    console.log('📱 Test 3: Mobile Responsiveness Performance');
    console.log('------------------------------------------');
    
    const mobileViewports = [
      { name: 'iPhone 12', width: 390, height: 844 },
      { name: 'Samsung Galaxy S21', width: 384, height: 854 },
      { name: 'iPad', width: 768, height: 1024 }
    ];
    
    for (const viewport of mobileViewports) {
      console.log(`🔍 Testing ${viewport.name} (${viewport.width}x${viewport.height})...`);
      
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      
      const mobileLoadStart = performance.now();
      await page.reload({ waitUntil: 'networkidle' });
      const mobileLoadEnd = performance.now();
      const mobileLoadTime = mobileLoadEnd - mobileLoadStart;
      
      console.log(`   ⏱️ ${viewport.name} Load: ${mobileLoadTime.toFixed(2)}ms`);
      
      performanceResults.mobile[viewport.name] = {
        loadTime: mobileLoadTime,
        viewport: viewport
      };
    }
    
    // Reset to desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Test 4: Resource Loading Performance
    console.log('');
    console.log('📦 Test 4: Resource Loading Performance');
    console.log('-------------------------------------');
    
    console.log('🔍 Analyzing resource loading...');
    
    // Navigate to a fresh page to measure resources
    await page.goto('http://localhost:5182/', { waitUntil: 'networkidle' });
    
    const resourceMetrics = await page.evaluate(() => {
      const resources = performance.getEntriesByType('resource');
      const navigation = performance.getEntriesByType('navigation')[0];
      
      const resourceSummary = {
        totalResources: resources.length,
        totalSize: 0,
        resourceTypes: {},
        slowestResources: [],
        navigationTiming: {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          firstByte: navigation.responseStart - navigation.requestStart
        }
      };
      
      resources.forEach(resource => {
        const type = resource.initiatorType || 'other';
        if (!resourceSummary.resourceTypes[type]) {
          resourceSummary.resourceTypes[type] = { count: 0, totalDuration: 0 };
        }
        
        resourceSummary.resourceTypes[type].count++;
        resourceSummary.resourceTypes[type].totalDuration += resource.duration;
        
        if (resource.transferSize) {
          resourceSummary.totalSize += resource.transferSize;
        }
        
        if (resource.duration > 100) {
          resourceSummary.slowestResources.push({
            name: resource.name.split('/').pop(),
            duration: resource.duration,
            size: resource.transferSize
          });
        }
      });
      
      return resourceSummary;
    });
    
    console.log(`   📊 Total Resources: ${resourceMetrics.totalResources}`);
    console.log(`   📊 Total Size: ${(resourceMetrics.totalSize / 1024).toFixed(2)} KB`);
    console.log(`   📊 DOM Content Loaded: ${resourceMetrics.navigationTiming.domContentLoaded.toFixed(2)}ms`);
    console.log(`   📊 Load Complete: ${resourceMetrics.navigationTiming.loadComplete.toFixed(2)}ms`);
    console.log(`   📊 Time to First Byte: ${resourceMetrics.navigationTiming.firstByte.toFixed(2)}ms`);
    
    if (resourceMetrics.slowestResources.length > 0) {
      console.log('   ⚠️ Slow Resources (>100ms):');
      resourceMetrics.slowestResources.slice(0, 3).forEach(resource => {
        console.log(`     - ${resource.name}: ${resource.duration.toFixed(2)}ms`);
      });
    }
    
    performanceResults.resources = resourceMetrics;
    
    // Test 5: Bundle Size Analysis
    console.log('');
    console.log('📦 Test 5: Bundle Size Analysis');
    console.log('------------------------------');
    
    const bundleAnalysis = await page.evaluate(() => {
      const scripts = Array.from(document.querySelectorAll('script[src]'));
      const stylesheets = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
      
      return {
        scriptCount: scripts.length,
        stylesheetCount: stylesheets.length,
        scripts: scripts.map(script => ({
          src: script.src.split('/').pop(),
          async: script.async,
          defer: script.defer
        })),
        stylesheets: stylesheets.map(link => ({
          href: link.href.split('/').pop()
        }))
      };
    });
    
    console.log(`   📊 JavaScript Files: ${bundleAnalysis.scriptCount}`);
    console.log(`   📊 CSS Files: ${bundleAnalysis.stylesheetCount}`);
    
    performanceResults.resources.bundleAnalysis = bundleAnalysis;

  } catch (error) {
    console.error('💥 Browser performance assessment failed:', error);
    performanceResults.error = error.message;
  } finally {
    await browser.close();
  }

  // Performance Analysis and Recommendations
  console.log('');
  console.log('📊 Performance Analysis');
  console.log('----------------------');
  
  // Page Load Analysis
  if (performanceResults.pageLoad.landingPage > 3000) {
    performanceResults.recommendations.push('Landing page load time >3s - optimize critical resources');
  }
  
  if (performanceResults.pageLoad.authPage > 2000) {
    performanceResults.recommendations.push('Auth page load time >2s - consider code splitting');
  }
  
  // Core Web Vitals Analysis
  if (performanceResults.coreWebVitals.lcp > 2500) {
    performanceResults.recommendations.push('LCP >2.5s - optimize largest contentful paint');
  }
  
  if (performanceResults.coreWebVitals.cls > 0.1) {
    performanceResults.recommendations.push('CLS >0.1 - reduce layout shifts');
  }
  
  // Authentication Performance Analysis
  if (performanceResults.authentication.totalFlow > 5000) {
    performanceResults.recommendations.push('Total auth flow >5s - optimize authentication UX');
  }
  
  // Resource Analysis
  if (performanceResults.resources.totalSize > 1024 * 1024) { // 1MB
    performanceResults.recommendations.push('Bundle size >1MB - consider code splitting and optimization');
  }
  
  // Save results
  fs.writeFileSync('browser-performance-results.json', JSON.stringify(performanceResults, null, 2));
  
  return performanceResults;
}

// Run the browser performance assessment
browserPerformanceAssessment().then((results) => {
  console.log('');
  console.log('📊 BROWSER PERFORMANCE ASSESSMENT SUMMARY');
  console.log('==========================================');
  console.log('');
  
  console.log('🎯 PERFORMANCE METRICS:');
  if (results.pageLoad.landingPage) {
    console.log(`   🏠 Landing Page: ${results.pageLoad.landingPage.toFixed(2)}ms`);
  }
  if (results.pageLoad.authPage) {
    console.log(`   🔐 Auth Page: ${results.pageLoad.authPage.toFixed(2)}ms`);
  }
  if (results.authentication.totalFlow) {
    console.log(`   🔄 Total Auth Flow: ${results.authentication.totalFlow.toFixed(2)}ms`);
  }
  if (results.coreWebVitals.lcp) {
    console.log(`   📊 LCP: ${results.coreWebVitals.lcp.toFixed(2)}ms`);
  }
  if (results.resources.totalSize) {
    console.log(`   📦 Bundle Size: ${(results.resources.totalSize / 1024).toFixed(2)} KB`);
  }
  
  console.log('');
  if (results.recommendations.length > 0) {
    console.log('📝 PERFORMANCE RECOMMENDATIONS:');
    results.recommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`);
    });
  } else {
    console.log('✅ No performance issues detected');
  }
  
  console.log('');
  console.log('📝 Results saved to: browser-performance-results.json');
  process.exit(0);
}).catch(error => {
  console.error('💥 Browser performance assessment suite failed:', error);
  process.exit(1);
});

/**
 * Test Festivals Form
 * 
 * This test verifies that the festivals form works correctly
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

test('Test Festivals Form', async ({ page }) => {
  console.log('🧪 Testing festivals form...');
  
  const loginSuccess = await loginAsAdmin(page);
  console.log(`Login success: ${loginSuccess}`);
  
  if (loginSuccess) {
    // Navigate to festivals form
    await page.goto('/admin/festivals/new');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    console.log('📝 Checking festivals form...');
    
    // Check if form elements exist
    const hasName = await page.locator('input[name="name"]').count() > 0;
    const hasDescription = await page.locator('textarea[name="description"]').count() > 0;
    const hasLocation = await page.locator('input[name="location"]').count() > 0;
    const hasSubmitButton = await page.locator('button[type="submit"]').count() > 0;
    
    console.log(`Form elements found:`);
    console.log(`  Name field: ${hasName}`);
    console.log(`  Description field: ${hasDescription}`);
    console.log(`  Location field: ${hasLocation}`);
    console.log(`  Submit button: ${hasSubmitButton}`);
    
    await page.screenshot({ path: 'test-results/festivals-form.png', fullPage: true });
    
    // Try to fill a simple form
    if (hasName && hasDescription && hasLocation) {
      console.log('📝 Filling basic form data...');
      
      await page.fill('input[name="name"]', 'Test Festival ' + Date.now());
      await page.fill('textarea[name="description"]', 'This is a test festival for schema validation.');
      await page.fill('input[name="location"]', 'Test Festival Location');
      
      await page.screenshot({ path: 'test-results/festivals-form-filled.png', fullPage: true });
      
      console.log('✅ Basic form data filled successfully');
    }
    
    // Check if we can navigate to festivals list
    await page.goto('/admin/festivals');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const festivalRows = await page.locator('tbody tr, .festival-item, [data-testid*="festival"]').count();
    console.log(`Festivals list shows ${festivalRows} festivals`);
    
    await page.screenshot({ path: 'test-results/festivals-list.png', fullPage: true });
    
    console.log('✅ Festivals test completed');
  }
});

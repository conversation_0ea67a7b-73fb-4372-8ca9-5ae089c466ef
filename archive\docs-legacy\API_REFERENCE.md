# Festival Family - API Reference

## 🔌 **API Overview**

Festival Family uses Supabase as the backend service, providing a comprehensive API for authentication, database operations, and real-time features. This document outlines the consolidated API architecture and usage patterns.

## 🔐 **Authentication API**

### **ConsolidatedAuthProvider**
**Location**: `src/providers/ConsolidatedAuthProvider.tsx`

#### **Sign In**
```typescript
const signIn = async (email: string, password: string): Promise<AuthResult>
```
- **Purpose**: Authenticate user with email/password
- **Returns**: AuthResult with user data or error
- **Error Handling**: Comprehensive error categorization

#### **Sign Up**
```typescript
const signUp = async (email: string, password: string): Promise<AuthResult>
```
- **Purpose**: Register new user account
- **Returns**: AuthResult with confirmation requirements
- **Side Effects**: Creates profile record

#### **Sign Out**
```typescript
const signOut = async (): Promise<void>
```
- **Purpose**: End user session
- **Side Effects**: Clears local storage, redirects to login

#### **Refresh Profile**
```typescript
const refreshProfile = async (): Promise<void>
```
- **Purpose**: Sync profile data from database
- **Use Case**: After profile updates

## 🗄️ **Database Services**

### **Base Service Pattern**
**Location**: `src/lib/supabase/services/base-service.ts`

All services extend the base service for consistent error handling:

```typescript
interface ServiceResponse<T> {
  data: T | null
  error: ServiceError | null
  status: 'success' | 'error'
}
```

### **Profile Service**
**Location**: `src/lib/supabase/services/profile-service.ts`

#### **Get Profile**
```typescript
async getProfile(userId: string): Promise<ServiceResponse<Profile>>
```

#### **Update Profile**
```typescript
async updateProfile(userId: string, updates: ProfileUpdate): Promise<ServiceResponse<Profile>>
```

#### **Upload Avatar**
```typescript
async uploadAvatar(userId: string, file: File): Promise<ServiceResponse<string>>
```

### **Activity Attendance Service**
**Location**: `src/lib/supabase/services/activity-attendance-service.ts`

#### **Mark Attendance**
```typescript
async markAttendance(
  userId: string, 
  activityId: string, 
  status: AttendanceStatus
): Promise<ServiceResponse<boolean>>
```

#### **Get Activity Attendees**
```typescript
async getActivityAttendees(activityId: string): Promise<ServiceResponse<Profile[]>>
```

#### **Find Activity Buddies**
```typescript
async findActivityBuddies(
  userId: string, 
  activityId: string
): Promise<ServiceResponse<Profile[]>>
```

#### **Get Activity Suggestions**
```typescript
async getActivitySuggestions(
  userId: string, 
  festivalId: string, 
  limit?: number
): Promise<ServiceResponse<ActivitySuggestion[]>>
```

### **Connection Service**
**Location**: `src/lib/supabase/services/connection-service.ts`

#### **Send Connection Request**
```typescript
async sendConnectionRequest(
  fromUserId: string, 
  toUserId: string
): Promise<ServiceResponse<boolean>>
```

#### **Accept Connection**
```typescript
async acceptConnection(connectionId: string): Promise<ServiceResponse<boolean>>
```

#### **Get User Connections**
```typescript
async getUserConnections(userId: string): Promise<ServiceResponse<Connection[]>>
```

## 🎯 **React Hooks API**

### **Authentication Hooks**

#### **useAuth**
```typescript
const { user, profile, loading, signIn, signUp, signOut } = useAuth()
```
- **Purpose**: Access authentication state and methods
- **Provider**: ConsolidatedAuthProvider

#### **useAdmin**
```typescript
const { isAdmin, hasPermission } = useAdmin()
```
- **Purpose**: Check admin status and permissions
- **Roles**: SUPER_ADMIN, CONTENT_ADMIN, MODERATOR

#### **useAdminAccess**
```typescript
const { canAccess, requiredRole } = useAdminAccess(requiredRole)
```
- **Purpose**: Role-based access control
- **Usage**: Component-level access control

### **Data Hooks**

#### **useProfile**
```typescript
const { 
  profile, 
  isLoading, 
  updateProfile, 
  uploadAvatar 
} = useProfile(userId?)
```
- **Purpose**: Profile data management
- **Features**: Automatic caching, optimistic updates

#### **useActivities**
```typescript
const { 
  activities, 
  isLoading, 
  markAttendance, 
  getAttendees 
} = useActivities(festivalId)
```
- **Purpose**: Activity data and attendance management

#### **useConnections**
```typescript
const { 
  connections, 
  pendingRequests, 
  sendRequest, 
  acceptRequest 
} = useConnections()
```
- **Purpose**: User connection management

## 🔧 **Error Handling API**

### **Error Handler**
**Location**: `src/lib/utils/supabase-error-handler.ts`

#### **Error Categories**
```typescript
enum ErrorCategory {
  AUTH = 'auth',
  DB = 'db', 
  NETWORK = 'network',
  VALIDATION = 'validation',
  UNKNOWN = 'unknown'
}
```

#### **Error Codes**
```typescript
enum ErrorCode {
  // Authentication
  AUTH_INVALID_CREDENTIALS = 'AUTH_INVALID_CREDENTIALS',
  AUTH_USER_NOT_FOUND = 'AUTH_USER_NOT_FOUND',
  AUTH_EMAIL_IN_USE = 'AUTH_EMAIL_IN_USE',
  
  // Database
  DB_QUERY_ERROR = 'DB_QUERY_ERROR',
  DB_CONSTRAINT_VIOLATION = 'DB_CONSTRAINT_VIOLATION',
  DB_RECORD_NOT_FOUND = 'DB_RECORD_NOT_FOUND',
  
  // Network
  NETWORK_ERROR = 'NETWORK_ERROR',
  NETWORK_TIMEOUT = 'NETWORK_TIMEOUT',
  
  // Validation
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  
  // General
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  NOT_IMPLEMENTED = 'NOT_IMPLEMENTED'
}
```

#### **Handle Supabase Error**
```typescript
function handleSupabaseError(error: any): {
  code: ErrorCode
  message: string
  category: ErrorCategory
}
```

### **Error Components**

#### **ErrorMessage**
```typescript
<ErrorMessage 
  message="Error occurred"
  errorCode={ErrorCode.AUTH_INVALID_CREDENTIALS}
  onRetry={() => {}}
/>
```

#### **ErrorBoundary**
```typescript
<ErrorBoundary fallback={<ErrorFallback />}>
  <App />
</ErrorBoundary>
```

## 📊 **Type Definitions**

### **Core Types**
**Location**: `src/types/core.ts`

```typescript
interface Profile {
  id: string
  username: string
  email: string
  full_name: string | null
  avatar_url: string | null
  bio: string | null
  role: UserRole
  created_at: string
  updated_at: string
}

type UserRole = 'SUPER_ADMIN' | 'CONTENT_ADMIN' | 'MODERATOR' | 'USER'
```

### **Database Types**
**Location**: `src/types/database.ts`

```typescript
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update']
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert']
```

### **Activity Types**
**Location**: `src/types/activities.ts`

```typescript
interface Activity {
  id: string
  name: string
  description: string
  festival_id: string
  start_time: string
  end_time: string
  location: string
  capacity?: number
}

type AttendanceStatus = 'going' | 'interested' | 'not_going'
```

## 🔄 **Real-time Features**

### **Supabase Realtime**
```typescript
// Subscribe to profile changes
const subscription = supabase
  .channel('profile-changes')
  .on('postgres_changes', 
    { event: 'UPDATE', schema: 'public', table: 'profiles' },
    (payload) => {
      // Handle profile update
    }
  )
  .subscribe()
```

### **Activity Updates**
```typescript
// Subscribe to activity attendance changes
const subscription = supabase
  .channel('activity-attendance')
  .on('postgres_changes',
    { event: '*', schema: 'public', table: 'attendees' },
    (payload) => {
      // Handle attendance change
    }
  )
  .subscribe()
```

## 🚀 **Usage Examples**

### **Complete Authentication Flow**
```typescript
function LoginComponent() {
  const { signIn, loading } = useAuth()
  
  const handleLogin = async (email: string, password: string) => {
    const result = await signIn(email, password)
    if (result.error) {
      // Handle error with error code
      console.error(result.error.code, result.error.message)
    }
  }
  
  return (
    <form onSubmit={handleLogin}>
      {/* Login form */}
    </form>
  )
}
```

### **Activity Attendance Management**
```typescript
function ActivityCard({ activity }: { activity: Activity }) {
  const { markAttendance } = useActivities(activity.festival_id)
  const { user } = useAuth()
  
  const handleAttendance = async (status: AttendanceStatus) => {
    if (!user) return
    
    const result = await markAttendance(user.id, activity.id, status)
    if (result.error) {
      // Handle error
    }
  }
  
  return (
    <div>
      <h3>{activity.name}</h3>
      <button onClick={() => handleAttendance('going')}>
        I'm Going
      </button>
    </div>
  )
}
```

### **Admin Access Control**
```typescript
function AdminPanel() {
  const { canAccess } = useAdminAccess('CONTENT_ADMIN')
  
  if (!canAccess) {
    return <div>Access Denied</div>
  }
  
  return (
    <div>
      {/* Admin content */}
    </div>
  )
}
```

---

**Last Updated**: December 2024  
**API Version**: 1.0 (Production Ready)  
**Status**: ✅ Complete Documentation

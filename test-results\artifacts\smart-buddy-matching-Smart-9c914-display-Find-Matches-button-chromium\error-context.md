# Test info

- Name: Smart Buddy Matching Feature >> Component Functionality >> should display Find Matches button
- Location: C:\Users\<USER>\CascadeProjects\festival-family\tests\smart-buddy-matching.spec.js:84:5

# Error details

```
TimeoutError: locator.click: Timeout 10000ms exceeded.
Call log:
  - waiting for locator('button:has-text("Smart Matching")')

    at C:\Users\<USER>\CascadeProjects\festival-family\tests\smart-buddy-matching.spec.js:56:30
```

# Page snapshot

```yaml
- img
- heading "Welcome Back!" [level=1]
- paragraph: Sign in to continue to Festival Family
- img
- text: Email Address
- textbox "Email Address"
- img
- text: Password
- textbox "Password"
- button:
  - img
- checkbox "Remember me for 30 days"
- text: Remember me for 30 days
- button "Sign In":
  - img
  - text: Sign In
- button "Switch to password reset mode": Forgot your password?
- button "Switch to registration mode": Don't have an account? Sign up
- text: Or continue with
- button "Google" [disabled]:
  - img
  - text: Google
- button "GitHub" [disabled]:
  - img
  - text: GitHub
- button:
  - img
- button "Open Tanstack query devtools":
  - img
```

# Test source

```ts
   1 | /**
   2 |  * Smart Buddy Matching Feature Tests
   3 |  * 
   4 |  * Comprehensive testing for the AI-powered buddy matching system
   5 |  * including component rendering, functionality, and performance.
   6 |  * 
   7 |  * @module SmartBuddyMatchingTests
   8 |  * @version 1.0.0
   9 |  */
   10 |
   11 | import { test, expect } from '@playwright/test';
   12 |
   13 | test.describe('Smart Buddy Matching Feature', () => {
   14 |   
   15 |   test.beforeEach(async ({ page }) => {
   16 |     // Navigate to FamHub and wait for it to load
   17 |     await page.goto('/famhub');
   18 |     await page.waitForLoadState('networkidle');
   19 |   });
   20 |
   21 |   test.describe('Feature Integration', () => {
   22 |     
   23 |     test('should display Smart Matching tab in FamHub', async ({ page }) => {
   24 |       // Check if Smart Matching tab is present
   25 |       const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
   26 |       await expect(smartMatchingTab).toBeVisible();
   27 |       
   28 |       // Verify tab has the correct icon
   29 |       const heartIcon = smartMatchingTab.locator('svg');
   30 |       await expect(heartIcon).toBeVisible();
   31 |     });
   32 |
   33 |     test('should navigate to Smart Matching section when tab is clicked', async ({ page }) => {
   34 |       // Click on Smart Matching tab
   35 |       const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
   36 |       await smartMatchingTab.click();
   37 |       
   38 |       // Wait for content to load
   39 |       await page.waitForTimeout(1000);
   40 |       
   41 |       // Check if Smart Buddy Matching component is displayed
   42 |       const buddyMatchingComponent = page.locator('text=Smart Buddy Matching');
   43 |       await expect(buddyMatchingComponent).toBeVisible();
   44 |       
   45 |       // Check for AI-Powered badge
   46 |       const aiPoweredBadge = page.locator('text=AI-Powered');
   47 |       await expect(aiPoweredBadge).toBeVisible();
   48 |     });
   49 |   });
   50 |
   51 |   test.describe('Component Functionality', () => {
   52 |     
   53 |     test.beforeEach(async ({ page }) => {
   54 |       // Navigate to Smart Matching tab
   55 |       const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
>  56 |       await smartMatchingTab.click();
      |                              ^ TimeoutError: locator.click: Timeout 10000ms exceeded.
   57 |       await page.waitForTimeout(1000);
   58 |     });
   59 |
   60 |     test('should display filters button and functionality', async ({ page }) => {
   61 |       // Check if Filters button is present
   62 |       const filtersButton = page.locator('button:has-text("Filters")');
   63 |       await expect(filtersButton).toBeVisible();
   64 |       
   65 |       // Click filters button to open filters panel
   66 |       await filtersButton.click();
   67 |       
   68 |       // Check if filters panel is displayed
   69 |       const filtersPanel = page.locator('text=Matching Preferences');
   70 |       await expect(filtersPanel).toBeVisible();
   71 |       
   72 |       // Check for distance slider
   73 |       const distanceSlider = page.locator('text=Maximum Distance');
   74 |       await expect(distanceSlider).toBeVisible();
   75 |       
   76 |       // Check for compatibility sliders
   77 |       const musicCompatibility = page.locator('text=Min Music Compatibility');
   78 |       await expect(musicCompatibility).toBeVisible();
   79 |       
   80 |       const activityCompatibility = page.locator('text=Min Activity Compatibility');
   81 |       await expect(activityCompatibility).toBeVisible();
   82 |     });
   83 |
   84 |     test('should display Find Matches button', async ({ page }) => {
   85 |       // Check if Find Matches button is present
   86 |       const findMatchesButton = page.locator('button:has-text("Find Matches")');
   87 |       await expect(findMatchesButton).toBeVisible();
   88 |       
   89 |       // Button should have search icon
   90 |       const searchIcon = findMatchesButton.locator('text=🔍');
   91 |       await expect(searchIcon).toBeVisible();
   92 |     });
   93 |
   94 |     test('should handle authentication state correctly', async ({ page }) => {
   95 |       // If user is not authenticated, should show sign-in message
   96 |       const signInMessage = page.locator('text=Sign In Required');
   97 |       const buddyMatchingContent = page.locator('text=Find your perfect festival companions');
   98 |       
   99 |       // Either sign-in message or buddy matching content should be visible
  100 |       const hasSignIn = await signInMessage.isVisible();
  101 |       const hasContent = await buddyMatchingContent.isVisible();
  102 |       
  103 |       expect(hasSignIn || hasContent).toBe(true);
  104 |       
  105 |       if (hasSignIn) {
  106 |         // Check sign-in message content
  107 |         const signInDescription = page.locator('text=Please sign in to find your festival buddies!');
  108 |         await expect(signInDescription).toBeVisible();
  109 |       }
  110 |     });
  111 |   });
  112 |
  113 |   test.describe('Standardized Components Usage', () => {
  114 |     
  115 |     test.beforeEach(async ({ page }) => {
  116 |       // Navigate to Smart Matching tab
  117 |       const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
  118 |       await smartMatchingTab.click();
  119 |       await page.waitForTimeout(1000);
  120 |     });
  121 |
  122 |     test('should use BentoCard components consistently', async ({ page }) => {
  123 |       // Check for BentoCard usage in the component
  124 |       const bentoCards = page.locator('[class*="bento"], [class*="card"]');
  125 |       const cardCount = await bentoCards.count();
  126 |       
  127 |       expect(cardCount).toBeGreaterThan(0);
  128 |       
  129 |       // Verify cards have proper styling
  130 |       if (cardCount > 0) {
  131 |         const firstCard = bentoCards.first();
  132 |         const cardClasses = await firstCard.getAttribute('class');
  133 |         expect(cardClasses).toMatch(/card|bento|rounded|shadow/i);
  134 |       }
  135 |     });
  136 |
  137 |     test('should use EnhancedUnifiedBadge for AI-Powered indicator', async ({ page }) => {
  138 |       // Check for AI-Powered badge
  139 |       const aiPoweredBadge = page.locator('text=AI-Powered');
  140 |       
  141 |       if (await aiPoweredBadge.isVisible()) {
  142 |         // Verify badge styling
  143 |         const badgeElement = aiPoweredBadge.locator('..');
  144 |         const badgeClasses = await badgeElement.getAttribute('class');
  145 |         expect(badgeClasses).toMatch(/badge|unified|enhanced/i);
  146 |       }
  147 |     });
  148 |
  149 |     test('should use UnifiedInteractionButton for user actions', async ({ page }) => {
  150 |       // Look for interaction buttons (these would appear when matches are found)
  151 |       const interactionButtons = page.locator('[data-testid*="unified-interaction"], button[class*="unified-interaction"]');
  152 |       
  153 |       // Note: These might not be visible if no matches are found or user is not authenticated
  154 |       // This test validates the component structure when buttons are present
  155 |       const buttonCount = await interactionButtons.count();
  156 |       
```
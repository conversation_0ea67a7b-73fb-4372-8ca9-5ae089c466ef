import React from 'react';
import { BadgeColorManager } from '@/components/admin/BadgeColorManager';
import { UnifiedBadge } from '@/components/design-system';

const BadgeTestPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-primary">Badge Visibility & Accessibility Testing</h1>
          <p className="text-lg text-muted-foreground">
            Testing badge visibility, contrast ratios, and admin customization system
          </p>
        </div>

        {/* Badge Examples Section */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-primary">Badge Examples</h2>
          
          {/* Light Background Test */}
          <div className="bg-white p-6 rounded-lg border">
            <h3 className="text-lg font-semibold mb-4 text-gray-900">Light Background Test</h3>
            <div className="flex flex-wrap gap-4">
              <UnifiedBadge variant="priority" priority="high" overlayMode={true}>Featured</UnifiedBadge>
              <UnifiedBadge variant="category" overlayMode={true}>Event</UnifiedBadge>
              <UnifiedBadge variant="success" overlayMode={true}>Trending</UnifiedBadge>
              <UnifiedBadge variant="admin-customizable" customColor="#FF6B35" overlayMode={true}>Custom</UnifiedBadge>
              <UnifiedBadge variant="secondary" overlayMode={true}>Popular</UnifiedBadge>
            </div>
          </div>

          {/* Dark Background Test */}
          <div className="bg-gray-900 p-6 rounded-lg border">
            <h3 className="text-lg font-semibold mb-4 text-white">Dark Background Test</h3>
            <div className="flex flex-wrap gap-4">
              <UnifiedBadge variant="priority" priority="high" overlayMode={true}>Featured</UnifiedBadge>
              <UnifiedBadge variant="category" overlayMode={true}>Event</UnifiedBadge>
              <UnifiedBadge variant="success" overlayMode={true}>Trending</UnifiedBadge>
              <UnifiedBadge variant="admin-customizable" customColor="#4ECDC4" overlayMode={true}>Custom</UnifiedBadge>
              <UnifiedBadge variant="secondary" overlayMode={true}>Popular</UnifiedBadge>
            </div>
          </div>

          {/* Image Overlay Test */}
          <div className="relative">
            <h3 className="text-lg font-semibold mb-4">Image Overlay Test</h3>
            <div className="relative h-64 bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 rounded-lg overflow-hidden">
              <img 
                src="https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=400&fit=crop" 
                alt="Festival crowd"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
              <div className="absolute top-4 left-4 flex gap-2">
                <UnifiedBadge variant="priority" priority="high" overlayMode={true}>Featured</UnifiedBadge>
                <UnifiedBadge variant="category" overlayMode={true}>Event</UnifiedBadge>
              </div>
              <div className="absolute top-4 right-4">
                <UnifiedBadge variant="success" overlayMode={true}>Trending</UnifiedBadge>
              </div>
              <div className="absolute bottom-4 left-4">
                <UnifiedBadge variant="admin-customizable" customColor="#F7931E" overlayMode={true}>Custom Badge</UnifiedBadge>
              </div>
            </div>
          </div>

          {/* Mobile Size Test */}
          <div className="max-w-sm mx-auto border rounded-lg overflow-hidden">
            <h3 className="text-lg font-semibold p-4 bg-muted">Mobile Size Test (375px)</h3>
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=400&h=200&fit=crop" 
                alt="Concert stage"
                className="w-full h-32 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
              <div className="absolute top-2 left-2 flex gap-1">
                <UnifiedBadge variant="category" overlayMode={true} size="sm">Event</UnifiedBadge>
                <UnifiedBadge variant="priority" overlayMode={true} size="sm">Featured</UnifiedBadge>
              </div>
            </div>
            <div className="p-4">
              <h4 className="font-semibold">Post Malone - Main Stage</h4>
              <p className="text-sm text-muted-foreground">Grammy-nominated rapper performs live</p>
            </div>
          </div>
        </div>

        {/* Admin Badge Color Manager */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-primary">Admin Badge Color Manager</h2>
          <BadgeColorManager />
        </div>

        {/* Accessibility Information */}
        <div className="bg-muted/50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Accessibility Testing Results</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">✅ WCAG 2.1 Compliance</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Contrast ratio: 4.5:1+ for normal text</li>
                <li>• Backdrop blur for enhanced visibility</li>
                <li>• Shadow effects for depth perception</li>
                <li>• Keyboard navigation support</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">✅ Responsive Design</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Mobile-first badge positioning</li>
                <li>• Scalable typography (sm, md, lg)</li>
                <li>• Touch-friendly target sizes</li>
                <li>• Cross-device compatibility</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BadgeTestPage;

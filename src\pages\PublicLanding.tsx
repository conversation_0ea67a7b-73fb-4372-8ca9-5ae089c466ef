import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Users, Calendar, Heart, Music, Shield, Zap, ArrowRight, Play, ChevronDown } from 'lucide-react';
import { motion } from 'framer-motion';
import MobileUXTester from '../components/testing/MobileUXTester';

/**
 * Public Marketing Landing Page
 * 
 * This is the main entry point for new visitors to Festival Family.
 * It presents the app's value proposition without requiring authentication
 * and includes prominent calls-to-action for login/signup.
 */
const PublicLanding: React.FC = () => {

  return (
    <div className="min-h-screen bg-background overflow-x-hidden">
      {/* Navigation is handled by AppLayout - no duplicate navigation needed */}

      {/* Enhanced Mobile-First Hero Section */}
      <section className="relative z-10 min-h-[80vh] flex items-center justify-center">
        <div className="container-responsive text-center text-white">
          {/* Animated Hero Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="max-w-4xl mx-auto"
          >
            {/* Mobile-Optimized Typography */}
            <motion.h1
              className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 leading-tight"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Find Your Tribe,
              <br className="hidden sm:block" />
              <span className="sm:hidden"> </span>
              <motion.span
                className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1, delay: 0.8 }}
              >
                Share the Vibe
              </motion.span>
            </motion.h1>

            {/* Mobile-Optimized Description */}
            <motion.p
              className="text-base sm:text-lg md:text-xl lg:text-2xl mb-6 sm:mb-8 text-white/80 max-w-3xl mx-auto leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              Never festival alone again. Connect with like-minded music lovers,
              discover amazing events, and build lifelong friendships in the festival community.
            </motion.p>

            {/* Enhanced Mobile-First CTAs */}
            <motion.div
              className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center max-w-md sm:max-w-none mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              {/* Primary CTA - Mobile Optimized */}
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="w-full sm:w-auto"
              >
                <Link
                  to="/auth"
                  className="group w-full sm:w-auto inline-flex items-center justify-center gap-2 touch-target px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 rounded-xl text-white font-semibold text-base sm:text-lg transition-all duration-300 shadow-lg hover:shadow-xl"
                  style={{ touchAction: 'manipulation' }}
                >
                  <span>Join Festival Family</span>
                  <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-1 transition-transform" />
                </Link>
              </motion.div>

              {/* Secondary CTA - Mobile Optimized */}
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="w-full sm:w-auto"
              >
                <Link
                  to="/discover"
                  className="group w-full sm:w-auto inline-flex items-center justify-center gap-2 touch-target px-6 sm:px-8 py-3 sm:py-4 bg-white/10 hover:bg-white/20 backdrop-blur-md border border-white/20 rounded-xl text-white font-semibold text-base sm:text-lg transition-all duration-300"
                  style={{ touchAction: 'manipulation' }}
                >
                  <Play className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span>Explore Events</span>
                </Link>
              </motion.div>
            </motion.div>

            {/* Mobile-Friendly Scroll Indicator */}
            <motion.div
              className="mt-12 sm:mt-16 flex justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: 1.2 }}
            >
              <motion.div
                animate={{ y: [0, 8, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="flex flex-col items-center gap-2 text-white/60 cursor-pointer"
                onClick={() => window.scrollTo({ top: window.innerHeight, behavior: 'smooth' })}
              >
                <span className="text-xs sm:text-sm">Discover More</span>
                <ChevronDown className="w-4 h-4 sm:w-5 sm:h-5" />
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Enhanced Mobile-First Features Section */}
      <section className="relative z-10 py-16 sm:py-20">
        <div className="container-responsive">
          {/* Section Header - Mobile Optimized */}
          <motion.div
            className="text-center mb-12 sm:mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-3 sm:mb-4">
              Everything You Need for Festival Life
            </h2>
            <p className="text-base sm:text-lg md:text-xl text-white/70 max-w-2xl mx-auto leading-relaxed">
              From discovering events to building connections, Festival Family has all the tools
              to enhance your festival experience.
            </p>
          </motion.div>

          {/* Mobile-Optimized Feature Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
            {/* Feature 1: Connect */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02, y: -4 }}
              className="group"
            >
              <div className="h-full p-4 sm:p-6 bg-white/10 backdrop-blur-md rounded-xl sm:rounded-2xl border border-white/20 hover:bg-white/15 hover:border-white/30 transition-all duration-300 cursor-pointer">
                <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-purple-500/20 to-purple-600/20 rounded-lg sm:rounded-xl flex items-center justify-center mb-3 sm:mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Users className="w-6 h-6 sm:w-7 sm:h-7 text-purple-400" />
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-white mb-2">Connect</h3>
                <p className="text-sm sm:text-base text-white/70 leading-relaxed">
                  Meet like-minded festival-goers and build your festival family through our FamHub community.
                </p>
              </div>
            </motion.div>

            {/* Feature 2: Discover */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02, y: -4 }}
              className="group"
            >
              <div className="h-full p-4 sm:p-6 bg-white/10 backdrop-blur-md rounded-xl sm:rounded-2xl border border-white/20 hover:bg-white/15 hover:border-white/30 transition-all duration-300 cursor-pointer">
                <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-blue-500/20 to-blue-600/20 rounded-lg sm:rounded-xl flex items-center justify-center mb-3 sm:mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Calendar className="w-6 h-6 sm:w-7 sm:h-7 text-blue-400" />
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-white mb-2">Discover</h3>
                <p className="text-sm sm:text-base text-white/70 leading-relaxed">
                  Find festivals and events that match your music taste and interests across the globe.
                </p>
              </div>
            </motion.div>

            {/* Feature 3: Share */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02, y: -4 }}
              className="group"
            >
              <div className="h-full p-4 sm:p-6 bg-card/80 backdrop-blur-md rounded-xl sm:rounded-2xl border border-border hover:bg-card/90 hover:border-border transition-all duration-300 cursor-pointer">
                <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-destructive/20 to-destructive/30 rounded-lg sm:rounded-xl flex items-center justify-center mb-3 sm:mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Heart className="w-6 h-6 sm:w-7 sm:h-7 text-destructive" />
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-white mb-2">Share</h3>
                <p className="text-sm sm:text-base text-white/70 leading-relaxed">
                  Share your festival experiences, photos, and memories with the community.
                </p>
              </div>
            </motion.div>

            {/* Feature 4: Stay Safe */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02, y: -4 }}
              className="group"
            >
              <div className="h-full p-4 sm:p-6 bg-white/10 backdrop-blur-md rounded-xl sm:rounded-2xl border border-white/20 hover:bg-white/15 hover:border-white/30 transition-all duration-300 cursor-pointer">
                <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-green-500/20 to-green-600/20 rounded-lg sm:rounded-xl flex items-center justify-center mb-3 sm:mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Shield className="w-6 h-6 sm:w-7 sm:h-7 text-green-400" />
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-white mb-2">Stay Safe</h3>
                <p className="text-sm sm:text-base text-white/70 leading-relaxed">
                  Access safety resources, emergency contacts, and community support when you need it.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Enhanced Mobile-First Community Stats */}
      <section className="relative z-10 py-16 sm:py-20">
        <div className="container-responsive">
          <motion.div
            className="bg-white/10 backdrop-blur-md rounded-2xl sm:rounded-3xl border border-white/20 p-6 sm:p-8 lg:p-12"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="text-center mb-6 sm:mb-8">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-3 sm:mb-4">
                Join Thousands of Festival Lovers
              </h2>
              <p className="text-base sm:text-lg text-white/70 max-w-2xl mx-auto">
                Be part of a growing community that's revolutionizing the festival experience.
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 text-center">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
                className="p-4 sm:p-6 rounded-xl bg-white/5 border border-white/10"
              >
                <div className="text-3xl sm:text-4xl lg:text-5xl font-bold text-purple-400 mb-2">10K+</div>
                <div className="text-sm sm:text-base text-white/70">Festival Goers</div>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="p-4 sm:p-6 rounded-xl bg-white/5 border border-white/10"
              >
                <div className="text-3xl sm:text-4xl lg:text-5xl font-bold text-blue-400 mb-2">500+</div>
                <div className="text-sm sm:text-base text-white/70">Events Listed</div>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
                className="p-4 sm:p-6 rounded-xl bg-white/5 border border-white/10"
              >
                <div className="text-3xl sm:text-4xl lg:text-5xl font-bold text-pink-400 mb-2">50K+</div>
                <div className="text-sm sm:text-base text-white/70">Connections Made</div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Enhanced Mobile-First Final CTA */}
      <section className="relative z-10 py-16 sm:py-20">
        <div className="container-responsive text-center">
          <motion.div
            className="bg-gradient-to-r from-primary/20 to-accent/20 backdrop-blur-md rounded-2xl sm:rounded-3xl border border-border p-6 sm:p-8 lg:p-12"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.h2
              className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 sm:mb-6"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              Ready to Find Your Festival Family?
            </motion.h2>
            <motion.p
              className="text-base sm:text-lg md:text-xl text-white/80 mb-6 sm:mb-8 max-w-2xl mx-auto leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              Join thousands of festival-goers who have already found their tribe.
              Your next festival adventure starts here.
            </motion.p>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                to="/auth"
                className="inline-flex items-center gap-2 touch-target px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 rounded-xl text-white font-semibold text-base sm:text-lg transition-all duration-300 shadow-lg hover:shadow-xl"
                style={{ touchAction: 'manipulation' }}
              >
                <Zap className="w-4 h-4 sm:w-5 sm:h-5" />
                <span>Get Started Now</span>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Enhanced Mobile-First Footer */}
      <footer className="relative z-10 border-t border-white/10 bg-black/20 backdrop-blur-sm">
        <div className="container-responsive py-8 sm:py-12">
          <motion.div
            className="flex flex-col items-center text-center space-y-4 sm:space-y-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Logo */}
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-purple-400 to-pink-400 rounded-lg flex items-center justify-center">
                <Music className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
              </div>
              <span className="text-white font-semibold text-lg sm:text-xl">Festival Family</span>
            </div>

            {/* Navigation Links */}
            <div className="flex flex-wrap justify-center gap-4 sm:gap-6 text-white/60 text-sm sm:text-base">
              <Link
                to="/help"
                className="hover:text-white transition-colors touch-target px-2 py-1"
                style={{ touchAction: 'manipulation' }}
              >
                Help
              </Link>
              <Link
                to="/emergency"
                className="hover:text-white transition-colors touch-target px-2 py-1"
                style={{ touchAction: 'manipulation' }}
              >
                Safety
              </Link>
              <Link
                to="/about"
                className="hover:text-white transition-colors touch-target px-2 py-1"
                style={{ touchAction: 'manipulation' }}
              >
                About
              </Link>
            </div>

            {/* Copyright */}
            <div className="text-white/50 text-xs sm:text-sm">
              © {new Date().getFullYear()} Festival Family. All rights reserved.
            </div>

            {/* Mobile-safe bottom spacing */}
            <div className="h-4 sm:h-0" />
          </motion.div>
        </div>
      </footer>

      {/* Development-only Mobile UX Testing Tool */}
      <MobileUXTester />
    </div>
  );
};

export default PublicLanding;

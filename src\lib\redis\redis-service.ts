/**
 * Redis Service for Festival Family
 * 
 * Provides high-performance caching and real-time features using Upstash Redis.
 * Designed to eliminate performance bottlenecks and provide instant user feedback.
 * 
 * Key Features:
 * - Participant count caching with instant updates
 * - User interaction state caching (join/favorite status)
 * - Real-time activity feed with Redis streams
 * - Subscription storm elimination with Redis pub/sub
 * - Rate limiting for user interactions
 * 
 * @module RedisService
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface RedisConfig {
  url: string
  token: string
  enableLogging?: boolean
}

export interface ParticipantCountCache {
  activityId: string
  count: number
  lastUpdated: number
}

export interface UserInteractionCache {
  userId: string
  activityId: string
  isParticipant: boolean
  isFavorite: boolean
  lastUpdated: number
}

export interface ActivityFeedItem {
  id: string
  type: 'join' | 'favorite' | 'view' | 'new_activity'
  userId: string
  activityId: string
  timestamp: number
  metadata?: Record<string, any>
}

export interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: number
}

// ============================================================================
// REDIS SERVICE CLASS
// ============================================================================

export class RedisService {
  private config: RedisConfig
  private baseUrl: string
  private headers: Record<string, string>

  constructor(config: RedisConfig) {
    this.config = config
    this.baseUrl = config.url
    this.headers = {
      'Authorization': `Bearer ${config.token}`,
      'Content-Type': 'application/json'
    }
  }

  // ========================================================================
  // CORE REDIS OPERATIONS
  // ========================================================================

  private async executeCommand(command: string[]): Promise<any> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify(command)
      })

      if (!response.ok) {
        throw new Error(`Redis command failed: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      
      if (this.config.enableLogging) {
        console.log(`🔴 Redis: ${command[0]} ->`, result)
      }

      return result.result
    } catch (error) {
      console.error('🚨 Redis Error:', error)
      throw error
    }
  }

  // ========================================================================
  // PARTICIPANT COUNT CACHING
  // ========================================================================

  /**
   * Cache participant count for an activity
   */
  async cacheParticipantCount(activityId: string, count: number): Promise<void> {
    const key = `activity:${activityId}:participants:count`
    const data: ParticipantCountCache = {
      activityId,
      count,
      lastUpdated: Date.now()
    }

    await this.executeCommand(['SET', key, JSON.stringify(data), 'EX', '3600']) // 1 hour TTL
    
    // Publish update for real-time subscribers
    await this.executeCommand(['PUBLISH', `activity:${activityId}:participants`, JSON.stringify({ count })])
  }

  /**
   * Get cached participant count
   */
  async getParticipantCount(activityId: string): Promise<number | null> {
    const key = `activity:${activityId}:participants:count`
    const cached = await this.executeCommand(['GET', key])
    
    if (!cached) return null
    
    try {
      const data: ParticipantCountCache = JSON.parse(cached)
      return data.count
    } catch {
      return null
    }
  }

  /**
   * Increment participant count atomically
   */
  async incrementParticipantCount(activityId: string): Promise<number> {
    const key = `activity:${activityId}:participants:count`
    
    // Use Lua script for atomic increment with cache update
    const luaScript = `
      local key = KEYS[1]
      local current = redis.call('GET', key)
      local count = 0
      
      if current then
        local data = cjson.decode(current)
        count = data.count + 1
      else
        count = 1
      end
      
      local newData = cjson.encode({
        activityId = ARGV[1],
        count = count,
        lastUpdated = tonumber(ARGV[2])
      })
      
      redis.call('SET', key, newData, 'EX', 3600)
      redis.call('PUBLISH', 'activity:' .. ARGV[1] .. ':participants', cjson.encode({count = count}))
      
      return count
    `

    return await this.executeCommand(['EVAL', luaScript, '1', key, activityId, Date.now().toString()])
  }

  /**
   * Decrement participant count atomically
   */
  async decrementParticipantCount(activityId: string): Promise<number> {
    const key = `activity:${activityId}:participants:count`
    
    const luaScript = `
      local key = KEYS[1]
      local current = redis.call('GET', key)
      local count = 0
      
      if current then
        local data = cjson.decode(current)
        count = math.max(0, data.count - 1)
      end
      
      local newData = cjson.encode({
        activityId = ARGV[1],
        count = count,
        lastUpdated = tonumber(ARGV[2])
      })
      
      redis.call('SET', key, newData, 'EX', 3600)
      redis.call('PUBLISH', 'activity:' .. ARGV[1] .. ':participants', cjson.encode({count = count}))
      
      return count
    `

    return await this.executeCommand(['EVAL', luaScript, '1', key, activityId, Date.now().toString()])
  }

  // ========================================================================
  // USER INTERACTION CACHING
  // ========================================================================

  /**
   * Cache user interaction state
   */
  async cacheUserInteraction(userId: string, activityId: string, isParticipant: boolean, isFavorite: boolean): Promise<void> {
    const key = `user:${userId}:activity:${activityId}:interaction`
    const data: UserInteractionCache = {
      userId,
      activityId,
      isParticipant,
      isFavorite,
      lastUpdated: Date.now()
    }

    await this.executeCommand(['SET', key, JSON.stringify(data), 'EX', '1800']) // 30 minutes TTL
  }

  /**
   * Get cached user interaction state
   */
  async getUserInteraction(userId: string, activityId: string): Promise<UserInteractionCache | null> {
    const key = `user:${userId}:activity:${activityId}:interaction`
    const cached = await this.executeCommand(['GET', key])
    
    if (!cached) return null
    
    try {
      return JSON.parse(cached)
    } catch {
      return null
    }
  }

  // ========================================================================
  // ACTIVITY FEED WITH REDIS STREAMS
  // ========================================================================

  /**
   * Add item to activity feed stream
   */
  async addToActivityFeed(item: Omit<ActivityFeedItem, 'id' | 'timestamp'>): Promise<string> {
    const streamKey = 'activity:feed'
    const id = await this.executeCommand([
      'XADD', streamKey, '*',
      'type', item.type,
      'userId', item.userId,
      'activityId', item.activityId,
      'metadata', JSON.stringify(item.metadata || {})
    ])

    // Keep only last 1000 items
    await this.executeCommand(['XTRIM', streamKey, 'MAXLEN', '~', '1000'])
    
    return id
  }

  /**
   * Get recent activity feed items
   */
  async getActivityFeed(limit: number = 20): Promise<ActivityFeedItem[]> {
    const streamKey = 'activity:feed'
    const result = await this.executeCommand(['XREVRANGE', streamKey, '+', '-', 'COUNT', limit.toString()])
    
    return result.map(([id, fields]: [string, string[]]) => {
      const fieldMap: Record<string, string> = {}
      for (let i = 0; i < fields.length; i += 2) {
        fieldMap[fields[i]] = fields[i + 1]
      }
      
      return {
        id,
        type: fieldMap.type as ActivityFeedItem['type'],
        userId: fieldMap.userId,
        activityId: fieldMap.activityId,
        timestamp: parseInt(id.split('-')[0]),
        metadata: JSON.parse(fieldMap.metadata || '{}')
      }
    })
  }

  // ========================================================================
  // RATE LIMITING
  // ========================================================================

  /**
   * Check rate limit for user action
   */
  async checkRateLimit(userId: string, action: string, limit: number = 10, windowSeconds: number = 60): Promise<RateLimitResult> {
    const key = `ratelimit:${userId}:${action}`
    const now = Date.now()
    const windowStart = now - (windowSeconds * 1000)

    const luaScript = `
      local key = KEYS[1]
      local now = tonumber(ARGV[1])
      local windowStart = tonumber(ARGV[2])
      local limit = tonumber(ARGV[3])
      local windowSeconds = tonumber(ARGV[4])
      
      -- Remove old entries
      redis.call('ZREMRANGEBYSCORE', key, 0, windowStart)
      
      -- Count current entries
      local current = redis.call('ZCARD', key)
      
      if current < limit then
        -- Add new entry
        redis.call('ZADD', key, now, now)
        redis.call('EXPIRE', key, windowSeconds)
        return {1, limit - current - 1, now + (windowSeconds * 1000)}
      else
        return {0, 0, now + (windowSeconds * 1000)}
      end
    `

    const result = await this.executeCommand([
      'EVAL', luaScript, '1', key, 
      now.toString(), windowStart.toString(), limit.toString(), windowSeconds.toString()
    ])

    return {
      allowed: result[0] === 1,
      remaining: result[1],
      resetTime: result[2]
    }
  }

  // ========================================================================
  // UTILITY METHODS
  // ========================================================================

  /**
   * Clear all cached data for an activity
   */
  async clearActivityCache(activityId: string): Promise<void> {
    const pattern = `activity:${activityId}:*`
    const keys = await this.executeCommand(['KEYS', pattern])
    
    if (keys.length > 0) {
      await this.executeCommand(['DEL', ...keys])
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<Record<string, any>> {
    const info = await this.executeCommand(['INFO', 'memory'])
    const keyspace = await this.executeCommand(['INFO', 'keyspace'])
    
    return {
      memory: info,
      keyspace: keyspace,
      timestamp: Date.now()
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.executeCommand(['PING'])
      return result === 'PONG'
    } catch {
      return false
    }
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

let redisInstance: RedisService | null = null

export function createRedisService(config: RedisConfig): RedisService {
  if (!redisInstance) {
    redisInstance = new RedisService(config)
  }
  return redisInstance
}

export function getRedisService(): RedisService | null {
  return redisInstance
}

// ============================================================================
// EXPORTS
// ============================================================================

export default RedisService

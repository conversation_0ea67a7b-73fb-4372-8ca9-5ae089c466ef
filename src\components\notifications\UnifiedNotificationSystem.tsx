import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { BentoCard } from '@/components/design-system/BentoGrid';
import { EnhancedUnifiedBadge } from '@/components/design-system';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { useUnifiedNotifications, type UnifiedNotification } from '@/hooks/useUnifiedNotifications';
import {
  Bell,
  X,
  AlertTriangle,
  Info,
  CheckCircle,
  AlertCircle,
  Megaphone,
  Clock,
  Users,
  Star
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface UnifiedNotificationSystemProps {
  displayType?: 'banner' | 'popup' | 'feed' | 'toast';
  limit?: number;
  showHeader?: boolean;
  className?: string;
  autoRefresh?: boolean;
}

export const UnifiedNotificationSystem: React.FC<UnifiedNotificationSystemProps> = ({
  displayType = 'feed',
  limit = 10,
  showHeader = true,
  className = '',
  autoRefresh = true
}) => {
  const { notifications, isLoading, error, dismissNotification } = useUnifiedNotifications({
    displayType,
    limit,
    autoRefresh,
    refreshInterval: 30000
  });

  // Get notification icon
  const getNotificationIcon = (type: UnifiedNotification['type'], priority: UnifiedNotification['priority']) => {
    if (priority === 'high') {
      return <AlertTriangle className="w-5 h-5 text-destructive" />;
    }

    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-festival-success" />;
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-festival-warning" />;
      case 'error':
        return <AlertTriangle className="w-5 h-5 text-destructive" />;
      case 'urgent':
        return <Megaphone className="w-5 h-5 text-destructive" />;
      default:
        return <Info className="w-5 h-5 text-primary" />;
    }
  };

  // Get badge variant
  const getBadgeVariant = (type: UnifiedNotification['type'], priority: UnifiedNotification['priority']) => {
    if (priority === 'high') return 'destructive';

    switch (type) {
      case 'success':
        return 'success';
      case 'warning':
        return 'category';
      case 'error':
        return 'destructive';
      case 'urgent':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  // Get priority label
  const getPriorityLabel = (priority: UnifiedNotification['priority'], isPinned: boolean) => {
    if (isPinned) return 'Pinned';
    return priority.charAt(0).toUpperCase() + priority.slice(1);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8 text-muted-foreground">
        <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
        <p>{error}</p>
      </div>
    );
  }

  if (displayType === 'banner' && notifications.length > 0) {
    const notification = notifications[0]; // Show first high-priority notification
    return (
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        className={`bg-gradient-to-r ${
          notification.type === 'error' || notification.type === 'urgent' 
            ? 'from-destructive/20 to-destructive/10 border-destructive/30' 
            : 'from-primary/20 to-primary/10 border-primary/30'
        } border-b backdrop-blur-sm p-4 ${className}`}
      >
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center gap-3">
            {getNotificationIcon(notification.type, notification.priority)}
            <div>
              <h4 className="font-semibold text-sm">{notification.title}</h4>
              <p className="text-xs text-muted-foreground">{notification.content}</p>
            </div>
          </div>
          <button
            onClick={() => dismissNotification(notification.id)}
            className="p-1 rounded-full hover:bg-white/10 transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </motion.div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {showHeader && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bell className="w-5 h-5 text-primary" />
            <h3 className="text-lg font-semibold">Notifications</h3>
          </div>
          {notifications.length > 0 && (
            <EnhancedUnifiedBadge variant="secondary" size="sm">
              {notifications.length}
            </EnhancedUnifiedBadge>
          )}
        </div>
      )}

      <div className="space-y-3">
        <AnimatePresence>
          {notifications.map((notification, index) => (
            <motion.div
              key={notification.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <BentoCard
                title=""
                variant="glassmorphism"
                className={`p-4 ${
                  notification.is_pinned ? 'border-primary/50 bg-primary/5' : ''
                } ${
                  notification.priority === 'high' ? 'border-destructive/50 bg-destructive/5' : ''
                }`}
              >
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 mt-1">
                    {getNotificationIcon(notification.type, notification.priority)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-2">
                      <h4 className="font-semibold text-sm text-foreground">
                        {notification.title}
                      </h4>
                      <button
                        onClick={() => dismissNotification(notification.id)}
                        className="p-1 rounded-full hover:bg-white/10 transition-colors flex-shrink-0"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                    
                    <p className="text-sm text-muted-foreground mt-1 line-clamp-3">
                      {notification.content}
                    </p>
                    
                    <div className="flex items-center gap-2 mt-2">
                      <Clock className="w-3 h-3 text-muted-foreground" />
                      <span className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                      </span>
                      
                      <EnhancedUnifiedBadge 
                        variant={getBadgeVariant(notification.type, notification.priority)} 
                        size="sm"
                      >
                        {getPriorityLabel(notification.priority, notification.is_pinned)}
                      </EnhancedUnifiedBadge>
                    </div>
                  </div>
                </div>
              </BentoCard>
            </motion.div>
          ))}
        </AnimatePresence>

        {notifications.length === 0 && (
          <div className="text-center p-8 text-muted-foreground">
            <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No notifications</p>
            <p className="text-xs mt-1">You're all caught up!</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default UnifiedNotificationSystem;

/**
 * Optimized Realtime Hook
 *
 * High-performance hook for real-time subscriptions with automatic cleanup,
 * connection pooling, and performance monitoring.
 */

import { useEffect, useRef, useCallback, useMemo } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { optimizedRealtimeService, type SubscriptionCallback } from '@/lib/supabase/services/optimized-realtime-service'

interface UseOptimizedRealtimeOptions<T extends { [key: string]: any }> {
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*'
  filter?: string
  priority?: 'high' | 'medium' | 'low'
  callback?: SubscriptionCallback<T>
  enabled?: boolean
  debounceMs?: number
}

/**
 * Optimized hook for real-time subscriptions with performance enhancements
 */
export function useOptimizedRealtime<T extends { [key: string]: any }>(
  table: string,
  queryKey: unknown[],
  options: UseOptimizedRealtimeOptions<T> = {}
) {
  const queryClient = useQueryClient()
  const subscriptionIdRef = useRef<string | null>(null)
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)
  const lastInvalidationRef = useRef<number>(0)
  
  const {
    event = '*',
    filter,
    priority = 'medium',
    callback,
    enabled = true,
    debounceMs = 100
  } = options

  // Memoize query key to prevent unnecessary re-subscriptions
  const stableQueryKey = useMemo(() => JSON.stringify(queryKey), [queryKey])

  // Debounced invalidation to prevent excessive re-renders
  const debouncedInvalidation = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current)
    }

    debounceTimerRef.current = setTimeout(() => {
      const now = Date.now()
      
      // Prevent invalidation spam (max once per 50ms)
      if (now - lastInvalidationRef.current > 50) {
        queryClient.invalidateQueries({ queryKey })
        lastInvalidationRef.current = now
      }
    }, debounceMs)
  }, [queryClient, queryKey, debounceMs])

  // Optimized callback that combines custom callback with query invalidation
  const optimizedCallback = useCallback<SubscriptionCallback<T>>(
    (payload) => {
      try {
        // Call custom callback first if provided
        if (callback) {
          callback(payload)
        }
        
        // Then invalidate queries with debouncing
        debouncedInvalidation()
        
      } catch (error) {
        console.error('❌ Optimized realtime callback error:', error)
      }
    },
    [callback, debouncedInvalidation]
  )

  useEffect(() => {
    if (!enabled) return

    // Subscribe with optimized service
    const subscriptionId = optimizedRealtimeService.subscribe<T>(
      table,
      optimizedCallback,
      {
        event,
        filter,
        priority
      }
    )

    subscriptionIdRef.current = subscriptionId

    console.log(`🚀 Optimized subscription active: ${table} (${subscriptionId})`)

    // Cleanup function
    return () => {
      if (subscriptionIdRef.current) {
        optimizedRealtimeService.unsubscribe(subscriptionIdRef.current)
        subscriptionIdRef.current = null
      }
      
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
        debounceTimerRef.current = null
      }
      
      console.log(`🔌 Optimized subscription cleaned up: ${table}`)
    }
  }, [table, stableQueryKey, event, filter, priority, enabled, optimizedCallback])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
    }
  }, [])
}

/**
 * Hook for subscribing to multiple tables efficiently
 */
export function useOptimizedMultiTableRealtime<T extends { [key: string]: any }>(
  subscriptions: Array<{
    table: string
    queryKey: unknown[]
    options?: UseOptimizedRealtimeOptions<T>
  }>
) {
  const queryClient = useQueryClient()
  const subscriptionIdsRef = useRef<string[]>([])

  useEffect(() => {
    const newSubscriptionIds: string[] = []

    // Create all subscriptions
    subscriptions.forEach(({ table, queryKey, options = {} }) => {
      const {
        event = '*',
        filter,
        priority = 'medium',
        callback,
        enabled = true
      } = options

      if (!enabled) return

      const optimizedCallback: SubscriptionCallback<T> = (payload) => {
        try {
          if (callback) {
            callback(payload)
          }
          queryClient.invalidateQueries({ queryKey })
        } catch (error) {
          console.error(`❌ Multi-table callback error for ${table}:`, error)
        }
      }

      const subscriptionId = optimizedRealtimeService.subscribe<T>(
        table,
        optimizedCallback,
        { event, filter, priority }
      )

      newSubscriptionIds.push(subscriptionId)
    })

    subscriptionIdsRef.current = newSubscriptionIds

    console.log(`🚀 Multi-table subscriptions active: ${newSubscriptionIds.length} tables`)

    // Cleanup function
    return () => {
      subscriptionIdsRef.current.forEach(subscriptionId => {
        optimizedRealtimeService.unsubscribe(subscriptionId)
      })
      subscriptionIdsRef.current = []
      
      console.log('🔌 Multi-table subscriptions cleaned up')
    }
  }, [subscriptions, queryClient])
}

/**
 * Hook for getting real-time performance metrics
 */
export function useRealtimeMetrics() {
  const metricsRef = useRef(optimizedRealtimeService.getPerformanceMetrics())

  useEffect(() => {
    const interval = setInterval(() => {
      metricsRef.current = optimizedRealtimeService.getPerformanceMetrics()
    }, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [])

  return metricsRef.current
}

/**
 * Hook for activity-specific real-time updates (high-priority)
 */
export function useActivityRealtime(
  activityId: string,
  queryKey: unknown[],
  options: Omit<UseOptimizedRealtimeOptions<any>, 'priority'> = {}
) {
  return useOptimizedRealtime(
    'activities',
    queryKey,
    {
      ...options,
      filter: `id=eq.${activityId}`,
      priority: 'high'
    }
  )
}

/**
 * Hook for participant updates (medium-priority)
 */
export function useParticipantRealtime(
  activityId: string,
  queryKey: unknown[],
  options: Omit<UseOptimizedRealtimeOptions<any>, 'priority' | 'filter'> = {}
) {
  return useOptimizedRealtime(
    'activity_participants',
    queryKey,
    {
      ...options,
      filter: `activity_id=eq.${activityId}`,
      priority: 'medium'
    }
  )
}

/**
 * Hook for user favorites updates (low-priority)
 */
export function useFavoritesRealtime(
  userId: string,
  queryKey: unknown[],
  options: Omit<UseOptimizedRealtimeOptions<any>, 'priority' | 'filter'> = {}
) {
  return useOptimizedRealtime(
    'user_favorites',
    queryKey,
    {
      ...options,
      filter: `user_id=eq.${userId}`,
      priority: 'low'
    }
  )
}

export default useOptimizedRealtime

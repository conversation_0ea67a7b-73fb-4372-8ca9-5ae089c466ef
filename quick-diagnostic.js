#!/usr/bin/env node

/**
 * Quick Diagnostic Script for Festival Family
 * 
 * This script quickly identifies the main issues blocking the app
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

console.log('🔍 Festival Family Quick Diagnostic');
console.log('==================================');

// Check 1: TypeScript compilation
console.log('\n1. 📝 TypeScript Compilation Check');
try {
  const tscOutput = execSync('npx tsc --noEmit', { encoding: 'utf8', stdio: 'pipe' });
  console.log('✅ TypeScript compilation: CLEAN');
} catch (error) {
  console.log('❌ TypeScript compilation: ERRORS FOUND');
  const errorLines = error.stdout.split('\n').slice(0, 10);
  console.log('First 10 errors:');
  errorLines.forEach(line => {
    if (line.trim()) console.log('   ', line);
  });
  console.log('   ... (run "npx tsc --noEmit" for full list)');
}

// Check 2: Critical files exist
console.log('\n2. 📁 Critical Files Check');
const criticalFiles = [
  'src/components/ui/button.tsx',
  'src/components/ui/card.tsx', 
  'src/types/supabase.ts',
  'src/types/database.ts',
  'jest.config.js',
  'playwright.config.js',
  'package.json'
];

criticalFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
  }
});

// Check 3: Environment variables
console.log('\n3. 🌍 Environment Variables Check');
const envFile = '.env';
if (fs.existsSync(envFile)) {
  console.log('✅ .env file exists');
  const envContent = fs.readFileSync(envFile, 'utf8');
  const hasSupabaseUrl = envContent.includes('VITE_SUPABASE_URL');
  const hasSupabaseKey = envContent.includes('VITE_SUPABASE_ANON_KEY');
  
  console.log(`${hasSupabaseUrl ? '✅' : '❌'} VITE_SUPABASE_URL`);
  console.log(`${hasSupabaseKey ? '✅' : '❌'} VITE_SUPABASE_ANON_KEY`);
} else {
  console.log('❌ .env file missing');
}

// Check 4: Dependencies
console.log('\n4. 📦 Dependencies Check');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const criticalDeps = [
    'react',
    'typescript', 
    '@supabase/supabase-js',
    'vite',
    'jest',
    '@playwright/test'
  ];
  
  criticalDeps.forEach(dep => {
    const hasInDeps = packageJson.dependencies?.[dep];
    const hasInDevDeps = packageJson.devDependencies?.[dep];
    
    if (hasInDeps || hasInDevDeps) {
      console.log(`✅ ${dep}`);
    } else {
      console.log(`❌ ${dep} - MISSING`);
    }
  });
} catch (error) {
  console.log('❌ Error reading package.json');
}

// Check 5: Test files
console.log('\n5. 🧪 Test Files Check');
const testDirs = ['src/__tests__', 'tests'];
testDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    const testFiles = fs.readdirSync(dir).filter(f => f.includes('.test.') || f.includes('.spec.'));
    console.log(`✅ ${dir}: ${testFiles.length} test files`);
  } else {
    console.log(`❌ ${dir} - MISSING`);
  }
});

// Check 6: Archive status
console.log('\n6. 📚 Archive Check');
if (fs.existsSync('archive')) {
  const archiveContents = fs.readdirSync('archive');
  console.log(`✅ Archive exists with ${archiveContents.length} folders`);
  
  // Check if important tests were archived
  const testScriptsArchived = fs.existsSync('archive/test-scripts');
  const testEvidenceArchived = fs.existsSync('archive/test-evidence');
  
  console.log(`${testScriptsArchived ? '⚠️' : '✅'} Test scripts ${testScriptsArchived ? 'ARCHIVED' : 'not archived'}`);
  console.log(`${testEvidenceArchived ? '⚠️' : '✅'} Test evidence ${testEvidenceArchived ? 'ARCHIVED' : 'not archived'}`);
} else {
  console.log('✅ No archive folder');
}

// Summary
console.log('\n🎯 QUICK DIAGNOSTIC SUMMARY');
console.log('===========================');
console.log('Next steps based on findings:');
console.log('1. Fix TypeScript errors (if any found above)');
console.log('2. Ensure all critical files exist');
console.log('3. Verify environment variables are set');
console.log('4. Run: npm run build');
console.log('5. Run: npm run dev');
console.log('6. Test admin login: <EMAIL> / testpassword123');

console.log('\n📋 Use RECOVERY_PLAN.md for detailed step-by-step instructions');
console.log('🚀 You have all the pieces - just need to fix the TypeScript issues!');

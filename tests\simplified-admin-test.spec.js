/**
 * Simplified Admin Testing
 * 
 * Streamlined admin testing to identify key functionality
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function takeEvidence(page, name, description) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `admin-${name}-${timestamp}.png`;
  await page.screenshot({ 
    path: `test-results/${filename}`,
    fullPage: true 
  });
  console.log(`📸 Admin Evidence: ${filename} - ${description}`);
  return filename;
}

async function loginAsAdmin(page) {
  console.log('🔐 Logging in as admin...');
  
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  
  const currentUrl = page.url();
  const loginSuccessful = !currentUrl.includes('/auth');
  
  if (loginSuccessful) {
    console.log('✅ Admin login successful');
  } else {
    console.log('❌ Admin login failed');
  }
  
  return loginSuccessful;
}

test.describe('Simplified Admin Testing', () => {
  
  test('Admin Authentication Test', async ({ page }) => {
    console.log('🧪 Testing admin authentication...');
    
    const loginSuccess = await loginAsAdmin(page);
    await takeEvidence(page, 'login-result', 'Admin login attempt result');
    
    expect(loginSuccess).toBe(true);
    
    // Test session persistence
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const stillLoggedIn = !page.url().includes('/auth');
    await takeEvidence(page, 'session-persistence', 'Session persistence after reload');
    
    console.log(`Session persistence: ${stillLoggedIn ? 'Working' : 'Failed'}`);
  });

  test('Admin Navigation Test', async ({ page }) => {
    console.log('🧪 Testing admin navigation...');
    
    const loginSuccess = await loginAsAdmin(page);
    if (!loginSuccess) {
      console.log('⚠️ Skipping navigation test - login failed');
      return;
    }
    
    // Look for admin navigation
    const adminLinks = await page.locator('a[href*="/admin"], button:has-text("Admin"), a:has-text("Admin")').count();
    console.log(`Found ${adminLinks} admin navigation elements`);
    
    await takeEvidence(page, 'admin-navigation', 'Admin navigation elements');
    
    // Try to navigate to admin area
    if (adminLinks > 0) {
      const adminLink = page.locator('a[href*="/admin"], button:has-text("Admin"), a:has-text("Admin")').first();
      await adminLink.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      await takeEvidence(page, 'admin-area', 'Admin area after navigation');
      
      const currentUrl = page.url();
      const inAdminArea = currentUrl.includes('/admin');
      console.log(`Successfully navigated to admin area: ${inAdminArea}`);
      console.log(`Current URL: ${currentUrl}`);
    } else {
      console.log('⚠️ No admin navigation elements found');
    }
  });

  test('Admin Interface Analysis', async ({ page }) => {
    console.log('🧪 Analyzing admin interface...');
    
    const loginSuccess = await loginAsAdmin(page);
    if (!loginSuccess) {
      console.log('⚠️ Skipping interface analysis - login failed');
      return;
    }
    
    // Navigate to admin area
    const adminLink = page.locator('a[href*="/admin"], button:has-text("Admin"), a:has-text("Admin")').first();
    if (await adminLink.isVisible()) {
      await adminLink.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
    }
    
    // Analyze admin interface elements
    const interfaceAnalysis = {
      forms: await page.locator('form').count(),
      tables: await page.locator('table').count(),
      buttons: await page.locator('button').count(),
      inputs: await page.locator('input').count(),
      links: await page.locator('a').count(),
      headings: await page.locator('h1, h2, h3').count()
    };
    
    console.log('Admin interface analysis:', interfaceAnalysis);
    
    await takeEvidence(page, 'interface-analysis', 'Admin interface elements analysis');
    
    // Look for specific admin sections
    const adminSections = [
      'users', 'events', 'activities', 'content', 'settings', 
      'dashboard', 'management', 'admin'
    ];
    
    const foundSections = [];
    for (const section of adminSections) {
      const sectionElements = await page.locator(`a:has-text("${section}"), button:has-text("${section}"), h1:has-text("${section}"), h2:has-text("${section}")`, { timeout: 1000 }).count();
      if (sectionElements > 0) {
        foundSections.push(section);
      }
    }
    
    console.log(`Found admin sections: ${foundSections.join(', ')}`);
    
    // Test if we can create/edit content
    const hasCreateButtons = await page.locator('button:has-text("Create"), button:has-text("Add"), button:has-text("New")').count();
    const hasEditButtons = await page.locator('button:has-text("Edit"), button:has-text("Update"), a:has-text("Edit")').count();
    
    console.log(`Create buttons found: ${hasCreateButtons}`);
    console.log(`Edit buttons found: ${hasEditButtons}`);
    
    const adminFunctionality = {
      hasForms: interfaceAnalysis.forms > 0,
      hasCreateButtons: hasCreateButtons > 0,
      hasEditButtons: hasEditButtons > 0,
      foundSections: foundSections.length,
      totalElements: Object.values(interfaceAnalysis).reduce((a, b) => a + b, 0)
    };
    
    console.log('Admin functionality summary:', adminFunctionality);
    
    // Basic functionality check
    expect(adminFunctionality.totalElements).toBeGreaterThan(10); // Should have some interface elements
  });

  test('Database Connection Test', async ({ page }) => {
    console.log('🧪 Testing database connection...');
    
    const loginSuccess = await loginAsAdmin(page);
    if (!loginSuccess) {
      console.log('⚠️ Skipping database test - login failed');
      return;
    }
    
    // Check for database-related errors in console
    const consoleErrors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Navigate through pages that should load data
    const testPages = ['/activities', '/discover', '/profile'];
    const pageResults = [];
    
    for (const testPage of testPages) {
      try {
        await page.goto(testPage);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        // Check for loading indicators or data
        const hasLoadingIndicator = await page.locator('.loading, .spinner, [data-testid*="loading"]').count() > 0;
        const hasData = await page.locator('.card, .item, .list, table tr').count() > 1;
        const hasEmptyState = await page.locator('text="No data", text="Empty", text="Nothing found"').count() > 0;
        const hasError = await page.locator('text="Error", text="Failed", text="Unable"').count() > 0;
        
        pageResults.push({
          page: testPage,
          hasLoadingIndicator,
          hasData,
          hasEmptyState,
          hasError,
          success: (hasData || hasEmptyState) && !hasError
        });
        
        await takeEvidence(page, `db-test-${testPage.replace('/', '')}`, `Database test for ${testPage}`);
        
      } catch (error) {
        console.log(`❌ Database test failed for ${testPage}: ${error.message}`);
        pageResults.push({
          page: testPage,
          success: false,
          error: error.message
        });
      }
    }
    
    console.log('Database connection test results:', pageResults);
    console.log(`Console errors during testing: ${consoleErrors.length}`);
    
    if (consoleErrors.length > 0) {
      console.log('Console errors:', consoleErrors.slice(0, 5)); // Show first 5 errors
    }
    
    const successfulPages = pageResults.filter(r => r.success).length;
    console.log(`Database connectivity: ${successfulPages}/${pageResults.length} pages working`);
    
    // At least some pages should work
    expect(successfulPages).toBeGreaterThan(0);
  });
});

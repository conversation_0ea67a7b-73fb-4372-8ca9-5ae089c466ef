#!/usr/bin/env node

/**
 * Development Server Manager
 * 
 * This script provides robust development server management with:
 * - Automatic restart on crashes
 * - Health monitoring
 * - Process cleanup
 * - Detailed logging
 */

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';

const SERVER_PORT = 5173;
const SERVER_HOST = 'localhost';
const SERVER_URL = `http://${SERVER_HOST}:${SERVER_PORT}`;
const MAX_RESTART_ATTEMPTS = 5;
const HEALTH_CHECK_INTERVAL = 10000; // 10 seconds
const STARTUP_TIMEOUT = 30000; // 30 seconds

let devServerProcess = null;
let restartAttempts = 0;
let isShuttingDown = false;
let healthCheckInterval = null;

// Logging functions
function log(message, type = 'INFO') {
  const timestamp = new Date().toISOString();
  const prefix = {
    'INFO': '📋',
    'SUCCESS': '✅',
    'ERROR': '❌',
    'WARNING': '⚠️',
    'DEBUG': '🔍'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function logServerOutput(data, isError = false) {
  const output = data.toString().trim();
  if (output) {
    const prefix = isError ? '🔴 SERVER ERROR' : '🟢 SERVER';
    console.log(`${prefix}: ${output}`);
  }
}

// Health check function
async function checkServerHealth() {
  try {
    const response = await fetch(SERVER_URL, {
      method: 'GET',
      timeout: 5000
    });
    
    if (response.ok) {
      log('Server health check passed', 'SUCCESS');
      return true;
    } else {
      log(`Server health check failed: HTTP ${response.status}`, 'WARNING');
      return false;
    }
  } catch (error) {
    log(`Server health check failed: ${error.message}`, 'WARNING');
    return false;
  }
}

// Start health monitoring
function startHealthMonitoring() {
  if (healthCheckInterval) {
    clearInterval(healthCheckInterval);
  }
  
  healthCheckInterval = setInterval(async () => {
    if (devServerProcess && !isShuttingDown) {
      const isHealthy = await checkServerHealth();
      if (!isHealthy && devServerProcess) {
        log('Server appears unhealthy, attempting restart...', 'WARNING');
        await restartServer();
      }
    }
  }, HEALTH_CHECK_INTERVAL);
}

// Stop health monitoring
function stopHealthMonitoring() {
  if (healthCheckInterval) {
    clearInterval(healthCheckInterval);
    healthCheckInterval = null;
  }
}

// Kill existing processes on port
async function killExistingProcesses() {
  try {
    log('Checking for existing processes on port 5173...', 'DEBUG');
    
    // Windows command to find and kill processes on port 5173
    const findProcess = spawn('powershell', [
      '-Command',
      `Get-NetTCPConnection -LocalPort 5173 -ErrorAction SilentlyContinue | ForEach-Object { Stop-Process -Id $_.OwningProcess -Force -ErrorAction SilentlyContinue }`
    ], { stdio: 'pipe' });
    
    await new Promise((resolve) => {
      findProcess.on('close', () => {
        log('Cleaned up existing processes', 'SUCCESS');
        resolve();
      });
      
      setTimeout(resolve, 3000); // Timeout after 3 seconds
    });
  } catch (error) {
    log(`Process cleanup warning: ${error.message}`, 'WARNING');
  }
}

// Start the development server
async function startServer() {
  if (devServerProcess) {
    log('Server process already exists, killing first...', 'DEBUG');
    devServerProcess.kill('SIGTERM');
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  log('Starting development server...', 'INFO');
  
  devServerProcess = spawn('npm', ['run', 'dev'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    shell: true,
    env: {
      ...process.env,
      NODE_ENV: 'development',
      FORCE_COLOR: '1'
    }
  });

  let serverReady = false;
  let startupOutput = '';
  
  // Set up startup timeout
  const startupTimeout = setTimeout(() => {
    if (!serverReady) {
      log('Server startup timeout - killing process', 'ERROR');
      if (devServerProcess) {
        devServerProcess.kill('SIGTERM');
      }
    }
  }, STARTUP_TIMEOUT);

  // Handle server output
  devServerProcess.stdout.on('data', (data) => {
    const output = data.toString();
    startupOutput += output;
    logServerOutput(data);
    
    // Check for server ready indicators
    if ((output.includes('ready in') || output.includes('Local:')) && 
        (output.includes('5173') || output.includes('localhost'))) {
      if (!serverReady) {
        serverReady = true;
        clearTimeout(startupTimeout);
        log('Development server is ready!', 'SUCCESS');
        log(`Server URL: ${SERVER_URL}`, 'INFO');
        
        // Start health monitoring after successful startup
        setTimeout(() => {
          startHealthMonitoring();
        }, 5000);
      }
    }
  });

  // Handle server errors
  devServerProcess.stderr.on('data', (data) => {
    logServerOutput(data, true);
    
    const error = data.toString();
    if (error.includes('EADDRINUSE') || error.includes('Port') || error.includes('already in use')) {
      log('Port conflict detected, attempting cleanup...', 'WARNING');
      clearTimeout(startupTimeout);
      killExistingProcesses().then(() => {
        setTimeout(() => startServer(), 2000);
      });
    }
  });

  // Handle process exit
  devServerProcess.on('exit', (code, signal) => {
    clearTimeout(startupTimeout);
    stopHealthMonitoring();
    
    if (isShuttingDown) {
      log('Server shutdown complete', 'INFO');
      return;
    }
    
    if (code === 0) {
      log('Server exited normally', 'INFO');
    } else {
      log(`Server exited with code ${code} (signal: ${signal})`, 'ERROR');
      
      if (restartAttempts < MAX_RESTART_ATTEMPTS) {
        restartAttempts++;
        log(`Attempting restart ${restartAttempts}/${MAX_RESTART_ATTEMPTS}...`, 'WARNING');
        setTimeout(() => startServer(), 3000);
      } else {
        log('Maximum restart attempts reached. Manual intervention required.', 'ERROR');
        process.exit(1);
      }
    }
  });

  // Handle process errors
  devServerProcess.on('error', (error) => {
    clearTimeout(startupTimeout);
    log(`Server process error: ${error.message}`, 'ERROR');
    
    if (restartAttempts < MAX_RESTART_ATTEMPTS) {
      restartAttempts++;
      log(`Attempting restart ${restartAttempts}/${MAX_RESTART_ATTEMPTS}...`, 'WARNING');
      setTimeout(() => startServer(), 3000);
    }
  });

  return new Promise((resolve) => {
    const checkReady = () => {
      if (serverReady) {
        resolve(true);
      } else {
        setTimeout(checkReady, 1000);
      }
    };
    checkReady();
  });
}

// Restart the server
async function restartServer() {
  log('Restarting development server...', 'INFO');
  stopHealthMonitoring();
  
  if (devServerProcess) {
    devServerProcess.kill('SIGTERM');
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  await startServer();
}

// Graceful shutdown
async function shutdown() {
  if (isShuttingDown) return;
  
  isShuttingDown = true;
  log('Shutting down development server...', 'INFO');
  
  stopHealthMonitoring();
  
  if (devServerProcess) {
    devServerProcess.kill('SIGTERM');
    
    // Force kill after 5 seconds if not terminated
    setTimeout(() => {
      if (devServerProcess) {
        log('Force killing server process...', 'WARNING');
        devServerProcess.kill('SIGKILL');
      }
    }, 5000);
  }
  
  setTimeout(() => {
    process.exit(0);
  }, 6000);
}

// Main function
async function main() {
  log('🚀 Development Server Manager Starting', 'INFO');
  log(`Target URL: ${SERVER_URL}`, 'INFO');
  
  // Set up signal handlers for graceful shutdown
  process.on('SIGINT', shutdown);
  process.on('SIGTERM', shutdown);
  process.on('SIGQUIT', shutdown);
  
  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    log(`Uncaught exception: ${error.message}`, 'ERROR');
    shutdown();
  });
  
  process.on('unhandledRejection', (reason, promise) => {
    log(`Unhandled rejection: ${reason}`, 'ERROR');
    shutdown();
  });
  
  try {
    // Clean up any existing processes
    await killExistingProcesses();
    
    // Start the server
    await startServer();
    
    log('Development server manager is running. Press Ctrl+C to stop.', 'SUCCESS');
    
  } catch (error) {
    log(`Failed to start server: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Run the manager
main().catch((error) => {
  log(`Manager error: ${error.message}`, 'ERROR');
  process.exit(1);
});

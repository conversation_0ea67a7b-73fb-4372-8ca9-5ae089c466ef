/**
 * Test User Interaction Functionality
 * 
 * Tests the interactive features of activity cards: <PERSON><PERSON>, Jo<PERSON>, Click handlers
 */

import { test, expect } from '@playwright/test';

test('Test User Activity Card Interactions', async ({ page }) => {
  console.log('🧪 Testing user activity card interactions...');
  
  // Navigate to activities page
  await page.goto('/activities');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000); // Give time for data loading
  
  console.log('📋 Checking activity cards...');
  
  // Check if activity cards are visible
  const activityCards = await page.locator('.card').count();
  console.log(`Found ${activityCards} activity cards`);
  
  if (activityCards > 0) {
    console.log('✅ Activity cards are visible');
    
    // Test "Details" button functionality
    console.log('\n🔍 Testing Details button...');
    
    const detailsButtons = await page.locator('button:has-text("Details")').count();
    console.log(`Found ${detailsButtons} Details buttons`);
    
    if (detailsButtons > 0) {
      const firstDetailsButton = page.locator('button:has-text("Details")').first();
      
      // Check if button is clickable
      const isClickable = await firstDetailsButton.isEnabled();
      console.log(`Details button clickable: ${isClickable}`);
      
      if (isClickable) {
        const initialUrl = page.url();
        
        // Click the Details button
        await firstDetailsButton.click();
        await page.waitForTimeout(2000);
        
        const newUrl = page.url();
        const urlChanged = newUrl !== initialUrl;
        
        // Check for modal/popup
        const hasModal = await page.locator('[role="dialog"], .modal, .popup').count() > 0;
        const hasOverlay = await page.locator('.overlay, [data-testid*="overlay"]').count() > 0;
        
        console.log(`  URL changed: ${urlChanged} (${initialUrl} → ${newUrl})`);
        console.log(`  Modal opened: ${hasModal}`);
        console.log(`  Overlay visible: ${hasOverlay}`);
        
        await page.screenshot({ path: 'test-results/details-button-clicked.png', fullPage: true });
        
        if (urlChanged || hasModal || hasOverlay) {
          console.log('✅ Details button functionality working');
        } else {
          console.log('⚠️ Details button clicked but no visible response');
        }
      }
    }
    
    // Test "Join Activity" button functionality
    console.log('\n🤝 Testing Join Activity button...');
    
    // Go back to activities page if we navigated away
    await page.goto('/activities');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const joinButtons = await page.locator('button:has-text("Join Activity"), button:has-text("Join")').count();
    console.log(`Found ${joinButtons} Join buttons`);
    
    if (joinButtons > 0) {
      const firstJoinButton = page.locator('button:has-text("Join Activity"), button:has-text("Join")').first();
      
      const isClickable = await firstJoinButton.isEnabled();
      console.log(`Join button clickable: ${isClickable}`);
      
      if (isClickable) {
        const initialText = await firstJoinButton.textContent();
        console.log(`Initial button text: "${initialText}"`);
        
        // Click the Join button
        await firstJoinButton.click();
        await page.waitForTimeout(2000);
        
        const newText = await firstJoinButton.textContent();
        const textChanged = newText !== initialText;
        
        // Check for success messages
        const hasToast = await page.locator('.toast, [data-testid*="toast"], [class*="toast"]').count() > 0;
        const hasSuccessMessage = await page.locator('text="Joined", text="Success"').count() > 0;
        
        console.log(`  Button text changed: ${textChanged} ("${initialText}" → "${newText}")`);
        console.log(`  Toast notification: ${hasToast}`);
        console.log(`  Success message: ${hasSuccessMessage}`);
        
        await page.screenshot({ path: 'test-results/join-button-clicked.png', fullPage: true });
        
        if (textChanged || hasToast || hasSuccessMessage) {
          console.log('✅ Join button functionality working');
        } else {
          console.log('⚠️ Join button clicked but no visible response');
        }
      }
    }
    
    // Test card click functionality (clicking on the card itself)
    console.log('\n👆 Testing card click functionality...');
    
    const firstCard = page.locator('.card').first();
    const cardClickable = await firstCard.isVisible();
    
    if (cardClickable) {
      const initialUrl = page.url();
      
      // Click on the card (not on buttons)
      await firstCard.click({ position: { x: 100, y: 100 } }); // Click in the middle-left area
      await page.waitForTimeout(2000);
      
      const newUrl = page.url();
      const urlChanged = newUrl !== initialUrl;
      
      // Check for modal/popup
      const hasModal = await page.locator('[role="dialog"], .modal, .popup').count() > 0;
      
      console.log(`  Card click URL changed: ${urlChanged}`);
      console.log(`  Card click modal opened: ${hasModal}`);
      
      if (urlChanged || hasModal) {
        console.log('✅ Card click functionality working');
      } else {
        console.log('⚠️ Card click has no visible response (might be intentional)');
      }
    }
    
    // Test favorite/heart button
    console.log('\n❤️ Testing favorite button...');
    
    const heartButtons = await page.locator('button:has([data-testid*="heart"]), button svg[class*="heart"]').count();
    console.log(`Found ${heartButtons} heart/favorite buttons`);
    
    if (heartButtons > 0) {
      const firstHeartButton = page.locator('button:has([data-testid*="heart"]), button svg[class*="heart"]').first();
      
      await firstHeartButton.click();
      await page.waitForTimeout(1000);
      
      // Check if heart button state changed (filled vs outline)
      const heartFilled = await page.locator('svg[class*="fill-current"]').count() > 0;
      console.log(`  Heart button filled after click: ${heartFilled}`);
      
      if (heartFilled) {
        console.log('✅ Favorite button functionality working');
      } else {
        console.log('⚠️ Favorite button clicked but no visual change');
      }
    }
    
  } else {
    console.log('❌ No activity cards found - cannot test interactions');
  }
  
  // Final assessment
  console.log('\n📊 USER INTERACTION ASSESSMENT:');
  console.log('================================');
  console.log(`Activity cards visible: ${activityCards > 0 ? '✅ YES' : '❌ NO'}`);
  console.log(`Interactive buttons present: ${detailsButtons > 0 || joinButtons > 0 ? '✅ YES' : '❌ NO'}`);
  
  console.log('✅ User interaction test completed');
});

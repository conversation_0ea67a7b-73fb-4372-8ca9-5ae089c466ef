<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Festival Family Database Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .test-section.error {
            border-left-color: #f44336;
        }
        .test-section.warning {
            border-left-color: #ff9800;
        }
        .result {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .table-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .table-item {
            background: #333;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .table-item.exists {
            border: 2px solid #4CAF50;
        }
        .table-item.missing {
            border: 2px solid #f44336;
        }
    </style>
</head>
<body>
    <h1>🔍 Festival Family Database Test</h1>
    <p>This page tests the database connectivity and table existence for Festival Family.</p>

    <div class="test-section">
        <h2>📊 Database Connectivity Test</h2>
        <button onclick="testConnectivity()">Test Connection</button>
        <div id="connectivity-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📋 Table Existence Check</h2>
        <button onclick="checkTables()">Check All Tables</button>
        <div id="tables-result" class="result"></div>
        <div id="tables-grid" class="table-list"></div>
    </div>

    <div class="test-section">
        <h2>⚙️ Admin Functions Test</h2>
        <button onclick="testAdminFunctions()">Test Admin Functions</button>
        <div id="admin-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔄 CRUD Operations Test</h2>
        <button onclick="testCrudOperations()">Test CRUD</button>
        <div id="crud-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>👤 Profile System Test</h2>
        <button onclick="testProfileSystem()">Test Profile Updates</button>
        <div id="profile-result" class="result"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // Initialize Supabase client
        const supabaseUrl = 'https://ealstndyhwjwipzlrxmg.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVhbHN0bmR5aHdqd2lwemxyeG1nIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzcxOTYyNjcsImV4cCI6MjA1Mjc3MjI2N30.cmIkfiBKJbnZ2VN6MUrfI1kF_0J2s8frDtJwzn_dP4k';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        // Test 1: Database Connectivity
        async function testConnectivity() {
            const resultDiv = document.getElementById('connectivity-result');
            resultDiv.innerHTML = '🔄 Testing connectivity...';
            
            try {
                const { data, error } = await supabase
                    .from('profiles')
                    .select('count')
                    .limit(1);
                
                if (error) {
                    resultDiv.innerHTML = `❌ Connection failed: ${error.message}`;
                    resultDiv.className = 'result error';
                } else {
                    resultDiv.innerHTML = '✅ Database connection successful!';
                    resultDiv.className = 'result success';
                }
            } catch (err) {
                resultDiv.innerHTML = `💥 Connection error: ${err.message}`;
                resultDiv.className = 'result error';
            }
        }

        // Test 2: Table Existence Check
        async function checkTables() {
            const resultDiv = document.getElementById('tables-result');
            const gridDiv = document.getElementById('tables-grid');
            resultDiv.innerHTML = '🔄 Checking tables...';
            gridDiv.innerHTML = '';
            
            const tables = [
                'profiles', 'festivals', 'events', 'activities', 'groups', 'group_members',
                'announcements', 'tips', 'faqs', 'content_management', 'user_preferences',
                'emergency_contacts', 'safety_information', 'chat_rooms', 'chat_room_members', 'chat_messages'
            ];
            
            const results = {};
            let successCount = 0;
            
            for (const table of tables) {
                try {
                    const { data, error, count } = await supabase
                        .from(table)
                        .select('*', { count: 'exact' })
                        .limit(1);
                    
                    if (error) {
                        results[table] = { exists: false, error: error.message };
                    } else {
                        results[table] = { exists: true, count: count };
                        successCount++;
                    }
                } catch (err) {
                    results[table] = { exists: false, error: err.message };
                }
                
                // Create table item
                const tableItem = document.createElement('div');
                tableItem.className = `table-item ${results[table].exists ? 'exists' : 'missing'}`;
                tableItem.innerHTML = `
                    <strong>${table}</strong><br>
                    ${results[table].exists ? 
                        `✅ ${results[table].count} records` : 
                        `❌ Missing`
                    }
                `;
                gridDiv.appendChild(tableItem);
            }
            
            resultDiv.innerHTML = `📊 Tables Check Complete: ${successCount}/${tables.length} tables exist`;
            resultDiv.className = successCount === tables.length ? 'result success' : 'result warning';
        }

        // Test 3: Admin Functions
        async function testAdminFunctions() {
            const resultDiv = document.getElementById('admin-result');
            resultDiv.innerHTML = '🔄 Testing admin functions...';
            
            const functions = ['is_admin', 'is_super_admin', 'is_content_admin', 'can_manage_groups'];
            const results = [];
            
            for (const func of functions) {
                try {
                    const { data, error } = await supabase.rpc(func);
                    
                    if (error) {
                        results.push(`❌ ${func}: ${error.message}`);
                    } else {
                        results.push(`✅ ${func}: Working (result: ${data})`);
                    }
                } catch (err) {
                    results.push(`💥 ${func}: ${err.message}`);
                }
            }
            
            resultDiv.innerHTML = results.join('\n');
            resultDiv.className = 'result info';
        }

        // Test 4: CRUD Operations
        async function testCrudOperations() {
            const resultDiv = document.getElementById('crud-result');
            resultDiv.innerHTML = '🔄 Testing CRUD operations...';
            
            const results = [];
            
            // Test announcements CRUD (if table exists)
            try {
                // Test READ
                const { data: readData, error: readError } = await supabase
                    .from('announcements')
                    .select('*')
                    .limit(1);
                
                if (!readError) {
                    results.push(`✅ Announcements READ: Working (${readData?.length || 0} records)`);
                    
                    // Test CREATE
                    const testAnnouncement = {
                        title: 'Test Announcement',
                        content: 'This is a test announcement for validation.',
                        type: 'info',
                        priority: 'low',
                        is_active: false
                    };
                    
                    const { data: created, error: createError } = await supabase
                        .from('announcements')
                        .insert([testAnnouncement])
                        .select()
                        .single();
                    
                    if (!createError && created) {
                        results.push('✅ Announcements CREATE: Working');
                        
                        // Test UPDATE
                        const { error: updateError } = await supabase
                            .from('announcements')
                            .update({ title: 'Updated Test Announcement' })
                            .eq('id', created.id);
                        
                        if (!updateError) {
                            results.push('✅ Announcements UPDATE: Working');
                        }
                        
                        // Test DELETE
                        const { error: deleteError } = await supabase
                            .from('announcements')
                            .delete()
                            .eq('id', created.id);
                        
                        if (!deleteError) {
                            results.push('✅ Announcements DELETE: Working');
                        }
                    } else {
                        results.push(`❌ Announcements CREATE: ${createError?.message}`);
                    }
                } else {
                    results.push(`❌ Announcements READ: ${readError.message}`);
                }
            } catch (err) {
                results.push(`💥 Announcements CRUD: ${err.message}`);
            }
            
            resultDiv.innerHTML = results.join('\n');
            resultDiv.className = 'result info';
        }

        // Test 5: Profile System
        async function testProfileSystem() {
            const resultDiv = document.getElementById('profile-result');
            resultDiv.innerHTML = '🔄 Testing profile system...';
            
            const results = [];
            
            try {
                // Test profile read
                const { data: profiles, error: readError } = await supabase
                    .from('profiles')
                    .select('id, email, full_name, bio, website, interests')
                    .limit(1);
                
                if (!readError && profiles && profiles.length > 0) {
                    results.push(`✅ Profile READ: Working (${profiles.length} profiles)`);
                    
                    const profile = profiles[0];
                    results.push(`📋 Profile fields available: ${Object.keys(profile).join(', ')}`);
                    
                    // Check for extended fields
                    const extendedFields = ['bio', 'website', 'interests'];
                    const hasExtended = extendedFields.some(field => profile.hasOwnProperty(field));
                    
                    if (hasExtended) {
                        results.push('✅ Extended profile fields detected');
                    } else {
                        results.push('⚠️ Extended profile fields (bio, website, interests) not found');
                    }
                } else {
                    results.push(`❌ Profile read: ${readError?.message || 'No profiles found'}`);
                }
                
                // Test storage buckets
                const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
                
                if (!bucketsError && buckets) {
                    const avatarBucket = buckets.find(b => b.name === 'avatars');
                    if (avatarBucket) {
                        results.push('✅ Avatar storage bucket exists');
                    } else {
                        results.push('⚠️ Avatar storage bucket not found');
                    }
                } else {
                    results.push(`❌ Storage check: ${bucketsError?.message}`);
                }
                
            } catch (err) {
                results.push(`💥 Profile system test: ${err.message}`);
            }
            
            resultDiv.innerHTML = results.join('\n');
            resultDiv.className = 'result info';
        }

        // Auto-run connectivity test on page load
        window.addEventListener('load', () => {
            testConnectivity();
        });
    </script>
</body>
</html>

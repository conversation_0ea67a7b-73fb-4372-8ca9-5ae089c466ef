#!/usr/bin/env node

/**
 * Authentication Sign-Up and Login Testing Script
 * 
 * This script tests the complete authentication flow including:
 * 1. User registration (sign-up)
 * 2. User login (sign-in)
 * 3. Authenticated dashboard access
 * 4. Sign-out functionality
 */

import { chromium } from 'playwright';
import { promises as fs } from 'fs';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'auth-signup-evidence';

// Test user credentials
const TEST_USER = {
  email: `test.user.${Date.now()}@festivalfamily.test`,
  password: 'TestPassword123!',
  fullName: 'Test User'
};

async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

async function testUserSignUp(page) {
  console.log('\n📝 Step 1: Testing User Sign-Up');
  console.log('===============================');
  console.log(`👤 Test User: ${TEST_USER.email}`);
  
  // Navigate to auth page
  await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(2000);
  
  // Capture initial auth page
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/01-auth-page-initial.png`,
    fullPage: true 
  });
  
  try {
    // Look for sign-up mode or toggle
    const signUpTab = await page.$('text=Sign Up') || await page.$('text=Register') || await page.$('[data-testid="signup-tab"]');
    if (signUpTab) {
      console.log('🔄 Switching to Sign Up mode...');
      await signUpTab.click();
      await page.waitForTimeout(1000);
    }
    
    // Capture sign-up form
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/02-signup-form.png`,
      fullPage: true 
    });
    
    // Fill out sign-up form
    const emailInput = await page.$('input[type="email"]') || await page.$('input[name="email"]');
    const passwordInput = await page.$('input[type="password"]') || await page.$('input[name="password"]');
    const nameInput = await page.$('input[name="name"]') || await page.$('input[name="fullName"]') || await page.$('input[placeholder*="name"]');
    
    if (emailInput && passwordInput) {
      console.log('📧 Filling email field...');
      await emailInput.fill(TEST_USER.email);
      
      console.log('🔒 Filling password field...');
      await passwordInput.fill(TEST_USER.password);
      
      if (nameInput) {
        console.log('👤 Filling name field...');
        await nameInput.fill(TEST_USER.fullName);
      }
      
      // Capture filled form
      await page.screenshot({ 
        path: `${EVIDENCE_DIR}/03-signup-form-filled.png`,
        fullPage: true 
      });
      
      // Submit form
      const submitButton = await page.$('button[type="submit"]') || await page.$('text=Sign Up') || await page.$('text=Register');
      if (submitButton) {
        console.log('🚀 Submitting sign-up form...');
        await submitButton.click();
        
        // Wait for response
        await page.waitForTimeout(3000);
        
        // Capture result
        await page.screenshot({ 
          path: `${EVIDENCE_DIR}/04-signup-result.png`,
          fullPage: true 
        });
        
        // Check for success indicators
        const successMessage = await page.$('text=success') || await page.$('text=registered') || await page.$('text=welcome');
        const errorMessage = await page.$('text=error') || await page.$('text=failed') || await page.$('[class*="error"]');
        
        console.log(`✅ Success Message: ${successMessage ? 'Found' : 'Not Found'}`);
        console.log(`❌ Error Message: ${errorMessage ? 'Found' : 'Not Found'}`);
        
        return {
          formFilled: true,
          submitted: true,
          hasSuccess: !!successMessage,
          hasError: !!errorMessage,
          currentUrl: page.url()
        };
      } else {
        console.log('❌ Submit button not found');
        return { formFilled: true, submitted: false };
      }
    } else {
      console.log('❌ Required form fields not found');
      return { formFilled: false, submitted: false };
    }
    
  } catch (error) {
    console.error('❌ Sign-up test failed:', error.message);
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/04-signup-error.png`,
      fullPage: true 
    });
    return { error: error.message };
  }
}

async function testUserSignIn(page) {
  console.log('\n🔐 Step 2: Testing User Sign-In');
  console.log('===============================');
  
  // Navigate to auth page
  await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(2000);
  
  try {
    // Look for sign-in mode or toggle
    const signInTab = await page.$('text=Sign In') || await page.$('text=Login') || await page.$('[data-testid="signin-tab"]');
    if (signInTab) {
      console.log('🔄 Switching to Sign In mode...');
      await signInTab.click();
      await page.waitForTimeout(1000);
    }
    
    // Capture sign-in form
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/05-signin-form.png`,
      fullPage: true 
    });
    
    // Fill out sign-in form
    const emailInput = await page.$('input[type="email"]') || await page.$('input[name="email"]');
    const passwordInput = await page.$('input[type="password"]') || await page.$('input[name="password"]');
    
    if (emailInput && passwordInput) {
      console.log('📧 Filling email field...');
      await emailInput.fill(TEST_USER.email);
      
      console.log('🔒 Filling password field...');
      await passwordInput.fill(TEST_USER.password);
      
      // Capture filled form
      await page.screenshot({ 
        path: `${EVIDENCE_DIR}/06-signin-form-filled.png`,
        fullPage: true 
      });
      
      // Submit form
      const submitButton = await page.$('button[type="submit"]') || await page.$('text=Sign In') || await page.$('text=Login');
      if (submitButton) {
        console.log('🚀 Submitting sign-in form...');
        await submitButton.click();
        
        // Wait for response and potential redirect
        await page.waitForTimeout(5000);
        
        // Capture result
        await page.screenshot({ 
          path: `${EVIDENCE_DIR}/07-signin-result.png`,
          fullPage: true 
        });
        
        // Check current state
        const currentUrl = page.url();
        const welcomeMessage = await page.$('text=Welcome back') || await page.$('text=Welcome,') || await page.$('text=Dashboard');
        const signOutButton = await page.$('text=Sign Out') || await page.$('text=Logout');
        
        console.log(`🔗 Current URL: ${currentUrl}`);
        console.log(`👋 Welcome Message: ${welcomeMessage ? 'Found' : 'Not Found'}`);
        console.log(`🚪 Sign Out Button: ${signOutButton ? 'Found' : 'Not Found'}`);
        
        return {
          submitted: true,
          currentUrl,
          isAuthenticated: !!welcomeMessage || !!signOutButton,
          hasWelcomeMessage: !!welcomeMessage,
          hasSignOutButton: !!signOutButton
        };
      } else {
        console.log('❌ Submit button not found');
        return { submitted: false };
      }
    } else {
      console.log('❌ Required form fields not found');
      return { submitted: false };
    }
    
  } catch (error) {
    console.error('❌ Sign-in test failed:', error.message);
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/07-signin-error.png`,
      fullPage: true 
    });
    return { error: error.message };
  }
}

async function testAuthenticatedDashboard(page) {
  console.log('\n🏠 Step 3: Testing Authenticated Dashboard');
  console.log('==========================================');
  
  // Navigate to home page
  await page.goto(APP_URL, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  // Capture authenticated dashboard
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/08-authenticated-dashboard.png`,
    fullPage: true 
  });
  
  // Check for authenticated content
  const welcomeMessage = await page.$('text=Welcome back') || await page.$('text=Welcome,');
  const profileLink = await page.$('text=Profile') || await page.$('[href*="profile"]');
  const activitiesLink = await page.$('text=Activities') || await page.$('[href*="activities"]');
  const famhubLink = await page.$('text=FamHub') || await page.$('[href*="famhub"]');
  const discoverLink = await page.$('text=Discover') || await page.$('[href*="discover"]');
  
  console.log(`👋 Welcome Message: ${welcomeMessage ? '✅ Found' : '❌ Not Found'}`);
  console.log(`👤 Profile Link: ${profileLink ? '✅ Found' : '❌ Not Found'}`);
  console.log(`📅 Activities Link: ${activitiesLink ? '✅ Found' : '❌ Not Found'}`);
  console.log(`👥 FamHub Link: ${famhubLink ? '✅ Found' : '❌ Not Found'}`);
  console.log(`🔍 Discover Link: ${discoverLink ? '✅ Found' : '❌ Not Found'}`);
  
  return {
    hasWelcomeMessage: !!welcomeMessage,
    hasProfileLink: !!profileLink,
    hasActivitiesLink: !!activitiesLink,
    hasFamhubLink: !!famhubLink,
    hasDiscoverLink: !!discoverLink,
    navigationComplete: !!(profileLink && activitiesLink && famhubLink && discoverLink)
  };
}

async function runCompleteAuthTest() {
  console.log('🔐 COMPLETE AUTHENTICATION TESTING');
  console.log('==================================');
  console.log(`🕐 Start Time: ${new Date().toISOString()}`);
  console.log(`👤 Test User: ${TEST_USER.email}`);
  
  await ensureEvidenceDir();
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();
  
  try {
    const results = {};
    
    // Test 1: User Sign-Up
    results.signUp = await testUserSignUp(page);
    
    // Test 2: User Sign-In
    results.signIn = await testUserSignIn(page);
    
    // Test 3: Authenticated Dashboard
    if (results.signIn.isAuthenticated) {
      results.dashboard = await testAuthenticatedDashboard(page);
    } else {
      console.log('⚠️ Skipping dashboard test - user not authenticated');
      results.dashboard = { skipped: true, reason: 'User not authenticated' };
    }
    
    // Save comprehensive results
    const evidence = {
      timestamp: new Date().toISOString(),
      testUser: { email: TEST_USER.email, fullName: TEST_USER.fullName },
      testResults: results,
      summary: {
        signUpWorking: results.signUp.submitted && !results.signUp.error,
        signInWorking: results.signIn.submitted && results.signIn.isAuthenticated,
        dashboardAccessible: results.dashboard.navigationComplete || false,
        authFlowComplete: results.signIn.isAuthenticated && (results.dashboard.navigationComplete || false)
      }
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/complete-auth-test-results.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    console.log('\n📊 COMPLETE AUTHENTICATION TEST SUMMARY');
    console.log('========================================');
    console.log(`📝 Sign-Up: ${evidence.summary.signUpWorking ? '✅ Working' : '❌ Issues'}`);
    console.log(`🔐 Sign-In: ${evidence.summary.signInWorking ? '✅ Working' : '❌ Issues'}`);
    console.log(`🏠 Dashboard: ${evidence.summary.dashboardAccessible ? '✅ Accessible' : '❌ Issues'}`);
    console.log(`🎯 Auth Flow: ${evidence.summary.authFlowComplete ? '✅ Complete' : '❌ Incomplete'}`);
    console.log(`📁 Evidence: ${EVIDENCE_DIR}/`);
    
    return evidence;
    
  } catch (error) {
    console.error('\n💥 Complete authentication test failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

// Run the complete authentication test
runCompleteAuthTest()
  .then(() => {
    console.log('\n✅ Complete authentication testing finished');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Complete authentication testing failed:', error);
    process.exit(1);
  });

import React, { useState } from 'react';
import { <PERSON>, Sun, CloudRain, Wind, Thermometer, Droplets, Eye, Compass } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { cn } from '../lib/utils';

interface WeatherData {
  location: string;
  current: {
    temperature: number;
    condition: string;
    humidity: number;
    windSpeed: number;
    windDirection: string;
    visibility: number;
    uvIndex: number;
    feelsLike: number;
  };
  forecast: Array<{
    date: Date;
    high: number;
    low: number;
    condition: string;
    precipitation: number;
  }>;
  alerts?: Array<{
    type: 'warning' | 'watch' | 'advisory';
    title: string;
    description: string;
  }>;
}

const mockWeatherData: WeatherData = {
  location: 'Sziget Festival, Budapest',
  current: {
    temperature: 24,
    condition: 'Partly Cloudy',
    humidity: 65,
    windSpeed: 12,
    windDirection: 'NW',
    visibility: 10,
    uvIndex: 6,
    feelsLike: 26
  },
  forecast: [
    {
      date: new Date(),
      high: 26,
      low: 18,
      condition: 'Partly Cloudy',
      precipitation: 10
    },
    {
      date: new Date(Date.now() + 24 * 60 * 60 * 1000),
      high: 28,
      low: 20,
      condition: 'Sunny',
      precipitation: 0
    },
    {
      date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
      high: 25,
      low: 17,
      condition: 'Light Rain',
      precipitation: 70
    },
    {
      date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
      high: 23,
      low: 16,
      condition: 'Cloudy',
      precipitation: 30
    },
    {
      date: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000),
      high: 27,
      low: 19,
      condition: 'Sunny',
      precipitation: 5
    }
  ],
  alerts: [
    {
      type: 'advisory',
      title: 'UV Index High',
      description: 'UV levels will be high today. Consider wearing sunscreen and protective clothing.'
    }
  ]
};

const Weather: React.FC = () => {
  const [weatherData] = useState<WeatherData>(mockWeatherData);
  const [selectedLocation, setSelectedLocation] = useState('current');

  const getWeatherIcon = (condition: string) => {
    const lowerCondition = condition.toLowerCase();
    if (lowerCondition.includes('sunny') || lowerCondition.includes('clear')) {
      return Sun;
    } else if (lowerCondition.includes('rain')) {
      return CloudRain;
    } else if (lowerCondition.includes('cloud')) {
      return Cloud;
    }
    return Cloud;
  };

  const getConditionColor = (condition: string) => {
    const lowerCondition = condition.toLowerCase();
    if (lowerCondition.includes('sunny') || lowerCondition.includes('clear')) {
      return 'text-accent';
    } else if (lowerCondition.includes('rain')) {
      return 'text-primary';
    }
    return 'text-muted-foreground';
  };

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'warning': return 'bg-destructive/20 text-destructive border-destructive/30';
      case 'watch': return 'bg-accent/20 text-accent-foreground border-accent/30';
      case 'advisory': return 'bg-primary/20 text-primary border-primary/30';
      default: return 'bg-muted/20 text-muted-foreground border-border';
    }
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      return date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' });
    }
  };

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Cloud className="h-6 w-6 text-primary" />
          <h1 className="text-2xl font-bold text-foreground">Festival Weather</h1>
        </div>
        <Button variant="outline" size="sm">
          Change Location
        </Button>
      </div>

      {/* Weather Alerts */}
      {weatherData.alerts && weatherData.alerts.length > 0 && (
        <div className="mb-6 space-y-2">
          {weatherData.alerts.map((alert, index) => (
            <Card key={index} className={cn("border-2", getAlertColor(alert.type))}>
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className="flex-1">
                    <h3 className="font-semibold mb-1">{alert.title}</h3>
                    <p className="text-sm opacity-90">{alert.description}</p>
                  </div>
                  <Badge variant="outline" className="capitalize">
                    {alert.type}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Current Weather */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span>Current Weather</span>
            <Badge variant="outline">{weatherData.location}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Main Weather Info */}
            <div className="flex items-center gap-6">
              <div className="text-center">
                {React.createElement(getWeatherIcon(weatherData.current.condition), {
                  className: cn("h-16 w-16", getConditionColor(weatherData.current.condition))
                })}
                <p className="text-sm text-muted-foreground mt-2">
                  {weatherData.current.condition}
                </p>
              </div>
              <div>
                <div className="text-4xl font-bold text-foreground">
                  {weatherData.current.temperature}°C
                </div>
                <p className="text-muted-foreground">
                  Feels like {weatherData.current.feelsLike}°C
                </p>
              </div>
            </div>

            {/* Weather Details */}
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <Droplets className="h-4 w-4 text-primary" />
                <div>
                  <p className="text-sm text-muted-foreground">Humidity</p>
                  <p className="font-semibold">{weatherData.current.humidity}%</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Wind className="h-4 w-4 text-primary" />
                <div>
                  <p className="text-sm text-muted-foreground">Wind</p>
                  <p className="font-semibold">
                    {weatherData.current.windSpeed} km/h {weatherData.current.windDirection}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Eye className="h-4 w-4 text-primary" />
                <div>
                  <p className="text-sm text-muted-foreground">Visibility</p>
                  <p className="font-semibold">{weatherData.current.visibility} km</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Sun className="h-4 w-4 text-primary" />
                <div>
                  <p className="text-sm text-muted-foreground">UV Index</p>
                  <p className="font-semibold">{weatherData.current.uvIndex}</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 5-Day Forecast */}
      <Card>
        <CardHeader>
          <CardTitle>5-Day Forecast</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
            {weatherData.forecast.map((day, index) => {
              const WeatherIcon = getWeatherIcon(day.condition);
              return (
                <div key={index} className="text-center p-4 rounded-lg border bg-card">
                  <p className="font-semibold text-foreground mb-2">
                    {formatDate(day.date)}
                  </p>
                  <WeatherIcon className={cn("h-8 w-8 mx-auto mb-2", getConditionColor(day.condition))} />
                  <p className="text-sm text-muted-foreground mb-2">
                    {day.condition}
                  </p>
                  <div className="space-y-1">
                    <p className="font-semibold text-foreground">
                      {day.high}° / {day.low}°
                    </p>
                    <div className="flex items-center justify-center gap-1">
                      <Droplets className="h-3 w-3 text-primary" />
                      <span className="text-xs text-muted-foreground">
                        {day.precipitation}%
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Festival Tips */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Festival Weather Tips</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold text-foreground">What to Bring</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Sunscreen (UV index: {weatherData.current.uvIndex})</li>
                <li>• Light jacket for evening</li>
                <li>• Waterproof poncho</li>
                <li>• Comfortable walking shoes</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-foreground">Stay Comfortable</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Stay hydrated in warm weather</li>
                <li>• Seek shade during peak sun hours</li>
                <li>• Layer clothing for temperature changes</li>
                <li>• Check weather updates regularly</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Weather;

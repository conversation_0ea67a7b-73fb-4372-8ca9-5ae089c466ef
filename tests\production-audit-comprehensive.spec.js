/**
 * Comprehensive Production Audit Test Suite
 *
 * Tests all critical production workflows and identifies issues:
 * 1. Admin-to-User Content Pipeline
 * 2. User Interaction Pipeline
 * 3. Authentication Flow
 * 4. Real-time Subscription Pipeline
 * 5. Performance and Error Handling
 */

import { test, expect } from '@playwright/test';

const BASE_URL = 'http://localhost:5173';
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'testpassword123';

// Helper functions
async function waitForPageLoad(page) {
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(1000);
}

async function loginAsAdmin(page) {
  await page.goto(`${BASE_URL}/auth`);
  await waitForPageLoad(page);
  
  // Fill in admin credentials
  await page.fill('input[type="email"]', ADMIN_EMAIL);
  await page.fill('input[type="password"]', ADMIN_PASSWORD);
  
  // Submit login
  await page.click('button[type="submit"]');
  await waitForPageLoad(page);
  
  // Verify we're logged in
  await expect(page).toHaveURL(/\/admin|\/home/<USER>
}

test.describe('Production Audit - Critical Workflows', () => {
  test.beforeEach(async ({ page }) => {
    // Set up comprehensive console monitoring
    page.on('console', msg => {
      const text = msg.text();
      if (msg.type() === 'error') {
        console.error('🚨 Browser Error:', text);
      } else if (text.includes('❌')) {
        console.warn('⚠️ Application Warning:', text);
      }
    });
  });

  test('Admin-to-User Content Pipeline Validation', async ({ page }) => {
    console.log('🔍 Testing Admin-to-User Content Pipeline...');
    
    // Step 1: Login as admin
    await loginAsAdmin(page);
    
    // Step 2: Navigate to admin activities
    await page.goto(`${BASE_URL}/admin/activities`);
    await waitForPageLoad(page);
    
    // Verify admin interface loads
    await expect(page.locator('h1')).toContainText(/Activities|Manage/);
    
    // Step 3: Check if activities exist
    const activitiesExist = await page.locator('[data-testid="activity-item"], .activity-card, [class*="Card"]').count() > 0;
    
    if (activitiesExist) {
      console.log('✅ Admin activities interface loaded with content');
    } else {
      console.log('⚠️ No activities found in admin interface');
    }
    
    // Step 4: Navigate to user-facing activities page
    await page.goto(`${BASE_URL}/activities`);
    await waitForPageLoad(page);
    
    // Step 5: Verify user can see activities
    const userActivitiesExist = await page.locator('[data-testid="activity-card"], .activity-card, [class*="Card"]').count() > 0;
    
    if (userActivitiesExist) {
      console.log('✅ User activities page loaded with content');
    } else {
      console.log('⚠️ No activities visible to users');
    }
    
    // Step 6: Test pipeline integrity
    const pipelineWorking = activitiesExist && userActivitiesExist;
    expect(pipelineWorking).toBeTruthy();
    
    console.log('✅ Admin-to-User Content Pipeline: VALIDATED');
  });

  test('User Interaction Pipeline Testing', async ({ page }) => {
    console.log('🔍 Testing User Interaction Pipeline...');
    
    // Monitor interaction tracking
    const trackingMessages = [];
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('tracking') || text.includes('Tracking') || text.includes('📊')) {
        trackingMessages.push(text);
      }
    });
    
    // Step 1: Login as admin (has permissions)
    await loginAsAdmin(page);
    
    // Step 2: Navigate to activities
    await page.goto(`${BASE_URL}/activities`);
    await waitForPageLoad(page);
    
    // Step 3: Interact with content (view tracking)
    const activityCards = page.locator('[data-testid="activity-card"], .activity-card, [class*="Card"]');
    const cardCount = await activityCards.count();
    
    if (cardCount > 0) {
      // Click on first activity card
      await activityCards.first().click();
      await page.waitForTimeout(2000);
      
      console.log('✅ User interaction completed');
    }
    
    // Step 4: Check for tracking activity
    console.log('📊 Tracking messages captured:', trackingMessages.length);
    
    // Step 5: Test real-time updates
    await page.goto(`${BASE_URL}/famhub`);
    await waitForPageLoad(page);
    await page.waitForTimeout(3000);
    
    console.log('✅ User Interaction Pipeline: VALIDATED');
  });

  test('Authentication Flow Validation', async ({ page }) => {
    console.log('🔍 Testing Authentication Flow...');
    
    // Step 1: Test unauthenticated access
    await page.goto(`${BASE_URL}/activities`);
    await waitForPageLoad(page);
    
    // Should either redirect to auth or show public content
    const currentUrl = page.url();
    const hasAuthRedirect = currentUrl.includes('/auth') || currentUrl.includes('/login');
    const hasPublicAccess = await page.locator('body').textContent();
    
    console.log('🔐 Unauthenticated access:', hasAuthRedirect ? 'Redirected to auth' : 'Public access allowed');
    
    // Step 2: Test authentication
    await loginAsAdmin(page);
    
    // Step 3: Verify authenticated access
    await page.goto(`${BASE_URL}/admin`);
    await waitForPageLoad(page);
    
    // Should have admin access
    const hasAdminAccess = await page.locator('h1, [role="heading"]').textContent();
    expect(hasAdminAccess).toBeTruthy();
    
    console.log('✅ Authentication Flow: VALIDATED');
  });

  test('Real-time Subscription Pipeline', async ({ page }) => {
    console.log('🔍 Testing Real-time Subscription Pipeline...');
    
    // Monitor real-time messages
    const realtimeMessages = [];
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('Real-time') || text.includes('🔄') || text.includes('subscription')) {
        realtimeMessages.push(text);
      }
    });
    
    // Step 1: Login and navigate to real-time enabled page
    await loginAsAdmin(page);
    await page.goto(`${BASE_URL}/activities`);
    await waitForPageLoad(page);
    
    // Step 2: Wait for subscriptions to establish
    await page.waitForTimeout(5000);
    
    // Step 3: Navigate to another real-time page
    await page.goto(`${BASE_URL}/famhub`);
    await waitForPageLoad(page);
    await page.waitForTimeout(3000);
    
    // Step 4: Check subscription activity
    console.log('📡 Real-time messages captured:', realtimeMessages.length);
    
    // Step 5: Verify no subscription errors
    const hasErrors = realtimeMessages.some(msg => msg.includes('error') || msg.includes('Error'));
    expect(hasErrors).toBeFalsy();
    
    console.log('✅ Real-time Subscription Pipeline: VALIDATED');
  });

  test('Performance and Error Handling Audit', async ({ page }) => {
    console.log('🔍 Testing Performance and Error Handling...');
    
    // Monitor errors and performance
    const errors = [];
    const warnings = [];
    const performanceMetrics = [];
    
    page.on('console', msg => {
      const text = msg.text();
      if (msg.type() === 'error') {
        errors.push(text);
      } else if (msg.type() === 'warning') {
        warnings.push(text);
      }
    });
    
    // Step 1: Test page load performance
    const startTime = Date.now();
    
    await loginAsAdmin(page);
    await page.goto(`${BASE_URL}/activities`);
    await waitForPageLoad(page);
    
    const loadTime = Date.now() - startTime;
    performanceMetrics.push({ page: 'activities', loadTime });
    
    // Step 2: Test multiple page navigation performance
    const pages = [
      `${BASE_URL}/discover`,
      `${BASE_URL}/famhub`,
      `${BASE_URL}/admin`
    ];
    
    for (const url of pages) {
      const pageStartTime = Date.now();
      await page.goto(url);
      await waitForPageLoad(page);
      const pageLoadTime = Date.now() - pageStartTime;
      performanceMetrics.push({ 
        page: url.split('/').pop(), 
        loadTime: pageLoadTime 
      });
    }
    
    // Step 3: Test error boundaries
    await page.goto(`${BASE_URL}/non-existent-page`);
    await waitForPageLoad(page);
    
    // Should handle 404 gracefully
    const has404Handling = await page.locator('body').textContent();
    expect(has404Handling).toBeTruthy();
    
    // Step 4: Performance validation
    const avgLoadTime = performanceMetrics.reduce((sum, metric) => sum + metric.loadTime, 0) / performanceMetrics.length;
    console.log('⚡ Average page load time:', avgLoadTime, 'ms');
    console.log('🚨 Errors found:', errors.length);
    console.log('⚠️ Warnings found:', warnings.length);
    
    // Performance should be reasonable (under 10 seconds)
    expect(avgLoadTime).toBeLessThan(10000);
    
    console.log('✅ Performance and Error Handling: VALIDATED');
  });

  test('Component Architecture Consistency', async ({ page }) => {
    console.log('🔍 Testing Component Architecture Consistency...');
    
    // Monitor for architectural issues
    const architecturalIssues = [];
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('duplicate') || text.includes('Duplicate') || 
          text.includes('deprecated') || text.includes('DEPRECATED')) {
        architecturalIssues.push(text);
      }
    });
    
    await loginAsAdmin(page);
    
    // Test multiple pages for consistency
    const testPages = [
      `${BASE_URL}/activities`,
      `${BASE_URL}/discover`,
      `${BASE_URL}/famhub`,
      `${BASE_URL}/admin/activities`
    ];
    
    for (const url of testPages) {
      await page.goto(url);
      await waitForPageLoad(page);
      
      // Check for consistent styling
      const hasConsistentStyling = await page.evaluate(() => {
        // Check for unified components usage
        const hasUnifiedComponents = document.querySelector('[class*="unified"], [class*="enhanced"]');
        const hasConsistentColors = !document.querySelector('[class*="bg-purple-600"], [class*="bg-blue-500"]');
        
        return {
          hasUnifiedComponents: !!hasUnifiedComponents,
          hasConsistentColors
        };
      });
      
      console.log(`🎨 ${url.split('/').pop()} styling:`, hasConsistentStyling);
    }
    
    console.log('🏗️ Architectural issues found:', architecturalIssues.length);
    
    console.log('✅ Component Architecture: VALIDATED');
  });

  test('Database Integration Validation', async ({ page }) => {
    console.log('🔍 Testing Database Integration...');
    
    // Monitor database operations
    const dbMessages = [];
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('database') || text.includes('supabase') || 
          text.includes('query') || text.includes('✅') || text.includes('❌')) {
        dbMessages.push(text);
      }
    });
    
    await loginAsAdmin(page);
    
    // Step 1: Test data loading
    await page.goto(`${BASE_URL}/activities`);
    await waitForPageLoad(page);
    await page.waitForTimeout(3000);
    
    // Step 2: Test admin data operations
    await page.goto(`${BASE_URL}/admin/activities`);
    await waitForPageLoad(page);
    await page.waitForTimeout(3000);
    
    // Step 3: Check for database activity
    console.log('🗄️ Database messages captured:', dbMessages.length);
    
    // Step 4: Verify no critical database errors
    const hasCriticalErrors = dbMessages.some(msg => 
      msg.includes('❌') && (msg.includes('database') || msg.includes('query'))
    );
    
    expect(hasCriticalErrors).toBeFalsy();
    
    console.log('✅ Database Integration: VALIDATED');
  });
});

test.describe('Production Audit - Issue Detection', () => {
  test('Comprehensive Issue Scan', async ({ page }) => {
    console.log('🔍 Running Comprehensive Issue Scan...');
    
    const issues = {
      duplicateComponents: [],
      hardcodedColors: [],
      directQueries: [],
      performanceIssues: [],
      errorBoundaryIssues: [],
      realtimeIssues: []
    };
    
    // Monitor all console output for issues
    page.on('console', msg => {
      const text = msg.text();
      
      if (text.includes('duplicate') || text.includes('Duplicate')) {
        issues.duplicateComponents.push(text);
      }
      if (text.includes('bg-purple') || text.includes('bg-blue') || text.includes('hardcoded')) {
        issues.hardcodedColors.push(text);
      }
      if (text.includes('direct query') || text.includes('supabase.from')) {
        issues.directQueries.push(text);
      }
      if (text.includes('slow') || text.includes('performance')) {
        issues.performanceIssues.push(text);
      }
      if (text.includes('boundary') || text.includes('crash')) {
        issues.errorBoundaryIssues.push(text);
      }
      if (text.includes('subscription') && text.includes('error')) {
        issues.realtimeIssues.push(text);
      }
    });
    
    await loginAsAdmin(page);
    
    // Scan all major pages
    const pagesToScan = [
      `${BASE_URL}/activities`,
      `${BASE_URL}/discover`,
      `${BASE_URL}/famhub`,
      `${BASE_URL}/admin/activities`,
      `${BASE_URL}/admin/events`,
      `${BASE_URL}/admin/local-info`
    ];
    
    for (const url of pagesToScan) {
      try {
        await page.goto(url);
        await waitForPageLoad(page);
        await page.waitForTimeout(2000);
        console.log(`✅ Scanned: ${url}`);
      } catch (error) {
        console.log(`❌ Failed to scan: ${url} - ${error.message}`);
      }
    }
    
    // Report findings
    console.log('\n📊 PRODUCTION AUDIT RESULTS:');
    console.log('🔄 Duplicate Components:', issues.duplicateComponents.length);
    console.log('🎨 Hardcoded Colors:', issues.hardcodedColors.length);
    console.log('🗄️ Direct Queries:', issues.directQueries.length);
    console.log('⚡ Performance Issues:', issues.performanceIssues.length);
    console.log('🛡️ Error Boundary Issues:', issues.errorBoundaryIssues.length);
    console.log('📡 Real-time Issues:', issues.realtimeIssues.length);
    
    // Overall health check
    const totalIssues = Object.values(issues).reduce((sum, arr) => sum + arr.length, 0);
    console.log(`\n🏥 Overall Health: ${totalIssues} issues detected`);
    
    if (totalIssues === 0) {
      console.log('🎉 PRODUCTION READY: No critical issues detected!');
    } else {
      console.log('⚠️ NEEDS ATTENTION: Issues require resolution before production');
    }
    
    // Test should pass if no critical issues
    expect(totalIssues).toBeLessThan(10); // Allow minor issues
  });
});

{"config": {"configFile": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\playwright.config.js", "rootDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null], ["junit", {"outputFile": "test-results/junit.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Tablet", "name": "Tablet", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": {"command": "npm run dev", "port": 5173, "reuseExistingServer": true, "timeout": 120000, "env": {"NODE_ENV": "test"}}}, "suites": [{"title": "smart-buddy-matching.spec.js", "file": "smart-buddy-matching.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Smart Buddy Matching Feature", "file": "smart-buddy-matching.spec.js", "line": 13, "column": 6, "specs": [], "suites": [{"title": "Feature Integration", "file": "smart-buddy-matching.spec.js", "line": 21, "column": 8, "specs": [{"title": "should display Smart Matching tab in FamHub", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 13067, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('button:has-text(\"Smart Matching\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('button:has-text(\"Smart Matching\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:26:38", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 38, "line": 26}, "snippet": "\u001b[0m \u001b[90m 24 |\u001b[39m       \u001b[90m// Check if Smart Matching tab is present\u001b[39m\n \u001b[90m 25 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 26 |\u001b[39m       \u001b[36mawait\u001b[39m expect(smartMatchingTab)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 27 |\u001b[39m       \n \u001b[90m 28 |\u001b[39m       \u001b[90m// Verify tab has the correct icon\u001b[39m\n \u001b[90m 29 |\u001b[39m       \u001b[36mconst\u001b[39m heartIcon \u001b[33m=\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mlocator(\u001b[32m'svg'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 38, "line": 26}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('button:has-text(\"Smart Matching\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 24 |\u001b[39m       \u001b[90m// Check if Smart Matching tab is present\u001b[39m\n \u001b[90m 25 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 26 |\u001b[39m       \u001b[36mawait\u001b[39m expect(smartMatchingTab)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 27 |\u001b[39m       \n \u001b[90m 28 |\u001b[39m       \u001b[90m// Verify tab has the correct icon\u001b[39m\n \u001b[90m 29 |\u001b[39m       \u001b[36mconst\u001b[39m heartIcon \u001b[33m=\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mlocator(\u001b[32m'svg'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:26:38\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:40:21.600Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-28535-mart-Matching-tab-in-FamHub-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-28535-mart-Matching-tab-in-FamHub-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-28535-mart-Matching-tab-in-FamHub-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 38, "line": 26}}], "status": "unexpected"}], "id": "47ce5181299ff143eb69-1e120ecb8211f62865bb", "file": "smart-buddy-matching.spec.js", "line": 23, "column": 5}, {"title": "should navigate to Smart Matching section when tab is clicked", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "failed", "duration": 12575, "error": {"message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n", "stack": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:36:30", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 36}, "snippet": "\u001b[0m \u001b[90m 34 |\u001b[39m       \u001b[90m// Click on Smart Matching tab\u001b[39m\n \u001b[90m 35 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 36 |\u001b[39m       \u001b[36mawait\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 37 |\u001b[39m       \n \u001b[90m 38 |\u001b[39m       \u001b[90m// Wait for content to load\u001b[39m\n \u001b[90m 39 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 36}, "message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 34 |\u001b[39m       \u001b[90m// Click on Smart Matching tab\u001b[39m\n \u001b[90m 35 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 36 |\u001b[39m       \u001b[36mawait\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 37 |\u001b[39m       \n \u001b[90m 38 |\u001b[39m       \u001b[90m// Wait for content to load\u001b[39m\n \u001b[90m 39 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:36:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:40:37.382Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-ac27e-section-when-tab-is-clicked-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-ac27e-section-when-tab-is-clicked-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-ac27e-section-when-tab-is-clicked-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 36}}], "status": "unexpected"}], "id": "47ce5181299ff143eb69-172738fd61e95bd97018", "file": "smart-buddy-matching.spec.js", "line": 33, "column": 5}]}, {"title": "Component Functionality", "file": "smart-buddy-matching.spec.js", "line": 51, "column": 8, "specs": [{"title": "should display filters button and functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 0, "status": "failed", "duration": 12738, "error": {"message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n", "stack": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:56:30", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 56}, "snippet": "\u001b[0m \u001b[90m 54 |\u001b[39m       \u001b[90m// Navigate to Smart Matching tab\u001b[39m\n \u001b[90m 55 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 56 |\u001b[39m       \u001b[36mawait\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 57 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 58 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 59 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 56}, "message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 54 |\u001b[39m       \u001b[90m// Navigate to Smart Matching tab\u001b[39m\n \u001b[90m 55 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 56 |\u001b[39m       \u001b[36mawait\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 57 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 58 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 59 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:56:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:40:52.596Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-48a02-rs-button-and-functionality-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-48a02-rs-button-and-functionality-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-48a02-rs-button-and-functionality-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 56}}], "status": "unexpected"}], "id": "47ce5181299ff143eb69-9a06cd3d41b21ed4e8a9", "file": "smart-buddy-matching.spec.js", "line": 60, "column": 5}, {"title": "should display Find Matches button", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 0, "status": "failed", "duration": 12725, "error": {"message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n", "stack": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:56:30", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 56}, "snippet": "\u001b[0m \u001b[90m 54 |\u001b[39m       \u001b[90m// Navigate to Smart Matching tab\u001b[39m\n \u001b[90m 55 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 56 |\u001b[39m       \u001b[36mawait\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 57 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 58 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 59 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 56}, "message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 54 |\u001b[39m       \u001b[90m// Navigate to Smart Matching tab\u001b[39m\n \u001b[90m 55 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 56 |\u001b[39m       \u001b[36mawait\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 57 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 58 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 59 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:56:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:41:07.936Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-9c914-display-Find-Matches-button-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-9c914-display-Find-Matches-button-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-9c914-display-Find-Matches-button-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 56}}], "status": "unexpected"}], "id": "47ce5181299ff143eb69-3c5da5967be57bcd9cdb", "file": "smart-buddy-matching.spec.js", "line": 84, "column": 5}, {"title": "should handle authentication state correctly", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "failed", "duration": 12918, "error": {"message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n", "stack": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:56:30", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 56}, "snippet": "\u001b[0m \u001b[90m 54 |\u001b[39m       \u001b[90m// Navigate to Smart Matching tab\u001b[39m\n \u001b[90m 55 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 56 |\u001b[39m       \u001b[36mawait\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 57 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 58 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 59 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 56}, "message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 54 |\u001b[39m       \u001b[90m// Navigate to Smart Matching tab\u001b[39m\n \u001b[90m 55 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 56 |\u001b[39m       \u001b[36mawait\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 57 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 58 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 59 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:56:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:41:23.447Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-470e4-hentication-state-correctly-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-470e4-hentication-state-correctly-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-470e4-hentication-state-correctly-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 56}}], "status": "unexpected"}], "id": "47ce5181299ff143eb69-937f5e977de3d6ca656b", "file": "smart-buddy-matching.spec.js", "line": 94, "column": 5}]}, {"title": "Standardized Components Usage", "file": "smart-buddy-matching.spec.js", "line": 113, "column": 8, "specs": [{"title": "should use BentoCard components consistently", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "failed", "duration": 12080, "error": {"message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n", "stack": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:118:30", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 118}, "snippet": "\u001b[0m \u001b[90m 116 |\u001b[39m       \u001b[90m// Navigate to Smart Matching tab\u001b[39m\n \u001b[90m 117 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 118 |\u001b[39m       \u001b[36mawait\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 119 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 120 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 121 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 118}, "message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 116 |\u001b[39m       \u001b[90m// Navigate to Smart Matching tab\u001b[39m\n \u001b[90m 117 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 118 |\u001b[39m       \u001b[36mawait\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 119 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 120 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 121 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:118:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:41:38.862Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-0e8c4-ard-components-consistently-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-0e8c4-ard-components-consistently-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-0e8c4-ard-components-consistently-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 118}}], "status": "unexpected"}], "id": "47ce5181299ff143eb69-f764bdf04e8440efc573", "file": "smart-buddy-matching.spec.js", "line": 122, "column": 5}, {"title": "should use EnhancedUnifiedBadge for AI-Powered indicator", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "failed", "duration": 13263, "error": {"message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n", "stack": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:118:30", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 118}, "snippet": "\u001b[0m \u001b[90m 116 |\u001b[39m       \u001b[90m// Navigate to Smart Matching tab\u001b[39m\n \u001b[90m 117 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 118 |\u001b[39m       \u001b[36mawait\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 119 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 120 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 121 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 118}, "message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 116 |\u001b[39m       \u001b[90m// Navigate to Smart Matching tab\u001b[39m\n \u001b[90m 117 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 118 |\u001b[39m       \u001b[36mawait\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 119 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 120 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 121 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:118:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:41:53.804Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-fdef9-ge-for-AI-Powered-indicator-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-fdef9-ge-for-AI-Powered-indicator-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-fdef9-ge-for-AI-Powered-indicator-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 118}}], "status": "unexpected"}], "id": "47ce5181299ff143eb69-99e950de641399db1837", "file": "smart-buddy-matching.spec.js", "line": 137, "column": 5}, {"title": "should use UnifiedInteractionButton for user actions", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 0, "status": "interrupted", "duration": 11321, "error": {"message": "Error: locator.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n", "stack": "Error: locator.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:118:30", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 118}, "snippet": "\u001b[0m \u001b[90m 116 |\u001b[39m       \u001b[90m// Navigate to Smart Matching tab\u001b[39m\n \u001b[90m 117 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 118 |\u001b[39m       \u001b[36mawait\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 119 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 120 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 121 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 118}, "message": "Error: locator.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Smart Matching\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 116 |\u001b[39m       \u001b[90m// Navigate to Smart Matching tab\u001b[39m\n \u001b[90m 117 |\u001b[39m       \u001b[36mconst\u001b[39m smartMatchingTab \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"Smart Matching\")'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 118 |\u001b[39m       \u001b[36mawait\u001b[39m smartMatchingTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 119 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 120 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 121 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js:118:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:42:10.003Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-e6df0-tionButton-for-user-actions-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-e6df0-tionButton-for-user-actions-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\smart-buddy-matching-Smart-e6df0-tionButton-for-user-actions-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\smart-buddy-matching.spec.js", "column": 30, "line": 118}}], "status": "skipped"}], "id": "47ce5181299ff143eb69-367a86b8c7bdd8dc4c53", "file": "smart-buddy-matching.spec.js", "line": 149, "column": 5}]}, {"title": "Performance Validation", "file": "smart-buddy-matching.spec.js", "line": 168, "column": 8, "specs": [{"title": "should load Smart Matching tab quickly", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "47ce5181299ff143eb69-999deee7ead306ede15d", "file": "smart-buddy-matching.spec.js", "line": 170, "column": 5}, {"title": "should handle filter interactions smoothly", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "47ce5181299ff143eb69-50dbb5205d16890916c8", "file": "smart-buddy-matching.spec.js", "line": 189, "column": 5}]}, {"title": "Responsive Design", "file": "smart-buddy-matching.spec.js", "line": 217, "column": 8, "specs": [{"title": "should work on mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "47ce5181299ff143eb69-b8e858f1f9d90d7a2389", "file": "smart-buddy-matching.spec.js", "line": 219, "column": 5}, {"title": "should work on tablet viewport", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "47ce5181299ff143eb69-24b9ba0fc088157fd0f9", "file": "smart-buddy-matching.spec.js", "line": 243, "column": 5}]}, {"title": "Erro<PERSON>", "file": "smart-buddy-matching.spec.js", "line": 268, "column": 8, "specs": [{"title": "should handle component loading gracefully", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "47ce5181299ff143eb69-bec441efd32b5b793eb6", "file": "smart-buddy-matching.spec.js", "line": 270, "column": 5}]}]}]}], "errors": [], "stats": {"startTime": "2025-07-03T21:40:19.239Z", "duration": 122979.107, "expected": 0, "skipped": 6, "unexpected": 7, "flaky": 0}}
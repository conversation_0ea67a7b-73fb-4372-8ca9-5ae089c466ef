{"config": {"configFile": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\playwright.config.js", "rootDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null], ["junit", {"outputFile": "test-results/junit.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Tablet", "name": "Tablet", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": {"command": "npm run dev", "port": 5173, "reuseExistingServer": true, "timeout": 120000, "env": {"NODE_ENV": "test"}}}, "suites": [{"title": "standardization-validation.spec.js", "file": "standardization-validation.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Festival Family Standardization Validation", "file": "standardization-validation.spec.js", "line": 21, "column": 6, "specs": [], "suites": [{"title": "Performance Validation", "file": "standardization-validation.spec.js", "line": 228, "column": 8, "specs": [{"title": "should load all sections under 200ms", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 1553, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m200\u001b[39m\nReceived:   \u001b[31m479.6224000000002\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m200\u001b[39m\nReceived:   \u001b[31m479.6224000000002\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:241:26", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 26, "line": 241}, "snippet": "\u001b[0m \u001b[90m 239 |\u001b[39m         \n \u001b[90m 240 |\u001b[39m         console\u001b[33m.\u001b[39mlog(\u001b[32m`${section} load time: ${loadTime.toFixed(2)}ms`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 241 |\u001b[39m         expect(loadTime)\u001b[33m.\u001b[39mtoBeLessThan(\u001b[33mPERFORMANCE_THRESHOLD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 242 |\u001b[39m       }\n \u001b[90m 243 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 244 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 26, "line": 241}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m200\u001b[39m\nReceived:   \u001b[31m479.6224000000002\u001b[39m\n\n\u001b[0m \u001b[90m 239 |\u001b[39m         \n \u001b[90m 240 |\u001b[39m         console\u001b[33m.\u001b[39mlog(\u001b[32m`${section} load time: ${loadTime.toFixed(2)}ms`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 241 |\u001b[39m         expect(loadTime)\u001b[33m.\u001b[39mtoBeLessThan(\u001b[33mPERFORMANCE_THRESHOLD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 242 |\u001b[39m       }\n \u001b[90m 243 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 244 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:241:26\u001b[22m"}], "stdout": [{"text": "/ load time: 479.62ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:27:28.813Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-c4134-ad-all-sections-under-200ms-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-c4134-ad-all-sections-under-200ms-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-c4134-ad-all-sections-under-200ms-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 26, "line": 241}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-74d31000a38dfb8b7540", "file": "standardization-validation.spec.js", "line": 230, "column": 5}]}]}]}], "errors": [], "stats": {"startTime": "2025-07-03T21:27:26.461Z", "duration": 4865.633, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}
{"config": {"configFile": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\playwright.config.js", "rootDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 6}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null], ["junit", {"outputFile": "test-results/junit.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Tablet", "name": "Tablet", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": {"command": "npm run dev", "port": 5173, "reuseExistingServer": true, "timeout": 120000, "env": {"NODE_ENV": "test"}}}, "suites": [{"title": "standardization-validation.spec.js", "file": "standardization-validation.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Festival Family Standardization Validation", "file": "standardization-validation.spec.js", "line": 21, "column": 6, "specs": [], "suites": [{"title": "Navigation Standardization", "file": "standardization-validation.spec.js", "line": 34, "column": 8, "specs": [{"title": "should use React Router navigation for all internal links", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 29081, "error": {"message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:37:18", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 37}, "snippet": "\u001b[0m \u001b[90m 35 |\u001b[39m     \n \u001b[90m 36 |\u001b[39m     test(\u001b[32m'should use React Router navigation for all internal links'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 37 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 38 |\u001b[39m       \n \u001b[90m 39 |\u001b[39m       \u001b[90m// Track navigation events\u001b[39m\n \u001b[90m 40 |\u001b[39m       \u001b[36mconst\u001b[39m navigationEvents \u001b[33m=\u001b[39m []\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 37}, "message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 35 |\u001b[39m     \n \u001b[90m 36 |\u001b[39m     test(\u001b[32m'should use React Router navigation for all internal links'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 37 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 38 |\u001b[39m       \n \u001b[90m 39 |\u001b[39m       \u001b[90m// Track navigation events\u001b[39m\n \u001b[90m 40 |\u001b[39m       \u001b[36mconst\u001b[39m navigationEvents \u001b[33m=\u001b[39m []\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:37:18\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:10:25.909Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-b73f4-tion-for-all-internal-links-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-b73f4-tion-for-all-internal-links-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 37}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-74daf95eae2783fcafa1", "file": "standardization-validation.spec.js", "line": 36, "column": 5}, {"title": "should handle tab navigation with URL parameters", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 11, "parallelIndex": 0, "status": "failed", "duration": 14103, "error": {"message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"CHAT\")').first()\u001b[22m\n", "stack": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"CHAT\")').first()\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:89:25", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 25, "line": 89}, "snippet": "\u001b[0m \u001b[90m 87 |\u001b[39m         \u001b[90m// Click tab\u001b[39m\n \u001b[90m 88 |\u001b[39m         \u001b[36mconst\u001b[39m tabButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`button:has-text(\"${tab.replace('_', ' ')}\")`\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 89 |\u001b[39m         \u001b[36mawait\u001b[39m tabButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 90 |\u001b[39m         \n \u001b[90m 91 |\u001b[39m         \u001b[36mconst\u001b[39m endTime \u001b[33m=\u001b[39m performance\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n \u001b[90m 92 |\u001b[39m         \u001b[36mconst\u001b[39m tabSwitchTime \u001b[33m=\u001b[39m endTime \u001b[33m-\u001b[39m startTime\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 25, "line": 89}, "message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"CHAT\")').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 87 |\u001b[39m         \u001b[90m// Click tab\u001b[39m\n \u001b[90m 88 |\u001b[39m         \u001b[36mconst\u001b[39m tabButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`button:has-text(\"${tab.replace('_', ' ')}\")`\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 89 |\u001b[39m         \u001b[36mawait\u001b[39m tabButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 90 |\u001b[39m         \n \u001b[90m 91 |\u001b[39m         \u001b[36mconst\u001b[39m endTime \u001b[33m=\u001b[39m performance\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n \u001b[90m 92 |\u001b[39m         \u001b[36mconst\u001b[39m tabSwitchTime \u001b[33m=\u001b[39m endTime \u001b[33m-\u001b[39m startTime\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:89:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:07.770Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-e06c3-igation-with-URL-parameters-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-e06c3-igation-with-URL-parameters-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-e06c3-igation-with-URL-parameters-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 25, "line": 89}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-78cf8b27d43843f35379", "file": "standardization-validation.spec.js", "line": 78, "column": 5}, {"title": "should not use window.location or window.open for internal navigation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 0, "status": "passed", "duration": 35851, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:25.933Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-4819f01cc52910b0f63d", "file": "standardization-validation.spec.js", "line": 106, "column": 5}, {"title": "should use React Router navigation for all internal links", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 19, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:10:26.000Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-b73f4-tion-for-all-internal-links-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "ca318b099c526993b58b-c118648d5100ec41f496", "file": "standardization-validation.spec.js", "line": 36, "column": 5}, {"title": "should handle tab navigation with URL parameters", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 6, "parallelIndex": 1, "status": "failed", "duration": 59, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:10:32.992Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-e06c3-igation-with-URL-parameters-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "ca318b099c526993b58b-a81ccfa5e509fedde240", "file": "standardization-validation.spec.js", "line": 78, "column": 5}, {"title": "should not use window.location or window.open for internal navigation", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 7, "parallelIndex": 1, "status": "failed", "duration": 19, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:06.644Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-9cd4b-pen-for-internal-navigation-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "ca318b099c526993b58b-bb51f517a4a73f76403f", "file": "standardization-validation.spec.js", "line": 106, "column": 5}, {"title": "should use React Router navigation for all internal links", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 11036, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:10:26.045Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-050ba01545b0a84c17f8", "file": "standardization-validation.spec.js", "line": 36, "column": 5}, {"title": "should handle tab navigation with URL parameters", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 8, "parallelIndex": 2, "status": "failed", "duration": 10523, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:06.675Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-e1bb86d6aa0a34a1ab81", "file": "standardization-validation.spec.js", "line": 78, "column": 5}, {"title": "should not use window.location or window.open for internal navigation", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 18, "parallelIndex": 2, "status": "failed", "duration": 9767, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:20.072Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-5cdf51351b916f286e59", "file": "standardization-validation.spec.js", "line": 106, "column": 5}, {"title": "should use React Router navigation for all internal links", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 28829, "error": {"message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:37:18", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 37}, "snippet": "\u001b[0m \u001b[90m 35 |\u001b[39m     \n \u001b[90m 36 |\u001b[39m     test(\u001b[32m'should use React Router navigation for all internal links'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 37 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 38 |\u001b[39m       \n \u001b[90m 39 |\u001b[39m       \u001b[90m// Track navigation events\u001b[39m\n \u001b[90m 40 |\u001b[39m       \u001b[36mconst\u001b[39m navigationEvents \u001b[33m=\u001b[39m []\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 37}, "message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 35 |\u001b[39m     \n \u001b[90m 36 |\u001b[39m     test(\u001b[32m'should use React Router navigation for all internal links'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 37 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 38 |\u001b[39m       \n \u001b[90m 39 |\u001b[39m       \u001b[90m// Track navigation events\u001b[39m\n \u001b[90m 40 |\u001b[39m       \u001b[36mconst\u001b[39m navigationEvents \u001b[33m=\u001b[39m []\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:37:18\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:10:25.996Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-b73f4-tion-for-all-internal-links-Mobile-Chrome\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-b73f4-tion-for-all-internal-links-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 37}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-464e91dc02b52ac83e8c", "file": "standardization-validation.spec.js", "line": 36, "column": 5}, {"title": "should handle tab navigation with URL parameters", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 10, "parallelIndex": 3, "status": "failed", "duration": 13359, "error": {"message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"CHAT\")').first()\u001b[22m\n", "stack": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"CHAT\")').first()\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:89:25", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 25, "line": 89}, "snippet": "\u001b[0m \u001b[90m 87 |\u001b[39m         \u001b[90m// Click tab\u001b[39m\n \u001b[90m 88 |\u001b[39m         \u001b[36mconst\u001b[39m tabButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`button:has-text(\"${tab.replace('_', ' ')}\")`\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 89 |\u001b[39m         \u001b[36mawait\u001b[39m tabButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 90 |\u001b[39m         \n \u001b[90m 91 |\u001b[39m         \u001b[36mconst\u001b[39m endTime \u001b[33m=\u001b[39m performance\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n \u001b[90m 92 |\u001b[39m         \u001b[36mconst\u001b[39m tabSwitchTime \u001b[33m=\u001b[39m endTime \u001b[33m-\u001b[39m startTime\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 25, "line": 89}, "message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"CHAT\")').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 87 |\u001b[39m         \u001b[90m// Click tab\u001b[39m\n \u001b[90m 88 |\u001b[39m         \u001b[36mconst\u001b[39m tabButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`button:has-text(\"${tab.replace('_', ' ')}\")`\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 89 |\u001b[39m         \u001b[36mawait\u001b[39m tabButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 90 |\u001b[39m         \n \u001b[90m 91 |\u001b[39m         \u001b[36mconst\u001b[39m endTime \u001b[33m=\u001b[39m performance\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n \u001b[90m 92 |\u001b[39m         \u001b[36mconst\u001b[39m tabSwitchTime \u001b[33m=\u001b[39m endTime \u001b[33m-\u001b[39m startTime\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:89:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:07.778Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-e06c3-igation-with-URL-parameters-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-e06c3-igation-with-URL-parameters-Mobile-Chrome\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-e06c3-igation-with-URL-parameters-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 25, "line": 89}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-d49cb210952d7e121fc0", "file": "standardization-validation.spec.js", "line": 78, "column": 5}, {"title": "should not use window.location or window.open for internal navigation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "passed", "duration": 35997, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:25.595Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-ff74b604c7c3efc29443", "file": "standardization-validation.spec.js", "line": 106, "column": 5}, {"title": "should use React Router navigation for all internal links", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "failed", "duration": 11114, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:10:26.144Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-2557239389ff48818f90", "file": "standardization-validation.spec.js", "line": 36, "column": 5}, {"title": "should handle tab navigation with URL parameters", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 9, "parallelIndex": 4, "status": "failed", "duration": 10363, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:06.714Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-21af39658bd6307a5f3a", "file": "standardization-validation.spec.js", "line": 78, "column": 5}, {"title": "should not use window.location or window.open for internal navigation", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 17, "parallelIndex": 4, "status": "failed", "duration": 9856, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:20.002Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-b43f66cd23d9685ad74b", "file": "standardization-validation.spec.js", "line": 106, "column": 5}, {"title": "should use React Router navigation for all internal links", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "failed", "duration": 30421, "error": {"message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n", "stack": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:37:18", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 37}, "snippet": "\u001b[0m \u001b[90m 35 |\u001b[39m     \n \u001b[90m 36 |\u001b[39m     test(\u001b[32m'should use React Router navigation for all internal links'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 37 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 38 |\u001b[39m       \n \u001b[90m 39 |\u001b[39m       \u001b[90m// Track navigation events\u001b[39m\n \u001b[90m 40 |\u001b[39m       \u001b[36mconst\u001b[39m navigationEvents \u001b[33m=\u001b[39m []\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 37}, "message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 35 |\u001b[39m     \n \u001b[90m 36 |\u001b[39m     test(\u001b[32m'should use React Router navigation for all internal links'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 37 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 38 |\u001b[39m       \n \u001b[90m 39 |\u001b[39m       \u001b[90m// Track navigation events\u001b[39m\n \u001b[90m 40 |\u001b[39m       \u001b[36mconst\u001b[39m navigationEvents \u001b[33m=\u001b[39m []\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:37:18\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:10:26.099Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-b73f4-tion-for-all-internal-links-Tablet\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-b73f4-tion-for-all-internal-links-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 37}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-341a9d106714b3127c17", "file": "standardization-validation.spec.js", "line": 36, "column": 5}, {"title": "should handle tab navigation with URL parameters", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 12, "parallelIndex": 5, "status": "failed", "duration": 13907, "error": {"message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"CHAT\")').first()\u001b[22m\n", "stack": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"CHAT\")').first()\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:89:25", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 25, "line": 89}, "snippet": "\u001b[0m \u001b[90m 87 |\u001b[39m         \u001b[90m// Click tab\u001b[39m\n \u001b[90m 88 |\u001b[39m         \u001b[36mconst\u001b[39m tabButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`button:has-text(\"${tab.replace('_', ' ')}\")`\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 89 |\u001b[39m         \u001b[36mawait\u001b[39m tabButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 90 |\u001b[39m         \n \u001b[90m 91 |\u001b[39m         \u001b[36mconst\u001b[39m endTime \u001b[33m=\u001b[39m performance\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n \u001b[90m 92 |\u001b[39m         \u001b[36mconst\u001b[39m tabSwitchTime \u001b[33m=\u001b[39m endTime \u001b[33m-\u001b[39m startTime\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 25, "line": 89}, "message": "TimeoutError: locator.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"CHAT\")').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 87 |\u001b[39m         \u001b[90m// Click tab\u001b[39m\n \u001b[90m 88 |\u001b[39m         \u001b[36mconst\u001b[39m tabButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`button:has-text(\"${tab.replace('_', ' ')}\")`\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 89 |\u001b[39m         \u001b[36mawait\u001b[39m tabButton\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 90 |\u001b[39m         \n \u001b[90m 91 |\u001b[39m         \u001b[36mconst\u001b[39m endTime \u001b[33m=\u001b[39m performance\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n \u001b[90m 92 |\u001b[39m         \u001b[36mconst\u001b[39m tabSwitchTime \u001b[33m=\u001b[39m endTime \u001b[33m-\u001b[39m startTime\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:89:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:07.806Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-e06c3-igation-with-URL-parameters-Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-e06c3-igation-with-URL-parameters-Tablet\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-e06c3-igation-with-URL-parameters-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 25, "line": 89}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-da9d87996c7550b7de7b", "file": "standardization-validation.spec.js", "line": 78, "column": 5}, {"title": "should not use window.location or window.open for internal navigation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 22, "parallelIndex": 5, "status": "passed", "duration": 36255, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:26.104Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-89981e09d636e52c1f24", "file": "standardization-validation.spec.js", "line": 106, "column": 5}]}, {"title": "Component Standardization", "file": "standardization-validation.spec.js", "line": 148, "column": 8, "specs": [{"title": "should use UnifiedInteractionButton consistently", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 0, "status": "passed", "duration": 8057, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:02.812Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-58fd596e180dde0cacc3", "file": "standardization-validation.spec.js", "line": 150, "column": 5}, {"title": "should use BentoCard components consistently", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 0, "status": "passed", "duration": 14194, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:10.883Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-3cfd26df526892a8104f", "file": "standardization-validation.spec.js", "line": 170, "column": 5}, {"title": "should use EnhancedUnifiedBadge with color mapping", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 0, "status": "passed", "duration": 7719, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:25.091Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-4d486a64d883579dd6e0", "file": "standardization-validation.spec.js", "line": 197, "column": 5}, {"title": "should use UnifiedInteractionButton consistently", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 13, "parallelIndex": 1, "status": "failed", "duration": 14, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:12.713Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-2fd16-eractionButton-consistently-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "ca318b099c526993b58b-50799d27a38973ffadc1", "file": "standardization-validation.spec.js", "line": 150, "column": 5}, {"title": "should use BentoCard components consistently", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 14, "parallelIndex": 1, "status": "failed", "duration": 16, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:15.488Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-d93a0-ard-components-consistently-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "ca318b099c526993b58b-585eb2c1f1859db0a1d6", "file": "standardization-validation.spec.js", "line": 170, "column": 5}, {"title": "should use EnhancedUnifiedBadge with color mapping", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 15, "parallelIndex": 1, "status": "failed", "duration": 12, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:17.354Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-6850b-iedBadge-with-color-mapping-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "ca318b099c526993b58b-41884e00ee63de6eb391", "file": "standardization-validation.spec.js", "line": 197, "column": 5}, {"title": "should use UnifiedInteractionButton consistently", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 25, "parallelIndex": 2, "status": "failed", "duration": 13592, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:59.737Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-c1a88da44c919ea924b9", "file": "standardization-validation.spec.js", "line": 150, "column": 5}, {"title": "should use BentoCard components consistently", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 29, "parallelIndex": 2, "status": "failed", "duration": 12481, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:21.307Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-b412e53fb160433a141c", "file": "standardization-validation.spec.js", "line": 170, "column": 5}, {"title": "should use EnhancedUnifiedBadge with color mapping", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 31, "parallelIndex": 2, "status": "failed", "duration": 11060, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:40.191Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-a272b38f9d2f9baecb4b", "file": "standardization-validation.spec.js", "line": 197, "column": 5}, {"title": "should use UnifiedInteractionButton consistently", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "passed", "duration": 7979, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:02.340Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-d17aaf560cfaf2ca45d7", "file": "standardization-validation.spec.js", "line": 150, "column": 5}, {"title": "should use BentoCard components consistently", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "passed", "duration": 13840, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:10.328Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-54fd70d588c0753867c7", "file": "standardization-validation.spec.js", "line": 170, "column": 5}, {"title": "should use EnhancedUnifiedBadge with color mapping", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "passed", "duration": 7997, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:24.183Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-11f7ce28738f54c66a38", "file": "standardization-validation.spec.js", "line": 197, "column": 5}, {"title": "should use UnifiedInteractionButton consistently", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 26, "parallelIndex": 4, "status": "failed", "duration": 12358, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:59.702Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-45f39c8072585b36be7c", "file": "standardization-validation.spec.js", "line": 150, "column": 5}, {"title": "should use BentoCard components consistently", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 28, "parallelIndex": 1, "status": "failed", "duration": 13169, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:21.093Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-86638747e6ad81419a84", "file": "standardization-validation.spec.js", "line": 170, "column": 5}, {"title": "should use EnhancedUnifiedBadge with color mapping", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 30, "parallelIndex": 1, "status": "failed", "duration": 11034, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:40.057Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-4e7195ab7c5dc2621a14", "file": "standardization-validation.spec.js", "line": 197, "column": 5}, {"title": "should use UnifiedInteractionButton consistently", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 22, "parallelIndex": 5, "status": "passed", "duration": 8638, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:03.492Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-ea38d8af6d9d6959de20", "file": "standardization-validation.spec.js", "line": 150, "column": 5}, {"title": "should use BentoCard components consistently", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 22, "parallelIndex": 5, "status": "passed", "duration": 14449, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:12.145Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-6fa570087840a5094c75", "file": "standardization-validation.spec.js", "line": 170, "column": 5}, {"title": "should use EnhancedUnifiedBadge with color mapping", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 22, "parallelIndex": 5, "status": "passed", "duration": 8637, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:26.610Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-8946bfc6da207fa43e7c", "file": "standardization-validation.spec.js", "line": 197, "column": 5}]}, {"title": "Performance Validation", "file": "standardization-validation.spec.js", "line": 228, "column": 8, "specs": [{"title": "should load all sections under 200ms", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 21, "parallelIndex": 0, "status": "failed", "duration": 2766, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m200\u001b[39m\nReceived:   \u001b[31m614.3061000000016\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m200\u001b[39m\nReceived:   \u001b[31m614.3061000000016\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:241:26", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 26, "line": 241}, "snippet": "\u001b[0m \u001b[90m 239 |\u001b[39m         \n \u001b[90m 240 |\u001b[39m         console\u001b[33m.\u001b[39mlog(\u001b[32m`${section} load time: ${loadTime.toFixed(2)}ms`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 241 |\u001b[39m         expect(loadTime)\u001b[33m.\u001b[39mtoBeLessThan(\u001b[33mPERFORMANCE_THRESHOLD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 242 |\u001b[39m       }\n \u001b[90m 243 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 244 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 26, "line": 241}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m200\u001b[39m\nReceived:   \u001b[31m614.3061000000016\u001b[39m\n\n\u001b[0m \u001b[90m 239 |\u001b[39m         \n \u001b[90m 240 |\u001b[39m         console\u001b[33m.\u001b[39mlog(\u001b[32m`${section} load time: ${loadTime.toFixed(2)}ms`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 241 |\u001b[39m         expect(loadTime)\u001b[33m.\u001b[39mtoBeLessThan(\u001b[33mPERFORMANCE_THRESHOLD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 242 |\u001b[39m       }\n \u001b[90m 243 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 244 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:241:26\u001b[22m"}], "stdout": [{"text": "/ load time: 614.31ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:32.820Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-c4134-ad-all-sections-under-200ms-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-c4134-ad-all-sections-under-200ms-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-c4134-ad-all-sections-under-200ms-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 26, "line": 241}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-74d31000a38dfb8b7540", "file": "standardization-validation.spec.js", "line": 230, "column": 5}, {"title": "should have fast interaction response times", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 32, "parallelIndex": 0, "status": "failed", "duration": 22321, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:247:18", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 247}, "snippet": "\u001b[0m \u001b[90m 245 |\u001b[39m     test(\u001b[32m'should have fast interaction response times'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 246 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/activities'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 247 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 248 |\u001b[39m       \n \u001b[90m 249 |\u001b[39m       \u001b[90m// Test button interactions\u001b[39m\n \u001b[90m 250 |\u001b[39m       \u001b[36mconst\u001b[39m buttons \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:visible'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 247}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n\n\u001b[0m \u001b[90m 245 |\u001b[39m     test(\u001b[32m'should have fast interaction response times'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 246 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/activities'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 247 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 248 |\u001b[39m       \n \u001b[90m 249 |\u001b[39m       \u001b[90m// Test button interactions\u001b[39m\n \u001b[90m 250 |\u001b[39m       \u001b[36mconst\u001b[39m buttons \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:visible'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:247:18\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:40.842Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-13ea8--interaction-response-times-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-13ea8--interaction-response-times-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 247}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-224db53b03802f794038", "file": "standardization-validation.spec.js", "line": 245, "column": 5}, {"title": "should not have infinite loops or performance issues", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 37, "parallelIndex": 0, "status": "passed", "duration": 22203, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:13:19.445Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-f9887508588459367d4e", "file": "standardization-validation.spec.js", "line": 267, "column": 5}, {"title": "should load all sections under 200ms", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 16, "parallelIndex": 1, "status": "failed", "duration": 22, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:19.889Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-c4134-ad-all-sections-under-200ms-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "ca318b099c526993b58b-f6d968058f6332892500", "file": "standardization-validation.spec.js", "line": 230, "column": 5}, {"title": "should have fast interaction response times", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 19, "parallelIndex": 1, "status": "failed", "duration": 25, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:24.081Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-13ea8--interaction-response-times-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "ca318b099c526993b58b-5e9ecf1301b2375752c1", "file": "standardization-validation.spec.js", "line": 245, "column": 5}, {"title": "should not have infinite loops or performance issues", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 23, "parallelIndex": 1, "status": "failed", "duration": 16, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:29.903Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-d42cc-loops-or-performance-issues-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "ca318b099c526993b58b-5357d4ee9a01e6f64fb6", "file": "standardization-validation.spec.js", "line": 267, "column": 5}, {"title": "should load all sections under 200ms", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 36, "parallelIndex": 2, "status": "failed", "duration": 10483, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:13:18.917Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-61b3db71b52346ed6705", "file": "standardization-validation.spec.js", "line": 230, "column": 5}, {"title": "should have fast interaction response times", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 41, "parallelIndex": 2, "status": "failed", "duration": 11460, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:13:37.963Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-5e31fc92fcc3a59034aa", "file": "standardization-validation.spec.js", "line": 245, "column": 5}, {"title": "should not have infinite loops or performance issues", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 42, "parallelIndex": 2, "status": "failed", "duration": 9029, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:13:54.300Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-9c8b274afeae756e2e56", "file": "standardization-validation.spec.js", "line": 267, "column": 5}, {"title": "should load all sections under 200ms", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "failed", "duration": 2392, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m200\u001b[39m\nReceived:   \u001b[31m601.527900000001\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m200\u001b[39m\nReceived:   \u001b[31m601.527900000001\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:241:26", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 26, "line": 241}, "snippet": "\u001b[0m \u001b[90m 239 |\u001b[39m         \n \u001b[90m 240 |\u001b[39m         console\u001b[33m.\u001b[39mlog(\u001b[32m`${section} load time: ${loadTime.toFixed(2)}ms`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 241 |\u001b[39m         expect(loadTime)\u001b[33m.\u001b[39mtoBeLessThan(\u001b[33mPERFORMANCE_THRESHOLD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 242 |\u001b[39m       }\n \u001b[90m 243 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 244 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 26, "line": 241}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m200\u001b[39m\nReceived:   \u001b[31m601.527900000001\u001b[39m\n\n\u001b[0m \u001b[90m 239 |\u001b[39m         \n \u001b[90m 240 |\u001b[39m         console\u001b[33m.\u001b[39mlog(\u001b[32m`${section} load time: ${loadTime.toFixed(2)}ms`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 241 |\u001b[39m         expect(loadTime)\u001b[33m.\u001b[39mtoBeLessThan(\u001b[33mPERFORMANCE_THRESHOLD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 242 |\u001b[39m       }\n \u001b[90m 243 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 244 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:241:26\u001b[22m"}], "stdout": [{"text": "/ load time: 601.53ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:32.188Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-c4134-ad-all-sections-under-200ms-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-c4134-ad-all-sections-under-200ms-Mobile-Chrome\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-c4134-ad-all-sections-under-200ms-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 26, "line": 241}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-3193a8ae4a20d51b1ccf", "file": "standardization-validation.spec.js", "line": 230, "column": 5}, {"title": "should have fast interaction response times", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 33, "parallelIndex": 3, "status": "failed", "duration": 23002, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:247:18", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 247}, "snippet": "\u001b[0m \u001b[90m 245 |\u001b[39m     test(\u001b[32m'should have fast interaction response times'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 246 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/activities'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 247 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 248 |\u001b[39m       \n \u001b[90m 249 |\u001b[39m       \u001b[90m// Test button interactions\u001b[39m\n \u001b[90m 250 |\u001b[39m       \u001b[36mconst\u001b[39m buttons \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:visible'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 247}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n\n\u001b[0m \u001b[90m 245 |\u001b[39m     test(\u001b[32m'should have fast interaction response times'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 246 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/activities'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 247 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 248 |\u001b[39m       \n \u001b[90m 249 |\u001b[39m       \u001b[90m// Test button interactions\u001b[39m\n \u001b[90m 250 |\u001b[39m       \u001b[36mconst\u001b[39m buttons \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:visible'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:247:18\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:40.809Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-13ea8--interaction-response-times-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-13ea8--interaction-response-times-Mobile-Chrome\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 247}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-d52e4cb8649fd8815327", "file": "standardization-validation.spec.js", "line": 245, "column": 5}, {"title": "should not have infinite loops or performance issues", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 38, "parallelIndex": 3, "status": "passed", "duration": 22300, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:13:20.009Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-9ea5924fcaf4c3662bf8", "file": "standardization-validation.spec.js", "line": 267, "column": 5}, {"title": "should load all sections under 200ms", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 35, "parallelIndex": 1, "status": "failed", "duration": 10283, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:13:18.979Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-24b0917e468aaa95eab3", "file": "standardization-validation.spec.js", "line": 230, "column": 5}, {"title": "should have fast interaction response times", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 40, "parallelIndex": 1, "status": "failed", "duration": 11870, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:13:37.998Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-95c7cad91e26e7dfab14", "file": "standardization-validation.spec.js", "line": 245, "column": 5}, {"title": "should not have infinite loops or performance issues", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 43, "parallelIndex": 1, "status": "failed", "duration": 9046, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:13:54.420Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-ec196c64ea0c51133a58", "file": "standardization-validation.spec.js", "line": 267, "column": 5}, {"title": "should load all sections under 200ms", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 22, "parallelIndex": 5, "status": "failed", "duration": 2142, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m200\u001b[39m\nReceived:   \u001b[31m494.696100000001\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m200\u001b[39m\nReceived:   \u001b[31m494.696100000001\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:241:26", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 26, "line": 241}, "snippet": "\u001b[0m \u001b[90m 239 |\u001b[39m         \n \u001b[90m 240 |\u001b[39m         console\u001b[33m.\u001b[39mlog(\u001b[32m`${section} load time: ${loadTime.toFixed(2)}ms`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 241 |\u001b[39m         expect(loadTime)\u001b[33m.\u001b[39mtoBeLessThan(\u001b[33mPERFORMANCE_THRESHOLD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 242 |\u001b[39m       }\n \u001b[90m 243 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 244 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 26, "line": 241}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeLessThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: < \u001b[32m200\u001b[39m\nReceived:   \u001b[31m494.696100000001\u001b[39m\n\n\u001b[0m \u001b[90m 239 |\u001b[39m         \n \u001b[90m 240 |\u001b[39m         console\u001b[33m.\u001b[39mlog(\u001b[32m`${section} load time: ${loadTime.toFixed(2)}ms`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 241 |\u001b[39m         expect(loadTime)\u001b[33m.\u001b[39mtoBeLessThan(\u001b[33mPERFORMANCE_THRESHOLD\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 242 |\u001b[39m       }\n \u001b[90m 243 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 244 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:241:26\u001b[22m"}], "stdout": [{"text": "/ load time: 494.70ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:35.258Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-c4134-ad-all-sections-under-200ms-Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-c4134-ad-all-sections-under-200ms-Tablet\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-c4134-ad-all-sections-under-200ms-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 26, "line": 241}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-40b518123ce05afc28c8", "file": "standardization-validation.spec.js", "line": 230, "column": 5}, {"title": "should have fast interaction response times", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 34, "parallelIndex": 4, "status": "failed", "duration": 28952, "error": {"message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:246:18", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 246}, "snippet": "\u001b[0m \u001b[90m 244 |\u001b[39m\n \u001b[90m 245 |\u001b[39m     test(\u001b[32m'should have fast interaction response times'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 246 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 247 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 248 |\u001b[39m       \n \u001b[90m 249 |\u001b[39m       \u001b[90m// Test button interactions\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 246}, "message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 244 |\u001b[39m\n \u001b[90m 245 |\u001b[39m     test(\u001b[32m'should have fast interaction response times'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 246 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 247 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 248 |\u001b[39m       \n \u001b[90m 249 |\u001b[39m       \u001b[90m// Test button interactions\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:246:18\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:42.100Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-13ea8--interaction-response-times-Tablet\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-13ea8--interaction-response-times-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 18, "line": 246}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-d95c86265e186dead414", "file": "standardization-validation.spec.js", "line": 245, "column": 5}, {"title": "should not have infinite loops or performance issues", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 39, "parallelIndex": 4, "status": "passed", "duration": 22580, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:13:20.013Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-47fbe495cfc683e7322b", "file": "standardization-validation.spec.js", "line": 267, "column": 5}]}, {"title": "Cross-Section Consistency", "file": "standardization-validation.spec.js", "line": 292, "column": 8, "specs": [{"title": "should have consistent header and navigation across all sections", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 37, "parallelIndex": 0, "status": "failed", "duration": 20059, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreater<PERSON>han\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m2828.8\u001b[39m\nReceived:   \u001b[31m0\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m2828.8\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:313:44", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 44, "line": 313}, "snippet": "\u001b[0m \u001b[90m 311 |\u001b[39m           \n \u001b[90m 312 |\u001b[39m           \u001b[90m// Headers should be structurally similar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 313 |\u001b[39m           expect(normalizedCurrent\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBeGreaterThan(normalizedOriginal\u001b[33m.\u001b[39mlength \u001b[33m*\u001b[39m \u001b[35m0.8\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 314 |\u001b[39m           expect(normalizedCurrent\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBeLessThan(normalizedOriginal\u001b[33m.\u001b[39mlength \u001b[33m*\u001b[39m \u001b[35m1.2\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 315 |\u001b[39m         }\n \u001b[90m 316 |\u001b[39m       }\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 44, "line": 313}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m2828.8\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n\n\u001b[0m \u001b[90m 311 |\u001b[39m           \n \u001b[90m 312 |\u001b[39m           \u001b[90m// Headers should be structurally similar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 313 |\u001b[39m           expect(normalizedCurrent\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBeGreaterThan(normalizedOriginal\u001b[33m.\u001b[39mlength \u001b[33m*\u001b[39m \u001b[35m0.8\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 314 |\u001b[39m           expect(normalizedCurrent\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBeLessThan(normalizedOriginal\u001b[33m.\u001b[39mlength \u001b[33m*\u001b[39m \u001b[35m1.2\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 315 |\u001b[39m         }\n \u001b[90m 316 |\u001b[39m       }\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:313:44\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:13:42.587Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-3c29f-igation-across-all-sections-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-3c29f-igation-across-all-sections-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-3c29f-igation-across-all-sections-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 44, "line": 313}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-18549cf1b86a25d55186", "file": "standardization-validation.spec.js", "line": 294, "column": 5}, {"title": "should have consistent styling and theme across sections", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 44, "parallelIndex": 0, "status": "failed", "duration": 24169, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:324:20", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 20, "line": 324}, "snippet": "\u001b[0m \u001b[90m 322 |\u001b[39m       \u001b[36mfor\u001b[39m (\u001b[36mconst\u001b[39m section \u001b[36mof\u001b[39m \u001b[33mSECTIONS\u001b[39m) {\n \u001b[90m 323 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(section)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 324 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 325 |\u001b[39m         \n \u001b[90m 326 |\u001b[39m         \u001b[90m// Get computed styles of main elements\u001b[39m\n \u001b[90m 327 |\u001b[39m         \u001b[36mconst\u001b[39m bodyStyles \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mevaluate(() \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 20, "line": 324}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n\n\u001b[0m \u001b[90m 322 |\u001b[39m       \u001b[36mfor\u001b[39m (\u001b[36mconst\u001b[39m section \u001b[36mof\u001b[39m \u001b[33mSECTIONS\u001b[39m) {\n \u001b[90m 323 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(section)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 324 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 325 |\u001b[39m         \n \u001b[90m 326 |\u001b[39m         \u001b[90m// Get computed styles of main elements\u001b[39m\n \u001b[90m 327 |\u001b[39m         \u001b[36mconst\u001b[39m bodyStyles \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mevaluate(() \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:324:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:14:06.088Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-16594-g-and-theme-across-sections-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-16594-g-and-theme-across-sections-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 20, "line": 324}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-1bcdcdec517734d9cb9e", "file": "standardization-validation.spec.js", "line": 319, "column": 5}, {"title": "should have consistent header and navigation across all sections", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 24, "parallelIndex": 1, "status": "failed", "duration": 53, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:11:59.728Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-3c29f-igation-across-all-sections-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "ca318b099c526993b58b-b2b5b7f5fbe33f5371f9", "file": "standardization-validation.spec.js", "line": 294, "column": 5}, {"title": "should have consistent styling and theme across sections", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 27, "parallelIndex": 1, "status": "failed", "duration": 101, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:12:10.069Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-16594-g-and-theme-across-sections-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "ca318b099c526993b58b-d9f8edfaa6f1853e7d0d", "file": "standardization-validation.spec.js", "line": 319, "column": 5}, {"title": "should have consistent header and navigation across all sections", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 46, "parallelIndex": 2, "status": "failed", "duration": 11207, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:14:06.673Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-dfb4cfa902a7449262b4", "file": "standardization-validation.spec.js", "line": 294, "column": 5}, {"title": "should have consistent styling and theme across sections", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 49, "parallelIndex": 2, "status": "failed", "duration": 9828, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:14:35.845Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-2da37361e4e96cfbe4a4", "file": "standardization-validation.spec.js", "line": 319, "column": 5}, {"title": "should have consistent header and navigation across all sections", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 38, "parallelIndex": 3, "status": "failed", "duration": 20270, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreater<PERSON>han\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m2828.8\u001b[39m\nReceived:   \u001b[31m0\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m2828.8\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:313:44", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 44, "line": 313}, "snippet": "\u001b[0m \u001b[90m 311 |\u001b[39m           \n \u001b[90m 312 |\u001b[39m           \u001b[90m// Headers should be structurally similar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 313 |\u001b[39m           expect(normalizedCurrent\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBeGreaterThan(normalizedOriginal\u001b[33m.\u001b[39mlength \u001b[33m*\u001b[39m \u001b[35m0.8\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 314 |\u001b[39m           expect(normalizedCurrent\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBeLessThan(normalizedOriginal\u001b[33m.\u001b[39mlength \u001b[33m*\u001b[39m \u001b[35m1.2\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 315 |\u001b[39m         }\n \u001b[90m 316 |\u001b[39m       }\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 44, "line": 313}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m2828.8\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n\n\u001b[0m \u001b[90m 311 |\u001b[39m           \n \u001b[90m 312 |\u001b[39m           \u001b[90m// Headers should be structurally similar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 313 |\u001b[39m           expect(normalizedCurrent\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBeGreaterThan(normalizedOriginal\u001b[33m.\u001b[39mlength \u001b[33m*\u001b[39m \u001b[35m0.8\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 314 |\u001b[39m           expect(normalizedCurrent\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBeLessThan(normalizedOriginal\u001b[33m.\u001b[39mlength \u001b[33m*\u001b[39m \u001b[35m1.2\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 315 |\u001b[39m         }\n \u001b[90m 316 |\u001b[39m       }\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:313:44\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:13:42.915Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-3c29f-igation-across-all-sections-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-3c29f-igation-across-all-sections-Mobile-Chrome\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-3c29f-igation-across-all-sections-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 44, "line": 313}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-c6e7ed21acf1406f5a29", "file": "standardization-validation.spec.js", "line": 294, "column": 5}, {"title": "should have consistent styling and theme across sections", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 45, "parallelIndex": 3, "status": "passed", "duration": 34126, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:14:06.609Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-6ca99c5ecdef726e9077", "file": "standardization-validation.spec.js", "line": 319, "column": 5}, {"title": "should have consistent header and navigation across all sections", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 47, "parallelIndex": 1, "status": "failed", "duration": 11156, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:14:06.823Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-329d19957fa261e99267", "file": "standardization-validation.spec.js", "line": 294, "column": 5}, {"title": "should have consistent styling and theme across sections", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 50, "parallelIndex": 1, "status": "failed", "duration": 9714, "error": {"message": "Error: browserContext.newPage: Target page, context or browser has been closed", "stack": "Error: browserContext.newPage: Target page, context or browser has been closed"}, "errors": [{"message": "Error: browserContext.newPage: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:14:35.854Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "ca318b099c526993b58b-98841eb364d442c242db", "file": "standardization-validation.spec.js", "line": 319, "column": 5}, {"title": "should have consistent header and navigation across all sections", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 39, "parallelIndex": 4, "status": "failed", "duration": 20816, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreater<PERSON>han\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m2828.8\u001b[39m\nReceived:   \u001b[31m0\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m2828.8\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:313:44", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 44, "line": 313}, "snippet": "\u001b[0m \u001b[90m 311 |\u001b[39m           \n \u001b[90m 312 |\u001b[39m           \u001b[90m// Headers should be structurally similar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 313 |\u001b[39m           expect(normalizedCurrent\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBeGreaterThan(normalizedOriginal\u001b[33m.\u001b[39mlength \u001b[33m*\u001b[39m \u001b[35m0.8\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 314 |\u001b[39m           expect(normalizedCurrent\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBeLessThan(normalizedOriginal\u001b[33m.\u001b[39mlength \u001b[33m*\u001b[39m \u001b[35m1.2\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 315 |\u001b[39m         }\n \u001b[90m 316 |\u001b[39m       }\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 44, "line": 313}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m2828.8\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n\n\u001b[0m \u001b[90m 311 |\u001b[39m           \n \u001b[90m 312 |\u001b[39m           \u001b[90m// Headers should be structurally similar\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 313 |\u001b[39m           expect(normalizedCurrent\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBeGreaterThan(normalizedOriginal\u001b[33m.\u001b[39mlength \u001b[33m*\u001b[39m \u001b[35m0.8\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 314 |\u001b[39m           expect(normalizedCurrent\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBeLessThan(normalizedOriginal\u001b[33m.\u001b[39mlength \u001b[33m*\u001b[39m \u001b[35m1.2\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 315 |\u001b[39m         }\n \u001b[90m 316 |\u001b[39m       }\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js:313:44\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:13:43.366Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-3c29f-igation-across-all-sections-Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-3c29f-igation-across-all-sections-Tablet\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\standardization-validation-3c29f-igation-across-all-sections-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\standardization-validation.spec.js", "column": 44, "line": 313}}], "status": "unexpected"}], "id": "ca318b099c526993b58b-f946db9b341e713f46e1", "file": "standardization-validation.spec.js", "line": 294, "column": 5}, {"title": "should have consistent styling and theme across sections", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 48, "parallelIndex": 4, "status": "passed", "duration": 33333, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T21:14:08.624Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "ca318b099c526993b58b-bc592131b0099e7f0fc7", "file": "standardization-validation.spec.js", "line": 319, "column": 5}]}]}]}], "errors": [], "stats": {"startTime": "2025-07-03T21:10:22.972Z", "duration": 263107.22, "expected": 17, "skipped": 0, "unexpected": 49, "flaky": 0}}
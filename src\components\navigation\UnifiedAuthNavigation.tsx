/**
 * Unified Authentication Navigation Component
 * 
 * This component consolidates all authentication-related navigation elements
 * to prevent duplication and ensure consistent UI states between
 * authenticated and unauthenticated users.
 */

import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from '../../providers/ConsolidatedAuthProvider';
import { LoadingSpinner } from '../ui/LoadingSpinner';

interface UnifiedAuthNavigationProps {
  variant?: 'header' | 'mobile' | 'landing' | 'bottom';
  showLogo?: boolean;
  className?: string;
}

const UnifiedAuthNavigation: React.FC<UnifiedAuthNavigationProps> = ({
  variant = 'header',
  showLogo = false,
  className = ''
}) => {
  const { user, loading, signOut } = useAuth();
  const navigate = useNavigate();
  const isAuthenticated = !!user;

  // Loading state
  if (loading) {
    return (
      <div className={`flex items-center ${className}`}>
        <LoadingSpinner size="sm" />
        <span className="ml-2 text-muted-foreground text-sm">Loading...</span>
      </div>
    );
  }

  // Authenticated user state
  if (isAuthenticated) {
    switch (variant) {
      case 'header':
        return (
          <div className={`flex items-center space-x-4 ${className}`}>
            {showLogo && (
              <Link to="/" className="flex items-center space-x-2">
                <img src="/logo.svg" alt="Festival Family" className="h-8 w-8" />
                <span className="text-xl font-bold text-foreground">Festival Family</span>
              </Link>
            )}
            <div className="flex items-center space-x-2">
              <span className="text-muted-foreground text-sm">
                {user.email?.split('@')[0]}
              </span>
              <button
                type="button"
                onClick={() => signOut()}
                className="px-3 py-1 bg-destructive/80 hover:bg-destructive rounded text-destructive-foreground text-sm font-medium transition-colors"
              >
                Sign Out
              </button>
            </div>
          </div>
        );

      case 'mobile':
        return (
          <div className={`flex items-center space-x-2 ${className}`}>
            <div className="text-muted-foreground text-sm">
              {user.email?.split('@')[0]}
            </div>
            <button
              type="button"
              onClick={() => signOut()}
              className="px-2 py-1 bg-destructive/80 hover:bg-destructive rounded text-destructive-foreground text-xs font-medium transition-colors"
            >
              Sign Out
            </button>
          </div>
        );

      case 'bottom':
        return (
          <div className={`flex justify-center items-center px-4 py-1 bg-green-600/20 border-b border-green-400/20 ${className}`}>
            <span className="text-green-400 text-xs font-medium">
              Welcome, {user.email?.split('@')[0]}!
            </span>
          </div>
        );

      default:
        return null;
    }
  }

  // Unauthenticated user state
  switch (variant) {
    case 'header':
      return (
        <div className={`flex items-center space-x-4 ${className}`}>
          {showLogo && (
            <Link to="/" className="flex items-center space-x-2">
              <img src="/logo.svg" alt="Festival Family" className="h-8 w-8" />
              <span className="text-xl font-bold text-foreground">Festival Family</span>
            </Link>
          )}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link
              to="/auth"
              className="px-4 py-2 bg-primary hover:bg-primary/90 rounded-lg text-primary-foreground font-medium transition-all duration-300 shadow-lg"
            >
              Sign In
            </Link>
          </motion.div>
        </div>
      );

    case 'mobile':
      return (
        <Link
          to="/auth"
          className={`px-2 py-1 bg-primary/80 hover:bg-primary rounded text-primary-foreground text-xs font-medium transition-colors ${className}`}
        >
          Sign In
        </Link>
      );

    case 'landing':
      return (
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className={className}
        >
          <Link
            to="/auth"
            className="touch-target px-4 py-2 sm:px-6 sm:py-3 bg-primary hover:bg-primary/90 rounded-lg sm:rounded-xl text-primary-foreground font-medium text-sm sm:text-base transition-all duration-300 shadow-lg hover:shadow-xl"
            style={{ touchAction: 'manipulation' }}
          >
            <span className="hidden sm:inline">Sign In / Join</span>
            <span className="sm:hidden">Join</span>
          </Link>
        </motion.div>
      );

    case 'bottom':
      return (
        <div className={`flex justify-center items-center px-4 py-1 bg-primary/20 border-b border-primary/20 ${className}`}>
          <button
            onClick={() => navigate('/auth')}
            className="px-3 py-1 bg-primary/80 hover:bg-primary rounded text-primary-foreground text-xs font-medium transition-colors"
            style={{ touchAction: 'manipulation' }}
          >
            Sign In to Continue
          </button>
        </div>
      );

    default:
      return null;
  }
};

export default UnifiedAuthNavigation;

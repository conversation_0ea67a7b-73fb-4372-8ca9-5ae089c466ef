/**
 * Group Suggestion Card Component
 * 
 * Interactive card for displaying and responding to smart group suggestions.
 * Enables users to accept/decline activity-based and music-based group formations.
 * 
 * @module GroupSuggestionCard
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { UnifiedBadge } from '@/components/design-system'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Textarea } from '@/components/ui/textarea'
import { 
  Users, 
  Music, 
  Calendar, 
  Clock, 
  CheckCircle, 
  XCircle,
  MessageSquare,
  Sparkles,
  Target
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { GroupSuggestion } from '@/lib/supabase/activity-coordination/types'

interface GroupSuggestionCardProps {
  suggestion: GroupSuggestion
  onRespond: (suggestionId: string, response: boolean, notes?: string) => void
  isResponding?: boolean
  className?: string
}

const formationTypeConfig = {
  activity_based: {
    icon: Calendar,
    label: 'Activity Group',
    color: 'bg-secondary',
    bgColor: 'bg-secondary/10',
    textColor: 'text-secondary-foreground',
    description: 'Based on shared activity interests'
  },
  music_based: {
    icon: Music,
    label: 'Music Group',
    color: 'bg-primary',
    bgColor: 'bg-primary/10',
    textColor: 'text-primary-foreground',
    description: 'Based on music preferences'
  },
  hybrid: {
    icon: Sparkles,
    label: 'Hybrid Group',
    color: 'bg-gradient-to-r from-secondary to-primary',
    bgColor: 'bg-gradient-to-r from-secondary/10 to-primary/10',
    textColor: 'text-foreground',
    description: 'Based on activities and music'
  },
  manual: {
    icon: Users,
    label: 'Manual Group',
    color: 'bg-gray-500',
    bgColor: 'bg-gray-50',
    textColor: 'text-gray-700',
    description: 'Manually created'
  },
  spontaneous: {
    icon: Target,
    label: 'Spontaneous',
    color: 'bg-orange-500',
    bgColor: 'bg-orange-50',
    textColor: 'text-orange-700',
    description: 'Real-time coordination'
  }
} as const

export function GroupSuggestionCard({
  suggestion,
  onRespond,
  isResponding = false,
  className
}: GroupSuggestionCardProps) {
  const [showResponseForm, setShowResponseForm] = useState(false)
  const [responseNotes, setResponseNotes] = useState('')
  
  const config = formationTypeConfig[suggestion.formation_type]
  const Icon = config.icon
  
  const isExpired = new Date(suggestion.expires_at) < new Date()
  const timeUntilExpiry = Math.max(0, new Date(suggestion.expires_at).getTime() - Date.now())
  const hoursUntilExpiry = Math.floor(timeUntilExpiry / (1000 * 60 * 60))
  const minutesUntilExpiry = Math.floor((timeUntilExpiry % (1000 * 60 * 60)) / (1000 * 60))

  const handleAccept = () => {
    onRespond(suggestion.id, true, responseNotes || undefined)
    setShowResponseForm(false)
    setResponseNotes('')
  }

  const handleDecline = () => {
    onRespond(suggestion.id, false, responseNotes || undefined)
    setShowResponseForm(false)
    setResponseNotes('')
  }

  const confidencePercentage = Math.round(suggestion.confidence_score * 100)

  return (
    <Card className={cn(
      'transition-all duration-200 hover:shadow-md',
      isExpired && 'opacity-60',
      className
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className={cn(
              'p-2 rounded-lg',
              config.bgColor
            )}>
              <Icon className={cn('h-5 w-5', config.textColor)} />
            </div>
            <div>
              <CardTitle className="text-lg">{suggestion.suggested_name}</CardTitle>
              <CardDescription className="flex items-center gap-2 mt-1">
                <UnifiedBadge variant="secondary" size="sm" className={config.textColor}>
                  {config.label}
                </UnifiedBadge>
                <span className="text-xs text-gray-500">
                  {confidencePercentage}% match
                </span>
              </CardDescription>
            </div>
          </div>
          
          {!isExpired && (
            <div className="text-right text-xs text-gray-500">
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {hoursUntilExpiry > 0 ? (
                  <span>{hoursUntilExpiry}h {minutesUntilExpiry}m left</span>
                ) : (
                  <span>{minutesUntilExpiry}m left</span>
                )}
              </div>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {suggestion.suggested_description && (
          <p className="text-sm text-gray-600">
            {suggestion.suggested_description}
          </p>
        )}

        {/* Activity Focus */}
        {suggestion.activity_focus && suggestion.activity_focus.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Activities
            </h4>
            <div className="flex flex-wrap gap-1">
              {suggestion.activity_focus.map((activity, index) => (
                <UnifiedBadge key={index} variant="category" size="sm" className="text-xs">
                  {activity}
                </UnifiedBadge>
              ))}
            </div>
          </div>
        )}

        {/* Music Focus */}
        {suggestion.music_focus && suggestion.music_focus.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
              <Music className="h-4 w-4" />
              Music
            </h4>
            <div className="flex flex-wrap gap-1">
              {suggestion.music_focus.map((music, index) => (
                <UnifiedBadge key={index} variant="category" size="sm" className="text-xs">
                  {music}
                </UnifiedBadge>
              ))}
            </div>
          </div>
        )}

        {/* Group Size */}
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <Users className="h-4 w-4" />
            <span>{suggestion.min_members}-{suggestion.max_members} members</span>
          </div>
          <div className="flex items-center gap-1">
            <Target className="h-4 w-4" />
            <span>{suggestion.target_users.length} invited</span>
          </div>
        </div>

        {/* Response Form */}
        {showResponseForm && (
          <div className="space-y-3 p-3 bg-gray-50 rounded-lg">
            <Textarea
              placeholder="Add a note (optional)..."
              value={responseNotes}
              onChange={(e) => setResponseNotes(e.target.value)}
              className="min-h-[60px]"
            />
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={handleAccept}
                disabled={isResponding}
                className="flex-1"
              >
                <CheckCircle className="h-4 w-4 mr-1" />
                Accept
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleDecline}
                disabled={isResponding}
                className="flex-1"
              >
                <XCircle className="h-4 w-4 mr-1" />
                Decline
              </Button>
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-3">
        {!isExpired && !showResponseForm ? (
          <div className="flex gap-2 w-full">
            <Button
              onClick={() => setShowResponseForm(true)}
              disabled={isResponding}
              className="flex-1"
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              Respond
            </Button>
          </div>
        ) : showResponseForm ? (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setShowResponseForm(false)
              setResponseNotes('')
            }}
            className="w-full"
          >
            Cancel
          </Button>
        ) : (
          <div className="w-full text-center text-sm text-gray-500">
            This suggestion has expired
          </div>
        )}
      </CardFooter>
    </Card>
  )
}

/**
 * Group Suggestion List Component
 */
interface GroupSuggestionListProps {
  suggestions: GroupSuggestion[]
  onRespond: (suggestionId: string, response: boolean, notes?: string) => void
  isResponding?: boolean
  emptyMessage?: string
  className?: string
}

export function GroupSuggestionList({
  suggestions,
  onRespond,
  isResponding = false,
  emptyMessage = "No group suggestions available",
  className
}: GroupSuggestionListProps) {
  if (suggestions.length === 0) {
    return (
      <div className={cn(
        'text-center py-8 text-gray-500',
        className
      )}>
        <Users className="h-12 w-12 mx-auto mb-3 opacity-50" />
        <p>{emptyMessage}</p>
      </div>
    )
  }

  return (
    <div className={cn('space-y-4', className)}>
      {suggestions.map((suggestion) => (
        <GroupSuggestionCard
          key={suggestion.id}
          suggestion={suggestion}
          onRespond={onRespond}
          isResponding={isResponding}
        />
      ))}
    </div>
  )
}

/**
 * Compact Group Suggestion Card for smaller spaces
 */
interface CompactGroupSuggestionCardProps {
  suggestion: GroupSuggestion
  onRespond: (suggestionId: string, response: boolean) => void
  isResponding?: boolean
  className?: string
}

export function CompactGroupSuggestionCard({
  suggestion,
  onRespond,
  isResponding = false,
  className
}: CompactGroupSuggestionCardProps) {
  const config = formationTypeConfig[suggestion.formation_type]
  const Icon = config.icon
  const isExpired = new Date(suggestion.expires_at) < new Date()

  return (
    <Card className={cn(
      'p-3 transition-all duration-200 hover:shadow-sm',
      isExpired && 'opacity-60',
      className
    )}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className={cn('p-1.5 rounded', config.bgColor)}>
            <Icon className={cn('h-4 w-4', config.textColor)} />
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm truncate">
              {suggestion.suggested_name}
            </h4>
            <p className="text-xs text-gray-500 truncate">
              {suggestion.target_users.length} members • {Math.round(suggestion.confidence_score * 100)}% match
            </p>
          </div>
        </div>
        
        {!isExpired && (
          <div className="flex gap-1 ml-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => onRespond(suggestion.id, true)}
              disabled={isResponding}
              className="h-8 w-8 p-0"
            >
              <CheckCircle className="h-3 w-3" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onRespond(suggestion.id, false)}
              disabled={isResponding}
              className="h-8 w-8 p-0"
            >
              <XCircle className="h-3 w-3" />
            </Button>
          </div>
        )}
      </div>
    </Card>
  )
}

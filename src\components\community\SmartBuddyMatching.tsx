/**
 * Smart Buddy Matching Component
 * 
 * AI-powered festival buddy matching interface leveraging our standardized
 * architecture with UnifiedInteractionButton, BentoCard, and EnhancedUnifiedBadge.
 * 
 * @module SmartBuddyMatching
 * @version 1.0.0
 */

import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { BentoCard } from '@/components/design-system/BentoGrid';
import { UnifiedInteractionButton } from '@/components/design-system/UnifiedInteractionButton';
import { EnhancedUnifiedBadge } from '@/components/design-system/EnhancedUnifiedBadge';
import { UnifiedModal } from '@/components/design-system/UnifiedModal';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { smartBuddyMatchingService, type SmartBuddyMatch, type MatchingFilters, type MatchingPreferences } from '@/lib/intelligence/smart-buddy-matching';
import { useAuth } from '@/providers/ConsolidatedAuthProvider';
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';

interface SmartBuddyMatchingProps {
  className?: string;
}

export const SmartBuddyMatching: React.FC<SmartBuddyMatchingProps> = ({
  className = ''
}) => {
  const { user } = useAuth();
  const [showFilters, setShowFilters] = useState(false);
  const [selectedMatch, setSelectedMatch] = useState<SmartBuddyMatch | null>(null);
  const [filters, setFilters] = useState<MatchingFilters>({
    maxDistance: 50,
    minMusicCompatibility: 30,
    minActivityCompatibility: 30,
    excludeExistingConnections: true,
    onlyActiveUsers: true
  });
  
  const [preferences, setPreferences] = useState<MatchingPreferences>({
    prioritizeMusic: true,
    prioritizeActivities: true,
    prioritizeLocation: false,
    openToNewGenres: true,
    preferGroupActivities: false,
    maxMatches: 10
  });

  // Fetch smart matches
  const {
    data: matches = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['smart-buddy-matches', user?.id, filters, preferences],
    queryFn: async () => {
      if (!user?.id) return [];
      
      const response = await smartBuddyMatchingService.findSmartMatches(
        user.id,
        filters,
        preferences
      );
      
      if (response.error) {
        throw response.error;
      }
      
      return response.data || [];
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  });

  // Get compatibility badge color
  const getCompatibilityColor = (score: number) => {
    if (score >= 80) return 'green';
    if (score >= 60) return 'blue';
    if (score >= 40) return 'yellow';
    return 'red';
  };

  // Get confidence badge variant
  const getConfidenceBadge = (confidence: string) => {
    const variants = {
      high: { variant: 'default' as const, emoji: '🎯' },
      medium: { variant: 'secondary' as const, emoji: '📊' },
      low: { variant: 'outline' as const, emoji: '🤔' }
    };
    return variants[confidence as keyof typeof variants] || variants.low;
  };

  if (!user) {
    return (
      <BentoCard className={`p-6 text-center ${className}`}>
        <div className="space-y-4">
          <div className="text-2xl">🔐</div>
          <h3 className="text-lg font-semibold">Sign In Required</h3>
          <p className="text-gray-600">Please sign in to find your festival buddies!</p>
        </div>
      </BentoCard>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Filters */}
      <BentoCard className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold flex items-center gap-2">
              🧠 Smart Buddy Matching
              <EnhancedUnifiedBadge
                contentType="features"
                contentCategory="ai"
                size="sm"
              >
                AI-Powered
              </EnhancedUnifiedBadge>
            </h2>
            <p className="text-gray-600 mt-1">
              Find your perfect festival companions based on music taste, interests, and location
            </p>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              ⚙️ Filters
            </Button>
            <Button
              onClick={() => refetch()}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              {isLoading ? '🔄' : '🔍'} Find Matches
            </Button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-lg">Matching Preferences</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Distance Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Maximum Distance: {filters.maxDistance}km
                </label>
                <Slider
                  value={[filters.maxDistance || 50]}
                  onValueChange={([value]) => setFilters(prev => ({ ...prev, maxDistance: value }))}
                  max={200}
                  min={5}
                  step={5}
                  className="w-full"
                />
              </div>

              {/* Compatibility Thresholds */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Min Music Compatibility: {filters.minMusicCompatibility}%
                  </label>
                  <Slider
                    value={[filters.minMusicCompatibility || 30]}
                    onValueChange={([value]) => setFilters(prev => ({ ...prev, minMusicCompatibility: value }))}
                    max={100}
                    min={0}
                    step={10}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Min Activity Compatibility: {filters.minActivityCompatibility}%
                  </label>
                  <Slider
                    value={[filters.minActivityCompatibility || 30]}
                    onValueChange={([value]) => setFilters(prev => ({ ...prev, minActivityCompatibility: value }))}
                    max={100}
                    min={0}
                    step={10}
                  />
                </div>
              </div>

              {/* Preferences Toggles */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Prioritize Music</label>
                  <Switch
                    checked={preferences.prioritizeMusic}
                    onCheckedChange={(checked) => setPreferences(prev => ({ ...prev, prioritizeMusic: checked }))}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Prioritize Activities</label>
                  <Switch
                    checked={preferences.prioritizeActivities}
                    onCheckedChange={(checked) => setPreferences(prev => ({ ...prev, prioritizeActivities: checked }))}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Prioritize Location</label>
                  <Switch
                    checked={preferences.prioritizeLocation}
                    onCheckedChange={(checked) => setPreferences(prev => ({ ...prev, prioritizeLocation: checked }))}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Exclude Existing Connections</label>
                  <Switch
                    checked={filters.excludeExistingConnections}
                    onCheckedChange={(checked) => setFilters(prev => ({ ...prev, excludeExistingConnections: checked }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </BentoCard>

      {/* Loading State */}
      {isLoading && (
        <BentoCard className="p-8 text-center">
          <div className="space-y-4">
            <div className="text-4xl animate-spin">🧠</div>
            <h3 className="text-lg font-semibold">Finding Your Perfect Matches...</h3>
            <p className="text-gray-600">Our AI is analyzing compatibility patterns</p>
          </div>
        </BentoCard>
      )}

      {/* Error State */}
      {error && (
        <BentoCard className="p-6 border-red-200 bg-red-50">
          <div className="text-center space-y-2">
            <div className="text-2xl">❌</div>
            <h3 className="text-lg font-semibold text-red-800">Matching Error</h3>
            <p className="text-red-600">Failed to find matches. Please try again.</p>
            <Button onClick={() => refetch()} variant="outline" className="mt-2">
              Try Again
            </Button>
          </div>
        </BentoCard>
      )}

      {/* No Matches */}
      {!isLoading && !error && matches.length === 0 && (
        <BentoCard className="p-8 text-center">
          <div className="space-y-4">
            <div className="text-4xl">🔍</div>
            <h3 className="text-lg font-semibold">No Matches Found</h3>
            <p className="text-gray-600">
              Try adjusting your filters or completing your profile for better matches
            </p>
            <Button onClick={() => setShowFilters(true)} variant="outline">
              Adjust Filters
            </Button>
          </div>
        </BentoCard>
      )}

      {/* Matches Grid */}
      {!isLoading && matches.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {matches.map((match) => (
            <BentoCard key={match.profile.id} className="p-6 hover:shadow-lg transition-shadow">
              {/* Profile Header */}
              <div className="flex items-center gap-3 mb-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={match.profile.avatar_url || ''} />
                  <AvatarFallback>
                    {match.profile.username?.charAt(0).toUpperCase() || '?'}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold truncate">
                    {match.profile.full_name || match.profile.username}
                  </h3>
                  <p className="text-sm text-gray-600 truncate">
                    {match.profile.location || 'Location not specified'}
                  </p>
                </div>
                
                <div className="flex flex-col items-end gap-1">
                  <Badge 
                    variant="outline"
                    className={`text-${getCompatibilityColor(match.matchScore.overall)}-600 border-${getCompatibilityColor(match.matchScore.overall)}-300`}
                  >
                    {match.matchScore.overall}% Match
                  </Badge>
                  
                  <Badge {...getConfidenceBadge(match.matchScore.confidence)}>
                    {getConfidenceBadge(match.matchScore.confidence).emoji} {match.matchScore.confidence}
                  </Badge>
                </div>
              </div>

              {/* Compatibility Scores */}
              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span>🎵 Music</span>
                  <span className="font-medium">{match.matchScore.music}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>🎯 Activities</span>
                  <span className="font-medium">{match.matchScore.activities}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>📍 Location</span>
                  <span className="font-medium">{match.matchScore.location}%</span>
                </div>
              </div>

              {/* Match Reasons */}
              <div className="space-y-2 mb-4">
                {match.matchReasons.slice(0, 2).map((reason, index) => (
                  <p key={index} className="text-xs text-gray-600 bg-gray-50 rounded-lg px-2 py-1">
                    {reason}
                  </p>
                ))}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedMatch(match)}
                  className="flex-1"
                >
                  View Details
                </Button>
                
                <UnifiedInteractionButton
                  type="connect"
                  itemId={match.profile.id}
                  itemType="user"
                  variant="compact"
                  size="sm"
                  showCount={false}
                />
              </div>
            </BentoCard>
          ))}
        </div>
      )}

      {/* Match Details Modal */}
      {selectedMatch && (
        <UnifiedModal
          isOpen={!!selectedMatch}
          onClose={() => setSelectedMatch(null)}
          title={`Match Details: ${selectedMatch.profile.full_name || selectedMatch.profile.username}`}
          size="lg"
        >
          <div className="space-y-6">
            {/* Profile Overview */}
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={selectedMatch.profile.avatar_url || ''} />
                <AvatarFallback className="text-lg">
                  {selectedMatch.profile.username?.charAt(0).toUpperCase() || '?'}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1">
                <h3 className="text-xl font-semibold">
                  {selectedMatch.profile.full_name || selectedMatch.profile.username}
                </h3>
                <p className="text-gray-600">{selectedMatch.profile.location}</p>
                {selectedMatch.profile.bio && (
                  <p className="text-sm text-gray-700 mt-1">{selectedMatch.profile.bio}</p>
                )}
              </div>
              
              <div className="text-right">
                <div className="text-2xl font-bold text-blue-600">
                  {selectedMatch.matchScore.overall}%
                </div>
                <div className="text-sm text-gray-600">Overall Match</div>
              </div>
            </div>

            {/* Detailed Compatibility */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    🎵 Music Compatibility
                    <Badge variant="outline">{selectedMatch.matchScore.music}%</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {selectedMatch.musicCompatibility.sharedGenres.length > 0 && (
                    <div className="mb-3">
                      <p className="text-sm font-medium mb-1">Shared Genres:</p>
                      <div className="flex flex-wrap gap-1">
                        {selectedMatch.musicCompatibility.sharedGenres.map((genre) => (
                          <EnhancedUnifiedBadge
                            key={genre}
                            contentType="music"
                            contentCategory="genre"
                            size="xs"
                          >
                            {genre}
                          </EnhancedUnifiedBadge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {selectedMatch.musicCompatibility.sharedArtists.length > 0 && (
                    <div>
                      <p className="text-sm font-medium mb-1">Shared Artists:</p>
                      <div className="flex flex-wrap gap-1">
                        {selectedMatch.musicCompatibility.sharedArtists.map((artist) => (
                          <EnhancedUnifiedBadge
                            key={artist}
                            contentType="music"
                            contentCategory="artist"
                            size="xs"
                          >
                            {artist}
                          </EnhancedUnifiedBadge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    🎯 Activity Compatibility
                    <Badge variant="outline">{selectedMatch.matchScore.activities}%</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {selectedMatch.activityCompatibility.sharedInterests.length > 0 && (
                    <div className="mb-3">
                      <p className="text-sm font-medium mb-1">Shared Interests:</p>
                      <div className="flex flex-wrap gap-1">
                        {selectedMatch.activityCompatibility.sharedInterests.map((interest) => (
                          <EnhancedUnifiedBadge
                            key={interest}
                            contentType="activities"
                            contentCategory="interest"
                            size="xs"
                          >
                            {interest}
                          </EnhancedUnifiedBadge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {selectedMatch.suggestedActivities.length > 0 && (
                    <div>
                      <p className="text-sm font-medium mb-1">Suggested Activities:</p>
                      <div className="flex flex-wrap gap-1">
                        {selectedMatch.suggestedActivities.map((activity) => (
                          <Badge key={activity} variant="secondary" className="text-xs">
                            {activity}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* All Match Reasons */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Why You Match</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {selectedMatch.matchReasons.map((reason, index) => (
                    <p key={index} className="text-sm bg-blue-50 rounded-lg px-3 py-2">
                      {reason}
                    </p>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4 border-t">
              <UnifiedInteractionButton
                type="connect"
                itemId={selectedMatch.profile.id}
                itemType="user"
                variant="default"
                size="default"
                showCount={false}
                className="flex-1"
              />
              
              <Button variant="outline" className="flex-1">
                💬 Start Chat
              </Button>
              
              <Button variant="outline" className="flex-1">
                🎯 Suggest Activity
              </Button>
            </div>
          </div>
        </UnifiedModal>
      )}
    </div>
  );
};

export default SmartBuddyMatching;

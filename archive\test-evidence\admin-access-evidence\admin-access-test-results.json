{"timestamp": "2025-05-30T00:46:39.549Z", "testUser": {"email": "<EMAIL>", "password": "TestPassword123!", "fullName": "Access Test User"}, "accessControlResults": {"navigation": {"adminNavLink": false, "adminNavText": false, "adminButton": false, "secure": true}, "access": {"mainRouteBlocked": true, "finalUrl": "http://localhost:5173/", "accessDeniedMessage": false, "redirectedToHome": true, "redirectedToAuth": false}, "subRoutes": [{"route": "/admin/users", "blocked": true, "finalUrl": "http://localhost:5173/"}, {"route": "/admin/festivals", "blocked": true, "finalUrl": "http://localhost:5173/"}, {"route": "/admin/events", "blocked": true, "finalUrl": "http://localhost:5173/"}, {"route": "/admin/activities", "blocked": true, "finalUrl": "http://localhost:5173/"}], "security": {"navigationSecure": true, "mainRouteSecure": true, "subRoutesSecure": true, "overallScore": 100}, "screenshots": ["01-authenticated-home.png", "02-admin-access-attempt.png", "03-final-access-state.png"]}, "summary": {"securityScore": 100, "adminAccessBlocked": true, "navigationSecure": true, "subRoutesSecure": true, "overallSecure": true, "screenshots": ["01-authenticated-home.png", "02-admin-access-attempt.png", "03-final-access-state.png"]}}
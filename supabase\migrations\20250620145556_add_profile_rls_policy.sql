-- Enable Row Level Security for the profiles table if it's not already
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policy if it exists, to ensure a clean state
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;

-- Create the new policy
CREATE POLICY "Users can view their own profile"
ON public.profiles
FOR SELECT
USING (
  -- The authenticated user's ID matches the profile's ID
  auth.uid() = id OR
  -- Or the user has an admin role (using the is_admin function)
  is_admin(auth.uid())
);

-- Also, allow users to update their own profile
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;

CREATE POLICY "Users can update their own profile"
ON public.profiles
FOR UPDATE
USING (
  auth.uid() = id
)
WITH CHECK (
  auth.uid() = id
);
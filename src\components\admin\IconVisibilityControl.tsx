import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Palette, Eye, EyeOff, ExternalLink } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { toast } from 'react-hot-toast';

/**
 * IconVisibilityControl - Compact Admin Component
 *
 * A compact component that can be embedded in admin forms to provide
 * quick access to icon visibility controls for specific content types.
 * Links to the full IconEmojiManager for comprehensive management.
 */

interface IconVisibilityControlProps {
  contentType: string;
  category?: string;
  className?: string;
  showFullLink?: boolean;
}

interface ColorMapping {
  id: string;
  content_type: string;
  category: string;
  emoji_icon: string | null;
  show_icon: boolean | null;
}

const IconVisibilityControl: React.FC<IconVisibilityControlProps> = ({
  contentType,
  category = 'main',
  className = '',
  showFullLink = true
}) => {
  const [mapping, setMapping] = useState<ColorMapping | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  // Load the color mapping for this content type and category
  const loadMapping = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('color_mappings')
        .select('id, content_type, category, emoji_icon, show_icon')
        .eq('content_type', contentType)
        .eq('category', category)
        .single();

      if (error) {
        console.error('Error loading color mapping:', error);
        return;
      }

      setMapping(data);
    } catch (error) {
      console.error('Exception loading color mapping:', error);
    } finally {
      setLoading(false);
    }
  };

  // Update icon visibility
  const updateIconVisibility = async (showIcon: boolean) => {
    if (!mapping) return;

    try {
      setUpdating(true);
      const { error } = await supabase
        .from('color_mappings')
        .update({
          show_icon: showIcon,
          updated_at: new Date().toISOString()
        })
        .eq('id', mapping.id);

      if (error) {
        console.error('Error updating icon visibility:', error);
        toast.error('Failed to update icon visibility');
        return;
      }

      setMapping(prev => prev ? { ...prev, show_icon: showIcon } : null);
      toast.success(`Icon ${showIcon ? 'enabled' : 'disabled'} for ${contentType}.${category}`);
    } catch (error) {
      console.error('Exception updating icon visibility:', error);
      toast.error('Failed to update icon visibility');
    } finally {
      setUpdating(false);
    }
  };

  // Update emoji
  const updateEmoji = async (newEmoji: string) => {
    if (!mapping) return;

    try {
      setUpdating(true);
      const { error } = await supabase
        .from('color_mappings')
        .update({
          emoji_icon: newEmoji,
          updated_at: new Date().toISOString()
        })
        .eq('id', mapping.id);

      if (error) {
        console.error('Error updating emoji:', error);
        toast.error('Failed to update emoji');
        return;
      }

      setMapping(prev => prev ? { ...prev, emoji_icon: newEmoji } : null);
      toast.success(`Emoji updated for ${contentType}.${category}`);
    } catch (error) {
      console.error('Exception updating emoji:', error);
      toast.error('Failed to update emoji');
    } finally {
      setUpdating(false);
    }
  };

  useEffect(() => {
    loadMapping();
  }, [contentType, category]);

  if (loading) {
    return (
      <Card className={`${className} opacity-50`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Palette className="w-4 h-4" />
            <span>Loading icon settings...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!mapping) {
    return (
      <Card className={`${className} border-orange-200 bg-orange-50`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-sm text-orange-700">
            <Palette className="w-4 h-4" />
            <span>No icon mapping found for {contentType}.{category}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Palette className="w-4 h-4 text-primary" />
            <CardTitle className="text-sm">Icon Settings</CardTitle>
          </div>
          {showFullLink && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.open('/admin/icon-emoji-management', '_blank')}
              className="text-xs h-6 px-2"
            >
              <ExternalLink className="w-3 h-3 mr-1" />
              Full Manager
            </Button>
          )}
        </div>
        <CardDescription className="text-xs">
          {contentType}.{category}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Current Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-lg">{mapping.emoji_icon}</span>
            <Badge variant={mapping.show_icon ? "default" : "secondary"} className="text-xs">
              {mapping.show_icon ? 'Visible' : 'Hidden'}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            {mapping.show_icon ? (
              <Eye className="w-4 h-4 text-green-600" />
            ) : (
              <EyeOff className="w-4 h-4 text-gray-400" />
            )}
          </div>
        </div>

        {/* Quick Controls */}
        <div className="grid grid-cols-2 gap-2">
          <div className="space-y-1">
            <Label className="text-xs">Emoji</Label>
            <Input
              value={mapping.emoji_icon || ''}
              onChange={(e) => updateEmoji(e.target.value)}
              disabled={updating}
              className="text-center text-sm h-8"
              maxLength={4}
            />
          </div>
          <div className="space-y-1">
            <Label className="text-xs">Visibility</Label>
            <div className="flex items-center justify-center h-8">
              <Switch
                checked={mapping.show_icon || false}
                onCheckedChange={updateIconVisibility}
                disabled={updating}
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default IconVisibilityControl;
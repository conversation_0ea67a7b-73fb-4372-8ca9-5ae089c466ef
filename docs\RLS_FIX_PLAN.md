# RLS Fix Plan for `profiles` Table

This document outlines the plan to resolve the issue where users cannot access their profile data after logging in.

### **Analysis of the Current Code**

1.  **Authentication Flow (`src/providers/ConsolidatedAuthProvider.tsx`):** When a user signs in, the application successfully authenticates them with Supabase. Immediately after, the `handleAuthChange` function calls `fetchProfile` to retrieve that user's specific data (like `username`, `role`, etc.) from the `profiles` table.
2.  **The Point of Failure:** The application's user interface and functionality depend on the data returned from `fetchProfile`. If this function fails to get the data, it creates a temporary, generic profile, which is what's causing the broken experience.
3.  **The Root Cause (`supabase/migrations/..._fix_all_rls_recursion_issues.sql`):** This migration file, which was applied recently, updated many security policies but critically **omitted** any policy for the `profiles` table. By default, if Row Level Security (RLS) is enabled on a table, it denies all access unless a specific policy grants it.

Therefore, the `fetchProfile` function is being blocked by the database's security rules from reading the user's own data.

### **Proposed Plan**

Here is a step-by-step plan to resolve the issue by implementing the missing security policy.

#### **Step 1: Create a New Supabase Migration File**

A new SQL migration file will be created in the `supabase/migrations/` directory. This file will define the necessary RLS policy for the `profiles` table.

*   **Filename:** `supabase/migrations/YYYYMMDDHHMMSS_add_profile_rls_policy.sql` (timestamp will be current)

#### **Step 2: Define the RLS Policy**

The new migration file will contain the following SQL code. This policy will allow users to view their own profile and allow administrators to view all profiles.

```sql
-- Enable Row Level Security for the profiles table if it's not already
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policy if it exists, to ensure a clean state
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;

-- Create the new policy
CREATE POLICY "Users can view their own profile"
ON public.profiles
FOR SELECT
USING (
  -- The authenticated user's ID matches the profile's ID
  auth.uid() = id OR
  -- Or the user has an admin role (using the is_admin function)
  is_admin(auth.uid())
);

-- Also, allow users to update their own profile
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;

CREATE POLICY "Users can update their own profile"
ON public.profiles
FOR UPDATE
USING (
  auth.uid() = id
)
WITH CHECK (
  auth.uid() = id
);
```

#### **Step 3: Visualizing the Fix**

This diagram illustrates how the authentication flow is currently broken and how the new policy will fix it.

```mermaid
sequenceDiagram
    participant ClientApp as Client App
    participant SupabaseAuth as Supabase Auth
    participant Database as Supabase DB (with RLS)

    ClientApp->>SupabaseAuth: User logs in (email/password)
    SupabaseAuth-->>ClientApp: Login Success (Session returned)

    ClientApp->>Database: fetchProfile(user.id)

    Note right of Database: CURRENT STATE: No RLS policy for `profiles` table. Access is DENIED.
    Database-->>ClientApp: Returns no data / empty array

    ClientApp->>ClientApp: Creates temporary, generic profile
    Note over ClientApp: UI is broken or shows incomplete data

    %% --- The Fix --- %%

    participant NewRLSPolicy as New RLS Policy

    Note over Database: PROPOSED FIX: Apply new RLS Policy
    NewRLSPolicy-->>Database: CREATE POLICY "Users can view their own profile"

    %% --- After the Fix --- %%

    ClientApp->>Database: fetchProfile(user.id)
    Note right of Database: FIXED STATE: RLS policy allows access because auth.uid() == profile.id
    Database-->>ClientApp: Returns full user profile data
    ClientApp->>ClientApp: Sets correct user profile in state
    Note over ClientApp: UI displays correctly with user's data
```

#### **Step 4: Verification**

After this migration is created, it will need to be applied to your Supabase instance. Once applied, the `fetchProfile` function will no longer be blocked, and the application should function correctly.
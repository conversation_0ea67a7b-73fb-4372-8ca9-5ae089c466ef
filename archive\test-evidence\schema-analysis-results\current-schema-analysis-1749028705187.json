{"timestamp": "2025-06-04T09:18:23.469Z", "coreTablesStatus": {"groups": {"exists": true, "accessible": true, "recordCount": 0}, "group_members": {"exists": false, "accessible": false, "error": "relation \"public.group_members\" does not exist"}, "profiles": {"exists": true, "accessible": true, "recordCount": 0}, "activities": {"exists": true, "accessible": true, "recordCount": 0}, "festivals": {"exists": true, "accessible": true, "recordCount": 0}, "group_invitations": {"exists": false, "accessible": false, "error": "relation \"public.group_invitations\" does not exist"}}, "missingDependencies": [{"type": "table", "name": "group_members", "description": "Group membership (MISSING - causes Smart Group Formation to fail)", "impact": "Smart Group Formation will fail"}, {"type": "column", "table": "groups", "column": "is_private", "description": "Group privacy setting (MISSING - causes Smart Group Formation to fail)", "impact": "Smart Group Formation will fail"}], "readyForSmartGroupFormation": false}
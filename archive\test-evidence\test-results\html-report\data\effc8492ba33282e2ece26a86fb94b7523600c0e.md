# Test info

- Name: Festival Family Database Verification >> CRUD Operations Test
- Location: C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:119:3

# Error details

```
Error: expect(received).toMatch(expected)

Expected pattern: /(READ|CREATE|UPDATE|DELETE)/
Received string:  "🔄 Testing CRUD operations..."
    at C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:140:20
```

# Page snapshot

```yaml
- heading "🔍 Festival Family Database Test" [level=1]
- paragraph: This page tests the database connectivity and table existence for Festival Family.
- heading "📊 Database Connectivity Test" [level=2]
- button "Test Connection"
- text: ✅ Database connection successful!
- heading "📋 Table Existence Check" [level=2]
- button "Check All Tables"
- heading "⚙️ Admin Functions Test" [level=2]
- button "Test Admin Functions"
- heading "🔄 CRUD Operations Test" [level=2]
- button "Test CRUD"
- text: "✅ Announcements READ: Working (1 records) ❌ Announcements CREATE: Could not find the 'is_active' column of 'announcements' in the schema cache"
- heading "👤 Profile System Test" [level=2]
- button "Test Profile Updates"
```

# Test source

```ts
   40 |     
   41 |     // Click the table check button
   42 |     await page.click('button:has-text("Check All Tables")');
   43 |     
   44 |     // Wait for results
   45 |     await page.waitForSelector('#tables-result', { timeout: 15000 });
   46 |     await page.waitForSelector('.table-item', { timeout: 15000 });
   47 |     
   48 |     // Get all table items
   49 |     const tableItems = await page.$$('.table-item');
   50 |     const tableResults = [];
   51 |     
   52 |     for (const item of tableItems) {
   53 |       const text = await item.textContent();
   54 |       const className = await item.getAttribute('class');
   55 |       tableResults.push({
   56 |         text: text?.trim(),
   57 |         exists: className?.includes('exists') || false
   58 |       });
   59 |     }
   60 |     
   61 |     // Take screenshot
   62 |     await page.screenshot({ 
   63 |       path: 'test-results/table-existence.png',
   64 |       fullPage: true 
   65 |     });
   66 |     
   67 |     // Log results
   68 |     console.log('📋 Table Existence Results:');
   69 |     tableResults.forEach(table => {
   70 |       console.log(`${table.exists ? '✅' : '❌'} ${table.text}`);
   71 |     });
   72 |     
   73 |     // Verify core tables exist
   74 |     const coreTablesExist = tableResults.filter(t => 
   75 |       t.text?.includes('profiles') || 
   76 |       t.text?.includes('festivals') || 
   77 |       t.text?.includes('activities')
   78 |     ).every(t => t.exists);
   79 |     
   80 |     expect(coreTablesExist).toBe(true);
   81 |     
   82 |     // Check for new tables
   83 |     const newTables = ['content_management', 'user_preferences', 'emergency_contacts', 'announcements'];
   84 |     const newTablesExist = newTables.map(tableName => {
   85 |       const tableResult = tableResults.find(t => t.text?.includes(tableName));
   86 |       return { name: tableName, exists: tableResult?.exists || false };
   87 |     });
   88 |     
   89 |     console.log('🆕 New Tables Status:');
   90 |     newTablesExist.forEach(table => {
   91 |       console.log(`${table.exists ? '✅' : '❌'} ${table.name}`);
   92 |     });
   93 |   });
   94 |
   95 |   test('Admin Functions Test', async ({ page }) => {
   96 |     await page.goto('file://' + process.cwd() + '/database-test.html');
   97 |     await page.waitForLoadState('networkidle');
   98 |     
   99 |     // Click admin functions test
  100 |     await page.click('button:has-text("Test Admin Functions")');
  101 |     
  102 |     // Wait for results
  103 |     await page.waitForSelector('#admin-result', { timeout: 10000 });
  104 |     
  105 |     const result = await page.textContent('#admin-result');
  106 |     
  107 |     // Take screenshot
  108 |     await page.screenshot({ 
  109 |       path: 'test-results/admin-functions.png',
  110 |       fullPage: true 
  111 |     });
  112 |     
  113 |     console.log('⚙️ Admin Functions Result:', result);
  114 |     
  115 |     // Verify at least some admin functions work
  116 |     expect(result).toMatch(/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/);
  117 |   });
  118 |
  119 |   test('CRUD Operations Test', async ({ page }) => {
  120 |     await page.goto('file://' + process.cwd() + '/database-test.html');
  121 |     await page.waitForLoadState('networkidle');
  122 |     
  123 |     // Click CRUD test
  124 |     await page.click('button:has-text("Test CRUD")');
  125 |     
  126 |     // Wait for results
  127 |     await page.waitForSelector('#crud-result', { timeout: 15000 });
  128 |     
  129 |     const result = await page.textContent('#crud-result');
  130 |     
  131 |     // Take screenshot
  132 |     await page.screenshot({ 
  133 |       path: 'test-results/crud-operations.png',
  134 |       fullPage: true 
  135 |     });
  136 |     
  137 |     console.log('🔄 CRUD Operations Result:', result);
  138 |     
  139 |     // Verify CRUD operations are accessible
> 140 |     expect(result).toMatch(/(READ|CREATE|UPDATE|DELETE)/);
      |                    ^ Error: expect(received).toMatch(expected)
  141 |   });
  142 |
  143 |   test('Profile System Test', async ({ page }) => {
  144 |     await page.goto('file://' + process.cwd() + '/database-test.html');
  145 |     await page.waitForLoadState('networkidle');
  146 |     
  147 |     // Click profile system test
  148 |     await page.click('button:has-text("Test Profile Updates")');
  149 |     
  150 |     // Wait for results
  151 |     await page.waitForSelector('#profile-result', { timeout: 10000 });
  152 |     
  153 |     const result = await page.textContent('#profile-result');
  154 |     
  155 |     // Take screenshot
  156 |     await page.screenshot({ 
  157 |       path: 'test-results/profile-system.png',
  158 |       fullPage: true 
  159 |     });
  160 |     
  161 |     console.log('👤 Profile System Result:', result);
  162 |     
  163 |     // Verify profile system is accessible
  164 |     expect(result).toMatch(/(Profile|profile|fields|storage)/);
  165 |   });
  166 | });
  167 |
  168 | test.describe('Festival Family Admin Dashboard Tests', () => {
  169 |   test.beforeEach(async ({ page }) => {
  170 |     test.setTimeout(60000);
  171 |   });
  172 |
  173 |   test('Admin Dashboard Authentication', async ({ page }) => {
  174 |     await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
  175 |     await page.waitForLoadState('networkidle');
  176 |     
  177 |     // Click authentication test
  178 |     await page.click('button:has-text("Test Admin Authentication")');
  179 |     
  180 |     // Wait for results
  181 |     await page.waitForSelector('#auth-result', { timeout: 10000 });
  182 |     
  183 |     const result = await page.textContent('#auth-result');
  184 |     
  185 |     // Take screenshot
  186 |     await page.screenshot({ 
  187 |       path: 'test-results/admin-authentication.png',
  188 |       fullPage: true 
  189 |     });
  190 |     
  191 |     console.log('🔐 Admin Authentication Result:', result);
  192 |     
  193 |     // Check if user needs to sign in or is already authenticated
  194 |     if (result?.includes('No active session')) {
  195 |       console.log('⚠️ User needs to sign in for admin testing');
  196 |     } else {
  197 |       expect(result).toMatch(/(authenticated|admin)/i);
  198 |     }
  199 |   });
  200 |
  201 |   test('Admin Functions Status', async ({ page }) => {
  202 |     await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
  203 |     await page.waitForLoadState('networkidle');
  204 |     
  205 |     // Click admin functions test
  206 |     await page.click('button:has-text("Test All Admin Functions")');
  207 |     
  208 |     // Wait for results and grid
  209 |     await page.waitForSelector('#admin-functions-result', { timeout: 10000 });
  210 |     await page.waitForSelector('.status-item', { timeout: 5000 });
  211 |     
  212 |     // Get function status
  213 |     const statusItems = await page.$$('.status-item');
  214 |     const functionResults = [];
  215 |     
  216 |     for (const item of statusItems) {
  217 |       const text = await item.textContent();
  218 |       const className = await item.getAttribute('class');
  219 |       functionResults.push({
  220 |         text: text?.trim(),
  221 |         working: className?.includes('working') || false
  222 |       });
  223 |     }
  224 |     
  225 |     // Take screenshot
  226 |     await page.screenshot({ 
  227 |       path: 'test-results/admin-functions-status.png',
  228 |       fullPage: true 
  229 |     });
  230 |     
  231 |     console.log('⚙️ Admin Functions Status:');
  232 |     functionResults.forEach(func => {
  233 |       console.log(`${func.working ? '✅' : '❌'} ${func.text}`);
  234 |     });
  235 |   });
  236 |
  237 |   test('Content Management Test', async ({ page }) => {
  238 |     await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
  239 |     await page.waitForLoadState('networkidle');
  240 |     
```
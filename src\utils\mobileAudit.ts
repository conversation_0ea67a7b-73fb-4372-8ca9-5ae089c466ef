/**
 * Mobile Performance & Accessibility Audit System
 * 
 * Comprehensive audit system for validating mobile optimizations,
 * performance metrics, accessibility compliance, and architecture integrity.
 */

import { measurePerformance, checkPerformanceBudget } from './performanceOptimization';
import { auditAccessibility } from './accessibilityUtils';

export interface AuditResult {
  category: string;
  passed: boolean;
  score: number;
  issues: AuditIssue[];
  recommendations: string[];
}

export interface AuditIssue {
  severity: 'error' | 'warning' | 'info';
  message: string;
  element?: string;
  fix?: string;
}

export interface ComprehensiveAuditReport {
  overall: {
    score: number;
    passed: boolean;
    timestamp: string;
  };
  performance: AuditResult;
  accessibility: AuditResult;
  mobile: AuditResult;
  architecture: AuditResult;
  recommendations: string[];
}

// Performance audit
export const auditPerformance = async (): Promise<AuditResult> => {
  const issues: AuditIssue[] = [];
  const recommendations: string[] = [];

  try {
    const budget = await checkPerformanceBudget();
    let score = 100;

    if (!budget.passed) {
      budget.violations.forEach(violation => {
        issues.push({
          severity: 'error',
          message: `Performance budget violation: ${violation}`,
          fix: 'Optimize loading performance and reduce resource sizes'
        });
        score -= 15;
      });
    }

    // Check for large bundle sizes
    if (performance.getEntriesByType) {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const largeResources = resources.filter(resource => 
        resource.transferSize > 500000 // 500KB
      );

      largeResources.forEach(resource => {
        issues.push({
          severity: 'warning',
          message: `Large resource detected: ${resource.name} (${Math.round(resource.transferSize / 1024)}KB)`,
          fix: 'Consider code splitting or compression'
        });
        score -= 5;
      });
    }

    // Performance recommendations
    if (score < 90) {
      recommendations.push('Implement lazy loading for images and components');
      recommendations.push('Enable compression and caching');
      recommendations.push('Optimize bundle size with code splitting');
    }

    return {
      category: 'Performance',
      passed: score >= 90,
      score: Math.max(0, score),
      issues,
      recommendations
    };
  } catch (error) {
    return {
      category: 'Performance',
      passed: false,
      score: 0,
      issues: [{
        severity: 'error',
        message: `Performance audit failed: ${error}`,
        fix: 'Check browser compatibility and performance API support'
      }],
      recommendations: ['Ensure performance monitoring is properly configured']
    };
  }
};

// Mobile-specific audit
export const auditMobileOptimization = (): AuditResult => {
  const issues: AuditIssue[] = [];
  const recommendations: string[] = [];
  let score = 100;

  // Check viewport meta tag
  const viewport = document.querySelector('meta[name="viewport"]');
  if (!viewport) {
    issues.push({
      severity: 'error',
      message: 'Missing viewport meta tag',
      fix: 'Add <meta name="viewport" content="width=device-width, initial-scale=1.0">'
    });
    score -= 20;
  }

  // Check touch target sizes
  const interactiveElements = document.querySelectorAll('button, a, input, select, textarea');
  let smallTargets = 0;

  interactiveElements.forEach((element, index) => {
    const rect = element.getBoundingClientRect();
    if (rect.width < 44 || rect.height < 44) {
      smallTargets++;
      if (smallTargets <= 5) { // Limit reported issues
        issues.push({
          severity: 'warning',
          message: `Touch target too small: ${element.tagName} (${Math.round(rect.width)}x${Math.round(rect.height)}px)`,
          element: element.tagName.toLowerCase(),
          fix: 'Increase touch target size to minimum 44x44px'
        });
      }
    }
  });

  if (smallTargets > 0) {
    score -= Math.min(30, smallTargets * 3);
    if (smallTargets > 5) {
      issues.push({
        severity: 'warning',
        message: `${smallTargets - 5} additional touch targets are too small`,
        fix: 'Review all interactive elements for proper touch target sizing'
      });
    }
  }

  // Check for mobile-unfriendly patterns
  const fixedElements = document.querySelectorAll('[style*="position: fixed"]');
  if (fixedElements.length > 3) {
    issues.push({
      severity: 'warning',
      message: 'Multiple fixed position elements may cause mobile layout issues',
      fix: 'Review fixed positioning for mobile compatibility'
    });
    score -= 10;
  }

  // Check for horizontal scrolling
  if (document.body.scrollWidth > window.innerWidth) {
    issues.push({
      severity: 'error',
      message: 'Horizontal scrolling detected on mobile',
      fix: 'Ensure content fits within viewport width'
    });
    score -= 15;
  }

  // Mobile recommendations
  if (score < 90) {
    recommendations.push('Implement touch-friendly navigation patterns');
    recommendations.push('Optimize content layout for mobile screens');
    recommendations.push('Add haptic feedback for better mobile UX');
  }

  return {
    category: 'Mobile Optimization',
    passed: score >= 90,
    score: Math.max(0, score),
    issues,
    recommendations
  };
};

// Architecture audit for single source of truth
export const auditArchitecture = (): AuditResult => {
  const issues: AuditIssue[] = [];
  const recommendations: string[] = [];
  let score = 100;

  // Check for duplicate component patterns (simplified check)
  const suspiciousPatterns = [
    'BottomNav', 'Navigation', 'Nav', // Navigation components
    'Button', 'Btn', // Button components
    'Modal', 'Dialog', 'Popup', // Modal components
    'Card', // Card components
  ];

  // This is a simplified check - in a real implementation, 
  // you'd analyze the actual component tree and imports
  const scripts = Array.from(document.querySelectorAll('script[src]'));
  const duplicatePatterns = new Set<string>();

  suspiciousPatterns.forEach(pattern => {
    const matches = scripts.filter(script => 
      script.getAttribute('src')?.includes(pattern.toLowerCase())
    );
    if (matches.length > 1) {
      duplicatePatterns.add(pattern);
    }
  });

  if (duplicatePatterns.size > 0) {
    duplicatePatterns.forEach(pattern => {
      issues.push({
        severity: 'warning',
        message: `Potential duplicate component pattern detected: ${pattern}`,
        fix: 'Consolidate to single source of truth implementation'
      });
    });
    score -= duplicatePatterns.size * 10;
  }

  // Check for consistent mobile patterns
  const mobileUtilityUsage = document.querySelectorAll('[class*="mobile-"], [class*="touch-"]');
  if (mobileUtilityUsage.length === 0) {
    issues.push({
      severity: 'info',
      message: 'No mobile-specific utility classes detected',
      fix: 'Consider using consistent mobile utility patterns'
    });
    score -= 5;
  }

  // Architecture recommendations
  if (score < 95) {
    recommendations.push('Audit codebase for duplicate component implementations');
    recommendations.push('Establish clear component hierarchy and naming conventions');
    recommendations.push('Implement consistent mobile utility patterns');
  }

  return {
    category: 'Architecture',
    passed: score >= 95,
    score: Math.max(0, score),
    issues,
    recommendations
  };
};

// Comprehensive audit
export const runComprehensiveAudit = async (): Promise<ComprehensiveAuditReport> => {
  console.log('🔍 Starting comprehensive mobile audit...');

  const [performance, accessibility, mobile, architecture] = await Promise.all([
    auditPerformance(),
    auditAccessibility().then(result => ({
      category: 'Accessibility',
      passed: result.passed,
      score: result.passed ? 100 : Math.max(0, 100 - result.violations.length * 10),
      issues: [
        ...result.violations.map(v => ({ severity: 'error' as const, message: v, fix: 'Address accessibility violation' })),
        ...result.warnings.map(w => ({ severity: 'warning' as const, message: w, fix: 'Consider accessibility improvement' }))
      ],
      recommendations: result.passed ? [] : ['Review WCAG 2.1 AA guidelines', 'Implement proper ARIA labels', 'Ensure keyboard navigation support']
    })),
    auditMobileOptimization(),
    auditArchitecture()
  ]);

  const overallScore = Math.round(
    (performance.score + accessibility.score + mobile.score + architecture.score) / 4
  );

  const allRecommendations = [
    ...performance.recommendations,
    ...accessibility.recommendations,
    ...mobile.recommendations,
    ...architecture.recommendations
  ];

  const report: ComprehensiveAuditReport = {
    overall: {
      score: overallScore,
      passed: overallScore >= 90,
      timestamp: new Date().toISOString()
    },
    performance,
    accessibility,
    mobile,
    architecture,
    recommendations: [...new Set(allRecommendations)]
  };

  console.log('✅ Audit completed:', {
    score: overallScore,
    passed: report.overall.passed,
    categories: {
      performance: performance.score,
      accessibility: accessibility.score,
      mobile: mobile.score,
      architecture: architecture.score
    }
  });

  return report;
};

// Generate audit report
export const generateAuditReport = (report: ComprehensiveAuditReport): string => {
  const { overall, performance, accessibility, mobile, architecture, recommendations } = report;

  return `
# Mobile Performance & Accessibility Audit Report

**Generated:** ${new Date(overall.timestamp).toLocaleString()}
**Overall Score:** ${overall.score}/100 ${overall.passed ? '✅' : '❌'}

## Category Scores

| Category | Score | Status | Issues |
|----------|-------|--------|--------|
| Performance | ${performance.score}/100 | ${performance.passed ? '✅' : '❌'} | ${performance.issues.length} |
| Accessibility | ${accessibility.score}/100 | ${accessibility.passed ? '✅' : '❌'} | ${accessibility.issues.length} |
| Mobile Optimization | ${mobile.score}/100 | ${mobile.passed ? '✅' : '❌'} | ${mobile.issues.length} |
| Architecture | ${architecture.score}/100 | ${architecture.passed ? '✅' : '❌'} | ${architecture.issues.length} |

## Issues Found

${[performance, accessibility, mobile, architecture]
  .flatMap(category => 
    category.issues.map(issue => 
      `- **${issue.severity.toUpperCase()}** [${category.category}]: ${issue.message}${issue.fix ? ` → ${issue.fix}` : ''}`
    )
  )
  .join('\n')}

## Recommendations

${recommendations.map(rec => `- ${rec}`).join('\n')}

## Next Steps

${overall.passed 
  ? '🎉 Great job! Your mobile optimization is meeting high standards. Continue monitoring performance and accessibility.'
  : '🔧 Focus on addressing the issues above to improve your mobile experience. Prioritize errors first, then warnings.'
}
  `;
};

export default {
  auditPerformance,
  auditMobileOptimization,
  auditArchitecture,
  runComprehensiveAudit,
  generateAuditReport
};

/**
 * Optimized Realtime Service
 *
 * High-performance real-time subscription manager with connection pooling,
 * subscription batching, and automatic cleanup for production scalability.
 */

import { BaseService } from './base-service'
import type { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js'

export type SubscriptionCallback<T extends { [key: string]: any }> = (payload: RealtimePostgresChangesPayload<T>) => void

interface SubscriptionConfig {
  table: string
  event: 'INSERT' | 'UPDATE' | 'DELETE' | '*'
  filter?: string
  callback: SubscriptionCallback<any>
  priority: 'high' | 'medium' | 'low'
}

interface ChannelMetrics {
  subscriptionCount: number
  lastActivity: number
  connectionHealth: 'healthy' | 'degraded' | 'failed'
  errorCount: number
  messageCount: number
}

interface SubscriptionBatch {
  channelId: string
  subscriptions: Map<string, SubscriptionConfig>
  channel: RealtimeChannel | null
  metrics: ChannelMetrics
  cleanupTimer?: NodeJS.Timeout
}

export class OptimizedRealtimeService extends BaseService {
  private subscriptionBatches: Map<string, SubscriptionBatch> = new Map()
  private subscriptionRegistry: Map<string, string> = new Map() // subscriptionId -> channelId
  private performanceMetrics = {
    totalSubscriptions: 0,
    activeChannels: 0,
    messagesProcessed: 0,
    errorsEncountered: 0,
    averageLatency: 0,
    connectionUptime: Date.now()
  }
  
  // Configuration
  private readonly MAX_SUBSCRIPTIONS_PER_CHANNEL = 10
  private readonly CLEANUP_INTERVAL = 30000 // 30 seconds
  private readonly IDLE_TIMEOUT = 300000 // 5 minutes
  private readonly BATCH_DELAY = 100 // 100ms batching delay
  
  constructor() {
    super()
    this.startPerformanceMonitoring()
    this.startCleanupScheduler()
  }

  /**
   * Subscribe to table changes with optimized batching
   */
  subscribe<T extends { [key: string]: any }>(
    table: string,
    callback: SubscriptionCallback<T>,
    options?: {
      event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*'
      filter?: string
      priority?: 'high' | 'medium' | 'low'
    }
  ): string {
    const event = options?.event || '*'
    const filter = options?.filter
    const priority = options?.priority || 'medium'
    
    // Generate unique subscription ID
    const subscriptionId = `${table}_${event}_${filter || 'all'}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // Find or create optimal channel for this subscription
    const channelId = this.findOptimalChannel(table, event, filter)
    
    const config: SubscriptionConfig = {
      table,
      event,
      filter,
      callback: callback as SubscriptionCallback<any>,
      priority
    }
    
    // Add subscription to batch
    this.addSubscriptionToBatch(channelId, subscriptionId, config)
    
    // Register subscription
    this.subscriptionRegistry.set(subscriptionId, channelId)
    this.performanceMetrics.totalSubscriptions++
    
    console.log(`📡 Optimized subscription created: ${subscriptionId} -> ${channelId}`)
    
    return subscriptionId
  }

  /**
   * Find optimal channel for subscription (connection pooling)
   */
  private findOptimalChannel(table: string, event: string, filter?: string): string {
    const baseChannelId = `${table}_${event}_${filter || 'all'}`
    
    // Look for existing channels with capacity
    for (const [channelId, batch] of this.subscriptionBatches) {
      if (
        channelId.startsWith(baseChannelId) &&
        batch.subscriptions.size < this.MAX_SUBSCRIPTIONS_PER_CHANNEL &&
        batch.metrics.connectionHealth === 'healthy'
      ) {
        return channelId
      }
    }
    
    // Create new channel if needed
    const newChannelId = `${baseChannelId}_${Date.now()}`
    this.createSubscriptionBatch(newChannelId)
    
    return newChannelId
  }

  /**
   * Create new subscription batch
   */
  private createSubscriptionBatch(channelId: string): void {
    const batch: SubscriptionBatch = {
      channelId,
      subscriptions: new Map(),
      channel: null,
      metrics: {
        subscriptionCount: 0,
        lastActivity: Date.now(),
        connectionHealth: 'healthy',
        errorCount: 0,
        messageCount: 0
      }
    }
    
    this.subscriptionBatches.set(channelId, batch)
  }

  /**
   * Add subscription to batch with delayed channel creation
   */
  private addSubscriptionToBatch(
    channelId: string,
    subscriptionId: string,
    config: SubscriptionConfig
  ): void {
    const batch = this.subscriptionBatches.get(channelId)
    if (!batch) return
    
    batch.subscriptions.set(subscriptionId, config)
    batch.metrics.subscriptionCount++
    
    // Delay channel creation to allow batching
    if (batch.cleanupTimer) {
      clearTimeout(batch.cleanupTimer)
    }
    
    batch.cleanupTimer = setTimeout(() => {
      this.createOrUpdateChannel(channelId)
    }, this.BATCH_DELAY)
  }

  /**
   * Create or update channel with all batched subscriptions
   */
  private createOrUpdateChannel(channelId: string): void {
    const batch = this.subscriptionBatches.get(channelId)
    if (!batch || batch.subscriptions.size === 0) return
    
    // Close existing channel if any
    if (batch.channel) {
      batch.channel.unsubscribe()
    }
    
    // Create new channel
    const channel = this.client.channel(channelId)
    
    // Add all subscriptions to the channel
    for (const [subscriptionId, config] of batch.subscriptions) {
      channel.on(
        'postgres_changes' as any,
        {
          event: config.event,
          schema: 'public',
          table: config.table,
          filter: config.filter
        },
        (payload: any) => {
          try {
            const startTime = performance.now()
            
            // Update metrics
            batch.metrics.messageCount++
            batch.metrics.lastActivity = Date.now()
            this.performanceMetrics.messagesProcessed++
            
            // Call subscription callback
            config.callback(payload as RealtimePostgresChangesPayload<any>)
            
            // Track latency
            const latency = performance.now() - startTime
            this.updateAverageLatency(latency)
            
          } catch (error) {
            console.error(`❌ Subscription callback error for ${subscriptionId}:`, error)
            batch.metrics.errorCount++
            this.performanceMetrics.errorsEncountered++
            
            // Mark channel as degraded if too many errors
            if (batch.metrics.errorCount > 5) {
              batch.metrics.connectionHealth = 'degraded'
            }
          }
        }
      )
    }
    
    // Subscribe to channel
    channel.subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        batch.metrics.connectionHealth = 'healthy'
        batch.metrics.errorCount = 0
        this.performanceMetrics.activeChannels++
        console.log(`✅ Channel subscribed: ${channelId} (${batch.subscriptions.size} subscriptions)`)
      } else if (status === 'CHANNEL_ERROR') {
        batch.metrics.connectionHealth = 'failed'
        console.error(`❌ Channel error: ${channelId}`)
      }
    })
    
    batch.channel = channel
  }

  /**
   * Unsubscribe from a specific subscription
   */
  unsubscribe(subscriptionId: string): void {
    const channelId = this.subscriptionRegistry.get(subscriptionId)
    if (!channelId) return
    
    const batch = this.subscriptionBatches.get(channelId)
    if (!batch) return
    
    // Remove subscription from batch
    batch.subscriptions.delete(subscriptionId)
    batch.metrics.subscriptionCount--
    this.subscriptionRegistry.delete(subscriptionId)
    this.performanceMetrics.totalSubscriptions--
    
    console.log(`🔌 Subscription removed: ${subscriptionId}`)
    
    // If no subscriptions left, schedule channel cleanup
    if (batch.subscriptions.size === 0) {
      this.scheduleChannelCleanup(channelId)
    } else {
      // Recreate channel with remaining subscriptions
      this.createOrUpdateChannel(channelId)
    }
  }

  /**
   * Schedule channel cleanup for idle channels
   */
  private scheduleChannelCleanup(channelId: string): void {
    const batch = this.subscriptionBatches.get(channelId)
    if (!batch) return
    
    if (batch.cleanupTimer) {
      clearTimeout(batch.cleanupTimer)
    }
    
    batch.cleanupTimer = setTimeout(() => {
      this.cleanupChannel(channelId)
    }, this.IDLE_TIMEOUT)
  }

  /**
   * Clean up idle channel
   */
  private cleanupChannel(channelId: string): void {
    const batch = this.subscriptionBatches.get(channelId)
    if (!batch) return
    
    if (batch.channel) {
      batch.channel.unsubscribe()
      this.performanceMetrics.activeChannels--
    }
    
    if (batch.cleanupTimer) {
      clearTimeout(batch.cleanupTimer)
    }
    
    this.subscriptionBatches.delete(channelId)
    console.log(`🧹 Channel cleaned up: ${channelId}`)
  }

  /**
   * Unsubscribe from all channels
   */
  unsubscribeAll(): void {
    for (const [channelId] of this.subscriptionBatches) {
      this.cleanupChannel(channelId)
    }
    
    this.subscriptionRegistry.clear()
    this.performanceMetrics.totalSubscriptions = 0
    this.performanceMetrics.activeChannels = 0
    
    console.log('🧹 All subscriptions cleaned up')
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    return {
      ...this.performanceMetrics,
      channelDetails: Array.from(this.subscriptionBatches.entries()).map(([channelId, batch]) => ({
        channelId,
        subscriptionCount: batch.subscriptions.size,
        connectionHealth: batch.metrics.connectionHealth,
        messageCount: batch.metrics.messageCount,
        errorCount: batch.metrics.errorCount,
        lastActivity: batch.metrics.lastActivity
      }))
    }
  }

  /**
   * Start performance monitoring
   */
  private startPerformanceMonitoring(): void {
    setInterval(() => {
      const metrics = this.getPerformanceMetrics()
      
      // Log performance summary in development
      if (process.env.NODE_ENV === 'development') {
        console.log('📊 Realtime Performance:', {
          subscriptions: metrics.totalSubscriptions,
          channels: metrics.activeChannels,
          messages: metrics.messagesProcessed,
          errors: metrics.errorsEncountered,
          avgLatency: `${metrics.averageLatency.toFixed(2)}ms`
        })
      }
    }, 60000) // Every minute
  }

  /**
   * Start cleanup scheduler
   */
  private startCleanupScheduler(): void {
    setInterval(() => {
      const now = Date.now()
      
      for (const [channelId, batch] of this.subscriptionBatches) {
        // Clean up idle channels
        if (
          batch.subscriptions.size === 0 &&
          now - batch.metrics.lastActivity > this.IDLE_TIMEOUT
        ) {
          this.cleanupChannel(channelId)
        }
        
        // Reset error counts periodically
        if (batch.metrics.errorCount > 0 && now - batch.metrics.lastActivity > 300000) {
          batch.metrics.errorCount = 0
          batch.metrics.connectionHealth = 'healthy'
        }
      }
    }, this.CLEANUP_INTERVAL)
  }

  /**
   * Update average latency metric
   */
  private updateAverageLatency(newLatency: number): void {
    const alpha = 0.1 // Exponential moving average factor
    this.performanceMetrics.averageLatency = 
      this.performanceMetrics.averageLatency * (1 - alpha) + newLatency * alpha
  }
}

// Export singleton instance
export const optimizedRealtimeService = new OptimizedRealtimeService()

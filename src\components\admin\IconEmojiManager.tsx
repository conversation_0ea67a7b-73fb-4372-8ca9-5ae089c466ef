import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Palette,
  Eye,
  EyeOff,
  Save,
  RefreshCw,
  Sparkles,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { supabase } from '@/lib/supabase';

/**
 * IconEmojiManager - Comprehensive Admin Interface
 *
 * Manages icons, emojis, colors, and visual elements with real-time preview
 * capabilities for Festival Family's enhanced visual design system.
 */

interface ColorMapping {
  id: string;
  content_type: string;
  category: string;
  color_primary: string;
  color_secondary: string;
  color_accent: string;
  emoji_icon: string | null;
  description: string | null;
  show_icon: boolean | null;
  admin_configurable: boolean | null;
}

interface ColorMappingUpdate {
  id: string;
  color_primary?: string;
  color_secondary?: string;
  color_accent?: string;
  emoji_icon?: string;
  show_icon?: boolean;
  description?: string;
}

const IconEmojiManager: React.FC = () => {
  const [colorMappings, setColorMappings] = useState<ColorMapping[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [selectedContentType, setSelectedContentType] = useState<string>('activities');
  const [previewMode, setPreviewMode] = useState(false);
  const [changes, setChanges] = useState<Map<string, ColorMappingUpdate>>(new Map());

  // Content type configurations for organization
  const contentTypes = [
    { key: 'activities', label: 'Activities', icon: '🎪', description: 'Festival activities and events' },
    { key: 'tips', label: 'Tips', icon: '💡', description: 'Helpful tips and advice' },
    { key: 'community', label: 'Community', icon: '🤝', description: 'Social connections and groups' },
    { key: 'festivals', label: 'Festivals', icon: '🎪', description: 'Festival information' },
    { key: 'resources', label: 'Resources', icon: '📚', description: 'Guides and resources' }
  ];

  // Load color mappings from database
  const loadColorMappings = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('color_mappings')
        .select('*')
        .order('content_type', { ascending: true })
        .order('category', { ascending: true });

      if (error) {
        console.error('Error loading color mappings:', error);
        toast.error('Failed to load color mappings');
        return;
      }

      setColorMappings(data || []);
      console.log('✅ Loaded color mappings:', data?.length || 0);
    } catch (error) {
      console.error('Exception loading color mappings:', error);
      toast.error('Failed to load color mappings');
    } finally {
      setLoading(false);
    }
  };

  // Save changes to database
  const saveChanges = async () => {
    if (changes.size === 0) {
      toast.success('No changes to save');
      return;
    }

    try {
      setSaving(true);
      const updates = Array.from(changes.values());

      console.log('💾 Saving changes:', updates);

      for (const update of updates) {
        const { id, ...updateData } = update;
        const { error } = await supabase
          .from('color_mappings')
          .update({
            ...updateData,
            updated_at: new Date().toISOString()
          })
          .eq('id', id);

        if (error) {
          console.error('Error updating color mapping:', error);
          toast.error(`Failed to update mapping: ${error.message}`);
          return;
        }
      }

      // Reload data and clear changes
      await loadColorMappings();
      setChanges(new Map());
      toast.success(`Successfully updated ${updates.length} color mappings`);

    } catch (error) {
      console.error('Exception saving changes:', error);
      toast.error('Failed to save changes');
    } finally {
      setSaving(false);
    }
  };

  // Update a color mapping locally (before saving)
  const updateMapping = (id: string, updates: Partial<ColorMappingUpdate>) => {
    const existingChange = changes.get(id) || { id };
    const newChange = { ...existingChange, ...updates };

    const newChanges = new Map(changes);
    newChanges.set(id, newChange);
    setChanges(newChanges);

    // Update local state for immediate UI feedback
    setColorMappings(prev => prev.map(mapping =>
      mapping.id === id ? { ...mapping, ...updates } : mapping
    ));
  };

  // Get filtered mappings for selected content type
  const getFilteredMappings = () => {
    return colorMappings.filter(mapping => mapping.content_type === selectedContentType);
  };

  // Load data on component mount
  useEffect(() => {
    loadColorMappings();
  }, []);

  return (
    <div className="w-full max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                <Palette className="w-5 h-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-2xl">Icon & Emoji Management</CardTitle>
                <CardDescription>
                  Manage visual elements, colors, and icon visibility across Festival Family
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={() => setPreviewMode(!previewMode)}
                className="flex items-center gap-2"
              >
                {previewMode ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                {previewMode ? 'Exit Preview' : 'Preview Mode'}
              </Button>
              <Button
                onClick={saveChanges}
                disabled={saving || changes.size === 0}
                className="flex items-center gap-2"
              >
                {saving ? <Loader2 className="w-4 h-4 animate-spin" /> : <Save className="w-4 h-4" />}
                Save Changes {changes.size > 0 && `(${changes.size})`}
              </Button>
              <Button
                variant="outline"
                onClick={loadColorMappings}
                disabled={loading}
                className="flex items-center gap-2"
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Content Type Tabs */}
      <Tabs value={selectedContentType} onValueChange={setSelectedContentType}>
        <TabsList className="grid w-full grid-cols-5">
          {contentTypes.map((type) => (
            <TabsTrigger key={type.key} value={type.key} className="flex items-center gap-2">
              <span className="text-lg">{type.icon}</span>
              <span className="hidden sm:inline">{type.label}</span>
            </TabsTrigger>
          ))}
        </TabsList>

        {contentTypes.map((type) => (
          <TabsContent key={type.key} value={type.key} className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <span className="text-2xl">{type.icon}</span>
                  {type.label} Visual Settings
                </CardTitle>
                <CardDescription>{type.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ColorMappingGrid
                  mappings={getFilteredMappings()}
                  onUpdate={updateMapping}
                  previewMode={previewMode}
                  changes={changes}
                />
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Changes Summary */}
      {changes.size > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="fixed bottom-6 right-6 z-50"
        >
          <Card className="bg-blue-50 border-blue-200 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-blue-800">
                <AlertCircle className="w-4 h-4" />
                <span className="font-medium">{changes.size} unsaved changes</span>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
};

// ColorMappingGrid Component
interface ColorMappingGridProps {
  mappings: ColorMapping[];
  onUpdate: (id: string, updates: Partial<ColorMappingUpdate>) => void;
  previewMode: boolean;
  changes: Map<string, ColorMappingUpdate>;
}

const ColorMappingGrid: React.FC<ColorMappingGridProps> = ({
  mappings,
  onUpdate,
  previewMode,
  changes
}) => {
  if (mappings.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <Sparkles className="w-12 h-12 mx-auto mb-4 opacity-50" />
        <p>No color mappings found for this content type</p>
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {mappings.map((mapping) => (
        <ColorMappingCard
          key={mapping.id}
          mapping={mapping}
          onUpdate={onUpdate}
          previewMode={previewMode}
          hasChanges={changes.has(mapping.id)}
        />
      ))}
    </div>
  );
};

// ColorMappingCard Component
interface ColorMappingCardProps {
  mapping: ColorMapping;
  onUpdate: (id: string, updates: Partial<ColorMappingUpdate>) => void;
  previewMode: boolean;
  hasChanges: boolean;
}

const ColorMappingCard: React.FC<ColorMappingCardProps> = ({
  mapping,
  onUpdate,
  previewMode,
  hasChanges
}) => {
  const [localEmoji, setLocalEmoji] = useState(mapping.emoji_icon);
  const [localDescription, setLocalDescription] = useState(mapping.description);

  const handleEmojiChange = (newEmoji: string) => {
    setLocalEmoji(newEmoji);
    onUpdate(mapping.id, { emoji_icon: newEmoji });
  };

  const handleDescriptionChange = (newDescription: string) => {
    setLocalDescription(newDescription);
    onUpdate(mapping.id, { description: newDescription });
  };

  const handleColorChange = (colorType: 'color_primary' | 'color_secondary' | 'color_accent', newColor: string) => {
    onUpdate(mapping.id, { [colorType]: newColor });
  };

  const handleIconVisibilityToggle = () => {
    onUpdate(mapping.id, { show_icon: !mapping.show_icon });
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`relative ${hasChanges ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}`}
    >
      <Card className={`${previewMode ? 'bg-gray-50' : ''} transition-all duration-200`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div
                className="w-8 h-8 rounded-lg flex items-center justify-center text-lg"
                style={{ backgroundColor: `${mapping.color_primary}20` }}
              >
                {mapping.show_icon ? localEmoji : '👁️‍🗨️'}
              </div>
              <div>
                <h3 className="font-semibold capitalize">{mapping.category}</h3>
                <p className="text-xs text-muted-foreground">
                  {mapping.content_type}.{mapping.category}
                </p>
              </div>
            </div>
            {hasChanges && (
              <Badge variant="secondary" className="text-xs">
                Modified
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Color Swatches */}
          <div className="space-y-2">
            <Label className="text-xs font-medium">Colors</Label>
            <div className="grid grid-cols-3 gap-2">
              <ColorInput
                label="Primary"
                value={mapping.color_primary}
                onChange={(color) => handleColorChange('color_primary', color)}
                disabled={previewMode}
              />
              <ColorInput
                label="Secondary"
                value={mapping.color_secondary}
                onChange={(color) => handleColorChange('color_secondary', color)}
                disabled={previewMode}
              />
              <ColorInput
                label="Accent"
                value={mapping.color_accent}
                onChange={(color) => handleColorChange('color_accent', color)}
                disabled={previewMode}
              />
            </div>
          </div>

          {/* Emoji/Icon Input */}
          <div className="space-y-2">
            <Label className="text-xs font-medium">Icon/Emoji</Label>
            <div className="flex items-center gap-2">
              <Input
                value={localEmoji || ''}
                onChange={(e) => handleEmojiChange(e.target.value)}
                placeholder="🎪"
                className="text-center text-lg w-16 h-10"
                disabled={previewMode}
                maxLength={4}
              />
              <Switch
                checked={mapping.show_icon || false}
                onCheckedChange={handleIconVisibilityToggle}
                disabled={previewMode}
              />
              <Label className="text-xs text-muted-foreground">
                {mapping.show_icon ? 'Visible' : 'Hidden'}
              </Label>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label className="text-xs font-medium">Description</Label>
            <Input
              value={localDescription || ''}
              onChange={(e) => handleDescriptionChange(e.target.value)}
              placeholder="Description..."
              disabled={previewMode}
              className="text-sm"
            />
          </div>

          {/* Preview */}
          <div className="space-y-2">
            <Label className="text-xs font-medium">Preview</Label>
            <div
              className="p-3 rounded-lg border-2 border-dashed"
              style={{
                background: `linear-gradient(135deg, ${mapping.color_primary}15, ${mapping.color_secondary}10)`,
                borderColor: `${mapping.color_primary}30`
              }}
            >
              <div className="flex items-center gap-2">
                {mapping.show_icon && (
                  <span className="text-lg">{localEmoji}</span>
                )}
                <div>
                  <div className="font-medium text-sm" style={{ color: mapping.color_primary }}>
                    Sample Content
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {mapping.category} • {mapping.content_type}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

// ColorInput Component
interface ColorInputProps {
  label: string;
  value: string;
  onChange: (color: string) => void;
  disabled?: boolean;
}

const ColorInput: React.FC<ColorInputProps> = ({ label, value, onChange, disabled }) => {
  return (
    <div className="space-y-1">
      <Label className="text-xs text-muted-foreground">{label}</Label>
      <div className="relative">
        <input
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          className="w-full h-8 rounded border border-input cursor-pointer disabled:cursor-not-allowed disabled:opacity-50"
        />
        <div
          className="absolute inset-0 rounded border-2 border-white shadow-sm pointer-events-none"
          style={{ backgroundColor: value }}
        />
      </div>
      <div className="text-xs font-mono text-muted-foreground text-center">
        {value.toUpperCase()}
      </div>
    </div>
  );
};

export default IconEmojiManager;
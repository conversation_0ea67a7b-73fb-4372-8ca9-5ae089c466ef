# Test Automation CI/CD Pipeline
# 
# Comprehensive automated testing workflow with deployment gates
# and quality assurance for the Festival Family application.

name: Test Automation Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run daily at 2 AM UTC for continuous monitoring
    - cron: '0 2 * * *'

env:
  NODE_VERSION: '18'
  CI: true
  HEADLESS: true

jobs:
  # Phase 1: Code Quality and Type Safety
  code-quality:
    name: Code Quality & Type Safety
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: TypeScript type checking
        run: npm run type-check
        
      - name: ESLint code quality
        run: npm run lint
        
      - name: Upload type check results
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: type-check-results
          path: |
            *.log
            typescript-errors.txt

  # Phase 2: Unit and Integration Tests
  unit-tests:
    name: Unit & Integration Tests
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run unit tests with coverage
        run: npm run test:unit:coverage
        
      - name: Generate coverage report
        run: node scripts/coverage-reporter.js
        
      - name: Upload coverage reports
        uses: actions/upload-artifact@v4
        with:
          name: coverage-reports
          path: |
            coverage/
            coverage-reports/
            
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: unit-test-results
          path: |
            test-results/
            junit.xml

  # Phase 3: End-to-End Browser Tests
  e2e-tests:
    name: E2E Browser Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
        
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install Playwright browsers
        run: npx playwright install ${{ matrix.browser }} --with-deps
        
      - name: Run E2E tests
        run: npx playwright test --project=${{ matrix.browser }}
        
      - name: Upload E2E test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-results-${{ matrix.browser }}
          path: |
            test-results/
            playwright-report/

  # Phase 4: Cross-Browser Compatibility
  cross-browser:
    name: Cross-Browser Compatibility
    runs-on: ubuntu-latest
    needs: e2e-tests
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install all Playwright browsers
        run: npx playwright install --with-deps
        
      - name: Run cross-browser tests
        run: npm run test:cross-browser
        
      - name: Run mobile tests
        run: npm run test:mobile
        
      - name: Upload cross-browser results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: cross-browser-results
          path: |
            test-results/
            playwright-report/

  # Phase 5: Production Readiness Assessment
  production-readiness:
    name: Production Readiness Assessment
    runs-on: ubuntu-latest
    needs: [code-quality, unit-tests, e2e-tests, cross-browser]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install Playwright browsers
        run: npx playwright install --with-deps
        
      - name: Run production readiness assessment
        run: npm run test:production-readiness
        
      - name: Upload production readiness report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: production-readiness-report
          path: |
            production-readiness-evidence/
            coverage-reports/
            test-automation-reports/

  # Phase 6: Build Verification
  build-verification:
    name: Build Verification
    runs-on: ubuntu-latest
    needs: production-readiness
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build application
        run: npm run build
        
      - name: Verify build output
        run: |
          ls -la dist/
          du -sh dist/
          
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: dist/

  # Phase 7: Deployment Gate
  deployment-gate:
    name: Deployment Gate
    runs-on: ubuntu-latest
    needs: [production-readiness, build-verification]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Download production readiness report
        uses: actions/download-artifact@v4
        with:
          name: production-readiness-report
          
      - name: Download coverage reports
        uses: actions/download-artifact@v4
        with:
          name: coverage-reports
          
      - name: Evaluate deployment readiness
        run: |
          echo "🚀 Evaluating deployment readiness..."
          
          # Check if production readiness report exists and indicates success
          if [ -f "production-readiness-evidence/production-readiness-report.json" ]; then
            OVERALL_STATUS=$(cat production-readiness-evidence/production-readiness-report.json | jq -r '.assessment.overallStatus')
            SUCCESS_RATE=$(cat production-readiness-evidence/production-readiness-report.json | jq -r '.assessment.successRate')
            
            echo "Overall Status: $OVERALL_STATUS"
            echo "Success Rate: $SUCCESS_RATE"
            
            if [ "$OVERALL_STATUS" = "READY" ]; then
              echo "✅ Application is ready for deployment!"
              echo "deployment-ready=true" >> $GITHUB_OUTPUT
            else
              echo "❌ Application is not ready for deployment"
              echo "deployment-ready=false" >> $GITHUB_OUTPUT
              exit 1
            fi
          else
            echo "❌ Production readiness report not found"
            exit 1
          fi
        id: gate
        
      - name: Create deployment summary
        if: success()
        run: |
          echo "## 🚀 Deployment Gate Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "✅ **Status**: Ready for deployment" >> $GITHUB_STEP_SUMMARY
          echo "📊 **Success Rate**: $(cat production-readiness-evidence/production-readiness-report.json | jq -r '.assessment.successRate')" >> $GITHUB_STEP_SUMMARY
          echo "🧪 **Tests Passed**: $(cat production-readiness-evidence/production-readiness-report.json | jq -r '.assessment.passedTests')/$(cat production-readiness-evidence/production-readiness-report.json | jq -r '.assessment.totalTests')" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Next Steps" >> $GITHUB_STEP_SUMMARY
          echo "- Application is ready for production deployment" >> $GITHUB_STEP_SUMMARY
          echo "- All quality gates have been passed" >> $GITHUB_STEP_SUMMARY
          echo "- Deployment can proceed with confidence" >> $GITHUB_STEP_SUMMARY

  # Phase 8: Notification and Reporting
  notification:
    name: Test Results Notification
    runs-on: ubuntu-latest
    needs: [deployment-gate]
    if: always()
    
    steps:
      - name: Notify test results
        run: |
          echo "📧 Test automation pipeline completed"
          echo "Status: ${{ needs.deployment-gate.result }}"
          
          if [ "${{ needs.deployment-gate.result }}" = "success" ]; then
            echo "✅ All tests passed - Ready for deployment"
          else
            echo "❌ Some tests failed - Deployment blocked"
          fi
          
          # Additional notification logic can be added here
          # (Slack, Discord, Email, etc.)

# Workflow-level settings
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

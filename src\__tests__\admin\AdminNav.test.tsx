/**
 * AdminNav Component Tests
 * 
 * Tests for the admin navigation component including role-based access and navigation items.
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { MemoryRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import AdminNav from '../../components/admin/AdminNav'
import { UserRole } from '../../types/core'

// Mock the auth provider
const mockAuthContext = {
  session: null,
  user: null,
  profile: null,
  loading: false,
  isAdmin: true,
  signIn: jest.fn(),
  signUp: jest.fn(),
  signOut: jest.fn(),
  refreshProfile: jest.fn(),
}

jest.mock('../../providers/ConsolidatedAuthProvider', () => ({
  useAuth: () => mockAuthContext,
}))

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <MemoryRouter>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </MemoryRouter>
  )
}

describe('AdminNav', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('should render all admin navigation items', () => {
    render(
      <TestWrapper>
        <AdminNav />
      </TestWrapper>
    )

    // Check for main admin navigation items
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Festivals')).toBeInTheDocument()
    expect(screen.getByText('Events')).toBeInTheDocument()
    expect(screen.getByText('Users')).toBeInTheDocument()
    expect(screen.getByText('Activities')).toBeInTheDocument()
    expect(screen.getByText('External Links')).toBeInTheDocument()
    expect(screen.getByText('Announcements')).toBeInTheDocument()
    expect(screen.getByText('FAQs')).toBeInTheDocument()
    expect(screen.getByText('Guides')).toBeInTheDocument()
    expect(screen.getByText('Tips')).toBeInTheDocument()
  })

  test('should render navigation links with correct hrefs', () => {
    render(
      <TestWrapper>
        <AdminNav />
      </TestWrapper>
    )

    // Check that links have correct href attributes
    const dashboardLink = screen.getByRole('link', { name: /dashboard/i })
    const festivalsLink = screen.getByRole('link', { name: /festivals/i })
    const usersLink = screen.getByRole('link', { name: /users/i })

    expect(dashboardLink).toHaveAttribute('href', '/admin')
    expect(festivalsLink).toHaveAttribute('href', '/admin/festivals')
    expect(usersLink).toHaveAttribute('href', '/admin/users')
  })

  test('should render with proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <AdminNav />
      </TestWrapper>
    )

    // Check for navigation landmark
    const nav = screen.getByRole('navigation')
    expect(nav).toBeInTheDocument()

    // Check that all links are accessible
    const links = screen.getAllByRole('link')
    expect(links.length).toBeGreaterThan(0)
    
    links.forEach(link => {
      expect(link).toBeVisible()
      expect(link).toHaveAttribute('href')
    })
  })

  test('should display icons for navigation items', () => {
    render(
      <TestWrapper>
        <AdminNav />
      </TestWrapper>
    )

    // Check that SVG icons are present (Lucide icons render as SVG)
    const svgIcons = document.querySelectorAll('svg')
    expect(svgIcons.length).toBeGreaterThan(0)
  })

  test('should handle active state for current route', () => {
    // Test with specific route
    render(
      <MemoryRouter initialEntries={['/admin/festivals']}>
        <QueryClientProvider client={new QueryClient()}>
          <AdminNav />
        </QueryClientProvider>
      </MemoryRouter>
    )

    // The component should handle active states (implementation may vary)
    const festivalsLink = screen.getByRole('link', { name: /festivals/i })
    expect(festivalsLink).toBeInTheDocument()
  })
})

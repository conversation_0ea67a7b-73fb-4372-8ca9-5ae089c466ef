/**
 * Performance Optimization Utilities
 * 
 * Comprehensive performance optimization utilities for mobile-first applications
 * including lazy loading, bundle optimization, memory management, and Core Web Vitals.
 */

import { useEffect, useRef, useCallback, useState } from 'react';

// Performance monitoring
export interface PerformanceMetrics {
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  ttfb: number; // Time to First Byte
}

// Lazy loading utilities
export const useLazyLoading = (threshold = 0.1) => {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold]);

  return { ref, isVisible };
};

// Image lazy loading with progressive enhancement
export const useLazyImage = (src: string, placeholder?: string) => {
  const [imageSrc, setImageSrc] = useState(placeholder || '');
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const { ref, isVisible } = useLazyLoading();

  useEffect(() => {
    if (isVisible && src) {
      const img = new Image();
      img.onload = () => {
        setImageSrc(src);
        setIsLoaded(true);
      };
      img.onerror = () => {
        setIsError(true);
      };
      img.src = src;
    }
  }, [isVisible, src]);

  return { ref, imageSrc, isLoaded, isError };
};

// Memory management utilities
export const useMemoryOptimization = () => {
  const cleanupFunctions = useRef<(() => void)[]>([]);

  const addCleanup = useCallback((cleanup: () => void) => {
    cleanupFunctions.current.push(cleanup);
  }, []);

  useEffect(() => {
    return () => {
      cleanupFunctions.current.forEach(cleanup => cleanup());
      cleanupFunctions.current = [];
    };
  }, []);

  return { addCleanup };
};

// Bundle size optimization
export const dynamicImport = async <T>(
  importFn: () => Promise<{ default: T }>,
  fallback?: T
): Promise<T> => {
  try {
    const module = await importFn();
    return module.default;
  } catch (error) {
    console.warn('Dynamic import failed:', error);
    if (fallback) {
      return fallback;
    }
    throw error;
  }
};

// Performance monitoring
export const measurePerformance = (): Promise<PerformanceMetrics> => {
  return new Promise((resolve) => {
    const metrics: Partial<PerformanceMetrics> = {};

    // Measure Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        switch (entry.entryType) {
          case 'paint':
            if (entry.name === 'first-contentful-paint') {
              metrics.fcp = entry.startTime;
            }
            break;
          case 'largest-contentful-paint':
            metrics.lcp = entry.startTime;
            break;
          case 'first-input':
            metrics.fid = (entry as any).processingStart - entry.startTime;
            break;
          case 'layout-shift':
            if (!(entry as any).hadRecentInput) {
              metrics.cls = (metrics.cls || 0) + (entry as any).value;
            }
            break;
          case 'navigation':
            metrics.ttfb = (entry as any).responseStart;
            break;
        }
      }

      // Check if all metrics are collected
      if (metrics.fcp && metrics.lcp && metrics.fid !== undefined && 
          metrics.cls !== undefined && metrics.ttfb) {
        observer.disconnect();
        resolve(metrics as PerformanceMetrics);
      }
    });

    observer.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'first-input', 'layout-shift', 'navigation'] });

    // Fallback timeout
    setTimeout(() => {
      observer.disconnect();
      resolve({
        fcp: metrics.fcp || 0,
        lcp: metrics.lcp || 0,
        fid: metrics.fid || 0,
        cls: metrics.cls || 0,
        ttfb: metrics.ttfb || 0
      });
    }, 10000);
  });
};

// Animation performance optimization
export const useOptimizedAnimation = (enabled = true) => {
  const [shouldAnimate, setShouldAnimate] = useState(enabled);

  useEffect(() => {
    // Check for reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const handleChange = () => {
      setShouldAnimate(enabled && !mediaQuery.matches);
    };

    handleChange();
    mediaQuery.addEventListener('change', handleChange);

    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [enabled]);

  // Check device performance
  useEffect(() => {
    if ('deviceMemory' in navigator) {
      const deviceMemory = (navigator as any).deviceMemory;
      if (deviceMemory < 4) {
        setShouldAnimate(false);
      }
    }

    // Check for slow connection
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
        setShouldAnimate(false);
      }
    }
  }, []);

  return shouldAnimate;
};

// Resource preloading
export const preloadResource = (href: string, as: string, type?: string) => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = as;
  if (type) link.type = type;
  document.head.appendChild(link);
};

// Critical resource loading
export const loadCriticalResources = () => {
  // Preload critical fonts
  preloadResource('/fonts/inter-var.woff2', 'font', 'font/woff2');
  
  // Preload critical images
  preloadResource('/images/hero-mobile.webp', 'image');
  
  // Preload critical CSS
  preloadResource('/css/critical.css', 'style');
};

// Performance budget monitoring
export const checkPerformanceBudget = async (): Promise<{
  passed: boolean;
  metrics: PerformanceMetrics;
  violations: string[];
}> => {
  const metrics = await measurePerformance();
  const violations: string[] = [];

  // Performance budget thresholds
  const budgets = {
    fcp: 1800, // 1.8s
    lcp: 2500, // 2.5s
    fid: 100,  // 100ms
    cls: 0.1,  // 0.1
    ttfb: 800  // 800ms
  };

  // Check violations
  if (metrics.fcp > budgets.fcp) violations.push(`FCP: ${metrics.fcp}ms > ${budgets.fcp}ms`);
  if (metrics.lcp > budgets.lcp) violations.push(`LCP: ${metrics.lcp}ms > ${budgets.lcp}ms`);
  if (metrics.fid > budgets.fid) violations.push(`FID: ${metrics.fid}ms > ${budgets.fid}ms`);
  if (metrics.cls > budgets.cls) violations.push(`CLS: ${metrics.cls} > ${budgets.cls}`);
  if (metrics.ttfb > budgets.ttfb) violations.push(`TTFB: ${metrics.ttfb}ms > ${budgets.ttfb}ms`);

  return {
    passed: violations.length === 0,
    metrics,
    violations
  };
};

// Mobile-specific optimizations
export const optimizeForMobile = () => {
  // Disable hover effects on touch devices
  if ('ontouchstart' in window) {
    document.body.classList.add('touch-device');
  }

  // Optimize viewport for mobile
  const viewport = document.querySelector('meta[name="viewport"]');
  if (!viewport) {
    const meta = document.createElement('meta');
    meta.name = 'viewport';
    meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes';
    document.head.appendChild(meta);
  }

  // Add performance hints
  const dnsPreconnect = document.createElement('link');
  dnsPreconnect.rel = 'dns-prefetch';
  dnsPreconnect.href = '//fonts.googleapis.com';
  document.head.appendChild(dnsPreconnect);
};

// Initialize performance optimizations
export const initializePerformanceOptimizations = () => {
  optimizeForMobile();
  loadCriticalResources();

  // Monitor performance in development
  if (process.env.NODE_ENV === 'development') {
    setTimeout(async () => {
      const budget = await checkPerformanceBudget();
      if (!budget.passed) {
        console.warn('Performance budget violations:', budget.violations);
      }
      console.log('Performance metrics:', budget.metrics);
    }, 5000);
  }
};

// Export performance utilities
export default {
  useLazyLoading,
  useLazyImage,
  useMemoryOptimization,
  useOptimizedAnimation,
  dynamicImport,
  measurePerformance,
  checkPerformanceBudget,
  preloadResource,
  loadCriticalResources,
  optimizeForMobile,
  initializePerformanceOptimizations
};

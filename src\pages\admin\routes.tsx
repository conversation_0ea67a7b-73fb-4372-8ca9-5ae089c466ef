/**
 * Admin Routes Configuration
 * 
 * Defines all admin-related routes and their access permissions.
 * Uses role-based access control for security.
 */

import { lazy, Suspense } from 'react'
import { Navigate, RouteObject } from 'react-router-dom'
import AdminLayout from '../../components/layout/AdminLayout'
import ProtectedRoute from '../../components/auth/AdminRoute'
import { type Permission } from '@/types/core'

// Lazy load admin components
const AdminDashboard = lazy(() => import('./Dashboard'))
const Analytics = lazy(() => import('./Analytics'))
const ActivityForm = lazy(() => import('./ActivityForm'))
const FestivalForm = lazy(() => import('./FestivalForm'))
const Festivals = lazy(() => import('./Festivals'))
const Events = lazy(() => import('./Events'))
const EventForm = lazy(() => import('./EventForm'))
const Activities = lazy(() => import('./Activities'))
const Users = lazy(() => import('./Users'))
const FAQs = lazy(() => import('./FAQ'))
const FAQForm = lazy(() => import('./FAQForm'))
const ContentManagement = lazy(() => import('./ContentManagement'))
const EmergencyManagement = lazy(() => import('./EmergencyManagement'))
const Guides = lazy(() => import('./Guides'))
const GuideForm = lazy(() => import('./GuideForm'))
const Tips = lazy(() => import('./Tips'))
const TipForm = lazy(() => import('./TipForm'))
const Announcements = lazy(() => import('./Announcements'))
const ExternalLinks = lazy(() => import('./ExternalLinks'))
const IconEmojiManagement = lazy(() => import('./IconEmojiManagement'))
const LocalInfoManagement = lazy(() => import('./LocalInfoManagement'))

// Loading spinner component for admin routes
const AdminLoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-[200px]">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
)

// Admin route configuration with permissions
export const adminRoutes: RouteObject[] = [
  {
    path: '/admin',
    element: (
      <ProtectedRoute requiredPermission={'access_admin' as Permission}>
        <AdminLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: (
          <Suspense fallback={<AdminLoadingSpinner />}>
            <AdminDashboard />
          </Suspense>
        ),
      },
      // Analytics route
      {
        path: 'analytics',
        element: (
          <Suspense fallback={<AdminLoadingSpinner />}>
            <Analytics />
          </Suspense>
        ),
      },
      // Activities routes
      {
        path: 'activities',
        children: [
          {
            index: true,
            element: (
              <ProtectedRoute requiredPermission={'manage_activities' as Permission}>
                <Suspense fallback={<AdminLoadingSpinner />}>
                  <Activities />
                </Suspense>
              </ProtectedRoute>
            ),
          },
          {
            path: 'new',
            element: (
              <ProtectedRoute requiredPermission={'manage_activities' as Permission}>
                <Suspense fallback={<AdminLoadingSpinner />}>
                  <ActivityForm />
                </Suspense>
              </ProtectedRoute>
            ),
          },
          {
            path: ':id/edit',
            element: (
              <ProtectedRoute requiredPermission={'manage_activities' as Permission}>
                <Suspense fallback={<AdminLoadingSpinner />}>
                  <ActivityForm />
                </Suspense>
              </ProtectedRoute>
            ),
          },
        ],
      },
      // Festival routes
      {
        path: 'festivals',
        children: [
          {
            index: true,
            element: (
              <ProtectedRoute requiredPermission={'manage_festivals' as Permission}>
                <Suspense fallback={<AdminLoadingSpinner />}>
                  <Festivals />
                </Suspense>
              </ProtectedRoute>
            ),
          },
          {
            path: 'new',
            element: (
              <ProtectedRoute requiredPermission={'manage_festivals' as Permission}>
                <Suspense fallback={<AdminLoadingSpinner />}>
                  <FestivalForm />
                </Suspense>
              </ProtectedRoute>
            ),
          },
          {
            path: ':id/edit',
            element: (
              <ProtectedRoute requiredPermission={'manage_festivals' as Permission}>
                <Suspense fallback={<AdminLoadingSpinner />}>
                  <FestivalForm />
                </Suspense>
              </ProtectedRoute>
            ),
          },
        ],
      },
      // Events routes
      {
        path: 'events',
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<AdminLoadingSpinner />}>
                <Events />
              </Suspense>
            ),
          },
          {
            path: 'new',
            element: (
              <Suspense fallback={<AdminLoadingSpinner />}>
                <EventForm />
              </Suspense>
            ),
          },
          {
            path: ':id/edit',
            element: (
              <Suspense fallback={<AdminLoadingSpinner />}>
                <EventForm />
              </Suspense>
            ),
          },
        ],
      },
      // Users route
      {
        path: 'users',
        children: [
          {
            index: true,
            element: (
              <ProtectedRoute requiredPermission={'manage_users' as Permission}>
                <Suspense fallback={<AdminLoadingSpinner />}>
                  <Users />
                </Suspense>
              </ProtectedRoute>
            ),
          },
        ],
      },
      // FAQs route
      {
        path: 'faqs',
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<AdminLoadingSpinner />}>
                <FAQs />
              </Suspense>
            ),
          },
          {
            path: 'new',
            element: (
              <Suspense fallback={<AdminLoadingSpinner />}>
                <FAQForm />
              </Suspense>
            ),
          },
          {
            path: ':id/edit',
            element: (
              <Suspense fallback={<AdminLoadingSpinner />}>
                <FAQForm />
              </Suspense>
            ),
          },
        ],
      },
      // Guides route
      {
        path: 'guides',
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<AdminLoadingSpinner />}>
                <Guides />
              </Suspense>
            ),
          },
          {
            path: 'new',
            element: (
              <Suspense fallback={<AdminLoadingSpinner />}>
                <GuideForm />
              </Suspense>
            ),
          },
          {
            path: ':id/edit',
            element: (
              <Suspense fallback={<AdminLoadingSpinner />}>
                <GuideForm />
              </Suspense>
            ),
          },
        ],
      },
      // Tips route
      {
        path: 'tips',
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<AdminLoadingSpinner />}>
                <Tips />
              </Suspense>
            ),
          },
          {
            path: 'new',
            element: (
              <Suspense fallback={<AdminLoadingSpinner />}>
                <TipForm />
              </Suspense>
            ),
          },
          {
            path: ':id/edit',
            element: (
              <Suspense fallback={<AdminLoadingSpinner />}>
                <TipForm />
              </Suspense>
            ),
          },
        ],
      },
      // Announcements route
      {
        path: 'announcements',
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<AdminLoadingSpinner />}>
                <Announcements />
              </Suspense>
            ),
          },
        ],
      },
      // Content Management route
      {
        path: 'content',
        children: [
          {
            index: true,
            element: (
              <ProtectedRoute requiredPermission={'manage_content' as Permission}>
                <Suspense fallback={<AdminLoadingSpinner />}>
                  <ContentManagement />
                </Suspense>
              </ProtectedRoute>
            ),
          },
        ],
      },
      // Emergency Management route
      {
        path: 'emergency',
        children: [
          {
            index: true,
            element: (
              <ProtectedRoute requiredPermission={'manage_content' as Permission}>
                <Suspense fallback={<AdminLoadingSpinner />}>
                  <EmergencyManagement />
                </Suspense>
              </ProtectedRoute>
            ),
          },
        ],
      },
      // External Links route
      {
        path: 'external-links',
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<AdminLoadingSpinner />}>
                <ExternalLinks />
              </Suspense>
            ),
          },
        ],
      },
      // Local Information Management route
      {
        path: 'local-info',
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<AdminLoadingSpinner />}>
                <LocalInfoManagement />
              </Suspense>
            ),
          },
        ],
      },
      // Icon & Emoji Management route
      {
        path: 'icon-emoji-management',
        children: [
          {
            index: true,
            element: (
              <ProtectedRoute requiredPermission={'manage_content' as Permission}>
                <Suspense fallback={<AdminLoadingSpinner />}>
                  <IconEmojiManagement />
                </Suspense>
              </ProtectedRoute>
            ),
          },
        ],
      },
      // Catch-all redirect
      {
        path: '*',
        element: <Navigate to="/admin" replace />,
      },
    ],
  },
]

import { test, expect } from '@playwright/test';

/**
 * Quick Theme Fix Verification Test
 * 
 * Verifies that the theme fix (defaulting to light mode) resolves
 * the white text on white background issue in admin forms.
 */

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  console.log('🔐 Logging in as admin...');
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  
  // Fill login form
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  
  // Wait for redirect to admin dashboard
  await page.waitForURL('/admin', { timeout: 10000 });
  console.log('✅ Successfully logged in as admin');
}

test.describe('Theme Fix Verification', () => {
  test.beforeEach(async ({ page }) => {
    await loginAsAdmin(page);
  });

  test('Events form should have readable text after theme fix', async ({ page }) => {
    console.log('📝 Testing Events form readability...');
    
    // Navigate to Events form
    await page.goto('/admin/events/new');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Take screenshot for evidence
    await page.screenshot({ 
      path: 'test-results/theme-fix-events.png', 
      fullPage: true 
    });
    
    // Check that the page title is visible (not white text on white background)
    const title = page.locator('h1').first();
    const titleText = await title.textContent();
    console.log(`Found title: "${titleText}"`);
    
    // Check computed styles
    const titleStyles = await title.evaluate(el => {
      const computed = window.getComputedStyle(el);
      return {
        color: computed.color,
        backgroundColor: computed.backgroundColor,
        visibility: computed.visibility
      };
    });
    
    console.log('Title styles:', titleStyles);
    
    // Verify title is not white text on white background
    expect(titleStyles.color).not.toBe('rgb(255, 255, 255)');
    expect(titleStyles.visibility).toBe('visible');
    
    // Check form labels
    const labels = await page.locator('label').all();
    console.log(`Found ${labels.length} form labels`);
    
    for (const label of labels.slice(0, 3)) { // Check first 3 labels
      const labelText = await label.textContent();
      if (!labelText || labelText.trim() === '') continue;
      
      const labelStyles = await label.evaluate(el => {
        const computed = window.getComputedStyle(el);
        return {
          color: computed.color,
          backgroundColor: computed.backgroundColor
        };
      });
      
      console.log(`Label "${labelText}" styles:`, labelStyles);
      
      // Verify label is not white text on white background
      expect(labelStyles.color).not.toBe('rgb(255, 255, 255)');
    }
    
    console.log('✅ Events form has readable text');
  });

  test('Activities form should have readable text after theme fix', async ({ page }) => {
    console.log('📝 Testing Activities form readability...');
    
    // Navigate to Activities form
    await page.goto('/admin/activities/new');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Take screenshot for evidence
    await page.screenshot({ 
      path: 'test-results/theme-fix-activities.png', 
      fullPage: true 
    });
    
    // Check that form inputs are readable
    const inputs = await page.locator('input[type="text"], textarea').all();
    console.log(`Found ${inputs.length} form inputs`);
    
    for (const input of inputs.slice(0, 2)) { // Check first 2 inputs
      const inputStyles = await input.evaluate(el => {
        const computed = window.getComputedStyle(el);
        return {
          color: computed.color,
          backgroundColor: computed.backgroundColor,
          placeholder: el.placeholder || 'No placeholder'
        };
      });
      
      console.log(`Input (${inputStyles.placeholder}) styles:`, inputStyles);
      
      // Verify input text is not white on white
      if (inputStyles.backgroundColor === 'rgb(255, 255, 255)') {
        expect(inputStyles.color).not.toBe('rgb(255, 255, 255)');
      }
    }
    
    console.log('✅ Activities form has readable text');
  });

  test('Theme context should default to light mode', async ({ page }) => {
    console.log('🎨 Testing theme context default...');
    
    // Navigate to any admin page
    await page.goto('/admin/events/new');
    await page.waitForLoadState('networkidle');
    
    // Check if dark class is applied to html element
    const htmlClasses = await page.evaluate(() => {
      return document.documentElement.className;
    });
    
    console.log('HTML classes:', htmlClasses);
    
    // Should NOT have 'dark' class by default now
    expect(htmlClasses).not.toContain('dark');
    
    // Check CSS variables
    const cssVariables = await page.evaluate(() => {
      const styles = getComputedStyle(document.documentElement);
      return {
        background: styles.getPropertyValue('--background').trim(),
        foreground: styles.getPropertyValue('--foreground').trim()
      };
    });
    
    console.log('CSS variables:', cssVariables);
    
    // Light mode should have dark foreground text
    expect(cssVariables.foreground).toBe('30 10% 11%'); // Dark text for light mode
    expect(cssVariables.background).toBe('0 0% 100%'); // White background for light mode
    
    console.log('✅ Theme defaults to light mode correctly');
  });
});

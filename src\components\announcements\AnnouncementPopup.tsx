import React from 'react';
import { Megaphone, AlertTriangle, Info, CheckCircle, Tag, Star } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { UnifiedModal } from '@/components/design-system/UnifiedModal';
import { type Announcement } from '@/types/announcements';
import { useEnhancedColorMapping } from '@/hooks/useEnhancedColorMapping';

export interface AnnouncementPopupProps {
  announcement: Announcement;
  isOpen: boolean;
  onClose: () => void;
  onDismiss: (id: string) => void;
}

const AnnouncementPopup: React.FC<AnnouncementPopupProps> = ({ announcement, isOpen, onClose, onDismiss }) => {
  // Use enhanced color mapping for database-driven colors
  const { colorMapping } = useEnhancedColorMapping('announcement', announcement?.priority ?? 'medium');

  // Generate gradient classes from color mapping
  const getGradientClasses = () => {
    if (!colorMapping) {
      // Fallback based on priority
      switch (announcement?.priority) {
        case 'HIGH': return 'from-destructive to-destructive';
        case 'MEDIUM': return 'from-accent to-accent';
        case 'LOW': return 'from-primary to-primary';
        default: return 'from-muted to-muted-foreground';
      }
    }
    return `from-[${colorMapping.color_primary}/80] to-[${colorMapping.color_secondary}/80]`;
  };

  if (!announcement) return null;

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return AlertTriangle;
      case 'medium': return Info;
      case 'low': return CheckCircle;
      default: return Megaphone;
    }
  };

  const colorClass = getGradientClasses(); // Use database-driven gradient
  const IconComponent = getPriorityIcon(announcement.priority || 'medium');

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={onClose}
      size="xl"
      showCloseButton={false}
      className="bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900"
    >
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center gap-3 p-6 border-b border-border">
          <div className={`w-10 h-10 bg-gradient-to-br ${colorClass} rounded-lg flex items-center justify-center`}>
            <IconComponent className="w-5 h-5 text-primary-foreground" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-foreground">{announcement.title}</h2>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="secondary" className="capitalize">
                {announcement.priority || 'medium'} Priority
              </Badge>
              {announcement.display_type && (
                <Badge variant="outline" className="text-white/70 border-white/20">
                  {announcement.display_type}
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
              <div className="prose prose-invert max-w-none">
                <p className="text-white/90 leading-relaxed whitespace-pre-wrap">{announcement.content}</p>
              </div>

              {/* Tags */}
              {announcement.metadata?.tags && Array.isArray(announcement.metadata.tags) && announcement.metadata.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {announcement.metadata.tags.map((tag: string, index: number) => (
                    <Badge key={`tag-${tag}-${index}`} variant="outline" className="text-white/70 border-white/20">
                      <Tag className="w-3 h-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}

              {/* Meta Info */}
              <div className="flex items-center gap-4 text-sm text-white/60">
                <span>
                  Posted {new Date(announcement.created_at).toLocaleDateString()}
                </span>
                {announcement.target_audience && (
                  <span>
                    Target: {Array.isArray(announcement.target_audience)
                      ? announcement.target_audience.join(', ')
                      : announcement.target_audience}
                  </span>
                )}
              </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-white/10">
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDismiss(announcement.id)}
              >
                Dismiss
              </Button>
              <Button
                variant="outline"
                onClick={onClose}
              >
                Close
              </Button>
            </div>
            {announcement.is_featured && (
              <Badge variant="outline" className="bg-accent/20 text-accent border-accent/30">
                <Star className="w-3 h-3 mr-1" />
                Featured
              </Badge>
            )}
          </div>
        </div>
      </div>
    </ResponsiveModal>
  );
};

export default AnnouncementPopup;

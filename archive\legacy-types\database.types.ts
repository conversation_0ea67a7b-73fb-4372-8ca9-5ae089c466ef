export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      activities: {
        Row: {
          capacity: number | null
          created_at: string | null
          description: string | null
          end_time: string | null
          festival_id: string | null
          id: string
          location: string | null
          name: string
          parent_activity_id: string | null
          start_time: string | null
          type: Database["public"]["Enums"]["activity_type"]
          updated_at: string | null
        }
        Insert: {
          capacity?: number | null
          created_at?: string | null
          description?: string | null
          end_time?: string | null
          festival_id?: string | null
          id?: string
          location?: string | null
          name: string
          parent_activity_id?: string | null
          start_time?: string | null
          type: Database["public"]["Enums"]["activity_type"]
          updated_at?: string | null
        }
        Update: {
          capacity?: number | null
          created_at?: string | null
          description?: string | null
          end_time?: string | null
          festival_id?: string | null
          id?: string
          location?: string | null
          name?: string
          parent_activity_id?: string | null
          start_time?: string | null
          type?: Database["public"]["Enums"]["activity_type"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "activities_festival_id_fkey"
            columns: ["festival_id"]
            isOneToOne: false
            referencedRelation: "festivals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activities_parent_activity_id_fkey"
            columns: ["parent_activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
        ]
      }
      announcements: {
        Row: {
          content: string
          created_at: string
          end_date: string | null
          id: string
          is_active: boolean
          start_date: string | null
          target_audience: string | null
          title: string
          type: Database["public"]["Enums"]["announcement_type"]
          updated_at: string
          user_id: string | null
        }
        Insert: {
          content: string
          created_at?: string
          end_date?: string | null
          id?: string
          is_active?: boolean
          start_date?: string | null
          target_audience?: string | null
          title: string
          type?: Database["public"]["Enums"]["announcement_type"]
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          content?: string
          created_at?: string
          end_date?: string | null
          id?: string
          is_active?: boolean
          start_date?: string | null
          target_audience?: string | null
          title?: string
          type?: Database["public"]["Enums"]["announcement_type"]
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "announcements_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_logs: {
        Row: {
          action: string
          created_at: string
          details: Json | null
          id: number
          target_id: string | null
          target_type: string | null
          user_id: string
        }
        Insert: {
          action: string
          created_at?: string
          details?: Json | null
          id?: number
          target_id?: string | null
          target_type?: string | null
          user_id: string
        }
        Update: {
          action?: string
          created_at?: string
          details?: Json | null
          id?: number
          target_id?: string | null
          target_type?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "audit_logs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_messages: {
        Row: {
          content: string
          created_at: string
          id: string
          room_id: string
          sender_id: string
          updated_at: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          room_id: string
          sender_id: string
          updated_at?: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          room_id?: string
          sender_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "chat_messages_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "chat_rooms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_room_participants: {
        Row: {
          created_at: string
          id: string
          joined_at: string | null
          left_at: string | null
          room_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          joined_at?: string | null
          left_at?: string | null
          room_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          joined_at?: string | null
          left_at?: string | null
          room_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "chat_room_participants_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "chat_rooms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_room_participants_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_rooms: {
        Row: {
          created_at: string
          created_by: string | null
          description: string | null
          id: string
          is_group_chat: boolean
          last_message_at: string | null
          name: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          is_group_chat?: boolean
          last_message_at?: string | null
          name?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          is_group_chat?: boolean
          last_message_at?: string | null
          name?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "chat_rooms_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      emergency_contacts: {
        Row: {
          contact_info: string
          created_at: string
          id: string
          name: string
          relationship: string | null
          user_id: string
        }
        Insert: {
          contact_info: string
          created_at?: string
          id?: string
          name: string
          relationship?: string | null
          user_id: string
        }
        Update: {
          contact_info?: string
          created_at?: string
          id?: string
          name?: string
          relationship?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "emergency_contacts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      events: {
        Row: {
          created_at: string
          description: string | null
          end_date: string
          festival_id: string
          id: string
          image_url: string | null
          is_active: boolean | null
          location: string | null
          title: string
          start_date: string
          status: Database["public"]["Enums"]["event_status"]
          updated_at: string
          created_by: string | null
          capacity: number | null
          registration_required: boolean | null
        }
        Insert: {
          created_at?: string
          description?: string | null
          end_date: string
          festival_id: string
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          location?: string | null
          title: string
          start_date: string
          status?: Database["public"]["Enums"]["event_status"]
          updated_at?: string
          created_by?: string | null
          capacity?: number | null
          registration_required?: boolean | null
        }
        Update: {
          created_at?: string
          description?: string | null
          end_date?: string
          festival_id?: string
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          location?: string | null
          title?: string
          start_date?: string
          status?: Database["public"]["Enums"]["event_status"]
          updated_at?: string
          created_by?: string | null
          capacity?: number | null
          registration_required?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "events_festival_id_fkey"
            columns: ["festival_id"]
            isOneToOne: false
            referencedRelation: "festivals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "events_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      external_links: {
        Row: {
          created_at: string
          description: string | null
          id: string
          title: string
          type: Database["public"]["Enums"]["link_type"]
          updated_at: string
          url: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          title: string
          type: Database["public"]["Enums"]["link_type"]
          updated_at?: string
          url: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          title?: string
          type?: Database["public"]["Enums"]["link_type"]
          updated_at?: string
          url?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "external_links_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      faqs: {
        Row: {
          answer: string
          created_at: string
          id: string
          is_published: boolean
          question: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          answer: string
          created_at?: string
          id?: string
          is_published?: boolean
          question: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          answer?: string
          created_at?: string
          id?: string
          is_published?: boolean
          question?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "faqs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      festival_admins: {
        Row: {
          created_at: string
          festival_id: string
          id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          festival_id: string
          id?: string
          user_id: string
        }
        Update: {
          created_at?: string
          festival_id?: string
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "festival_admins_festival_id_fkey"
            columns: ["festival_id"]
            isOneToOne: false
            referencedRelation: "festivals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "festival_admins_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      festivals: {
        Row: {
          created_at: string
          description: string | null
          end_date: string
          id: string
          image_url: string | null
          is_featured: boolean
          location: string | null
          name: string
          start_date: string
          status: Database["public"]["Enums"]["festival_status"]
          updated_at: string
          website_url: string | null
        }
        Insert: {
          created_at?: string
          description?: string | null
          end_date: string
          id?: string
          image_url?: string | null
          is_featured?: boolean
          location?: string | null
          name: string
          start_date: string
          status?: Database["public"]["Enums"]["festival_status"]
          updated_at?: string
          website_url?: string | null
        }
        Update: {
          created_at?: string
          description?: string | null
          end_date?: string
          id?: string
          image_url?: string | null
          is_featured?: boolean
          location?: string | null
          name?: string
          start_date?: string
          status?: Database["public"]["Enums"]["festival_status"]
          updated_at?: string
          website_url?: string | null
        }
        Relationships: []
      }
      guides: {
        Row: {
          content: string
          created_at: string
          id: string
          is_published: boolean
          title: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          is_published?: boolean
          title: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          is_published?: boolean
          title?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "guides_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      local_info: {
        Row: {
          content: string
          created_at: string
          id: string
          info_type: Database["public"]["Enums"]["local_info_type"]
          is_published: boolean
          title: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          info_type: Database["public"]["Enums"]["local_info_type"]
          is_published?: boolean
          title: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          info_type?: Database["public"]["Enums"]["local_info_type"]
          is_published?: boolean
          title?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "local_info_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      meetups: {
        Row: {
          activity_id: string
          created_at: string | null
          description: string | null
          id: string
          location: string | null
          meetup_time: string | null
          organizer_id: string | null
          topic: string | null
          updated_at: string | null
        }
        Insert: {
          activity_id: string
          created_at?: string | null
          description?: string | null
          id?: string
          location?: string | null
          meetup_time?: string | null
          organizer_id?: string | null
          topic?: string | null
          updated_at?: string | null
        }
        Update: {
          activity_id?: string
          created_at?: string | null
          description?: string | null
          id?: string
          location?: string | null
          meetup_time?: string | null
          organizer_id?: string | null
          topic?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "meetups_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: true
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "meetups_organizer_id_fkey"
            columns: ["organizer_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          bio: string | null
          created_at: string
          full_name: string | null
          id: string
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string
          full_name?: string | null
          id: string
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string
          full_name?: string | null
          id?: string
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string
          username?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      resources: {
        Row: {
          category: string | null
          content: string
          created_at: string
          id: string
          is_published: boolean
          resource_type: Database["public"]["Enums"]["resource_type"]
          title: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          category?: string | null
          content: string
          created_at?: string
          id?: string
          is_published?: boolean
          resource_type: Database["public"]["Enums"]["resource_type"]
          title: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          category?: string | null
          content?: string
          created_at?: string
          id?: string
          is_published?: boolean
          resource_type?: Database["public"]["Enums"]["resource_type"]
          title?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "resources_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      tips: {
        Row: {
          category: string | null
          content: string
          created_at: string
          id: string
          is_published: boolean
          title: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          category?: string | null
          content: string
          created_at?: string
          id?: string
          is_published?: boolean
          title: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          category?: string | null
          content?: string
          created_at?: string
          id?: string
          is_published?: boolean
          title?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tips_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_dismissed_announcements: {
        Row: {
          announcement_id: string
          dismissed_at: string
          id: string
          user_id: string
        }
        Insert: {
          announcement_id: string
          dismissed_at?: string
          id?: string
          user_id: string
        }
        Update: {
          announcement_id?: string
          dismissed_at?: string
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_dismissed_announcements_announcement_id_fkey"
            columns: ["announcement_id"]
            isOneToOne: false
            referencedRelation: "announcements"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_dismissed_announcements_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      workshops: {
        Row: {
          activity_id: string
          created_at: string | null
          description: string | null
          id: string
          instructor: string | null
          materials_needed: string | null
          skill_level: string | null
          topic: string | null
          updated_at: string | null
        }
        Insert: {
          activity_id: string
          created_at?: string | null
          description?: string | null
          id?: string
          instructor?: string | null
          materials_needed?: string | null
          skill_level?: string | null
          topic?: string | null
          updated_at?: string | null
        }
        Update: {
          activity_id?: string
          created_at?: string | null
          description?: string | null
          id?: string
          instructor?: string | null
          materials_needed?: string | null
          skill_level?: string | null
          topic?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "workshops_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: true
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      activity_type: "workshop" | "meetup" | "performance" | "other"
      announcement_type: "general" | "emergency" | "update" | "event"
      event_status: "planned" | "ongoing" | "completed" | "cancelled"
      festival_status: "upcoming" | "active" | "past" | "cancelled"
      link_type: "official" | "social" | "community" | "resource" | "other"
      local_info_type: "transport" | "accommodation" | "food" | "safety" | "other"
      resource_type: "guide" | "map" | "link" | "document" | "other"
      user_role: "admin" | "moderator" | "user"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T];

// Add any custom types or interfaces needed for your application below

export interface Festival extends Tables<'festivals'> {}
export interface Activity extends Tables<'activities'> {
  workshop?: Tables<'workshops'> | null;
  meetup?: Tables<'meetups'> | null;
}

// Activity Coordination Types
export interface ActivityAttendance {
  id: string;
  user_id: string;
  activity_id: string;
  status: 'going' | 'interested' | 'maybe' | 'not_going';
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface ArtistPreference {
  id: string;
  user_id: string;
  artist_name: string;
  preference_level: 'love' | 'like' | 'neutral' | 'dislike';
  genre?: string;
  spotify_artist_id?: string;
  created_at: string;
  updated_at: string;
}

export interface MusicGenrePreference {
  id: string;
  user_id: string;
  genre: string;
  preference_level: 'love' | 'like' | 'neutral' | 'dislike';
  created_at: string;
  updated_at: string;
}

// Group types (since groups table may not be in generated types yet)
export interface Group {
  id: string;
  name: string;
  description?: string;
  creator_id: string;
  festival_id?: string;
  is_private: boolean;
  formation_type?: 'manual' | 'activity_based' | 'music_based' | 'hybrid' | 'spontaneous';
  max_members?: number;
  auto_accept_threshold?: number;
  activity_focus?: string[];
  music_focus?: string[];
  tags?: string[];
  is_active?: boolean;
  expires_at?: string;
  created_at: string;
  updated_at: string;
}

export interface GroupMember {
  id: string;
  group_id: string;
  user_id: string;
  role: 'admin' | 'moderator' | 'member';
  joined_at: string;
  left_at?: string;
}

export interface GroupExternalLink {
  id: string;
  group_id: string;
  platform: 'whatsapp' | 'discord' | 'telegram' | 'signal' | 'other';
  link_url: string;
  display_name: string;
  description?: string;
  created_by: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Smart Group Formation table types
export interface GroupSuggestion {
  id: string;
  suggested_name: string;
  suggested_description?: string;
  formation_type: 'manual' | 'activity_based' | 'music_based' | 'hybrid' | 'spontaneous';
  festival_id?: string;
  activity_focus?: string[];
  music_focus?: string[];
  target_users: string[];
  creator_id: string;
  min_members: number;
  max_members: number;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  confidence_score: number;
  expires_at: string;
  created_at: string;
  updated_at: string;
}

export interface GroupSuggestionResponse {
  id: string;
  suggestion_id: string;
  user_id: string;
  response: boolean;
  response_notes?: string;
  responded_at: string;
  formed_group_id?: string;
}

export interface GroupActivity {
  id: string;
  group_id: string;
  activity_id: string;
  added_by: string;
  is_primary: boolean;
  created_at: string;
}
export interface Workshop extends Tables<'workshops'> {}
export interface Meetup extends Tables<'meetups'> {}
export interface Event extends Tables<'events'> {}
export interface Profile extends Tables<'profiles'> {}
export interface Announcement extends Tables<'announcements'> {}
export interface Resource extends Tables<'resources'> {}
export interface LocalInfo extends Tables<'local_info'> {}
export interface Tip extends Tables<'tips'> {}
export interface FAQ extends Tables<'faqs'> {}
export interface Guide extends Tables<'guides'> {}
export interface ExternalLink extends Tables<'external_links'> {}
export interface ChatRoom extends Tables<'chat_rooms'> {}
export interface ChatMessage extends Tables<'chat_messages'> {}
export interface ChatRoomParticipant extends Tables<'chat_room_participants'> {}

export interface UserProfile extends Profile {
  // Add any additional user-specific fields if needed
}

// Example of extending a type for specific use cases
export interface ActivityWithDetails extends Activity {
  festival?: Festival | null; // Optional: Link to the festival if needed
  parentActivity?: Activity | null; // Optional: Link to parent activity
}

export interface FestivalWithDetails extends Festival {
  activities?: Activity[];
  events?: Event[];
}

// Type for API responses that include pagination or additional metadata
export interface PaginatedResponse<T> {
  data: T[];
  count: number | null;
  error: Error | null;
}

// Type guards
export function isWorkshop(activity: Activity): activity is Activity & { workshop: Workshop } {
  return activity.type === 'workshop' && activity.workshop !== null;
}

export function isMeetup(activity: Activity): activity is Activity & { meetup: Meetup } {
  return activity.type === 'meetup' && activity.meetup !== null;
}
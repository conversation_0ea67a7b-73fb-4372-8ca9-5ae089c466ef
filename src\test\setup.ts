/**
 * Test Setup
 *
 * This file is run before each test file.
 */

import '@testing-library/jest-dom'
import { vi } from 'vitest'

// Mock import.meta.env for Jest
const mockImportMeta = {
  env: {
    VITE_SUPABASE_URL: 'https://test.supabase.co',
    VITE_SUPABASE_ANON_KEY: 'test-anon-key',
    VITE_SENTRY_DSN: '',
    VITE_VERCEL_ANALYTICS_ID: '',
    DEV: true,
    MODE: 'test',
    PROD: false,
    SSR: false,
    BASE_URL: '/',
  },
  hot: {
    accept: vi.fn(),
    dispose: vi.fn(),
    decline: vi.fn(),
    invalidate: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
    send: vi.fn(),
  },
  glob: vi.fn(() => ({})),
  url: 'file:///test',
};

// Set up import.meta globally
Object.defineProperty(globalThis, 'import', {
  value: { meta: mockImportMeta },
  writable: true,
  configurable: true,
});

// Also set up for window object (browser environment)
Object.defineProperty(window, 'import', {
  value: { meta: mockImportMeta },
  writable: true,
  configurable: true,
});

// Mock environment variables for Node.js
process.env.VITE_SUPABASE_URL = 'https://test.supabase.co'
process.env.VITE_SUPABASE_ANON_KEY = 'test-anon-key'
process.env.NODE_ENV = 'test'

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock IntersectionObserver
class MockIntersectionObserver {
  constructor(callback: IntersectionObserverCallback) {
    this.callback = callback
  }
  
  callback: IntersectionObserverCallback
  
  observe = jest.fn()
  unobserve = jest.fn()
  disconnect = jest.fn()
}

Object.defineProperty(window, 'IntersectionObserver', {
  writable: true,
  value: MockIntersectionObserver,
})

// Mock Supabase - Comprehensive mocking for all test scenarios
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: null },
        error: null,
      }),
      getUser: jest.fn().mockResolvedValue({
        data: { user: null },
        error: null,
      }),
      signInWithPassword: jest.fn().mockResolvedValue({
        data: { user: null, session: null },
        error: null,
      }),
      signUp: jest.fn().mockResolvedValue({
        data: { user: null, session: null },
        error: null,
      }),
      signOut: jest.fn().mockResolvedValue({
        error: null,
      }),
      onAuthStateChange: jest.fn(() => ({
        data: { subscription: { unsubscribe: jest.fn() } },
      })),
      updateUser: jest.fn().mockResolvedValue({
        data: { user: null },
        error: null,
      }),
      resetPasswordForEmail: jest.fn().mockResolvedValue({
        data: {},
        error: null,
      }),
    },
    from: jest.fn((table) => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        delete: jest.fn().mockReturnThis(),
        upsert: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        neq: jest.fn().mockReturnThis(),
        gt: jest.fn().mockReturnThis(),
        gte: jest.fn().mockReturnThis(),
        lt: jest.fn().mockReturnThis(),
        lte: jest.fn().mockReturnThis(),
        like: jest.fn().mockReturnThis(),
        ilike: jest.fn().mockReturnThis(),
        is: jest.fn().mockReturnThis(),
        in: jest.fn().mockReturnThis(),
        contains: jest.fn().mockReturnThis(),
        containedBy: jest.fn().mockReturnThis(),
        rangeGt: jest.fn().mockReturnThis(),
        rangeGte: jest.fn().mockReturnThis(),
        rangeLt: jest.fn().mockReturnThis(),
        rangeLte: jest.fn().mockReturnThis(),
        rangeAdjacent: jest.fn().mockReturnThis(),
        overlaps: jest.fn().mockReturnThis(),
        textSearch: jest.fn().mockReturnThis(),
        match: jest.fn().mockReturnThis(),
        not: jest.fn().mockReturnThis(),
        or: jest.fn().mockReturnThis(),
        filter: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        range: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error: null,
        }),
        maybeSingle: jest.fn().mockResolvedValue({
          data: null,
          error: null,
        }),
        then: jest.fn().mockResolvedValue({
          data: [],
          error: null,
        }),
      };
      return mockQueryBuilder;
    }),
    storage: {
      from: jest.fn(() => ({
        upload: jest.fn().mockResolvedValue({
          data: { path: 'test-path' },
          error: null,
        }),
        download: jest.fn().mockResolvedValue({
          data: new Blob(),
          error: null,
        }),
        list: jest.fn().mockResolvedValue({
          data: [],
          error: null,
        }),
        remove: jest.fn().mockResolvedValue({
          data: [],
          error: null,
        }),
        getPublicUrl: jest.fn(() => ({
          data: { publicUrl: 'https://example.com/image.jpg' },
        })),
        createSignedUrl: jest.fn().mockResolvedValue({
          data: { signedUrl: 'https://example.com/signed-url' },
          error: null,
        }),
      })),
    },
    channel: jest.fn(() => ({
      on: jest.fn().mockReturnThis(),
      subscribe: jest.fn().mockResolvedValue('ok'),
      unsubscribe: jest.fn().mockResolvedValue('ok'),
      send: jest.fn().mockResolvedValue('ok'),
    })),
    realtime: {
      channel: jest.fn(() => ({
        on: jest.fn().mockReturnThis(),
        subscribe: jest.fn().mockResolvedValue('ok'),
        unsubscribe: jest.fn().mockResolvedValue('ok'),
      })),
    },
  })),
}))

// Also mock the specific supabase client import
// This mock allows individual tests to override specific methods
jest.mock('../lib/supabase', () => {
  // Create mock functions that can be overridden by individual tests
  const mockAuth = {
    getSession: jest.fn().mockResolvedValue({
      data: { session: null },
      error: null,
    }),
    getUser: jest.fn().mockResolvedValue({
      data: { user: null },
      error: null,
    }),
    signInWithPassword: jest.fn().mockResolvedValue({
      data: { user: null, session: null },
      error: null,
    }),
    signUp: jest.fn().mockResolvedValue({
      data: { user: null, session: null },
      error: null,
    }),
    signOut: jest.fn().mockResolvedValue({
      error: null,
    }),
    onAuthStateChange: jest.fn(() => ({
      data: { subscription: { unsubscribe: jest.fn() } },
    })),
    updateUser: jest.fn().mockResolvedValue({
      data: { user: null },
      error: null,
    }),
  };

  const mockFrom = jest.fn((table) => {
    // Create a query builder that avoids circular references
    const mockQueryBuilder = {
      // Selection methods
      select: jest.fn(),

      // Modification methods
      insert: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      upsert: jest.fn(),

      // Filter methods
      eq: jest.fn(),
      neq: jest.fn(),
      gt: jest.fn(),
      gte: jest.fn(),
      lt: jest.fn(),
      lte: jest.fn(),
      like: jest.fn(),
      ilike: jest.fn(),
      is: jest.fn(),
      in: jest.fn(),
      contains: jest.fn(),
      containedBy: jest.fn(),
      rangeGt: jest.fn(),
      rangeGte: jest.fn(),
      rangeLt: jest.fn(),
      rangeLte: jest.fn(),
      rangeAdjacent: jest.fn(),
      overlaps: jest.fn(),
      textSearch: jest.fn(),
      match: jest.fn(),
      not: jest.fn(),
      or: jest.fn(),
      filter: jest.fn(),

      // Ordering and limiting
      order: jest.fn(),
      limit: jest.fn(),
      range: jest.fn(),

      // Execution methods
      single: jest.fn().mockResolvedValue({
        data: null,
        error: null,
      }),
      maybeSingle: jest.fn().mockResolvedValue({
        data: null,
        error: null,
      }),
      then: jest.fn().mockResolvedValue({
        data: [],
        error: null,
      }),
    };

    // Set up all methods to return the same query builder (avoiding circular references)
    Object.keys(mockQueryBuilder).forEach(key => {
      if (key !== 'single' && key !== 'maybeSingle' && key !== 'then') {
        (mockQueryBuilder as any)[key].mockReturnValue(mockQueryBuilder);
      }
    });

    return mockQueryBuilder;
  });

  const mockStorage = {
    from: jest.fn(() => ({
      upload: jest.fn().mockResolvedValue({
        data: { path: 'test-path' },
        error: null,
      }),
      getPublicUrl: jest.fn(() => ({
        data: { publicUrl: 'https://example.com/image.jpg' },
      })),
    })),
  };

  const mockSupabaseClient = {
    auth: mockAuth,
    from: mockFrom,
    storage: mockStorage,
  };

  return {
    supabase: mockSupabaseClient,
    client: mockSupabaseClient,
    default: mockSupabaseClient,
  };
})

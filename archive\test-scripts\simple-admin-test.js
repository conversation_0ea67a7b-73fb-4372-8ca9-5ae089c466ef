#!/usr/bin/env node

/**
 * Simple Admin Functionality Test
 * 
 * This script assumes the dev server is already running and focuses on
 * testing admin functionality directly with visual browser automation.
 */

import { chromium } from 'playwright';
import { promises as fs } from 'fs';
import path from 'path';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'simple-admin-evidence';

// Admin credentials from user confirmation
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

const testResults = {
  timestamp: new Date().toISOString(),
  connectivity: {},
  authentication: {},
  adminAccess: {},
  navigation: {},
  screenshots: [],
  errors: []
};

async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

async function captureScreenshot(page, name, description) {
  const filename = `${String(testResults.screenshots.length + 1).padStart(2, '0')}-${name}.png`;
  const filepath = path.join(EVIDENCE_DIR, filename);
  
  try {
    await page.screenshot({ 
      path: filepath, 
      fullPage: true,
      animations: 'disabled'
    });
    
    testResults.screenshots.push({
      filename,
      description,
      timestamp: new Date().toISOString()
    });
    
    console.log(`📸 ${filename}: ${description}`);
    return filename;
  } catch (error) {
    console.log(`❌ Screenshot failed: ${error.message}`);
    return null;
  }
}

async function testConnectivity() {
  console.log('\n🌐 Testing Basic Connectivity');
  console.log('=============================');
  
  const browser = await chromium.launch({ 
    headless: false,  // Visual mode for debugging
    slowMo: 500       // Slow down for observation
  });
  
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 }
  });
  
  const page = await context.newPage();
  
  // Capture console messages
  page.on('console', msg => {
    console.log(`🖥️ Console [${msg.type()}]: ${msg.text()}`);
  });
  
  // Capture page errors
  page.on('pageerror', error => {
    testResults.errors.push({
      type: 'page_error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
    console.log(`❌ Page Error: ${error.message}`);
  });

  try {
    console.log(`📍 Connecting to: ${APP_URL}`);
    
    const response = await page.goto(APP_URL, { 
      waitUntil: 'domcontentloaded', 
      timeout: 15000 
    });
    
    testResults.connectivity.success = true;
    testResults.connectivity.status = response.status();
    
    console.log(`✅ Connected successfully (Status: ${response.status()})`);
    await captureScreenshot(page, 'home-page', 'Home page loaded');
    
    return { browser, context, page };
    
  } catch (error) {
    console.log(`❌ Connectivity failed: ${error.message}`);
    testResults.connectivity.success = false;
    testResults.connectivity.error = error.message;
    
    await browser.close();
    return null;
  }
}

async function testAuthentication(browserInfo) {
  if (!browserInfo) {
    console.log('\n⏭️ Skipping authentication test - no browser connection');
    return false;
  }

  console.log('\n🔐 Testing Admin Authentication');
  console.log('===============================');
  
  const { page } = browserInfo;
  
  try {
    // Navigate to auth page
    console.log('📍 Navigating to authentication page...');
    await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded', timeout: 15000 });
    await page.waitForTimeout(2000);
    
    await captureScreenshot(page, 'auth-page', 'Authentication page');
    
    // Look for email input
    const emailInput = page.locator('input[type="email"]').first();
    const passwordInput = page.locator('input[type="password"]').first();
    
    if (await emailInput.isVisible()) {
      console.log('📝 Filling in admin credentials...');
      await emailInput.fill(ADMIN_CREDENTIALS.email);
      await passwordInput.fill(ADMIN_CREDENTIALS.password);
      
      await captureScreenshot(page, 'credentials-filled', 'Admin credentials entered');
      
      // Submit the form
      console.log('🚀 Submitting login form...');
      const submitButton = page.locator('button[type="submit"]').first();
      await submitButton.click();
      
      // Wait for navigation
      await page.waitForTimeout(5000);
      
      const currentUrl = page.url();
      console.log(`📍 Current URL after login: ${currentUrl}`);
      
      if (currentUrl.includes('/auth')) {
        // Still on auth page - login failed
        testResults.authentication.success = false;
        testResults.authentication.error = 'Remained on auth page after login';
        console.log('❌ Login failed - still on auth page');
        
        // Check for error messages
        const errorElement = page.locator('[role="alert"], .error, .text-red').first();
        if (await errorElement.isVisible()) {
          const errorText = await errorElement.textContent();
          testResults.authentication.errorMessage = errorText;
          console.log(`❌ Error message: ${errorText}`);
        }
        
        await captureScreenshot(page, 'login-failed', 'Login failed state');
        return false;
      } else {
        // Successfully navigated away from auth page
        testResults.authentication.success = true;
        testResults.authentication.redirectUrl = currentUrl;
        console.log('✅ Login successful - redirected to:', currentUrl);
        
        await captureScreenshot(page, 'login-success', 'Successful login state');
        return true;
      }
      
    } else {
      testResults.authentication.success = false;
      testResults.authentication.error = 'Login form not found';
      console.log('❌ Login form not found on auth page');
      return false;
    }
    
  } catch (error) {
    testResults.authentication.success = false;
    testResults.authentication.error = error.message;
    console.log(`❌ Authentication error: ${error.message}`);
    return false;
  }
}

async function testAdminAccess(browserInfo, isAuthenticated) {
  if (!browserInfo) {
    console.log('\n⏭️ Skipping admin access test - no browser connection');
    return;
  }

  console.log('\n👑 Testing Admin Dashboard Access');
  console.log('=================================');
  
  const { page } = browserInfo;
  
  try {
    // Try to access admin dashboard
    console.log('📍 Attempting to access admin dashboard...');
    await page.goto(`${APP_URL}/admin`, { waitUntil: 'domcontentloaded', timeout: 15000 });
    await page.waitForTimeout(3000);
    
    const currentUrl = page.url();
    const pageContent = await page.textContent('body');
    
    console.log(`📍 Admin page URL: ${currentUrl}`);
    
    if (currentUrl.includes('/admin')) {
      testResults.adminAccess.accessible = true;
      testResults.adminAccess.url = currentUrl;
      console.log('✅ Admin dashboard is accessible');
      
      // Check for admin-specific content
      const hasAdminContent = pageContent.toLowerCase().includes('admin') || 
                             pageContent.toLowerCase().includes('dashboard');
      testResults.adminAccess.hasAdminContent = hasAdminContent;
      
      await captureScreenshot(page, 'admin-dashboard', 'Admin dashboard page');
      
      // Test admin navigation
      await testAdminNavigation(page);
      
    } else {
      testResults.adminAccess.accessible = false;
      testResults.adminAccess.redirectedTo = currentUrl;
      console.log(`❌ Admin dashboard not accessible - redirected to: ${currentUrl}`);
      
      await captureScreenshot(page, 'admin-access-denied', 'Admin access denied');
    }
    
  } catch (error) {
    testResults.adminAccess.accessible = false;
    testResults.adminAccess.error = error.message;
    console.log(`❌ Admin access error: ${error.message}`);
  }
}

async function testAdminNavigation(page) {
  console.log('\n🧭 Testing Admin Navigation');
  console.log('===========================');
  
  try {
    // Look for common admin navigation elements
    const navElements = [
      'Users', 'Dashboard', 'Settings', 'Analytics', 'Content', 'Admin'
    ];
    
    testResults.navigation.elements = {};
    
    for (const element of navElements) {
      const link = page.locator(`a:has-text("${element}"), button:has-text("${element}")`).first();
      const isVisible = await link.isVisible().catch(() => false);
      
      testResults.navigation.elements[element] = isVisible;
      console.log(`   ${element}: ${isVisible ? '✅ Found' : '❌ Not found'}`);
    }
    
    await captureScreenshot(page, 'admin-navigation', 'Admin navigation elements');
    
  } catch (error) {
    testResults.navigation.error = error.message;
    console.log(`❌ Navigation test error: ${error.message}`);
  }
}

async function saveResults() {
  const resultsFile = path.join(EVIDENCE_DIR, 'simple-admin-test-results.json');
  await fs.writeFile(resultsFile, JSON.stringify(testResults, null, 2));
  console.log(`💾 Test results saved: ${resultsFile}`);
}

async function generateSummary() {
  console.log('\n📋 TEST SUMMARY');
  console.log('===============');
  
  console.log(`🕐 Test completed: ${new Date().toISOString()}`);
  console.log(`📸 Screenshots: ${testResults.screenshots.length}`);
  console.log(`❌ Errors: ${testResults.errors.length}`);
  
  // Connectivity
  if (testResults.connectivity.success) {
    console.log('✅ Application connectivity: Working');
  } else {
    console.log('❌ Application connectivity: Failed');
  }
  
  // Authentication
  if (testResults.authentication.success) {
    console.log('✅ Admin authentication: Working');
  } else {
    console.log('❌ Admin authentication: Failed');
  }
  
  // Admin access
  if (testResults.adminAccess.accessible) {
    console.log('✅ Admin dashboard access: Working');
  } else {
    console.log('❌ Admin dashboard access: Failed');
  }
  
  console.log(`\n📁 Evidence saved in: ${EVIDENCE_DIR}`);
  
  if (testResults.errors.length > 0) {
    console.log('\n⚠️ Errors encountered:');
    testResults.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error.message}`);
    });
  }
}

async function runSimpleAdminTest() {
  console.log('🎯 SIMPLE ADMIN FUNCTIONALITY TEST');
  console.log('==================================');
  console.log(`🌐 Testing URL: ${APP_URL}`);
  console.log(`👤 Admin Email: ${ADMIN_CREDENTIALS.email}`);
  
  await ensureEvidenceDir();
  
  let browserInfo = null;
  
  try {
    // Test basic connectivity
    browserInfo = await testConnectivity();
    
    // Test authentication
    const isAuthenticated = await testAuthentication(browserInfo);
    
    // Test admin access
    await testAdminAccess(browserInfo, isAuthenticated);
    
    // Generate summary and save results
    await generateSummary();
    await saveResults();
    
  } catch (error) {
    console.error('❌ Test execution error:', error);
  } finally {
    // Cleanup
    if (browserInfo) {
      try {
        await browserInfo.browser.close();
        console.log('🔚 Browser closed');
      } catch (error) {
        console.log('⚠️ Browser cleanup error:', error.message);
      }
    }
  }
}

// Run the test
runSimpleAdminTest().catch(console.error);

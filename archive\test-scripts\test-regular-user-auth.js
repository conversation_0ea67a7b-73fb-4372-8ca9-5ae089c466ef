/**
 * Test Regular User Authentication Flow
 * 
 * This script tests the complete regular user authentication flow:
 * 1. Sign out current user
 * 2. Create a test regular user account
 * 3. Test sign-in with regular user
 * 4. Verify role: "user" and isAdmin: false
 * 5. Test profile loading performance
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('🧪 Testing Regular User Authentication Flow');
console.log('==========================================');

async function testRegularUserAuthentication() {
  try {
    // Step 1: Sign out current user
    console.log('🔓 Step 1: Signing out current user');
    console.log('----------------------------------');
    
    const { error: signOutError } = await supabase.auth.signOut();
    if (signOutError) {
      console.error('❌ Sign out failed:', signOutError.message);
    } else {
      console.log('✅ Successfully signed out');
    }
    console.log('');

    // Step 2: Create test regular user
    console.log('👤 Step 2: Creating test regular user');
    console.log('------------------------------------');
    
    const testUserEmail = '<EMAIL>';
    const testUserPassword = 'testpassword123';
    
    console.log(`📧 Email: ${testUserEmail}`);
    console.log(`🔑 Password: ${testUserPassword}`);
    
    // Try to sign up the test user
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: testUserEmail,
      password: testUserPassword,
      options: {
        data: {
          username: 'testuser'
        }
      }
    });
    
    if (signUpError) {
      if (signUpError.message.includes('already registered')) {
        console.log('ℹ️  User already exists, proceeding to sign in test');
      } else {
        console.error('❌ Sign up failed:', signUpError.message);
        return;
      }
    } else {
      console.log('✅ Sign up successful');
      console.log('👤 User ID:', signUpData.user?.id);
      console.log('📧 Email:', signUpData.user?.email);
    }
    console.log('');

    // Step 3: Test sign-in with regular user
    console.log('🔐 Step 3: Testing regular user sign-in');
    console.log('--------------------------------------');
    
    const startTime = Date.now();
    
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: testUserEmail,
      password: testUserPassword
    });
    
    const signInDuration = Date.now() - startTime;
    
    if (signInError) {
      console.error('❌ Sign in failed:', signInError.message);
      return;
    }
    
    console.log('✅ Sign in successful');
    console.log(`⏱️  Sign in duration: ${signInDuration}ms`);
    console.log('👤 User ID:', signInData.user?.id);
    console.log('📧 Email:', signInData.user?.email);
    console.log('');

    // Step 4: Test profile fetching with timing
    console.log('📊 Step 4: Testing profile fetching performance');
    console.log('----------------------------------------------');
    
    const profileStartTime = Date.now();
    
    // Test our improved profile fetching with timeout
    const timeoutPromise = new Promise((resolve) => {
      setTimeout(() => {
        console.log('⏰ Profile fetch timeout triggered after 5 seconds');
        resolve({ 
          data: null, 
          error: { message: 'Profile fetch timeout', code: 'TIMEOUT' } 
        });
      }, 5000);
    });

    const queryPromise = supabase
      .from('profiles')
      .select('*')
      .eq('id', signInData.user.id)
      .single();

    console.log('🔍 Starting profile fetch race (query vs 5-second timeout)...');
    
    const result = await Promise.race([queryPromise, timeoutPromise]);
    const { data: profileData, error: profileError } = result;
    
    const profileDuration = Date.now() - profileStartTime;
    console.log(`⏱️  Profile fetch duration: ${profileDuration}ms`);
    
    if (profileError) {
      if (profileError.code === 'TIMEOUT') {
        console.log('⏰ Profile fetch timed out - this indicates a performance issue');
        
        // Test basic profile creation for regular user
        console.log('📝 Testing basic profile creation for regular user');
        const basicProfile = {
          id: signInData.user.id,
          email: signInData.user.email || '',
          username: signInData.user.email?.split('@')[0] || 'user',
          role: 'user', // Should be 'user' for regular users
          created_at: new Date().toISOString(),
        };
        
        console.log('👤 Basic profile for regular user:');
        console.log(JSON.stringify(basicProfile, null, 2));
        console.log('🔍 Role check: role =', basicProfile.role, ', isAdmin =', basicProfile.role === 'SUPER_ADMIN');
        
      } else if (profileError.code === 'PGRST116') {
        console.log('ℹ️  Profile not found - this is expected for new users');
        console.log('📝 Would create basic profile with role: "user"');
      } else {
        console.error('❌ Profile fetch error:', profileError);
      }
    } else {
      console.log('✅ Profile fetched successfully:');
      console.log('👤 Role:', profileData.role);
      console.log('🔑 Is Admin:', profileData.role === 'SUPER_ADMIN');
      console.log('📊 Full profile:', JSON.stringify(profileData, null, 2));
      
      // Verify regular user has correct role
      if (profileData.role === 'user') {
        console.log('✅ CORRECT: Regular user has role: "user"');
      } else {
        console.log('❌ ERROR: Regular user has unexpected role:', profileData.role);
      }
    }
    
    console.log('');

    // Step 5: Performance analysis
    console.log('📈 Step 5: Performance Analysis');
    console.log('------------------------------');
    console.log(`🔐 Sign-in performance: ${signInDuration}ms`);
    console.log(`📊 Profile fetch performance: ${profileDuration}ms`);
    
    if (signInDuration < 1000) {
      console.log('✅ Sign-in performance: EXCELLENT (< 1 second)');
    } else if (signInDuration < 2000) {
      console.log('✅ Sign-in performance: GOOD (< 2 seconds)');
    } else {
      console.log('⚠️  Sign-in performance: SLOW (> 2 seconds)');
    }
    
    if (profileDuration < 1000) {
      console.log('✅ Profile fetch performance: EXCELLENT (< 1 second)');
    } else if (profileDuration < 2000) {
      console.log('✅ Profile fetch performance: GOOD (< 2 seconds)');
    } else if (profileDuration >= 5000) {
      console.log('❌ Profile fetch performance: TIMEOUT (>= 5 seconds)');
    } else {
      console.log('⚠️  Profile fetch performance: SLOW (2-5 seconds)');
    }

  } catch (error) {
    console.error('💥 Test failed with exception:', error);
  }
}

// Run the test
testRegularUserAuthentication().then(() => {
  console.log('');
  console.log('🎯 Regular User Authentication Test Complete');
  console.log('===========================================');
  process.exit(0);
}).catch(error => {
  console.error('💥 Test suite failed:', error);
  process.exit(1);
});

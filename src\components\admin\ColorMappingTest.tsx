import React, { useState } from 'react';
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';
import { useEnhancedColorMapping } from '@/hooks/useEnhancedColorMapping';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';

/**
 * Color Mapping Test Component
 * Tests the integration between the database schema and the enhanced color mapping service
 */

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  details?: any;
}

const ColorMappingTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [overallStatus, setOverallStatus] = useState<'idle' | 'running' | 'success' | 'error'>('idle');

  // Test the hook with a sample content type
  const { colorMapping: activitiesMapping, isLoading: activitiesLoading, error: activitiesError } =
    useEnhancedColorMapping('activities', 'main');

  const runTests = async () => {
    setIsRunning(true);
    setOverallStatus('running');
    const results: TestResult[] = [];

    // Test 1: Initialize color mappings
    try {
      results.push({ name: 'Initialize Color Mappings', status: 'pending', message: 'Starting initialization...' });
      setTestResults([...results]);

      const initResult = await enhancedColorMappingService.initializeEnhancedColorMappings();

      if (initResult.success) {
        results[results.length - 1] = {
          name: 'Initialize Color Mappings',
          status: 'success',
          message: `Successfully initialized ${initResult.count} color mappings`,
          details: initResult
        };
      } else {
        results[results.length - 1] = {
          name: 'Initialize Color Mappings',
          status: 'error',
          message: `Failed with ${initResult.errors.length} errors`,
          details: initResult.errors
        };
      }
    } catch (error) {
      results[results.length - 1] = {
        name: 'Initialize Color Mappings',
        status: 'error',
        message: `Exception: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: error
      };
    }

    setTestResults([...results]);

    // Test 2: Fetch specific color mapping
    try {
      results.push({ name: 'Fetch Activities Color Mapping', status: 'pending', message: 'Fetching activities mapping...' });
      setTestResults([...results]);

      const activitiesMapping = await enhancedColorMappingService.getColorMapping('activities', 'main');

      if (activitiesMapping) {
        results[results.length - 1] = {
          name: 'Fetch Activities Color Mapping',
          status: 'success',
          message: 'Successfully fetched activities color mapping',
          details: activitiesMapping
        };
      } else {
        results[results.length - 1] = {
          name: 'Fetch Activities Color Mapping',
          status: 'error',
          message: 'No color mapping found for activities.main',
          details: null
        };
      }
    } catch (error) {
      results[results.length - 1] = {
        name: 'Fetch Activities Color Mapping',
        status: 'error',
        message: `Exception: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: error
      };
    }

    setTestResults([...results]);

    // Test 3: Fetch all mappings for a content type
    try {
      results.push({ name: 'Fetch All Activities Mappings', status: 'pending', message: 'Fetching all activities mappings...' });
      setTestResults([...results]);

      const allActivitiesMappings = await enhancedColorMappingService.getContentTypeColorMappings('activities');

      if (allActivitiesMappings.length > 0) {
        results[results.length - 1] = {
          name: 'Fetch All Activities Mappings',
          status: 'success',
          message: `Successfully fetched ${allActivitiesMappings.length} activities mappings`,
          details: allActivitiesMappings
        };
      } else {
        results[results.length - 1] = {
          name: 'Fetch All Activities Mappings',
          status: 'error',
          message: 'No color mappings found for activities content type',
          details: []
        };
      }
    } catch (error) {
      results[results.length - 1] = {
        name: 'Fetch All Activities Mappings',
        status: 'error',
        message: `Exception: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: error
      };
    }

    setTestResults([...results]);

    // Test 4: Verify hook integration
    results.push({
      name: 'Hook Integration Test',
      status: activitiesError ? 'error' : (activitiesMapping ? 'success' : 'pending'),
      message: activitiesError
        ? `Hook error: ${activitiesError}`
        : (activitiesMapping ? 'Hook successfully loaded color mapping' : 'Hook still loading...'),
      details: activitiesMapping
    });

    setTestResults([...results]);

    // Determine overall status
    const hasErrors = results.some(r => r.status === 'error');
    const allComplete = results.every(r => r.status !== 'pending');

    if (hasErrors) {
      setOverallStatus('error');
    } else if (allComplete) {
      setOverallStatus('success');
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'pending':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-100 text-green-800">Success</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🎨 Enhanced Color Mapping Integration Test
          {overallStatus === 'running' && <Loader2 className="w-5 h-5 animate-spin" />}
        </CardTitle>
        <CardDescription>
          Tests the integration between the database schema and the enhanced color mapping service
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Test Controls */}
        <div className="flex items-center gap-4">
          <Button
            onClick={runTests}
            disabled={isRunning}
            className="flex items-center gap-2"
          >
            {isRunning ? <Loader2 className="w-4 h-4 animate-spin" /> : null}
            {isRunning ? 'Running Tests...' : 'Run Integration Tests'}
          </Button>

          {overallStatus !== 'idle' && (
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Overall Status:</span>
              {getStatusBadge(overallStatus as any)}
            </div>
          )}
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Test Results</h3>
            {testResults.map((result, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(result.status)}
                    <span className="font-medium">{result.name}</span>
                  </div>
                  {getStatusBadge(result.status)}
                </div>
                <p className="text-sm text-muted-foreground">{result.message}</p>
                {result.details && (
                  <details className="text-xs">
                    <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                      View Details
                    </summary>
                    <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto">
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Hook Status Display */}
        <div className="border rounded-lg p-4 space-y-2">
          <h4 className="font-medium">useEnhancedColorMapping Hook Status</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium">Loading:</span> {activitiesLoading ? 'Yes' : 'No'}
            </div>
            <div>
              <span className="font-medium">Error:</span> {activitiesError || 'None'}
            </div>
            <div>
              <span className="font-medium">Mapping Loaded:</span> {activitiesMapping ? 'Yes' : 'No'}
            </div>
          </div>
          {activitiesMapping && (
            <div className="mt-2">
              <span className="font-medium">Sample Colors:</span>
              <div className="flex items-center gap-2 mt-1">
                <div
                  className="w-6 h-6 rounded border"
                  style={{ backgroundColor: activitiesMapping.color_primary }}
                  title={`Primary: ${activitiesMapping.color_primary}`}
                />
                <div
                  className="w-6 h-6 rounded border"
                  style={{ backgroundColor: activitiesMapping.color_secondary }}
                  title={`Secondary: ${activitiesMapping.color_secondary}`}
                />
                <div
                  className="w-6 h-6 rounded border"
                  style={{ backgroundColor: activitiesMapping.color_accent }}
                  title={`Accent: ${activitiesMapping.color_accent}`}
                />
                <span className="text-lg">{activitiesMapping.emoji_icon}</span>
                <span className="text-sm">Show Icon: {activitiesMapping.show_icon ? 'Yes' : 'No'}</span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ColorMappingTest;
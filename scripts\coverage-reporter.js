#!/usr/bin/env node

/**
 * Coverage Reporter and Notification System
 * 
 * Automated coverage analysis, reporting, and notification system
 * that provides insights into test coverage and quality metrics.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// Configuration
const COVERAGE_THRESHOLDS = {
  statements: 80,
  branches: 75,
  functions: 80,
  lines: 80
};

const NOTIFICATION_CHANNELS = {
  CONSOLE: true,
  FILE: true,
  WEBHOOK: false, // Can be configured for Slack/Discord/etc.
  EMAIL: false    // Can be configured for email notifications
};

const REPORT_CONFIG = {
  outputDir: 'coverage-reports',
  historyFile: 'coverage-history.json',
  trendsFile: 'coverage-trends.json',
  maxHistoryEntries: 50
};

// Utility functions
function logInfo(message) {
  console.log(`📊 [Coverage Reporter] ${message}`);
}

function logSuccess(message) {
  console.log(`✅ [Coverage Reporter] ${message}`);
}

function logError(message) {
  console.log(`❌ [Coverage Reporter] ${message}`);
}

function logWarning(message) {
  console.log(`⚠️  [Coverage Reporter] ${message}`);
}

function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

function runCoverageAnalysis() {
  logInfo('Running comprehensive coverage analysis...');
  
  try {
    // Run Jest with coverage
    const jestOutput = execSync('npm run test:unit:coverage', { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    // Run Playwright tests (they don't generate coverage but we track them)
    const playwrightOutput = execSync('npx playwright test --list', {
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    return {
      jest: jestOutput,
      playwright: playwrightOutput,
      success: true
    };
  } catch (error) {
    logError(`Coverage analysis failed: ${error.message}`);
    return {
      jest: error.stdout || '',
      playwright: '',
      success: false,
      error: error.message
    };
  }
}

function parseCoverageData() {
  const coverageFile = 'coverage/coverage-summary.json';
  
  if (!fs.existsSync(coverageFile)) {
    logWarning('Coverage summary file not found');
    return null;
  }
  
  try {
    const coverageData = JSON.parse(fs.readFileSync(coverageFile, 'utf8'));
    return coverageData;
  } catch (error) {
    logError(`Failed to parse coverage data: ${error.message}`);
    return null;
  }
}

function calculateCoverageMetrics(coverageData) {
  if (!coverageData || !coverageData.total) {
    return null;
  }
  
  const total = coverageData.total;
  
  return {
    statements: {
      covered: total.statements.covered,
      total: total.statements.total,
      percentage: total.statements.pct
    },
    branches: {
      covered: total.branches.covered,
      total: total.branches.total,
      percentage: total.branches.pct
    },
    functions: {
      covered: total.functions.covered,
      total: total.functions.total,
      percentage: total.functions.pct
    },
    lines: {
      covered: total.lines.covered,
      total: total.lines.total,
      percentage: total.lines.pct
    }
  };
}

function assessCoverageQuality(metrics) {
  if (!metrics) {
    return { overall: 'UNKNOWN', issues: ['Coverage data unavailable'] };
  }
  
  const issues = [];
  let passedThresholds = 0;
  const totalThresholds = Object.keys(COVERAGE_THRESHOLDS).length;
  
  for (const [type, threshold] of Object.entries(COVERAGE_THRESHOLDS)) {
    const actual = metrics[type]?.percentage || 0;
    if (actual < threshold) {
      issues.push(`${type} coverage (${actual}%) below threshold (${threshold}%)`);
    } else {
      passedThresholds++;
    }
  }
  
  let overall;
  if (passedThresholds === totalThresholds) {
    overall = 'EXCELLENT';
  } else if (passedThresholds >= totalThresholds * 0.75) {
    overall = 'GOOD';
  } else if (passedThresholds >= totalThresholds * 0.5) {
    overall = 'FAIR';
  } else {
    overall = 'POOR';
  }
  
  return { overall, issues, passedThresholds, totalThresholds };
}

function loadCoverageHistory() {
  const historyFile = path.join(REPORT_CONFIG.outputDir, REPORT_CONFIG.historyFile);
  
  if (!fs.existsSync(historyFile)) {
    return [];
  }
  
  try {
    return JSON.parse(fs.readFileSync(historyFile, 'utf8'));
  } catch (error) {
    logWarning(`Failed to load coverage history: ${error.message}`);
    return [];
  }
}

function saveCoverageHistory(history) {
  const historyFile = path.join(REPORT_CONFIG.outputDir, REPORT_CONFIG.historyFile);
  
  try {
    fs.writeFileSync(historyFile, JSON.stringify(history, null, 2));
  } catch (error) {
    logError(`Failed to save coverage history: ${error.message}`);
  }
}

function calculateTrends(history) {
  if (history.length < 2) {
    return { trend: 'INSUFFICIENT_DATA', changes: {} };
  }
  
  const current = history[history.length - 1];
  const previous = history[history.length - 2];
  
  const changes = {};
  let improvementCount = 0;
  let degradationCount = 0;
  
  for (const metric of ['statements', 'branches', 'functions', 'lines']) {
    const currentPct = current.metrics[metric]?.percentage || 0;
    const previousPct = previous.metrics[metric]?.percentage || 0;
    const change = currentPct - previousPct;
    
    changes[metric] = {
      current: currentPct,
      previous: previousPct,
      change: change,
      direction: change > 0 ? 'UP' : change < 0 ? 'DOWN' : 'STABLE'
    };
    
    if (change > 0) improvementCount++;
    if (change < 0) degradationCount++;
  }
  
  let trend;
  if (improvementCount > degradationCount) {
    trend = 'IMPROVING';
  } else if (degradationCount > improvementCount) {
    trend = 'DECLINING';
  } else {
    trend = 'STABLE';
  }
  
  return { trend, changes, improvementCount, degradationCount };
}

function generateCoverageReport() {
  logInfo('Generating comprehensive coverage report...');
  
  ensureDirectoryExists(REPORT_CONFIG.outputDir);
  
  // Run coverage analysis
  const analysisResult = runCoverageAnalysis();
  
  // Parse coverage data
  const coverageData = parseCoverageData();
  const metrics = calculateCoverageMetrics(coverageData);
  const quality = assessCoverageQuality(metrics);
  
  // Load and update history
  const history = loadCoverageHistory();
  const currentEntry = {
    timestamp: new Date().toISOString(),
    metrics,
    quality,
    success: analysisResult.success
  };
  
  history.push(currentEntry);
  
  // Limit history size
  if (history.length > REPORT_CONFIG.maxHistoryEntries) {
    history.splice(0, history.length - REPORT_CONFIG.maxHistoryEntries);
  }
  
  saveCoverageHistory(history);
  
  // Calculate trends
  const trends = calculateTrends(history);
  
  // Generate report
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      overall: quality.overall,
      trend: trends.trend,
      passedThresholds: `${quality.passedThresholds}/${quality.totalThresholds}`,
      analysisSuccess: analysisResult.success
    },
    metrics,
    quality,
    trends,
    thresholds: COVERAGE_THRESHOLDS,
    recommendations: generateRecommendations(quality, trends, metrics)
  };
  
  // Save detailed report
  const reportFile = path.join(REPORT_CONFIG.outputDir, `coverage-report-${Date.now()}.json`);
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  
  // Generate and save text report
  const textReport = generateTextReport(report);
  const textReportFile = path.join(REPORT_CONFIG.outputDir, `coverage-report-${Date.now()}.txt`);
  fs.writeFileSync(textReportFile, textReport);
  
  return report;
}

function generateRecommendations(quality, trends, metrics) {
  const recommendations = [];
  
  if (quality.overall === 'POOR') {
    recommendations.push('🚨 CRITICAL: Coverage is below acceptable levels. Prioritize writing tests for uncovered code.');
  }
  
  if (trends.trend === 'DECLINING') {
    recommendations.push('📉 WARNING: Coverage is declining. Review recent changes and add missing tests.');
  }
  
  if (quality.issues.length > 0) {
    recommendations.push(`📋 Address specific coverage gaps: ${quality.issues.join(', ')}`);
  }
  
  if (metrics) {
    // Identify the weakest area
    const weakestMetric = Object.entries(metrics)
      .sort((a, b) => a[1].percentage - b[1].percentage)[0];
    
    if (weakestMetric[1].percentage < 70) {
      recommendations.push(`🎯 Focus on improving ${weakestMetric[0]} coverage (currently ${weakestMetric[1].percentage}%)`);
    }
  }
  
  if (trends.trend === 'IMPROVING') {
    recommendations.push('✨ Great job! Coverage is improving. Keep up the good work!');
  }
  
  if (quality.overall === 'EXCELLENT') {
    recommendations.push('🏆 Excellent coverage! Consider adding edge case tests and performance tests.');
  }
  
  return recommendations;
}

function generateTextReport(report) {
  return `
FESTIVAL FAMILY - COVERAGE REPORT
Generated: ${report.timestamp}

SUMMARY
=======
Overall Quality: ${report.summary.overall}
Trend: ${report.summary.trend}
Thresholds Passed: ${report.summary.passedThresholds}
Analysis Success: ${report.summary.analysisSuccess}

COVERAGE METRICS
================
${report.metrics ? Object.entries(report.metrics).map(([type, data]) => 
  `${type.toUpperCase()}: ${data.covered}/${data.total} (${data.percentage}%)`
).join('\n') : 'No metrics available'}

QUALITY ASSESSMENT
==================
Status: ${report.quality.overall}
Issues: ${report.quality.issues.length > 0 ? report.quality.issues.join('\n        ') : 'None'}

TRENDS
======
Direction: ${report.trends.trend}
${report.trends.changes ? Object.entries(report.trends.changes).map(([type, change]) =>
  `${type}: ${change.previous}% → ${change.current}% (${change.direction})`
).join('\n') : 'No trend data available'}

RECOMMENDATIONS
===============
${report.recommendations.map(rec => `• ${rec}`).join('\n')}

THRESHOLDS
==========
${Object.entries(report.thresholds).map(([type, threshold]) =>
  `${type}: ${threshold}%`
).join('\n')}
`;
}

function sendNotifications(report) {
  if (NOTIFICATION_CHANNELS.CONSOLE) {
    logInfo('Coverage Report Summary:');
    console.log(`  Overall: ${report.summary.overall}`);
    console.log(`  Trend: ${report.summary.trend}`);
    console.log(`  Thresholds: ${report.summary.passedThresholds}`);
    
    if (report.quality.issues.length > 0) {
      logWarning('Coverage Issues:');
      report.quality.issues.forEach(issue => console.log(`    • ${issue}`));
    }
    
    if (report.recommendations.length > 0) {
      logInfo('Recommendations:');
      report.recommendations.forEach(rec => console.log(`    ${rec}`));
    }
  }
  
  // Additional notification channels can be implemented here
  // (Slack, Discord, Email, etc.)
}

// Main execution
function main() {
  logInfo('Starting coverage analysis and reporting...');
  
  try {
    const report = generateCoverageReport();
    sendNotifications(report);
    
    logSuccess(`Coverage report generated successfully`);
    logInfo(`Reports saved to: ${REPORT_CONFIG.outputDir}`);
    
    // Exit with appropriate code based on coverage quality
    const exitCode = report.quality.overall === 'POOR' ? 1 : 0;
    process.exit(exitCode);
    
  } catch (error) {
    logError(`Coverage reporting failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { generateCoverageReport, calculateCoverageMetrics, assessCoverageQuality };

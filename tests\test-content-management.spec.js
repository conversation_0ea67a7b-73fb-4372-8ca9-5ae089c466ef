/**
 * Test Content Management
 * 
 * This test verifies that the content management system works correctly
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

test('Test Content Management System', async ({ page }) => {
  console.log('🧪 Testing content management system...');
  
  const loginSuccess = await loginAsAdmin(page);
  console.log(`Login success: ${loginSuccess}`);
  
  if (loginSuccess) {
    // Navigate to content management
    await page.goto('/admin/content');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    console.log('📝 Checking content management interface...');
    
    // Check if content management elements exist
    const hasCreateButton = await page.locator('button:has-text("Create Content")').count() > 0;
    const hasTabs = await page.locator('[role="tablist"], .tabs').count() > 0;
    const hasContentItems = await page.locator('.card, [data-testid*="content"]').count() > 0;
    
    console.log(`Content management elements found:`);
    console.log(`  Create button: ${hasCreateButton}`);
    console.log(`  Tabs interface: ${hasTabs}`);
    console.log(`  Content items: ${hasContentItems}`);
    
    await page.screenshot({ path: 'test-results/content-management.png', fullPage: true });
    
    // Test create content functionality
    if (hasCreateButton) {
      console.log('🔗 Testing create content functionality...');
      
      await page.click('button:has-text("Create Content")');
      await page.waitForTimeout(1000);
      
      // Check if create form appears
      const hasCreateForm = await page.locator('form').count() > 0;
      const hasContentKeyField = await page.locator('input[placeholder*="content_key"], input[placeholder*="hero_title"]').count() > 0;
      const hasTitleField = await page.locator('input[placeholder*="title"]').count() > 0;
      const hasContentField = await page.locator('textarea').count() > 0;
      
      console.log(`Create form elements:`);
      console.log(`  Form visible: ${hasCreateForm}`);
      console.log(`  Content key field: ${hasContentKeyField}`);
      console.log(`  Title field: ${hasTitleField}`);
      console.log(`  Content field: ${hasContentField}`);
      
      await page.screenshot({ path: 'test-results/content-management-create-form.png', fullPage: true });
      
      // Try to fill the form
      if (hasCreateForm && hasContentField) {
        console.log('📝 Filling create content form...');
        
        // Fill content key
        const contentKeyField = page.locator('input[placeholder*="content_key"], input[placeholder*="hero_title"]').first();
        if (await contentKeyField.isVisible()) {
          await contentKeyField.fill('test_content_' + Date.now());
        }
        
        // Fill title
        const titleField = page.locator('input[placeholder*="title"]').first();
        if (await titleField.isVisible()) {
          await titleField.fill('Test Content Title');
        }
        
        // Fill content
        const contentField = page.locator('textarea').first();
        if (await contentField.isVisible()) {
          await contentField.fill('This is test content created by automated testing.');
        }
        
        await page.screenshot({ path: 'test-results/content-management-form-filled.png', fullPage: true });
        
        console.log('✅ Content form filled successfully');
      }
    }
    
    // Test edit functionality
    console.log('🔗 Testing edit functionality...');
    
    const editButtons = await page.locator('button[aria-label*="edit"], button:has([data-testid*="edit"]), svg[data-testid*="pencil"]').count();
    console.log(`Found ${editButtons} edit buttons`);
    
    if (editButtons > 0) {
      const firstEditButton = page.locator('button[aria-label*="edit"], button:has([data-testid*="edit"]), svg[data-testid*="pencil"]').first();
      await firstEditButton.click();
      await page.waitForTimeout(1000);
      
      await page.screenshot({ path: 'test-results/content-management-edit.png', fullPage: true });
      
      console.log('✅ Edit functionality tested');
    }
    
    console.log('✅ Content management test completed');
  }
});

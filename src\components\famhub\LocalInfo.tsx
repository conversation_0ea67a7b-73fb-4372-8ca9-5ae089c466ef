import React, { useState, useEffect } from 'react';
import { MapPin, Star, ExternalLink, Filter } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useLocationBasedContent, useLocationSuggestions } from '@/hooks/useLocationBasedContent';
import { type LocationContext } from '@/lib/services/locationBasedContentService';
import type { LocalInfo as LocalInfoType } from '@/types';

interface LocalInfoItem {
  id: string;
  title: string;
  description: string | null;
  type?: 'venue' | 'accommodation' | 'transport';
  category?: string;
  is_featured?: boolean | null;
  priority?: number | null;
  link?: string | null;
  locationScore?: number;
}

interface LocalInfoProps {
  items?: (LocalInfoItem | LocalInfoType)[];
  locationContext?: LocationContext;
  showLocationFilter?: boolean;
  maxItems?: number;
}

const CATEGORY_COLORS = {
  accommodation: 'bg-blue-100 text-blue-800',
  transportation: 'bg-green-100 text-green-800',
  food: 'bg-orange-100 text-orange-800',
  safety: 'bg-red-100 text-red-800',
  attractions: 'bg-purple-100 text-purple-800',
  weather: 'bg-cyan-100 text-cyan-800',
  shopping: 'bg-pink-100 text-pink-800',
  entertainment: 'bg-yellow-100 text-yellow-800',
  services: 'bg-gray-100 text-gray-800'
};

const LocalInfo: React.FC<LocalInfoProps> = ({
  items = [],
  locationContext,
  showLocationFilter = false,
  maxItems = 10
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);

  // Use location-based content if context is provided
  const {
    data: locationData,
    isLoading: locationLoading
  } = useLocationBasedContent(
    locationContext || {},
    {
      category: selectedCategory !== 'all' ? selectedCategory : undefined,
      featured: showFeaturedOnly || undefined,
      active: true
    },
    { enabled: !!locationContext }
  );

  // Use location-based data if available, otherwise use provided items
  const displayItems = locationContext && locationData
    ? locationData.localInfo.slice(0, maxItems)
    : items.slice(0, maxItems);

  // Get unique categories from items
  const availableCategories = React.useMemo(() => {
    const categories = new Set<string>();
    displayItems.forEach(item => {
      if (item.category) categories.add(item.category);
      if ('type' in item && item.type) categories.add(item.type);
    });
    return Array.from(categories);
  }, [displayItems]);

  // Filter items based on selected filters
  const filteredItems = React.useMemo(() => {
    return displayItems.filter(item => {
      const matchesCategory = selectedCategory === 'all' ||
        item.category === selectedCategory ||
        ('type' in item && item.type === selectedCategory);

      const matchesFeatured = !showFeaturedOnly || ('is_featured' in item && item.is_featured);

      return matchesCategory && matchesFeatured;
    });
  }, [displayItems, selectedCategory, showFeaturedOnly]);

  if (locationLoading) {
    return (
      <div className="grid gap-4">
        <div className="animate-pulse">
          <div className="bg-white/5 rounded-lg p-4 border border-white/10">
            <div className="h-4 bg-white/10 rounded mb-2"></div>
            <div className="h-3 bg-white/10 rounded mb-2"></div>
            <div className="h-3 bg-white/10 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Location-based header */}
      {locationContext && locationData && (
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium">
              Location-based recommendations
            </span>
            {locationData.locationScore > 0 && (
              <Badge variant="secondary" className="text-xs">
                {locationData.locationScore}% relevant
              </Badge>
            )}
          </div>
        </div>
      )}

      {/* Filters */}
      {showLocationFilter && availableCategories.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4">
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-48">
              <Filter className="mr-2 h-4 w-4" />
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {availableCategories.map(category => (
                <SelectItem key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant={showFeaturedOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowFeaturedOnly(!showFeaturedOnly)}
          >
            <Star className="mr-1 h-3 w-3" />
            Featured Only
          </Button>
        </div>
      )}

      {/* Local info items */}
      <div className="grid gap-4">
        {filteredItems.length === 0 ? (
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 text-center">
            <MapPin className="w-8 h-8 text-white/40 mx-auto mb-2" />
            <p className="text-white/60">No local information available</p>
            {selectedCategory !== 'all' && (
              <p className="text-xs text-white/40 mt-1">
                Try selecting a different category
              </p>
            )}
          </div>
        ) : (
          filteredItems.map((item) => (
            <div
              key={item.id}
              className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10 hover:bg-white/10 transition-colors"
            >
              <div className="flex items-start justify-between mb-2">
                <h3 className="text-lg font-semibold text-white">{item.title}</h3>
                <div className="flex items-center gap-1">
                  {'is_featured' in item && item.is_featured && (
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  )}
                  {'priority' in item && item.priority && item.priority > 7 && (
                    <Badge variant="secondary" className="text-xs">
                      High Priority
                    </Badge>
                  )}
                </div>
              </div>

              <p className="text-sm text-white/70 mb-3">{item.description || 'No description available'}</p>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge
                    className={
                      CATEGORY_COLORS[item.category as keyof typeof CATEGORY_COLORS] ||
                      CATEGORY_COLORS.services
                    }
                  >
                    {item.category || ('type' in item ? item.type : '')}
                  </Badge>

                  {'locationScore' in item && item.locationScore && item.locationScore > 0 && (
                    <Badge variant="outline" className="text-xs">
                      {item.locationScore}% match
                    </Badge>
                  )}
                </div>

                {item.link && (
                  <Button
                    variant="ghost"
                    size="sm"
                    asChild
                    className="text-white/70 hover:text-white"
                  >
                    <a
                      href={item.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1"
                    >
                      <ExternalLink className="w-3 h-3" />
                      <span className="text-xs">View</span>
                    </a>
                  </Button>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default LocalInfo;

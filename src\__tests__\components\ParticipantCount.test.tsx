/**
 * ParticipantCount Component Tests
 * 
 * Tests for the simplified ParticipantCount component that replaces
 * the complex legacy implementation from JoinLeaveButton.
 * 
 * @module ParticipantCount.test
 * @version 1.0.0
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ParticipantCount } from '@/components/activities/ParticipantCount';
import { useUserInteractions } from '@/hooks/useUserInteractions';

// Mock the user interactions hook
vi.mock('@/hooks/useUserInteractions', () => ({
  useUserInteractions: vi.fn(() => ({
    participantCount: 5,
    isLoading: false,
    error: null
  }))
}));

// Test wrapper with React Query
const createTestWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('ParticipantCount', () => {
  let TestWrapper: ReturnType<typeof createTestWrapper>;

  beforeEach(() => {
    TestWrapper = createTestWrapper();
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders participant count correctly', () => {
      render(
        <ParticipantCount activityId="test-activity-1" />,
        { wrapper: TestWrapper }
      );

      expect(screen.getByText('5 participants')).toBeInTheDocument();
    });

    it('renders singular form for one participant', () => {
      vi.mocked(useUserInteractions).mockReturnValue({
        participantCount: 1,
        isLoading: false,
        error: null,
        userStatus: null,
        attendanceCounts: null,
        joinActivity: vi.fn(),
        leaveActivity: vi.fn(),
        toggleFavorite: vi.fn(),
        setAttendanceStatus: vi.fn(),
        refreshStatus: vi.fn(),
        subscribeToUpdates: vi.fn(),
        unsubscribeFromUpdates: vi.fn()
      });

      render(
        <ParticipantCount activityId="test-activity-1" />,
        { wrapper: TestWrapper }
      );

      expect(screen.getByText('1 participant')).toBeInTheDocument();
    });

    it('renders plural form for multiple participants', () => {
      vi.mocked(useUserInteractions).mockReturnValue({
        participantCount: 10,
        isLoading: false,
        error: null,
        userStatus: null,
        attendanceCounts: null,
        joinActivity: vi.fn(),
        leaveActivity: vi.fn(),
        toggleFavorite: vi.fn(),
        setAttendanceStatus: vi.fn(),
        refreshStatus: vi.fn(),
        subscribeToUpdates: vi.fn(),
        unsubscribeFromUpdates: vi.fn()
      });

      render(
        <ParticipantCount activityId="test-activity-1" />,
        { wrapper: TestWrapper }
      );

      expect(screen.getByText('10 participants')).toBeInTheDocument();
    });

    it('renders zero participants correctly', () => {
      vi.mocked(useUserInteractions).mockReturnValue({
        participantCount: 0,
        isLoading: false,
        error: null,
        userStatus: null,
        attendanceCounts: null,
        joinActivity: vi.fn(),
        leaveActivity: vi.fn(),
        toggleFavorite: vi.fn(),
        setAttendanceStatus: vi.fn(),
        refreshStatus: vi.fn(),
        subscribeToUpdates: vi.fn(),
        unsubscribeFromUpdates: vi.fn()
      });

      render(
        <ParticipantCount activityId="test-activity-1" />,
        { wrapper: TestWrapper }
      );

      expect(screen.getByText('0 participants')).toBeInTheDocument();
    });
  });

  describe('Icon Display', () => {
    it('shows icon by default', () => {
      render(
        <ParticipantCount activityId="test-activity-1" />,
        { wrapper: TestWrapper }
      );

      // Check for Users icon (Lucide React icon)
      const iconElement = screen.getByText('5 participants').parentElement;
      expect(iconElement).toBeInTheDocument();
    });

    it('hides icon when showIcon is false', () => {
      render(
        <ParticipantCount 
          activityId="test-activity-1" 
          showIcon={false}
        />,
        { wrapper: TestWrapper }
      );

      expect(screen.getByText('5 participants')).toBeInTheDocument();
    });
  });

  describe('Size Variants', () => {
    it('applies small size correctly', () => {
      render(
        <ParticipantCount 
          activityId="test-activity-1" 
          size="sm"
        />,
        { wrapper: TestWrapper }
      );

      const element = screen.getByText('5 participants').parentElement;
      expect(element).toHaveClass('text-xs');
    });

    it('applies medium size correctly (default)', () => {
      render(
        <ParticipantCount 
          activityId="test-activity-1" 
          size="md"
        />,
        { wrapper: TestWrapper }
      );

      const element = screen.getByText('5 participants').parentElement;
      expect(element).toHaveClass('text-sm');
    });

    it('applies large size correctly', () => {
      render(
        <ParticipantCount 
          activityId="test-activity-1" 
          size="lg"
        />,
        { wrapper: TestWrapper }
      );

      const element = screen.getByText('5 participants').parentElement;
      expect(element).toHaveClass('text-base');
    });
  });

  describe('Custom Styling', () => {
    it('applies custom className', () => {
      render(
        <ParticipantCount 
          activityId="test-activity-1" 
          className="custom-class"
        />,
        { wrapper: TestWrapper }
      );

      const element = screen.getByText('5 participants').parentElement;
      expect(element).toHaveClass('custom-class');
    });

    it('maintains default classes with custom className', () => {
      render(
        <ParticipantCount 
          activityId="test-activity-1" 
          className="custom-class"
        />,
        { wrapper: TestWrapper }
      );

      const element = screen.getByText('5 participants').parentElement;
      expect(element).toHaveClass('flex', 'items-center', 'gap-1', 'custom-class');
    });
  });

  describe('Loading States', () => {
    it('handles loading state gracefully', () => {
      vi.mocked(useUserInteractions).mockReturnValue({
        participantCount: 0,
        isLoading: true,
        error: null,
        userStatus: null,
        attendanceCounts: null,
        joinActivity: vi.fn(),
        leaveActivity: vi.fn(),
        toggleFavorite: vi.fn(),
        setAttendanceStatus: vi.fn(),
        refreshStatus: vi.fn(),
        subscribeToUpdates: vi.fn(),
        unsubscribeFromUpdates: vi.fn()
      });

      render(
        <ParticipantCount activityId="test-activity-1" />,
        { wrapper: TestWrapper }
      );

      // Should still render with 0 participants during loading
      expect(screen.getByText('0 participants')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles error state gracefully', () => {
      vi.mocked(useUserInteractions).mockReturnValue({
        participantCount: 0,
        isLoading: false,
        error: 'Failed to fetch participants',
        userStatus: null,
        attendanceCounts: null,
        joinActivity: vi.fn(),
        leaveActivity: vi.fn(),
        toggleFavorite: vi.fn(),
        setAttendanceStatus: vi.fn(),
        refreshStatus: vi.fn(),
        subscribeToUpdates: vi.fn(),
        unsubscribeFromUpdates: vi.fn()
      });

      render(
        <ParticipantCount activityId="test-activity-1" />,
        { wrapper: TestWrapper }
      );

      // Should fallback to 0 participants on error
      expect(screen.getByText('0 participants')).toBeInTheDocument();
    });
  });

  describe('Legacy Component Replacement', () => {
    it('provides same functionality as legacy ParticipantCount', () => {
      // Test that it provides the same essential functionality
      // as the complex ParticipantCount from JoinLeaveButton
      render(
        <ParticipantCount activityId="activity-123" />,
        { wrapper: TestWrapper }
      );

      // Should display participant count
      expect(screen.getByText('5 participants')).toBeInTheDocument();
      
      // Should have proper semantic structure
      const element = screen.getByText('5 participants').parentElement;
      expect(element).toHaveClass('flex', 'items-center');
    });

    it('is simpler than legacy implementation', () => {
      // This test documents that the new implementation is simpler
      // The legacy ParticipantCount was part of a 200+ line complex component
      // This new implementation is ~50 lines and focused on one responsibility
      
      render(
        <ParticipantCount activityId="activity-123" />,
        { wrapper: TestWrapper }
      );

      // Simple, focused functionality
      expect(screen.getByText('5 participants')).toBeInTheDocument();
      
      // No complex state management or multiple responsibilities
      // Just displays participant count with proper formatting
    });
  });

  describe('Accessibility', () => {
    it('has proper semantic structure', () => {
      render(
        <ParticipantCount activityId="test-activity-1" />,
        { wrapper: TestWrapper }
      );

      const element = screen.getByText('5 participants').parentElement;
      expect(element).toHaveClass('flex', 'items-center');
    });

    it('provides meaningful text content', () => {
      render(
        <ParticipantCount activityId="test-activity-1" />,
        { wrapper: TestWrapper }
      );

      // Text should be descriptive and meaningful
      expect(screen.getByText('5 participants')).toBeInTheDocument();
    });
  });
});

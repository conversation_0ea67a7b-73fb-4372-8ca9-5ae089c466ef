/**
 * useUserInteractions Hook
 * 
 * React hook providing a clean interface to user interaction functionality:
 * - Activity participation (join/leave)
 * - User favorites (add/remove)
 * - Activity attendance (RSVP status)
 * - Real-time updates
 * 
 * This hook manages state, loading indicators, error handling, and provides
 * optimistic updates for better user experience.
 * 
 * @module useUserInteractions
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { toast } from 'react-hot-toast'
import { userInteractionService } from '@/lib/supabase/services/user-interaction-service'
import { useAuth } from '@/providers/ConsolidatedAuthProvider'
import type { 
  AttendanceStatus, 
  ParticipantCounts, 
  UserInteractionStatus 
} from '@/lib/supabase/services/user-interaction-service'

// ============================================================================
// HOOK INTERFACE
// ============================================================================

export interface UseUserInteractionsReturn {
  // State
  isLoading: boolean
  error: string | null
  userStatus: UserInteractionStatus | null
  participantCount: number
  attendanceCounts: ParticipantCounts | null

  // Actions
  joinActivity: () => Promise<void>
  leaveActivity: () => Promise<void>
  toggleFavorite: () => Promise<void>
  setAttendanceStatus: (status: AttendanceStatus) => Promise<void>
  refreshStatus: () => Promise<void>

  // Real-time subscriptions
  subscribeToUpdates: () => void
  unsubscribeFromUpdates: () => void
}

// ============================================================================
// HOOK IMPLEMENTATION
// ============================================================================

export function useUserInteractions(activityId: string): UseUserInteractionsReturn {
  const { user } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [userStatus, setUserStatus] = useState<UserInteractionStatus | null>(null)
  const [participantCount, setParticipantCount] = useState(0)
  const [attendanceCounts, setAttendanceCounts] = useState<ParticipantCounts | null>(null)
  const subscriptionsRef = useRef<string[]>([])

  // ========================================================================
  // UTILITY FUNCTIONS
  // ========================================================================

  const handleError = useCallback((error: any, action: string) => {
    const message = error?.message || `Failed to ${action}`
    setError(message)
    toast.error(message)
    console.error(`User interaction error (${action}):`, error)
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // ========================================================================
  // DATA FETCHING
  // ========================================================================

  // Removed individual fetch functions to prevent dependency loops
  // All fetching is now consolidated in refreshStatus

  const refreshStatus = useCallback(async () => {
    if (!user?.id || !activityId) return

    setIsLoading(true)
    clearError()

    try {
      // Fetch user status
      const userStatusResponse = await userInteractionService.getUserInteractionStatus(user.id, activityId)
      if (userStatusResponse.status === 'success' && userStatusResponse.data) {
        setUserStatus(userStatusResponse.data)
      }

      // Fetch participant count
      const countResponse = await userInteractionService.getParticipantCount(activityId)
      if (countResponse.status === 'success' && countResponse.data !== null) {
        setParticipantCount(countResponse.data)
      }

      // Fetch attendance counts
      const attendanceResponse = await userInteractionService.getAttendanceCounts(activityId)
      if (attendanceResponse.status === 'success' && attendanceResponse.data) {
        setAttendanceCounts(attendanceResponse.data)
      }
    } catch (error) {
      handleError(error, 'refresh status')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id, activityId, clearError, handleError])

  // ========================================================================
  // USER ACTIONS
  // ========================================================================

  const joinActivity = useCallback(async () => {
    if (!user?.id || !activityId || isLoading) return

    setIsLoading(true)
    clearError()

    // Optimistic update
    setUserStatus(prev => prev ? { ...prev, is_participant: true } : null)
    setParticipantCount(prev => prev + 1)

    try {
      const response = await userInteractionService.joinActivity(user.id, activityId)
      if (response.status === 'success') {
        toast.success('Successfully joined activity!')
        await refreshStatus() // Refresh to get accurate data
      } else {
        // Revert optimistic update
        setUserStatus(prev => prev ? { ...prev, is_participant: false } : null)
        setParticipantCount(prev => Math.max(0, prev - 1))
        handleError(response.error, 'join activity')
      }
    } catch (error) {
      // Revert optimistic update
      setUserStatus(prev => prev ? { ...prev, is_participant: false } : null)
      setParticipantCount(prev => Math.max(0, prev - 1))
      handleError(error, 'join activity')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id, activityId, isLoading, clearError, handleError, refreshStatus])

  const leaveActivity = useCallback(async () => {
    if (!user?.id || !activityId || isLoading) return

    setIsLoading(true)
    clearError()

    // Optimistic update
    setUserStatus(prev => prev ? { ...prev, is_participant: false } : null)
    setParticipantCount(prev => Math.max(0, prev - 1))

    try {
      const response = await userInteractionService.leaveActivity(user.id, activityId)
      if (response.status === 'success') {
        toast.success('Successfully left activity')
        await refreshStatus() // Refresh to get accurate data
      } else {
        // Revert optimistic update
        setUserStatus(prev => prev ? { ...prev, is_participant: true } : null)
        setParticipantCount(prev => prev + 1)
        handleError(response.error, 'leave activity')
      }
    } catch (error) {
      // Revert optimistic update
      setUserStatus(prev => prev ? { ...prev, is_participant: true } : null)
      setParticipantCount(prev => prev + 1)
      handleError(error, 'leave activity')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id, activityId, isLoading, clearError, handleError, refreshStatus])

  const toggleFavorite = useCallback(async () => {
    if (!user?.id || !activityId || isLoading) return

    setIsLoading(true)
    clearError()

    const wasLiked = userStatus?.is_favorite || false
    
    // Optimistic update
    setUserStatus(prev => prev ? { ...prev, is_favorite: !wasLiked } : null)

    try {
      const response = wasLiked 
        ? await userInteractionService.removeFromFavorites(user.id, activityId)
        : await userInteractionService.addToFavorites(user.id, activityId)

      if (response.status === 'success') {
        toast.success(wasLiked ? 'Removed from favorites' : 'Added to favorites')
        await refreshStatus() // Refresh to get accurate data
      } else {
        // Revert optimistic update
        setUserStatus(prev => prev ? { ...prev, is_favorite: wasLiked } : null)
        handleError(response.error, wasLiked ? 'remove from favorites' : 'add to favorites')
      }
    } catch (error) {
      // Revert optimistic update
      setUserStatus(prev => prev ? { ...prev, is_favorite: wasLiked } : null)
      handleError(error, wasLiked ? 'remove from favorites' : 'add to favorites')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id, activityId, isLoading, userStatus?.is_favorite, clearError, handleError, refreshStatus])

  const setAttendanceStatus = useCallback(async (status: AttendanceStatus) => {
    if (!user?.id || !activityId || isLoading) return

    setIsLoading(true)
    clearError()

    // Optimistic update
    const previousStatus = userStatus?.attendance_status
    setUserStatus(prev => prev ? { ...prev, attendance_status: status } : null)

    try {
      const response = await userInteractionService.setAttendanceStatus(user.id, activityId, status)
      if (response.status === 'success') {
        toast.success(`Status updated to ${status}`)
        await refreshStatus() // Refresh all data
      } else {
        // Revert optimistic update
        setUserStatus(prev => prev ? { ...prev, attendance_status: previousStatus } : null)
        handleError(response.error, 'update attendance status')
      }
    } catch (error) {
      // Revert optimistic update
      setUserStatus(prev => prev ? { ...prev, attendance_status: previousStatus } : null)
      handleError(error, 'update attendance status')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id, activityId, isLoading, userStatus?.attendance_status, clearError, handleError, refreshStatus])

  // ========================================================================
  // REAL-TIME SUBSCRIPTIONS
  // ========================================================================

  const subscribeToUpdates = useCallback(() => {
    if (!activityId || !user?.id) return

    // Clean up existing subscriptions first
    subscriptionsRef.current.forEach(subId => {
      userInteractionService.unsubscribe(subId)
    })
    subscriptionsRef.current = []

    const newSubscriptions: string[] = []

    // Subscribe to participant count updates
    const participantSub = userInteractionService.subscribeToParticipantUpdates(
      activityId,
      (count) => setParticipantCount(count)
    )
    newSubscriptions.push(participantSub)

    // Subscribe to attendance count updates
    const attendanceSub = userInteractionService.subscribeToAttendanceUpdates(
      activityId,
      (counts) => setAttendanceCounts(counts)
    )
    newSubscriptions.push(attendanceSub)

    // Subscribe to user's favorites updates
    const favoritesSub = userInteractionService.subscribeToUserFavorites(
      user.id,
      () => {
        // Use a simple refresh without dependencies to avoid loops
        if (user?.id && activityId) {
          userInteractionService.getUserInteractionStatus(user.id, activityId)
            .then(response => {
              if (response.status === 'success' && response.data) {
                setUserStatus(response.data)
              }
            })
            .catch(console.error)
        }
      }
    )
    newSubscriptions.push(favoritesSub)

    subscriptionsRef.current = newSubscriptions
  }, [activityId, user?.id])

  const unsubscribeFromUpdates = useCallback(() => {
    subscriptionsRef.current.forEach(subId => {
      userInteractionService.unsubscribe(subId)
    })
    subscriptionsRef.current = []
  }, [])

  // ========================================================================
  // EFFECTS
  // ========================================================================

  // Initial data fetch
  useEffect(() => {
    if (user?.id && activityId) {
      refreshStatus()
    }
  }, [user?.id, activityId]) // Remove refreshStatus from dependencies

  // Setup real-time subscriptions
  useEffect(() => {
    if (user?.id && activityId) {
      subscribeToUpdates()
    }
    return () => unsubscribeFromUpdates()
  }, [user?.id, activityId]) // Only depend on user and activity ID

  // ========================================================================
  // RETURN INTERFACE
  // ========================================================================

  return {
    // State
    isLoading,
    error,
    userStatus,
    participantCount,
    attendanceCounts,

    // Actions
    joinActivity,
    leaveActivity,
    toggleFavorite,
    setAttendanceStatus,
    refreshStatus,

    // Real-time subscriptions
    subscribeToUpdates,
    unsubscribeFromUpdates
  }
}

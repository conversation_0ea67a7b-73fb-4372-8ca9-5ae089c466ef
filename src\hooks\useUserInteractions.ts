/**
 * useUserInteractions Hook
 * 
 * React hook providing a clean interface to user interaction functionality:
 * - Activity participation (join/leave)
 * - User favorites (add/remove)
 * - Activity attendance (RSVP status)
 * - Real-time updates
 * 
 * This hook manages state, loading indicators, error handling, and provides
 * optimistic updates for better user experience.
 * 
 * @module useUserInteractions
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { useState, useEffect, useCallback } from 'react'
import { toast } from 'react-hot-toast'
import { userInteractionService } from '@/lib/supabase/services/user-interaction-service'
import { useAuth } from '@/providers/ConsolidatedAuthProvider'
import type { 
  AttendanceStatus, 
  ParticipantCounts, 
  UserInteractionStatus 
} from '@/lib/supabase/services/user-interaction-service'

// ============================================================================
// HOOK INTERFACE
// ============================================================================

export interface UseUserInteractionsReturn {
  // State
  isLoading: boolean
  error: string | null
  userStatus: UserInteractionStatus | null
  participantCount: number
  attendanceCounts: ParticipantCounts | null

  // Actions
  joinActivity: () => Promise<void>
  leaveActivity: () => Promise<void>
  toggleFavorite: () => Promise<void>
  setAttendanceStatus: (status: AttendanceStatus) => Promise<void>
  refreshStatus: () => Promise<void>

  // Real-time subscriptions
  subscribeToUpdates: () => void
  unsubscribeFromUpdates: () => void
}

// ============================================================================
// HOOK IMPLEMENTATION
// ============================================================================

export function useUserInteractions(activityId: string): UseUserInteractionsReturn {
  const { user } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [userStatus, setUserStatus] = useState<UserInteractionStatus | null>(null)
  const [participantCount, setParticipantCount] = useState(0)
  const [attendanceCounts, setAttendanceCounts] = useState<ParticipantCounts | null>(null)
  const [subscriptions, setSubscriptions] = useState<string[]>([])

  // ========================================================================
  // UTILITY FUNCTIONS
  // ========================================================================

  const handleError = useCallback((error: any, action: string) => {
    const message = error?.message || `Failed to ${action}`
    setError(message)
    toast.error(message)
    console.error(`User interaction error (${action}):`, error)
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // ========================================================================
  // DATA FETCHING
  // ========================================================================

  const fetchUserStatus = useCallback(async () => {
    if (!user?.id || !activityId) return

    try {
      const response = await userInteractionService.getUserInteractionStatus(user.id, activityId)
      if (response.status === 'success' && response.data) {
        setUserStatus(response.data)
      } else {
        handleError(response.error, 'fetch user status')
      }
    } catch (error) {
      handleError(error, 'fetch user status')
    }
  }, [user?.id, activityId, handleError])

  const fetchParticipantCount = useCallback(async () => {
    if (!activityId) return

    try {
      const response = await userInteractionService.getParticipantCount(activityId)
      if (response.status === 'success' && response.data !== null) {
        setParticipantCount(response.data)
      } else {
        handleError(response.error, 'fetch participant count')
      }
    } catch (error) {
      handleError(error, 'fetch participant count')
    }
  }, [activityId, handleError])

  const fetchAttendanceCounts = useCallback(async () => {
    if (!activityId) return

    try {
      const response = await userInteractionService.getAttendanceCounts(activityId)
      if (response.status === 'success' && response.data) {
        setAttendanceCounts(response.data)
      } else {
        handleError(response.error, 'fetch attendance counts')
      }
    } catch (error) {
      handleError(error, 'fetch attendance counts')
    }
  }, [activityId, handleError])

  const refreshStatus = useCallback(async () => {
    setIsLoading(true)
    clearError()

    await Promise.all([
      fetchUserStatus(),
      fetchParticipantCount(),
      fetchAttendanceCounts()
    ])

    setIsLoading(false)
  }, [fetchUserStatus, fetchParticipantCount, fetchAttendanceCounts, clearError])

  // ========================================================================
  // USER ACTIONS
  // ========================================================================

  const joinActivity = useCallback(async () => {
    if (!user?.id || !activityId || isLoading) return

    setIsLoading(true)
    clearError()

    // Optimistic update
    setUserStatus(prev => prev ? { ...prev, is_participant: true } : null)
    setParticipantCount(prev => prev + 1)

    try {
      const response = await userInteractionService.joinActivity(user.id, activityId)
      if (response.status === 'success') {
        toast.success('Successfully joined activity!')
        await fetchUserStatus() // Refresh to get accurate data
      } else {
        // Revert optimistic update
        setUserStatus(prev => prev ? { ...prev, is_participant: false } : null)
        setParticipantCount(prev => Math.max(0, prev - 1))
        handleError(response.error, 'join activity')
      }
    } catch (error) {
      // Revert optimistic update
      setUserStatus(prev => prev ? { ...prev, is_participant: false } : null)
      setParticipantCount(prev => Math.max(0, prev - 1))
      handleError(error, 'join activity')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id, activityId, isLoading, clearError, handleError, fetchUserStatus])

  const leaveActivity = useCallback(async () => {
    if (!user?.id || !activityId || isLoading) return

    setIsLoading(true)
    clearError()

    // Optimistic update
    setUserStatus(prev => prev ? { ...prev, is_participant: false } : null)
    setParticipantCount(prev => Math.max(0, prev - 1))

    try {
      const response = await userInteractionService.leaveActivity(user.id, activityId)
      if (response.status === 'success') {
        toast.success('Successfully left activity')
        await fetchUserStatus() // Refresh to get accurate data
      } else {
        // Revert optimistic update
        setUserStatus(prev => prev ? { ...prev, is_participant: true } : null)
        setParticipantCount(prev => prev + 1)
        handleError(response.error, 'leave activity')
      }
    } catch (error) {
      // Revert optimistic update
      setUserStatus(prev => prev ? { ...prev, is_participant: true } : null)
      setParticipantCount(prev => prev + 1)
      handleError(error, 'leave activity')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id, activityId, isLoading, clearError, handleError, fetchUserStatus])

  const toggleFavorite = useCallback(async () => {
    if (!user?.id || !activityId || isLoading) return

    setIsLoading(true)
    clearError()

    const wasLiked = userStatus?.is_favorite || false
    
    // Optimistic update
    setUserStatus(prev => prev ? { ...prev, is_favorite: !wasLiked } : null)

    try {
      const response = wasLiked 
        ? await userInteractionService.removeFromFavorites(user.id, activityId)
        : await userInteractionService.addToFavorites(user.id, activityId)

      if (response.status === 'success') {
        toast.success(wasLiked ? 'Removed from favorites' : 'Added to favorites')
        await fetchUserStatus() // Refresh to get accurate data
      } else {
        // Revert optimistic update
        setUserStatus(prev => prev ? { ...prev, is_favorite: wasLiked } : null)
        handleError(response.error, wasLiked ? 'remove from favorites' : 'add to favorites')
      }
    } catch (error) {
      // Revert optimistic update
      setUserStatus(prev => prev ? { ...prev, is_favorite: wasLiked } : null)
      handleError(error, wasLiked ? 'remove from favorites' : 'add to favorites')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id, activityId, isLoading, userStatus?.is_favorite, clearError, handleError, fetchUserStatus])

  const setAttendanceStatus = useCallback(async (status: AttendanceStatus) => {
    if (!user?.id || !activityId || isLoading) return

    setIsLoading(true)
    clearError()

    // Optimistic update
    const previousStatus = userStatus?.attendance_status
    setUserStatus(prev => prev ? { ...prev, attendance_status: status } : null)

    try {
      const response = await userInteractionService.setAttendanceStatus(user.id, activityId, status)
      if (response.status === 'success') {
        toast.success(`Status updated to ${status}`)
        await Promise.all([fetchUserStatus(), fetchAttendanceCounts()])
      } else {
        // Revert optimistic update
        setUserStatus(prev => prev ? { ...prev, attendance_status: previousStatus } : null)
        handleError(response.error, 'update attendance status')
      }
    } catch (error) {
      // Revert optimistic update
      setUserStatus(prev => prev ? { ...prev, attendance_status: previousStatus } : null)
      handleError(error, 'update attendance status')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id, activityId, isLoading, userStatus?.attendance_status, clearError, handleError, fetchUserStatus, fetchAttendanceCounts])

  // ========================================================================
  // REAL-TIME SUBSCRIPTIONS
  // ========================================================================

  const subscribeToUpdates = useCallback(() => {
    if (!activityId || !user?.id) return

    const newSubscriptions: string[] = []

    // Subscribe to participant count updates
    const participantSub = userInteractionService.subscribeToParticipantUpdates(
      activityId,
      (count) => setParticipantCount(count)
    )
    newSubscriptions.push(participantSub)

    // Subscribe to attendance count updates
    const attendanceSub = userInteractionService.subscribeToAttendanceUpdates(
      activityId,
      (counts) => setAttendanceCounts(counts)
    )
    newSubscriptions.push(attendanceSub)

    // Subscribe to user's favorites updates
    const favoritesSub = userInteractionService.subscribeToUserFavorites(
      user.id,
      () => fetchUserStatus() // Refresh user status when favorites change
    )
    newSubscriptions.push(favoritesSub)

    setSubscriptions(newSubscriptions)
  }, [activityId, user?.id, fetchUserStatus])

  const unsubscribeFromUpdates = useCallback(() => {
    subscriptions.forEach(subId => {
      userInteractionService.unsubscribe(subId)
    })
    setSubscriptions([])
  }, [subscriptions])

  // ========================================================================
  // EFFECTS
  // ========================================================================

  // Initial data fetch
  useEffect(() => {
    if (user?.id && activityId) {
      refreshStatus()
    }
  }, [user?.id, activityId, refreshStatus])

  // Setup real-time subscriptions
  useEffect(() => {
    subscribeToUpdates()
    return () => unsubscribeFromUpdates()
  }, [subscribeToUpdates, unsubscribeFromUpdates])

  // ========================================================================
  // RETURN INTERFACE
  // ========================================================================

  return {
    // State
    isLoading,
    error,
    userStatus,
    participantCount,
    attendanceCounts,

    // Actions
    joinActivity,
    leaveActivity,
    toggleFavorite,
    setAttendanceStatus,
    refreshStatus,

    // Real-time subscriptions
    subscribeToUpdates,
    unsubscribeFromUpdates
  }
}

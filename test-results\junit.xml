<testsuites id="" name="" tests="66" failures="49" skipped="0" errors="0" time="263.10722">
<testsuite name="standardization-validation.spec.js" timestamp="2025-07-03T21:10:23.383Z" hostname="chromium" tests="11" failures="6" skipped="0" time="200.523" errors="0">
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should use React Router navigation for all internal links" classname="standardization-validation.spec.js" time="29.081">
<failure message="standardization-validation.spec.js:36:5 should use React Router navigation for all internal links" type="FAILURE">
<![CDATA[  [chromium] › standardization-validation.spec.js:36:5 › Festival Family Standardization Validation › Navigation Standardization › should use React Router navigation for all internal links 

    TimeoutError: page.goto: Timeout 20000ms exceeded.
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      35 |     
      36 |     test('should use React Router navigation for all internal links', async ({ page }) => {
    > 37 |       await page.goto('/');
         |                  ^
      38 |       
      39 |       // Track navigation events
      40 |       const navigationEvents = [];
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:37:18

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-b73f4-tion-for-all-internal-links-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\standardization-validation-b73f4-tion-for-all-internal-links-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-b73f4-tion-for-all-internal-links-chromium\video.webm]]

[[ATTACHMENT|artifacts\standardization-validation-b73f4-tion-for-all-internal-links-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should handle tab navigation with URL parameters" classname="standardization-validation.spec.js" time="14.103">
<failure message="standardization-validation.spec.js:78:5 should handle tab navigation with URL parameters" type="FAILURE">
<![CDATA[  [chromium] › standardization-validation.spec.js:78:5 › Festival Family Standardization Validation › Navigation Standardization › should handle tab navigation with URL parameters 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button:has-text("CHAT")').first()


      87 |         // Click tab
      88 |         const tabButton = page.locator(`button:has-text("${tab.replace('_', ' ')}")`).first();
    > 89 |         await tabButton.click();
         |                         ^
      90 |         
      91 |         const endTime = performance.now();
      92 |         const tabSwitchTime = endTime - startTime;
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:89:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-e06c3-igation-with-URL-parameters-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-e06c3-igation-with-URL-parameters-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\standardization-validation-e06c3-igation-with-URL-parameters-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-e06c3-igation-with-URL-parameters-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\standardization-validation-e06c3-igation-with-URL-parameters-chromium\video.webm]]

[[ATTACHMENT|artifacts\standardization-validation-e06c3-igation-with-URL-parameters-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should not use window.location or window.open for internal navigation" classname="standardization-validation.spec.js" time="35.851">
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use UnifiedInteractionButton consistently" classname="standardization-validation.spec.js" time="8.057">
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use BentoCard components consistently" classname="standardization-validation.spec.js" time="14.194">
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use EnhancedUnifiedBadge with color mapping" classname="standardization-validation.spec.js" time="7.719">
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should load all sections under 200ms" classname="standardization-validation.spec.js" time="2.766">
<failure message="standardization-validation.spec.js:230:5 should load all sections under 200ms" type="FAILURE">
<![CDATA[  [chromium] › standardization-validation.spec.js:230:5 › Festival Family Standardization Validation › Performance Validation › should load all sections under 200ms 

    Error: expect(received).toBeLessThan(expected)

    Expected: < 200
    Received:   614.3061000000016

      239 |         
      240 |         console.log(`${section} load time: ${loadTime.toFixed(2)}ms`);
    > 241 |         expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLD);
          |                          ^
      242 |       }
      243 |     });
      244 |
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:241:26

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[/ load time: 614.31ms

[[ATTACHMENT|artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-chromium\video.webm]]

[[ATTACHMENT|artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should have fast interaction response times" classname="standardization-validation.spec.js" time="22.321">
<failure message="standardization-validation.spec.js:245:5 should have fast interaction response times" type="FAILURE">
<![CDATA[  [chromium] › standardization-validation.spec.js:245:5 › Festival Family Standardization Validation › Performance Validation › should have fast interaction response times 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.

      245 |     test('should have fast interaction response times', async ({ page }) => {
      246 |       await page.goto('/activities');
    > 247 |       await page.waitForLoadState('networkidle');
          |                  ^
      248 |       
      249 |       // Test button interactions
      250 |       const buttons = page.locator('button:visible').first();
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:247:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-13ea8--interaction-response-times-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-13ea8--interaction-response-times-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-13ea8--interaction-response-times-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\standardization-validation-13ea8--interaction-response-times-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should not have infinite loops or performance issues" classname="standardization-validation.spec.js" time="22.203">
</testcase>
<testcase name="Festival Family Standardization Validation › Cross-Section Consistency › should have consistent header and navigation across all sections" classname="standardization-validation.spec.js" time="20.059">
<failure message="standardization-validation.spec.js:294:5 should have consistent header and navigation across all sections" type="FAILURE">
<![CDATA[  [chromium] › standardization-validation.spec.js:294:5 › Festival Family Standardization Validation › Cross-Section Consistency › should have consistent header and navigation across all sections 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 2828.8
    Received:   0

      311 |           
      312 |           // Headers should be structurally similar
    > 313 |           expect(normalizedCurrent.length).toBeGreaterThan(normalizedOriginal.length * 0.8);
          |                                            ^
      314 |           expect(normalizedCurrent.length).toBeLessThan(normalizedOriginal.length * 1.2);
      315 |         }
      316 |       }
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:313:44

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-3c29f-igation-across-all-sections-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-3c29f-igation-across-all-sections-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\standardization-validation-3c29f-igation-across-all-sections-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-3c29f-igation-across-all-sections-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\standardization-validation-3c29f-igation-across-all-sections-chromium\video.webm]]

[[ATTACHMENT|artifacts\standardization-validation-3c29f-igation-across-all-sections-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Cross-Section Consistency › should have consistent styling and theme across sections" classname="standardization-validation.spec.js" time="24.169">
<failure message="standardization-validation.spec.js:319:5 should have consistent styling and theme across sections" type="FAILURE">
<![CDATA[  [chromium] › standardization-validation.spec.js:319:5 › Festival Family Standardization Validation › Cross-Section Consistency › should have consistent styling and theme across sections 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.

      322 |       for (const section of SECTIONS) {
      323 |         await page.goto(section);
    > 324 |         await page.waitForLoadState('networkidle');
          |                    ^
      325 |         
      326 |         // Get computed styles of main elements
      327 |         const bodyStyles = await page.evaluate(() => {
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:324:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-16594-g-and-theme-across-sections-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-16594-g-and-theme-across-sections-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-16594-g-and-theme-across-sections-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\standardization-validation-16594-g-and-theme-across-sections-chromium\video.webm]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="standardization-validation.spec.js" timestamp="2025-07-03T21:10:23.383Z" hostname="firefox" tests="11" failures="11" skipped="0" time="0.356" errors="0">
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should use React Router navigation for all internal links" classname="standardization-validation.spec.js" time="0.019">
<failure message="standardization-validation.spec.js:36:5 should use React Router navigation for all internal links" type="FAILURE">
<![CDATA[  [firefox] › standardization-validation.spec.js:36:5 › Festival Family Standardization Validation › Navigation Standardization › should use React Router navigation for all internal links 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\standardization-validation-b73f4-tion-for-all-internal-links-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-b73f4-tion-for-all-internal-links-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should handle tab navigation with URL parameters" classname="standardization-validation.spec.js" time="0.059">
<failure message="standardization-validation.spec.js:78:5 should handle tab navigation with URL parameters" type="FAILURE">
<![CDATA[  [firefox] › standardization-validation.spec.js:78:5 › Festival Family Standardization Validation › Navigation Standardization › should handle tab navigation with URL parameters 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\standardization-validation-e06c3-igation-with-URL-parameters-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-e06c3-igation-with-URL-parameters-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should not use window.location or window.open for internal navigation" classname="standardization-validation.spec.js" time="0.019">
<failure message="standardization-validation.spec.js:106:5 should not use window.location or window.open for internal navigation" type="FAILURE">
<![CDATA[  [firefox] › standardization-validation.spec.js:106:5 › Festival Family Standardization Validation › Navigation Standardization › should not use window.location or window.open for internal navigation 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\standardization-validation-9cd4b-pen-for-internal-navigation-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-9cd4b-pen-for-internal-navigation-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use UnifiedInteractionButton consistently" classname="standardization-validation.spec.js" time="0.014">
<failure message="standardization-validation.spec.js:150:5 should use UnifiedInteractionButton consistently" type="FAILURE">
<![CDATA[  [firefox] › standardization-validation.spec.js:150:5 › Festival Family Standardization Validation › Component Standardization › should use UnifiedInteractionButton consistently 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\standardization-validation-2fd16-eractionButton-consistently-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-2fd16-eractionButton-consistently-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use BentoCard components consistently" classname="standardization-validation.spec.js" time="0.016">
<failure message="standardization-validation.spec.js:170:5 should use BentoCard components consistently" type="FAILURE">
<![CDATA[  [firefox] › standardization-validation.spec.js:170:5 › Festival Family Standardization Validation › Component Standardization › should use BentoCard components consistently 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\standardization-validation-d93a0-ard-components-consistently-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-d93a0-ard-components-consistently-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use EnhancedUnifiedBadge with color mapping" classname="standardization-validation.spec.js" time="0.012">
<failure message="standardization-validation.spec.js:197:5 should use EnhancedUnifiedBadge with color mapping" type="FAILURE">
<![CDATA[  [firefox] › standardization-validation.spec.js:197:5 › Festival Family Standardization Validation › Component Standardization › should use EnhancedUnifiedBadge with color mapping 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\standardization-validation-6850b-iedBadge-with-color-mapping-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-6850b-iedBadge-with-color-mapping-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should load all sections under 200ms" classname="standardization-validation.spec.js" time="0.022">
<failure message="standardization-validation.spec.js:230:5 should load all sections under 200ms" type="FAILURE">
<![CDATA[  [firefox] › standardization-validation.spec.js:230:5 › Festival Family Standardization Validation › Performance Validation › should load all sections under 200ms 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should have fast interaction response times" classname="standardization-validation.spec.js" time="0.025">
<failure message="standardization-validation.spec.js:245:5 should have fast interaction response times" type="FAILURE">
<![CDATA[  [firefox] › standardization-validation.spec.js:245:5 › Festival Family Standardization Validation › Performance Validation › should have fast interaction response times 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\standardization-validation-13ea8--interaction-response-times-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-13ea8--interaction-response-times-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should not have infinite loops or performance issues" classname="standardization-validation.spec.js" time="0.016">
<failure message="standardization-validation.spec.js:267:5 should not have infinite loops or performance issues" type="FAILURE">
<![CDATA[  [firefox] › standardization-validation.spec.js:267:5 › Festival Family Standardization Validation › Performance Validation › should not have infinite loops or performance issues 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\standardization-validation-d42cc-loops-or-performance-issues-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-d42cc-loops-or-performance-issues-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Cross-Section Consistency › should have consistent header and navigation across all sections" classname="standardization-validation.spec.js" time="0.053">
<failure message="standardization-validation.spec.js:294:5 should have consistent header and navigation across all sections" type="FAILURE">
<![CDATA[  [firefox] › standardization-validation.spec.js:294:5 › Festival Family Standardization Validation › Cross-Section Consistency › should have consistent header and navigation across all sections 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\standardization-validation-3c29f-igation-across-all-sections-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-3c29f-igation-across-all-sections-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Cross-Section Consistency › should have consistent styling and theme across sections" classname="standardization-validation.spec.js" time="0.101">
<failure message="standardization-validation.spec.js:319:5 should have consistent styling and theme across sections" type="FAILURE">
<![CDATA[  [firefox] › standardization-validation.spec.js:319:5 › Festival Family Standardization Validation › Cross-Section Consistency › should have consistent styling and theme across sections 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\standardization-validation-16594-g-and-theme-across-sections-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-16594-g-and-theme-across-sections-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="standardization-validation.spec.js" timestamp="2025-07-03T21:10:23.383Z" hostname="webkit" tests="11" failures="11" skipped="0" time="120.466" errors="0">
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should use React Router navigation for all internal links" classname="standardization-validation.spec.js" time="11.036">
<failure message="standardization-validation.spec.js:36:5 should use React Router navigation for all internal links" type="FAILURE">
<![CDATA[  [webkit] › standardization-validation.spec.js:36:5 › Festival Family Standardization Validation › Navigation Standardization › should use React Router navigation for all internal links 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should handle tab navigation with URL parameters" classname="standardization-validation.spec.js" time="10.523">
<failure message="standardization-validation.spec.js:78:5 should handle tab navigation with URL parameters" type="FAILURE">
<![CDATA[  [webkit] › standardization-validation.spec.js:78:5 › Festival Family Standardization Validation › Navigation Standardization › should handle tab navigation with URL parameters 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should not use window.location or window.open for internal navigation" classname="standardization-validation.spec.js" time="9.767">
<failure message="standardization-validation.spec.js:106:5 should not use window.location or window.open for internal navigation" type="FAILURE">
<![CDATA[  [webkit] › standardization-validation.spec.js:106:5 › Festival Family Standardization Validation › Navigation Standardization › should not use window.location or window.open for internal navigation 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use UnifiedInteractionButton consistently" classname="standardization-validation.spec.js" time="13.592">
<failure message="standardization-validation.spec.js:150:5 should use UnifiedInteractionButton consistently" type="FAILURE">
<![CDATA[  [webkit] › standardization-validation.spec.js:150:5 › Festival Family Standardization Validation › Component Standardization › should use UnifiedInteractionButton consistently 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use BentoCard components consistently" classname="standardization-validation.spec.js" time="12.481">
<failure message="standardization-validation.spec.js:170:5 should use BentoCard components consistently" type="FAILURE">
<![CDATA[  [webkit] › standardization-validation.spec.js:170:5 › Festival Family Standardization Validation › Component Standardization › should use BentoCard components consistently 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use EnhancedUnifiedBadge with color mapping" classname="standardization-validation.spec.js" time="11.06">
<failure message="standardization-validation.spec.js:197:5 should use EnhancedUnifiedBadge with color mapping" type="FAILURE">
<![CDATA[  [webkit] › standardization-validation.spec.js:197:5 › Festival Family Standardization Validation › Component Standardization › should use EnhancedUnifiedBadge with color mapping 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should load all sections under 200ms" classname="standardization-validation.spec.js" time="10.483">
<failure message="standardization-validation.spec.js:230:5 should load all sections under 200ms" type="FAILURE">
<![CDATA[  [webkit] › standardization-validation.spec.js:230:5 › Festival Family Standardization Validation › Performance Validation › should load all sections under 200ms 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should have fast interaction response times" classname="standardization-validation.spec.js" time="11.46">
<failure message="standardization-validation.spec.js:245:5 should have fast interaction response times" type="FAILURE">
<![CDATA[  [webkit] › standardization-validation.spec.js:245:5 › Festival Family Standardization Validation › Performance Validation › should have fast interaction response times 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should not have infinite loops or performance issues" classname="standardization-validation.spec.js" time="9.029">
<failure message="standardization-validation.spec.js:267:5 should not have infinite loops or performance issues" type="FAILURE">
<![CDATA[  [webkit] › standardization-validation.spec.js:267:5 › Festival Family Standardization Validation › Performance Validation › should not have infinite loops or performance issues 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Cross-Section Consistency › should have consistent header and navigation across all sections" classname="standardization-validation.spec.js" time="11.207">
<failure message="standardization-validation.spec.js:294:5 should have consistent header and navigation across all sections" type="FAILURE">
<![CDATA[  [webkit] › standardization-validation.spec.js:294:5 › Festival Family Standardization Validation › Cross-Section Consistency › should have consistent header and navigation across all sections 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Cross-Section Consistency › should have consistent styling and theme across sections" classname="standardization-validation.spec.js" time="9.828">
<failure message="standardization-validation.spec.js:319:5 should have consistent styling and theme across sections" type="FAILURE">
<![CDATA[  [webkit] › standardization-validation.spec.js:319:5 › Festival Family Standardization Validation › Cross-Section Consistency › should have consistent styling and theme across sections 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="standardization-validation.spec.js" timestamp="2025-07-03T21:10:23.383Z" hostname="Mobile Chrome" tests="11" failures="5" skipped="0" time="210.091" errors="0">
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should use React Router navigation for all internal links" classname="standardization-validation.spec.js" time="28.829">
<failure message="standardization-validation.spec.js:36:5 should use React Router navigation for all internal links" type="FAILURE">
<![CDATA[  [Mobile Chrome] › standardization-validation.spec.js:36:5 › Festival Family Standardization Validation › Navigation Standardization › should use React Router navigation for all internal links 

    TimeoutError: page.goto: Timeout 20000ms exceeded.
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      35 |     
      36 |     test('should use React Router navigation for all internal links', async ({ page }) => {
    > 37 |       await page.goto('/');
         |                  ^
      38 |       
      39 |       // Track navigation events
      40 |       const navigationEvents = [];
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:37:18

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-b73f4-tion-for-all-internal-links-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\standardization-validation-b73f4-tion-for-all-internal-links-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-b73f4-tion-for-all-internal-links-Mobile-Chrome\video.webm]]

[[ATTACHMENT|artifacts\standardization-validation-b73f4-tion-for-all-internal-links-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should handle tab navigation with URL parameters" classname="standardization-validation.spec.js" time="13.359">
<failure message="standardization-validation.spec.js:78:5 should handle tab navigation with URL parameters" type="FAILURE">
<![CDATA[  [Mobile Chrome] › standardization-validation.spec.js:78:5 › Festival Family Standardization Validation › Navigation Standardization › should handle tab navigation with URL parameters 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button:has-text("CHAT")').first()


      87 |         // Click tab
      88 |         const tabButton = page.locator(`button:has-text("${tab.replace('_', ' ')}")`).first();
    > 89 |         await tabButton.click();
         |                         ^
      90 |         
      91 |         const endTime = performance.now();
      92 |         const tabSwitchTime = endTime - startTime;
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:89:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-e06c3-igation-with-URL-parameters-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-e06c3-igation-with-URL-parameters-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\standardization-validation-e06c3-igation-with-URL-parameters-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-e06c3-igation-with-URL-parameters-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|artifacts\standardization-validation-e06c3-igation-with-URL-parameters-Mobile-Chrome\video.webm]]

[[ATTACHMENT|artifacts\standardization-validation-e06c3-igation-with-URL-parameters-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should not use window.location or window.open for internal navigation" classname="standardization-validation.spec.js" time="35.997">
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use UnifiedInteractionButton consistently" classname="standardization-validation.spec.js" time="7.979">
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use BentoCard components consistently" classname="standardization-validation.spec.js" time="13.84">
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use EnhancedUnifiedBadge with color mapping" classname="standardization-validation.spec.js" time="7.997">
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should load all sections under 200ms" classname="standardization-validation.spec.js" time="2.392">
<failure message="standardization-validation.spec.js:230:5 should load all sections under 200ms" type="FAILURE">
<![CDATA[  [Mobile Chrome] › standardization-validation.spec.js:230:5 › Festival Family Standardization Validation › Performance Validation › should load all sections under 200ms 

    Error: expect(received).toBeLessThan(expected)

    Expected: < 200
    Received:   601.527900000001

      239 |         
      240 |         console.log(`${section} load time: ${loadTime.toFixed(2)}ms`);
    > 241 |         expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLD);
          |                          ^
      242 |       }
      243 |     });
      244 |
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:241:26

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[/ load time: 601.53ms

[[ATTACHMENT|artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-Mobile-Chrome\video.webm]]

[[ATTACHMENT|artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should have fast interaction response times" classname="standardization-validation.spec.js" time="23.002">
<failure message="standardization-validation.spec.js:245:5 should have fast interaction response times" type="FAILURE">
<![CDATA[  [Mobile Chrome] › standardization-validation.spec.js:245:5 › Festival Family Standardization Validation › Performance Validation › should have fast interaction response times 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.

      245 |     test('should have fast interaction response times', async ({ page }) => {
      246 |       await page.goto('/activities');
    > 247 |       await page.waitForLoadState('networkidle');
          |                  ^
      248 |       
      249 |       // Test button interactions
      250 |       const buttons = page.locator('button:visible').first();
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:247:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-13ea8--interaction-response-times-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-13ea8--interaction-response-times-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-13ea8--interaction-response-times-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|artifacts\standardization-validation-13ea8--interaction-response-times-Mobile-Chrome\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should not have infinite loops or performance issues" classname="standardization-validation.spec.js" time="22.3">
</testcase>
<testcase name="Festival Family Standardization Validation › Cross-Section Consistency › should have consistent header and navigation across all sections" classname="standardization-validation.spec.js" time="20.27">
<failure message="standardization-validation.spec.js:294:5 should have consistent header and navigation across all sections" type="FAILURE">
<![CDATA[  [Mobile Chrome] › standardization-validation.spec.js:294:5 › Festival Family Standardization Validation › Cross-Section Consistency › should have consistent header and navigation across all sections 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 2828.8
    Received:   0

      311 |           
      312 |           // Headers should be structurally similar
    > 313 |           expect(normalizedCurrent.length).toBeGreaterThan(normalizedOriginal.length * 0.8);
          |                                            ^
      314 |           expect(normalizedCurrent.length).toBeLessThan(normalizedOriginal.length * 1.2);
      315 |         }
      316 |       }
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:313:44

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-3c29f-igation-across-all-sections-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-3c29f-igation-across-all-sections-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\standardization-validation-3c29f-igation-across-all-sections-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-3c29f-igation-across-all-sections-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|artifacts\standardization-validation-3c29f-igation-across-all-sections-Mobile-Chrome\video.webm]]

[[ATTACHMENT|artifacts\standardization-validation-3c29f-igation-across-all-sections-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Cross-Section Consistency › should have consistent styling and theme across sections" classname="standardization-validation.spec.js" time="34.126">
</testcase>
</testsuite>
<testsuite name="standardization-validation.spec.js" timestamp="2025-07-03T21:10:23.383Z" hostname="Mobile Safari" tests="11" failures="11" skipped="0" time="119.963" errors="0">
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should use React Router navigation for all internal links" classname="standardization-validation.spec.js" time="11.114">
<failure message="standardization-validation.spec.js:36:5 should use React Router navigation for all internal links" type="FAILURE">
<![CDATA[  [Mobile Safari] › standardization-validation.spec.js:36:5 › Festival Family Standardization Validation › Navigation Standardization › should use React Router navigation for all internal links 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should handle tab navigation with URL parameters" classname="standardization-validation.spec.js" time="10.363">
<failure message="standardization-validation.spec.js:78:5 should handle tab navigation with URL parameters" type="FAILURE">
<![CDATA[  [Mobile Safari] › standardization-validation.spec.js:78:5 › Festival Family Standardization Validation › Navigation Standardization › should handle tab navigation with URL parameters 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should not use window.location or window.open for internal navigation" classname="standardization-validation.spec.js" time="9.856">
<failure message="standardization-validation.spec.js:106:5 should not use window.location or window.open for internal navigation" type="FAILURE">
<![CDATA[  [Mobile Safari] › standardization-validation.spec.js:106:5 › Festival Family Standardization Validation › Navigation Standardization › should not use window.location or window.open for internal navigation 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use UnifiedInteractionButton consistently" classname="standardization-validation.spec.js" time="12.358">
<failure message="standardization-validation.spec.js:150:5 should use UnifiedInteractionButton consistently" type="FAILURE">
<![CDATA[  [Mobile Safari] › standardization-validation.spec.js:150:5 › Festival Family Standardization Validation › Component Standardization › should use UnifiedInteractionButton consistently 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use BentoCard components consistently" classname="standardization-validation.spec.js" time="13.169">
<failure message="standardization-validation.spec.js:170:5 should use BentoCard components consistently" type="FAILURE">
<![CDATA[  [Mobile Safari] › standardization-validation.spec.js:170:5 › Festival Family Standardization Validation › Component Standardization › should use BentoCard components consistently 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use EnhancedUnifiedBadge with color mapping" classname="standardization-validation.spec.js" time="11.034">
<failure message="standardization-validation.spec.js:197:5 should use EnhancedUnifiedBadge with color mapping" type="FAILURE">
<![CDATA[  [Mobile Safari] › standardization-validation.spec.js:197:5 › Festival Family Standardization Validation › Component Standardization › should use EnhancedUnifiedBadge with color mapping 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should load all sections under 200ms" classname="standardization-validation.spec.js" time="10.283">
<failure message="standardization-validation.spec.js:230:5 should load all sections under 200ms" type="FAILURE">
<![CDATA[  [Mobile Safari] › standardization-validation.spec.js:230:5 › Festival Family Standardization Validation › Performance Validation › should load all sections under 200ms 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should have fast interaction response times" classname="standardization-validation.spec.js" time="11.87">
<failure message="standardization-validation.spec.js:245:5 should have fast interaction response times" type="FAILURE">
<![CDATA[  [Mobile Safari] › standardization-validation.spec.js:245:5 › Festival Family Standardization Validation › Performance Validation › should have fast interaction response times 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should not have infinite loops or performance issues" classname="standardization-validation.spec.js" time="9.046">
<failure message="standardization-validation.spec.js:267:5 should not have infinite loops or performance issues" type="FAILURE">
<![CDATA[  [Mobile Safari] › standardization-validation.spec.js:267:5 › Festival Family Standardization Validation › Performance Validation › should not have infinite loops or performance issues 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Cross-Section Consistency › should have consistent header and navigation across all sections" classname="standardization-validation.spec.js" time="11.156">
<failure message="standardization-validation.spec.js:294:5 should have consistent header and navigation across all sections" type="FAILURE">
<![CDATA[  [Mobile Safari] › standardization-validation.spec.js:294:5 › Festival Family Standardization Validation › Cross-Section Consistency › should have consistent header and navigation across all sections 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
<testcase name="Festival Family Standardization Validation › Cross-Section Consistency › should have consistent styling and theme across sections" classname="standardization-validation.spec.js" time="9.714">
<failure message="standardization-validation.spec.js:319:5 should have consistent styling and theme across sections" type="FAILURE">
<![CDATA[  [Mobile Safari] › standardization-validation.spec.js:319:5 › Festival Family Standardization Validation › Cross-Section Consistency › should have consistent styling and theme across sections 

    Error: browserContext.newPage: Target page, context or browser has been closed
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="standardization-validation.spec.js" timestamp="2025-07-03T21:10:23.383Z" hostname="Tablet" tests="11" failures="5" skipped="0" time="220.13" errors="0">
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should use React Router navigation for all internal links" classname="standardization-validation.spec.js" time="30.421">
<failure message="standardization-validation.spec.js:36:5 should use React Router navigation for all internal links" type="FAILURE">
<![CDATA[  [Tablet] › standardization-validation.spec.js:36:5 › Festival Family Standardization Validation › Navigation Standardization › should use React Router navigation for all internal links 

    TimeoutError: page.goto: Timeout 20000ms exceeded.
    Call log:
      - navigating to "http://localhost:5173/", waiting until "load"


      35 |     
      36 |     test('should use React Router navigation for all internal links', async ({ page }) => {
    > 37 |       await page.goto('/');
         |                  ^
      38 |       
      39 |       // Track navigation events
      40 |       const navigationEvents = [];
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:37:18

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-b73f4-tion-for-all-internal-links-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\standardization-validation-b73f4-tion-for-all-internal-links-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-b73f4-tion-for-all-internal-links-Tablet\video.webm]]

[[ATTACHMENT|artifacts\standardization-validation-b73f4-tion-for-all-internal-links-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should handle tab navigation with URL parameters" classname="standardization-validation.spec.js" time="13.907">
<failure message="standardization-validation.spec.js:78:5 should handle tab navigation with URL parameters" type="FAILURE">
<![CDATA[  [Tablet] › standardization-validation.spec.js:78:5 › Festival Family Standardization Validation › Navigation Standardization › should handle tab navigation with URL parameters 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button:has-text("CHAT")').first()


      87 |         // Click tab
      88 |         const tabButton = page.locator(`button:has-text("${tab.replace('_', ' ')}")`).first();
    > 89 |         await tabButton.click();
         |                         ^
      90 |         
      91 |         const endTime = performance.now();
      92 |         const tabSwitchTime = endTime - startTime;
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:89:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-e06c3-igation-with-URL-parameters-Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-e06c3-igation-with-URL-parameters-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\standardization-validation-e06c3-igation-with-URL-parameters-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-e06c3-igation-with-URL-parameters-Tablet\test-failed-1.png]]

[[ATTACHMENT|artifacts\standardization-validation-e06c3-igation-with-URL-parameters-Tablet\video.webm]]

[[ATTACHMENT|artifacts\standardization-validation-e06c3-igation-with-URL-parameters-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Navigation Standardization › should not use window.location or window.open for internal navigation" classname="standardization-validation.spec.js" time="36.255">
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use UnifiedInteractionButton consistently" classname="standardization-validation.spec.js" time="8.638">
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use BentoCard components consistently" classname="standardization-validation.spec.js" time="14.449">
</testcase>
<testcase name="Festival Family Standardization Validation › Component Standardization › should use EnhancedUnifiedBadge with color mapping" classname="standardization-validation.spec.js" time="8.637">
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should load all sections under 200ms" classname="standardization-validation.spec.js" time="2.142">
<failure message="standardization-validation.spec.js:230:5 should load all sections under 200ms" type="FAILURE">
<![CDATA[  [Tablet] › standardization-validation.spec.js:230:5 › Festival Family Standardization Validation › Performance Validation › should load all sections under 200ms 

    Error: expect(received).toBeLessThan(expected)

    Expected: < 200
    Received:   494.696100000001

      239 |         
      240 |         console.log(`${section} load time: ${loadTime.toFixed(2)}ms`);
    > 241 |         expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLD);
          |                          ^
      242 |       }
      243 |     });
      244 |
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:241:26

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[/ load time: 494.70ms

[[ATTACHMENT|artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-Tablet\test-failed-1.png]]

[[ATTACHMENT|artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-Tablet\video.webm]]

[[ATTACHMENT|artifacts\standardization-validation-c4134-ad-all-sections-under-200ms-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should have fast interaction response times" classname="standardization-validation.spec.js" time="28.952">
<failure message="standardization-validation.spec.js:245:5 should have fast interaction response times" type="FAILURE">
<![CDATA[  [Tablet] › standardization-validation.spec.js:245:5 › Festival Family Standardization Validation › Performance Validation › should have fast interaction response times 

    TimeoutError: page.goto: Timeout 20000ms exceeded.
    Call log:
      - navigating to "http://localhost:5173/activities", waiting until "load"


      244 |
      245 |     test('should have fast interaction response times', async ({ page }) => {
    > 246 |       await page.goto('/activities');
          |                  ^
      247 |       await page.waitForLoadState('networkidle');
      248 |       
      249 |       // Test button interactions
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:246:18

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-13ea8--interaction-response-times-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\standardization-validation-13ea8--interaction-response-times-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-13ea8--interaction-response-times-Tablet\video.webm]]

[[ATTACHMENT|artifacts\standardization-validation-13ea8--interaction-response-times-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Performance Validation › should not have infinite loops or performance issues" classname="standardization-validation.spec.js" time="22.58">
</testcase>
<testcase name="Festival Family Standardization Validation › Cross-Section Consistency › should have consistent header and navigation across all sections" classname="standardization-validation.spec.js" time="20.816">
<failure message="standardization-validation.spec.js:294:5 should have consistent header and navigation across all sections" type="FAILURE">
<![CDATA[  [Tablet] › standardization-validation.spec.js:294:5 › Festival Family Standardization Validation › Cross-Section Consistency › should have consistent header and navigation across all sections 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 2828.8
    Received:   0

      311 |           
      312 |           // Headers should be structurally similar
    > 313 |           expect(normalizedCurrent.length).toBeGreaterThan(normalizedOriginal.length * 0.8);
          |                                            ^
      314 |           expect(normalizedCurrent.length).toBeLessThan(normalizedOriginal.length * 1.2);
      315 |         }
      316 |       }
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:313:44

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-3c29f-igation-across-all-sections-Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\standardization-validation-3c29f-igation-across-all-sections-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\standardization-validation-3c29f-igation-across-all-sections-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\standardization-validation-3c29f-igation-across-all-sections-Tablet\test-failed-1.png]]

[[ATTACHMENT|artifacts\standardization-validation-3c29f-igation-across-all-sections-Tablet\video.webm]]

[[ATTACHMENT|artifacts\standardization-validation-3c29f-igation-across-all-sections-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Standardization Validation › Cross-Section Consistency › should have consistent styling and theme across sections" classname="standardization-validation.spec.js" time="33.333">
</testcase>
</testsuite>
</testsuites>
<testsuites id="" name="" tests="1" failures="0" skipped="0" errors="0" time="17.521227">
<testsuite name="production-validation.spec.js" timestamp="2025-07-02T23:27:48.489Z" hostname="chromium" tests="1" failures="0" skipped="0" time="10.213" errors="0">
<testcase name="Production Readiness Validation › Home page loads successfully with SmartHome component" classname="production-validation.spec.js" time="10.213">
<system-out>
<![CDATA[✅ Home page loads successfully
]]>
</system-out>
<system-err>
<![CDATA[Browser console error: The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.
Browser console error: X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.
Browser console error: The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.
]]>
</system-err>
</testcase>
</testsuite>
</testsuites>
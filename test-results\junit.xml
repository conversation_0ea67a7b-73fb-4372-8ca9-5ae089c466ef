<testsuites id="" name="" tests="13" failures="7" skipped="6" errors="0" time="122.979107">
<testsuite name="smart-buddy-matching.spec.js" timestamp="2025-07-03T21:40:19.623Z" hostname="chromium" tests="13" failures="7" skipped="6" time="100.687" errors="0">
<testcase name="Smart Buddy Matching Feature › Feature Integration › should display Smart Matching tab in FamHub" classname="smart-buddy-matching.spec.js" time="13.067">
<failure message="smart-buddy-matching.spec.js:23:5 should display Smart Matching tab in FamHub" type="FAILURE">
<![CDATA[  [chromium] › smart-buddy-matching.spec.js:23:5 › Smart Buddy Matching Feature › Feature Integration › should display Smart Matching tab in FamHub 

    Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

    Locator: locator('button:has-text("Smart Matching")')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for locator('button:has-text("Smart Matching")')


      24 |       // Check if Smart Matching tab is present
      25 |       const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
    > 26 |       await expect(smartMatchingTab).toBeVisible();
         |                                      ^
      27 |       
      28 |       // Verify tab has the correct icon
      29 |       const heartIcon = smartMatchingTab.locator('svg');
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\smart-buddy-matching.spec.js:26:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\smart-buddy-matching-Smart-28535-mart-Matching-tab-in-FamHub-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\smart-buddy-matching-Smart-28535-mart-Matching-tab-in-FamHub-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\smart-buddy-matching-Smart-28535-mart-Matching-tab-in-FamHub-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-28535-mart-Matching-tab-in-FamHub-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-28535-mart-Matching-tab-in-FamHub-chromium\video.webm]]

[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-28535-mart-Matching-tab-in-FamHub-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Smart Buddy Matching Feature › Feature Integration › should navigate to Smart Matching section when tab is clicked" classname="smart-buddy-matching.spec.js" time="12.575">
<failure message="smart-buddy-matching.spec.js:33:5 should navigate to Smart Matching section when tab is clicked" type="FAILURE">
<![CDATA[  [chromium] › smart-buddy-matching.spec.js:33:5 › Smart Buddy Matching Feature › Feature Integration › should navigate to Smart Matching section when tab is clicked 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button:has-text("Smart Matching")')


      34 |       // Click on Smart Matching tab
      35 |       const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
    > 36 |       await smartMatchingTab.click();
         |                              ^
      37 |       
      38 |       // Wait for content to load
      39 |       await page.waitForTimeout(1000);
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\smart-buddy-matching.spec.js:36:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\smart-buddy-matching-Smart-ac27e-section-when-tab-is-clicked-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\smart-buddy-matching-Smart-ac27e-section-when-tab-is-clicked-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\smart-buddy-matching-Smart-ac27e-section-when-tab-is-clicked-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-ac27e-section-when-tab-is-clicked-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-ac27e-section-when-tab-is-clicked-chromium\video.webm]]

[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-ac27e-section-when-tab-is-clicked-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Smart Buddy Matching Feature › Component Functionality › should display filters button and functionality" classname="smart-buddy-matching.spec.js" time="12.738">
<failure message="smart-buddy-matching.spec.js:60:5 should display filters button and functionality" type="FAILURE">
<![CDATA[  [chromium] › smart-buddy-matching.spec.js:60:5 › Smart Buddy Matching Feature › Component Functionality › should display filters button and functionality 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button:has-text("Smart Matching")')


      54 |       // Navigate to Smart Matching tab
      55 |       const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
    > 56 |       await smartMatchingTab.click();
         |                              ^
      57 |       await page.waitForTimeout(1000);
      58 |     });
      59 |
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\smart-buddy-matching.spec.js:56:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\smart-buddy-matching-Smart-48a02-rs-button-and-functionality-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\smart-buddy-matching-Smart-48a02-rs-button-and-functionality-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\smart-buddy-matching-Smart-48a02-rs-button-and-functionality-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-48a02-rs-button-and-functionality-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-48a02-rs-button-and-functionality-chromium\video.webm]]

[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-48a02-rs-button-and-functionality-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Smart Buddy Matching Feature › Component Functionality › should display Find Matches button" classname="smart-buddy-matching.spec.js" time="12.725">
<failure message="smart-buddy-matching.spec.js:84:5 should display Find Matches button" type="FAILURE">
<![CDATA[  [chromium] › smart-buddy-matching.spec.js:84:5 › Smart Buddy Matching Feature › Component Functionality › should display Find Matches button 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button:has-text("Smart Matching")')


      54 |       // Navigate to Smart Matching tab
      55 |       const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
    > 56 |       await smartMatchingTab.click();
         |                              ^
      57 |       await page.waitForTimeout(1000);
      58 |     });
      59 |
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\smart-buddy-matching.spec.js:56:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\smart-buddy-matching-Smart-9c914-display-Find-Matches-button-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\smart-buddy-matching-Smart-9c914-display-Find-Matches-button-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\smart-buddy-matching-Smart-9c914-display-Find-Matches-button-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-9c914-display-Find-Matches-button-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-9c914-display-Find-Matches-button-chromium\video.webm]]

[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-9c914-display-Find-Matches-button-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Smart Buddy Matching Feature › Component Functionality › should handle authentication state correctly" classname="smart-buddy-matching.spec.js" time="12.918">
<failure message="smart-buddy-matching.spec.js:94:5 should handle authentication state correctly" type="FAILURE">
<![CDATA[  [chromium] › smart-buddy-matching.spec.js:94:5 › Smart Buddy Matching Feature › Component Functionality › should handle authentication state correctly 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button:has-text("Smart Matching")')


      54 |       // Navigate to Smart Matching tab
      55 |       const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
    > 56 |       await smartMatchingTab.click();
         |                              ^
      57 |       await page.waitForTimeout(1000);
      58 |     });
      59 |
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\smart-buddy-matching.spec.js:56:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\smart-buddy-matching-Smart-470e4-hentication-state-correctly-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\smart-buddy-matching-Smart-470e4-hentication-state-correctly-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\smart-buddy-matching-Smart-470e4-hentication-state-correctly-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-470e4-hentication-state-correctly-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-470e4-hentication-state-correctly-chromium\video.webm]]

[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-470e4-hentication-state-correctly-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Smart Buddy Matching Feature › Standardized Components Usage › should use BentoCard components consistently" classname="smart-buddy-matching.spec.js" time="12.08">
<failure message="smart-buddy-matching.spec.js:122:5 should use BentoCard components consistently" type="FAILURE">
<![CDATA[  [chromium] › smart-buddy-matching.spec.js:122:5 › Smart Buddy Matching Feature › Standardized Components Usage › should use BentoCard components consistently 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button:has-text("Smart Matching")')


      116 |       // Navigate to Smart Matching tab
      117 |       const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
    > 118 |       await smartMatchingTab.click();
          |                              ^
      119 |       await page.waitForTimeout(1000);
      120 |     });
      121 |
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\smart-buddy-matching.spec.js:118:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\smart-buddy-matching-Smart-0e8c4-ard-components-consistently-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\smart-buddy-matching-Smart-0e8c4-ard-components-consistently-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\smart-buddy-matching-Smart-0e8c4-ard-components-consistently-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-0e8c4-ard-components-consistently-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-0e8c4-ard-components-consistently-chromium\video.webm]]

[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-0e8c4-ard-components-consistently-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Smart Buddy Matching Feature › Standardized Components Usage › should use EnhancedUnifiedBadge for AI-Powered indicator" classname="smart-buddy-matching.spec.js" time="13.263">
<failure message="smart-buddy-matching.spec.js:137:5 should use EnhancedUnifiedBadge for AI-Powered indicator" type="FAILURE">
<![CDATA[  [chromium] › smart-buddy-matching.spec.js:137:5 › Smart Buddy Matching Feature › Standardized Components Usage › should use EnhancedUnifiedBadge for AI-Powered indicator 

    TimeoutError: locator.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button:has-text("Smart Matching")')


      116 |       // Navigate to Smart Matching tab
      117 |       const smartMatchingTab = page.locator('button:has-text("Smart Matching")');
    > 118 |       await smartMatchingTab.click();
          |                              ^
      119 |       await page.waitForTimeout(1000);
      120 |     });
      121 |
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\smart-buddy-matching.spec.js:118:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\smart-buddy-matching-Smart-fdef9-ge-for-AI-Powered-indicator-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\smart-buddy-matching-Smart-fdef9-ge-for-AI-Powered-indicator-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\smart-buddy-matching-Smart-fdef9-ge-for-AI-Powered-indicator-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-fdef9-ge-for-AI-Powered-indicator-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-fdef9-ge-for-AI-Powered-indicator-chromium\video.webm]]

[[ATTACHMENT|artifacts\smart-buddy-matching-Smart-fdef9-ge-for-AI-Powered-indicator-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Smart Buddy Matching Feature › Standardized Components Usage › should use UnifiedInteractionButton for user actions" classname="smart-buddy-matching.spec.js" time="11.321">
<skipped>
</skipped>
</testcase>
<testcase name="Smart Buddy Matching Feature › Performance Validation › should load Smart Matching tab quickly" classname="smart-buddy-matching.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Smart Buddy Matching Feature › Performance Validation › should handle filter interactions smoothly" classname="smart-buddy-matching.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Smart Buddy Matching Feature › Responsive Design › should work on mobile viewport" classname="smart-buddy-matching.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Smart Buddy Matching Feature › Responsive Design › should work on tablet viewport" classname="smart-buddy-matching.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Smart Buddy Matching Feature › Error Handling › should handle component loading gracefully" classname="smart-buddy-matching.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>
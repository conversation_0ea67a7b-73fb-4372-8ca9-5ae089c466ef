/**
 * Lazy Realtime Hook
 * 
 * Performance-optimized hook that delays real-time subscription initialization
 * until after the initial render to improve page load times.
 * 
 * This addresses the 200ms performance bottleneck from immediate subscription setup.
 * 
 * @module useLazyRealtime
 * @version 1.0.0
 */

import { useEffect, useRef, useCallback, useState } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { realtimeService, SubscriptionCallback } from '@/lib/supabase/services/realtime-service'

interface UseLazyRealtimeOptions<T extends { [key: string]: any }> {
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*'
  filter?: string
  callback?: SubscriptionCallback<T>
  enabled?: boolean
  delayMs?: number // Delay before subscription starts
  priority?: 'immediate' | 'afterRender' | 'onInteraction'
}

/**
 * Hook for lazy-loaded real-time subscriptions to improve initial page load performance
 * 
 * @param table The table to subscribe to
 * @param queryKey The query key to invalidate on updates
 * @param options Options for the lazy subscription
 */
export function useLazyRealtimeSubscription<T extends { [key: string]: any }>(
  table: string,
  queryKey: unknown[],
  options: UseLazyRealtimeOptions<T> = {}
) {
  const queryClient = useQueryClient()
  const subscriptionIdRef = useRef<string | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const [isSubscribed, setIsSubscribed] = useState(false)
  
  const {
    event = '*',
    filter,
    callback,
    enabled = true,
    delayMs = 1000, // Default 1 second delay
    priority = 'afterRender'
  } = options

  // Optimized callback that invalidates queries
  const optimizedCallback = useCallback<SubscriptionCallback<T>>((payload) => {
    try {
      if (callback) {
        callback(payload)
      }
      queryClient.invalidateQueries({ queryKey })
    } catch (error) {
      console.error(`❌ Lazy realtime callback error for ${table}:`, error)
    }
  }, [callback, queryClient, queryKey, table])

  // Function to start subscription
  const startSubscription = useCallback(() => {
    if (!enabled || subscriptionIdRef.current) return

    console.log(`🚀 Starting lazy subscription for ${table} (priority: ${priority})`)
    
    const subscriptionId = realtimeService.subscribe<T>(
      table,
      optimizedCallback,
      { event, filter }
    )
    
    subscriptionIdRef.current = subscriptionId
    setIsSubscribed(true)
    
    console.log(`✅ Lazy subscription active: ${table}`)
  }, [table, optimizedCallback, event, filter, enabled, priority])

  // Function to stop subscription
  const stopSubscription = useCallback(() => {
    if (subscriptionIdRef.current) {
      realtimeService.unsubscribe(subscriptionIdRef.current)
      subscriptionIdRef.current = null
      setIsSubscribed(false)
      console.log(`🔌 Lazy subscription stopped: ${table}`)
    }
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
  }, [table])

  // Setup lazy subscription based on priority
  useEffect(() => {
    if (!enabled) {
      stopSubscription()
      return
    }

    switch (priority) {
      case 'immediate':
        // Start immediately (same as regular subscription)
        startSubscription()
        break
        
      case 'afterRender':
        // Start after a delay to allow initial render to complete
        timeoutRef.current = setTimeout(startSubscription, delayMs)
        break
        
      case 'onInteraction':
        // Don't start automatically - will be started manually
        break
    }

    return stopSubscription
  }, [enabled, priority, delayMs, startSubscription, stopSubscription])

  return {
    isSubscribed,
    startSubscription,
    stopSubscription
  }
}

/**
 * Hook for multiple lazy real-time subscriptions with shared delay
 */
export function useLazyMultipleRealtimeSubscriptions<T extends { [key: string]: any }>(
  subscriptions: Array<{
    table: string
    queryKey: unknown[]
    options?: UseLazyRealtimeOptions<T>
  }>,
  globalOptions: {
    enabled?: boolean
    delayMs?: number
    priority?: 'immediate' | 'afterRender' | 'onInteraction'
  } = {}
) {
  const subscriptionRefs = useRef<Map<string, string>>(new Map())
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const [subscribedTables, setSubscribedTables] = useState<Set<string>>(new Set())
  const queryClient = useQueryClient()
  
  const {
    enabled = true,
    delayMs = 1000,
    priority = 'afterRender'
  } = globalOptions

  // Start all subscriptions
  const startAllSubscriptions = useCallback(() => {
    if (!enabled) return

    console.log(`🚀 Starting ${subscriptions.length} lazy subscriptions (priority: ${priority})`)
    
    const newSubscribedTables = new Set<string>()
    
    subscriptions.forEach(({ table, queryKey, options = {} }) => {
      if (subscriptionRefs.current.has(table)) return // Already subscribed
      
      const {
        event = '*',
        filter,
        callback,
        enabled: tableEnabled = true
      } = options
      
      if (!tableEnabled) return
      
      const optimizedCallback: SubscriptionCallback<T> = (payload) => {
        try {
          if (callback) {
            callback(payload)
          }
          queryClient.invalidateQueries({ queryKey })
        } catch (error) {
          console.error(`❌ Multi-lazy callback error for ${table}:`, error)
        }
      }
      
      const subscriptionId = realtimeService.subscribe<T>(
        table,
        optimizedCallback,
        { event, filter }
      )
      
      subscriptionRefs.current.set(table, subscriptionId)
      newSubscribedTables.add(table)
    })
    
    setSubscribedTables(newSubscribedTables)
    console.log(`✅ ${newSubscribedTables.size} lazy subscriptions active`)
  }, [subscriptions, enabled, priority, queryClient])

  // Stop all subscriptions
  const stopAllSubscriptions = useCallback(() => {
    subscriptionRefs.current.forEach((subscriptionId, table) => {
      realtimeService.unsubscribe(subscriptionId)
      console.log(`🔌 Lazy subscription stopped: ${table}`)
    })
    
    subscriptionRefs.current.clear()
    setSubscribedTables(new Set())
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
  }, [])

  // Setup lazy subscriptions based on priority
  useEffect(() => {
    if (!enabled) {
      stopAllSubscriptions()
      return
    }

    switch (priority) {
      case 'immediate':
        startAllSubscriptions()
        break
        
      case 'afterRender':
        timeoutRef.current = setTimeout(startAllSubscriptions, delayMs)
        break
        
      case 'onInteraction':
        // Don't start automatically
        break
    }

    return stopAllSubscriptions
  }, [enabled, priority, delayMs, startAllSubscriptions, stopAllSubscriptions])

  return {
    subscribedTables,
    isFullySubscribed: subscribedTables.size === subscriptions.filter(s => s.options?.enabled !== false).length,
    startAllSubscriptions,
    stopAllSubscriptions
  }
}

/**
 * Hook for interaction-triggered lazy subscriptions
 * Useful for components that only need real-time updates after user interaction
 */
export function useInteractionTriggeredRealtime<T extends { [key: string]: any }>(
  table: string,
  queryKey: unknown[],
  options: Omit<UseLazyRealtimeOptions<T>, 'priority'> = {}
) {
  const [hasInteracted, setHasInteracted] = useState(false)
  
  const { isSubscribed, startSubscription, stopSubscription } = useLazyRealtimeSubscription(
    table,
    queryKey,
    {
      ...options,
      priority: 'onInteraction',
      enabled: options.enabled && hasInteracted
    }
  )

  // Function to trigger subscription on first interaction
  const triggerSubscription = useCallback(() => {
    if (!hasInteracted) {
      setHasInteracted(true)
      // Subscription will start automatically due to enabled state change
    }
  }, [hasInteracted])

  return {
    isSubscribed,
    hasInteracted,
    triggerSubscription,
    startSubscription,
    stopSubscription
  }
}

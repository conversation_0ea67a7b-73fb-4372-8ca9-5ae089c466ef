#!/usr/bin/env node

/**
 * Navigation System Completeness Assessment
 * 
 * This script performs comprehensive testing of both navigation systems:
 * - SimpleNavigation (desktop) component analysis
 * - ModernBottomNav (mobile) component analysis
 * - Responsive behavior testing
 * - Feature accessibility evaluation
 * - Navigation completeness scoring
 */

import { chromium } from 'playwright';
import { promises as fs } from 'fs';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'navigation-system-evidence';

// Test user credentials
const TEST_USER = {
  email: `nav.test.${Date.now()}@festivalfamily.test`,
  password: 'TestPassword123!',
  fullName: 'Navigation Test User'
};

async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

async function authenticateUser(page) {
  console.log('\n🔐 Setting up authenticated session for navigation testing');
  console.log('======================================================');
  
  await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(2000);
  
  // Sign up new user
  const signUpTab = await page.$('text=Sign Up');
  if (signUpTab) {
    await signUpTab.click();
    await page.waitForTimeout(1000);
  }
  
  const emailInput = await page.$('input[type="email"]');
  const passwordInput = await page.$('input[type="password"]');
  
  if (emailInput && passwordInput) {
    await emailInput.fill(TEST_USER.email);
    await passwordInput.fill(TEST_USER.password);
    
    const submitButton = await page.$('button[type="submit"]');
    if (submitButton) {
      await submitButton.click();
      await page.waitForTimeout(3000);
    }
  }
  
  console.log(`✅ Authenticated as: ${TEST_USER.email}`);
}

async function testDesktopNavigation(page) {
  console.log('\n🖥️ DESKTOP NAVIGATION ASSESSMENT');
  console.log('================================');
  
  // Set desktop viewport
  await page.setViewportSize({ width: 1280, height: 720 });
  await page.goto(APP_URL, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  // Capture desktop navigation
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/01-desktop-navigation.png`,
    fullPage: true 
  });
  
  // Test SimpleNavigation component
  const navElement = await page.$('nav');
  const brandLogo = await page.$('text=Festival Family');
  const navItems = await page.$$('nav a, nav button');
  
  // Check for specific navigation items
  const homeLink = await page.$('nav a[href="/"]');
  const activitiesLink = await page.$('nav a[href="/activities"]');
  const famhubLink = await page.$('nav a[href="/famhub"]');
  const discoverLink = await page.$('nav a[href="/discover"]');
  const profileLink = await page.$('nav a[href="/profile"]');
  const adminLink = await page.$('nav a[href="/admin"]');
  const signOutButton = await page.$('nav button') || await page.$('nav [role="button"]');
  
  // Test navigation functionality
  const navigationTests = [];
  
  if (activitiesLink) {
    console.log('🔄 Testing Activities navigation...');
    await activitiesLink.click();
    await page.waitForTimeout(2000);
    const currentUrl = page.url();
    navigationTests.push({
      link: 'Activities',
      clicked: true,
      expectedUrl: '/activities',
      actualUrl: currentUrl,
      working: currentUrl.includes('/activities')
    });
    
    // Capture navigation result
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/02-activities-navigation.png`,
      fullPage: true 
    });
  }
  
  // Return to home for next test
  await page.goto(APP_URL, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(2000);
  
  if (profileLink) {
    console.log('🔄 Testing Profile navigation...');
    await profileLink.click();
    await page.waitForTimeout(2000);
    const currentUrl = page.url();
    navigationTests.push({
      link: 'Profile',
      clicked: true,
      expectedUrl: '/profile',
      actualUrl: currentUrl,
      working: currentUrl.includes('/profile')
    });
    
    // Capture navigation result
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/03-profile-navigation.png`,
      fullPage: true 
    });
  }
  
  console.log('\n📊 DESKTOP NAVIGATION ANALYSIS:');
  console.log(`🧭 Navigation Element: ${navElement ? '✅ Present' : '❌ Missing'}`);
  console.log(`🏷️ Brand Logo: ${brandLogo ? '✅ Present' : '❌ Missing'}`);
  console.log(`🔗 Total Nav Items: ${navItems.length}`);
  console.log(`🏠 Home Link: ${homeLink ? '✅ Present' : '❌ Missing'}`);
  console.log(`📅 Activities Link: ${activitiesLink ? '✅ Present' : '❌ Missing'}`);
  console.log(`👥 FamHub Link: ${famhubLink ? '✅ Present' : '❌ Missing'}`);
  console.log(`🔍 Discover Link: ${discoverLink ? '✅ Present' : '❌ Missing'}`);
  console.log(`👤 Profile Link: ${profileLink ? '✅ Present' : '❌ Missing'}`);
  console.log(`⚙️ Admin Link: ${adminLink ? '✅ Present' : '❌ Missing'}`);
  console.log(`🚪 Sign Out: ${signOutButton ? '✅ Present' : '❌ Missing'}`);
  
  console.log('\n🔄 NAVIGATION FUNCTIONALITY:');
  navigationTests.forEach(test => {
    console.log(`${test.link}: ${test.working ? '✅ Working' : '❌ Failed'} (${test.actualUrl})`);
  });
  
  // Calculate desktop navigation score
  const expectedLinks = ['Home', 'Activities', 'FamHub', 'Discover', 'Profile'];
  const presentLinks = [homeLink, activitiesLink, famhubLink, discoverLink, profileLink].filter(Boolean).length;
  const workingLinks = navigationTests.filter(test => test.working).length;
  const functionalityScore = navigationTests.length > 0 ? (workingLinks / navigationTests.length * 100) : 0;
  const completenessScore = (presentLinks / expectedLinks.length * 100);
  const overallScore = (completenessScore + functionalityScore) / 2;
  
  console.log(`\n📊 DESKTOP NAVIGATION SCORE: ${overallScore.toFixed(1)}%`);
  console.log(`   - Completeness: ${completenessScore.toFixed(1)}%`);
  console.log(`   - Functionality: ${functionalityScore.toFixed(1)}%`);
  
  return {
    type: 'desktop',
    navigation: {
      present: !!navElement,
      brandLogo: !!brandLogo,
      totalItems: navItems.length,
      links: {
        home: !!homeLink,
        activities: !!activitiesLink,
        famhub: !!famhubLink,
        discover: !!discoverLink,
        profile: !!profileLink,
        admin: !!adminLink,
        signOut: !!signOutButton
      }
    },
    functionality: navigationTests,
    scores: {
      completeness: completenessScore,
      functionality: functionalityScore,
      overall: overallScore
    },
    screenshots: [
      '01-desktop-navigation.png',
      '02-activities-navigation.png',
      '03-profile-navigation.png'
    ]
  };
}

async function testMobileNavigation(page) {
  console.log('\n📱 MOBILE NAVIGATION ASSESSMENT');
  console.log('==============================');
  
  // Set mobile viewport
  await page.setViewportSize({ width: 375, height: 667 });
  await page.goto(APP_URL, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  // Capture mobile navigation
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/04-mobile-navigation.png`,
    fullPage: true 
  });
  
  // Test ModernBottomNav component
  const bottomNav = await page.$('[class*="bottom"]') || await page.$('[class*="fixed bottom"]');
  const bottomNavItems = await page.$$('[class*="bottom"] a, [class*="bottom"] button');
  
  // Check for specific bottom nav items
  const mobileHomeLink = await page.$('[class*="bottom"] [href="/"]');
  const mobileActivitiesLink = await page.$('[class*="bottom"] [href="/activities"]');
  const mobileFamhubLink = await page.$('[class*="bottom"] [href="/famhub"]');
  const mobileDiscoverLink = await page.$('[class*="bottom"] [href="/discover"]');
  const quickActionsButton = await page.$('[class*="bottom"] button') || await page.$('[class*="bottom"] [class*="add"]');
  
  // Test quick actions menu
  let quickActionsWorking = false;
  if (quickActionsButton) {
    console.log('🔄 Testing Quick Actions menu...');
    await quickActionsButton.click();
    await page.waitForTimeout(1000);
    
    // Check if quick actions menu appeared
    const quickActionsMenu = await page.$('[class*="quick"], [role="menu"]');
    quickActionsWorking = !!quickActionsMenu;
    
    // Capture quick actions state
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/05-mobile-quick-actions.png`,
      fullPage: true 
    });
    
    // Close menu if it opened
    if (quickActionsMenu) {
      await quickActionsButton.click();
      await page.waitForTimeout(500);
    }
  }
  
  // Test mobile navigation functionality
  const mobileNavigationTests = [];
  
  if (mobileActivitiesLink) {
    console.log('🔄 Testing Mobile Activities navigation...');
    await mobileActivitiesLink.click();
    await page.waitForTimeout(2000);
    const currentUrl = page.url();
    mobileNavigationTests.push({
      link: 'Mobile Activities',
      clicked: true,
      expectedUrl: '/activities',
      actualUrl: currentUrl,
      working: currentUrl.includes('/activities')
    });
    
    // Capture mobile navigation result
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/06-mobile-activities-navigation.png`,
      fullPage: true 
    });
  }
  
  console.log('\n📊 MOBILE NAVIGATION ANALYSIS:');
  console.log(`📱 Bottom Nav: ${bottomNav ? '✅ Present' : '❌ Missing'}`);
  console.log(`🔗 Total Nav Items: ${bottomNavItems.length}`);
  console.log(`🏠 Mobile Home: ${mobileHomeLink ? '✅ Present' : '❌ Missing'}`);
  console.log(`📅 Mobile Activities: ${mobileActivitiesLink ? '✅ Present' : '❌ Missing'}`);
  console.log(`👥 Mobile FamHub: ${mobileFamhubLink ? '✅ Present' : '❌ Missing'}`);
  console.log(`🔍 Mobile Discover: ${mobileDiscoverLink ? '✅ Present' : '❌ Missing'}`);
  console.log(`⚡ Quick Actions: ${quickActionsButton ? '✅ Present' : '❌ Missing'}`);
  console.log(`🎯 Quick Actions Working: ${quickActionsWorking ? '✅ Functional' : '❌ Not Working'}`);
  
  console.log('\n🔄 MOBILE NAVIGATION FUNCTIONALITY:');
  mobileNavigationTests.forEach(test => {
    console.log(`${test.link}: ${test.working ? '✅ Working' : '❌ Failed'} (${test.actualUrl})`);
  });
  
  // Calculate mobile navigation score
  const expectedMobileLinks = ['Home', 'Activities', 'FamHub', 'Discover'];
  const presentMobileLinks = [mobileHomeLink, mobileActivitiesLink, mobileFamhubLink, mobileDiscoverLink].filter(Boolean).length;
  const workingMobileLinks = mobileNavigationTests.filter(test => test.working).length;
  const mobileFunctionalityScore = mobileNavigationTests.length > 0 ? (workingMobileLinks / mobileNavigationTests.length * 100) : 0;
  const mobileCompletenessScore = (presentMobileLinks / expectedMobileLinks.length * 100);
  const quickActionsScore = quickActionsWorking ? 100 : 0;
  const mobileOverallScore = (mobileCompletenessScore + mobileFunctionalityScore + quickActionsScore) / 3;
  
  console.log(`\n📊 MOBILE NAVIGATION SCORE: ${mobileOverallScore.toFixed(1)}%`);
  console.log(`   - Completeness: ${mobileCompletenessScore.toFixed(1)}%`);
  console.log(`   - Functionality: ${mobileFunctionalityScore.toFixed(1)}%`);
  console.log(`   - Quick Actions: ${quickActionsScore}%`);
  
  return {
    type: 'mobile',
    navigation: {
      present: !!bottomNav,
      totalItems: bottomNavItems.length,
      links: {
        home: !!mobileHomeLink,
        activities: !!mobileActivitiesLink,
        famhub: !!mobileFamhubLink,
        discover: !!mobileDiscoverLink,
        quickActions: !!quickActionsButton
      },
      quickActionsWorking
    },
    functionality: mobileNavigationTests,
    scores: {
      completeness: mobileCompletenessScore,
      functionality: mobileFunctionalityScore,
      quickActions: quickActionsScore,
      overall: mobileOverallScore
    },
    screenshots: [
      '04-mobile-navigation.png',
      '05-mobile-quick-actions.png',
      '06-mobile-activities-navigation.png'
    ]
  };
}

async function runNavigationSystemAudit() {
  console.log('🧭 NAVIGATION SYSTEM COMPLETENESS ASSESSMENT');
  console.log('============================================');
  console.log(`🕐 Start Time: ${new Date().toISOString()}`);
  
  await ensureEvidenceDir();
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Authenticate user
    await authenticateUser(page);
    
    const results = {};
    
    // Test Desktop Navigation
    results.desktop = await testDesktopNavigation(page);
    
    // Test Mobile Navigation
    results.mobile = await testMobileNavigation(page);
    
    // Calculate overall navigation system score
    const overallScore = (results.desktop.scores.overall + results.mobile.scores.overall) / 2;
    
    // Save comprehensive results
    const evidence = {
      timestamp: new Date().toISOString(),
      testUser: { email: TEST_USER.email },
      navigationResults: results,
      summary: {
        desktopScore: results.desktop.scores.overall,
        mobileScore: results.mobile.scores.overall,
        overallScore: parseFloat(overallScore.toFixed(1)),
        criticalIssues: [],
        screenshots: [...results.desktop.screenshots, ...results.mobile.screenshots]
      }
    };
    
    // Identify critical issues
    if (results.desktop.scores.functionality < 50) {
      evidence.summary.criticalIssues.push('Desktop navigation functionality severely impaired');
    }
    if (results.mobile.scores.functionality < 50) {
      evidence.summary.criticalIssues.push('Mobile navigation functionality severely impaired');
    }
    if (!results.desktop.navigation.present) {
      evidence.summary.criticalIssues.push('Desktop navigation component missing');
    }
    if (!results.mobile.navigation.present) {
      evidence.summary.criticalIssues.push('Mobile navigation component missing');
    }
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/navigation-system-audit-results.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    console.log('\n📊 NAVIGATION SYSTEM AUDIT SUMMARY');
    console.log('==================================');
    console.log(`🖥️ Desktop Score: ${evidence.summary.desktopScore.toFixed(1)}%`);
    console.log(`📱 Mobile Score: ${evidence.summary.mobileScore.toFixed(1)}%`);
    console.log(`🎯 Overall Score: ${evidence.summary.overallScore}%`);
    console.log(`🚨 Critical Issues: ${evidence.summary.criticalIssues.length}`);
    evidence.summary.criticalIssues.forEach(issue => console.log(`   - ${issue}`));
    console.log(`📸 Screenshots: ${evidence.summary.screenshots.length}`);
    console.log(`📁 Evidence: ${EVIDENCE_DIR}/`);
    
    return evidence;
    
  } catch (error) {
    console.error('\n💥 Navigation system audit failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

// Run the navigation system audit
runNavigationSystemAudit()
  .then(() => {
    console.log('\n✅ Navigation system audit completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Navigation system audit failed:', error);
    process.exit(1);
  });

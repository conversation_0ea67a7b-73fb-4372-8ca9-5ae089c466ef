/**
 * Redis Favorites Service - High-Performance User Favorites Caching
 * 
 * Provides Redis-cached user favorites functionality with database fallback.
 * Uses the same proven patterns as RedisParticipantService for optimal performance.
 * 
 * Features:
 * - Redis caching with 5-minute TTL
 * - Graceful fallback to database queries
 * - Performance tracking and monitoring
 * - Atomic operations for data consistency
 * - Browser-safe Redis client initialization
 * 
 * @module RedisFavoritesService
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { Redis } from '@upstash/redis'
import { userInteractionService } from '@/lib/supabase/services/user-interaction-service'

// ============================================================================
// REDIS CLIENT CONFIGURATION
// ============================================================================

// Browser-safe Redis client initialization
let redis: Redis | null = null

const initRedis = () => {
  if (!redis) {
    try {
      redis = new Redis({
        url: 'https://legal-crab-25449.upstash.io',
        token: 'AWNpAAIjcDE3YzM0NDA5YzZkNDY0Mzg3YjQzM2YzNDNkODI5ZGIyY3AxMA'
      })
    } catch (error) {
      console.warn('Failed to initialize Redis client:', error)
      redis = null
    }
  }
  return redis
}

// ============================================================================
// CACHE CONFIGURATION
// ============================================================================

const CACHE_KEYS = {
  USER_FAVORITES: (userId: string) => `user:${userId}:favorites`,
  USER_FAVORITE_STATUS: (userId: string, itemId: string) => `user:${userId}:favorite:${itemId}`,
  PERFORMANCE: (operation: string) => `perf:favorites:${operation}`
}

const CACHE_TTL = {
  USER_FAVORITES: 300, // 5 minutes
  FAVORITE_STATUS: 300, // 5 minutes
  PERFORMANCE: 3600 // 1 hour
}

// ============================================================================
// PERFORMANCE TRACKING
// ============================================================================

interface PerformanceMetrics {
  operation: string
  responseTime: number
  cacheHit: boolean
  timestamp: number
}

const trackPerformance = async (metrics: PerformanceMetrics) => {
  const redisClient = initRedis()
  
  if (!redisClient) {
    return // Skip performance tracking if Redis is not available
  }
  
  try {
    const key = CACHE_KEYS.PERFORMANCE(metrics.operation)
    await redisClient.lpush(key, JSON.stringify(metrics))
    await redisClient.expire(key, CACHE_TTL.PERFORMANCE)
    await redisClient.ltrim(key, 0, 99) // Keep last 100 metrics
  } catch (error) {
    console.warn('Failed to track performance metrics:', error)
  }
}

// ============================================================================
// REDIS FAVORITES SERVICE
// ============================================================================

export class RedisFavoritesService {
  
  /**
   * Get user favorites with Redis caching
   */
  async getUserFavorites(userId: string, itemType: string = 'activity'): Promise<string[]> {
    const startTime = performance.now()
    const redisClient = initRedis()
    
    if (!redisClient) {
      console.warn('Redis client not available, using database fallback')
      return this.getFavoritesFromDatabase(userId, itemType)
    }
    
    try {
      const key = CACHE_KEYS.USER_FAVORITES(userId)
      const cachedFavorites = await redisClient.get(key)
      const responseTime = performance.now() - startTime
      
      if (cachedFavorites !== null) {
        await trackPerformance({
          operation: 'getUserFavorites',
          responseTime,
          cacheHit: true,
          timestamp: Date.now()
        })
        
        console.log(`📦 Cache HIT: ${key} (${responseTime.toFixed(2)}ms)`)
        return Array.isArray(cachedFavorites) ? cachedFavorites : []
      }
      
      // Cache miss - fetch from database and cache result
      const favorites = await this.getFavoritesFromDatabase(userId, itemType)
      await this.cacheFavorites(userId, favorites)
      
      await trackPerformance({
        operation: 'getUserFavorites',
        responseTime,
        cacheHit: false,
        timestamp: Date.now()
      })
      
      console.log(`📦 Cache MISS: ${key} (${responseTime.toFixed(2)}ms)`)
      return favorites
      
    } catch (error) {
      console.warn('Redis getUserFavorites error:', error)
      return this.getFavoritesFromDatabase(userId, itemType)
    }
  }

  /**
   * Check if item is favorited by user (with caching)
   */
  async isFavorite(userId: string, itemId: string): Promise<boolean> {
    const startTime = performance.now()
    const redisClient = initRedis()
    
    if (!redisClient) {
      const favorites = await this.getFavoritesFromDatabase(userId)
      return favorites.includes(itemId)
    }
    
    try {
      const key = CACHE_KEYS.USER_FAVORITE_STATUS(userId, itemId)
      const cachedStatus = await redisClient.get(key)
      const responseTime = performance.now() - startTime
      
      if (cachedStatus !== null) {
        await trackPerformance({
          operation: 'isFavorite',
          responseTime,
          cacheHit: true,
          timestamp: Date.now()
        })
        
        console.log(`📦 Cache HIT: ${key} (${responseTime.toFixed(2)}ms)`)
        return cachedStatus === 'true'
      }
      
      // Cache miss - check database
      const favorites = await this.getFavoritesFromDatabase(userId)
      const isFav = favorites.includes(itemId)
      
      // Cache the result
      await redisClient.setex(key, CACHE_TTL.FAVORITE_STATUS, isFav ? 'true' : 'false')
      
      await trackPerformance({
        operation: 'isFavorite',
        responseTime,
        cacheHit: false,
        timestamp: Date.now()
      })
      
      console.log(`📦 Cache MISS: ${key} (${responseTime.toFixed(2)}ms)`)
      return isFav
      
    } catch (error) {
      console.warn('Redis isFavorite error:', error)
      const favorites = await this.getFavoritesFromDatabase(userId)
      return favorites.includes(itemId)
    }
  }

  /**
   * Add item to favorites (with cache invalidation)
   */
  async addToFavorites(userId: string, itemId: string, itemType: string = 'activity'): Promise<boolean> {
    const startTime = performance.now()
    
    try {
      // Add to database first
      const success = await this.addFavoriteToDatabase(userId, itemId, itemType)
      
      if (success) {
        // Invalidate cache
        await this.invalidateUserCache(userId, itemId)
        console.log(`✅ Added to favorites and invalidated cache: ${itemId}`)
      }
      
      const responseTime = performance.now() - startTime
      await trackPerformance({
        operation: 'addToFavorites',
        responseTime,
        cacheHit: false,
        timestamp: Date.now()
      })
      
      return success
    } catch (error) {
      console.warn('Redis addToFavorites error:', error)
      return false
    }
  }

  /**
   * Remove item from favorites (with cache invalidation)
   */
  async removeFromFavorites(userId: string, itemId: string): Promise<boolean> {
    const startTime = performance.now()
    
    try {
      // Remove from database first
      const success = await this.removeFavoriteFromDatabase(userId, itemId)
      
      if (success) {
        // Invalidate cache
        await this.invalidateUserCache(userId, itemId)
        console.log(`✅ Removed from favorites and invalidated cache: ${itemId}`)
      }
      
      const responseTime = performance.now() - startTime
      await trackPerformance({
        operation: 'removeFromFavorites',
        responseTime,
        cacheHit: false,
        timestamp: Date.now()
      })
      
      return success
    } catch (error) {
      console.warn('Redis removeFromFavorites error:', error)
      return false
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Cache user favorites list
   */
  private async cacheFavorites(userId: string, favorites: string[]): Promise<void> {
    const redisClient = initRedis()
    
    if (!redisClient) {
      return
    }
    
    try {
      const key = CACHE_KEYS.USER_FAVORITES(userId)
      await redisClient.setex(key, CACHE_TTL.USER_FAVORITES, JSON.stringify(favorites))
      console.log(`📦 Cache SET: ${key}`)
    } catch (error) {
      console.warn('Failed to cache favorites:', error)
    }
  }

  /**
   * Invalidate user cache after favorites change
   */
  private async invalidateUserCache(userId: string, itemId: string): Promise<void> {
    const redisClient = initRedis()
    
    if (!redisClient) {
      return
    }
    
    try {
      const favoritesKey = CACHE_KEYS.USER_FAVORITES(userId)
      const statusKey = CACHE_KEYS.USER_FAVORITE_STATUS(userId, itemId)
      
      await Promise.all([
        redisClient.del(favoritesKey),
        redisClient.del(statusKey)
      ])
      
      console.log(`🗑️ Cache invalidated for user ${userId}, item ${itemId}`)
    } catch (error) {
      console.warn('Failed to invalidate cache:', error)
    }
  }

  /**
   * Database fallback methods - integrated with userInteractionService
   */
  private async getFavoritesFromDatabase(userId: string, itemType: string = 'activity'): Promise<string[]> {
    try {
      console.log(`🔄 Database fallback: getUserFavorites(${userId}, ${itemType})`)
      const response = await userInteractionService.getUserFavorites(userId, itemType)

      if (response.status === 'success' && response.data) {
        return response.data.map(fav => fav.activity_id)
      }

      return []
    } catch (error) {
      console.warn('Database getFavorites error:', error)
      return []
    }
  }

  private async addFavoriteToDatabase(userId: string, itemId: string, itemType: string): Promise<boolean> {
    try {
      console.log(`🔄 Database fallback: addToFavorites(${userId}, ${itemId}, ${itemType})`)
      const response = await userInteractionService.addToFavorites(userId, itemId, itemType)
      return response.status === 'success'
    } catch (error) {
      console.warn('Database addFavorite error:', error)
      return false
    }
  }

  private async removeFavoriteFromDatabase(userId: string, itemId: string): Promise<boolean> {
    try {
      console.log(`🔄 Database fallback: removeFromFavorites(${userId}, ${itemId})`)
      const response = await userInteractionService.removeFromFavorites(userId, itemId)
      return response.status === 'success'
    } catch (error) {
      console.warn('Database removeFavorite error:', error)
      return false
    }
  }
}

// ============================================================================
// EXPORT SINGLETON INSTANCE
// ============================================================================

export const redisFavoritesService = new RedisFavoritesService()

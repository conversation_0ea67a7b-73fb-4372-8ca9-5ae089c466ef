/**
 * useAdminAccess Hook Tests
 * 
 * Tests for the admin access control hook including navigation and permission checking.
 */

import { renderHook } from '@testing-library/react'
import { useAdminAccess } from '../../hooks/useAdminAccess'
import { UserRole, Permission } from '../../types/core'

// Mock react-router-dom
const mockNavigate = jest.fn()
jest.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
}))

// Mock the auth provider
const mockAuthContext = {
  session: null,
  user: null,
  profile: null,
  loading: false,
  isAdmin: false,
  signIn: jest.fn(),
  signUp: jest.fn(),
  signOut: jest.fn(),
  refreshProfile: jest.fn(),
}

jest.mock('../../providers/ConsolidatedAuthProvider', () => ({
  useAuth: () => mockAuthContext,
}))

describe('useAdminAccess', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockAuthContext.isAdmin = false
    mockAuthContext.profile = null
  })

  test('should redirect to home when user is not admin', () => {
    mockAuthContext.isAdmin = false

    renderHook(() => useAdminAccess())

    expect(mockNavigate).toHaveBeenCalledWith('/')
  })

  test('should not redirect when user is admin', () => {
    mockAuthContext.isAdmin = true
    mockAuthContext.profile = {
      id: 'admin-id',
      username: 'admin',
      email: '<EMAIL>',
      role: 'SUPER_ADMIN' as UserRole,
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
      avatar_url: null,
      full_name: 'Admin User',
      bio: null,
      location: null,
      interests: null,
      website: null,
    } as any

    const { result } = renderHook(() => useAdminAccess())

    expect(mockNavigate).not.toHaveBeenCalled()
    expect(result.current.isAdmin).toBe(true)
    expect(result.current.profile).toBe(mockAuthContext.profile)
  })

  test('should redirect to admin dashboard when user lacks required permission', () => {
    mockAuthContext.isAdmin = true
    mockAuthContext.profile = {
      id: 'admin-id',
      username: 'admin',
      email: '<EMAIL>',
      role: 'MODERATOR' as UserRole,
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
      avatar_url: null,
      full_name: 'Moderator User',
      bio: null,
      location: null,
      interests: null,
      website: null,
    } as any

    // Test with a permission that moderator doesn't have
    renderHook(() => useAdminAccess('manage_users' as Permission))

    // Should redirect to admin dashboard since moderator doesn't have manage_users permission
    expect(mockNavigate).toHaveBeenCalledWith('/admin')
  })

  test('should allow access when user has required permission', () => {
    mockAuthContext.isAdmin = true
    mockAuthContext.profile = {
      id: 'admin-id',
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin' as UserRole,
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
      avatar_url: null,
      full_name: 'Admin User',
      bio: null,
      location: null,
      interests: null,
      website: null,
    } as any

    const { result } = renderHook(() => useAdminAccess('manage_users' as Permission))

    // Admin role should have access to manage_users
    expect(mockNavigate).not.toHaveBeenCalled()
    expect(result.current.isAdmin).toBe(true)
  })

  test('should handle missing profile gracefully', () => {
    mockAuthContext.isAdmin = true
    mockAuthContext.profile = null

    renderHook(() => useAdminAccess('manage_users' as Permission))

    // Should not crash - the hook handles missing profile by not calling hasPermission
    // Since profile is null, the permission check is skipped
    expect(mockNavigate).not.toHaveBeenCalled()
  })

  test('should return correct values', () => {
    mockAuthContext.isAdmin = true
    mockAuthContext.profile = {
      id: 'admin-id',
      username: 'admin',
      email: '<EMAIL>',
      role: 'SUPER_ADMIN' as UserRole,
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
      avatar_url: null,
      full_name: 'Admin User',
      bio: null,
      location: null,
      interests: null,
      website: null,
    } as any

    const { result } = renderHook(() => useAdminAccess())

    expect(result.current.isAdmin).toBe(true)
    expect(result.current.profile).toBe(mockAuthContext.profile)
  })

  test('should handle permission checking for moderator role', () => {
    mockAuthContext.isAdmin = true
    mockAuthContext.profile = {
      id: 'mod-id',
      username: 'moderator',
      email: '<EMAIL>',
      role: 'moderator' as UserRole,
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
      avatar_url: null,
      full_name: 'Moderator User',
      bio: null,
      location: null,
      interests: null,
      website: null,
    } as any

    // Moderator should have access to basic permissions
    const { result } = renderHook(() => useAdminAccess('moderate_content' as Permission))

    expect(mockNavigate).not.toHaveBeenCalled()
    expect(result.current.isAdmin).toBe(true)
  })
})

/**
 * Batched User Interactions Hook
 * 
 * Optimizes user interaction queries by batching multiple activity status checks
 * into single database queries. Maintains compatibility with existing useUserInteractions
 * pattern while significantly reducing database load.
 * 
 * This hook works alongside the existing standardized architecture:
 * - UnifiedInteractionButton components
 * - userInteractionService for mutations
 * - React Query caching patterns
 * 
 * @module useBatchedUserInteractions
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/providers/ConsolidatedAuthProvider';
import { supabase } from '@/lib/supabase';

// ============================================================================
// TYPES
// ============================================================================

export interface BatchedUserInteraction {
  is_participant: boolean;
  is_favorite: boolean;
  attendance_status?: string;
  participant_count?: number;
}

export interface BatchedInteractionsResult {
  interactions: Record<string, BatchedUserInteraction>;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

// ============================================================================
// BATCHED HOOK
// ============================================================================

/**
 * Batched hook for user interaction status across multiple activities
 * Optimizes performance by fetching all user interactions in a single query
 * while maintaining compatibility with existing useUserInteractions pattern
 */
export function useBatchedUserInteractions(activityIds: string[]): BatchedInteractionsResult {
  const { user } = useAuth();

  const {
    data: interactions = {},
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['user-interactions-batch', user?.id, activityIds.sort().join(',')],
    queryFn: async () => {
      if (!user?.id || activityIds.length === 0) return {};
      
      console.log('🔄 useBatchedUserInteractions: Fetching interactions for', activityIds.length, 'activities');
      
      try {
        // Batch query for all user interactions
        const [participantsResult, favoritesResult, countsResult] = await Promise.all([
          // Get user participation status
          supabase
            .from('activity_participants')
            .select('activity_id, status, attendance_status')
            .eq('user_id', user.id)
            .in('activity_id', activityIds),
          
          // Get user favorites
          supabase
            .from('user_favorites')
            .select('activity_id')
            .eq('user_id', user.id)
            .in('activity_id', activityIds),
          
          // Get participant counts for all activities
          supabase
            .from('activity_participants')
            .select('activity_id')
            .in('activity_id', activityIds)
        ]);

        if (participantsResult.error) {
          console.error('Error fetching batch participants:', participantsResult.error);
          throw participantsResult.error;
        }

        if (favoritesResult.error) {
          console.error('Error fetching batch favorites:', favoritesResult.error);
          throw favoritesResult.error;
        }

        if (countsResult.error) {
          console.error('Error fetching batch counts:', countsResult.error);
          throw countsResult.error;
        }

        // Transform to lookup object for easy access
        const batchedInteractions: Record<string, BatchedUserInteraction> = {};

        // Initialize all activities with default values
        activityIds.forEach(id => {
          batchedInteractions[id] = {
            is_participant: false,
            is_favorite: false,
            participant_count: 0
          };
        });

        // Populate participant data
        participantsResult.data?.forEach(p => {
          if (batchedInteractions[p.activity_id]) {
            batchedInteractions[p.activity_id].is_participant = true;
            batchedInteractions[p.activity_id].attendance_status = p.attendance_status;
          }
        });

        // Populate favorite data
        favoritesResult.data?.forEach(f => {
          if (batchedInteractions[f.activity_id]) {
            batchedInteractions[f.activity_id].is_favorite = true;
          }
        });

        // Calculate participant counts
        const countsByActivity: Record<string, number> = {};
        countsResult.data?.forEach(p => {
          countsByActivity[p.activity_id] = (countsByActivity[p.activity_id] || 0) + 1;
        });

        // Add counts to interactions
        Object.keys(batchedInteractions).forEach(activityId => {
          batchedInteractions[activityId].participant_count = countsByActivity[activityId] || 0;
        });

        console.log('✅ useBatchedUserInteractions: Fetched interactions for', Object.keys(batchedInteractions).length, 'activities');
        return batchedInteractions;
      } catch (error) {
        console.error('Error in batched user interactions:', error);
        throw error;
      }
    },
    enabled: !!user?.id && activityIds.length > 0,
    staleTime: 1000 * 60 * 2, // 2 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
    retry: 2,
    refetchOnWindowFocus: false
  });

  return {
    interactions,
    isLoading,
    error: error?.message || null,
    refetch
  };
}

/**
 * Helper hook that provides a single activity's interaction status
 * from the batched query result. Maintains API compatibility with
 * the existing useUserInteractions hook.
 */
export function useActivityInteractionFromBatch(
  activityId: string,
  batchedResult: BatchedInteractionsResult
): BatchedUserInteraction | null {
  if (!activityId || !batchedResult.interactions[activityId]) {
    return null;
  }

  return batchedResult.interactions[activityId];
}

/**
 * Hook that provides batched interactions for activities currently visible
 * on the Activities page. This is the main hook to use in the Activities component.
 */
export function useActivitiesPageInteractions(activities: Array<{ id: string }>) {
  const activityIds = activities.map(a => a.id);
  return useBatchedUserInteractions(activityIds);
}

/**
 * Test Activity List Display
 * 
 * This test verifies that activities are properly displayed in the admin list
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

test('Test Activity List Display and Click Handlers', async ({ page }) => {
  console.log('🧪 Testing activity list display and click handlers...');
  
  const loginSuccess = await loginAsAdmin(page);
  console.log(`Login success: ${loginSuccess}`);
  
  if (loginSuccess) {
    // Navigate to activities list
    await page.goto('/admin/activities');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    console.log('📋 Checking activities list...');
    
    // Check if activities are displayed
    const activityRows = await page.locator('tbody tr').count();
    console.log(`Found ${activityRows} activity rows`);
    
    // Look for our test activity
    const testActivityVisible = await page.locator('text="Schema Fixed Test Activity"').count() > 0;
    console.log(`Test activity visible: ${testActivityVisible}`);
    
    await page.screenshot({ path: 'test-results/activity-list-display.png', fullPage: true });
    
    if (activityRows > 0) {
      console.log('✅ Activities are being displayed');
      
      // Test edit button click
      const editButtons = await page.locator('button:has-text("Edit")').count();
      console.log(`Found ${editButtons} edit buttons`);
      
      if (editButtons > 0) {
        console.log('🔗 Testing edit button click...');
        
        const firstEditButton = page.locator('button:has-text("Edit")').first();
        const initialUrl = page.url();
        
        await firstEditButton.click();
        await page.waitForTimeout(2000);
        
        const newUrl = page.url();
        const clickWorked = newUrl !== initialUrl && newUrl.includes('/admin/activities/');
        
        console.log(`Edit button click worked: ${clickWorked}`);
        console.log(`Initial URL: ${initialUrl}`);
        console.log(`New URL: ${newUrl}`);
        
        if (clickWorked) {
          console.log('✅ Edit button navigation working');
          await page.screenshot({ path: 'test-results/activity-edit-navigation.png', fullPage: true });
        } else {
          console.log('❌ Edit button navigation not working');
        }
      } else {
        console.log('❌ No edit buttons found');
      }
    } else {
      console.log('❌ No activities displayed');
    }
    
    // Test create new activity button
    console.log('🔗 Testing create new activity button...');
    await page.goto('/admin/activities');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const createButton = page.locator('button:has-text("Create New Activity")');
    const createButtonVisible = await createButton.isVisible();
    console.log(`Create button visible: ${createButtonVisible}`);
    
    if (createButtonVisible) {
      const initialUrl = page.url();
      await createButton.click();
      await page.waitForTimeout(2000);
      
      const newUrl = page.url();
      const createClickWorked = newUrl !== initialUrl && newUrl.includes('/admin/activities/new');
      
      console.log(`Create button click worked: ${createClickWorked}`);
      
      if (createClickWorked) {
        console.log('✅ Create button navigation working');
      } else {
        console.log('❌ Create button navigation not working');
      }
    }
  }
});

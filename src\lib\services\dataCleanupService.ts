import { supabase } from '@/lib/supabase';

/**
 * Data Cleanup Service
 * Removes test/fake data and placeholder content from the Festival Family app
 */

interface CleanupResult {
  success: boolean;
  itemsRemoved: number;
  errors: string[];
  details: string[];
}

class DataCleanupService {
  /**
   * Patterns to identify test/fake data
   */
  private readonly testDataPatterns = [
    // Generic test patterns
    /test/i,
    /fake/i,
    /placeholder/i,
    /example/i,
    /demo/i,
    /sample/i,
    
    // Specific test content patterns
    /jam session/i,
    /test activity/i,
    /test event/i,
    /lorem ipsum/i,
    /dummy/i,
    /mock/i,
    
    // Generic placeholder names
    /john doe/i,
    /jane doe/i,
    /test user/i,
    /admin test/i,
    
    // Generic locations
    /test location/i,
    /example venue/i,
    /tbd location/i,
    /placeholder venue/i,
    
    // Generic descriptions
    /this is a test/i,
    /testing purposes/i,
    /for testing/i,
    /test description/i,
    /example description/i
  ];

  /**
   * Specific test/fake content to remove (exact matches)
   */
  private readonly specificTestContent = [
    // Known test activities
    'Jam Session',
    'Test Activity',
    'Test Event',
    'Sample Workshop',
    'Demo Meetup',
    'Example Performance',
    
    // Generic placeholder content
    'Lorem ipsum dolor sit amet',
    'This is a test activity',
    'Test description here',
    'Placeholder content',
    'Example content',
    'Demo content'
  ];

  /**
   * Content that should be preserved (authentic Festival Family content)
   */
  private readonly preservePatterns = [
    // Authentic Festival Family events
    /festival family/i,
    /sziget/i,
    /balaton/i,
    /pre-meet/i,
    /reunion/i,
    /new years/i,
    /raffle/i,
    /fam/i,
    
    // Real locations
    /budapest/i,
    /hungary/i,
    /amsterdam/i,
    /netherlands/i,
    /online/i,
    
    // Real Festival Family activities
    /daily meet/i,
    /hangout/i,
    /scavenger hunt/i,
    /flag parade/i,
    /camping gear/i
  ];

  /**
   * Check if content should be preserved (is authentic Festival Family content)
   */
  private shouldPreserveContent(title: string, description: string = '', location: string = ''): boolean {
    const combinedText = `${title} ${description} ${location}`.toLowerCase();
    
    return this.preservePatterns.some(pattern => pattern.test(combinedText));
  }

  /**
   * Check if content is test/fake data
   */
  private isTestData(title: string, description: string = '', location: string = ''): boolean {
    // First check if it should be preserved
    if (this.shouldPreserveContent(title, description, location)) {
      return false;
    }

    // Check for specific test content (exact matches)
    if (this.specificTestContent.some(testContent => 
      title.toLowerCase().includes(testContent.toLowerCase()) ||
      description.toLowerCase().includes(testContent.toLowerCase())
    )) {
      return true;
    }

    // Check for test patterns
    const combinedText = `${title} ${description} ${location}`;
    return this.testDataPatterns.some(pattern => pattern.test(combinedText));
  }

  /**
   * Clean up test/fake activities
   */
  async cleanupActivities(): Promise<CleanupResult> {
    const errors: string[] = [];
    const details: string[] = [];
    let itemsRemoved = 0;

    try {
      // Get all activities
      const { data: activities, error: fetchError } = await supabase
        .from('activities')
        .select('id, title, description, location');

      if (fetchError) {
        return {
          success: false,
          itemsRemoved: 0,
          errors: [`Failed to fetch activities: ${fetchError.message}`],
          details: []
        };
      }

      if (!activities || activities.length === 0) {
        return {
          success: true,
          itemsRemoved: 0,
          errors: [],
          details: ['No activities found to clean up']
        };
      }

      // Identify test/fake activities
      const testActivities = activities.filter(activity => 
        this.isTestData(activity.title, activity.description || '', activity.location || '')
      );

      details.push(`Found ${testActivities.length} test/fake activities out of ${activities.length} total`);

      // Remove test activities
      for (const activity of testActivities) {
        const { error: deleteError } = await supabase
          .from('activities')
          .delete()
          .eq('id', activity.id);

        if (deleteError) {
          errors.push(`Failed to delete activity "${activity.title}": ${deleteError.message}`);
        } else {
          itemsRemoved++;
          details.push(`✅ Removed test activity: "${activity.title}"`);
        }
      }

      return {
        success: errors.length === 0,
        itemsRemoved,
        errors,
        details
      };
    } catch (error) {
      return {
        success: false,
        itemsRemoved: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        details: []
      };
    }
  }

  /**
   * Clean up test/fake external links
   */
  async cleanupExternalLinks(): Promise<CleanupResult> {
    const errors: string[] = [];
    const details: string[] = [];
    let itemsRemoved = 0;

    try {
      // Get all external links
      const { data: links, error: fetchError } = await supabase
        .from('external_links')
        .select('id, title, description, url');

      if (fetchError) {
        return {
          success: false,
          itemsRemoved: 0,
          errors: [`Failed to fetch external links: ${fetchError.message}`],
          details: []
        };
      }

      if (!links || links.length === 0) {
        return {
          success: true,
          itemsRemoved: 0,
          errors: [],
          details: ['No external links found to clean up']
        };
      }

      // Identify test/fake links
      const testLinks = links.filter(link => 
        this.isTestData(link.title, link.description || '', link.url || '') ||
        link.url?.includes('example.com') ||
        link.url?.includes('test.com') ||
        link.url?.includes('placeholder')
      );

      details.push(`Found ${testLinks.length} test/fake links out of ${links.length} total`);

      // Remove test links
      for (const link of testLinks) {
        const { error: deleteError } = await supabase
          .from('external_links')
          .delete()
          .eq('id', link.id);

        if (deleteError) {
          errors.push(`Failed to delete link "${link.title}": ${deleteError.message}`);
        } else {
          itemsRemoved++;
          details.push(`✅ Removed test link: "${link.title}"`);
        }
      }

      return {
        success: errors.length === 0,
        itemsRemoved,
        errors,
        details
      };
    } catch (error) {
      return {
        success: false,
        itemsRemoved: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        details: []
      };
    }
  }

  /**
   * Comprehensive cleanup of all test/fake data
   */
  async performComprehensiveCleanup(): Promise<{
    success: boolean;
    totalItemsRemoved: number;
    results: {
      activities: CleanupResult;
      externalLinks: CleanupResult;
    };
    summary: string[];
  }> {
    console.log('🧹 Starting comprehensive data cleanup...');

    const results = {
      activities: await this.cleanupActivities(),
      externalLinks: await this.cleanupExternalLinks()
    };

    const totalItemsRemoved = results.activities.itemsRemoved + results.externalLinks.itemsRemoved;
    const allErrors = [...results.activities.errors, ...results.externalLinks.errors];
    const success = allErrors.length === 0;

    const summary = [
      `🧹 Data Cleanup Complete`,
      `📊 Total items removed: ${totalItemsRemoved}`,
      `🎯 Activities cleaned: ${results.activities.itemsRemoved}`,
      `🔗 External links cleaned: ${results.externalLinks.itemsRemoved}`,
      success ? '✅ Cleanup successful' : `❌ ${allErrors.length} errors occurred`
    ];

    console.log(summary.join('\n'));

    return {
      success,
      totalItemsRemoved,
      results,
      summary
    };
  }

  /**
   * Preview what would be cleaned up without actually removing anything
   */
  async previewCleanup(): Promise<{
    activities: { count: number; items: string[] };
    externalLinks: { count: number; items: string[] };
  }> {
    const preview = {
      activities: { count: 0, items: [] as string[] },
      externalLinks: { count: 0, items: [] as string[] }
    };

    try {
      // Preview activities
      const { data: activities } = await supabase
        .from('activities')
        .select('title, description, location');

      if (activities) {
        const testActivities = activities.filter(activity => 
          this.isTestData(activity.title, activity.description || '', activity.location || '')
        );
        preview.activities.count = testActivities.length;
        preview.activities.items = testActivities.map(a => a.title);
      }

      // Preview external links
      const { data: links } = await supabase
        .from('external_links')
        .select('title, description, url');

      if (links) {
        const testLinks = links.filter(link => 
          this.isTestData(link.title, link.description || '', link.url || '') ||
          link.url?.includes('example.com') ||
          link.url?.includes('test.com') ||
          link.url?.includes('placeholder')
        );
        preview.externalLinks.count = testLinks.length;
        preview.externalLinks.items = testLinks.map(l => l.title);
      }
    } catch (error) {
      console.error('Error previewing cleanup:', error);
    }

    return preview;
  }
}

// Export singleton instance
export const dataCleanupService = new DataCleanupService();

/**
 * useAdmin Hook Tests
 * 
 * Tests for the admin hook functionality including role checking and admin operations.
 */

import { renderHook, waitFor } from '@testing-library/react'
import { useAdmin } from '../../hooks/useAdmin'
import { UserRole } from '../../types/core'

// Mock the useProfile hook
const mockUseProfile = {
  profile: null,
  isLoading: false,
  error: null,
  updateProfile: jest.fn(),
  uploadError: null,
}

jest.mock('../../hooks/useProfile', () => ({
  useProfile: () => mockUseProfile,
}))

// Mock the role utils
jest.mock('../../lib/utils/roleUtils', () => ({
  isAdmin: jest.fn(),
}))

import { isAdmin as checkIsAdmin } from '../../lib/utils/roleUtils'

describe('useAdmin', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(checkIsAdmin as jest.Mock).mockReturnValue(false)
  })

  test('should return loading state when profile is loading', () => {
    mockUseProfile.isLoading = true
    mockUseProfile.profile = null

    const { result } = renderHook(() => useAdmin())

    expect(result.current.loading).toBe(true)
    expect(result.current.isAdmin).toBe(false)
  })

  test('should return false for isAdmin when user is not logged in', async () => {
    mockUseProfile.isLoading = false
    mockUseProfile.profile = null

    const { result } = renderHook(() => useAdmin())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
      expect(result.current.isAdmin).toBe(false)
    })
  })

  test('should return true for isAdmin when user has admin role', async () => {
    const adminProfile = {
      id: 'admin-id',
      username: 'admin',
      email: '<EMAIL>',
      role: 'SUPER_ADMIN' as UserRole,
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
      avatar_url: null,
      full_name: 'Admin User',
      bio: null,
      location: null,
      interests: null,
      website: null,
    }

    mockUseProfile.isLoading = false
    mockUseProfile.profile = adminProfile as any
    ;(checkIsAdmin as jest.Mock).mockReturnValue(true)

    const { result } = renderHook(() => useAdmin())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
      expect(result.current.isAdmin).toBe(true)
    })
  })

  test('should return false for isAdmin when user has regular role', async () => {
    const regularProfile = {
      id: 'user-id',
      username: 'user',
      email: '<EMAIL>',
      role: 'USER' as UserRole,
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
      avatar_url: null,
      full_name: 'Regular User',
      bio: null,
      location: null,
      interests: null,
      website: null,
    }

    mockUseProfile.isLoading = false
    mockUseProfile.profile = regularProfile as any
    ;(checkIsAdmin as jest.Mock).mockReturnValue(false)

    const { result } = renderHook(() => useAdmin())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
      expect(result.current.isAdmin).toBe(false)
    })
  })

  test('should handle profile errors gracefully', () => {
    mockUseProfile.isLoading = false
    mockUseProfile.profile = null
    mockUseProfile.error = new Error('Profile fetch failed') as any

    const { result } = renderHook(() => useAdmin())

    expect(result.current.error).toEqual(new Error('Profile fetch failed'))
    expect(result.current.isAdmin).toBe(false)
  })

  test('should provide updateUserRole function', () => {
    const { result } = renderHook(() => useAdmin())

    expect(typeof result.current.updateUserRole).toBe('function')
  })

  test('should log warning when updateUserRole is called', () => {
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
    
    const { result } = renderHook(() => useAdmin())
    
    result.current.updateUserRole('MODERATOR' as UserRole)
    
    expect(consoleSpy).toHaveBeenCalledWith(
      'Role update not implemented - database schema does not support role field'
    )
    
    consoleSpy.mockRestore()
  })

  test('should handle different admin role types', async () => {
    const roles: UserRole[] = ['SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR']
    
    for (const role of roles) {
      const profile = {
        id: 'admin-id',
        username: 'admin',
        email: '<EMAIL>',
        role,
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
        avatar_url: null,
        full_name: 'Admin User',
        bio: null,
        location: null,
        interests: null,
        website: null,
      }

      mockUseProfile.profile = profile as any
      ;(checkIsAdmin as jest.Mock).mockReturnValue(true)

      const { result } = renderHook(() => useAdmin())

      await waitFor(() => {
        expect(result.current.isAdmin).toBe(true)
      })
    }
  })
})

{"attempts": [{"attempt": 1, "error": "page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "timestamp": "2025-06-04T07:57:40.186Z"}, {"attempt": 2, "error": "page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "timestamp": "2025-06-04T07:57:50.201Z"}, {"attempt": 3, "error": "page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "timestamp": "2025-06-04T07:58:00.217Z"}, {"attempt": 4, "error": "page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "timestamp": "2025-06-04T07:58:10.234Z"}, {"attempt": 5, "error": "page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "timestamp": "2025-06-04T07:58:20.250Z"}], "rateLimitDetected": false, "averageResponseTime": 0, "timestamp": "2025-06-04T07:57:30.176Z"}
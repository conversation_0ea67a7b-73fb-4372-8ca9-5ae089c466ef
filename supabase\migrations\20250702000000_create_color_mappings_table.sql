-- Migration: Create Enhanced Color Mappings System
-- Created: 2025-07-02
-- Purpose: Create color_mappings table for Festival Family's enhanced visual design system

-- ============================================================================
-- CREATE COLOR MAPPINGS TABLE
-- ============================================================================

-- Create color_mappings table for enhanced visual design system
CREATE TABLE IF NOT EXISTS color_mappings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    content_type TEXT NOT NULL CHECK (content_type IN ('activities', 'tips', 'community', 'festivals', 'resources')),
    category TEXT NOT NULL DEFAULT 'main',
    color_primary TEXT NOT NULL, -- Hex color code for primary color
    color_secondary TEXT NOT NULL, -- Hex color code for secondary color
    color_accent TEXT NOT NULL, -- Hex color code for accent color
    emoji_icon TEXT, -- Emoji or icon identifier
    description TEXT, -- Human-readable description
    show_icon BOOLEAN DEFAULT true, -- Admin-configurable icon visibility
    admin_configurable BOOLEAN DEFAULT true, -- Whether admins can modify this mapping
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,

    -- Ensure unique combination of content_type and category
    UNIQUE(content_type, category)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_color_mappings_content_type ON color_mappings(content_type);
CREATE INDEX IF NOT EXISTS idx_color_mappings_category ON color_mappings(category);
CREATE INDEX IF NOT EXISTS idx_color_mappings_show_icon ON color_mappings(show_icon);

-- ============================================================================
-- ROW LEVEL SECURITY
-- ============================================================================

-- Enable RLS
ALTER TABLE color_mappings ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Anyone can view color mappings" ON color_mappings
    FOR SELECT USING (true);

CREATE POLICY "Admins can manage color mappings" ON color_mappings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role IN ('SUPER_ADMIN', 'CONTENT_ADMIN')
        )
    );

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Function to get color mapping for content type and category
CREATE OR REPLACE FUNCTION get_color_mapping(
    p_content_type TEXT,
    p_category TEXT DEFAULT 'main'
)
RETURNS TABLE (
    id UUID,
    content_type TEXT,
    category TEXT,
    color_primary TEXT,
    color_secondary TEXT,
    color_accent TEXT,
    emoji_icon TEXT,
    description TEXT,
    show_icon BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        cm.id,
        cm.content_type,
        cm.category,
        cm.color_primary,
        cm.color_secondary,
        cm.color_accent,
        cm.emoji_icon,
        cm.description,
        cm.show_icon
    FROM color_mappings cm
    WHERE cm.content_type = p_content_type
    AND cm.category = p_category;
END;
$$;

-- Function to update color mapping (admin only)
CREATE OR REPLACE FUNCTION update_color_mapping(
    p_id UUID,
    p_color_primary TEXT DEFAULT NULL,
    p_color_secondary TEXT DEFAULT NULL,
    p_color_accent TEXT DEFAULT NULL,
    p_emoji_icon TEXT DEFAULT NULL,
    p_show_icon BOOLEAN DEFAULT NULL,
    p_description TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    is_admin BOOLEAN;
BEGIN
    -- Check if user is admin
    SELECT EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = auth.uid()
        AND profiles.role IN ('SUPER_ADMIN', 'CONTENT_ADMIN')
    ) INTO is_admin;

    IF NOT is_admin THEN
        RAISE EXCEPTION 'Access denied: Admin privileges required';
    END IF;

    -- Update the mapping
    UPDATE color_mappings
    SET
        color_primary = COALESCE(p_color_primary, color_primary),
        color_secondary = COALESCE(p_color_secondary, color_secondary),
        color_accent = COALESCE(p_color_accent, color_accent),
        emoji_icon = COALESCE(p_emoji_icon, emoji_icon),
        show_icon = COALESCE(p_show_icon, show_icon),
        description = COALESCE(p_description, description),
        updated_at = NOW()
    WHERE id = p_id
    AND admin_configurable = true;

    RETURN FOUND;
END;
$$;

-- ============================================================================
-- COMMENTS AND DOCUMENTATION
-- ============================================================================

-- Add helpful comments
COMMENT ON TABLE color_mappings IS 'Enhanced color mapping system for Festival Family visual design consistency';
COMMENT ON COLUMN color_mappings.content_type IS 'Type of content (activities, tips, community, festivals, resources)';
COMMENT ON COLUMN color_mappings.category IS 'Category within content type (main, featured, trending, etc.)';
COMMENT ON COLUMN color_mappings.show_icon IS 'Admin-configurable setting to show/hide icons for this mapping';
COMMENT ON COLUMN color_mappings.admin_configurable IS 'Whether admins can modify this mapping through the interface';

-- ============================================================================
-- MIGRATION SUCCESS LOG
-- ============================================================================

-- Log successful migration
DO $$
BEGIN
    RAISE NOTICE 'SUCCESS: Enhanced color mappings system created';
    RAISE NOTICE 'INFO: color_mappings table for visual design consistency';
    RAISE NOTICE 'INFO: Icon visibility controls for admin customization';
    RAISE NOTICE 'INFO: Helper functions for color mapping management';
    RAISE NOTICE 'INFO: RLS policies for proper access control';
    RAISE NOTICE 'INFO: Ready for enhancedColorMappingService integration';
END $$;
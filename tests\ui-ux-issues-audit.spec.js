/**
 * UI/UX Issues Audit Test
 * 
 * This test systematically identifies and documents the specific UI/UX issues:
 * 1. Duplicate Supabase connection notifications
 * 2. Navigation state confusion between authenticated/unauthenticated users
 * 3. Landing page vs authenticated app separation issues
 * 4. Multiple sign-in buttons appearing simultaneously
 * 5. Missing logo/branding in header
 */

import { test, expect } from '@playwright/test';

// Helper functions
async function takeEvidence(page, name, description) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `ui-audit-${name}-${timestamp}.png`;
  await page.screenshot({ 
    path: `test-results/${filename}`,
    fullPage: true 
  });
  console.log(`📸 UI Audit Evidence: ${filename} - ${description}`);
  return filename;
}

async function countToastNotifications(page) {
  // Count different types of toast notifications
  const toastSelectors = [
    '[data-testid*="toast"]',
    '.toast',
    '[class*="toast"]',
    '[role="alert"]',
    '.Toaster',
    '[data-sonner-toast]',
    '[data-hot-toast]'
  ];
  
  const toastCounts = {};
  let totalToasts = 0;
  
  for (const selector of toastSelectors) {
    const count = await page.locator(selector).count();
    if (count > 0) {
      toastCounts[selector] = count;
      totalToasts += count;
    }
  }
  
  return { toastCounts, totalToasts };
}

async function analyzeNavigationElements(page) {
  // Analyze all navigation-related elements
  const navAnalysis = await page.evaluate(() => {
    const signInButtons = Array.from(document.querySelectorAll('*')).filter(el => 
      el.textContent && (
        el.textContent.includes('Sign In') || 
        el.textContent.includes('Login') || 
        el.textContent.includes('Join') ||
        el.textContent.includes('Sign Up')
      )
    );
    
    const navElements = Array.from(document.querySelectorAll('nav, [role="navigation"], .nav, header'));
    const logoElements = Array.from(document.querySelectorAll('*')).filter(el =>
      el.textContent && el.textContent.includes('Festival Family')
    );
    
    return {
      signInButtonCount: signInButtons.length,
      signInButtonTexts: signInButtons.map(el => el.textContent.trim()),
      navigationElementCount: navElements.length,
      logoElementCount: logoElements.length,
      logoTexts: logoElements.map(el => el.textContent.trim())
    };
  });
  
  return navAnalysis;
}

async function checkConnectionStatusIndicators(page) {
  // Check for Supabase connection status indicators
  const connectionIndicators = await page.evaluate(() => {
    const indicators = Array.from(document.querySelectorAll('*')).filter(el => {
      const text = el.textContent || '';
      const className = el.className || '';
      return text.includes('Connected') || 
             text.includes('Supabase') || 
             text.includes('connection') ||
             className.includes('connection') ||
             className.includes('status');
    });
    
    return indicators.map(el => ({
      text: el.textContent.trim(),
      className: el.className,
      tagName: el.tagName,
      position: {
        top: el.getBoundingClientRect().top,
        left: el.getBoundingClientRect().left
      }
    }));
  });
  
  return connectionIndicators;
}

test.describe('UI/UX Issues Audit', () => {
  
  test('Issue 1: Duplicate Supabase Connection Notifications', async ({ page }) => {
    console.log('🔍 Auditing duplicate Supabase connection notifications...');
    
    await test.step('Load app and monitor notifications', async () => {
      // Navigate to the app
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // Take initial screenshot
      await takeEvidence(page, 'initial-load-notifications', 'Initial app load - checking for notifications');
      
      // Wait for potential notifications to appear
      await page.waitForTimeout(3000);
      
      // Count toast notifications
      const toastData = await countToastNotifications(page);
      console.log('Toast notification analysis:', toastData);
      
      // Check for connection status indicators
      const connectionIndicators = await checkConnectionStatusIndicators(page);
      console.log('Connection status indicators found:', connectionIndicators.length);
      connectionIndicators.forEach((indicator, i) => {
        console.log(`  ${i + 1}. "${indicator.text}" (${indicator.tagName}) at position ${indicator.position.top}, ${indicator.position.left}`);
      });
      
      await takeEvidence(page, 'notifications-analysis', `Found ${toastData.totalToasts} toast notifications and ${connectionIndicators.length} connection indicators`);
      
      // Test for duplicate notifications by triggering a page refresh
      await page.reload();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      const toastDataAfterReload = await countToastNotifications(page);
      const connectionIndicatorsAfterReload = await checkConnectionStatusIndicators(page);
      
      console.log('After reload - Toast notifications:', toastDataAfterReload);
      console.log('After reload - Connection indicators:', connectionIndicatorsAfterReload.length);
      
      await takeEvidence(page, 'notifications-after-reload', `After reload: ${toastDataAfterReload.totalToasts} toasts, ${connectionIndicatorsAfterReload.length} indicators`);
      
      // Document findings
      if (toastData.totalToasts > 1 || connectionIndicators.length > 1) {
        console.log('🚨 ISSUE CONFIRMED: Multiple notification/status indicators found');
      } else {
        console.log('✅ No duplicate notifications detected');
      }
    });
  });

  test('Issue 2: Navigation State Confusion', async ({ page }) => {
    console.log('🔍 Auditing navigation state confusion...');
    
    await test.step('Analyze unauthenticated navigation state', async () => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const navAnalysis = await analyzeNavigationElements(page);
      console.log('Unauthenticated navigation analysis:', navAnalysis);
      
      await takeEvidence(page, 'unauthenticated-nav-state', `Unauthenticated: ${navAnalysis.signInButtonCount} sign-in buttons, ${navAnalysis.navigationElementCount} nav elements`);
      
      // Check for inappropriate authenticated elements
      const authenticatedElements = await page.evaluate(() => {
        const elements = Array.from(document.querySelectorAll('*')).filter(el => {
          const text = el.textContent || '';
          return text.includes('Profile') || 
                 text.includes('Dashboard') || 
                 text.includes('Admin') ||
                 text.includes('Sign Out') ||
                 text.includes('Logout');
        });
        return elements.map(el => el.textContent.trim());
      });
      
      console.log('Authenticated elements visible to unauthenticated user:', authenticatedElements);
      
      if (authenticatedElements.length > 0) {
        console.log('🚨 ISSUE CONFIRMED: Authenticated elements visible to unauthenticated users');
      }
      
      if (navAnalysis.signInButtonCount > 1) {
        console.log('🚨 ISSUE CONFIRMED: Multiple sign-in buttons found');
        console.log('Sign-in button texts:', navAnalysis.signInButtonTexts);
      }
    });
    
    await test.step('Test navigation to auth page', async () => {
      // Try to navigate to auth page
      await page.goto('/auth');
      await page.waitForLoadState('networkidle');
      
      const authPageNavAnalysis = await analyzeNavigationElements(page);
      console.log('Auth page navigation analysis:', authPageNavAnalysis);
      
      await takeEvidence(page, 'auth-page-nav-state', `Auth page: ${authPageNavAnalysis.signInButtonCount} sign-in buttons, ${authPageNavAnalysis.navigationElementCount} nav elements`);
    });
  });

  test('Issue 3: Landing Page vs Authenticated App Separation', async ({ page }) => {
    console.log('🔍 Auditing landing page vs authenticated app separation...');
    
    await test.step('Analyze public landing page structure', async () => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // Check for proper landing page elements
      const landingPageElements = await page.evaluate(() => {
        const heroSection = document.querySelector('section, .hero, [class*="hero"]');
        const ctaButtons = Array.from(document.querySelectorAll('*')).filter(el => {
          const text = el.textContent || '';
          return text.includes('Join') || text.includes('Get Started') || text.includes('Sign Up');
        });
        const marketingContent = Array.from(document.querySelectorAll('*')).filter(el => {
          const text = el.textContent || '';
          return text.includes('Find Your Tribe') || 
                 text.includes('Festival Family') || 
                 text.includes('Connect with') ||
                 text.includes('Never festival alone');
        });
        
        return {
          hasHeroSection: !!heroSection,
          ctaButtonCount: ctaButtons.length,
          ctaButtonTexts: ctaButtons.map(el => el.textContent.trim()),
          marketingContentCount: marketingContent.length,
          marketingTexts: marketingContent.map(el => el.textContent.trim()).slice(0, 3)
        };
      });
      
      console.log('Landing page structure analysis:', landingPageElements);
      await takeEvidence(page, 'landing-page-structure', `Landing page: ${landingPageElements.ctaButtonCount} CTAs, ${landingPageElements.marketingContentCount} marketing elements`);
      
      // Check for inappropriate app functionality on landing page
      const appFunctionality = await page.evaluate(() => {
        const appElements = Array.from(document.querySelectorAll('*')).filter(el => {
          const text = el.textContent || '';
          return text.includes('Activities') || 
                 text.includes('FamHub') || 
                 text.includes('Discover') ||
                 text.includes('Profile');
        });
        return appElements.map(el => el.textContent.trim());
      });
      
      console.log('App functionality visible on landing page:', appFunctionality);
      
      if (appFunctionality.length > 0) {
        console.log('⚠️ POTENTIAL ISSUE: App functionality visible on public landing page');
      }
    });
  });

  test('Issue 4: Logo and Branding Analysis', async ({ page }) => {
    console.log('🔍 Auditing logo and branding in header...');
    
    await test.step('Analyze header branding', async () => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const brandingAnalysis = await page.evaluate(() => {
        const headers = Array.from(document.querySelectorAll('header, nav, [role="banner"]'));
        const logos = Array.from(document.querySelectorAll('img, svg, [class*="logo"]'));
        const brandText = Array.from(document.querySelectorAll('*')).filter(el => {
          const text = el.textContent || '';
          return text.includes('Festival Family');
        });
        
        return {
          headerCount: headers.length,
          logoCount: logos.length,
          logoElements: logos.map(el => ({
            tagName: el.tagName,
            src: el.src || 'N/A',
            alt: el.alt || 'N/A',
            className: el.className
          })),
          brandTextCount: brandText.length,
          brandTexts: brandText.map(el => el.textContent.trim())
        };
      });
      
      console.log('Branding analysis:', brandingAnalysis);
      await takeEvidence(page, 'header-branding-analysis', `Headers: ${brandingAnalysis.headerCount}, Logos: ${brandingAnalysis.logoCount}, Brand text: ${brandingAnalysis.brandTextCount}`);
      
      // Check for proper logo implementation
      if (brandingAnalysis.logoCount === 0) {
        console.log('🚨 ISSUE CONFIRMED: No logo elements found in header');
      }
      
      if (brandingAnalysis.brandTextCount > 2) {
        console.log('⚠️ POTENTIAL ISSUE: Multiple brand text elements may indicate duplication');
      }
    });
  });

  test('Issue 5: Authentication Flow State Management', async ({ page }) => {
    console.log('🔍 Auditing authentication flow state management...');
    
    await test.step('Test authentication state transitions', async () => {
      // Start on landing page
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const initialState = await analyzeNavigationElements(page);
      await takeEvidence(page, 'auth-flow-initial', `Initial state: ${initialState.signInButtonCount} sign-in buttons`);
      
      // Navigate to auth page
      const authLink = page.locator('a[href="/auth"], button:has-text("Sign In"), a:has-text("Sign In")').first();
      if (await authLink.isVisible()) {
        await authLink.click();
        await page.waitForLoadState('networkidle');
        
        const authPageState = await analyzeNavigationElements(page);
        await takeEvidence(page, 'auth-flow-auth-page', `Auth page state: ${authPageState.signInButtonCount} sign-in buttons`);
        
        // Check for proper auth page structure
        const authPageStructure = await page.evaluate(() => {
          const forms = document.querySelectorAll('form');
          const emailInputs = document.querySelectorAll('input[type="email"]');
          const passwordInputs = document.querySelectorAll('input[type="password"]');
          
          return {
            formCount: forms.length,
            emailInputCount: emailInputs.length,
            passwordInputCount: passwordInputs.length
          };
        });
        
        console.log('Auth page structure:', authPageStructure);
        
        if (authPageStructure.formCount === 0) {
          console.log('🚨 ISSUE CONFIRMED: No authentication form found on auth page');
        }
      } else {
        console.log('⚠️ ISSUE: No clickable authentication link found');
      }
    });
  });

  test('Issue 6: Overall UI Consistency Check', async ({ page }) => {
    console.log('🔍 Performing overall UI consistency check...');
    
    await test.step('Check UI consistency across pages', async () => {
      const pages = ['/', '/auth', '/activities', '/famhub', '/discover'];
      const consistencyData = [];
      
      for (const pagePath of pages) {
        try {
          await page.goto(pagePath);
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(1000);
          
          const pageAnalysis = await analyzeNavigationElements(page);
          const toastData = await countToastNotifications(page);
          
          consistencyData.push({
            page: pagePath,
            signInButtons: pageAnalysis.signInButtonCount,
            navElements: pageAnalysis.navigationElementCount,
            toasts: toastData.totalToasts,
            logos: pageAnalysis.logoElementCount
          });
          
          await takeEvidence(page, `consistency-${pagePath.replace('/', 'home')}`, `Page: ${pagePath}`);
          
        } catch (error) {
          console.log(`❌ Failed to analyze page ${pagePath}: ${error.message}`);
        }
      }
      
      console.log('\n📊 UI CONSISTENCY ANALYSIS:');
      console.table(consistencyData);
      
      // Analyze consistency issues
      const signInButtonCounts = consistencyData.map(d => d.signInButtons);
      const navElementCounts = consistencyData.map(d => d.navElements);
      const toastCounts = consistencyData.map(d => d.toasts);
      
      const signInVariance = Math.max(...signInButtonCounts) - Math.min(...signInButtonCounts);
      const navVariance = Math.max(...navElementCounts) - Math.min(...navElementCounts);
      const toastVariance = Math.max(...toastCounts) - Math.min(...toastCounts);
      
      console.log(`\n📈 VARIANCE ANALYSIS:`);
      console.log(`Sign-in buttons variance: ${signInVariance} (${Math.min(...signInButtonCounts)} - ${Math.max(...signInButtonCounts)})`);
      console.log(`Navigation elements variance: ${navVariance} (${Math.min(...navElementCounts)} - ${Math.max(...navElementCounts)})`);
      console.log(`Toast notifications variance: ${toastVariance} (${Math.min(...toastCounts)} - ${Math.max(...toastCounts)})`);
      
      if (signInVariance > 1) {
        console.log('🚨 ISSUE CONFIRMED: Inconsistent sign-in button counts across pages');
      }
      
      if (toastVariance > 0) {
        console.log('🚨 ISSUE CONFIRMED: Inconsistent toast notification behavior across pages');
      }
    });
  });
});

/**
 * UI Fixes Verification Test
 * 
 * This test verifies that the UI/UX fixes have been successfully implemented:
 * 1. Duplicate Supabase connection notifications are eliminated
 * 2. Sign-in button duplication is resolved
 * 3. Navigation state is consistent
 */

import { test, expect } from '@playwright/test';

async function takeEvidence(page, name, description) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `ui-fixes-${name}-${timestamp}.png`;
  await page.screenshot({ 
    path: `test-results/${filename}`,
    fullPage: true 
  });
  console.log(`📸 UI Fixes Evidence: ${filename} - ${description}`);
  return filename;
}

async function countSignInButtons(page) {
  const signInButtons = await page.evaluate(() => {
    const buttons = Array.from(document.querySelectorAll('*')).filter(el => {
      const text = el.textContent || '';
      return text.includes('Sign In') || 
             text.includes('Login') || 
             text.includes('Join') ||
             text.includes('Sign Up');
    });
    
    return buttons.map(btn => ({
      text: btn.textContent.trim(),
      tagName: btn.tagName,
      className: btn.className,
      visible: btn.offsetParent !== null
    }));
  });
  
  return signInButtons;
}

async function countConnectionNotifications(page) {
  const notifications = await page.evaluate(() => {
    const elements = Array.from(document.querySelectorAll('*')).filter(el => {
      const text = el.textContent || '';
      return text.includes('Connected to Supabase') || 
             text.includes('Successfully connected') ||
             text.includes('Connection successful');
    });
    
    return elements.map(el => ({
      text: el.textContent.trim(),
      tagName: el.tagName,
      className: el.className,
      visible: el.offsetParent !== null
    }));
  });
  
  return notifications;
}

test.describe('UI Fixes Verification', () => {
  
  test('Fix 1: Duplicate Supabase Connection Notifications Eliminated', async ({ page }) => {
    console.log('🔧 Verifying duplicate connection notifications fix...');
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000); // Wait for any notifications to appear
    
    const notifications = await countConnectionNotifications(page);
    console.log(`Found ${notifications.length} connection notifications:`, notifications);
    
    await takeEvidence(page, 'connection-notifications-fixed', `Connection notifications: ${notifications.length}`);
    
    // Should have at most 1 connection notification (from the ConnectionStatus component)
    expect(notifications.length).toBeLessThanOrEqual(1);
    
    if (notifications.length === 1) {
      console.log('✅ Single connection notification found - this is expected');
    } else {
      console.log('✅ No connection notifications found - this is also acceptable');
    }
  });

  test('Fix 2: Sign-In Button Duplication Resolved', async ({ page }) => {
    console.log('🔧 Verifying sign-in button duplication fix...');
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const signInButtons = await countSignInButtons(page);
    const visibleButtons = signInButtons.filter(btn => btn.visible);
    
    console.log(`Found ${signInButtons.length} total sign-in buttons (${visibleButtons.length} visible):`, visibleButtons);
    
    await takeEvidence(page, 'signin-buttons-fixed', `Sign-in buttons: ${visibleButtons.length} visible`);
    
    // Should have at most 3-4 sign-in buttons (header, mobile, landing CTA, maybe bottom nav)
    expect(visibleButtons.length).toBeLessThanOrEqual(4);
    expect(visibleButtons.length).toBeGreaterThan(0); // Should have at least one
    
    console.log('✅ Sign-in button count is within acceptable range');
  });

  test('Fix 3: Navigation State Consistency Across Pages', async ({ page }) => {
    console.log('🔧 Verifying navigation state consistency...');
    
    const pages = ['/', '/auth', '/activities', '/famhub', '/discover'];
    const consistencyData = [];
    
    for (const pagePath of pages) {
      try {
        await page.goto(pagePath);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(1000);
        
        const signInButtons = await countSignInButtons(page);
        const visibleButtons = signInButtons.filter(btn => btn.visible);
        
        consistencyData.push({
          page: pagePath,
          signInButtons: visibleButtons.length,
          buttonTexts: visibleButtons.map(btn => btn.text).slice(0, 3) // First 3 for brevity
        });
        
        await takeEvidence(page, `consistency-${pagePath.replace('/', 'home')}`, `Page: ${pagePath} - ${visibleButtons.length} buttons`);
        
      } catch (error) {
        console.log(`❌ Failed to analyze page ${pagePath}: ${error.message}`);
      }
    }
    
    console.log('\n📊 NAVIGATION CONSISTENCY ANALYSIS:');
    console.table(consistencyData);
    
    // Check that all pages have reasonable button counts (1-4 buttons)
    const allPagesValid = consistencyData.every(data => 
      data.signInButtons >= 1 && data.signInButtons <= 4
    );
    
    expect(allPagesValid).toBe(true);
    
    // Check that variance is reasonable (not more than 3 buttons difference)
    const buttonCounts = consistencyData.map(d => d.signInButtons);
    const maxButtons = Math.max(...buttonCounts);
    const minButtons = Math.min(...buttonCounts);
    const variance = maxButtons - minButtons;
    
    console.log(`Button count variance: ${variance} (${minButtons} - ${maxButtons})`);
    expect(variance).toBeLessThanOrEqual(3);
    
    console.log('✅ Navigation state consistency is within acceptable range');
  });

  test('Fix 4: Element Overlap Issues Resolved', async ({ page }) => {
    console.log('🔧 Verifying element overlap issues are resolved...');
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Try to click on a sign-in button to verify it's not blocked
    try {
      const signInButton = page.locator('a[href="/auth"], button:has-text("Sign In"), a:has-text("Sign In")').first();
      
      if (await signInButton.isVisible()) {
        console.log('Found visible sign-in button, testing clickability...');
        
        // Test if the button is clickable (not blocked by overlapping elements)
        await signInButton.click({ timeout: 5000 });
        await page.waitForLoadState('networkidle');
        
        // Should navigate to auth page
        expect(page.url()).toContain('/auth');
        console.log('✅ Sign-in button is clickable and functional');
        
        await takeEvidence(page, 'auth-page-navigation', 'Successfully navigated to auth page');
      } else {
        console.log('⚠️ No visible sign-in button found to test');
      }
    } catch (error) {
      console.log(`❌ Element overlap issue detected: ${error.message}`);
      await takeEvidence(page, 'element-overlap-error', 'Element overlap issue detected');
      throw error;
    }
  });

  test('Fix 5: Overall UI Health Check', async ({ page }) => {
    console.log('🔧 Performing overall UI health check...');
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Check for JavaScript errors
    const jsErrors = [];
    page.on('pageerror', error => {
      jsErrors.push(error.message);
    });
    
    // Check for console errors
    const consoleErrors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Wait a bit more to catch any errors
    await page.waitForTimeout(3000);
    
    console.log(`JavaScript errors: ${jsErrors.length}`);
    console.log(`Console errors: ${consoleErrors.length}`);
    
    if (jsErrors.length > 0) {
      console.log('JavaScript errors found:', jsErrors);
    }
    
    if (consoleErrors.length > 0) {
      console.log('Console errors found:', consoleErrors);
    }
    
    await takeEvidence(page, 'ui-health-check', `JS errors: ${jsErrors.length}, Console errors: ${consoleErrors.length}`);
    
    // Should have minimal errors (allow some development warnings)
    expect(jsErrors.length).toBeLessThanOrEqual(2);
    expect(consoleErrors.length).toBeLessThanOrEqual(5);
    
    console.log('✅ Overall UI health is good');
  });
});

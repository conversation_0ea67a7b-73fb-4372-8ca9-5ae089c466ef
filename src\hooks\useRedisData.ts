/**
 * Redis Data Hooks - High-Performance Data Fetching with Caching
 * 
 * Provides Redis-optimized hooks that extend the unified data hooks with
 * intelligent caching, subscription storm elimination, and performance tracking.
 * 
 * Features:
 * - Sub-100ms data access for cached data
 * - Intelligent cache invalidation
 * - Performance metrics tracking
 * - Graceful fallback to database queries
 * - Subscription storm elimination
 * 
 * @module useRedisData
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { redisDataService } from '@/lib/redis/redis-data-service'
import { unifiedDataService, type Activity, type Event, type Community, type Announcement, type ActivityFilters, type EventFilters } from '@/lib/data/unified-data-service'

// ============================================================================
// PERFORMANCE TRACKING
// ============================================================================

interface PerformanceMetrics {
  cacheHitRate: number
  averageResponseTime: number
  totalRequests: number
  cacheHits: number
  cacheMisses: number
}

const usePerformanceTracking = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    cacheHitRate: 0,
    averageResponseTime: 0,
    totalRequests: 0,
    cacheHits: 0,
    cacheMisses: 0
  })

  const updateMetrics = useCallback((responseTime: number, cacheHit: boolean) => {
    setMetrics(prev => {
      const newTotalRequests = prev.totalRequests + 1
      const newCacheHits = prev.cacheHits + (cacheHit ? 1 : 0)
      const newCacheMisses = prev.cacheMisses + (cacheHit ? 0 : 1)
      const newCacheHitRate = (newCacheHits / newTotalRequests) * 100
      const newAverageResponseTime = ((prev.averageResponseTime * prev.totalRequests) + responseTime) / newTotalRequests

      return {
        cacheHitRate: newCacheHitRate,
        averageResponseTime: newAverageResponseTime,
        totalRequests: newTotalRequests,
        cacheHits: newCacheHits,
        cacheMisses: newCacheMisses
      }
    })
  }, [])

  return { metrics, updateMetrics }
}

// ============================================================================
// REDIS ACTIVITIES HOOK
// ============================================================================

export const useRedisActivities = (filters?: ActivityFilters) => {
  const [activities, setActivities] = useState<Activity[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { metrics, updateMetrics } = usePerformanceTracking()
  const subscriptionRef = useRef<(() => void) | null>(null)

  const fetchActivities = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const startTime = performance.now()
      const data = await redisDataService.getActivities(filters)
      const responseTime = performance.now() - startTime
      
      // Determine if this was a cache hit based on response time
      const cacheHit = responseTime < 50 // Sub-50ms indicates cache hit
      updateMetrics(responseTime, cacheHit)
      
      setActivities(data)
      console.log(`⚡ Activities loaded: ${data.length} items (${responseTime.toFixed(2)}ms)`)
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch activities'
      setError(errorMessage)
      console.error('useRedisActivities error:', err)
    } finally {
      setIsLoading(false)
    }
  }, [filters, updateMetrics])

  // Setup real-time subscription with intelligent caching
  useEffect(() => {
    // Initial fetch
    fetchActivities()

    // Setup subscription for real-time updates
    const unsubscribe = unifiedDataService.subscribeToActivities(async (updatedActivities) => {
      // Invalidate cache when data changes
      await redisDataService.invalidateCache('activities')
      
      // Refetch with fresh cache
      fetchActivities()
    })

    subscriptionRef.current = unsubscribe

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current()
        subscriptionRef.current = null
      }
    }
  }, [fetchActivities])

  const refetch = useCallback(() => {
    fetchActivities()
  }, [fetchActivities])

  return {
    activities,
    isLoading,
    error,
    refetch,
    cacheHitRate: metrics.cacheHitRate,
    averageResponseTime: metrics.averageResponseTime
  }
}

// ============================================================================
// REDIS EVENTS HOOK
// ============================================================================

export const useRedisEvents = (filters?: EventFilters) => {
  const [events, setEvents] = useState<Event[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { metrics, updateMetrics } = usePerformanceTracking()
  const subscriptionRef = useRef<(() => void) | null>(null)

  const fetchEvents = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const startTime = performance.now()
      const data = await redisDataService.getEvents(filters)
      const responseTime = performance.now() - startTime
      
      // Determine if this was a cache hit based on response time
      const cacheHit = responseTime < 50 // Sub-50ms indicates cache hit
      updateMetrics(responseTime, cacheHit)
      
      setEvents(data)
      console.log(`⚡ Events loaded: ${data.length} items (${responseTime.toFixed(2)}ms)`)
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch events'
      setError(errorMessage)
      console.error('useRedisEvents error:', err)
    } finally {
      setIsLoading(false)
    }
  }, [filters, updateMetrics])

  // Setup real-time subscription with intelligent caching
  useEffect(() => {
    // Initial fetch
    fetchEvents()

    // Setup subscription for real-time updates
    const unsubscribe = unifiedDataService.subscribeToEvents(async (updatedEvents) => {
      // Invalidate cache when data changes
      await redisDataService.invalidateCache('events')
      
      // Refetch with fresh cache
      fetchEvents()
    })

    subscriptionRef.current = unsubscribe

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current()
        subscriptionRef.current = null
      }
    }
  }, [fetchEvents])

  const refetch = useCallback(() => {
    fetchEvents()
  }, [fetchEvents])

  return {
    events,
    isLoading,
    error,
    refetch,
    cacheHitRate: metrics.cacheHitRate,
    averageResponseTime: metrics.averageResponseTime
  }
}

// ============================================================================
// REDIS COMMUNITIES HOOK
// ============================================================================

export const useRedisCommunities = () => {
  const [communities, setCommunities] = useState<Community[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { metrics, updateMetrics } = usePerformanceTracking()

  const fetchCommunities = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const startTime = performance.now()
      const data = await redisDataService.getCommunities()
      const responseTime = performance.now() - startTime
      
      // Determine if this was a cache hit based on response time
      const cacheHit = responseTime < 50 // Sub-50ms indicates cache hit
      updateMetrics(responseTime, cacheHit)
      
      setCommunities(data)
      console.log(`⚡ Communities loaded: ${data.length} items (${responseTime.toFixed(2)}ms)`)
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch communities'
      setError(errorMessage)
      console.error('useRedisCommunities error:', err)
    } finally {
      setIsLoading(false)
    }
  }, [updateMetrics])

  useEffect(() => {
    fetchCommunities()
  }, [fetchCommunities])

  const refetch = useCallback(() => {
    fetchCommunities()
  }, [fetchCommunities])

  return {
    communities,
    isLoading,
    error,
    refetch,
    cacheHitRate: metrics.cacheHitRate,
    averageResponseTime: metrics.averageResponseTime
  }
}

// ============================================================================
// REDIS ANNOUNCEMENTS HOOK
// ============================================================================

export const useRedisAnnouncements = () => {
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { metrics, updateMetrics } = usePerformanceTracking()
  const subscriptionRef = useRef<(() => void) | null>(null)

  const fetchAnnouncements = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const startTime = performance.now()
      const data = await redisDataService.getAnnouncements()
      const responseTime = performance.now() - startTime
      
      // Determine if this was a cache hit based on response time
      const cacheHit = responseTime < 50 // Sub-50ms indicates cache hit
      updateMetrics(responseTime, cacheHit)
      
      setAnnouncements(data)
      console.log(`⚡ Announcements loaded: ${data.length} items (${responseTime.toFixed(2)}ms)`)
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch announcements'
      setError(errorMessage)
      console.error('useRedisAnnouncements error:', err)
    } finally {
      setIsLoading(false)
    }
  }, [updateMetrics])

  // Setup real-time subscription with intelligent caching
  useEffect(() => {
    // Initial fetch
    fetchAnnouncements()

    // Setup subscription for real-time updates
    const unsubscribe = unifiedDataService.subscribeToAnnouncements(async (updatedAnnouncements) => {
      // Invalidate cache when data changes
      await redisDataService.invalidateCache('announcements')
      
      // Refetch with fresh cache
      fetchAnnouncements()
    })

    subscriptionRef.current = unsubscribe

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current()
        subscriptionRef.current = null
      }
    }
  }, [fetchAnnouncements])

  const refetch = useCallback(() => {
    fetchAnnouncements()
  }, [fetchAnnouncements])

  return {
    announcements,
    isLoading,
    error,
    refetch,
    cacheHitRate: metrics.cacheHitRate,
    averageResponseTime: metrics.averageResponseTime
  }
}

// ============================================================================
// COMBINED REDIS DATA HOOK
// ============================================================================

export const useRedisData = () => {
  const activities = useRedisActivities()
  const events = useRedisEvents()
  const communities = useRedisCommunities()
  const announcements = useRedisAnnouncements()

  const isLoading = activities.isLoading || events.isLoading || communities.isLoading || announcements.isLoading
  const hasError = activities.error || events.error || communities.error || announcements.error

  // Calculate overall performance metrics
  const overallCacheHitRate = (
    activities.cacheHitRate + 
    events.cacheHitRate + 
    communities.cacheHitRate + 
    announcements.cacheHitRate
  ) / 4

  const overallAverageResponseTime = (
    activities.averageResponseTime + 
    events.averageResponseTime + 
    communities.averageResponseTime + 
    announcements.averageResponseTime
  ) / 4

  const refetchAll = useCallback(() => {
    activities.refetch()
    events.refetch()
    communities.refetch()
    announcements.refetch()
  }, [activities.refetch, events.refetch, communities.refetch, announcements.refetch])

  return {
    activities: activities.activities,
    events: events.events,
    communities: communities.communities,
    announcements: announcements.announcements,
    isLoading,
    error: hasError,
    refetchAll,
    performance: {
      cacheHitRate: overallCacheHitRate,
      averageResponseTime: overallAverageResponseTime
    }
  }
}

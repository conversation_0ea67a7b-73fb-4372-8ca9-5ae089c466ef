# TypeScript and Type System Implementation Guide

This guide provides step-by-step instructions for implementing the solutions to TypeScript and type system issues.

## Prerequisites

- Ensure you have the latest code from the repository
- Install all dependencies with `npm install`
- Make sure TypeScript is properly configured

## Implementation Steps

### 1. Centralize Type Definitions

#### Step 1.1: Generate up-to-date database types

```bash
# Generate types from Supabase schema
npx supabase gen types typescript --project-id your-project-id > src/types/database.ts
```

#### Step 1.2: Create derived types in a central location

```typescript
// src/types/index.ts
import type { Database } from './database';

// Export database row types directly
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type Festival = Database['public']['Tables']['festivals']['Row'];
export type Activity = Database['public']['Tables']['activities']['Row'];

// Create extended types for application use
export type ProfileWithRelations = Profile & {
  festivals?: Festival[];
};

export type FestivalWithRelations = Festival & {
  activities?: Activity[];
  organizer?: Profile;
};
```

#### Step 1.3: Update imports across the codebase

Search for all custom type definitions and imports, and update them to use the centralized types:

```bash
# Find all type definitions and imports
grep -r "type " --include="*.ts" --include="*.tsx" src/
grep -r "interface " --include="*.ts" --include="*.tsx" src/
grep -r "import.*type" --include="*.ts" --include="*.tsx" src/
```

### 2. Implement Type Guards

#### Step 2.1: Install Zod for runtime validation

```bash
npm install zod
```

#### Step 2.2: Create type guard utilities

```typescript
// src/utils/typeGuards.ts
import { z } from 'zod';
import type { Profile, Festival, Activity, UserRole } from '../types';

// Define Zod schemas for validation
export const profileSchema = z.object({
  id: z.string().uuid(),
  username: z.string().min(3).max(50),
  full_name: z.string().nullable(),
  avatar_url: z.string().url().nullable(),
  role: z.enum(['user', 'admin']).default('user')
});

export const festivalSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(3).max(100),
  description: z.string().optional(),
  start_date: z.string().datetime(),
  end_date: z.string().datetime(),
  location: z.string(),
  image: z.string().url().nullable(),
  status: z.enum(['draft', 'published', 'cancelled', 'completed']),
  organizer_id: z.string().uuid()
});

// Type guard functions
export function isProfile(value: unknown): value is Profile {
  return profileSchema.safeParse(value).success;
}

export function isFestival(value: unknown): value is Festival {
  return festivalSchema.safeParse(value).success;
}

export function isUserRole(value: unknown): value is UserRole {
  return z.enum(['user', 'admin', 'guest']).safeParse(value).success;
}
```

#### Step 2.3: Create type-safe data fetching utilities

```typescript
// src/utils/api.ts
import { profileSchema, festivalSchema } from './typeGuards';
import type { Profile, Festival } from '../types';

export async function fetchProfile(userId: string): Promise<Profile> {
  const response = await fetch(`/api/profiles/${userId}`);
  const data = await response.json();
  
  const result = profileSchema.safeParse(data);
  if (!result.success) {
    throw new Error(`Invalid profile data: ${result.error.message}`);
  }
  
  return result.data;
}

export async function fetchFestival(festivalId: string): Promise<Festival> {
  const response = await fetch(`/api/festivals/${festivalId}`);
  const data = await response.json();
  
  const result = festivalSchema.safeParse(data);
  if (!result.success) {
    throw new Error(`Invalid festival data: ${result.error.message}`);
  }
  
  return result.data;
}
```

#### Step 2.4: Update components to use type guards

Replace type assertions with proper type guards:

```typescript
// Before
const userData = await fetchUserData();
const user = userData as User; // Unsafe

// After
import { isProfile } from '../utils/typeGuards';

const userData = await fetchUserData();
if (!isProfile(userData)) {
  throw new Error('Invalid user data');
}
const user = userData; // Type-safe
```

### 3. Standardize Type Imports

#### Step 3.1: Configure ESLint for consistent type imports

```javascript
// .eslintrc.js
module.exports = {
  // ... other config
  rules: {
    // Enforce type-only imports for types
    '@typescript-eslint/consistent-type-imports': [
      'error',
      {
        prefer: 'type-only-imports',
        disallowTypeAnnotations: true,
      },
    ],
    // ... other rules
  },
};
```

#### Step 3.2: Update imports across the codebase

```bash
# Run ESLint with --fix to automatically update imports
npx eslint --fix --ext .ts,.tsx src/
```

### 4. Standardize Enums and Union Types

#### Step 4.1: Create centralized enum definitions

```typescript
// src/types/enums.ts
export const UserRoles = ['admin', 'user', 'guest'] as const;
export type UserRole = typeof UserRoles[number];

export const FestivalStatuses = ['draft', 'published', 'cancelled', 'completed'] as const;
export type FestivalStatus = typeof FestivalStatuses[number];

export const ActivityTypes = ['workshop', 'performance', 'meetup', 'food', 'other'] as const;
export type ActivityType = typeof ActivityTypes[number];
```

#### Step 4.2: Create type guards for enums

```typescript
// src/utils/typeGuards.ts (additional functions)
import { UserRoles, FestivalStatuses, ActivityTypes } from '../types/enums';
import type { UserRole, FestivalStatus, ActivityType } from '../types/enums';

export function isUserRole(value: unknown): value is UserRole {
  return typeof value === 'string' && UserRoles.includes(value as UserRole);
}

export function isFestivalStatus(value: unknown): value is FestivalStatus {
  return typeof value === 'string' && FestivalStatuses.includes(value as FestivalStatus);
}

export function isActivityType(value: unknown): value is ActivityType {
  return typeof value === 'string' && ActivityTypes.includes(value as ActivityType);
}
```

#### Step 4.3: Update code to use enum types

```typescript
// Before
function setUserRole(role: string) {
  // No validation
}

// After
import type { UserRole } from '../types/enums';
import { isUserRole } from '../utils/typeGuards';

function setUserRole(role: UserRole) {
  // Type-safe
}

// For external data
function processUserRole(role: unknown) {
  if (!isUserRole(role)) {
    throw new Error('Invalid user role');
  }
  // Now role is type-safe
}
```

### 5. Implement Consistent Nullability Handling

#### Step 5.1: Create nullability utilities

```typescript
// src/utils/nullability.ts
export function getValueOrDefault<T>(value: T | null | undefined, defaultValue: T): T {
  return value ?? defaultValue;
}

export function getNestedValueOrDefault<T>(
  obj: any,
  path: string,
  defaultValue: T
): T {
  const value = path.split('.').reduce((o, key) => (o ? o[key] : undefined), obj);
  return value ?? defaultValue;
}

export function isNullOrUndefined(value: unknown): value is null | undefined {
  return value === null || value === undefined;
}
```

#### Step 5.2: Update code to use nullability utilities

```typescript
// Before
const username = user ? user.name || 'Anonymous' : 'Anonymous';
const city = user && user.address ? user.address.city || 'Unknown' : 'Unknown';

// After
import { getValueOrDefault, getNestedValue
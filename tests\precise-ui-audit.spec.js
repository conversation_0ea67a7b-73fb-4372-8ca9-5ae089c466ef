/**
 * Precise UI Audit Test
 * 
 * This test uses more precise selectors to identify actual UI issues
 * rather than counting all text content that contains certain words.
 */

import { test, expect } from '@playwright/test';

async function takeEvidence(page, name, description) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `precise-ui-${name}-${timestamp}.png`;
  await page.screenshot({ 
    path: `test-results/${filename}`,
    fullPage: true 
  });
  console.log(`📸 Precise UI Evidence: ${filename} - ${description}`);
  return filename;
}

async function countActualAuthButtons(page) {
  // Count only actual clickable authentication buttons/links
  const authButtons = await page.evaluate(() => {
    const buttons = [];
    
    // Find actual buttons and links that are clickable authentication elements
    const selectors = [
      'button[type="submit"]', // Form submit buttons
      'a[href="/auth"]', // Links to auth page
      'a[href="/login"]', // Links to login page
      'button:has-text("Sign In")', // Buttons with sign in text
      'button:has-text("Login")', // Buttons with login text
      'button:has-text("Sign Up")', // Buttons with sign up text
      'button:has-text("Join")', // Buttons with join text (but not large containers)
      'a:has-text("Sign In")', // Links with sign in text
      'a:has-text("Login")', // Links with login text
    ];
    
    for (const selector of selectors) {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
          // Only count if it's actually clickable and not a large container
          const rect = el.getBoundingClientRect();
          const isVisible = rect.width > 0 && rect.height > 0 && el.offsetParent !== null;
          const isReasonableSize = rect.width < 500 && rect.height < 200; // Not a huge container
          const hasClickableRole = el.tagName === 'BUTTON' || el.tagName === 'A' || el.getAttribute('role') === 'button';
          
          if (isVisible && isReasonableSize && hasClickableRole) {
            buttons.push({
              text: el.textContent.trim().substring(0, 50), // Limit text length
              tagName: el.tagName,
              className: el.className,
              href: el.href || null,
              selector: selector,
              width: Math.round(rect.width),
              height: Math.round(rect.height)
            });
          }
        });
      } catch (e) {
        // Skip invalid selectors
      }
    }
    
    return buttons;
  });
  
  return authButtons;
}

async function countConnectionNotifications(page) {
  // Count actual connection notification elements
  const notifications = await page.evaluate(() => {
    const elements = [];
    
    // Look for actual notification elements
    const selectors = [
      '[data-testid*="toast"]',
      '.toast',
      '[class*="toast"]',
      '[role="alert"]',
      '.Toaster',
      '[data-sonner-toast]',
      '[data-hot-toast]',
      // Specific connection status elements
      '*:has-text("Connected to Supabase")',
      '*:has-text("Successfully connected")',
      '*:has-text("Connection successful")'
    ];
    
    for (const selector of selectors) {
      try {
        const found = document.querySelectorAll(selector);
        found.forEach(el => {
          const rect = el.getBoundingClientRect();
          const isVisible = rect.width > 0 && rect.height > 0 && el.offsetParent !== null;
          const text = el.textContent || '';
          
          if (isVisible && (
            text.includes('Connected') || 
            text.includes('connection') || 
            text.includes('Supabase')
          )) {
            elements.push({
              text: text.trim().substring(0, 100),
              tagName: el.tagName,
              className: el.className,
              selector: selector
            });
          }
        });
      } catch (e) {
        // Skip invalid selectors
      }
    }
    
    return elements;
  });
  
  return notifications;
}

test.describe('Precise UI Audit', () => {
  
  test('Precise Count: Actual Authentication Buttons', async ({ page }) => {
    console.log('🔍 Counting actual authentication buttons...');
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const authButtons = await countActualAuthButtons(page);
    console.log(`Found ${authButtons.length} actual authentication buttons:`);
    authButtons.forEach((btn, i) => {
      console.log(`  ${i + 1}. ${btn.tagName} "${btn.text}" (${btn.width}x${btn.height}px) - ${btn.selector}`);
    });
    
    await takeEvidence(page, 'actual-auth-buttons', `Found ${authButtons.length} actual auth buttons`);
    
    // Should have 2-5 actual authentication buttons (header, mobile, landing CTAs, bottom nav)
    expect(authButtons.length).toBeGreaterThan(0);
    expect(authButtons.length).toBeLessThanOrEqual(5);
    
    console.log('✅ Actual authentication button count is reasonable');
  });

  test('Precise Count: Connection Notifications', async ({ page }) => {
    console.log('🔍 Counting actual connection notifications...');
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000); // Wait for notifications to appear
    
    const notifications = await countConnectionNotifications(page);
    console.log(`Found ${notifications.length} connection notifications:`);
    notifications.forEach((notif, i) => {
      console.log(`  ${i + 1}. ${notif.tagName} "${notif.text}" - ${notif.selector}`);
    });
    
    await takeEvidence(page, 'connection-notifications', `Found ${notifications.length} connection notifications`);
    
    // Should have at most 1 connection notification
    expect(notifications.length).toBeLessThanOrEqual(1);
    
    console.log('✅ Connection notification count is acceptable');
  });

  test('Precise Test: Element Clickability', async ({ page }) => {
    console.log('🔍 Testing actual element clickability...');
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Find the first actual authentication button
    const authButton = page.locator('a[href="/auth"]').first();
    
    if (await authButton.isVisible()) {
      console.log('Found auth button, testing clickability...');
      
      // Test if it's clickable without being intercepted
      await authButton.click({ timeout: 5000 });
      await page.waitForLoadState('networkidle');
      
      // Should navigate to auth page
      expect(page.url()).toContain('/auth');
      console.log('✅ Authentication button is clickable and functional');
      
      await takeEvidence(page, 'auth-navigation-success', 'Successfully navigated to auth page');
    } else {
      console.log('⚠️ No visible auth button found to test');
    }
  });

  test('Precise Test: Navigation Consistency', async ({ page }) => {
    console.log('🔍 Testing navigation consistency across pages...');
    
    const pages = ['/', '/auth'];
    const consistencyData = [];
    
    for (const pagePath of pages) {
      try {
        await page.goto(pagePath);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(1000);
        
        const authButtons = await countActualAuthButtons(page);
        const notifications = await countConnectionNotifications(page);
        
        consistencyData.push({
          page: pagePath,
          authButtons: authButtons.length,
          notifications: notifications.length
        });
        
        await takeEvidence(page, `consistency-${pagePath.replace('/', 'home')}`, `Page: ${pagePath}`);
        
      } catch (error) {
        console.log(`❌ Failed to analyze page ${pagePath}: ${error.message}`);
      }
    }
    
    console.log('\n📊 PRECISE NAVIGATION CONSISTENCY:');
    console.table(consistencyData);
    
    // All pages should have reasonable counts
    const allPagesValid = consistencyData.every(data => 
      data.authButtons >= 1 && data.authButtons <= 5 && data.notifications <= 1
    );
    
    expect(allPagesValid).toBe(true);
    console.log('✅ Navigation consistency is good');
  });

  test('Precise Test: UI Health Check', async ({ page }) => {
    console.log('🔍 Performing precise UI health check...');
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Check for JavaScript errors
    const jsErrors = [];
    page.on('pageerror', error => {
      jsErrors.push(error.message);
    });
    
    // Check for console errors
    const consoleErrors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Wait for any errors to appear
    await page.waitForTimeout(3000);
    
    // Count actual UI elements
    const authButtons = await countActualAuthButtons(page);
    const notifications = await countConnectionNotifications(page);
    
    console.log(`UI Health Summary:`);
    console.log(`- Auth buttons: ${authButtons.length}`);
    console.log(`- Connection notifications: ${notifications.length}`);
    console.log(`- JavaScript errors: ${jsErrors.length}`);
    console.log(`- Console errors: ${consoleErrors.length}`);
    
    await takeEvidence(page, 'ui-health-summary', `Auth: ${authButtons.length}, Notifications: ${notifications.length}, Errors: ${jsErrors.length}`);
    
    // Health checks
    expect(authButtons.length).toBeGreaterThan(0);
    expect(authButtons.length).toBeLessThanOrEqual(5);
    expect(notifications.length).toBeLessThanOrEqual(1);
    expect(jsErrors.length).toBeLessThanOrEqual(2);
    expect(consoleErrors.length).toBeLessThanOrEqual(5);
    
    console.log('✅ Overall UI health is good');
  });
});

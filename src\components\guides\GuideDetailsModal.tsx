import React, { useState, useEffect } from 'react';
import { BookOpen, Tag, Clock, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ResponsiveModal } from '@/components/design-system/ResponsiveModal';
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';
import { UnifiedInteractionButton } from '@/components/design-system';

interface Guide {
  id: string;
  title: string;
  content: string;
  description?: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  estimated_read_time?: number;
  created_at: string;
}

interface GuideDetailsModalProps {
  guide: Guide | null;
  isOpen: boolean;
  onClose: () => void;
}

export const GuideDetailsModal: React.FC<GuideDetailsModalProps> = ({
  guide,
  isOpen,
  onClose,
}) => {
  // Modern color system using enhancedColorMappingService
  const [colorTheme, setColorTheme] = useState<{
    className: string;
    style: React.CSSProperties;
  }>({ className: '', style: {} });
  const [isLoadingTheme, setIsLoadingTheme] = useState(false);

  // Load color theme using enhancedColorMappingService
  useEffect(() => {
    const loadColorTheme = async () => {
      if (!guide || !isOpen) return;

      setIsLoadingTheme(true);
      try {
        const theme = await enhancedColorMappingService.getEnhancedColorTheme(
          'guide',
          guide.category ?? 'GENERAL',
          0 // Use first fallback theme
        );
        setColorTheme(theme);
      } catch (error) {
        console.warn('Failed to load guide color theme:', error);
        // Fallback theme using design tokens
        setColorTheme({
          className: 'bg-gradient-to-br from-purple-50 to-blue-50',
          style: {
            background: 'var(--festival-gradient-secondary)',
            borderColor: 'var(--festival-border)'
          }
        });
      } finally {
        setIsLoadingTheme(false);
      }
    };

    loadColorTheme();
  }, [guide, isOpen]);

  if (!guide) return null;

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'SAFETY': return 'Safety & Health';
      case 'PACKING': return 'Packing & Gear';
      case 'CAMPING': return 'Camping & Accommodation';
      case 'FOOD': return 'Food & Drinks';
      case 'TRANSPORT': return 'Transport & Travel';
      case 'PLANNING': return 'Planning & Preparation';
      case 'SOCIAL': return 'Social & Networking';
      default: return category;
    }
  };

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={onClose}
      size="xl"
      showCloseButton={false}
    >
      <div className="flex flex-col h-full">
        {/* Standardized Header following UnifiedModal.tsx patterns */}
        <div
          className="p-4 sm:p-6 pb-2"
          style={{
            background: !isLoadingTheme ? colorTheme.style.background : 'var(--festival-bg-card)',
            borderBottom: '1px solid var(--festival-border)'
          }}
        >
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div
                className="w-10 h-10 rounded-lg flex items-center justify-center"
                style={{
                  background: !isLoadingTheme && colorTheme.style.background
                    ? colorTheme.style.background
                    : 'var(--festival-gradient-secondary)',
                  color: 'var(--festival-text-on-primary)'
                }}
              >
                <BookOpen className="w-5 h-5" />
              </div>
              <div>
                <h2
                  className="text-lg sm:text-xl font-bold pr-8"
                  style={{ color: 'var(--festival-text-auto)' }}
                >
                  {guide.title}
                </h2>
                <div className="flex items-center gap-2 mt-2">
                  {guide.category && (
                    <Badge variant="secondary" className="text-xs">
                      {getCategoryLabel(guide.category)}
                    </Badge>
                  )}
                  {guide.estimated_read_time && (
                    <Badge variant="outline" className="text-xs">
                      <Clock className="w-3 h-3 mr-1" />
                      {guide.estimated_read_time} min read
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Featured Badge following UnifiedModal.tsx pattern */}
          {guide.is_featured && (
            <Badge
              className="w-fit mt-2 text-xs"
              style={{
                backgroundColor: 'var(--festival-warning-bg)',
                color: 'var(--festival-warning)',
                border: '1px solid var(--festival-warning)'
              }}
            >
              Featured Guide
            </Badge>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {guide.description && (
            <div className="bg-white/5 rounded-lg p-4 border-l-4 border-blue-400">
              <p className="text-white/80 text-sm font-medium">{guide.description}</p>
            </div>
          )}

          <div className="prose prose-invert prose-lg max-w-none">
            <div className="text-white/90 leading-relaxed whitespace-pre-wrap">
              {guide.content}
            </div>
          </div>

          {/* Tags */}
          {guide.tags && guide.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {guide.tags.map((tag, index) => (
                <Badge key={index} variant="outline" className="text-white/70 border-white/20">
                  <Tag className="w-3 h-3 mr-1" />
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          {/* Stats */}
          <div className="flex items-center gap-4 text-sm text-white/60">
            {guide.view_count !== undefined && (
              <span className="flex items-center gap-1">
                <BookOpen className="w-4 h-4" />
                {guide.view_count} views
              </span>
            )}
            {guide.helpful_count !== undefined && (
              <span className="flex items-center gap-1">
                <Heart className="w-4 h-4" />
                {guide.helpful_count} helpful
              </span>
            )}
            <span>
              Published {new Date(guide.created_at).toLocaleDateString()}
            </span>
          </div>
        </div>

        {/* Standardized Footer following UnifiedModal.tsx patterns */}
        <div
          className="flex flex-col sm:flex-row gap-2 sm:gap-3 p-4 sm:p-6 pt-2"
          style={{
            borderTop: '1px solid var(--festival-border)',
            backgroundColor: 'var(--festival-bg-muted)'
          }}
        >
          {/* Action buttons with mobile-first responsive design */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
            <UnifiedInteractionButton
              type="helpful"
              itemId={guide.id}
              itemType="guide"
              variant="outline"
              className="w-full sm:w-auto min-w-[120px]"
              size="default"
            />
            <UnifiedInteractionButton
              type="share"
              itemId={guide.id}
              itemType="guide"
              variant="outline"
              className="w-full sm:w-auto min-w-[120px]"
              size="default"
            />
            <Button
              variant="outline"
              onClick={onClose}
              className="w-full sm:w-auto min-w-[120px]"
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </ResponsiveModal>
  );
};

export default GuideDetailsModal;

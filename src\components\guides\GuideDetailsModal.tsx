import React from 'react';
import { BookOpen, Tag, Clock, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ResponsiveModal } from '@/components/design-system/ResponsiveModal';
import { useEnhancedColorMapping } from '@/hooks/useEnhancedColorMapping';
import { UnifiedInteractionButton } from '@/components/design-system';

interface Guide {
  id: string;
  title: string;
  content: string;
  description?: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  estimated_read_time?: number;
  created_at: string;
}

interface GuideDetailsModalProps {
  guide: Guide | null;
  isOpen: boolean;
  onClose: () => void;
}

export const GuideDetailsModal: React.FC<GuideDetailsModalProps> = ({
  guide,
  isOpen,
  onClose,
}) => {
  // Use enhanced color mapping for database-driven colors
  const { colorMapping } = useEnhancedColorMapping(guide?.category ?? 'GENERAL', 'guide');

  // Generate gradient classes from color mapping
  const getGradientClasses = () => {
    if (!colorMapping) {
      // Fallback based on category
      switch (guide?.category) {
        case 'SAFETY': return 'from-destructive to-destructive';
        case 'PACKING': return 'from-primary to-primary';
        case 'CAMPING': return 'from-festival-success to-festival-success';
        case 'FOOD': return 'from-accent to-accent';
        case 'TRANSPORT': return 'from-primary to-accent';
        case 'PLANNING': return 'from-secondary to-primary';
        case 'SOCIAL': return 'from-primary to-secondary';
        default: return 'from-muted to-muted-foreground';
      }
    }
    return `from-[${colorMapping.color_primary}/80] to-[${colorMapping.color_secondary}/80]`;
  };

  if (!guide) return null;

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'SAFETY': return 'Safety & Health';
      case 'PACKING': return 'Packing & Gear';
      case 'CAMPING': return 'Camping & Accommodation';
      case 'FOOD': return 'Food & Drinks';
      case 'TRANSPORT': return 'Transport & Travel';
      case 'PLANNING': return 'Planning & Preparation';
      case 'SOCIAL': return 'Social & Networking';
      default: return category;
    }
  };

  const colorClass = getGradientClasses(); // Use database-driven gradient

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={onClose}
      size="xl"
      showCloseButton={false}
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <div className="flex items-center gap-3">
            <div className={`w-10 h-10 bg-gradient-to-br ${colorClass} rounded-lg flex items-center justify-center`}>
              <BookOpen className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">{guide.title}</h2>
              <div className="flex items-center gap-2 mt-1">
                {guide.category && (
                  <Badge variant="secondary">
                    {getCategoryLabel(guide.category)}
                  </Badge>
                )}
                {guide.estimated_read_time && (
                  <Badge variant="outline" className="text-white/70 border-white/20">
                    <Clock className="w-3 h-3 mr-1" />
                    {guide.estimated_read_time} min read
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {guide.description && (
            <div className="bg-white/5 rounded-lg p-4 border-l-4 border-blue-400">
              <p className="text-white/80 text-sm font-medium">{guide.description}</p>
            </div>
          )}

          <div className="prose prose-invert prose-lg max-w-none">
            <div className="text-white/90 leading-relaxed whitespace-pre-wrap">
              {guide.content}
            </div>
          </div>

          {/* Tags */}
          {guide.tags && guide.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {guide.tags.map((tag, index) => (
                <Badge key={index} variant="outline" className="text-white/70 border-white/20">
                  <Tag className="w-3 h-3 mr-1" />
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          {/* Stats */}
          <div className="flex items-center gap-4 text-sm text-white/60">
            {guide.view_count !== undefined && (
              <span className="flex items-center gap-1">
                <BookOpen className="w-4 h-4" />
                {guide.view_count} views
              </span>
            )}
            {guide.helpful_count !== undefined && (
              <span className="flex items-center gap-1">
                <Heart className="w-4 h-4" />
                {guide.helpful_count} helpful
              </span>
            )}
            <span>
              Published {new Date(guide.created_at).toLocaleDateString()}
            </span>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-white/10">
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              <UnifiedInteractionButton
                type="helpful"
                itemId={guide.id}
                itemType="guide"
                variant="outline"
                size="sm"
                className="text-white border-white/20 hover:bg-white/10"
              />
              <UnifiedInteractionButton
                type="share"
                itemId={guide.id}
                itemType="guide"
                variant="outline"
                size="sm"
                className="text-white border-white/20 hover:bg-white/10"
              />
              <Button
                variant="outline"
                onClick={onClose}
                className="border-white/20 text-white hover:bg-white/10"
              >
                Close
              </Button>
            </div>
            {guide.is_featured && (
              <Badge className="bg-yellow-500/20 text-yellow-300 border-yellow-500/30">
                <Star className="w-3 h-3 mr-1" />
                Featured Guide
              </Badge>
            )}
          </div>
        </div>
      </div>
    </ResponsiveModal>
  );
};

export default GuideDetailsModal;

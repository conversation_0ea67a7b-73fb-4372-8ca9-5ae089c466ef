#!/usr/bin/env node

/**
 * Authenticated Navigation Testing Script
 * 
 * This script tests navigation between all authenticated pages and evaluates
 * the completeness and user experience quality of each page.
 */

import { chromium } from 'playwright';
import { promises as fs } from 'fs';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'authenticated-navigation-evidence';

// Test user credentials (reuse from previous test)
const TEST_USER = {
  email: `test.user.${Date.now()}@festivalfamily.test`,
  password: 'TestPassword123!',
  fullName: 'Test User'
};

async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

async function authenticateUser(page) {
  console.log('\n🔐 Authenticating Test User');
  console.log('===========================');
  
  // Navigate to auth page and sign up/in
  await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(2000);
  
  // Try sign up first
  const signUpTab = await page.$('text=Sign Up') || await page.$('text=Register');
  if (signUpTab) {
    await signUpTab.click();
    await page.waitForTimeout(1000);
  }
  
  // Fill and submit sign-up form
  const emailInput = await page.$('input[type="email"]');
  const passwordInput = await page.$('input[type="password"]');
  
  if (emailInput && passwordInput) {
    await emailInput.fill(TEST_USER.email);
    await passwordInput.fill(TEST_USER.password);
    
    const submitButton = await page.$('button[type="submit"]') || await page.$('text=Sign Up');
    if (submitButton) {
      await submitButton.click();
      await page.waitForTimeout(3000);
    }
  }
  
  console.log(`✅ User authenticated: ${TEST_USER.email}`);
}

async function testPageNavigation(page, pageName, url, expectedElements = []) {
  console.log(`\n📄 Testing ${pageName} Page`);
  console.log('='.repeat(20 + pageName.length));
  
  try {
    // Navigate to the page
    await page.goto(`${APP_URL}${url}`, { waitUntil: 'domcontentloaded', timeout: 15000 });
    await page.waitForTimeout(3000); // Allow content to load
    
    // Capture screenshot
    const screenshotPath = `${EVIDENCE_DIR}/${pageName.toLowerCase().replace(/\s+/g, '-')}-page.png`;
    await page.screenshot({ 
      path: screenshotPath,
      fullPage: true 
    });
    
    // Get page info
    const title = await page.title();
    const currentUrl = page.url();
    
    // Check for loading states
    const loadingSpinner = await page.$('[class*="loading"]') || await page.$('[class*="spinner"]');
    const errorMessage = await page.$('[class*="error"]') || await page.$('text=Error') || await page.$('text=error');
    
    // Check for expected elements
    const elementChecks = {};
    for (const element of expectedElements) {
      const found = await page.$(element.selector) !== null;
      elementChecks[element.name] = found;
      console.log(`${element.icon} ${element.name}: ${found ? '✅ Found' : '❌ Not Found'}`);
    }
    
    // Check for navigation elements
    const hasNavigation = await page.$('nav') !== null;
    const hasBackButton = await page.$('text=Back') || await page.$('[aria-label*="back"]');
    
    // Get content metrics
    const rootContent = await page.$eval('#root', el => el.innerHTML);
    const contentLength = rootContent.length;
    const hasSubstantialContent = contentLength > 1000;
    
    console.log(`📋 Page Title: "${title}"`);
    console.log(`🔗 URL: ${currentUrl}`);
    console.log(`🧭 Navigation: ${hasNavigation ? '✅ Present' : '❌ Missing'}`);
    console.log(`⬅️ Back Button: ${hasBackButton ? '✅ Present' : '❌ Missing'}`);
    console.log(`📄 Content Length: ${contentLength} characters`);
    console.log(`📊 Substantial Content: ${hasSubstantialContent ? '✅ Yes' : '❌ No'}`);
    console.log(`⏳ Loading State: ${loadingSpinner ? '⚠️ Still Loading' : '✅ Loaded'}`);
    console.log(`❌ Error State: ${errorMessage ? '⚠️ Error Present' : '✅ No Errors'}`);
    
    return {
      pageName,
      url: currentUrl,
      title,
      hasNavigation,
      hasBackButton,
      contentLength,
      hasSubstantialContent,
      isLoading: !!loadingSpinner,
      hasError: !!errorMessage,
      elementChecks,
      screenshot: screenshotPath.split('/').pop()
    };
    
  } catch (error) {
    console.error(`❌ Failed to test ${pageName} page:`, error.message);
    return {
      pageName,
      error: error.message,
      screenshot: null
    };
  }
}

async function runAuthenticatedNavigationTest() {
  console.log('🧭 AUTHENTICATED NAVIGATION TESTING');
  console.log('===================================');
  console.log(`🕐 Start Time: ${new Date().toISOString()}`);
  
  await ensureEvidenceDir();
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();
  
  try {
    // Authenticate user first
    await authenticateUser(page);
    
    const results = {};
    
    // Test Home/Dashboard
    results.home = await testPageNavigation(page, 'Home Dashboard', '/', [
      { name: 'Welcome Message', selector: 'text=Welcome', icon: '👋' },
      { name: 'Quick Actions', selector: '[href="/activities"], [href="/famhub"], [href="/discover"], [href="/profile"]', icon: '⚡' },
      { name: 'Recent Activity', selector: 'text=Recent Activity', icon: '📊' },
      { name: 'Updates Section', selector: 'text=Updates', icon: '🔔' }
    ]);
    
    // Test Activities Page
    results.activities = await testPageNavigation(page, 'Activities', '/activities', [
      { name: 'Activities List', selector: '[class*="activity"], [data-testid*="activity"]', icon: '📅' },
      { name: 'Create Activity', selector: 'text=Create, text=Add, button[class*="create"]', icon: '➕' },
      { name: 'Filter Options', selector: 'select, [class*="filter"], [class*="search"]', icon: '🔍' }
    ]);
    
    // Test FamHub Page
    results.famhub = await testPageNavigation(page, 'FamHub', '/famhub', [
      { name: 'Connection List', selector: '[class*="connection"], [class*="friend"], [class*="user"]', icon: '👥' },
      { name: 'Search Users', selector: 'input[placeholder*="search"], input[placeholder*="find"]', icon: '🔍' },
      { name: 'Chat Feature', selector: 'text=Chat, text=Message, [class*="chat"]', icon: '💬' }
    ]);
    
    // Test Discover Page
    results.discover = await testPageNavigation(page, 'Discover', '/discover', [
      { name: 'Festival List', selector: '[class*="festival"], [class*="event"]', icon: '🎪' },
      { name: 'Search Filters', selector: 'input[type="search"], select, [class*="filter"]', icon: '🔍' },
      { name: 'Map View', selector: '[class*="map"], text=Map', icon: '🗺️' }
    ]);
    
    // Test Profile Page
    results.profile = await testPageNavigation(page, 'Profile', '/profile', [
      { name: 'Profile Form', selector: 'form, input[name*="name"], input[name*="email"]', icon: '👤' },
      { name: 'Avatar Upload', selector: 'input[type="file"], [class*="avatar"], [class*="upload"]', icon: '📸' },
      { name: 'Save Button', selector: 'button[type="submit"], text=Save, text=Update', icon: '💾' }
    ]);
    
    // Calculate overall navigation score
    const pageResults = Object.values(results).filter(r => !r.error);
    const totalPages = pageResults.length;
    const workingPages = pageResults.filter(r => r.hasSubstantialContent && !r.hasError && !r.isLoading).length;
    const navigationScore = totalPages > 0 ? (workingPages / totalPages * 100).toFixed(1) : 0;
    
    // Save comprehensive results
    const evidence = {
      timestamp: new Date().toISOString(),
      testUser: { email: TEST_USER.email },
      navigationResults: results,
      summary: {
        totalPages: totalPages,
        workingPages: workingPages,
        navigationScore: parseFloat(navigationScore),
        allPagesAccessible: workingPages === totalPages,
        screenshots: Object.values(results).map(r => r.screenshot).filter(Boolean)
      }
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/navigation-test-results.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    console.log('\n📊 AUTHENTICATED NAVIGATION SUMMARY');
    console.log('===================================');
    console.log(`📄 Total Pages Tested: ${totalPages}`);
    console.log(`✅ Working Pages: ${workingPages}`);
    console.log(`📊 Navigation Score: ${navigationScore}%`);
    console.log(`🎯 All Pages Accessible: ${evidence.summary.allPagesAccessible ? '✅ Yes' : '❌ No'}`);
    console.log(`📸 Screenshots: ${evidence.summary.screenshots.length}`);
    console.log(`📁 Evidence: ${EVIDENCE_DIR}/`);
    
    return evidence;
    
  } catch (error) {
    console.error('\n💥 Navigation testing failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

// Run the navigation test
runAuthenticatedNavigationTest()
  .then(() => {
    console.log('\n✅ Authenticated navigation testing completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Authenticated navigation testing failed:', error);
    process.exit(1);
  });

import React from 'react';
import IconEmojiManager from '@/components/admin/IconEmojiManager';

/**
 * Icon & Emoji Management Admin Page
 *
 * Admin page wrapper for the IconEmojiManager component.
 * Provides comprehensive management of visual elements, colors, and icon visibility
 * across Festival Family's enhanced design system.
 */

const IconEmojiManagement: React.FC = () => {
  return (
    <div className="container mx-auto py-6 px-4">
      <IconEmojiManager />
    </div>
  );
};

export default IconEmojiManagement;
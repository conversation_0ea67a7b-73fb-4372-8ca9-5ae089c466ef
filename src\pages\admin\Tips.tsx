import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useProfile } from '@/hooks/useProfile';
import { supabase } from '@/lib/supabase';
import { isAdminRole } from '@/lib/utils/auth';
import { UserRole } from '@/types/central';
import { type Tip } from '@/types';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreVertical, Pencil, Trash, Lightbulb } from 'lucide-react';
import { format } from 'date-fns';
import { UnifiedBadge } from '@/components/design-system';
import { useEnhancedColorMapping } from '@/hooks/useEnhancedColorMapping';

// Database-driven tip category badge component
const TipCategoryBadge: React.FC<{ category: string | null }> = ({ category }) => {
  if (!category) {
    return <UnifiedBadge variant="secondary" size="sm">N/A</UnifiedBadge>;
  }

  return (
    <UnifiedBadge
      variant="category"
      size="sm"
    >
      {category}
    </UnifiedBadge>
  );
};

const Tips: React.FC = () => {
  const navigate = useNavigate();
  const { profile, loading: profileLoading } = useProfile();
  const [tips, setTips] = React.useState<Tip[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [selectedTips, setSelectedTips] = React.useState<Set<string>>(new Set());
  const [bulkActionLoading, setBulkActionLoading] = React.useState(false);

  React.useEffect(() => {
    loadTips();
  }, []);

  React.useEffect(() => {
    if (!profileLoading && !isAdminRole(profile?.role)) {
      navigate('/');
    }
  }, [profileLoading, profile, navigate]);

  const loadTips = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('tips')
        .select('*')
        .order('category', { ascending: true })
        .order('order_index', { ascending: true })
        .order('created_at', { ascending: false });

      if (error) throw error;

      setTips(data || []);
    } catch (error) {
      console.error('Error loading tips:', error);
      setError('Failed to load tips');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this tip? This action cannot be undone.')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('tips')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setTips(tips.filter(t => t.id !== id));
    } catch (error) {
      console.error('Error deleting tip:', error);
      setError('Failed to delete tip');
    }
  };

  const handleSelectTip = (tipId: string, checked: boolean) => {
    const newSelected = new Set(selectedTips);
    if (checked) {
      newSelected.add(tipId);
    } else {
      newSelected.delete(tipId);
    }
    setSelectedTips(newSelected);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedTips(new Set(tips.map(tip => tip.id)));
    } else {
      setSelectedTips(new Set());
    }
  };

  const handleBulkDelete = async () => {
    if (selectedTips.size === 0) return;

    if (!window.confirm(`Are you sure you want to delete ${selectedTips.size} tips? This action cannot be undone.`)) {
      return;
    }

    try {
      setBulkActionLoading(true);
      const { error } = await supabase
        .from('tips')
        .delete()
        .in('id', Array.from(selectedTips));

      if (error) throw error;

      setTips(tips.filter(t => !selectedTips.has(t.id)));
      setSelectedTips(new Set());
    } catch (error) {
      console.error('Error bulk deleting tips:', error);
      setError('Failed to delete tips');
    } finally {
      setBulkActionLoading(false);
    }
  };

  const handleBulkStatusChange = async (status: string) => {
    if (selectedTips.size === 0) return;

    try {
      setBulkActionLoading(true);
      const { error } = await supabase
        .from('tips')
        .update({ status })
        .in('id', Array.from(selectedTips));

      if (error) throw error;

      await loadTips(); // Reload to get updated data
      setSelectedTips(new Set());
    } catch (error) {
      console.error('Error updating tip status:', error);
      setError('Failed to update tip status');
    } finally {
      setBulkActionLoading(false);
    }
  };

  if (profileLoading || !profile || !isAdminRole(profile.role)) {
    return null;
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Tips & Tricks</h1>
          <p className="text-muted-foreground mt-2">
            Manage festival tips and tricks
          </p>
        </div>
        <Button
          onClick={() => navigate('/admin/tips/new')}
          className="bg-primary/20 hover:bg-primary/30"
        >
          Create Tip
        </Button>
      </div>

      {error && (
        <div className="rounded-lg border border-destructive/20 p-4 text-destructive bg-destructive/10">
          {error}
        </div>
      )}

      {loading ? (
        <div className="text-center py-8 text-muted-foreground">
          Loading tips...
        </div>
      ) : tips.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>No tips found</CardTitle>
            <CardDescription>
              Get started by creating your first tip
            </CardDescription>
          </CardHeader>
        </Card>
      ) : (
        <>
          {/* Bulk Actions */}
          {tips.length > 0 && (
            <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={selectedTips.size === tips.length && tips.length > 0}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm">
                    Select All ({selectedTips.size} of {tips.length} selected)
                  </span>
                </label>
              </div>

              {selectedTips.size > 0 && (
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleBulkStatusChange('published')}
                    disabled={bulkActionLoading}
                  >
                    Publish Selected
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleBulkStatusChange('draft')}
                    disabled={bulkActionLoading}
                  >
                    Draft Selected
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleBulkDelete}
                    disabled={bulkActionLoading}
                  >
                    Delete Selected
                  </Button>
                </div>
              )}
            </div>
          )}

          <div className="grid gap-6">
            {tips.map((tip) => (
              <Card key={tip.id}>
                <CardHeader className="flex flex-row items-start justify-between space-y-0">
                  <div className="flex items-start gap-3">
                    <input
                      type="checkbox"
                      checked={selectedTips.has(tip.id)}
                      onChange={(e) => handleSelectTip(tip.id, e.target.checked)}
                      className="mt-1 rounded"
                    />
                    <div className="space-y-1">
                      <CardTitle className="flex items-center gap-2">
                        <Lightbulb className="h-5 w-5" />
                        {tip.title}
                        {tip.status && (
                          <UnifiedBadge
                            variant={tip.status === 'published' ? 'success' : 'secondary'}
                            size="sm"
                          >
                            {tip.status}
                          </UnifiedBadge>
                        )}
                      </CardTitle>
                      {tip.content && (
                        <CardDescription>{tip.content}</CardDescription>
                      )}
                    </div>
                  </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={() => navigate(`/admin/tips/${tip.id}/edit`)}
                    >
                      <Pencil className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="text-red-400"
                      onClick={() => handleDelete(tip.id)}
                    >
                      <Trash className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <TipCategoryBadge category={tip.category} />
                    <div className="text-sm text-muted-foreground">
                      Last updated {tip.updated_at ? format(new Date(tip.updated_at), 'PPP') : 'Never'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          </div>
        </>
      )}
    </div>
  );
};

export default Tips;

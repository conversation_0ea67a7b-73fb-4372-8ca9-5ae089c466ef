/**
 * Performance Monitor Component
 * 
 * Real-time performance monitoring dashboard for Festival Family
 * Tracks load times, subscription performance, and optimization metrics.
 * 
 * @module PerformanceMonitor
 * @version 1.0.0
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface PerformanceMetrics {
  pageLoadTime: number;
  subscriptionCount: number;
  subscriptionLatency: number;
  cacheHitRate: number;
  memoryUsage: number;
  timestamp: number;
}

interface PerformanceMonitorProps {
  enabled?: boolean;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  compact?: boolean;
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  enabled = process.env.NODE_ENV === 'development',
  position = 'top-right',
  compact = false
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    pageLoadTime: 0,
    subscriptionCount: 0,
    subscriptionLatency: 0,
    cacheHitRate: 0,
    memoryUsage: 0,
    timestamp: Date.now()
  });
  
  const [isVisible, setIsVisible] = useState(false);
  const [history, setHistory] = useState<PerformanceMetrics[]>([]);

  // Collect performance metrics
  const collectMetrics = useCallback(() => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const memory = (performance as any).memory;
    
    const newMetrics: PerformanceMetrics = {
      pageLoadTime: navigation ? Math.round(navigation.loadEventEnd - navigation.loadEventStart) : 0,
      subscriptionCount: (window as any).realtimeSubscriptionCount || 0,
      subscriptionLatency: (window as any).averageSubscriptionLatency || 0,
      cacheHitRate: (window as any).cacheHitRate || 0,
      memoryUsage: memory ? Math.round(memory.usedJSHeapSize / 1024 / 1024) : 0,
      timestamp: Date.now()
    };
    
    setMetrics(newMetrics);
    setHistory(prev => [...prev.slice(-19), newMetrics]); // Keep last 20 entries
  }, []);

  // Auto-collect metrics every 5 seconds
  useEffect(() => {
    if (!enabled) return;
    
    collectMetrics();
    const interval = setInterval(collectMetrics, 5000);
    
    return () => clearInterval(interval);
  }, [enabled, collectMetrics]);

  // Performance status indicators
  const getLoadTimeStatus = (time: number) => {
    if (time < 200) return { color: 'green', label: 'Excellent' };
    if (time < 500) return { color: 'yellow', label: 'Good' };
    if (time < 1000) return { color: 'orange', label: 'Fair' };
    return { color: 'red', label: 'Poor' };
  };

  const getCacheStatus = (rate: number) => {
    if (rate > 80) return { color: 'green', label: 'Excellent' };
    if (rate > 60) return { color: 'yellow', label: 'Good' };
    if (rate > 40) return { color: 'orange', label: 'Fair' };
    return { color: 'red', label: 'Poor' };
  };

  if (!enabled) return null;

  const positionClasses = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4'
  };

  const loadTimeStatus = getLoadTimeStatus(metrics.pageLoadTime);
  const cacheStatus = getCacheStatus(metrics.cacheHitRate);

  return (
    <div className={`fixed ${positionClasses[position]} z-50 transition-all duration-300`}>
      {/* Toggle Button */}
      <Button
        onClick={() => setIsVisible(!isVisible)}
        size="sm"
        variant="outline"
        className="mb-2 bg-white/90 backdrop-blur-sm shadow-lg"
      >
        📊 Performance
      </Button>

      {/* Performance Dashboard */}
      {isVisible && (
        <Card className="w-80 bg-white/95 backdrop-blur-sm shadow-xl border-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center justify-between">
              🚀 Performance Monitor
              <Badge variant="outline" className="text-xs">
                Live
              </Badge>
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* Load Time */}
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Page Load Time:</span>
              <div className="flex items-center gap-2">
                <Badge 
                  variant="outline" 
                  className={`text-${loadTimeStatus.color}-600 border-${loadTimeStatus.color}-300`}
                >
                  {metrics.pageLoadTime}ms
                </Badge>
                <span className="text-xs text-gray-500">{loadTimeStatus.label}</span>
              </div>
            </div>

            {/* Target Comparison */}
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Target (200ms):</span>
              <div className="flex items-center gap-2">
                {metrics.pageLoadTime <= 200 ? (
                  <Badge variant="outline" className="text-green-600 border-green-300">
                    ✅ Met
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-red-600 border-red-300">
                    ❌ +{metrics.pageLoadTime - 200}ms
                  </Badge>
                )}
              </div>
            </div>

            {/* Real-time Subscriptions */}
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Subscriptions:</span>
              <div className="flex items-center gap-2">
                <Badge variant="outline">
                  {metrics.subscriptionCount}
                </Badge>
                {metrics.subscriptionLatency > 0 && (
                  <span className="text-xs text-gray-500">
                    ~{metrics.subscriptionLatency}ms
                  </span>
                )}
              </div>
            </div>

            {/* Cache Performance */}
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Cache Hit Rate:</span>
              <div className="flex items-center gap-2">
                <Badge 
                  variant="outline"
                  className={`text-${cacheStatus.color}-600 border-${cacheStatus.color}-300`}
                >
                  {metrics.cacheHitRate}%
                </Badge>
                <span className="text-xs text-gray-500">{cacheStatus.label}</span>
              </div>
            </div>

            {/* Memory Usage */}
            {metrics.memoryUsage > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Memory Usage:</span>
                <Badge variant="outline">
                  {metrics.memoryUsage}MB
                </Badge>
              </div>
            )}

            {/* Performance Trend */}
            {history.length > 1 && (
              <div className="pt-2 border-t">
                <div className="text-xs text-gray-500 mb-2">Trend (last 5 measurements):</div>
                <div className="flex gap-1">
                  {history.slice(-5).map((metric, index) => {
                    const status = getLoadTimeStatus(metric.pageLoadTime);
                    return (
                      <div
                        key={index}
                        className={`w-4 h-4 rounded-sm bg-${status.color}-200 border border-${status.color}-400`}
                        title={`${metric.pageLoadTime}ms at ${new Date(metric.timestamp).toLocaleTimeString()}`}
                      />
                    );
                  })}
                </div>
              </div>
            )}

            {/* Quick Actions */}
            <div className="pt-2 border-t flex gap-2">
              <Button
                onClick={collectMetrics}
                size="sm"
                variant="outline"
                className="text-xs flex-1"
              >
                🔄 Refresh
              </Button>
              <Button
                onClick={() => {
                  if (window.gc) {
                    window.gc();
                    setTimeout(collectMetrics, 100);
                  }
                }}
                size="sm"
                variant="outline"
                className="text-xs flex-1"
                disabled={!(window as any).gc}
              >
                🗑️ GC
              </Button>
            </div>

            {/* Optimization Status */}
            <div className="pt-2 border-t">
              <div className="text-xs text-gray-500 mb-1">Optimizations Active:</div>
              <div className="flex flex-wrap gap-1">
                <Badge variant="secondary" className="text-xs">
                  ⚡ Lazy Subscriptions
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  📦 Code Splitting
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  🎯 Redis Cache
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

// Hook to track performance metrics globally
export const usePerformanceTracking = () => {
  useEffect(() => {
    // Track subscription count
    let subscriptionCount = 0;
    const originalSubscribe = console.log;
    
    // Override console.log to track subscription messages
    console.log = (...args) => {
      const message = args.join(' ');
      if (message.includes('subscription active')) {
        subscriptionCount++;
        (window as any).realtimeSubscriptionCount = subscriptionCount;
      } else if (message.includes('subscription cleaned up')) {
        subscriptionCount = Math.max(0, subscriptionCount - 1);
        (window as any).realtimeSubscriptionCount = subscriptionCount;
      }
      originalSubscribe.apply(console, args);
    };

    return () => {
      console.log = originalSubscribe;
    };
  }, []);
};

export default PerformanceMonitor;

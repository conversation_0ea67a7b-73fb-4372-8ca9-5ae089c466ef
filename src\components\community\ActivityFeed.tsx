import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { BentoCard } from '@/components/design-system/BentoGrid';
import { EnhancedUnifiedBadge } from '@/components/design-system';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { useActivityFeed, type ActivityFeedItem } from '@/hooks/useActivityFeed';
import {
  Activity,
  Users,
  Heart,
  Eye,
  UserPlus,
  Calendar,
  Clock,
  Sparkles,
  TrendingUp
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface ActivityFeedProps {
  limit?: number;
  showHeader?: boolean;
  className?: string;
}

export const ActivityFeed: React.FC<ActivityFeedProps> = ({
  limit = 20,
  showHeader = true,
  className = ''
}) => {
  const { feedItems, isLoading, error } = useActivityFeed({
    limit,
    autoRefresh: true,
    refreshInterval: 30000
  });

  const getActivityIcon = (type: ActivityFeedItem['type']) => {
    switch (type) {
      case 'join':
        return <UserPlus className="w-4 h-4 text-festival-success" />;
      case 'favorite':
        return <Heart className="w-4 h-4 text-destructive" />;
      case 'view':
        return <Eye className="w-4 h-4 text-festival-accent" />;
      case 'new_activity':
        return <Sparkles className="w-4 h-4 text-primary" />;
      case 'new_user':
        return <Users className="w-4 h-4 text-festival-warning" />;
      default:
        return <Activity className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const getActivityMessage = (item: ActivityFeedItem) => {
    switch (item.type) {
      case 'join':
        return `${item.user_name} joined "${item.activity_title}"`;
      case 'favorite':
        return `${item.user_name} favorited "${item.activity_title}"`;
      case 'view':
        return `${item.user_name} viewed "${item.content_title}"`;
      case 'new_activity':
        return `New activity: "${item.activity_title}"`;
      case 'new_user':
        return `${item.user_name} joined the community`;
      default:
        return 'Community activity';
    }
  };

  const getBadgeVariant = (type: ActivityFeedItem['type']) => {
    switch (type) {
      case 'join':
        return 'success';
      case 'favorite':
        return 'destructive';
      case 'new_activity':
        return 'category';
      default:
        return 'secondary';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8 text-muted-foreground">
        <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {showHeader && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-primary" />
            <h3 className="text-lg font-semibold">Community Activity</h3>
          </div>
          <EnhancedUnifiedBadge variant="secondary" size="sm">
            Live
          </EnhancedUnifiedBadge>
        </div>
      )}

      <div className="space-y-3">
        <AnimatePresence>
          {feedItems.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <BentoCard
                title=""
                variant="glassmorphism"
                className="p-4 hover:bg-white/5 transition-colors cursor-pointer"
                onClick={() => {
                  if (item.activity_id) {
                    window.location.href = `/activities/${item.activity_id}`;
                  }
                }}
              >
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0">
                    {getActivityIcon(item.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-foreground">
                      {getActivityMessage(item)}
                    </p>
                    
                    <div className="flex items-center gap-2 mt-1">
                      <Clock className="w-3 h-3 text-muted-foreground" />
                      <span className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(item.timestamp), { addSuffix: true })}
                      </span>
                    </div>
                  </div>

                  <div className="flex-shrink-0">
                    <EnhancedUnifiedBadge 
                      variant={getBadgeVariant(item.type)} 
                      size="sm"
                    >
                      {item.type}
                    </EnhancedUnifiedBadge>
                  </div>
                </div>
              </BentoCard>
            </motion.div>
          ))}
        </AnimatePresence>

        {feedItems.length === 0 && (
          <div className="text-center p-8 text-muted-foreground">
            <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No recent activity</p>
            <p className="text-xs mt-1">Community activity will appear here</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ActivityFeed;

import React from 'react';
import { useAuth } from '../providers/ConsolidatedAuthProvider';
import PublicLanding from './PublicLanding';
import AuthenticatedHome from './AuthenticatedHome';
import UnifiedLoadingState from '../components/ui/UnifiedLoadingState';

/**
 * Smart Home Component
 * 
 * This component intelligently routes users to either the public marketing
 * landing page or the authenticated dashboard based on their authentication status.
 * 
 * - Unauthenticated users see the public marketing landing page
 * - Authenticated users see their personalized dashboard
 */
const SmartHome: React.FC = () => {
  const { user, loading } = useAuth();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <UnifiedLoadingState
        variant="fullscreen"
        message="Loading Festival Family..."
        subtitle="Preparing your festival experience"
        size="lg"
      />
    );
  }

  // Route to appropriate home page based on authentication status
  return user ? <AuthenticatedHome /> : <PublicLanding />;
};

export default SmartHome;

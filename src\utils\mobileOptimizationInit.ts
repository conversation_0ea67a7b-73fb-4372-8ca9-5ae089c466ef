/**
 * Mobile Optimization Initialization
 * 
 * Centralized initialization system for all mobile performance and accessibility optimizations.
 * This ensures consistent application of optimizations across the entire application.
 */

import { initializePerformanceOptimizations } from './performanceOptimization';
import { initializeAccessibility } from './accessibilityUtils';
import { initializeMobileUX } from './mobileUX';

// Global optimization state
interface OptimizationState {
  initialized: boolean;
  performanceOptimized: boolean;
  accessibilityEnabled: boolean;
  mobileUXEnabled: boolean;
  reducedMotion: boolean;
  touchDevice: boolean;
}

let optimizationState: OptimizationState = {
  initialized: false,
  performanceOptimized: false,
  accessibilityEnabled: false,
  mobileUXEnabled: false,
  reducedMotion: false,
  touchDevice: false
};

// Performance optimization initialization
const initializePerformance = (): Promise<void> => {
  return new Promise((resolve) => {
    try {
      initializePerformanceOptimizations();
      optimizationState.performanceOptimized = true;
      console.log('✅ Performance optimizations initialized');
      resolve();
    } catch (error) {
      console.warn('⚠️ Performance optimization failed:', error);
      resolve(); // Don't block initialization
    }
  });
};

// Accessibility initialization
const initializeAccessibilityFeatures = (): Promise<void> => {
  return new Promise((resolve) => {
    try {
      initializeAccessibility();
      
      // Check for reduced motion preference
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      optimizationState.reducedMotion = mediaQuery.matches;
      
      // Listen for changes
      mediaQuery.addEventListener('change', (e) => {
        optimizationState.reducedMotion = e.matches;
        document.documentElement.setAttribute('data-reduced-motion', e.matches.toString());
      });
      
      // Set initial state
      document.documentElement.setAttribute('data-reduced-motion', optimizationState.reducedMotion.toString());
      
      optimizationState.accessibilityEnabled = true;
      console.log('✅ Accessibility features initialized');
      resolve();
    } catch (error) {
      console.warn('⚠️ Accessibility initialization failed:', error);
      resolve(); // Don't block initialization
    }
  });
};

// Mobile UX initialization
const initializeMobileFeatures = (): Promise<void> => {
  return new Promise((resolve) => {
    try {
      initializeMobileUX();
      
      // Detect touch device
      optimizationState.touchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      document.documentElement.setAttribute('data-touch-device', optimizationState.touchDevice.toString());
      
      // Add mobile-specific CSS classes
      if (optimizationState.touchDevice) {
        document.body.classList.add('touch-device');
      }
      
      // Set up viewport optimization
      const viewport = document.querySelector('meta[name="viewport"]');
      if (!viewport) {
        const meta = document.createElement('meta');
        meta.name = 'viewport';
        meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes';
        document.head.appendChild(meta);
      }
      
      optimizationState.mobileUXEnabled = true;
      console.log('✅ Mobile UX features initialized');
      resolve();
    } catch (error) {
      console.warn('⚠️ Mobile UX initialization failed:', error);
      resolve(); // Don't block initialization
    }
  });
};

// Critical CSS injection for mobile-first loading
const injectCriticalCSS = (): void => {
  const criticalCSS = `
    /* Critical mobile-first styles */
    .sr-only {
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    }
    
    .touch-target {
      min-height: 44px;
      min-width: 44px;
    }
    
    .mobile-safe-area {
      padding-top: env(safe-area-inset-top);
      padding-bottom: env(safe-area-inset-bottom);
      padding-left: env(safe-area-inset-left);
      padding-right: env(safe-area-inset-right);
    }
    
    @media (prefers-reduced-motion: reduce) {
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }
    }
    
    /* Touch device optimizations */
    .touch-device button:hover {
      background-color: initial;
    }
    
    .touch-device *:focus {
      outline: 2px solid #3b82f6;
      outline-offset: 2px;
    }
  `;

  const style = document.createElement('style');
  style.textContent = criticalCSS;
  style.setAttribute('data-critical', 'true');
  document.head.insertBefore(style, document.head.firstChild);
};

// Resource preloading for mobile performance
const preloadCriticalResources = (): void => {
  const resources = [
    { href: '/fonts/inter-var.woff2', as: 'font', type: 'font/woff2' },
    { href: '/images/hero-mobile.webp', as: 'image' }
  ];

  resources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource.href;
    link.as = resource.as;
    if (resource.type) link.type = resource.type;
    link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
  });
};

// DNS prefetch for external resources
const setupDNSPrefetch = (): void => {
  const domains = [
    '//fonts.googleapis.com',
    '//fonts.gstatic.com'
  ];

  domains.forEach(domain => {
    const link = document.createElement('link');
    link.rel = 'dns-prefetch';
    link.href = domain;
    document.head.appendChild(link);
  });
};

// Main initialization function
export const initializeMobileOptimizations = async (): Promise<OptimizationState> => {
  if (optimizationState.initialized) {
    return optimizationState;
  }

  console.log('🚀 Initializing mobile optimizations...');

  // Inject critical CSS immediately
  injectCriticalCSS();
  
  // Setup resource preloading
  preloadCriticalResources();
  setupDNSPrefetch();

  // Initialize all optimization systems
  await Promise.all([
    initializePerformance(),
    initializeAccessibilityFeatures(),
    initializeMobileFeatures()
  ]);

  optimizationState.initialized = true;
  
  // Log final state
  console.log('🎉 Mobile optimizations complete:', {
    performance: optimizationState.performanceOptimized,
    accessibility: optimizationState.accessibilityEnabled,
    mobileUX: optimizationState.mobileUXEnabled,
    reducedMotion: optimizationState.reducedMotion,
    touchDevice: optimizationState.touchDevice
  });

  return optimizationState;
};

// Get current optimization state
export const getOptimizationState = (): OptimizationState => {
  return { ...optimizationState };
};

// Check if optimizations are ready
export const isOptimizationReady = (): boolean => {
  return optimizationState.initialized;
};

// Reinitialize optimizations (for development)
export const reinitializeOptimizations = async (): Promise<OptimizationState> => {
  optimizationState.initialized = false;
  return initializeMobileOptimizations();
};

// Performance monitoring
export const monitorOptimizationHealth = (): void => {
  if (process.env.NODE_ENV === 'development') {
    setInterval(() => {
      const state = getOptimizationState();
      if (!state.initialized) {
        console.warn('⚠️ Mobile optimizations not initialized');
      }
    }, 30000); // Check every 30 seconds
  }
};

// Export for use in app initialization
export default {
  initializeMobileOptimizations,
  getOptimizationState,
  isOptimizationReady,
  reinitializeOptimizations,
  monitorOptimizationHealth
};

/**
 * Comprehensive Error Detection and Documentation Suite
 * 
 * This test suite systematically identifies and documents issues:
 * - Broken forms and failed database operations
 * - Missing functionality and incomplete features
 * - Performance issues and slow loading times
 * - Evidence-based reporting with screenshots and logs
 */

import { test, expect } from '@playwright/test';

// Helper functions
async function takeEvidence(page, name, description) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `error-detection-${name}-${timestamp}.png`;
  await page.screenshot({ 
    path: `test-results/${filename}`,
    fullPage: true 
  });
  console.log(`📸 Error Detection Evidence: ${filename} - ${description}`);
  return filename;
}

async function measurePageLoadTime(page, url) {
  const startTime = Date.now();
  await page.goto(url);
  await page.waitForLoadState('networkidle');
  const endTime = Date.now();
  return endTime - startTime;
}

async function comprehensiveErrorCheck(page) {
  const errors = {
    console: [],
    page: [],
    network: [],
    ui: []
  };
  
  // Capture console errors
  page.on('console', msg => {
    if (msg.type() === 'error') {
      errors.console.push(msg.text());
    }
  });
  
  // Capture page errors
  page.on('pageerror', error => {
    errors.page.push(error.message);
  });
  
  // Capture network failures
  page.on('response', response => {
    if (response.status() >= 400) {
      errors.network.push(`${response.status()} ${response.url()}`);
    }
  });
  
  return errors;
}

async function checkForBrokenForms(page) {
  const brokenForms = [];
  
  // Find all forms on the page
  const forms = await page.locator('form').all();
  
  for (let i = 0; i < forms.length; i++) {
    const form = forms[i];
    
    try {
      // Check if form has required elements
      const hasInputs = await form.locator('input, textarea, select').count() > 0;
      const hasSubmitButton = await form.locator('button[type="submit"], input[type="submit"]').count() > 0;
      
      if (!hasInputs) {
        brokenForms.push(`Form ${i + 1}: No input fields found`);
      }
      
      if (!hasSubmitButton) {
        brokenForms.push(`Form ${i + 1}: No submit button found`);
      }
      
      // Check for validation attributes
      const requiredFields = await form.locator('input[required], textarea[required], select[required]').count();
      const hasValidation = requiredFields > 0;
      
      if (!hasValidation) {
        brokenForms.push(`Form ${i + 1}: No validation attributes found`);
      }
      
    } catch (error) {
      brokenForms.push(`Form ${i + 1}: Error analyzing form - ${error.message}`);
    }
  }
  
  return brokenForms;
}

async function checkForMissingFunctionality(page) {
  const missingFeatures = [];
  
  // Check for common missing elements
  const checks = [
    { selector: 'nav, [role="navigation"]', feature: 'Navigation' },
    { selector: 'main, [role="main"]', feature: 'Main content area' },
    { selector: 'footer, [role="contentinfo"]', feature: 'Footer' },
    { selector: 'button, [role="button"]', feature: 'Interactive buttons' },
    { selector: 'a[href]', feature: 'Working links' },
    { selector: 'img[src], img[alt]', feature: 'Proper images' }
  ];
  
  for (const check of checks) {
    const count = await page.locator(check.selector).count();
    if (count === 0) {
      missingFeatures.push(`Missing: ${check.feature}`);
    }
  }
  
  return missingFeatures;
}

test.describe('Phase 3: Error Detection and Documentation', () => {
  
  test('3.1 Systematic Issue Identification', async ({ page }) => {
    console.log('🧪 Performing systematic issue identification...');
    
    const allIssues = {
      brokenForms: [],
      missingFeatures: [],
      performanceIssues: [],
      errorsByPage: {}
    };
    
    // Test pages to analyze
    const testPages = [
      '/',
      '/auth',
      '/activities', 
      '/famhub',
      '/discover',
      '/profile',
      '/admin'
    ];
    
    for (const pagePath of testPages) {
      await test.step(`Analyze ${pagePath} for issues`, async () => {
        try {
          console.log(`🔍 Analyzing page: ${pagePath}`);
          
          // Set up error monitoring
          const errors = await comprehensiveErrorCheck(page);
          
          // Measure page load time
          const loadTime = await measurePageLoadTime(page, pagePath);
          
          // Check for broken forms
          const brokenForms = await checkForBrokenForms(page);
          
          // Check for missing functionality
          const missingFeatures = await checkForMissingFunctionality(page);
          
          // Take evidence screenshot
          await takeEvidence(page, `page-analysis-${pagePath.replace('/', 'home')}`, `Analysis of ${pagePath}`);
          
          // Wait a bit to capture any delayed errors
          await page.waitForTimeout(3000);
          
          // Compile results for this page
          const pageIssues = {
            path: pagePath,
            loadTime,
            brokenForms,
            missingFeatures,
            errors: {
              console: [...errors.console],
              page: [...errors.page],
              network: [...errors.network]
            }
          };
          
          allIssues.errorsByPage[pagePath] = pageIssues;
          allIssues.brokenForms.push(...brokenForms.map(f => `${pagePath}: ${f}`));
          allIssues.missingFeatures.push(...missingFeatures.map(f => `${pagePath}: ${f}`));
          
          if (loadTime > 5000) {
            allIssues.performanceIssues.push(`${pagePath}: Slow load time (${loadTime}ms)`);
          }
          
          console.log(`📊 ${pagePath} Analysis:`);
          console.log(`  Load time: ${loadTime}ms`);
          console.log(`  Broken forms: ${brokenForms.length}`);
          console.log(`  Missing features: ${missingFeatures.length}`);
          console.log(`  Console errors: ${errors.console.length}`);
          console.log(`  Page errors: ${errors.page.length}`);
          console.log(`  Network errors: ${errors.network.length}`);
          
        } catch (error) {
          console.log(`❌ Failed to analyze ${pagePath}: ${error.message}`);
          allIssues.errorsByPage[pagePath] = {
            path: pagePath,
            error: error.message,
            analysisFailure: true
          };
        }
      });
    }
    
    // Generate comprehensive report
    await test.step('Generate comprehensive issue report', async () => {
      console.log('\n🔍 COMPREHENSIVE ISSUE IDENTIFICATION REPORT');
      console.log('=' .repeat(60));
      
      // Performance Issues
      console.log('\n⏱️ PERFORMANCE ISSUES:');
      if (allIssues.performanceIssues.length > 0) {
        allIssues.performanceIssues.forEach(issue => console.log(`  ❌ ${issue}`));
      } else {
        console.log('  ✅ No performance issues detected');
      }
      
      // Broken Forms
      console.log('\n📝 BROKEN FORMS:');
      if (allIssues.brokenForms.length > 0) {
        allIssues.brokenForms.forEach(issue => console.log(`  ❌ ${issue}`));
      } else {
        console.log('  ✅ No broken forms detected');
      }
      
      // Missing Features
      console.log('\n🔧 MISSING FEATURES:');
      if (allIssues.missingFeatures.length > 0) {
        allIssues.missingFeatures.forEach(issue => console.log(`  ❌ ${issue}`));
      } else {
        console.log('  ✅ No missing features detected');
      }
      
      // Page-by-Page Error Summary
      console.log('\n📄 PAGE-BY-PAGE ERROR SUMMARY:');
      Object.values(allIssues.errorsByPage).forEach(pageData => {
        if (pageData.analysisFailure) {
          console.log(`  ❌ ${pageData.path}: Analysis failed - ${pageData.error}`);
        } else {
          const totalErrors = (pageData.errors?.console?.length || 0) + 
                            (pageData.errors?.page?.length || 0) + 
                            (pageData.errors?.network?.length || 0);
          
          if (totalErrors > 0) {
            console.log(`  ⚠️ ${pageData.path}: ${totalErrors} errors detected`);
            if (pageData.errors?.console?.length > 0) {
              console.log(`    Console errors: ${pageData.errors.console.length}`);
            }
            if (pageData.errors?.page?.length > 0) {
              console.log(`    Page errors: ${pageData.errors.page.length}`);
            }
            if (pageData.errors?.network?.length > 0) {
              console.log(`    Network errors: ${pageData.errors.network.length}`);
            }
          } else {
            console.log(`  ✅ ${pageData.path}: No errors detected`);
          }
        }
      });
      
      // Overall Health Score
      const totalPages = Object.keys(allIssues.errorsByPage).length;
      const pagesWithErrors = Object.values(allIssues.errorsByPage).filter(page => 
        !page.analysisFailure && 
        ((page.errors?.console?.length || 0) + (page.errors?.page?.length || 0) + (page.errors?.network?.length || 0)) > 0
      ).length;
      
      const healthScore = Math.round(((totalPages - pagesWithErrors) / totalPages) * 100);
      
      console.log('\n📊 OVERALL APPLICATION HEALTH:');
      console.log(`  Health Score: ${healthScore}%`);
      console.log(`  Pages analyzed: ${totalPages}`);
      console.log(`  Pages with errors: ${pagesWithErrors}`);
      console.log(`  Pages without errors: ${totalPages - pagesWithErrors}`);
      
      if (healthScore >= 80) {
        console.log('  🎉 Application is in good health!');
      } else if (healthScore >= 60) {
        console.log('  ⚠️ Application has some issues that should be addressed');
      } else {
        console.log('  🚨 Application has significant issues that need immediate attention');
      }
    });
    
    // Save detailed report to file
    await test.step('Save detailed error report', async () => {
      const reportData = {
        timestamp: new Date().toISOString(),
        summary: {
          totalPages: Object.keys(allIssues.errorsByPage).length,
          performanceIssues: allIssues.performanceIssues.length,
          brokenForms: allIssues.brokenForms.length,
          missingFeatures: allIssues.missingFeatures.length
        },
        detailedResults: allIssues
      };
      
      // Write to console as JSON for easy parsing
      console.log('\n📋 DETAILED REPORT (JSON):');
      console.log(JSON.stringify(reportData, null, 2));
    });
    
    // Basic health check assertion
    const totalIssues = allIssues.performanceIssues.length + 
                       allIssues.brokenForms.length + 
                       allIssues.missingFeatures.length;
    
    // Allow some issues but not too many
    expect(totalIssues).toBeLessThan(20);
  });
});

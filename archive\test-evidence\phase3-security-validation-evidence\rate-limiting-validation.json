{"attempts": [{"attempt": 1, "error": "page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "timestamp": "2025-06-04T09:52:19.403Z"}, {"attempt": 2, "error": "page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "timestamp": "2025-06-04T09:52:29.407Z"}, {"attempt": 3, "error": "page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n", "timestamp": "2025-06-04T09:52:39.421Z"}], "rateLimitDetected": false, "averageResponseTime": 0, "rateLimitThreshold": null, "timestamp": "2025-06-04T09:52:09.387Z"}
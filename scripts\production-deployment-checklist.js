/**
 * Festival Family Production Deployment Checklist
 * 
 * Comprehensive checklist and validation script for production deployment
 * ensuring all standardization and performance optimizations are ready.
 * 
 * @module ProductionDeploymentChecklist
 * @version 1.0.0
 */

import fs from 'fs/promises';
import path from 'path';
import { spawn } from 'child_process';

class ProductionDeploymentChecker {
  constructor() {
    this.results = {
      passed: [],
      failed: [],
      warnings: [],
      summary: {}
    };
  }

  /**
   * Run a command and return the result
   */
  async runCommand(command, args = []) {
    return new Promise((resolve, reject) => {
      const process = spawn(command, args, { stdio: 'pipe' });
      let stdout = '';
      let stderr = '';

      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      process.on('close', (code) => {
        resolve({ code, stdout, stderr });
      });

      process.on('error', reject);
    });
  }

  /**
   * Check if file exists
   */
  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check TypeScript compilation
   */
  async checkTypeScript() {
    console.log('🔍 Checking TypeScript compilation...');
    
    try {
      const result = await this.runCommand('npx', ['tsc', '--noEmit']);
      
      if (result.code === 0) {
        this.results.passed.push('✅ TypeScript compilation successful');
        return true;
      } else {
        this.results.failed.push(`❌ TypeScript errors found:\n${result.stderr}`);
        return false;
      }
    } catch (error) {
      this.results.failed.push(`❌ TypeScript check failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Check production build
   */
  async checkProductionBuild() {
    console.log('🔍 Checking production build...');
    
    try {
      const result = await this.runCommand('npm', ['run', 'build']);
      
      if (result.code === 0) {
        this.results.passed.push('✅ Production build successful');
        
        // Check if dist directory exists and has files
        const distExists = await this.fileExists('dist');
        if (distExists) {
          const distFiles = await fs.readdir('dist');
          if (distFiles.length > 0) {
            this.results.passed.push(`✅ Build artifacts created (${distFiles.length} files)`);
            return true;
          }
        }
        
        this.results.failed.push('❌ Build completed but no artifacts found');
        return false;
      } else {
        this.results.failed.push(`❌ Production build failed:\n${result.stderr}`);
        return false;
      }
    } catch (error) {
      this.results.failed.push(`❌ Build check failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Check environment configuration
   */
  async checkEnvironmentConfig() {
    console.log('🔍 Checking environment configuration...');
    
    const requiredEnvVars = [
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_ANON_KEY'
    ];
    
    let allPresent = true;
    
    for (const envVar of requiredEnvVars) {
      if (process.env[envVar]) {
        this.results.passed.push(`✅ ${envVar} configured`);
      } else {
        this.results.failed.push(`❌ Missing environment variable: ${envVar}`);
        allPresent = false;
      }
    }
    
    // Check for .env file
    const envExists = await this.fileExists('.env');
    if (envExists) {
      this.results.passed.push('✅ .env file present');
    } else {
      this.results.warnings.push('⚠️ No .env file found (may be using system env vars)');
    }
    
    return allPresent;
  }

  /**
   * Check standardization compliance
   */
  async checkStandardizationCompliance() {
    console.log('🔍 Checking standardization compliance...');
    
    const checks = [
      {
        name: 'UnifiedInteractionButton usage',
        path: 'src/components/design-system/UnifiedInteractionButton.tsx',
        required: true
      },
      {
        name: 'EnhancedUnifiedBadge usage',
        path: 'src/components/design-system/EnhancedUnifiedBadge.tsx',
        required: true
      },
      {
        name: 'BentoCard components',
        path: 'src/components/design-system/BentoCard.tsx',
        required: true
      },
      {
        name: 'Enhanced color mapping service',
        path: 'src/lib/services/enhancedColorMappingService.ts',
        required: true
      },
      {
        name: 'Lazy realtime hooks',
        path: 'src/hooks/realtime/useLazyRealtime.ts',
        required: true
      },
      {
        name: 'Performance monitoring',
        path: 'src/components/performance/PerformanceMonitor.tsx',
        required: true
      }
    ];
    
    let allPresent = true;
    
    for (const check of checks) {
      const exists = await this.fileExists(check.path);
      if (exists) {
        this.results.passed.push(`✅ ${check.name} implemented`);
      } else if (check.required) {
        this.results.failed.push(`❌ Missing required component: ${check.name}`);
        allPresent = false;
      } else {
        this.results.warnings.push(`⚠️ Optional component missing: ${check.name}`);
      }
    }
    
    return allPresent;
  }

  /**
   * Check performance optimizations
   */
  async checkPerformanceOptimizations() {
    console.log('🔍 Checking performance optimizations...');
    
    // Check Vite config for optimizations
    const viteConfigExists = await this.fileExists('vite.config.ts');
    if (viteConfigExists) {
      const viteConfig = await fs.readFile('vite.config.ts', 'utf-8');
      
      if (viteConfig.includes('manualChunks')) {
        this.results.passed.push('✅ Code splitting configured');
      } else {
        this.results.warnings.push('⚠️ Manual chunks not configured');
      }
      
      if (viteConfig.includes('compression')) {
        this.results.passed.push('✅ Compression enabled');
      } else {
        this.results.warnings.push('⚠️ Compression not enabled');
      }
    } else {
      this.results.failed.push('❌ Vite config not found');
    }
    
    // Check for lazy loading implementation
    const mainTsxExists = await this.fileExists('src/main.tsx');
    if (mainTsxExists) {
      const mainTsx = await fs.readFile('src/main.tsx', 'utf-8');
      
      if (mainTsx.includes('React.lazy') || mainTsx.includes('lazy(')) {
        this.results.passed.push('✅ React.lazy() implemented');
      } else {
        this.results.warnings.push('⚠️ React.lazy() not found in main.tsx');
      }
    }
    
    return true;
  }

  /**
   * Check security configuration
   */
  async checkSecurityConfig() {
    console.log('🔍 Checking security configuration...');
    
    // Check for security headers in Vite config
    const viteConfigExists = await this.fileExists('vite.config.ts');
    if (viteConfigExists) {
      const viteConfig = await fs.readFile('vite.config.ts', 'utf-8');
      
      if (viteConfig.includes('Strict-Transport-Security')) {
        this.results.passed.push('✅ HSTS headers configured');
      } else {
        this.results.warnings.push('⚠️ HSTS headers not configured');
      }
      
      if (viteConfig.includes('X-Content-Type-Options')) {
        this.results.passed.push('✅ Content type protection enabled');
      } else {
        this.results.warnings.push('⚠️ Content type protection not configured');
      }
    }
    
    return true;
  }

  /**
   * Check documentation
   */
  async checkDocumentation() {
    console.log('🔍 Checking documentation...');
    
    const docs = [
      { path: 'README.md', name: 'Main README' },
      { path: 'docs/ARCHITECTURE.md', name: 'Architecture documentation' },
      { path: 'docs/STYLING_GUIDELINES.md', name: 'Styling guidelines' }
    ];
    
    for (const doc of docs) {
      const exists = await this.fileExists(doc.path);
      if (exists) {
        this.results.passed.push(`✅ ${doc.name} present`);
      } else {
        this.results.warnings.push(`⚠️ ${doc.name} missing`);
      }
    }
    
    return true;
  }

  /**
   * Generate deployment summary
   */
  generateSummary() {
    const totalChecks = this.results.passed.length + this.results.failed.length + this.results.warnings.length;
    const passRate = Math.round((this.results.passed.length / totalChecks) * 100);
    
    this.results.summary = {
      totalChecks,
      passed: this.results.passed.length,
      failed: this.results.failed.length,
      warnings: this.results.warnings.length,
      passRate,
      readyForProduction: this.results.failed.length === 0,
      recommendedActions: this.results.failed.length > 0 ? 'Fix all failed checks before deployment' : 
                         this.results.warnings.length > 0 ? 'Consider addressing warnings for optimal deployment' :
                         'Ready for production deployment'
    };
  }

  /**
   * Run all checks
   */
  async runAllChecks() {
    console.log('🚀 Starting Festival Family Production Deployment Checklist...\n');
    
    try {
      await this.checkEnvironmentConfig();
      console.log('');
      
      await this.checkTypeScript();
      console.log('');
      
      await this.checkStandardizationCompliance();
      console.log('');
      
      await this.checkPerformanceOptimizations();
      console.log('');
      
      await this.checkSecurityConfig();
      console.log('');
      
      await this.checkDocumentation();
      console.log('');
      
      await this.checkProductionBuild();
      console.log('');
      
      this.generateSummary();
      this.displayResults();
      
      return this.results.summary.readyForProduction;
      
    } catch (error) {
      console.error('❌ Deployment check failed:', error);
      return false;
    }
  }

  /**
   * Display results
   */
  displayResults() {
    console.log('📊 PRODUCTION DEPLOYMENT CHECKLIST RESULTS');
    console.log('===========================================\n');
    
    if (this.results.passed.length > 0) {
      console.log('✅ PASSED CHECKS:');
      this.results.passed.forEach(item => console.log(`   ${item}`));
      console.log('');
    }
    
    if (this.results.warnings.length > 0) {
      console.log('⚠️  WARNINGS:');
      this.results.warnings.forEach(item => console.log(`   ${item}`));
      console.log('');
    }
    
    if (this.results.failed.length > 0) {
      console.log('❌ FAILED CHECKS:');
      this.results.failed.forEach(item => console.log(`   ${item}`));
      console.log('');
    }
    
    console.log('📈 SUMMARY:');
    console.log(`   Total Checks: ${this.results.summary.totalChecks}`);
    console.log(`   Passed: ${this.results.summary.passed}`);
    console.log(`   Warnings: ${this.results.summary.warnings}`);
    console.log(`   Failed: ${this.results.summary.failed}`);
    console.log(`   Pass Rate: ${this.results.summary.passRate}%`);
    console.log('');
    
    console.log('🎯 DEPLOYMENT STATUS:');
    if (this.results.summary.readyForProduction) {
      console.log('   ✅ READY FOR PRODUCTION DEPLOYMENT');
    } else {
      console.log('   ❌ NOT READY - Fix failed checks first');
    }
    
    console.log(`   Recommendation: ${this.results.summary.recommendedActions}`);
  }
}

// Run checklist if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const checker = new ProductionDeploymentChecker();
  checker.runAllChecks().then(ready => {
    process.exit(ready ? 0 : 1);
  }).catch(error => {
    console.error('Deployment check failed:', error);
    process.exit(1);
  });
}

export default ProductionDeploymentChecker;

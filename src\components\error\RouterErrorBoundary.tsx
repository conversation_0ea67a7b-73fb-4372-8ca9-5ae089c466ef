/**
 * Router Error Boundary
 *
 * Specialized error boundary for handling routing and dynamic import errors.
 * Provides specific fallbacks for different types of routing failures.
 */

import React, { useState } from 'react';
import { ErrorBoundary, FallbackProps } from 'react-error-boundary';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw, Home, ArrowLeft } from 'lucide-react';
import { BentoCard } from '@/components/design-system/BentoGrid';

interface RouterErrorBoundaryProps {
  children: React.ReactNode;
}

// Specialized fallback for routing errors
const RouterErrorFallback = ({ error, resetErrorBoundary }: FallbackProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isRetrying, setIsRetrying] = useState(false);

  // Detect different types of routing errors
  const isDynamicImportError = error.message.includes('Failed to fetch') || 
                               error.message.includes('Loading chunk') ||
                               error.message.includes('dynamic import') ||
                               error.message.includes('ChunkLoadError');

  const isNetworkError = error.message.includes('NetworkError') ||
                         error.message.includes('fetch');

  const isNotFoundError = error.message.includes('404') ||
                          error.message.includes('Not Found');

  // Handle retry with appropriate strategy
  const handleRetry = async () => {
    setIsRetrying(true);
    
    try {
      if (isDynamicImportError) {
        // For dynamic import errors, reload the page to clear module cache
        window.location.reload();
        return;
      }
      
      if (isNetworkError) {
        // For network errors, wait a moment then retry
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
      resetErrorBoundary();
    } catch (retryError) {
      console.error('Retry failed:', retryError);
      toast.error('Retry failed. Please try again.');
    } finally {
      setIsRetrying(false);
    }
  };

  // Navigate to safe routes
  const handleGoHome = () => {
    navigate('/', { replace: true });
    resetErrorBoundary();
  };

  const handleGoBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/', { replace: true });
    }
    resetErrorBoundary();
  };

  // Get appropriate error message and actions
  const getErrorInfo = () => {
    if (isDynamicImportError) {
      return {
        title: "Page Loading Error",
        message: "There was a problem loading this page. This might be due to a network issue or cached files.",
        suggestion: "Try reloading the page or check your internet connection.",
        primaryAction: "Reload Page",
        showBackButton: true
      };
    }
    
    if (isNetworkError) {
      return {
        title: "Network Error",
        message: "Unable to connect to the server. Please check your internet connection.",
        suggestion: "Make sure you're connected to the internet and try again.",
        primaryAction: "Retry",
        showBackButton: true
      };
    }
    
    if (isNotFoundError) {
      return {
        title: "Page Not Found",
        message: "The page you're looking for doesn't exist or has been moved.",
        suggestion: "Check the URL or navigate to a different page.",
        primaryAction: "Go Home",
        showBackButton: true
      };
    }
    
    return {
      title: "Navigation Error",
      message: "Something went wrong while navigating to this page.",
      suggestion: "Try going back or returning to the home page.",
      primaryAction: "Try Again",
      showBackButton: true
    };
  };

  const errorInfo = getErrorInfo();

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-lg">
        <BentoCard
          title=""
          variant="glassmorphism"
          className="p-8 text-center"
        >
          <div className="flex items-center justify-center mb-6">
            <AlertCircle className="h-16 w-16 text-destructive" />
          </div>

          <h1 className="text-2xl font-bold mb-4">{errorInfo.title}</h1>
          
          <p className="text-muted-foreground mb-4">
            {errorInfo.message}
          </p>
          
          <p className="text-sm text-muted-foreground mb-6">
            {errorInfo.suggestion}
          </p>

          {/* Error details for debugging */}
          <div className="bg-destructive/10 rounded-md p-4 mb-6 text-left">
            <p className="text-sm font-medium text-destructive mb-2">
              Technical Details:
            </p>
            <p className="text-xs text-muted-foreground font-mono">
              {error.message}
            </p>
            <p className="text-xs text-muted-foreground mt-2">
              Route: {location.pathname}
            </p>
          </div>

          {/* Action buttons */}
          <div className="flex flex-col space-y-3">
            <Button
              onClick={handleRetry}
              disabled={isRetrying}
              className="w-full"
              size="lg"
            >
              {isRetrying ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  {isDynamicImportError ? "Reloading..." : "Retrying..."}
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  {errorInfo.primaryAction}
                </>
              )}
            </Button>

            <div className="flex space-x-3">
              {errorInfo.showBackButton && (
                <Button
                  onClick={handleGoBack}
                  variant="outline"
                  className="flex-1"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Go Back
                </Button>
              )}
              
              <Button
                onClick={handleGoHome}
                variant="outline"
                className="flex-1"
              >
                <Home className="mr-2 h-4 w-4" />
                Home
              </Button>
            </div>
          </div>
        </BentoCard>
      </div>
    </div>
  );
};

// Error logger for routing errors
const logRoutingError = (error: Error, info: { componentStack?: string | null }) => {
  console.error('🚨 Router Error:', {
    message: error.message,
    stack: error.stack,
    componentStack: info.componentStack,
    route: window.location.pathname,
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString()
  });

  // Show appropriate toast based on error type
  if (error.message.includes('Failed to fetch') || error.message.includes('Loading chunk')) {
    toast.error('Page loading failed. Trying to reload...', {
      duration: 4000,
      position: 'top-center'
    });
  } else {
    toast.error('Navigation error occurred', {
      duration: 3000
    });
  }
};

// Main router error boundary component
export const RouterErrorBoundary: React.FC<RouterErrorBoundaryProps> = ({ children }) => {
  return (
    <ErrorBoundary
      FallbackComponent={RouterErrorFallback}
      onError={logRoutingError}
      onReset={() => {
        // Clear any cached modules on reset
        if ('serviceWorker' in navigator) {
          navigator.serviceWorker.getRegistrations().then(registrations => {
            registrations.forEach(registration => {
              registration.update();
            });
          });
        }
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

export default RouterErrorBoundary;

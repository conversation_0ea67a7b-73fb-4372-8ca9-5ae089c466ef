/**
 * Diagnostic Test
 * 
 * Simple test to diagnose what's happening with the application
 */

import { test, expect } from '@playwright/test';

test('Diagnostic: Check if app is running', async ({ page }) => {
  console.log('🔍 Diagnostic: Checking if app is accessible...');
  
  try {
    // Try to navigate to the home page
    console.log('Navigating to home page...');
    await page.goto('/', { timeout: 30000 });
    
    console.log('Waiting for page to load...');
    await page.waitForLoadState('networkidle', { timeout: 30000 });
    
    console.log('Taking screenshot...');
    await page.screenshot({ path: 'test-results/diagnostic-home.png', fullPage: true });
    
    console.log('Checking page title...');
    const title = await page.title();
    console.log(`Page title: ${title}`);
    
    console.log('Checking page URL...');
    const url = page.url();
    console.log(`Page URL: ${url}`);
    
    console.log('Checking for content...');
    const hasContent = await page.locator('body').count() > 0;
    console.log(`Has content: ${hasContent}`);
    
    console.log('✅ Diagnostic complete - app is accessible');
    
  } catch (error) {
    console.log(`❌ Diagnostic failed: ${error.message}`);
    throw error;
  }
});

test('Diagnostic: Check auth page', async ({ page }) => {
  console.log('🔍 Diagnostic: Checking auth page...');
  
  try {
    await page.goto('/auth', { timeout: 30000 });
    await page.waitForLoadState('networkidle', { timeout: 30000 });
    
    await page.screenshot({ path: 'test-results/diagnostic-auth.png', fullPage: true });
    
    const hasEmailField = await page.locator('input[type="email"]').count() > 0;
    const hasPasswordField = await page.locator('input[type="password"]').count() > 0;
    
    console.log(`Has email field: ${hasEmailField}`);
    console.log(`Has password field: ${hasPasswordField}`);
    
    console.log('✅ Auth page diagnostic complete');
    
  } catch (error) {
    console.log(`❌ Auth page diagnostic failed: ${error.message}`);
    throw error;
  }
});

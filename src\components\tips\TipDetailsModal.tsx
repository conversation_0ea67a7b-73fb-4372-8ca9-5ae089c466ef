import React, { useState, useEffect } from 'react';
import { Lightbulb, Tag, Clock, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ResponsiveModal } from '@/components/design-system/ResponsiveModal';
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';
import { UnifiedInteractionButton } from '@/components/design-system';

interface Tip {
  id: string;
  title: string;
  content: string;
  description?: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  created_at: string;
}

interface TipDetailsModalProps {
  tip: Tip | null;
  isOpen: boolean;
  onClose: () => void;
}

export const TipDetailsModal: React.FC<TipDetailsModalProps> = ({
  tip,
  isOpen,
  onClose,
}) => {
  // Modern color system using enhancedColorMappingService
  const [colorTheme, setColorTheme] = useState<{
    className: string;
    style: React.CSSProperties;
  }>({ className: '', style: {} });
  const [isLoadingTheme, setIsLoadingTheme] = useState(false);

  // Load color theme using enhancedColorMappingService
  useEffect(() => {
    const loadColorTheme = async () => {
      if (!tip || !isOpen) return;

      setIsLoadingTheme(true);
      try {
        const theme = await enhancedColorMappingService.getEnhancedColorTheme(
          'tip',
          tip.category ?? 'GENERAL',
          0 // Use first fallback theme
        );
        setColorTheme(theme);
      } catch (error) {
        console.warn('Failed to load tip color theme:', error);
        // Fallback theme using design tokens
        setColorTheme({
          className: 'bg-gradient-to-br from-blue-50 to-teal-50',
          style: {
            background: 'var(--festival-gradient-primary)',
            borderColor: 'var(--festival-border)'
          }
        });
      } finally {
        setIsLoadingTheme(false);
      }
    };

    loadColorTheme();
  }, [tip, isOpen]);

  if (!tip) return null;

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'SURVIVAL': return 'Festival Survival';
      case 'SOCIAL': return 'Social & Networking';
      case 'BUDGET': return 'Budget & Money';
      case 'COMFORT': return 'Comfort & Gear';
      case 'EXPERIENCE': return 'Experience & Fun';
      case 'SAFETY': return 'Safety & Health';
      default: return category;
    }
  };

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={onClose}
      size="lg"
      showCloseButton={false}
    >
      <div className="flex flex-col h-full">
        {/* Standardized Header following UnifiedModal.tsx patterns */}
        <div
          className="p-4 sm:p-6 pb-2"
          style={{
            background: !isLoadingTheme ? colorTheme.style.background : 'var(--festival-bg-card)',
            borderBottom: '1px solid var(--festival-border)'
          }}
        >
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div
                className="w-10 h-10 rounded-lg flex items-center justify-center"
                style={{
                  background: !isLoadingTheme && colorTheme.style.background
                    ? colorTheme.style.background
                    : 'var(--festival-gradient-primary)',
                  color: 'var(--festival-text-on-primary)'
                }}
              >
                <Lightbulb className="w-5 h-5" />
              </div>
              <div>
                <h2
                  className="text-lg sm:text-xl font-bold pr-8"
                  style={{ color: 'var(--festival-text-auto)' }}
                >
                  {tip.title}
                </h2>
                {tip.category && (
                  <Badge variant="secondary" className="mt-2 text-xs">
                    {getCategoryLabel(tip.category)}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Featured Badge following UnifiedModal.tsx pattern */}
          {tip.is_featured && (
            <Badge
              className="w-fit mt-2 text-xs"
              style={{
                backgroundColor: 'var(--festival-warning-bg)',
                color: 'var(--festival-warning)',
                border: '1px solid var(--festival-warning)'
              }}
            >
              Featured Tip
            </Badge>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          {tip.description && (
            <div className="bg-muted/20 rounded-lg p-4">
              <p className="text-muted-foreground text-sm">{tip.description}</p>
            </div>
          )}

          <div className="prose prose-foreground max-w-none">
            <p className="text-foreground leading-relaxed">{tip.content}</p>
          </div>

          {/* Tags */}
          {tip.tags && tip.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {tip.tags.map((tag, index) => (
                <Badge key={index} variant="outline">
                  <Tag className="w-3 h-3 mr-1" />
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          {/* Stats */}
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            {tip.view_count !== undefined && (
              <span className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {tip.view_count} views
              </span>
            )}
            {tip.helpful_count !== undefined && (
              <span className="flex items-center gap-1">
                <Heart className="w-4 h-4" />
                {tip.helpful_count} helpful
              </span>
            )}
            <span>
              {new Date(tip.created_at).toLocaleDateString()}
            </span>
          </div>
        </div>

        {/* Standardized Footer following UnifiedModal.tsx patterns */}
        <div
          className="flex flex-col sm:flex-row gap-2 sm:gap-3 p-4 sm:p-6 pt-2"
          style={{
            borderTop: '1px solid var(--festival-border)',
            backgroundColor: 'var(--festival-bg-muted)'
          }}
        >
          {/* Action buttons with mobile-first responsive design */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
            <UnifiedInteractionButton
              type="helpful"
              itemId={tip.id}
              itemType="tip"
              variant="outline"
              className="w-full sm:w-auto min-w-[120px]"
              size="default"
            />
            <UnifiedInteractionButton
              type="share"
              itemId={tip.id}
              itemType="tip"
              variant="outline"
              className="w-full sm:w-auto min-w-[120px]"
              size="default"
            />
            <Button
              variant="outline"
              onClick={onClose}
              className="w-full sm:w-auto min-w-[120px]"
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </ResponsiveModal>
  );
};

export default TipDetailsModal;

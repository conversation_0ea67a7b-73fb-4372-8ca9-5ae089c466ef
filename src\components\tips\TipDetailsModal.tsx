import React, { useState, useEffect } from 'react';
import { Lightbulb, Tag, Clock, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ResponsiveModal } from '@/components/design-system/ResponsiveModal';
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';
import { UnifiedInteractionButton } from '@/components/design-system';

interface Tip {
  id: string;
  title: string;
  content: string;
  description?: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  created_at: string;
}

interface TipDetailsModalProps {
  tip: Tip | null;
  isOpen: boolean;
  onClose: () => void;
}

export const TipDetailsModal: React.FC<TipDetailsModalProps> = ({
  tip,
  isOpen,
  onClose,
}) => {
  // Modern color system using enhancedColorMappingService
  const [colorTheme, setColorTheme] = useState<{
    className: string;
    style: React.CSSProperties;
  }>({ className: '', style: {} });
  const [isLoadingTheme, setIsLoadingTheme] = useState(false);

  // Load color theme using enhancedColorMappingService
  useEffect(() => {
    const loadColorTheme = async () => {
      if (!tip || !isOpen) return;

      setIsLoadingTheme(true);
      try {
        const theme = await enhancedColorMappingService.getEnhancedColorTheme(
          'tip',
          tip.category ?? 'GENERAL',
          0 // Use first fallback theme
        );
        setColorTheme(theme);
      } catch (error) {
        console.warn('Failed to load tip color theme:', error);
        // Fallback theme using design tokens
        setColorTheme({
          className: 'bg-gradient-to-br from-blue-50 to-teal-50',
          style: {
            background: 'var(--festival-gradient-primary)',
            borderColor: 'var(--festival-border)'
          }
        });
      } finally {
        setIsLoadingTheme(false);
      }
    };

    loadColorTheme();
  }, [tip, isOpen]);

  if (!tip) return null;

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'SURVIVAL': return 'Festival Survival';
      case 'SOCIAL': return 'Social & Networking';
      case 'BUDGET': return 'Budget & Money';
      case 'COMFORT': return 'Comfort & Gear';
      case 'EXPERIENCE': return 'Experience & Fun';
      case 'SAFETY': return 'Safety & Health';
      default: return category;
    }
  };

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={onClose}
      size="lg"
      showCloseButton={false}
    >
      <div className="flex flex-col h-full">
        {/* Enhanced Header with Advanced Design Token Integration */}
        <div
          className="p-4 sm:p-6 pb-2 festival-component"
          style={{
            background: !isLoadingTheme ? colorTheme.style.background : 'var(--light-effect-primary)',
            borderBottom: '1px solid var(--festival-border)',
            transition: 'var(--festival-transition)'
          }}
        >
          {/* Loading State Indicator */}
          {isLoadingTheme && (
            <div
              className="absolute top-2 right-2 w-4 h-4 rounded-full animate-pulse"
              style={{
                background: 'var(--festival-accent)',
                opacity: 0.6
              }}
            />
          )}

          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div
                className="w-10 h-10 flex items-center justify-center layered-effect"
                style={{
                  background: !isLoadingTheme && colorTheme.style.background
                    ? colorTheme.style.background
                    : 'var(--light-effect-primary)',
                  color: 'var(--festival-text-on-primary)',
                  borderRadius: 'var(--festival-radius)',
                  boxShadow: 'var(--layer-shadow-sm)'
                }}
              >
                <Lightbulb className="w-5 h-5" />
              </div>
              <div>
                <h2
                  className="text-lg sm:text-xl font-bold pr-8 festival-text-auto"
                  style={{
                    color: 'var(--festival-text-auto)',
                    transition: 'var(--festival-transition)'
                  }}
                >
                  {tip.title}
                </h2>
                {tip.category && (
                  <Badge
                    variant="secondary"
                    className="mt-2 text-xs festival-component"
                    style={{
                      backgroundColor: 'var(--festival-bg-muted)',
                      color: 'var(--festival-text-muted)',
                      border: '1px solid var(--festival-border)',
                      transition: 'var(--festival-transition)'
                    }}
                  >
                    {getCategoryLabel(tip.category)}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Enhanced Featured Badge with Advanced Design Tokens */}
          {tip.is_featured && (
            <Badge
              className="w-fit mt-2 text-xs festival-component hover-glow"
              style={{
                backgroundColor: 'var(--festival-warning-bg)',
                color: 'var(--festival-warning-text)',
                border: '1px solid var(--festival-warning)',
                boxShadow: 'var(--layer-shadow-sm)',
                transition: 'var(--festival-transition)'
              }}
            >
              ⭐ Featured Tip
            </Badge>
          )}
        </div>

        {/* Enhanced Content with Advanced Design Token Integration */}
        <div
          className="flex-1 overflow-y-auto p-6 space-y-4"
          style={{
            maxHeight: 'calc(100vh - 16rem)', // Account for header and footer
            color: 'var(--festival-text-auto)'
          }}
        >
          {tip.description && (
            <div
              className="festival-card-subtle p-4"
              style={{
                background: 'var(--light-effect-subtle)',
                border: '1px solid var(--festival-border)',
                borderRadius: 'var(--festival-radius)',
                transition: 'var(--festival-transition)'
              }}
            >
              <p
                className="text-sm"
                style={{ color: 'var(--festival-text-muted)' }}
              >
                {tip.description}
              </p>
            </div>
          )}

          <div
            className="prose max-w-none"
            style={{ color: 'var(--festival-text-auto)' }}
          >
            <p
              className="leading-relaxed"
              style={{
                color: 'var(--festival-text-auto)',
                lineHeight: '1.7'
              }}
            >
              {tip.content}
            </p>
          </div>

          {/* Enhanced Tags with Design Tokens */}
          {tip.tags && tip.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {tip.tags.map((tag, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="festival-component hover-scale"
                  style={{
                    backgroundColor: 'var(--festival-bg-muted)',
                    color: 'var(--festival-text-muted)',
                    border: '1px solid var(--festival-border)',
                    transition: 'var(--festival-transition)'
                  }}
                >
                  <Tag className="w-3 h-3 mr-1" />
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          {/* Enhanced Stats with Design Tokens */}
          <div
            className="flex items-center gap-4 text-sm"
            style={{ color: 'var(--festival-text-muted)' }}
          >
            {tip.view_count !== undefined && (
              <span className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {tip.view_count} views
              </span>
            )}
            {tip.helpful_count !== undefined && (
              <span className="flex items-center gap-1">
                <Heart className="w-4 h-4" />
                {tip.helpful_count} helpful
              </span>
            )}
            <span>
              {new Date(tip.created_at).toLocaleDateString()}
            </span>
          </div>
        </div>

        {/* Enhanced Footer with Advanced Design Token Integration */}
        <div
          className="flex flex-col sm:flex-row gap-2 sm:gap-3 p-4 sm:p-6 pt-2 festival-component"
          style={{
            borderTop: '1px solid var(--festival-border)',
            backgroundColor: 'var(--festival-bg-muted)',
            backdropFilter: 'var(--glassmorphism-backdrop)',
            transition: 'var(--festival-transition)'
          }}
        >
          {/* Enhanced Action buttons with mobile-first responsive design */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
            <UnifiedInteractionButton
              type="helpful"
              itemId={tip.id}
              itemType="tip"
              variant="outline"
              className="w-full sm:w-auto min-w-[120px] festival-component hover-lift"
              size="default"
            />
            <UnifiedInteractionButton
              type="share"
              itemId={tip.id}
              itemType="tip"
              variant="outline"
              className="w-full sm:w-auto min-w-[120px] festival-component hover-lift"
              size="default"
            />
            <Button
              variant="outline"
              onClick={onClose}
              className="w-full sm:w-auto min-w-[120px] festival-component hover-lift"
              style={{
                transition: 'var(--festival-transition)',
                borderColor: 'var(--festival-border)',
                backgroundColor: 'var(--festival-bg-card)',
                color: 'var(--festival-text-auto)'
              }}
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </ResponsiveModal>
  );
};

export default TipDetailsModal;

import React from 'react';
import { Lightbulb, Tag, Clock, Heart, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ResponsiveModal } from '@/components/design-system/ResponsiveModal';
import { useEnhancedColorMapping } from '@/hooks/useEnhancedColorMapping';

interface Tip {
  id: string;
  title: string;
  content: string;
  description?: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  created_at: string;
}

interface TipDetailsModalProps {
  tip: Tip | null;
  isOpen: boolean;
  onClose: () => void;
}

export const TipDetailsModal: React.FC<TipDetailsModalProps> = ({
  tip,
  isOpen,
  onClose,
}) => {
  // Use enhanced color mapping for database-driven colors
  const { colorMapping } = useEnhancedColorMapping(tip?.category ?? 'GENERAL', 'tip');

  // Generate gradient classes from color mapping
  const getGradientClasses = () => {
    if (!colorMapping) {
      // Fallback based on category
      switch (tip?.category) {
        case 'SURVIVAL': return 'from-festival-success to-festival-success';
        case 'SOCIAL': return 'from-primary to-secondary';
        case 'BUDGET': return 'from-accent to-secondary';
        case 'COMFORT': return 'from-primary to-accent';
        case 'EXPERIENCE': return 'from-secondary to-primary';
        case 'SAFETY': return 'from-destructive to-destructive';
        default: return 'from-muted to-muted-foreground';
      }
    }
    return `from-[${colorMapping.color_primary}/80] to-[${colorMapping.color_secondary}/80]`;
  };

  if (!tip) return null;

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'SURVIVAL': return 'Festival Survival';
      case 'SOCIAL': return 'Social & Networking';
      case 'BUDGET': return 'Budget & Money';
      case 'COMFORT': return 'Comfort & Gear';
      case 'EXPERIENCE': return 'Experience & Fun';
      case 'SAFETY': return 'Safety & Health';
      default: return category;
    }
  };

  const colorClass = getGradientClasses(); // Use database-driven gradient

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={onClose}
      size="lg"
      showCloseButton={false}
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center gap-3">
            <div className={`w-10 h-10 bg-gradient-to-br ${colorClass} rounded-lg flex items-center justify-center`}>
              <Lightbulb className="w-5 h-5 text-primary-foreground" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-foreground">{tip.title}</h2>
              {tip.category && (
                <Badge variant="secondary" className="mt-1">
                  {getCategoryLabel(tip.category)}
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          {tip.description && (
            <div className="bg-muted/20 rounded-lg p-4">
              <p className="text-muted-foreground text-sm">{tip.description}</p>
            </div>
          )}

          <div className="prose prose-foreground max-w-none">
            <p className="text-foreground leading-relaxed">{tip.content}</p>
          </div>

          {/* Tags */}
          {tip.tags && tip.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {tip.tags.map((tag, index) => (
                <Badge key={index} variant="outline">
                  <Tag className="w-3 h-3 mr-1" />
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          {/* Stats */}
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            {tip.view_count !== undefined && (
              <span className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {tip.view_count} views
              </span>
            )}
            {tip.helpful_count !== undefined && (
              <span className="flex items-center gap-1">
                <Heart className="w-4 h-4" />
                {tip.helpful_count} helpful
              </span>
            )}
            <span>
              {new Date(tip.created_at).toLocaleDateString()}
            </span>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-border">
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
              >
                <Heart className="w-4 h-4 mr-2" />
                Helpful
              </Button>
              <Button
                variant="outline"
                size="sm"
              >
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
              <Button
                variant="outline"
                onClick={onClose}
              >
                Close
              </Button>
            </div>
            {tip.is_featured && (
              <Badge variant="outline" className="bg-accent/20 text-accent border-accent/30">
                Featured
              </Badge>
            )}
          </div>
        </div>
      </div>
    </ResponsiveModal>
  );
};

export default TipDetailsModal;

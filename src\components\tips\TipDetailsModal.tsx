import React, { useState, useEffect } from 'react';
import { Lightbulb, Tag, Clock, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ResponsiveModal } from '@/components/design-system/ResponsiveModal';
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';
import { UnifiedInteractionButton } from '@/components/design-system';

interface Tip {
  id: string;
  title: string;
  content: string;
  description?: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  created_at: string;
}

interface TipDetailsModalProps {
  tip: Tip | null;
  isOpen: boolean;
  onClose: () => void;
}

export const TipDetailsModal: React.FC<TipDetailsModalProps> = ({
  tip,
  isOpen,
  onClose,
}) => {
  // Modern color system using enhancedColorMappingService
  const [colorTheme, setColorTheme] = useState<{
    className: string;
    style: React.CSSProperties;
  }>({ className: '', style: {} });
  const [isLoadingTheme, setIsLoadingTheme] = useState(false);

  // Load color theme using enhancedColorMappingService
  useEffect(() => {
    const loadColorTheme = async () => {
      if (!tip || !isOpen) return;

      setIsLoadingTheme(true);
      try {
        const theme = await enhancedColorMappingService.getEnhancedColorTheme(
          'tip',
          tip.category ?? 'GENERAL',
          0 // Use first fallback theme
        );
        setColorTheme(theme);
      } catch (error) {
        console.warn('Failed to load tip color theme:', error);
        // Fallback theme using design tokens
        setColorTheme({
          className: 'bg-gradient-to-br from-blue-50 to-teal-50',
          style: {
            background: 'var(--festival-gradient-primary)',
            borderColor: 'var(--festival-border)'
          }
        });
      } finally {
        setIsLoadingTheme(false);
      }
    };

    loadColorTheme();
  }, [tip, isOpen]);

  if (!tip) return null;

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'SURVIVAL': return 'Festival Survival';
      case 'SOCIAL': return 'Social & Networking';
      case 'BUDGET': return 'Budget & Money';
      case 'COMFORT': return 'Comfort & Gear';
      case 'EXPERIENCE': return 'Experience & Fun';
      case 'SAFETY': return 'Safety & Health';
      default: return category;
    }
  };

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={onClose}
      size="lg"
      showCloseButton={false}
    >
      <div className="flex flex-col h-full">
        {/* Header with enhanced color theming */}
        <div
          className="flex items-center justify-between p-6 border-b"
          style={{
            background: !isLoadingTheme ? colorTheme.style.background : 'var(--festival-bg-card)',
            borderColor: 'var(--festival-border)'
          }}
        >
          <div className="flex items-center gap-3">
            <div
              className="w-10 h-10 rounded-lg flex items-center justify-center"
              style={{
                background: !isLoadingTheme && colorTheme.style.background
                  ? colorTheme.style.background
                  : 'var(--festival-gradient-primary)',
                color: 'var(--festival-text-on-primary)'
              }}
            >
              <Lightbulb className="w-5 h-5" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-foreground">{tip.title}</h2>
              {tip.category && (
                <Badge variant="secondary" className="mt-1">
                  {getCategoryLabel(tip.category)}
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          {tip.description && (
            <div className="bg-muted/20 rounded-lg p-4">
              <p className="text-muted-foreground text-sm">{tip.description}</p>
            </div>
          )}

          <div className="prose prose-foreground max-w-none">
            <p className="text-foreground leading-relaxed">{tip.content}</p>
          </div>

          {/* Tags */}
          {tip.tags && tip.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {tip.tags.map((tag, index) => (
                <Badge key={index} variant="outline">
                  <Tag className="w-3 h-3 mr-1" />
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          {/* Stats */}
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            {tip.view_count !== undefined && (
              <span className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {tip.view_count} views
              </span>
            )}
            {tip.helpful_count !== undefined && (
              <span className="flex items-center gap-1">
                <Heart className="w-4 h-4" />
                {tip.helpful_count} helpful
              </span>
            )}
            <span>
              {new Date(tip.created_at).toLocaleDateString()}
            </span>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-border">
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              <UnifiedInteractionButton
                type="helpful"
                itemId={tip.id}
                itemType="tip"
                variant="outline"
                size="sm"
              />
              <UnifiedInteractionButton
                type="share"
                itemId={tip.id}
                itemType="tip"
                variant="outline"
                size="sm"
              />
              <Button
                variant="outline"
                onClick={onClose}
              >
                Close
              </Button>
            </div>
            {tip.is_featured && (
              <Badge variant="outline" className="bg-accent/20 text-accent border-accent/30">
                Featured
              </Badge>
            )}
          </div>
        </div>
      </div>
    </ResponsiveModal>
  );
};

export default TipDetailsModal;

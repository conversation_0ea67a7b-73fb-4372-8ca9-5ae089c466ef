#!/usr/bin/env node

/**
 * Admin Dashboard Testing Script
 * 
 * This script tests the admin dashboard functionality including
 * access control, role-based permissions, and admin operations.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Test admin access control and permissions
 */
async function testAdminAccessControl() {
  console.log('🔐 Testing Admin Access Control');
  console.log('==============================');
  
  const results = {
    adminUserExists: false,
    roleBasedAccess: false,
    permissionSystem: false,
    adminRoutes: false
  };

  // Test 1: Check for Admin Users
  console.log('\n1️⃣ Testing Admin User Existence...');
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, username, role')
      .in('role', ['SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR']);

    if (!error && data && data.length > 0) {
      console.log('✅ Admin users found');
      console.log(`   Admin users: ${data.length}`);
      data.forEach(admin => {
        console.log(`   - ${admin.username}: ${admin.role}`);
      });
      results.adminUserExists = true;
    } else if (!error) {
      console.log('⚠️  No admin users found');
    } else {
      console.log(`❌ Admin user check failed: ${error.message}`);
    }
  } catch (error) {
    console.log(`❌ Admin user check error: ${error.message}`);
  }

  // Test 2: Role-Based Access Verification
  console.log('\n2️⃣ Testing Role-Based Access...');
  try {
    // Test different role permissions
    const roles = ['SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR', 'USER'];
    let roleTestsPassed = 0;

    for (const role of roles) {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, role')
        .eq('role', role)
        .limit(1);

      if (!error) {
        console.log(`✅ Role ${role} query successful`);
        roleTestsPassed++;
      } else {
        console.log(`⚠️  Role ${role} query: ${error.message}`);
      }
    }

    if (roleTestsPassed >= 3) {
      console.log('✅ Role-based access system working');
      results.roleBasedAccess = true;
    }
  } catch (error) {
    console.log(`❌ Role-based access error: ${error.message}`);
  }

  // Test 3: Permission System
  console.log('\n3️⃣ Testing Permission System...');
  try {
    // Test permission-based queries
    const permissionTests = [
      { table: 'festivals', operation: 'select' },
      { table: 'events', operation: 'select' },
      { table: 'activities', operation: 'select' },
      { table: 'guides', operation: 'select' },
      { table: 'tips', operation: 'select' }
    ];

    let permissionsPassed = 0;

    for (const test of permissionTests) {
      const { data, error } = await supabase
        .from(test.table)
        .select('id')
        .limit(1);

      if (!error) {
        console.log(`✅ ${test.table} ${test.operation} permission working`);
        permissionsPassed++;
      } else {
        console.log(`⚠️  ${test.table} ${test.operation}: ${error.message}`);
      }
    }

    if (permissionsPassed >= 4) {
      console.log('✅ Permission system working');
      results.permissionSystem = true;
    }
  } catch (error) {
    console.log(`❌ Permission system error: ${error.message}`);
  }

  // Test 4: Admin Routes Accessibility
  console.log('\n4️⃣ Testing Admin Routes...');
  try {
    // Test admin-specific data access
    const adminTables = ['announcements', 'external_links'];
    let adminRoutesPassed = 0;

    for (const table of adminTables) {
      const { data, error } = await supabase
        .from(table)
        .select('id')
        .limit(1);

      if (!error) {
        console.log(`✅ Admin table ${table} accessible`);
        adminRoutesPassed++;
      } else {
        console.log(`⚠️  Admin table ${table}: ${error.message}`);
      }
    }

    if (adminRoutesPassed >= 1) {
      console.log('✅ Admin routes accessible');
      results.adminRoutes = true;
    }
  } catch (error) {
    console.log(`❌ Admin routes error: ${error.message}`);
  }

  return results;
}

/**
 * Test admin dashboard functionality
 */
async function testAdminDashboardFunctionality() {
  console.log('\n🎛️ Testing Admin Dashboard Functionality');
  console.log('========================================');
  
  const results = {
    userManagement: false,
    festivalManagement: false,
    eventManagement: false,
    contentManagement: false,
    announcementManagement: false,
    externalLinksManagement: false
  };

  // Test 1: User Management
  console.log('\n👥 Testing User Management...');
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, username, role, created_at')
      .order('created_at', { ascending: false })
      .limit(5);

    if (!error) {
      console.log('✅ User management data accessible');
      console.log(`   Can view ${data?.length || 0} user profiles`);
      results.userManagement = true;
    } else {
      console.log(`❌ User management failed: ${error.message}`);
    }
  } catch (error) {
    console.log(`❌ User management error: ${error.message}`);
  }

  // Test 2: Festival Management
  console.log('\n🎪 Testing Festival Management...');
  try {
    const { data, error } = await supabase
      .from('festivals')
      .select('id, name, status, location, created_at')
      .order('created_at', { ascending: false })
      .limit(5);

    if (!error) {
      console.log('✅ Festival management data accessible');
      console.log(`   Can manage ${data?.length || 0} festivals`);
      results.festivalManagement = true;
    } else {
      console.log(`❌ Festival management failed: ${error.message}`);
    }
  } catch (error) {
    console.log(`❌ Festival management error: ${error.message}`);
  }

  // Test 3: Event Management
  console.log('\n🎭 Testing Event Management...');
  try {
    const { data, error } = await supabase
      .from('events')
      .select('id, title, status, created_at')
      .order('created_at', { ascending: false })
      .limit(5);

    if (!error) {
      console.log('✅ Event management data accessible');
      console.log(`   Can manage ${data?.length || 0} events`);
      results.eventManagement = true;
    } else {
      console.log(`❌ Event management failed: ${error.message}`);
    }
  } catch (error) {
    console.log(`❌ Event management error: ${error.message}`);
  }

  // Test 4: Content Management
  console.log('\n📚 Testing Content Management...');
  try {
    const contentTables = ['guides', 'tips'];
    let contentSuccess = 0;

    for (const table of contentTables) {
      const { data, error } = await supabase
        .from(table)
        .select('id, title, created_at')
        .limit(3);

      if (!error) {
        console.log(`✅ ${table} management accessible (${data?.length || 0} items)`);
        contentSuccess++;
      } else {
        console.log(`⚠️  ${table} management: ${error.message}`);
      }
    }

    if (contentSuccess >= 1) {
      console.log('✅ Content management working');
      results.contentManagement = true;
    }
  } catch (error) {
    console.log(`❌ Content management error: ${error.message}`);
  }

  // Test 5: Announcement Management
  console.log('\n📢 Testing Announcement Management...');
  try {
    const { data, error } = await supabase
      .from('announcements')
      .select('id, title, priority, created_at')
      .order('created_at', { ascending: false })
      .limit(5);

    if (!error) {
      console.log('✅ Announcement management accessible');
      console.log(`   Can manage ${data?.length || 0} announcements`);
      results.announcementManagement = true;
    } else {
      console.log(`❌ Announcement management failed: ${error.message}`);
    }
  } catch (error) {
    console.log(`❌ Announcement management error: ${error.message}`);
  }

  // Test 6: External Links Management
  console.log('\n🔗 Testing External Links Management...');
  try {
    const { data, error } = await supabase
      .from('external_links')
      .select('id, title, url, category, created_at')
      .order('created_at', { ascending: false })
      .limit(5);

    if (!error) {
      console.log('✅ External links management accessible');
      console.log(`   Can manage ${data?.length || 0} external links`);
      results.externalLinksManagement = true;
    } else {
      console.log(`❌ External links management failed: ${error.message}`);
    }
  } catch (error) {
    console.log(`❌ External links management error: ${error.message}`);
  }

  return results;
}

/**
 * Generate comprehensive admin dashboard report
 */
function generateAdminReport(accessResults, functionalityResults) {
  console.log('\n📊 ADMIN DASHBOARD ASSESSMENT');
  console.log('=============================');
  
  const allResults = { ...accessResults, ...functionalityResults };
  
  const tests = [
    { name: 'Admin User Exists', key: 'adminUserExists', weight: 2 },
    { name: 'Role-Based Access', key: 'roleBasedAccess', weight: 2 },
    { name: 'Permission System', key: 'permissionSystem', weight: 2 },
    { name: 'Admin Routes', key: 'adminRoutes', weight: 1 },
    { name: 'User Management', key: 'userManagement', weight: 2 },
    { name: 'Festival Management', key: 'festivalManagement', weight: 2 },
    { name: 'Event Management', key: 'eventManagement', weight: 1 },
    { name: 'Content Management', key: 'contentManagement', weight: 1 },
    { name: 'Announcement Management', key: 'announcementManagement', weight: 1 },
    { name: 'External Links Management', key: 'externalLinksManagement', weight: 1 }
  ];

  let totalWeight = 0;
  let passedWeight = 0;

  tests.forEach(test => {
    const status = allResults[test.key] ? '✅ PASS' : '❌ FAIL';
    console.log(`${test.name}: ${status} (Weight: ${test.weight})`);
    
    totalWeight += test.weight;
    if (allResults[test.key]) {
      passedWeight += test.weight;
    }
  });

  const weightedScore = (passedWeight / totalWeight * 100).toFixed(1);
  console.log(`\nWeighted Score: ${passedWeight}/${totalWeight} (${weightedScore}%)`);

  // Map to admin dashboard checkpoints
  const adminCheckpoints = 9;
  const completedCheckpoints = Math.round((passedWeight / totalWeight) * adminCheckpoints);
  
  console.log(`\n📈 PRODUCTION READINESS UPDATE:`);
  console.log(`Admin Dashboard checkpoints: ${completedCheckpoints}/${adminCheckpoints} (${(completedCheckpoints/adminCheckpoints*100).toFixed(1)}%)`);

  // Assessment
  if (weightedScore >= 80) {
    console.log('\n✅ Admin dashboard is production-ready!');
  } else if (weightedScore >= 60) {
    console.log('\n⚠️  Admin dashboard is functional but needs improvements');
  } else {
    console.log('\n❌ Admin dashboard needs significant work before production');
  }

  return {
    score: weightedScore,
    checkpoints: completedCheckpoints,
    totalTests: tests.length,
    passedTests: tests.filter(t => allResults[t.key]).length
  };
}

// Run comprehensive admin dashboard testing
async function runAdminDashboardTests() {
  console.log('🚀 Starting Admin Dashboard Testing');
  console.log('==================================');
  
  try {
    const accessResults = await testAdminAccessControl();
    const functionalityResults = await testAdminDashboardFunctionality();
    
    const summary = generateAdminReport(accessResults, functionalityResults);
    
    console.log('\n🏁 Admin dashboard testing completed');
    console.log(`Final Score: ${summary.score}% (${summary.checkpoints}/9 checkpoints)`);
    console.log(`Tests Passed: ${summary.passedTests}/${summary.totalTests}`);
    
    return summary;
  } catch (error) {
    console.error('\n💥 Admin dashboard testing failed:', error);
    throw error;
  }
}

// Run the tests
runAdminDashboardTests()
  .then(() => {
    process.exit(0);
  })
  .catch((error) => {
    console.error('Admin dashboard testing failed:', error);
    process.exit(1);
  });

import React, { useEffect, useState, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../providers/ConsolidatedAuthProvider';
import { checkSupabaseConnection, supabase } from '@/lib/supabase';
import { toast } from 'react-hot-toast';
import { Calendar, Users, Music, Star, TrendingUp, Bell, RefreshCw, ArrowRight, Heart, Activity, MapPin, Clock, Eye, ChevronDown, ChevronUp } from 'lucide-react';
import { motion, AnimatePresence, PanInfo, useMotionValue, useTransform } from 'framer-motion';
import { simulateHapticFeedback, isMobileViewport, createTouchHandler } from '../utils/mobileUX';
import MobileUXTester from '../components/testing/MobileUXTester';
import AnnouncementPopup from '../components/announcements/AnnouncementPopup';
import { useGestureNavigation } from '@/hooks/ui/useGestureNavigation';
import OneHandedOptimizer from '@/components/mobile/OneHandedOptimizer';
import { useAnnouncementDismissal } from '@/hooks/useAnnouncementDismissal';
import type { Announcement } from '@/types';
import type { Database } from '@/types/supabase';
import { AnnouncementDisplayType, AnnouncementPriority } from '@/types/announcements';
import { dashboardIntelligenceService, type DashboardIntelligence, type LiveUpdate, type QuickAction, type CommunityBuzz } from '@/lib/intelligence';
import { usePersonalization, type PersonalizedContent } from '@/hooks/usePersonalization';
import { ActivityFeed } from '@/components/community/ActivityFeed';
import { UnifiedNotificationSystem } from '@/components/notifications/UnifiedNotificationSystem';
// Import new unified design system components
import {
  PageWrapper,
  UnifiedCard,
  UnifiedButton,
  UnifiedBadge,
  UnifiedAnnouncement,
  GridLayout,
  FlexLayout,
  BentoGrid,
  BentoCard,
  BentoStat
} from '@/components/design-system';

// Types for dashboard data (merged from Home.tsx)
type AnnouncementRow = Database['public']['Tables']['announcements']['Row'] & {
  category?: Array<{
    id: string;
    name: string;
    color: string | null;
    description: string | null;
    created_at: string;
    updated_at: string;
  }>;
  message?: string | null;
};

interface Festival {
  id: string;
  name: string;
  description: string | null;
  start_date?: string;
  end_date?: string;
  location?: string;
  featured?: boolean;
}

interface Activity {
  id: string;
  title: string;
  description: string;
  type: string;
  location?: string | null;
  start_date?: string | null;
  end_date?: string | null;
  capacity?: number | null;
  is_featured?: boolean | null;
  festival_id?: string | null;
}

interface Tip {
  id: string;
  title: string;
  description?: string | null;
  category: string | null;
  is_featured?: boolean | null;
  helpful_count?: number | null;
  view_count?: number | null;
}

interface Guide {
  id: string;
  title: string;
  description?: string | null;
  category: string | null;
  is_featured?: boolean | null;
  view_count?: number | null;
}

/**
 * Enhanced Authenticated Home Page
 *
 * Comprehensive dashboard for logged-in users with:
 * - Personalized content and quick access to features
 * - Today's activities, featured tips, and guides
 * - Recent announcements with priority-based color coding
 * - Pull-to-refresh functionality for mobile
 * - Uses unified design system to eliminate background layering
 */
const AuthenticatedHome: React.FC = () => {
  const { user, profile, loading, refreshProfile } = useAuth();
  const navigate = useNavigate();

  // Mobile-specific state management
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);

  // Personalization
  const {
    getPersonalizedActivities,
    getPersonalizedEvents,
    getPersonalizationMetrics,
    loading: personalizationLoading
  } = usePersonalization();
  const [personalizedContent, setPersonalizedContent] = useState<{
    activities: PersonalizedContent<any>[];
    events: PersonalizedContent<any>[];
    metrics: any;
  }>({ activities: [], events: [], metrics: null });
  const [isPulling, setIsPulling] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  // Dashboard data state (merged from Home.tsx)
  const [showAnnouncement, setShowAnnouncement] = useState(false);
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  // Enhanced personalized dashboard state
  const [userUpcomingActivities, setUserUpcomingActivities] = useState<Activity[]>([]);
  const [userSignedUpEvents, setUserSignedUpEvents] = useState<any[]>([]);
  const [userActivityCount, setUserActivityCount] = useState(0);
  const [todaysActivities, setTodaysActivities] = useState<Activity[]>([]);
  const [featuredTips, setFeaturedTips] = useState<Tip[]>([]);
  const [featuredGuides, setFeaturedGuides] = useState<Guide[]>([]);
  const [recentAnnouncements, setRecentAnnouncements] = useState<AnnouncementRow[]>([]);
  const [dashboardLoading, setDashboardLoading] = useState(true);

  // Social feed dashboard state
  const [dashboardIntelligence, setDashboardIntelligence] = useState<DashboardIntelligence | null>(null);
  const [intelligenceLoading, setIntelligenceLoading] = useState(false);

  // Mobile-first content prioritization state
  const [expandedSections, setExpandedSections] = useState({
    announcements: false,
    tips: false,
    guides: false,
    schedule: false
  });

  // Mobile content priority configuration
  const mobileContentPriority = {
    aboveFold: ['quickStats', 'urgentActions', 'happeningNow'],
    secondaryFold: ['announcements', 'schedule'],
    belowFold: ['featuredTips', 'guides']
  };

  // Helper function to toggle section expansion
  const toggleSection = useCallback((section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
    simulateHapticFeedback();
  }, []);

  // Gesture navigation for mobile
  const { gestureHandlers, gestureProgress, gestureDirection } = useGestureNavigation({
    routes: { right: '/activities' }
  });

  // Quick actions for one-handed optimization (will be populated after handlePullToRefresh is defined)
  const [oneHandedQuickActions, setOneHandedQuickActions] = useState<Array<{
    id: string;
    label: string;
    icon: React.ReactNode;
    action: () => void;
    priority: 'high' | 'medium' | 'low';
  }>>([]);

  // Swipeable Action Card Component for Tinder-style interactions
  const SwipeableActionCard: React.FC<{
    action: any;
    index: number;
    onSwipeAction: (action: any, direction: 'left' | 'right') => void;
  }> = ({ action, index, onSwipeAction }) => {
    const x = useMotionValue(0);
    const rotate = useTransform(x, [-200, 200], [-25, 25]);
    const opacity = useTransform(x, [-200, -100, 0, 100, 200], [0, 1, 1, 1, 0]);

    const handleDragEnd = (event: any, info: PanInfo) => {
      const threshold = 100;
      if (Math.abs(info.offset.x) > threshold) {
        const direction = info.offset.x > 0 ? 'right' : 'left';
        onSwipeAction(action, direction);
        simulateHapticFeedback();
      }
    };

    return (
      <motion.div
        drag="x"
        dragConstraints={{ left: 0, right: 0 }}
        style={{ x, rotate, opacity }}
        onDragEnd={handleDragEnd}
        whileDrag={{ scale: 1.05 }}
        className="relative"
      >
        <BentoCard
          title={action.title}
          description={action.description}
          variant="glassmorphism"
          interactive
          onClick={() => navigate(action.actionUrl)}
          className={`text-center ${action.urgency === 'high' ? 'pulse-glow' : ''} cursor-grab active:cursor-grabbing`}
        >
          <div className="text-2xl mb-2 flex justify-center">
            {action.icon === 'map-pin' && <MapPin className="w-6 h-6 text-festival-orange" />}
            {action.icon === 'clock' && <Clock className="w-6 h-6 text-festival-orange" />}
            {action.icon === 'calendar' && <Calendar className="w-6 h-6 text-festival-orange" />}
            {action.icon === 'users' && <Users className="w-6 h-6 text-festival-orange" />}
          </div>
        </BentoCard>

        {/* Swipe indicators */}
        <motion.div
          className="absolute inset-0 flex items-center justify-start pl-4 pointer-events-none"
          style={{ opacity: useTransform(x, [0, 100], [0, 1]) }}
        >
          <div className="bg-festival-success text-primary-foreground px-3 py-1 rounded-full text-sm font-medium">
            ✓ Quick Join
          </div>
        </motion.div>

        <motion.div
          className="absolute inset-0 flex items-center justify-end pr-4 pointer-events-none"
          style={{ opacity: useTransform(x, [-100, 0], [1, 0]) }}
        >
          <div className="bg-primary text-primary-foreground px-3 py-1 rounded-full text-sm font-medium">
            ℹ️ More Info
          </div>
        </motion.div>
      </motion.div>
    );
  };

  // Mobile-first collapsible section component
  const CollapsibleSection: React.FC<{
    title: string;
    sectionKey: keyof typeof expandedSections;
    children: React.ReactNode;
    priority?: 'high' | 'medium' | 'low';
    defaultExpanded?: boolean;
  }> = ({ title, sectionKey, children, priority = 'medium', defaultExpanded = false }) => {
    const isExpanded = expandedSections[sectionKey];
    const shouldShowCollapsed = !defaultExpanded && isMobileViewport();

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mobile-section-spacing"
      >
        <div
          className="flex items-center justify-between cursor-pointer mobile-content-spacing"
          onClick={() => toggleSection(sectionKey)}
        >
          <h2 className={`text-lg md:text-xl font-bold text-primary-light ${
            priority === 'high' ? 'text-festival-priority-high' :
            priority === 'low' ? 'text-muted-foreground' : ''
          }`}>
            {title}
          </h2>
          {shouldShowCollapsed && (
            <motion.div
              animate={{ rotate: isExpanded ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronDown className="w-5 h-5 text-muted-foreground" />
            </motion.div>
          )}
        </div>

        <AnimatePresence>
          {(isExpanded || !shouldShowCollapsed) && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              style={{ overflow: 'hidden' }}
            >
              {children}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    );
  };

  // Swipe action handler for quick actions
  const handleSwipeAction = useCallback((action: any, direction: 'left' | 'right') => {
    if (direction === 'right') {
      // Right swipe = Quick join/action
      navigate(action.actionUrl);
      toast.success(`Quick action: ${action.title}`);
    } else {
      // Left swipe = More info/details
      toast(`More info about: ${action.title}`, { icon: 'ℹ️' });
      // Could open a modal or navigate to details page
    }
  }, [navigate]);

  // Announcement dismissal
  const {
    dismissAnnouncement,
    isDismissed
  } = useAnnouncementDismissal();

  // Get today's date for filtering
  const today = new Date().toISOString().split('T')[0];

  // Fetch dashboard intelligence
  const fetchDashboardIntelligence = async () => {
    if (!user) return;

    try {
      setIntelligenceLoading(true);
      const { data, error } = await dashboardIntelligenceService.getDashboardIntelligence(user.id);

      if (error) {
        console.error('Error fetching dashboard intelligence:', error);
        return;
      }

      if (data) {
        setDashboardIntelligence(data);
      }
    } catch (error) {
      console.error('Error fetching dashboard intelligence:', error);
    } finally {
      setIntelligenceLoading(false);
    }
  };

  // Enhanced fetch dashboard data with personalized information
  const fetchDashboardData = async () => {
    try {
      setDashboardLoading(true);

      // Fetch user's upcoming activities (personalized)
      if (user) {
        const { data: userActivities } = await supabase
          .from('activity_participants')
          .select(`
            *,
            activities!inner(*)
          `)
          .eq('user_id', user.id)
          .eq('status', 'registered')
          .gte('activities.start_date', new Date().toISOString())
          .order('activities.start_date', { ascending: true })
          .limit(5);

        setUserUpcomingActivities(userActivities?.map(p => p.activities) || []);
        setUserActivityCount(userActivities?.length || 0);
      }

      // Fetch today's activities (general)
      const { data: activities } = await supabase
        .from('activities')
        .select('*')
        .gte('start_date', today)
        .lte('start_date', `${today}T23:59:59`)
        .order('start_date', { ascending: true })
        .limit(6);

      // Fetch featured tips
      const { data: tips } = await supabase
        .from('tips')
        .select('*')
        .eq('is_featured', true)
        .eq('status', 'published')
        .order('helpful_count', { ascending: false })
        .limit(4);

      // Fetch featured guides
      const { data: guides } = await supabase
        .from('guides')
        .select('*')
        .eq('is_featured', true)
        .eq('status', 'published')
        .order('view_count', { ascending: false })
        .limit(4);

      // Fetch recent announcements
      const { data: announcementsData } = await supabase
        .from('announcements')
        .select('*')
        .eq('active', true)
        .order('created_at', { ascending: false })
        .limit(3);

      setTodaysActivities(activities || []);
      setFeaturedTips(tips || []);
      setFeaturedGuides(guides || []);
      setRecentAnnouncements(announcementsData || []);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setDashboardLoading(false);
    }
  };

  // Fetch personalized content
  const fetchPersonalizedContent = async () => {
    if (!user) return;

    try {
      const [activities, events, metrics] = await Promise.all([
        getPersonalizedActivities(4),
        getPersonalizedEvents(3),
        getPersonalizationMetrics()
      ]);

      setPersonalizedContent({ activities, events, metrics });
    } catch (error) {
      console.error('Error fetching personalized content:', error);
    }
  };

  // Fetch popup announcements
  const fetchAnnouncements = async () => {
    try {
      const { data, error } = await supabase
        .from('announcements')
        .select('*')
        .eq('active', true)
        .eq('display_type', 'popup')
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;

      const mappedAnnouncements: Announcement[] = (data || []).map(row => ({
        ...row,
        priority: (row.priority as AnnouncementPriority) || 'NORMAL',
        display_type: (row.display_type as AnnouncementDisplayType) || 'BANNER',
        created_at: row.created_at || new Date().toISOString(),
        updated_at: row.updated_at || new Date().toISOString(),
        created_by: row.created_by || '',
        is_active: row.active || false,
        active: row.active || false,
        target_audience: row.target_audience as string[] || [],
      } as Announcement));

      const activeAnnouncements = mappedAnnouncements.filter(ann =>
        ann.active && !isDismissed(ann.id.toString())
      );

      setAnnouncements(activeAnnouncements);

      if (activeAnnouncements.length > 0) {
        setShowAnnouncement(true);
      }
    } catch (error) {
      console.error('Error fetching announcements:', error);
    }
  };

  // Check if mobile viewport
  useEffect(() => {
    const checkMobile = () => setIsMobile(isMobileViewport());
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Enhanced pull-to-refresh implementation
  const handlePullToRefresh = useCallback(async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    simulateHapticFeedback('medium');

    try {
      // Show immediate feedback with loading toast
      toast.loading('Refreshing dashboard...', { id: 'refresh-toast' });

      // Refresh profile, check connection, fetch dashboard data, and intelligence
      await Promise.all([
        refreshProfile(),
        checkSupabaseConnection(),
        fetchDashboardData(),
        fetchDashboardIntelligence(),
        fetchAnnouncements()
      ]);

      setLastRefresh(new Date());
      // Success feedback with haptic
      simulateHapticFeedback('light');
      toast.success('Dashboard refreshed!', { id: 'refresh-toast' });
    } catch (error) {
      console.error('Refresh failed:', error);
      toast.error('Failed to refresh. Please try again.', { id: 'refresh-toast' });
    } finally {
      setIsRefreshing(false);
      setPullDistance(0);
      setIsPulling(false);
    }
  }, [isRefreshing, refreshProfile]);

  // Populate quick actions after handlePullToRefresh is defined
  useEffect(() => {
    setOneHandedQuickActions([
      {
        id: 'refresh',
        label: 'Refresh',
        icon: <RefreshCw className="w-4 h-4" />,
        action: handlePullToRefresh,
        priority: 'high'
      },
      {
        id: 'activities',
        label: 'Activities',
        icon: <Calendar className="w-4 h-4" />,
        action: () => navigate('/activities'),
        priority: 'medium'
      }
    ]);
  }, [handlePullToRefresh]);

  // Unified touch handlers that coordinate pull-to-refresh and gesture navigation
  const handleUnifiedTouchStart = useCallback((e: React.TouchEvent) => {
    // Handle pull-to-refresh
    if (window.scrollY === 0 && isMobile) {
      setIsPulling(true);
    }

    // Handle gesture navigation
    gestureHandlers.onTouchStart(e);
  }, [isMobile, gestureHandlers]);

  const handleUnifiedTouchMove = useCallback((e: React.TouchEvent) => {
    // Handle pull-to-refresh
    if (isPulling && isMobile) {
      const touch = e.touches[0];
      const startY = touch.clientY;
      const distance = Math.max(0, Math.min(startY / 3, 100)); // Max 100px pull
      setPullDistance(distance);
    }

    // Handle gesture navigation
    gestureHandlers.onTouchMove(e);
  }, [isPulling, isMobile, gestureHandlers]);

  const handleUnifiedTouchEnd = useCallback((_e: React.TouchEvent) => {
    // Handle pull-to-refresh
    if (isPulling && isMobile) {
      if (pullDistance > 60) { // Threshold for refresh
        handlePullToRefresh();
      } else {
        setPullDistance(0);
        setIsPulling(false);
      }
    }

    // Handle gesture navigation
    gestureHandlers.onTouchEnd();
  }, [isPulling, isMobile, pullDistance, handlePullToRefresh, gestureHandlers]);

  // Utility functions for formatting
  const formatTime = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const getPriorityLevel = (priority?: string | null): 'high' | 'medium' | 'low' => {
    switch (priority?.toLowerCase()) {
      case 'urgent':
      case 'high':
        return 'high';
      case 'medium':
      case 'normal':
        return 'medium';
      default:
        return 'low';
    }
  };

  useEffect(() => {
    // Check Supabase connection and fetch data when component mounts
    checkSupabaseConnection().then(connectionResult => {
      if (!connectionResult.success) {
        console.error('Connection failed:', connectionResult.message);
        // Remove duplicate toast - connection status is handled by ConnectionStatus component
      } else if (user && !profile) {
        // If we have a user but no profile, try to refresh the profile
        console.log('User exists but no profile found. Attempting to refresh profile...');
        refreshProfile();
      }
    });

    // Fetch dashboard data and intelligence
    fetchDashboardData();
    fetchDashboardIntelligence();
    fetchAnnouncements();
  }, [user, profile, refreshProfile]);

  // Load personalized content when user is available
  useEffect(() => {
    if (user) {
      fetchPersonalizedContent();
    }
  }, [user]);

  if (loading || dashboardLoading || intelligenceLoading) {
    return (
      <PageWrapper title="Loading..." variant="default">
        <div className="flex items-center justify-center min-h-96">
          <motion.div
            className="text-center"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <motion.div
              className="w-12 h-12 sm:w-16 sm:h-16 border-3 border-white/30 border-t-purple-400 rounded-full mx-auto mb-4"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <h2 className="text-lg sm:text-xl font-semibold mb-2 text-primary-dark">Loading your dashboard...</h2>
            <p className="text-sm sm:text-base text-secondary-dark">
              {isMobile ? 'Pull down to refresh when ready' : 'If this takes too long, try refreshing the page.'}
            </p>
          </motion.div>
        </div>
      </PageWrapper>
    );
  }

  if (!user) {
    return (
      <PageWrapper title="Welcome to Festival Family" variant="default">
        <div className="flex items-center justify-center min-h-96">
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="w-16 h-16 sm:w-20 sm:h-20 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <Users className="w-8 h-8 sm:w-10 sm:h-10 text-primary" />
            </div>
            <h2 className="text-lg sm:text-xl font-semibold mb-4 text-primary-dark">Please sign in to access your dashboard</h2>
            {/* Authentication handled by navigation components - no duplicate sign-in button needed */}
          </motion.div>
        </div>
      </PageWrapper>
    );
  }

  return (
    <>
      {/* Announcement Popup */}
      <AnimatePresence>
        {showAnnouncement && announcements.length > 0 && (
          <AnnouncementPopup
            announcement={announcements[currentIndex] as any}
            isOpen={showAnnouncement}
            onClose={() => {
              if (currentIndex < announcements.length - 1) {
                setCurrentIndex(currentIndex + 1);
              } else {
                setShowAnnouncement(false);
              }
            }}
            onDismiss={(id: string) => {
              dismissAnnouncement(id);
              if (currentIndex < announcements.length - 1) {
                setCurrentIndex(currentIndex + 1);
              } else {
                setShowAnnouncement(false);
              }
            }}
          />
        )}
      </AnimatePresence>

      <OneHandedOptimizer quickActions={oneHandedQuickActions}>
        <div
          className={`flex-1 relative overflow-hidden ${gestureHandlers.className}`}
          onTouchStart={handleUnifiedTouchStart}
          onTouchMove={handleUnifiedTouchMove}
          onTouchEnd={handleUnifiedTouchEnd}
        >
        {/* Pull-to-Refresh Indicator */}
        <AnimatePresence>
          {(isPulling || isRefreshing) && (
            <motion.div
              className="absolute top-0 left-0 right-0 z-50 flex items-center justify-center bg-gradient-to-b from-primary/20 to-transparent backdrop-blur-sm"
              initial={{ height: 0, opacity: 0 }}
              animate={{
                height: Math.max(60, pullDistance + 20),
                opacity: 1
              }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              <motion.div
                className="flex items-center gap-2 text-primary-foreground"
                animate={isRefreshing ? { rotate: 360 } : { rotate: pullDistance * 3.6 }}
                transition={isRefreshing ? { duration: 1, repeat: Infinity, ease: "linear" } : { duration: 0.1 }}
              >
                <RefreshCw className="w-5 h-5" />
                <span className="text-sm font-medium">
                  {isRefreshing ? 'Refreshing...' : pullDistance > 60 ? 'Release to refresh' : 'Pull to refresh'}
                </span>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Dashboard Content using Unified Design System */}
        <div className="mobile-page-padding space-y-4 md:space-y-6">
          {/* Compact Horizontal Greeting Banner */}
          <div className="bg-gradient-to-r from-primary/10 to-accent/10 border border-primary/20 rounded-lg px-4 py-2 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-lg">👋</span>
                <span className="font-medium text-foreground">
                  Hey {profile?.full_name?.split(' ')[0] ?? user.email?.split('@')[0] ?? 'there'}!
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground hidden sm:block">
                  What's happening today?
                </span>
                <UnifiedButton
                  variant="ghost"
                  size="sm"
                  onClick={handlePullToRefresh}
                  disabled={isRefreshing}
                  className="hidden md:flex items-center gap-1 text-xs"
                >
                  <RefreshCw className={`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`} />
                  <span className="hidden lg:inline">{isRefreshing ? 'Refreshing...' : 'Refresh'}</span>
                </UnifiedButton>
              </div>
            </div>
          </div>

            {/* Mobile: Use pull-to-refresh instead of button - saves space */}
          </div>
          {/* Live Festival Hub - Mobile-First 2x2 Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="fade-in-up"
          >
            <div className="mb-4">
              <h2 className="text-lg md:text-xl font-bold text-primary-light">Live Festival Hub</h2>
              <p className="text-sm text-muted-light">What's happening right now</p>
            </div>

            {/* Mobile-First 2x2 Grid with Live Content */}
            <BentoGrid cols={2} gap="sm" className="mobile-section-spacing">
              {/* Live Weather Widget */}
              <BentoCard
                title="Weather"
                description="Budapest, Hungary"
                variant="featured"
                interactive
                onClick={() => navigate('/weather')}
                icon={<span className="text-2xl">☀️</span>}
                className="bg-gradient-to-br from-blue-500/10 to-blue-600/10 border-blue-500/20"
              >
                <div className="text-lg font-bold text-blue-400">
                  24°C
                </div>
                <div className="text-xs text-blue-300 mt-1">
                  Partly Cloudy • Feels like 26°C
                </div>
              </BentoCard>

              {/* Live Alerts/Notifications */}
              <BentoCard
                title="Alerts"
                description={`${dashboardIntelligence?.liveUpdates?.length || 0} active updates`}
                variant="featured"
                interactive
                onClick={() => navigate('/announcements')}
                icon={<Bell className="w-6 h-6 text-red-400" />}
                className={`${dashboardIntelligence?.liveUpdates?.some(u => u.urgency === 'high')
                  ? 'bg-gradient-to-br from-red-500/10 to-red-600/10 border-red-500/20 pulse-glow'
                  : 'bg-gradient-to-br from-gray-500/10 to-gray-600/10 border-gray-500/20'}`}
              >
                <div className="text-sm text-muted-light">
                  {dashboardIntelligence?.liveUpdates?.[0]?.title || "All clear"}
                </div>
              </BentoCard>

              {/* Today's Meetups */}
              <BentoCard
                title="Meetups"
                description={`${dashboardIntelligence?.happeningNow?.length || 0} happening now`}
                variant="featured"
                interactive
                onClick={() => navigate('/activities?tab=meetup')}
                icon={<Users className="w-6 h-6 text-emerald-400" />}
                className="bg-gradient-to-br from-emerald-500/10 to-emerald-600/10 border-emerald-500/20"
              >
                <div className="text-sm text-muted-light">
                  {dashboardIntelligence?.happeningNow?.[0]?.title || "No active meetups"}
                </div>
              </BentoCard>

              {/* Today's Events */}
              <BentoCard
                title="Events"
                description={`${dashboardIntelligence?.yourSchedule?.length || 0} on your schedule`}
                variant="featured"
                interactive
                onClick={() => navigate('/discover')}
                icon={<Music className="w-6 h-6 text-orange-400" />}
                className="bg-gradient-to-br from-orange-500/10 to-orange-600/10 border-orange-500/20"
              >
                <div className="text-sm text-muted-light">
                  {dashboardIntelligence?.yourSchedule?.[0]?.title || "No events scheduled"}
                </div>
              </BentoCard>
            </BentoGrid>
          </motion.div>

          {/* Live Social Feed - Happening Now */}
          {dashboardIntelligence?.happeningNow && dashboardIntelligence.happeningNow.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.15 }}
              className="mb-8"
            >
              <FlexLayout justify="between" align="center" className="mb-4">
                <div>
                  <h2 className="text-xl font-bold text-primary-light">🔥 Happening Now</h2>
                  <p className="text-sm text-muted-light">Activities starting in the next 30 minutes</p>
                </div>
                <UnifiedBadge variant="destructive" className="animate-pulse">
                  LIVE
                </UnifiedBadge>
              </FlexLayout>

              <BentoGrid cols={2} gap="md">
                {dashboardIntelligence.happeningNow.slice(0, 4).map((activity, index) => (
                  <BentoCard
                    key={activity.id}
                    title={activity.title}
                    description={`Starting at ${activity.location}`}
                    variant="featured"
                    interactive
                    onClick={() => navigate(`/activities/${activity.id}`)}
                    icon={<Clock className="w-5 h-5 text-destructive" />}
                    action={
                      <UnifiedButton variant="destructive" size="sm">
                        Join Now
                      </UnifiedButton>
                    }
                    className="border-destructive/20 bg-destructive/5 hover:bg-destructive/10"
                  >
                    <div className="text-sm text-muted-light">
                      Starts: {activity.start_date ? new Date(activity.start_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'TBD'}
                    </div>
                  </BentoCard>
                ))}
              </BentoGrid>
            </motion.div>
          )}

          {/* Personalized Content Section */}
          {user && personalizedContent.activities.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="mb-8"
            >
              <FlexLayout justify="between" align="center" className="mb-4">
                <div>
                  <h2 className="text-xl font-bold text-primary-light">✨ Recommended for You</h2>
                  <p className="text-sm text-muted-light">Based on your interests and activity</p>
                </div>
                <UnifiedBadge variant="secondary" className="bg-accent/20 text-accent">
                  Personalized
                </UnifiedBadge>
              </FlexLayout>

              <BentoGrid cols={2} gap="md">
                {personalizedContent.activities.slice(0, 4).map((item, index) => (
                  <BentoCard
                    key={item.item.id}
                    title={item.item.title}
                    description={item.item.description}
                    variant="glassmorphism"
                    interactive
                    onClick={() => navigate(`/activities/${item.item.id}`)}
                    icon={<Star className="w-5 h-5 text-accent" />}
                    action={
                      <UnifiedButton variant="outline" size="sm">
                        View
                      </UnifiedButton>
                    }
                    className="border-accent/20 bg-accent/5 hover:bg-accent/10"
                  >
                    <div className="space-y-1">
                      <div className="text-xs text-accent font-medium">
                        {item.reason}
                      </div>
                      {item.item.location && (
                        <div className="flex items-center gap-1 text-xs text-muted-light">
                          <MapPin className="w-3 h-3" />
                          <span>{item.item.location}</span>
                        </div>
                      )}
                    </div>
                  </BentoCard>
                ))}
              </BentoGrid>
            </motion.div>
          )}

          {/* Your Schedule - What you're signed up for */}
          {dashboardIntelligence?.yourSchedule && dashboardIntelligence.yourSchedule.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.21 }}
              className="mb-8"
            >
              <FlexLayout justify="between" align="center" className="mb-4">
                <div>
                  <h2 className="text-lg md:text-xl font-bold text-primary-light flex items-center gap-2">
                    <Calendar className="w-5 h-5 text-festival-orange" />
                    Your Schedule
                  </h2>
                  <p className="text-sm text-muted-light">Activities you're signed up for today</p>
                </div>
                <Link to="/activities">
                  <UnifiedButton variant="ghost" size="sm">
                    View All
                  </UnifiedButton>
                </Link>
              </FlexLayout>
              <div className="space-y-3">
                {dashboardIntelligence.yourSchedule.slice(0, 3).map((activity, index) => (
                  <BentoCard
                    key={activity.id}
                    variant="glassmorphism"
                    interactive
                    title={activity.title}
                    description={activity.description}
                    className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20"
                    overlayBadge={
                      <UnifiedBadge variant="default" size="sm" overlayMode={true}>
                        Registered
                      </UnifiedBadge>
                    }
                    badgePosition="top-right"
                  >
                    <FlexLayout align="center" gap="md" className="text-xs text-muted-light">
                      <span className="flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        {activity.location}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {activity.start_date ? new Date(activity.start_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'TBD'}
                      </span>
                    </FlexLayout>
                  </BentoCard>
                ))}
              </div>
            </motion.div>
          )}

          {/* Admin-Pinnable Community Updates */}
          {dashboardIntelligence?.liveUpdates && dashboardIntelligence.liveUpdates.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="mb-6"
            >
              <h2 className="text-lg font-semibold text-foreground mb-3 flex items-center gap-2">
                📌 Community Updates
                <UnifiedBadge variant="secondary" size="sm">
                  {dashboardIntelligence.liveUpdates.length}
                </UnifiedBadge>
              </h2>

              <div className="grid gap-3">
                {dashboardIntelligence.liveUpdates.slice(0, 3).map((update, index) => (
                  <BentoCard
                    key={update.id}
                    title={update.title}
                    description={update.message}
                    variant="glassmorphism"
                    interactive
                    onClick={() => update.actionUrl && navigate(update.actionUrl)}
                    className={`${
                      update.urgency === 'high' || update.urgency === 'critical'
                        ? 'bg-gradient-to-r from-destructive/10 to-destructive/5 border-destructive/20'
                        : 'bg-gradient-to-r from-primary/10 to-accent/5 border-primary/20'
                    }`}
                    icon={
                      <span className="text-lg">
                        {update.urgency === 'high' || update.urgency === 'critical' ? '🚨' : '📢'}
                      </span>
                    }
                  >
                    <div className="text-xs text-muted-foreground">
                      {new Date(update.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </div>
                  </BentoCard>
                ))}
              </div>
            </motion.div>
          )}



          {/* User's Upcoming Activities - Personalized Section */}
          {userUpcomingActivities.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.25 }}
              className="mb-8"
            >
              <FlexLayout justify="between" align="center" className="mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-primary-light">Your Upcoming Activities</h2>
                  <p className="text-sm text-secondary-light mt-1">Activities you've signed up for</p>
                </div>
                <Link to="/activities">
                  <UnifiedButton variant="outline" size="sm">
                    Manage All
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </UnifiedButton>
                </Link>
              </FlexLayout>
              <GridLayout cols={2} gap="md">
                {userUpcomingActivities.slice(0, 4).map((activity) => (
                  <BentoCard
                    key={activity.id}
                    title={activity.title}
                    description={activity.description || 'Upcoming activity'}
                    variant="glassmorphism"
                    interactive
                    onClick={() => navigate(`/activities/${activity.id}`)}
                    className="bg-gradient-to-r from-accent/10 to-accent/5 border-accent/20"
                    icon={<Calendar className="w-5 h-5 text-accent" />}
                  >
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        <span>{activity.start_date ? new Date(activity.start_date).toLocaleDateString() : 'TBD'}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        <span>{activity.location || 'Location TBD'}</span>
                      </div>
                    </div>
                  </BentoCard>
                ))}
              </GridLayout>
            </motion.div>
          )}

          {/* Real-time Activity Feed */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.25 }}
            className="mb-8"
          >
            <BentoCard
              title="Community Activity"
              description="See what's happening in the Festival Family community"
              variant="glassmorphism"
              className="bg-gradient-to-br from-primary/10 to-accent/10 border-primary/20"
              icon={<Activity className="w-5 h-5 text-primary" />}
            >
              <ActivityFeed
                limit={8}
                showHeader={false}
                className="mt-4"
              />
            </BentoCard>
          </motion.div>

          {/* Unified Notification System */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mb-8"
          >
            <BentoCard
              title="Community Notifications"
              description="Stay updated with the latest announcements and updates"
              variant="glassmorphism"
              className="bg-gradient-to-br from-accent/10 to-secondary/10 border-accent/20"
              icon={<Bell className="w-5 h-5 text-accent" />}
            >
              <UnifiedNotificationSystem
                displayType="feed"
                limit={5}
                showHeader={false}
                autoRefresh={true}
                className="mt-4"
              />
            </BentoCard>
          </motion.div>

          {/* Today's Activities */}
          {todaysActivities.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mb-8"
            >
              <FlexLayout justify="between" align="center" className="mb-6">
                <h2 className="text-2xl font-bold text-primary-light">Today's Activities</h2>
                <Link to="/activities">
                  <UnifiedButton variant="outline" size="sm">
                    View All
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </UnifiedButton>
                </Link>
              </FlexLayout>
              <GridLayout cols={2} gap="md">
                {todaysActivities.slice(0, 4).map((activity) => (
                  <BentoCard
                    key={activity.id}
                    title={activity.title}
                    description={activity.description || "Today's activity"}
                    variant="glassmorphism"
                    interactive
                    onClick={() => navigate(`/activities/${activity.id}`)}
                    className="bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20"
                    icon={<Clock className="w-5 h-5 text-primary" />}
                  >
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      {activity.start_date && (
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          <span>{formatTime(activity.start_date)}</span>
                        </div>
                      )}
                      {activity.location && (
                        <div className="flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          <span>{activity.location}</span>
                        </div>
                      )}
                      <UnifiedBadge variant="category" className="text-xs">{activity.type}</UnifiedBadge>
                    </div>
                  </BentoCard>
                ))}
              </GridLayout>
            </motion.div>
          )}

          {/* Compact Tips & Guides */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mb-6"
          >
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-sm font-medium text-muted-foreground">Quick Tips</h2>
              <Link to="/famhub?tab=RESOURCES">
                <UnifiedButton variant="ghost" size="sm" className="text-xs">
                  View All
                </UnifiedButton>
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {/* Compact Tips */}
              {featuredTips.slice(0, 2).map((tip, index) => (
                <BentoCard
                  key={tip.id}
                  title={tip.title}
                  description={tip.description || undefined}
                  variant="glassmorphism"
                  interactive
                  onClick={() => navigate(`/famhub?tab=RESOURCES&type=tips&id=${tip.id}`)}
                  className="bg-gradient-to-r from-secondary/10 to-secondary/5 border-secondary/20"
                  icon={<span className="text-sm">💡</span>}
                  action={
                    <UnifiedButton
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/famhub?tab=RESOURCES&type=tips&id=${tip.id}`);
                      }}
                      size="sm"
                      variant="outline"
                      className="text-xs min-h-[44px] px-4 touch-manipulation"
                    >
                      Read Tip
                    </UnifiedButton>
                  }
                >
                  <div className="flex items-center gap-3 text-xs text-muted-foreground">
                    {tip.helpful_count && (
                      <div className="flex items-center gap-1">
                        <Heart className="w-3 h-3" />
                        <span>{tip.helpful_count}</span>
                      </div>
                    )}
                    {tip.view_count && (
                      <div className="flex items-center gap-1">
                        <Eye className="w-3 h-3" />
                        <span>{tip.view_count}</span>
                      </div>
                    )}
                  </div>
                </BentoCard>
              ))}

              {/* Compact Guides */}
              {featuredGuides.slice(0, 2).map((guide, index) => (
                <BentoCard
                  key={guide.id}
                  title={guide.title}
                  description={guide.description || undefined}
                  variant="glassmorphism"
                  interactive
                  onClick={() => navigate(`/famhub?tab=RESOURCES&type=guides&id=${guide.id}`)}
                  className="bg-gradient-to-r from-muted/10 to-muted/5 border-muted/20"
                  icon={<span className="text-sm">📖</span>}
                  action={
                    <UnifiedButton
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/famhub?tab=RESOURCES&type=guides&id=${guide.id}`);
                      }}
                      size="sm"
                      variant="outline"
                      className="text-xs min-h-[44px] px-4 touch-manipulation"
                    >
                      Read Guide
                    </UnifiedButton>
                  }
                >
                  <div className="flex items-center gap-3 text-xs text-muted-foreground">
                    {guide.view_count && (
                      <div className="flex items-center gap-1">
                        <Eye className="w-3 h-3" />
                        <span>{guide.view_count}</span>
                      </div>
                    )}
                  </div>
                </BentoCard>
              ))}
            </div>
          </motion.div>


        </div>

        {/* Development-only Mobile UX Testing Tool */}
        <MobileUXTester />
      </OneHandedOptimizer>
    </>
  );
};

export default AuthenticatedHome;

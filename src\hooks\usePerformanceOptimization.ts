import { useCallback, useEffect, useRef, useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { unifiedCacheService, CacheKeys } from '@/lib/cache/unified-cache-service';
import { CACHE_DURATIONS } from '@/lib/data/constants';

interface PerformanceMetrics {
  loadTime: number;
  cacheHitRate: number;
  queryCount: number;
  errorRate: number;
}

interface UsePerformanceOptimizationOptions {
  enableCaching?: boolean;
  enablePrefetching?: boolean;
  enableMetrics?: boolean;
  cacheStrategy?: 'memory' | 'persistent' | 'hybrid';
}

interface UsePerformanceOptimizationReturn {
  metrics: PerformanceMetrics;
  optimizedFetch: <T>(
    key: string,
    fetchFn: () => Promise<T>,
    options?: {
      ttl?: number;
      priority?: 'low' | 'medium' | 'high';
      persist?: boolean;
    }
  ) => Promise<T | null>;
  prefetch: <T>(key: string, fetchFn: () => Promise<T>) => Promise<void>;
  invalidateCache: (pattern: string) => Promise<void>;
  getCacheStats: () => any;
}

export const usePerformanceOptimization = (
  options: UsePerformanceOptimizationOptions = {}
): UsePerformanceOptimizationReturn => {
  const {
    enableCaching = true,
    enablePrefetching = true,
    enableMetrics = true,
    cacheStrategy = 'hybrid'
  } = options;

  const queryClient = useQueryClient();
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    cacheHitRate: 0,
    queryCount: 0,
    errorRate: 0
  });

  const metricsRef = useRef({
    startTime: Date.now(),
    queryCount: 0,
    errorCount: 0,
    cacheHits: 0,
    cacheMisses: 0
  });

  // Update metrics periodically
  useEffect(() => {
    if (!enableMetrics) return;

    const interval = setInterval(() => {
      const current = metricsRef.current;
      const totalQueries = current.queryCount;
      const totalCacheRequests = current.cacheHits + current.cacheMisses;

      setMetrics({
        loadTime: Date.now() - current.startTime,
        cacheHitRate: totalCacheRequests > 0 ? (current.cacheHits / totalCacheRequests) * 100 : 0,
        queryCount: totalQueries,
        errorRate: totalQueries > 0 ? (current.errorCount / totalQueries) * 100 : 0
      });
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [enableMetrics]);

  // Optimized fetch function with caching
  const optimizedFetch = useCallback(async <T>(
    key: string,
    fetchFn: () => Promise<T>,
    options: {
      ttl?: number;
      priority?: 'low' | 'medium' | 'high';
      persist?: boolean;
    } = {}
  ): Promise<T | null> => {
    const startTime = performance.now();
    metricsRef.current.queryCount++;

    try {
      if (!enableCaching) {
        const result = await fetchFn();
        return result;
      }

      // Determine cache options based on strategy
      const cacheOptions = {
        ttl: options.ttl || CACHE_DURATIONS.MEDIUM,
        priority: options.priority || 'medium',
        persist: cacheStrategy === 'persistent' || (cacheStrategy === 'hybrid' && options.persist)
      };

      // Try to get from cache first
      const cachedResult = await unifiedCacheService.get<T>(key, fetchFn, cacheOptions);
      
      if (cachedResult !== null) {
        metricsRef.current.cacheHits++;
        console.log(`⚡ Cache HIT: ${key} (${performance.now() - startTime}ms)`);
        return cachedResult;
      } else {
        metricsRef.current.cacheMisses++;
        console.log(`💾 Cache MISS: ${key} (${performance.now() - startTime}ms)`);
        return null;
      }
    } catch (error) {
      metricsRef.current.errorCount++;
      console.error('Optimized fetch error:', error);
      return null;
    }
  }, [enableCaching, cacheStrategy]);

  // Prefetch function for proactive loading
  const prefetch = useCallback(async <T>(
    key: string,
    fetchFn: () => Promise<T>
  ): Promise<void> => {
    if (!enablePrefetching) return;

    try {
      await unifiedCacheService.prefetch(key, fetchFn, {
        ttl: CACHE_DURATIONS.LONG,
        priority: 'low',
        persist: cacheStrategy === 'persistent' || cacheStrategy === 'hybrid'
      });
    } catch (error) {
      console.error('Prefetch error:', error);
    }
  }, [enablePrefetching, cacheStrategy]);

  // Cache invalidation
  const invalidateCache = useCallback(async (pattern: string): Promise<void> => {
    try {
      await unifiedCacheService.invalidatePattern(pattern);
      
      // Also invalidate React Query cache
      queryClient.invalidateQueries({
        predicate: (query) => {
          const regex = new RegExp(pattern);
          return regex.test(query.queryKey.join(':'));
        }
      });
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }, [queryClient]);

  // Get cache statistics
  const getCacheStats = useCallback(() => {
    return unifiedCacheService.getStats();
  }, []);

  return {
    metrics,
    optimizedFetch,
    prefetch,
    invalidateCache,
    getCacheStats
  };
};

// Hook for optimized data fetching with React Query integration
export const useOptimizedQuery = <T>(
  queryKey: string[],
  fetchFn: () => Promise<T>,
  options: {
    enabled?: boolean;
    staleTime?: number;
    cacheTime?: number;
    refetchOnWindowFocus?: boolean;
    retry?: number;
    enableCaching?: boolean;
    cacheTtl?: number;
  } = {}
) => {
  const { optimizedFetch } = usePerformanceOptimization({
    enableCaching: options.enableCaching !== false
  });

  const cacheKey = queryKey.join(':');

  return useQuery({
    queryKey,
    queryFn: async () => {
      // Try optimized cache first
      const cachedResult = await optimizedFetch(
        cacheKey,
        fetchFn,
        {
          ttl: options.cacheTtl || CACHE_DURATIONS.MEDIUM,
          priority: 'medium',
          persist: true
        }
      );

      // If cache miss, fetch directly
      if (cachedResult === null) {
        return await fetchFn();
      }

      return cachedResult;
    },
    enabled: options.enabled !== false,
    staleTime: options.staleTime || CACHE_DURATIONS.SHORT,
    gcTime: options.cacheTime || CACHE_DURATIONS.LONG,
    refetchOnWindowFocus: options.refetchOnWindowFocus || false,
    retry: options.retry || 1
  });
};

// Hook for batch operations optimization
export const useBatchOptimization = () => {
  const batchQueue = useRef<Array<() => Promise<any>>>([]);
  const batchTimeout = useRef<NodeJS.Timeout | null>(null);

  const addToBatch = useCallback((operation: () => Promise<any>) => {
    batchQueue.current.push(operation);

    // Clear existing timeout
    if (batchTimeout.current) {
      clearTimeout(batchTimeout.current);
    }

    // Set new timeout to execute batch
    batchTimeout.current = setTimeout(async () => {
      const operations = [...batchQueue.current];
      batchQueue.current = [];

      try {
        // Execute all operations in parallel
        await Promise.all(operations.map(op => op()));
        console.log(`🚀 Batch executed: ${operations.length} operations`);
      } catch (error) {
        console.error('Batch execution error:', error);
      }
    }, 100); // 100ms batch window
  }, []);

  return { addToBatch };
};

// Performance monitoring hook
export const usePerformanceMonitoring = () => {
  const [performanceData, setPerformanceData] = useState({
    navigationTiming: null as PerformanceNavigationTiming | null,
    resourceTiming: [] as PerformanceResourceTiming[],
    paintTiming: [] as PerformanceEntry[],
    longTasks: [] as PerformanceEntry[]
  });

  useEffect(() => {
    // Collect performance data
    const collectPerformanceData = () => {
      if (typeof window !== 'undefined' && 'performance' in window) {
        setPerformanceData({
          navigationTiming: performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming,
          resourceTiming: performance.getEntriesByType('resource') as PerformanceResourceTiming[],
          paintTiming: performance.getEntriesByType('paint'),
          longTasks: performance.getEntriesByType('longtask')
        });
      }
    };

    // Collect initial data
    collectPerformanceData();

    // Collect data periodically
    const interval = setInterval(collectPerformanceData, 30000); // Every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const getPerformanceScore = useCallback(() => {
    const { navigationTiming, longTasks } = performanceData;
    
    if (!navigationTiming) return 0;

    const loadTime = navigationTiming.loadEventEnd - navigationTiming.fetchStart;
    const longTaskCount = longTasks.length;

    // Simple scoring algorithm
    let score = 100;
    
    // Penalize slow load times
    if (loadTime > 3000) score -= 30;
    else if (loadTime > 2000) score -= 15;
    else if (loadTime > 1000) score -= 5;

    // Penalize long tasks
    score -= longTaskCount * 10;

    return Math.max(0, Math.min(100, score));
  }, [performanceData]);

  return {
    performanceData,
    getPerformanceScore
  };
};

export default usePerformanceOptimization;

{"timestamp": "2025-05-31T23:24:03.195Z", "serverStartup": {"timeout": true, "output": "\n> festival-family@0.1.0 dev\n> vite --port 5173 --strictPort\n\nERROR: (!) the `splitVendorChunk` plugin doesn't have any effect when using the object form of `build.rollupOptions.output.manualChunks`. Consider using the function form instead.\n\n  \u001b[32m\u001b[1mVITE\u001b[22m v6.3.3\u001b[39m  \u001b[2mready in \u001b[0m\u001b[1m1463\u001b[22m\u001b[2m\u001b[0m ms\u001b[22m\n\n  \u001b[32m➜\u001b[39m  \u001b[1mLocal\u001b[22m:   \u001b[36mhttp://localhost:\u001b[1m5173\u001b[22m/\u001b[39m\n"}, "browserTests": {"skipped": true, "reason": "Server not ready"}, "adminTests": {"skipped": true}, "errors": [{"type": "server_startup", "message": "(!) the `splitVendorChunk` plugin doesn't have any effect when using the object form of `build.rollupOptions.output.manualChunks`. Consider using the function form instead.\n", "timestamp": "2025-05-31T23:24:05.869Z"}], "screenshots": [], "consoleMessages": [], "networkRequests": []}
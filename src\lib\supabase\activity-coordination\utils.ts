/**
 * Smart Group Formation Utilities
 * 
 * Utility functions for validating, formatting, and processing Smart Group Formation data.
 * Provides helper methods for group suggestions, formation types, and validation.
 * 
 * @module SmartGroupFormationUtils
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import type { 
  GroupSuggestion, 
  GroupFormationType, 
  SuggestionStatus,
  SmartGroupFormationRequest,
  GroupFormationInsights
} from './types'

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate group formation request
 */
export function validateGroupFormationRequest(request: SmartGroupFormationRequest): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  // Required fields
  if (!request.creator_id) {
    errors.push('Creator ID is required')
  }

  if (!request.festival_id) {
    errors.push('Festival ID is required')
  }

  if (!request.formation_type) {
    errors.push('Formation type is required')
  }

  // Formation type specific validation
  if (request.formation_type === 'activity_based' && (!request.activity_focus || request.activity_focus.length === 0)) {
    errors.push('Activity focus is required for activity-based groups')
  }

  if (request.formation_type === 'music_based' && (!request.music_focus || request.music_focus.length === 0)) {
    errors.push('Music focus is required for music-based groups')
  }

  if (request.formation_type === 'hybrid') {
    if (!request.activity_focus || request.activity_focus.length === 0) {
      errors.push('Activity focus is required for hybrid groups')
    }
    if (!request.music_focus || request.music_focus.length === 0) {
      errors.push('Music focus is required for hybrid groups')
    }
  }

  // Member count validation
  if (request.min_members && request.min_members < 2) {
    errors.push('Minimum members must be at least 2')
  }

  if (request.max_members && request.max_members > 50) {
    errors.push('Maximum members cannot exceed 50')
  }

  if (request.min_members && request.max_members && request.min_members > request.max_members) {
    errors.push('Minimum members cannot be greater than maximum members')
  }

  // Confidence threshold validation
  if (request.confidence_threshold && (request.confidence_threshold < 0 || request.confidence_threshold > 1)) {
    errors.push('Confidence threshold must be between 0 and 1')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validate group suggestion data
 */
export function validateGroupSuggestion(suggestion: Partial<GroupSuggestion>): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  if (!suggestion.suggested_name || suggestion.suggested_name.trim().length === 0) {
    errors.push('Group name is required')
  }

  if (suggestion.suggested_name && suggestion.suggested_name.length > 100) {
    errors.push('Group name cannot exceed 100 characters')
  }

  if (suggestion.suggested_description && suggestion.suggested_description.length > 500) {
    errors.push('Group description cannot exceed 500 characters')
  }

  if (!suggestion.formation_type) {
    errors.push('Formation type is required')
  }

  if (!suggestion.target_users || suggestion.target_users.length === 0) {
    errors.push('Target users are required')
  }

  if (suggestion.target_users && suggestion.target_users.length > 50) {
    errors.push('Cannot target more than 50 users')
  }

  if (!suggestion.creator_id) {
    errors.push('Creator ID is required')
  }

  if (suggestion.min_members && suggestion.min_members < 2) {
    errors.push('Minimum members must be at least 2')
  }

  if (suggestion.max_members && suggestion.max_members > 50) {
    errors.push('Maximum members cannot exceed 50')
  }

  if (suggestion.confidence_score && (suggestion.confidence_score < 0 || suggestion.confidence_score > 1)) {
    errors.push('Confidence score must be between 0 and 1')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// ============================================================================
// FORMATTING FUNCTIONS
// ============================================================================

/**
 * Format group formation type for display
 */
export function formatFormationType(type: GroupFormationType): string {
  const typeLabels: Record<GroupFormationType, string> = {
    manual: 'Manual',
    activity_based: 'Activity-Based',
    music_based: 'Music-Based',
    hybrid: 'Hybrid',
    spontaneous: 'Spontaneous'
  }

  return typeLabels[type] || type
}

/**
 * Format suggestion status for display
 */
export function formatSuggestionStatus(status: SuggestionStatus): string {
  const statusLabels: Record<SuggestionStatus, string> = {
    pending: 'Pending',
    accepted: 'Accepted',
    declined: 'Declined',
    expired: 'Expired'
  }

  return statusLabels[status] || status
}

/**
 * Format confidence score as percentage
 */
export function formatConfidenceScore(score: number): string {
  return `${Math.round(score * 100)}%`
}

/**
 * Format group formation insights for display
 */
export function formatFormationInsights(insights: GroupFormationInsights): {
  activitySummary: string
  musicSummary: string
  potentialSummary: string
  statusSummary: string
} {
  const activityCount = insights.activity_interests.length
  const musicCount = insights.music_interests.length
  const potential = Math.round(insights.formation_potential * 100)

  return {
    activitySummary: activityCount === 0 
      ? 'No activity interests set' 
      : `${activityCount} activity interest${activityCount === 1 ? '' : 's'}`,
    musicSummary: musicCount === 0 
      ? 'No music preferences set' 
      : `${musicCount} music preference${musicCount === 1 ? '' : 's'}`,
    potentialSummary: `${potential}% formation potential`,
    statusSummary: insights.pending_suggestions === 0 
      ? 'No pending suggestions' 
      : `${insights.pending_suggestions} pending suggestion${insights.pending_suggestions === 1 ? '' : 's'}`
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Check if a group suggestion is expired
 */
export function isGroupSuggestionExpired(suggestion: GroupSuggestion): boolean {
  return new Date(suggestion.expires_at) < new Date()
}

/**
 * Calculate time until suggestion expires
 */
export function getTimeUntilExpiry(suggestion: GroupSuggestion): {
  hours: number
  minutes: number
  isExpired: boolean
} {
  const now = new Date()
  const expiryDate = new Date(suggestion.expires_at)
  const timeDiff = expiryDate.getTime() - now.getTime()

  if (timeDiff <= 0) {
    return { hours: 0, minutes: 0, isExpired: true }
  }

  const hours = Math.floor(timeDiff / (1000 * 60 * 60))
  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))

  return { hours, minutes, isExpired: false }
}

/**
 * Generate default group name based on formation type and focus
 */
export function generateDefaultGroupName(
  formationType: GroupFormationType,
  activityFocus?: string[],
  musicFocus?: string[]
): string {
  switch (formationType) {
    case 'activity_based':
      return activityFocus && activityFocus.length > 0 
        ? `${activityFocus[0]} Group`
        : 'Activity Group'
    
    case 'music_based':
      return musicFocus && musicFocus.length > 0 
        ? `${musicFocus[0]} Fans`
        : 'Music Group'
    
    case 'hybrid':
      const activity = activityFocus?.[0] || 'Activity'
      const music = musicFocus?.[0] || 'Music'
      return `${activity} & ${music} Group`
    
    case 'spontaneous':
      return 'Spontaneous Meetup'
    
    case 'manual':
    default:
      return 'Festival Group'
  }
}

/**
 * Generate default group description based on formation type and focus
 */
export function generateDefaultGroupDescription(
  formationType: GroupFormationType,
  activityFocus?: string[],
  musicFocus?: string[]
): string {
  switch (formationType) {
    case 'activity_based':
      return activityFocus && activityFocus.length > 0 
        ? `Connect with others interested in ${activityFocus.join(', ')}`
        : 'Connect with others who share your activity interests'
    
    case 'music_based':
      return musicFocus && musicFocus.length > 0 
        ? `Connect with fellow ${musicFocus.join(', ')} enthusiasts`
        : 'Connect with others who share your music taste'
    
    case 'hybrid':
      const activities = activityFocus?.join(', ') || 'activities'
      const music = musicFocus?.join(', ') || 'music'
      return `Connect with others who love ${activities} and ${music}`
    
    case 'spontaneous':
      return 'Join for real-time coordination and spontaneous meetups'
    
    case 'manual':
    default:
      return 'Connect with fellow festival-goers'
  }
}

/**
 * Calculate formation potential score based on user data
 */
export function calculateFormationPotential(
  activityCount: number,
  musicPreferenceCount: number,
  existingGroupCount: number = 0
): number {
  // Base score from user engagement
  const activityScore = Math.min(activityCount / 10, 1) * 0.4 // Max 40% from activities
  const musicScore = Math.min(musicPreferenceCount / 20, 1) * 0.4 // Max 40% from music
  
  // Bonus for being active but not over-committed
  const engagementBonus = (activityCount > 0 && musicPreferenceCount > 0) ? 0.2 : 0
  
  // Slight penalty for being in too many groups already
  const groupPenalty = Math.min(existingGroupCount * 0.05, 0.2)
  
  const score = activityScore + musicScore + engagementBonus - groupPenalty
  
  return Math.max(0, Math.min(1, score))
}

/**
 * Filter suggestions by status and expiry
 */
export function filterActiveSuggestions(suggestions: GroupSuggestion[]): GroupSuggestion[] {
  return suggestions.filter(suggestion => 
    suggestion.status === 'pending' && !isGroupSuggestionExpired(suggestion)
  )
}

/**
 * Sort suggestions by priority (confidence score and expiry time)
 */
export function sortSuggestionsByPriority(suggestions: GroupSuggestion[]): GroupSuggestion[] {
  return [...suggestions].sort((a, b) => {
    // First, prioritize non-expired suggestions
    const aExpired = isGroupSuggestionExpired(a)
    const bExpired = isGroupSuggestionExpired(b)
    
    if (aExpired !== bExpired) {
      return aExpired ? 1 : -1
    }
    
    // Then sort by confidence score (higher first)
    if (a.confidence_score !== b.confidence_score) {
      return b.confidence_score - a.confidence_score
    }
    
    // Finally, sort by expiry time (sooner expiry first)
    return new Date(a.expires_at).getTime() - new Date(b.expires_at).getTime()
  })
}

// ============================================================================
// CONSTANTS
// ============================================================================

export const GROUP_FORMATION_LIMITS = {
  MIN_MEMBERS: 2,
  MAX_MEMBERS: 50,
  MAX_NAME_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 500,
  MAX_TARGET_USERS: 50,
  DEFAULT_EXPIRY_HOURS: 24,
  MIN_CONFIDENCE_THRESHOLD: 0.3,
  DEFAULT_CONFIDENCE_THRESHOLD: 0.6
} as const

export const FORMATION_TYPE_ICONS = {
  manual: 'Users',
  activity_based: 'Calendar',
  music_based: 'Music',
  hybrid: 'Sparkles',
  spontaneous: 'Target'
} as const

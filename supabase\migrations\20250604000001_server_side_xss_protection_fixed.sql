-- Server-Side XSS Protection Implementation (FIXED VERSION)
-- This migration adds database-level input sanitization to prevent XSS attacks
-- Fixed: Proper handling of NEW/OLD in trigger functions

-- Create XSS protection function
CREATE OR REPLACE FUNCTION sanitize_xss_input(input_text TEXT)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
  sanitized TEXT;
BEGIN
  -- Return empty string if input is null
  IF input_text IS NULL THEN
    RETURN '';
  END IF;
  
  sanitized := input_text;
  
  -- Remove script tags
  sanitized := regexp_replace(sanitized, '<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>', '', 'gi');
  
  -- Remove javascript: protocols
  sanitized := regexp_replace(sanitized, 'javascript:', '', 'gi');
  
  -- Remove on* event handlers
  sanitized := regexp_replace(sanitized, 'on\w+\s*=', '', 'gi');
  
  -- Remove iframe tags
  sanitized := regexp_replace(sanitized, '<iframe\b[^>]*>', '', 'gi');
  
  -- Remove object tags
  sanitized := regexp_replace(sanitized, '<object\b[^>]*>', '', 'gi');
  
  -- Remove embed tags
  sanitized := regexp_replace(sanitized, '<embed\b[^>]*>', '', 'gi');
  
  -- Remove link tags
  sanitized := regexp_replace(sanitized, '<link\b[^>]*>', '', 'gi');
  
  -- Remove meta tags
  sanitized := regexp_replace(sanitized, '<meta\b[^>]*>', '', 'gi');
  
  -- Remove style tags
  sanitized := regexp_replace(sanitized, '<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>', '', 'gi');
  
  -- Remove expression() CSS
  sanitized := regexp_replace(sanitized, 'expression\s*\(', '', 'gi');
  
  -- Remove vbscript: protocols
  sanitized := regexp_replace(sanitized, 'vbscript:', '', 'gi');
  
  -- Remove data:text/html
  sanitized := regexp_replace(sanitized, 'data:text\/html', '', 'gi');
  
  -- Encode HTML entities
  sanitized := replace(sanitized, '&', '&amp;');
  sanitized := replace(sanitized, '<', '&lt;');
  sanitized := replace(sanitized, '>', '&gt;');
  sanitized := replace(sanitized, '"', '&quot;');
  sanitized := replace(sanitized, '''', '&#x27;');
  sanitized := replace(sanitized, '/', '&#x2F;');
  sanitized := replace(sanitized, '`', '&#x60;');
  sanitized := replace(sanitized, '=', '&#x3D;');
  
  -- Remove null bytes and control characters
  sanitized := regexp_replace(sanitized, '[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', 'g');
  
  -- Trim whitespace
  sanitized := trim(sanitized);
  
  RETURN sanitized;
END;
$$;

-- Create trigger function for profiles table (FIXED)
CREATE OR REPLACE FUNCTION sanitize_profile_input()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
  sanitization_applied BOOLEAN := FALSE;
BEGIN
  -- Sanitize text fields in profiles
  IF NEW.full_name IS NOT NULL THEN
    NEW.full_name := left(sanitize_xss_input(NEW.full_name), 100);
  END IF;
  
  IF NEW.username IS NOT NULL THEN
    NEW.username := left(sanitize_xss_input(NEW.username), 50);
  END IF;
  
  IF NEW.bio IS NOT NULL THEN
    NEW.bio := left(sanitize_xss_input(NEW.bio), 500);
  END IF;
  
  IF NEW.location IS NOT NULL THEN
    NEW.location := left(sanitize_xss_input(NEW.location), 100);
  END IF;
  
  -- Check if any sanitization occurred (FIXED: Handle INSERT vs UPDATE)
  IF TG_OP = 'INSERT' THEN
    RAISE NOTICE 'XSS sanitization applied to new profile for user %', NEW.id;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Only compare with OLD for UPDATE operations
    IF (NEW.full_name IS DISTINCT FROM OLD.full_name) OR 
       (NEW.username IS DISTINCT FROM OLD.username) OR 
       (NEW.bio IS DISTINCT FROM OLD.bio) OR 
       (NEW.location IS DISTINCT FROM OLD.location) THEN
      RAISE NOTICE 'XSS sanitization applied to profile update for user %', NEW.id;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create trigger function for announcements table (FIXED)
CREATE OR REPLACE FUNCTION sanitize_announcement_input()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  -- Sanitize text fields in announcements
  IF NEW.title IS NOT NULL THEN
    NEW.title := left(sanitize_xss_input(NEW.title), 200);
  END IF;
  
  IF NEW.content IS NOT NULL THEN
    NEW.content := left(sanitize_xss_input(NEW.content), 2000);
  END IF;
  
  -- Log if any sanitization occurred (FIXED: Handle INSERT vs UPDATE)
  IF TG_OP = 'INSERT' THEN
    RAISE NOTICE 'XSS sanitization applied to new announcement ID %', NEW.id;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Only compare with OLD for UPDATE operations
    IF (NEW.title IS DISTINCT FROM OLD.title) OR (NEW.content IS DISTINCT FROM OLD.content) THEN
      RAISE NOTICE 'XSS sanitization applied to announcement update for ID %', NEW.id;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create trigger function for activities table (FIXED)
CREATE OR REPLACE FUNCTION sanitize_activity_input()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  -- Sanitize text fields in activities
  IF NEW.title IS NOT NULL THEN
    NEW.title := left(sanitize_xss_input(NEW.title), 200);
  END IF;
  
  IF NEW.description IS NOT NULL THEN
    NEW.description := left(sanitize_xss_input(NEW.description), 1000);
  END IF;
  
  -- Log if any sanitization occurred (FIXED: Handle INSERT vs UPDATE)
  IF TG_OP = 'INSERT' THEN
    RAISE NOTICE 'XSS sanitization applied to new activity ID %', NEW.id;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Only compare with OLD for UPDATE operations
    IF (NEW.title IS DISTINCT FROM OLD.title) OR (NEW.description IS DISTINCT FROM OLD.description) THEN
      RAISE NOTICE 'XSS sanitization applied to activity update for ID %', NEW.id;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$;

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS sanitize_profile_trigger ON profiles;
DROP TRIGGER IF EXISTS sanitize_announcement_trigger ON announcements;
DROP TRIGGER IF EXISTS sanitize_activity_trigger ON activities;

-- Create triggers for automatic sanitization
CREATE TRIGGER sanitize_profile_trigger
  BEFORE INSERT OR UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION sanitize_profile_input();

CREATE TRIGGER sanitize_announcement_trigger
  BEFORE INSERT OR UPDATE ON announcements
  FOR EACH ROW
  EXECUTE FUNCTION sanitize_announcement_input();

CREATE TRIGGER sanitize_activity_trigger
  BEFORE INSERT OR UPDATE ON activities
  FOR EACH ROW
  EXECUTE FUNCTION sanitize_activity_input();

-- Create a function to test XSS protection
CREATE OR REPLACE FUNCTION test_xss_protection(test_input TEXT)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN sanitize_xss_input(test_input);
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION sanitize_xss_input TO authenticated;
GRANT EXECUTE ON FUNCTION test_xss_protection TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION sanitize_xss_input IS 'Server-side XSS protection function that sanitizes user input';
COMMENT ON FUNCTION sanitize_profile_input IS 'Trigger function to sanitize profile data before database storage';
COMMENT ON FUNCTION sanitize_announcement_input IS 'Trigger function to sanitize announcement data before database storage';
COMMENT ON FUNCTION sanitize_activity_input IS 'Trigger function to sanitize activity data before database storage';
COMMENT ON FUNCTION test_xss_protection IS 'Test function to verify XSS protection is working correctly';

-- Log successful migration
DO $$
BEGIN
  RAISE NOTICE 'SUCCESS: Server-side XSS protection implemented successfully (FIXED VERSION)';
  RAISE NOTICE 'INFO: Database triggers created for profiles, announcements, and activities tables';
  RAISE NOTICE 'INFO: Use test_xss_protection() function to test sanitization';
  RAISE NOTICE 'INFO: Fixed NEW/OLD comparison issues in trigger functions';
END $$;

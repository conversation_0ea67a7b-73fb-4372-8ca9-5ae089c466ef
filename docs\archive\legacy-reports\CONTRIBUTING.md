# Contributing to Festival Family

Thank you for your interest in contributing to Festival Family! This document provides guidelines and instructions for contributing to the project.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Coding Standards](#coding-standards)
- [Commit Guidelines](#commit-guidelines)
- [Pull Request Process](#pull-request-process)
- [Testing](#testing)
- [Documentation](#documentation)

## Code of Conduct

Please read and follow our [Code of Conduct](CODE_OF_CONDUCT.md) to foster an inclusive and respectful community.

## Getting Started

### Prerequisites

- Node.js 20+
- PostgreSQL 15+
- Git 2.40+

### Setup

1. Fork the repository
2. Clone your fork:
   ```bash
   git clone https://github.com/YOUR_USERNAME/festival-family.git
   cd festival-family
   ```
3. Install dependencies:
   ```bash
   npm install
   ```
4. Set up environment variables:
   ```bash
   cp .env.example .env
   ```
5. Start the development server:
   ```bash
   npm run dev
   ```

## Development Workflow

1. Create a new branch for your feature or bugfix:
   ```bash
   git checkout -b feature/your-feature-name
   ```
   or
   ```bash
   git checkout -b fix/issue-you-are-fixing
   ```

2. Make your changes and commit them using the conventional commit format:
   ```bash
   npm run commit
   ```

3. Push your branch to your fork:
   ```bash
   git push origin feature/your-feature-name
   ```

4. Create a pull request from your fork to the main repository.

## Coding Standards

We use ESLint and Prettier to enforce coding standards. You can run the linter and formatter with:

```bash
npm run lint
npm run format
```

These checks are automatically run on pre-commit and pre-push hooks.

### TypeScript

- Use TypeScript for all new code
- Avoid using `any` type when possible
- Use type-only imports for types
- Follow the existing patterns in the codebase

### React

- Use functional components with hooks
- Use React Query for data fetching
- Follow the component structure in the codebase
- Use shadcn/ui components when possible

### CSS

- Use Tailwind CSS for styling
- Follow the existing design system
- Use the utility classes provided by Tailwind

## Commit Guidelines

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification for commit messages. This allows us to automatically generate changelogs and version numbers.

### Commit Message Format

```
type(scope): subject

body

footer
```

Where:
- **type**: The type of change (e.g., feat, fix, docs, style, refactor, perf, test, build, ci, chore, revert)
- **scope** (optional): The scope of the change (e.g., component name, file name)
- **subject**: A short description of the change
- **body** (optional): A more detailed description of the change
- **footer** (optional): Information about breaking changes or references to issues

### Using Commitizen

To make it easier to write conventional commits, you can use Commitizen by running:

```bash
npm run commit
```

This will guide you through creating a properly formatted commit message.

## Pull Request Process

1. Ensure your code passes all tests and linting checks
2. Update documentation if necessary
3. Add tests for new features
4. Make sure your branch is up to date with the main branch
5. Fill out the pull request template
6. Request a review from a maintainer
7. Address any feedback from the review

## Testing

We use Jest for testing. You can run the tests with:

```bash
npm run test
```

### Writing Tests

- Write unit tests for all new code
- Aim for at least 80% test coverage
- Use the testing utilities provided in the codebase
- Follow the existing test patterns

## Documentation

- Update documentation for any changes to the API or functionality
- Document new components and hooks
- Add JSDoc comments to functions and components
- Keep the README and other documentation up to date

Thank you for contributing to Festival Family!

/**
 * Festival Family - Authoritative Database Types
 *
 * This file contains the SINGLE SOURCE OF TRUTH for all database types.
 * These types are generated from the actual Supabase database schema.
 *
 * DO NOT modify these types manually - they should be regenerated from the database.
 * For application-specific types, create them in separate files and import these as needed.
 *
 * @version 3.0.0
 * @generated 2025-01-30 - Updated to match actual database schema
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      activities: {
        Row: {
          capacity: number | null
          created_at: string | null
          created_by: string | null
          description: string
          end_date: string | null
          festival_id: string | null
          id: string
          image_url: string | null
          is_featured: boolean | null
          location: string
          metadata: Json | null
          parent_activity_id: string | null
          start_date: string | null
          status: string | null
          tags: string[] | null
          title: string
          type: "workshop" | "meetup" | "performance" | "game" | "social" | "food" | "other"
          updated_at: string | null
        }
        Insert: {
          capacity?: number | null
          created_at?: string | null
          created_by?: string | null
          description: string
          end_date?: string | null
          festival_id?: string | null
          id?: string
          image_url?: string | null
          is_featured?: boolean | null
          location: string
          metadata?: Json | null
          parent_activity_id?: string | null
          start_date?: string | null
          status?: string | null
          tags?: string[] | null
          title: string
          type: "workshop" | "meetup" | "performance" | "game" | "social" | "food" | "other"
          updated_at?: string | null
        }
        Update: {
          capacity?: number | null
          created_at?: string | null
          created_by?: string | null
          description?: string
          end_date?: string | null
          festival_id?: string | null
          id?: string
          image_url?: string | null
          is_featured?: boolean | null
          location?: string
          metadata?: Json | null
          parent_activity_id?: string | null
          start_date?: string | null
          status?: string | null
          tags?: string[] | null
          title?: string
          type?: "workshop" | "meetup" | "performance" | "game" | "social" | "food" | "other"
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "activities_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activities_festival_id_fkey"
            columns: ["festival_id"]
            isOneToOne: false
            referencedRelation: "festivals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activities_parent_activity_id_fkey"
            columns: ["parent_activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          }
        ]
      }
      announcements: {
        Row: {
          active: boolean | null
          category_id: string | null
          content: string
          created_at: string | null
          created_by: string | null
          display_type: string | null
          end_date: string | null
          expires_at: string | null
          id: string
          is_featured: boolean | null
          notification_sent: boolean | null
          priority: string | null
          scheduled_for: string | null
          start_date: string | null
          status: string | null
          tags: string[] | null
          target_audience: string[] | null
          title: string
          updated_at: string | null
          view_count: number | null
        }
        Insert: {
          active?: boolean | null
          category_id?: string | null
          content: string
          created_at?: string | null
          created_by?: string | null
          display_type?: string | null
          end_date?: string | null
          expires_at?: string | null
          id?: string
          is_featured?: boolean | null
          notification_sent?: boolean | null
          priority?: string | null
          scheduled_for?: string | null
          start_date?: string | null
          status?: string | null
          tags?: string[] | null
          target_audience?: string[] | null
          title: string
          updated_at?: string | null
          view_count?: number | null
        }
        Update: {
          active?: boolean | null
          category_id?: string | null
          content?: string
          created_at?: string | null
          created_by?: string | null
          display_type?: string | null
          end_date?: string | null
          expires_at?: string | null
          id?: string
          is_featured?: boolean | null
          notification_sent?: boolean | null
          priority?: string | null
          scheduled_for?: string | null
          start_date?: string | null
          status?: string | null
          tags?: string[] | null
          target_audience?: string[] | null
          title?: string
          updated_at?: string | null
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "announcements_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      events: {
        Row: {
          capacity: number | null
          created_at: string | null
          created_by: string | null
          description: string
          end_date: string
          festival_id: string | null
          id: string
          image_url: string | null
          is_active: boolean | null
          location: string
          registration_required: boolean | null
          start_date: string
          status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | null
          title: string
          updated_at: string | null
        }
        Insert: {
          capacity?: number | null
          created_at?: string | null
          created_by?: string | null
          description: string
          end_date: string
          festival_id?: string | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          location: string
          registration_required?: boolean | null
          start_date: string
          status?: "DRAFT" | "PUBLISHED" | "ARCHIVED" | null
          title: string
          updated_at?: string | null
        }
        Update: {
          capacity?: number | null
          created_at?: string | null
          created_by?: string | null
          description?: string
          end_date?: string
          festival_id?: string | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          location?: string
          registration_required?: boolean | null
          start_date?: string
          status?: "DRAFT" | "PUBLISHED" | "ARCHIVED" | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "events_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "events_festival_id_fkey"
            columns: ["festival_id"]
            isOneToOne: false
            referencedRelation: "festivals"
            referencedColumns: ["id"]
          }
        ]
      }
      festivals: {
        Row: {
          created_at: string | null
          created_by: string | null
          description: string | null
          end_date: string | null
          featured: boolean
          id: string
          location: string | null
          name: string
          start_date: string | null
          status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          end_date?: string | null
          featured?: boolean
          id?: string
          location?: string | null
          name: string
          start_date?: string | null
          status?: "DRAFT" | "PUBLISHED" | "ARCHIVED" | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          end_date?: string | null
          featured?: boolean
          id?: string
          location?: string | null
          name?: string
          start_date?: string | null
          status?: "DRAFT" | "PUBLISHED" | "ARCHIVED" | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "festivals_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          bio: string | null
          created_at: string | null
          email: string
          full_name: string | null
          id: string
          interests: string[] | null
          location: string | null
          role: "SUPER_ADMIN" | "CONTENT_ADMIN" | "MODERATOR" | "USER" | null
          updated_at: string | null
          username: string
          website: string | null
        }
        Insert: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          email: string
          full_name?: string | null
          id?: string
          interests?: string[] | null
          location?: string | null
          role?: "SUPER_ADMIN" | "CONTENT_ADMIN" | "MODERATOR" | "USER" | null
          updated_at?: string | null
          username: string
          website?: string | null
        }
        Update: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          email?: string
          full_name?: string | null
          id?: string
          interests?: string[] | null
          location?: string | null
          role?: "SUPER_ADMIN" | "CONTENT_ADMIN" | "MODERATOR" | "USER" | null
          updated_at?: string | null
          username?: string
          website?: string | null
        }
        Relationships: []
      }
      tips: {
        Row: {
          category: "SURVIVAL" | "SOCIAL" | "COMFORT" | "BUDGET" | "EXPERIENCE" | "OTHER" | null
          content: string
          created_at: string | null
          created_by: string | null
          description: string | null
          helpful_count: number | null
          id: string
          image_url: string | null
          is_active: boolean | null
          is_featured: boolean | null
          order_index: number | null
          status: string | null
          tags: string[] | null
          title: string
          updated_at: string | null
          view_count: number | null
        }
        Insert: {
          category?: "SURVIVAL" | "SOCIAL" | "COMFORT" | "BUDGET" | "EXPERIENCE" | "OTHER" | null
          content: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          helpful_count?: number | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          order_index?: number | null
          status?: string | null
          tags?: string[] | null
          title: string
          updated_at?: string | null
          view_count?: number | null
        }
        Update: {
          category?: Database["public"]["Enums"]["tip_category"] | null
          content?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          helpful_count?: number | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          order_index?: number | null
          status?: string | null
          tags?: string[] | null
          title?: string
          updated_at?: string | null
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "tips_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      guides: {
        Row: {
          category: Database["public"]["Enums"]["guide_category"] | null
          content: string
          created_at: string | null
          created_by: string | null
          description: string | null
          estimated_read_time: number | null
          helpful_count: number | null
          id: string
          image_url: string | null
          is_active: boolean | null
          is_featured: boolean | null
          order_index: number | null
          status: string | null
          tags: string[] | null
          title: string
          updated_at: string | null
          view_count: number | null
        }
        Insert: {
          category?: Database["public"]["Enums"]["guide_category"] | null
          content: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          estimated_read_time?: number | null
          helpful_count?: number | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          order_index?: number | null
          status?: string | null
          tags?: string[] | null
          title: string
          updated_at?: string | null
          view_count?: number | null
        }
        Update: {
          category?: Database["public"]["Enums"]["guide_category"] | null
          content?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          estimated_read_time?: number | null
          helpful_count?: number | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          order_index?: number | null
          status?: string | null
          tags?: string[] | null
          title?: string
          updated_at?: string | null
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "guides_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      faqs: {
        Row: {
          answer: string
          category: string | null
          created_at: string | null
          created_by: string | null
          helpful_count: number | null
          id: string
          is_active: boolean | null
          is_featured: boolean | null
          order_index: number | null
          question: string
          status: string | null
          tags: string[] | null
          updated_at: string | null
          view_count: number | null
        }
        Insert: {
          answer: string
          category?: string | null
          created_at?: string | null
          created_by?: string | null
          helpful_count?: number | null
          id?: string
          is_active?: boolean | null
          is_featured?: boolean | null
          order_index?: number | null
          question: string
          status?: string | null
          tags?: string[] | null
          updated_at?: string | null
          view_count?: number | null
        }
        Update: {
          answer?: string
          category?: string | null
          created_at?: string | null
          created_by?: string | null
          helpful_count?: number | null
          id?: string
          is_active?: boolean | null
          is_featured?: boolean | null
          order_index?: number | null
          question?: string
          status?: string | null
          tags?: string[] | null
          updated_at?: string | null
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "faqs_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      external_links: {
        Row: {
          active: boolean | null
          created_at: string
          id: string
          title: string
          type: string
          updated_at: string
          url: string
        }
        Insert: {
          active?: boolean | null
          created_at?: string
          id?: string
          title: string
          type: string
          updated_at?: string
          url: string
        }
        Update: {
          active?: boolean | null
          created_at?: string
          id?: string
          title?: string
          type?: string
          updated_at?: string
          url?: string
        }
        Relationships: []
      }
      emergency_contacts: {
        Row: {
          contact_type: string
          created_at: string | null
          created_by: string | null
          description: string | null
          email: string | null
          festival_id: string | null
          id: string
          is_active: boolean | null
          is_primary: boolean | null
          name: string
          order_index: number | null
          phone: string | null
          updated_at: string | null
        }
        Insert: {
          contact_type: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          email?: string | null
          festival_id?: string | null
          id?: string
          is_active?: boolean | null
          is_primary?: boolean | null
          name: string
          order_index?: number | null
          phone?: string | null
          updated_at?: string | null
        }
        Update: {
          contact_type?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          email?: string | null
          festival_id?: string | null
          id?: string
          is_active?: boolean | null
          is_primary?: boolean | null
          name?: string
          order_index?: number | null
          phone?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "emergency_contacts_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "emergency_contacts_festival_id_fkey"
            columns: ["festival_id"]
            isOneToOne: false
            referencedRelation: "festivals"
            referencedColumns: ["id"]
          }
        ]
      }
      safety_information: {
        Row: {
          content: string
          created_at: string | null
          created_by: string | null
          festival_id: string | null
          id: string
          is_active: boolean | null
          is_alert: boolean | null
          order_index: number | null
          priority: string | null
          safety_category: string
          title: string
          updated_at: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          created_by?: string | null
          festival_id?: string | null
          id?: string
          is_active?: boolean | null
          is_alert?: boolean | null
          order_index?: number | null
          priority?: string | null
          safety_category: string
          title: string
          updated_at?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          created_by?: string | null
          festival_id?: string | null
          id?: string
          is_active?: boolean | null
          is_alert?: boolean | null
          order_index?: number | null
          priority?: string | null
          safety_category?: string
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "safety_information_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "safety_information_festival_id_fkey"
            columns: ["festival_id"]
            isOneToOne: false
            referencedRelation: "festivals"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      activity_type:
        | "workshop"
        | "meetup"
        | "performance"
        | "game"
        | "social"
        | "food"
        | "other"
      event_status: "DRAFT" | "PUBLISHED" | "ARCHIVED"
      festival_status: "DRAFT" | "PUBLISHED" | "ARCHIVED"
      user_role: "SUPER_ADMIN" | "CONTENT_ADMIN" | "MODERATOR" | "USER"
      tip_category:
        | "SURVIVAL"
        | "SOCIAL"
        | "COMFORT"
        | "BUDGET"
        | "EXPERIENCE"
        | "OTHER"
      guide_category:
        | "SAFETY"
        | "PACKING"
        | "CAMPING"
        | "FOOD"
        | "TRANSPORT"
        | "OTHER"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

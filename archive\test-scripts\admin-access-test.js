#!/usr/bin/env node

/**
 * Admin Access Testing
 * 
 * This script tests admin access control and role-based permissions:
 * - Regular user admin access blocking
 * - Admin navigation visibility
 * - Admin route protection
 * - Admin dashboard functionality
 */

import { chromium } from 'playwright';
import { promises as fs } from 'fs';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'admin-access-evidence';

// Test user credentials
const TEST_USER = {
  email: `access.test.${Date.now()}@festivalfamily.test`,
  password: 'TestPassword123!',
  fullName: 'Access Test User'
};

async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

async function authenticateUser(page) {
  console.log('\n🔐 Setting up authenticated session');
  console.log('==================================');
  
  await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(2000);
  
  // Sign up new user
  const signUpTab = await page.$('text=Sign Up');
  if (signUpTab) {
    await signUpTab.click();
    await page.waitForTimeout(1000);
  }
  
  const emailInput = await page.$('input[type="email"]');
  const passwordInput = await page.$('input[type="password"]');
  
  if (emailInput && passwordInput) {
    await emailInput.fill(TEST_USER.email);
    await passwordInput.fill(TEST_USER.password);
    
    const submitButton = await page.$('button[type="submit"]');
    if (submitButton) {
      await submitButton.click();
      await page.waitForTimeout(3000);
    }
  }
  
  console.log(`✅ Authenticated as: ${TEST_USER.email}`);
}

async function testAdminAccessControl(page) {
  console.log('\n🔒 ADMIN ACCESS CONTROL TESTING');
  console.log('===============================');
  
  // Navigate to home and check current state
  await page.goto(APP_URL, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  // Capture authenticated home state
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/01-authenticated-home.png`,
    fullPage: true 
  });
  
  // Check for admin navigation elements
  const adminNavLink = await page.$('nav a[href="/admin"]');
  const adminNavText = await page.$('nav:has-text("Admin")');
  const adminButton = await page.$('button:has-text("Admin")');
  
  console.log('🧭 ADMIN NAVIGATION CHECK:');
  console.log(`🔗 Admin Nav Link: ${adminNavLink ? '⚠️ Visible (may indicate admin role)' : '✅ Hidden (expected for regular user)'}`);
  console.log(`📝 Admin Nav Text: ${adminNavText ? '⚠️ Visible' : '✅ Hidden'}`);
  console.log(`🔘 Admin Button: ${adminButton ? '⚠️ Visible' : '✅ Hidden'}`);
  
  // Test direct admin access
  console.log('\n🔄 Testing direct admin route access...');
  await page.goto(`${APP_URL}/admin`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  const adminUrl = page.url();
  const adminAccessBlocked = !adminUrl.includes('/admin') || adminUrl.includes('/auth') || adminUrl.includes('/');
  
  // Capture admin access attempt result
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/02-admin-access-attempt.png`,
    fullPage: true 
  });
  
  // Check for access denied message or redirect
  const accessDeniedMessage = await page.$('text=Access Denied') || await page.$('text=Unauthorized');
  const isOnHomePage = adminUrl === `${APP_URL}/` || adminUrl === APP_URL;
  const isOnAuthPage = adminUrl.includes('/auth');
  
  console.log('\n🎯 ADMIN ACCESS RESULTS:');
  console.log(`🔗 Final URL: ${adminUrl}`);
  console.log(`🚫 Access Blocked: ${adminAccessBlocked ? '✅ Yes (Security Working)' : '❌ No (Security Issue)'}`);
  console.log(`⚠️ Access Denied Message: ${accessDeniedMessage ? '✅ Shown' : '❌ Not Shown'}`);
  console.log(`🏠 Redirected to Home: ${isOnHomePage ? '✅ Yes' : '❌ No'}`);
  console.log(`🔐 Redirected to Auth: ${isOnAuthPage ? '✅ Yes' : '❌ No'}`);
  
  // Test admin sub-routes
  console.log('\n🔄 Testing admin sub-route access...');
  const adminSubRoutes = ['/admin/users', '/admin/festivals', '/admin/events', '/admin/activities'];
  const subRouteResults = [];
  
  for (const route of adminSubRoutes) {
    await page.goto(`${APP_URL}${route}`, { waitUntil: 'domcontentloaded', timeout: 10000 });
    await page.waitForTimeout(2000);
    
    const currentUrl = page.url();
    const routeBlocked = !currentUrl.includes(route);
    
    subRouteResults.push({
      route,
      blocked: routeBlocked,
      finalUrl: currentUrl
    });
    
    console.log(`   ${route}: ${routeBlocked ? '✅ Blocked' : '❌ Accessible'}`);
  }
  
  // Capture final state
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/03-final-access-state.png`,
    fullPage: true 
  });
  
  // Calculate security score
  const navigationSecure = !adminNavLink && !adminNavText && !adminButton;
  const mainRouteSecure = adminAccessBlocked;
  const subRoutesSecure = subRouteResults.every(r => r.blocked);
  
  const securityChecks = [navigationSecure, mainRouteSecure, subRoutesSecure];
  const securityScore = (securityChecks.filter(Boolean).length / securityChecks.length) * 100;
  
  console.log(`\n📊 ADMIN SECURITY SCORE: ${securityScore.toFixed(1)}%`);
  console.log(`   - Navigation Security: ${navigationSecure ? '✅ Secure' : '❌ Exposed'}`);
  console.log(`   - Main Route Security: ${mainRouteSecure ? '✅ Secure' : '❌ Exposed'}`);
  console.log(`   - Sub-Routes Security: ${subRoutesSecure ? '✅ Secure' : '❌ Exposed'}`);
  
  return {
    navigation: {
      adminNavLink: !!adminNavLink,
      adminNavText: !!adminNavText,
      adminButton: !!adminButton,
      secure: navigationSecure
    },
    access: {
      mainRouteBlocked: adminAccessBlocked,
      finalUrl: adminUrl,
      accessDeniedMessage: !!accessDeniedMessage,
      redirectedToHome: isOnHomePage,
      redirectedToAuth: isOnAuthPage
    },
    subRoutes: subRouteResults,
    security: {
      navigationSecure,
      mainRouteSecure,
      subRoutesSecure,
      overallScore: securityScore
    },
    screenshots: [
      '01-authenticated-home.png',
      '02-admin-access-attempt.png',
      '03-final-access-state.png'
    ]
  };
}

async function runAdminAccessTest() {
  console.log('🔒 ADMIN ACCESS CONTROL TESTING');
  console.log('===============================');
  console.log(`🕐 Start Time: ${new Date().toISOString()}`);
  console.log(`👤 Test User: ${TEST_USER.email}`);
  
  await ensureEvidenceDir();
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();
  
  try {
    // Authenticate user
    await authenticateUser(page);
    
    // Test admin access control
    const results = await testAdminAccessControl(page);
    
    // Save comprehensive results
    const evidence = {
      timestamp: new Date().toISOString(),
      testUser: TEST_USER,
      accessControlResults: results,
      summary: {
        securityScore: results.security.overallScore,
        adminAccessBlocked: results.access.mainRouteBlocked,
        navigationSecure: results.navigation.secure,
        subRoutesSecure: results.security.subRoutesSecure,
        overallSecure: results.security.overallScore >= 100,
        screenshots: results.screenshots
      }
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/admin-access-test-results.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    console.log('\n📊 ADMIN ACCESS TEST SUMMARY');
    console.log('============================');
    console.log(`🔒 Security Score: ${evidence.summary.securityScore.toFixed(1)}%`);
    console.log(`🚫 Admin Access Blocked: ${evidence.summary.adminAccessBlocked ? '✅ Yes' : '❌ No'}`);
    console.log(`🧭 Navigation Secure: ${evidence.summary.navigationSecure ? '✅ Yes' : '❌ No'}`);
    console.log(`🔗 Sub-Routes Secure: ${evidence.summary.subRoutesSecure ? '✅ Yes' : '❌ No'}`);
    console.log(`🎯 Overall Secure: ${evidence.summary.overallSecure ? '✅ Yes' : '❌ No'}`);
    console.log(`📸 Screenshots: ${evidence.summary.screenshots.length}`);
    console.log(`📁 Evidence: ${EVIDENCE_DIR}/`);
    
    return evidence;
    
  } catch (error) {
    console.error('\n💥 Admin access test failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

// Run the admin access test
runAdminAccessTest()
  .then(() => {
    console.log('\n✅ Admin access test completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Admin access test failed:', error);
    process.exit(1);
  });

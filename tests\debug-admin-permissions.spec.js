/**
 * Debug Admin Permissions Test
 * 
 * This test checks the actual admin user role and permissions
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

test('Debug Admin Permissions', async ({ page }) => {
  console.log('🔍 Debugging admin permissions...');
  
  // Capture console logs
  const consoleLogs = [];
  page.on('console', msg => {
    consoleLogs.push(`${msg.type()}: ${msg.text()}`);
  });
  
  const loginSuccess = await loginAsAdmin(page);
  console.log(`Login success: ${loginSuccess}`);
  
  if (loginSuccess) {
    // Try to access admin activities page
    await page.goto('/admin/activities');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const currentUrl = page.url();
    console.log(`Current URL after admin/activities: ${currentUrl}`);
    
    // Try to access admin activities/new page
    await page.goto('/admin/activities/new');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const newUrl = page.url();
    console.log(`Current URL after admin/activities/new: ${newUrl}`);
    
    // Check if we can see the form
    const hasForm = await page.locator('form').count() > 0;
    const hasNameField = await page.locator('input[name="name"]').count() > 0;
    const hasSubmitButton = await page.locator('button[type="submit"]').count() > 0;
    
    console.log(`Has form: ${hasForm}`);
    console.log(`Has name field: ${hasNameField}`);
    console.log(`Has submit button: ${hasSubmitButton}`);
    
    // Print console logs to see permission checks
    console.log('\n📋 Console logs:');
    consoleLogs.forEach(log => console.log(`  ${log}`));
    
    await page.screenshot({ path: 'test-results/debug-admin-permissions.png', fullPage: true });
  }
});

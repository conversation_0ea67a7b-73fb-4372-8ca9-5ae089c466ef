-- Create Complete Announcements System
-- This migration creates the announcements table and related functionality
-- for the admin dashboard content management system

-- ============================================================================
-- CREATE ANNOUNCEMENTS TABLE
-- ============================================================================

-- Create announcements table if it doesn't exist
CREATE TABLE IF NOT EXISTS announcements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'success', 'error', 'urgent')),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
    is_active BOOLEAN DEFAULT true,
    is_pinned BOOLEAN DEFAULT false,
    target_audience TEXT DEFAULT 'all' CHECK (target_audience IN ('all', 'admins', 'users', 'festival_family')),
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- ============================================================================
-- CREATE TIPS TABLE (if not exists)
-- ============================================================================

-- Create tips table for proper informational content storage
CREATE TABLE IF NOT EXISTS tips (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('PACKING', 'BUDGET', 'NAVIGATION', 'SAFETY', 'TRANSPORTATION', 'ACCOMMODATION', 'ATTRACTIONS', 'GENERAL')),
    is_active BOOLEAN DEFAULT true,
    order_index INTEGER DEFAULT 0,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- ============================================================================
-- CREATE FAQ TABLE
-- ============================================================================

-- Create FAQ table for frequently asked questions
CREATE TABLE IF NOT EXISTS faqs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    category TEXT DEFAULT 'GENERAL' CHECK (category IN ('GENERAL', 'TICKETS', 'ACCOMMODATION', 'TRANSPORTATION', 'FESTIVAL', 'SAFETY')),
    is_active BOOLEAN DEFAULT true,
    order_index INTEGER DEFAULT 0,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Announcements indexes
CREATE INDEX IF NOT EXISTS idx_announcements_type ON announcements(type);
CREATE INDEX IF NOT EXISTS idx_announcements_priority ON announcements(priority);
CREATE INDEX IF NOT EXISTS idx_announcements_is_active ON announcements(is_active);
CREATE INDEX IF NOT EXISTS idx_announcements_target_audience ON announcements(target_audience);
CREATE INDEX IF NOT EXISTS idx_announcements_start_date ON announcements(start_date);
CREATE INDEX IF NOT EXISTS idx_announcements_end_date ON announcements(end_date);
CREATE INDEX IF NOT EXISTS idx_announcements_created_by ON announcements(created_by);

-- Tips indexes
CREATE INDEX IF NOT EXISTS idx_tips_category ON tips(category);
CREATE INDEX IF NOT EXISTS idx_tips_is_active ON tips(is_active);
CREATE INDEX IF NOT EXISTS idx_tips_order_index ON tips(order_index);

-- FAQ indexes
CREATE INDEX IF NOT EXISTS idx_faqs_category ON faqs(category);
CREATE INDEX IF NOT EXISTS idx_faqs_is_active ON faqs(is_active);
CREATE INDEX IF NOT EXISTS idx_faqs_order_index ON faqs(order_index);

-- ============================================================================
-- ROW LEVEL SECURITY POLICIES
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE announcements ENABLE ROW LEVEL SECURITY;
ALTER TABLE tips ENABLE ROW LEVEL SECURITY;
ALTER TABLE faqs ENABLE ROW LEVEL SECURITY;

-- Announcements policies
CREATE POLICY "Anyone can view active announcements" 
    ON announcements FOR SELECT 
    USING (
        is_active = true AND 
        (start_date IS NULL OR start_date <= NOW()) AND 
        (end_date IS NULL OR end_date >= NOW())
    );

CREATE POLICY "Admins can manage all announcements" 
    ON announcements FOR ALL 
    USING (is_admin(auth.uid()));

-- Tips policies
CREATE POLICY "Anyone can view active tips" 
    ON tips FOR SELECT 
    USING (is_active = true);

CREATE POLICY "Content admins can manage tips" 
    ON tips FOR ALL 
    USING (
        auth.uid() IN (
            SELECT id FROM profiles 
            WHERE role IN ('SUPER_ADMIN', 'CONTENT_ADMIN')
        )
    );

-- FAQ policies
CREATE POLICY "Anyone can view active FAQs" 
    ON faqs FOR SELECT 
    USING (is_active = true);

CREATE POLICY "Content admins can manage FAQs" 
    ON faqs FOR ALL 
    USING (
        auth.uid() IN (
            SELECT id FROM profiles 
            WHERE role IN ('SUPER_ADMIN', 'CONTENT_ADMIN')
        )
    );

-- ============================================================================
-- TRIGGERS FOR UPDATED_AT
-- ============================================================================

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
DROP TRIGGER IF EXISTS update_announcements_updated_at ON announcements;
CREATE TRIGGER update_announcements_updated_at
    BEFORE UPDATE ON announcements
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_tips_updated_at ON tips;
CREATE TRIGGER update_tips_updated_at
    BEFORE UPDATE ON tips
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_faqs_updated_at ON faqs;
CREATE TRIGGER update_faqs_updated_at
    BEFORE UPDATE ON faqs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Function to get active announcements for a user
CREATE OR REPLACE FUNCTION get_active_announcements(target_user_role TEXT DEFAULT 'all')
RETURNS TABLE (
    id UUID,
    title TEXT,
    content TEXT,
    type TEXT,
    priority TEXT,
    is_pinned BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.id,
        a.title,
        a.content,
        a.type,
        a.priority,
        a.is_pinned,
        a.created_at
    FROM announcements a
    WHERE 
        a.is_active = true 
        AND (a.start_date IS NULL OR a.start_date <= NOW())
        AND (a.end_date IS NULL OR a.end_date >= NOW())
        AND (a.target_audience = 'all' OR a.target_audience = target_user_role)
    ORDER BY 
        a.is_pinned DESC,
        a.priority DESC,
        a.created_at DESC;
END;
$$;

-- Function to get tips by category
CREATE OR REPLACE FUNCTION get_tips_by_category(tip_category TEXT DEFAULT NULL)
RETURNS TABLE (
    id UUID,
    title TEXT,
    content TEXT,
    category TEXT,
    order_index INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id,
        t.title,
        t.content,
        t.category,
        t.order_index
    FROM tips t
    WHERE 
        t.is_active = true 
        AND (tip_category IS NULL OR t.category = tip_category)
    ORDER BY 
        t.category,
        t.order_index,
        t.title;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_active_announcements TO authenticated;
GRANT EXECUTE ON FUNCTION get_tips_by_category TO authenticated;

-- ============================================================================
-- SAMPLE DATA
-- ============================================================================

-- Insert sample announcement
INSERT INTO announcements (title, content, type, priority, target_audience) VALUES
('Welcome to Festival Family!', 'Welcome to the Festival Family community! Check out our tips and guides to make the most of your Sziget Festival experience.', 'info', 'medium', 'all')
ON CONFLICT DO NOTHING;

-- Insert sample FAQ
INSERT INTO faqs (question, answer, category, order_index) VALUES
('How do I join Festival Family activities?', 'You can join Festival Family activities by browsing the activities section and clicking "Join" on any activity that interests you. Make sure to check the requirements and deadlines!', 'FESTIVAL', 1),
('What should I pack for Sziget Festival?', 'Check out our comprehensive packing guide in the Tips section! Essential items include comfortable shoes, sunscreen, portable charger, and cash in Hungarian Forint.', 'GENERAL', 2)
ON CONFLICT DO NOTHING;

-- ============================================================================
-- VALIDATION AND LOGGING
-- ============================================================================

-- Test that all tables are working
DO $$
DECLARE
    announcements_count INTEGER;
    tips_count INTEGER;
    faqs_count INTEGER;
BEGIN
    -- Check announcements table
    SELECT COUNT(*) INTO announcements_count FROM announcements;
    RAISE NOTICE 'SUCCESS: announcements table accessible with % records', announcements_count;
    
    -- Check tips table
    SELECT COUNT(*) INTO tips_count FROM tips;
    RAISE NOTICE 'SUCCESS: tips table accessible with % records', tips_count;
    
    -- Check faqs table
    SELECT COUNT(*) INTO faqs_count FROM faqs;
    RAISE NOTICE 'SUCCESS: faqs table accessible with % records', faqs_count;
    
    -- Test helper functions
    PERFORM get_active_announcements();
    RAISE NOTICE 'SUCCESS: get_active_announcements function working';
    
    PERFORM get_tips_by_category();
    RAISE NOTICE 'SUCCESS: get_tips_by_category function working';
END $$;

-- Log successful migration
DO $$
BEGIN
    RAISE NOTICE 'SUCCESS: Complete announcements system created';
    RAISE NOTICE 'INFO: announcements table with full admin management';
    RAISE NOTICE 'INFO: tips table for informational content';
    RAISE NOTICE 'INFO: faqs table for frequently asked questions';
    RAISE NOTICE 'INFO: RLS policies for proper access control';
    RAISE NOTICE 'INFO: Helper functions for content retrieval';
    RAISE NOTICE 'INFO: Ready for admin dashboard integration';
END $$;

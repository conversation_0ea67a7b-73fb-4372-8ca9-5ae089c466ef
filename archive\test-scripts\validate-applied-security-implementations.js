/**
 * Validate Applied Security Implementations
 * 
 * This script tests the security migrations that were successfully applied
 * to verify they are working correctly in the remote Supabase database.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔒 Validating Applied Security Implementations');
console.log('==============================================');

// Test 1: Verify Admin Account and Role Management
async function testAdminAccountAndRoles() {
  console.log('🔍 Test 1: Admin Account and Role Management');
  console.log('--------------------------------------------');
  
  const results = {
    adminAccountExists: false,
    adminHasSuperAdminRole: false,
    changeUserRoleFunctionExists: false,
    roleChangeTest: null,
    timestamp: new Date().toISOString()
  };
  
  try {
    // Test 1.1: Verify admin account exists and has SUPER_ADMIN role
    console.log('📋 Checking admin account status...');
    const { data: adminProfile, error: adminError } = await supabase
      .from('profiles')
      .select('id, email, role, created_at, updated_at')
      .eq('email', '<EMAIL>')
      .single();
    
    if (adminError) {
      console.log(`❌ Admin account check failed: ${adminError.message}`);
    } else if (adminProfile) {
      results.adminAccountExists = true;
      results.adminHasSuperAdminRole = adminProfile.role === 'SUPER_ADMIN';
      
      console.log('✅ Admin account found:');
      console.log(`   📧 Email: ${adminProfile.email}`);
      console.log(`   🛡️ Role: ${adminProfile.role}`);
      console.log(`   🆔 ID: ${adminProfile.id}`);
      console.log(`   📅 Created: ${adminProfile.created_at}`);
      console.log(`   🔄 Updated: ${adminProfile.updated_at}`);
      
      if (adminProfile.role === 'SUPER_ADMIN') {
        console.log('✅ Admin has SUPER_ADMIN role - privilege escalation fix preserved admin access');
      } else {
        console.log(`⚠️ Admin role is ${adminProfile.role}, expected SUPER_ADMIN`);
      }
    }
    
    // Test 1.2: Check if change_user_role function exists and is accessible
    console.log('🔧 Testing change_user_role function...');
    try {
      // Try to call the function with invalid parameters to test if it exists
      const { data: functionTest, error: functionError } = await supabase
        .rpc('change_user_role', { 
          target_user_id: '********-0000-0000-0000-********0000', 
          new_role: 'USER' 
        });
      
      if (functionError) {
        if (functionError.message.includes('Only SUPER_ADMIN can change user roles')) {
          console.log('✅ change_user_role function exists and security is working');
          results.changeUserRoleFunctionExists = true;
          results.roleChangeTest = 'Function exists and properly secured';
        } else if (functionError.message.includes('function') && functionError.message.includes('does not exist')) {
          console.log('❌ change_user_role function does not exist');
          results.roleChangeTest = 'Function not found';
        } else {
          console.log(`⚠️ change_user_role function test: ${functionError.message}`);
          results.changeUserRoleFunctionExists = true;
          results.roleChangeTest = functionError.message;
        }
      } else {
        console.log('✅ change_user_role function executed successfully');
        results.changeUserRoleFunctionExists = true;
        results.roleChangeTest = 'Function executed successfully';
      }
    } catch (err) {
      console.log(`⚠️ change_user_role function test error: ${err.message}`);
      results.roleChangeTest = err.message;
    }
    
  } catch (error) {
    console.error('💥 Admin account test failed:', error);
    results.error = error.message;
  }
  
  return results;
}

// Test 2: Verify XSS Protection Functions
async function testXSSProtectionFunctions() {
  console.log('\n🛡️ Test 2: XSS Protection Functions');
  console.log('-----------------------------------');
  
  const results = {
    sanitizeXssInputExists: false,
    testXssProtectionExists: false,
    xssTestResults: [],
    timestamp: new Date().toISOString()
  };
  
  // XSS test payloads
  const testPayloads = [
    '<script>alert("XSS")</script>',
    'javascript:alert("XSS")',
    '<img src="x" onerror="alert(\'XSS\')">',
    '<svg onload="alert(\'XSS\')">',
    '"><script>alert("XSS")</script>',
    'Hello <b>World</b>',
    'Normal text without XSS'
  ];
  
  try {
    // Test 2.1: Check if test_xss_protection function exists
    console.log('🔍 Testing XSS protection functions...');
    
    for (let i = 0; i < testPayloads.length; i++) {
      const payload = testPayloads[i];
      console.log(`🧪 Testing payload ${i + 1}/${testPayloads.length}: "${payload.substring(0, 30)}${payload.length > 30 ? '...' : ''}"`);
      
      try {
        const { data: sanitizedResult, error: testError } = await supabase
          .rpc('test_xss_protection', { test_input: payload });
        
        if (testError) {
          if (testError.message.includes('function') && testError.message.includes('does not exist')) {
            console.log('❌ test_xss_protection function does not exist');
            results.testXssProtectionExists = false;
            break;
          } else {
            console.log(`   ⚠️ Error: ${testError.message}`);
            results.xssTestResults.push({
              payload: payload.substring(0, 50),
              error: testError.message,
              sanitized: null
            });
          }
        } else {
          results.testXssProtectionExists = true;
          const wasSanitized = sanitizedResult !== payload;
          const protectionLevel = wasSanitized ? 'PROTECTED' : 'UNCHANGED';
          
          console.log(`   📤 Input:  "${payload}"`);
          console.log(`   📥 Output: "${sanitizedResult}"`);
          console.log(`   🛡️ Status: ${protectionLevel}`);
          
          results.xssTestResults.push({
            payload: payload.substring(0, 50),
            sanitized: sanitizedResult.substring(0, 50),
            wasSanitized,
            protectionLevel
          });
        }
      } catch (err) {
        console.log(`   💥 Test error: ${err.message}`);
        results.xssTestResults.push({
          payload: payload.substring(0, 50),
          error: err.message,
          sanitized: null
        });
      }
    }
    
    // Test 2.2: Direct sanitize_xss_input function test
    console.log('\n🔧 Testing direct sanitize_xss_input function...');
    try {
      const { data: directResult, error: directError } = await supabase
        .rpc('sanitize_xss_input', { input_text: '<script>alert("direct test")</script>Safe text' });
      
      if (directError) {
        if (directError.message.includes('function') && directError.message.includes('does not exist')) {
          console.log('❌ sanitize_xss_input function does not exist');
          results.sanitizeXssInputExists = false;
        } else {
          console.log(`⚠️ Direct function test error: ${directError.message}`);
        }
      } else {
        console.log('✅ sanitize_xss_input function exists and working');
        console.log(`   📥 Direct result: "${directResult}"`);
        results.sanitizeXssInputExists = true;
      }
    } catch (err) {
      console.log(`💥 Direct function test error: ${err.message}`);
    }
    
  } catch (error) {
    console.error('💥 XSS protection test failed:', error);
    results.error = error.message;
  }
  
  return results;
}

// Test 3: Verify RLS Policies and Database Security
async function testRLSPoliciesAndSecurity() {
  console.log('\n🔐 Test 3: RLS Policies and Database Security');
  console.log('---------------------------------------------');
  
  const results = {
    profilesRLSEnabled: false,
    auditLogTableExists: false,
    policiesActive: false,
    securityTestResults: [],
    timestamp: new Date().toISOString()
  };
  
  try {
    // Test 3.1: Check if RLS is enabled on profiles table
    console.log('🔍 Checking RLS status on profiles table...');
    const { data: rlsStatus, error: rlsError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (rlsError) {
      console.log(`⚠️ RLS test: ${rlsError.message}`);
      results.securityTestResults.push({
        test: 'RLS Status Check',
        result: 'Error',
        details: rlsError.message
      });
    } else {
      console.log('✅ Profiles table accessible - RLS policies working');
      results.profilesRLSEnabled = true;
      results.securityTestResults.push({
        test: 'RLS Status Check',
        result: 'Success',
        details: 'Profiles table accessible with proper RLS'
      });
    }
    
    // Test 3.2: Check if audit_log table exists
    console.log('📋 Checking audit_log table...');
    try {
      const { data: auditData, error: auditError } = await supabase
        .from('audit_log')
        .select('id')
        .limit(1);
      
      if (auditError) {
        if (auditError.message.includes('does not exist')) {
          console.log('❌ audit_log table does not exist');
          results.auditLogTableExists = false;
        } else {
          console.log(`⚠️ audit_log table test: ${auditError.message}`);
          results.auditLogTableExists = true; // Table exists but may have RLS restrictions
        }
      } else {
        console.log('✅ audit_log table exists and accessible');
        results.auditLogTableExists = true;
      }
    } catch (err) {
      console.log(`⚠️ audit_log table test error: ${err.message}`);
    }
    
    // Test 3.3: Test basic profile operations
    console.log('🧪 Testing basic profile operations...');
    try {
      const { data: profileCount, error: countError } = await supabase
        .from('profiles')
        .select('id', { count: 'exact', head: true });
      
      if (countError) {
        console.log(`⚠️ Profile count test: ${countError.message}`);
      } else {
        console.log(`✅ Profile operations working - found ${profileCount} profiles`);
        results.policiesActive = true;
      }
    } catch (err) {
      console.log(`⚠️ Profile operations test error: ${err.message}`);
    }
    
  } catch (error) {
    console.error('💥 RLS and security test failed:', error);
    results.error = error.message;
  }
  
  return results;
}

// Main execution
async function main() {
  try {
    console.log(`🕐 Started at: ${new Date().toISOString()}\n`);
    
    // Run all validation tests
    const adminResults = await testAdminAccountAndRoles();
    const xssResults = await testXSSProtectionFunctions();
    const rlsResults = await testRLSPoliciesAndSecurity();
    
    // Compile comprehensive results
    const validationResults = {
      testSuite: 'Applied Security Implementations Validation',
      timestamp: new Date().toISOString(),
      adminAccountTest: adminResults,
      xssProtectionTest: xssResults,
      rlsSecurityTest: rlsResults,
      overallStatus: {
        privilegeEscalationPrevention: adminResults.adminHasSuperAdminRole && adminResults.changeUserRoleFunctionExists,
        xssProtection: xssResults.testXssProtectionExists && xssResults.sanitizeXssInputExists,
        databaseSecurity: rlsResults.profilesRLSEnabled && rlsResults.policiesActive,
        adminFunctionalityPreserved: adminResults.adminAccountExists && adminResults.adminHasSuperAdminRole
      }
    };
    
    // Save results to file
    const resultsDir = 'security-validation-results';
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      `${resultsDir}/applied-security-validation-${Date.now()}.json`,
      JSON.stringify(validationResults, null, 2)
    );
    
    console.log('\n🎉 APPLIED SECURITY IMPLEMENTATIONS VALIDATION SUMMARY');
    console.log('======================================================');
    
    console.log('\n🛡️ SECURITY IMPLEMENTATION STATUS:');
    console.log(`✅ Privilege Escalation Prevention: ${validationResults.overallStatus.privilegeEscalationPrevention ? 'WORKING' : 'ISSUES DETECTED'}`);
    console.log(`✅ XSS Protection Functions: ${validationResults.overallStatus.xssProtection ? 'WORKING' : 'ISSUES DETECTED'}`);
    console.log(`✅ Database Security (RLS): ${validationResults.overallStatus.databaseSecurity ? 'WORKING' : 'ISSUES DETECTED'}`);
    console.log(`✅ Admin Functionality: ${validationResults.overallStatus.adminFunctionalityPreserved ? 'PRESERVED' : 'COMPROMISED'}`);
    
    console.log('\n📊 DETAILED FINDINGS:');
    console.log(`🔐 Admin Account: ${adminResults.adminAccountExists ? 'EXISTS' : 'MISSING'}`);
    console.log(`🛡️ Admin Role: ${adminResults.adminHasSuperAdminRole ? 'SUPER_ADMIN' : 'INCORRECT'}`);
    console.log(`⚙️ Role Change Function: ${adminResults.changeUserRoleFunctionExists ? 'AVAILABLE' : 'MISSING'}`);
    console.log(`🧪 XSS Test Function: ${xssResults.testXssProtectionExists ? 'AVAILABLE' : 'MISSING'}`);
    console.log(`🛡️ XSS Sanitize Function: ${xssResults.sanitizeXssInputExists ? 'AVAILABLE' : 'MISSING'}`);
    console.log(`🔒 RLS Policies: ${rlsResults.profilesRLSEnabled ? 'ACTIVE' : 'INACTIVE'}`);
    console.log(`📋 Audit Log Table: ${rlsResults.auditLogTableExists ? 'EXISTS' : 'MISSING'}`);
    
    if (xssResults.xssTestResults.length > 0) {
      const protectedCount = xssResults.xssTestResults.filter(r => r.wasSanitized).length;
      const totalTests = xssResults.xssTestResults.length;
      console.log(`🧪 XSS Protection Rate: ${protectedCount}/${totalTests} payloads sanitized`);
    }
    
    console.log(`\n📁 Results saved to: ${resultsDir}/applied-security-validation-${Date.now()}.json`);
    
  } catch (error) {
    console.error('💥 Validation process failed:', error);
  }
  
  process.exit(0);
}

main();

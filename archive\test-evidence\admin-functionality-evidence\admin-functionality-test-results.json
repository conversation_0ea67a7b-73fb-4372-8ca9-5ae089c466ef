{"timestamp": "2025-05-30T00:50:23.572Z", "adminUser": {"email": "<EMAIL>", "password": "AdminPassword123!", "fullName": "Admin Functionality Test User"}, "functionalityResults": {"adminUser": {"email": "<EMAIL>", "password": "AdminPassword123!", "fullName": "Admin Functionality Test User"}, "dashboardAccess": {"navigation": {"adminNavLink": false, "adminNavText": false, "adminButton": false}, "access": {"hasAdminAccess": false, "finalUrl": "http://localhost:5173/"}, "screenshots": ["01-authenticated-home-admin.png", "02-admin-dashboard-access.png"]}, "sectionFunctionality": {"skipped": true, "reason": "No admin access - user role not set to admin", "instructions": "Manually set user role to \"admin\" in database"}}, "summary": {"adminAccessWorking": false, "adminNavigationPresent": false, "functionalityScore": 0, "overallScore": 0, "requiresManualSetup": true, "screenshots": ["01-authenticated-home-admin.png", "02-admin-dashboard-access.png"]}}
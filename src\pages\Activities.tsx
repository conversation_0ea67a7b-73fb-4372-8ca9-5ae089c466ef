import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useActivityTracking } from '@/hooks/useUnifiedTracking';
import { usePersonalization } from '@/hooks/usePersonalization';
import { useActivityCounts } from '@/hooks/activities/useActivitiesQuery';
import { useRedisActivities } from '@/hooks/useRedisData';
import { useDatabaseErrorHandling } from '@/hooks/useErrorHandling';
import { useRealtimeSubscription } from '@/hooks/realtime/useRealtime';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Terminal, AlertCircle, RefreshCw, Search, Filter, Calendar, MapPin, Users, Clock, Heart, Share2, ChevronRight, Star, Music, Zap, Trophy } from 'lucide-react';
import { ResilientErrorBoundary } from '@/components/error/ResilientErrorBoundary';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { toast } from 'react-hot-toast';
import { simulateHapticFeedback, isMobileViewport, createTouchHandler } from '../utils/mobileUX';
import MobileUXTester from '../components/testing/MobileUXTester';
import { supabase } from '@/lib/supabase';
import { ActivityWithDetails } from '@/types/activities';
import { CACHE_DURATIONS } from '@/lib/data/constants';

import { ActivityDetailsModal } from '@/components/activities/ActivityDetailsModal';
import { ActivityType } from '@/types/enums';
import { optimizeActivityDescription, useIsMobile } from '@/utils/textOptimization';
import { ParticipantCount } from '@/components/activities/ParticipantCount';
// Unified interaction system - replacing scattered button implementations
import { UnifiedInteractionButton } from '@/components/design-system';
// Import unified design system components
import {
  PageWrapper,
  UnifiedCard,
  UnifiedButton,
  UnifiedBadge,
  UnifiedFilterBar,
  GridLayout,
  FlexLayout
} from '@/components/design-system';
import { BentoCard, BentoGrid } from '@/components/design-system/BentoGrid';
import { ActivityTypeBadge } from '@/components/design-system/EnhancedUnifiedBadge';
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';

// Error fallback component for Activities page using unified design system
const ActivitiesErrorFallback = ({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) => (
  <PageWrapper title="Error" variant="default">
    <UnifiedCard variant="outlined" priority="high" className="p-6 text-center">
      <FlexLayout direction="col" gap="md" align="center">
        <AlertCircle className="h-12 w-12 text-priority-high" />
        <h2 className="text-2xl font-bold text-primary-light">Unable to Load Activities</h2>
        <p className="text-secondary-light">
          We're having trouble loading the activities. This might be a temporary issue.
        </p>
        <div className="bg-priority-high-bg rounded-md p-4 border border-priority-high-border">
          <p className="text-sm text-priority-high">{error.message}</p>
        </div>
        <UnifiedButton onClick={resetErrorBoundary} variant="primary">
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </UnifiedButton>
      </FlexLayout>
    </UnifiedCard>
  </PageWrapper>
);

// Mock data removed - using real database connections

/**
 * Enhanced Mobile-First Activities page
 */
const Activities: React.FC = () => {
  // Error handling
  const { executeWithHandling } = useDatabaseErrorHandling();

  // Personalization
  const { sortByPersonalization } = usePersonalization();
  const [personalizedActivities, setPersonalizedActivities] = useState<any[]>([]);

  // Mobile state management
  const [isMobile, setIsMobile] = useState(false);
  const [activeTab, setActiveTab] = useState('meetup');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Redis-optimized data fetching with performance tracking
  const {
    activities: rawActivities = [],
    isLoading: dbLoading,
    error: dbError,
    refetch: refreshActivities,
    cacheHitRate,
    averageResponseTime
  } = useRedisActivities();

  // Get activity counts for filter badges (fallback to original hook)
  const { data: activityCounts } = useActivityCounts();

  // Real-time subscriptions for activities
  useRealtimeSubscription('activities', ['activities', 'all'], {
    event: '*',
    callback: (payload) => {
      console.log('🔄 Activities: Real-time update received:', payload.eventType);
      if (payload.eventType === 'INSERT') {
        console.log('✨ New activity added:', payload.new);
      } else if (payload.eventType === 'UPDATE') {
        console.log('📝 Activity updated:', payload.new);
      } else if (payload.eventType === 'DELETE') {
        console.log('🗑️ Activity deleted:', payload.old);
      }
    }
  });

  // Real-time subscriptions for participant updates
  useRealtimeSubscription('activity_participants', ['activities', 'participants'], {
    event: '*',
    callback: (payload) => {
      console.log('👥 Participant update:', payload.eventType);
    }
  });

  // Apply personalization to activities
  const [activitiesWithDetails, setActivitiesWithDetails] = useState<any[]>([]);

  useEffect(() => {
    if (!rawActivities) return;

    const applyPersonalization = async () => {
      try {
        // Create minimal objects for personalization scoring while preserving original data
        const itemsForScoring = rawActivities.map(activity => ({
          id: activity.id,
          category: activity.type || undefined,
          tags: activity.tags || undefined
        }));

        // Get personalized order
        const sortedItems = await sortByPersonalization(itemsForScoring, 'activity');

        // Apply the personalized order to the original complete activity objects
        const sortedActivities = sortedItems.map(sortedItem =>
          rawActivities.find(activity => activity.id === sortedItem.id)
        ).filter(Boolean); // Remove any undefined entries

        setActivitiesWithDetails(sortedActivities);
        setPersonalizedActivities(sortedActivities);
        console.log('🎯 Activities: Applied personalized sorting with preserved data');
      } catch (sortError) {
        console.log('📊 Activities: Personalization not available, using default order');
        setActivitiesWithDetails(rawActivities);
        setPersonalizedActivities([]);
      }
    };

    applyPersonalization();
  }, [rawActivities, sortByPersonalization]);

  // Modal state for activity details
  const [showDetails, setShowDetails] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<ActivityWithDetails | null>(null);

  // Check if mobile viewport
  useEffect(() => {
    const checkMobile = () => setIsMobile(isMobileViewport());
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Handle pull-to-refresh with comprehensive error handling
  const handleRefresh = useCallback(async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    simulateHapticFeedback('medium');

    const result = await executeWithHandling(
      async () => {
        refreshActivities();
        return true;
      },
      {
        context: { action: 'refresh_activities' },
        retryOptions: {
          maxRetries: 2,
          baseDelay: 1000
        },
        fallbackValue: false
      }
    );

    if (result) {
      console.log('✅ Activities refresh completed successfully');
    }

    setIsRefreshing(false);
  }, [isRefreshing, refreshActivities, executeWithHandling]);

  // Filter activities based on search and filters using real database data
  const filteredActivities = useMemo(() => {
    if (!activitiesWithDetails) return [];

    // Filter by tab type
    let activities = activitiesWithDetails;
    if (activeTab !== 'all') {
      activities = activitiesWithDetails.filter(activity => {
        switch (activeTab) {
          case 'meetup':
            return activity.type === ActivityType.MEETUP;
          case 'daily':
            return activity.type === ActivityType.WORKSHOP || activity.type === ActivityType.SOCIAL || activity.type === ActivityType.FOOD || activity.type === ActivityType.OTHER;
          case 'challenges':
            return activity.type === ActivityType.GAME || activity.type === ActivityType.PERFORMANCE;
          case 'upcoming':
            return activity.start_date ? new Date(activity.start_date) > new Date() : false;
          default:
            return true;
        }
      });
    }

    // Apply search and other filters
    return activities.filter(activity => {
      const matchesSearch = activity.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           activity.description?.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesSearch;
    });
  }, [activitiesWithDetails, activeTab, searchQuery]);

  // Handle tab change with haptic feedback
  const handleTabChange = useCallback((value: string) => {
    simulateHapticFeedback('light');
    setActiveTab(value);
  }, []);

  // Handle activity selection for modal
  const handleActivitySelect = useCallback((activity: ActivityWithDetails) => {
    setSelectedActivity(activity);
    setShowDetails(true);
    simulateHapticFeedback('light');
  }, []);

  if (dbLoading) {
    return (
      <PageWrapper title="Loading Activities..." variant="default">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <UnifiedCard variant="elevated" className="p-8 text-center">
            <FlexLayout direction="col" align="center" gap="md">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <Music className="w-12 h-12 text-festival-orange" />
              </motion.div>
              <LoadingSpinner size="lg" variant="dots" />
              <p className="text-secondary-light">
                {isMobile ? 'Loading activities...' : 'Loading festival activities...'}
              </p>
            </FlexLayout>
          </UnifiedCard>
        </motion.div>
      </PageWrapper>
    );
  }

  return (
    <ResilientErrorBoundary
      fallbackType="data-loading-error"
      isolationLevel="page"
      maxRetries={3}
      enableAutoRetry={true}
    >
      <PageWrapper
        title="Activities"
        subtitle="Discover and join festival activities"
        hideTitleOnMobile={true}
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
        >
          {/* Enhanced Activities Header Banner */}
          <div className="bg-gradient-to-r from-primary/15 to-accent/15 border border-primary/30 rounded-xl px-6 py-4 mb-6 shadow-lg backdrop-blur-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/20 rounded-lg">
                  <Music className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-foreground">
                    Festival Activities
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    Join the fun and connect with your tribe
                  </p>
                  {/* Redis Performance Metrics */}
                  {cacheHitRate > 0 && (
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary" className="text-xs">
                        ⚡ {cacheHitRate.toFixed(0)}% cache hit • {averageResponseTime.toFixed(0)}ms avg
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <UnifiedButton
                  variant="ghost"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                  className="text-xs"
                >
                  <RefreshCw className={`w-4 h-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                  <span className="hidden lg:inline">{isRefreshing ? 'Refreshing...' : 'Refresh'}</span>
                </UnifiedButton>
              </div>
            </div>
          </div>

          {/* Standardized Filter Bar */}
          <UnifiedFilterBar
            searchValue={searchQuery}
            onSearchChange={setSearchQuery}
            searchPlaceholder={isMobile ? "Search activities..." : "Search festival activities..."}
            filterOptions={[
              { id: 'meetup', label: 'Meetups', icon: Users, count: activityCounts?.meetup || 0, colorVariant: 'default' },
              { id: 'daily', label: 'Daily', icon: Calendar, count: activityCounts?.daily || 0, colorVariant: 'info' },
              { id: 'challenges', label: 'Compete', icon: Trophy, count: activityCounts?.compete || 0, colorVariant: 'warning' },
              { id: 'upcoming', label: 'Later', icon: Clock, count: activityCounts?.later || 0, colorVariant: 'success' }
            ]}
            activeFilter={activeTab}
            onFilterChange={handleTabChange}
            className="mb-6"
          />

          {/* Enhanced Activity Content */}
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <EnhancedActivityTab
                activities={filteredActivities}
                activeTab={activeTab}
                isMobile={isMobile}
                showFilters={showFilters}
                selectedDifficulty={selectedDifficulty}
                setSelectedDifficulty={setSelectedDifficulty}
                selectedTags={selectedTags}
                setSelectedTags={setSelectedTags}
                searchQuery={searchQuery}
                onActivitySelect={handleActivitySelect}
              />
            </motion.div>
          </AnimatePresence>

              {/* Activity Details Modal */}
              <ActivityDetailsModal
                activity={selectedActivity}
                isOpen={showDetails}
                onClose={() => {
                  setShowDetails(false);
                  setSelectedActivity(null);
                }}
              />
            </motion.div>

            {/* Development-only Mobile UX Testing Tool */}
            <MobileUXTester />
          </PageWrapper>
        </ResilientErrorBoundary>
  );
};

// Enhanced Activity Tab Component with Mobile-First Design
interface EnhancedActivityTabProps {
  activities: any[];
  activeTab: string;
  isMobile: boolean;
  showFilters: boolean;
  selectedDifficulty: string;
  setSelectedDifficulty: (difficulty: string) => void;
  selectedTags: string[];
  setSelectedTags: (tags: string[]) => void;
  searchQuery?: string;
  onActivitySelect?: (activity: ActivityWithDetails) => void;
}

const EnhancedActivityTab: React.FC<EnhancedActivityTabProps> = ({
  activities,
  activeTab,
  isMobile,
  showFilters,
  selectedDifficulty,
  setSelectedDifficulty,
  selectedTags,
  setSelectedTags,
  searchQuery,
  onActivitySelect
}) => {
  const getTabIcon = (tab: string) => {
    switch (tab) {
      case 'meetup': return Users;
      case 'daily': return Calendar;
      case 'challenges': return Trophy;
      case 'upcoming': return Clock;
      default: return Music;
    }
  };

  const getTabTitle = (tab: string) => {
    switch (tab) {
      case 'meetup': return 'Festival Meetups';
      case 'daily': return 'Daily Activities';
      case 'challenges': return 'Festival Challenges';
      case 'upcoming': return 'Upcoming Activities';
      default: return 'Activities';
    }
  };

  const getTabDescription = (tab: string) => {
    switch (tab) {
      case 'meetup': return 'Connect with other festival-goers at organized meetups';
      case 'daily': return 'Discover daily activities happening at festivals';
      case 'challenges': return 'Participate in fun challenges with other attendees';
      case 'upcoming': return 'See what\'s coming up at your favorite festivals';
      default: return 'Explore festival activities';
    }
  };

  const IconComponent = getTabIcon(activeTab);

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Tab Header */}
      <div className="flex items-center gap-3 mb-4">
        <div className="w-8 h-8 bg-gradient-to-br from-primary/20 to-primary/30 rounded-lg flex items-center justify-center">
          <IconComponent className="w-4 h-4 text-primary" />
        </div>
        <div>
          <h3 className="text-lg sm:text-xl font-semibold">{getTabTitle(activeTab)}</h3>
          <p className="text-white/70 text-sm sm:text-base">{getTabDescription(activeTab)}</p>
        </div>
      </div>

      {/* Mobile Filters Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white/5 rounded-xl p-4 border border-white/10"
          >
            <h4 className="text-sm font-medium text-white/80 mb-3">Filters</h4>
            <div className="space-y-3">
              {/* Difficulty Filter */}
              <div>
                <label className="block text-xs font-medium text-white/60 mb-2">Difficulty</label>
                <div className="flex flex-wrap gap-2">
                  {['all', 'Beginner', 'Intermediate', 'Advanced'].map((difficulty) => (
                    <motion.button
                      key={difficulty}
                      onClick={() => {
                        setSelectedDifficulty(difficulty);
                        simulateHapticFeedback('light');
                      }}
                      className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-colors ${
                        selectedDifficulty === difficulty
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted/10 text-muted-foreground hover:bg-muted/20'
                      }`}
                      whileTap={{ scale: 0.95 }}
                    >
                      {difficulty === 'all' ? 'All Levels' : difficulty}
                    </motion.button>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* AuthenticatedHome 4-Card Pattern: Full-Space Activities Grid */}
      {activities.length > 0 ? (
        <BentoGrid cols={2} gap="sm">
          {activities.map((activity, index) => (
            <MobileActivityCard
              key={activity.id}
              activity={activity}
              index={index}
              isMobile={isMobile}
              onActivitySelect={onActivitySelect}
            />
          ))}
        </BentoGrid>
      ) : (
        <div className="text-center py-8 sm:py-12">
          <div className="w-16 h-16 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-4">
            <IconComponent className="w-8 h-8 text-white/40" />
          </div>
          <h4 className="text-lg font-medium text-white/60 mb-2">No activities found</h4>
          <p className="text-white/40 text-sm">
            {searchQuery ? 'Try adjusting your search terms' : 'Check back later for new activities'}
          </p>
        </div>
      )}
    </div>
  );
};

// Mobile-Optimized Activity Card Component
interface MobileActivityCardProps {
  activity: ActivityWithDetails;
  index: number;
  isMobile: boolean;
  onActivitySelect?: (activity: ActivityWithDetails) => void;
}

const MobileActivityCard: React.FC<MobileActivityCardProps> = ({ activity, index, isMobile, onActivitySelect }) => {
  // Import unified tracking
  const { trackView } = useActivityTracking();

  // State for dynamic color theme from enhancedColorMappingService
  // Enhanced 2025 Visual Excellence: Using centralized vibrant fallback system
  const [colorTheme, setColorTheme] = useState({
    className: enhancedColorMappingService.getVibrantFallbackTheme(index),
    style: {}
  });
  const [isLoadingTheme, setIsLoadingTheme] = useState(true);

  // Track view when component mounts
  useEffect(() => {
    if (activity.id) {
      console.log(`🎯 MobileActivityCard: Tracking view for activity ${activity.id} (${activity.title})`);
      trackView('activity', activity.id).catch(console.error);
    }
  }, [activity.id, trackView]);

  // Load color theme from enhancedColorMappingService with sophisticated fallback
  useEffect(() => {
    const loadColorTheme = async () => {
      setIsLoadingTheme(true);
      try {
        console.log(`🎨 Loading color theme for activity: ${activity.title} (type: ${activity.type}, index: ${index})`);
        // Use enhanced service method with built-in fallback system
        const theme = await enhancedColorMappingService.getEnhancedColorTheme('activities', activity.type, index);
        console.log(`🎨 Color theme loaded for ${activity.title}:`, theme);
        setColorTheme(theme);
      } catch (error) {
        console.warn('Failed to load color theme from enhancedColorMappingService:', error);
        // Use vibrant fallback theme that matches visual excellence standards
        const fallbackTheme = {
          className: enhancedColorMappingService.getVibrantFallbackTheme(index),
          style: {}
        };
        console.log(`🎨 Using fallback theme for ${activity.title}:`, fallbackTheme);
        setColorTheme(fallbackTheme);
      } finally {
        setIsLoadingTheme(false);
      }
    };

    loadColorTheme();
  }, [activity.type, index]);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'meetup': return Users;
      case 'wellness': return Heart;
      case 'food': return Calendar;
      case 'challenge': return Trophy;
      case 'music': return Music;
      default: return Zap;
    }
  };

  const IconComponent = getActivityIcon(activity.type);

  const handleDetails = useCallback(() => {
    simulateHapticFeedback('light');
    onActivitySelect?.(activity);
  }, [onActivitySelect, activity]);

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.1 }}
        className={`group transition-all duration-300 ${isLoadingTheme ? 'opacity-90' : 'opacity-100'}`}
        data-testid="activity-card"
      >
        <div
          className={colorTheme.className}
          style={colorTheme.style}
        >
          <BentoCard
            title={activity.title}
            description={optimizeActivityDescription(activity.description, isMobileViewport())}
            variant="glassmorphism"
            interactive
            onClick={handleDetails}
            imageUrl={activity.image_url || undefined}
            className="bg-transparent border-transparent shadow-none"
            action={
            <UnifiedButton
              variant="primary"
              size="sm"
              onClick={handleDetails}
              className="text-xs min-h-[44px] px-6 touch-manipulation bg-gradient-to-r from-primary to-accent text-primary-foreground hover:opacity-90 shadow-lg font-medium"
            >
              <ChevronRight className="w-3 h-3 mr-1" />
              View Details
            </UnifiedButton>
          }
        >
          <div className="space-y-2">
            {/* Activity Details */}
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                <span>{activity.start_date ? new Date(activity.start_date).toLocaleDateString() : 'TBD'}</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="w-3 h-3" />
                <span>{activity.location || 'Location TBD'}</span>
              </div>
            </div>

            {/* Participant Count */}
            <ParticipantCount activityId={activity.id} className="text-xs" />

            {/* Activity Type Badge and User Interaction Buttons */}
            <div className="flex items-center justify-between gap-2">
              <ActivityTypeBadge
                type={activity.type}
                size="sm"
                overlayMode={false}
              />
              <div className="flex items-center gap-1">
                <UnifiedInteractionButton
                  type="rsvp"
                  itemId={activity.id}
                  itemType="activity"
                  variant="compact"
                  iconOnly
                  size="sm"
                  className="h-6 w-6"
                />
                <UnifiedInteractionButton
                  type="join"
                  itemId={activity.id}
                  itemType="activity"
                  variant="compact"
                  iconOnly
                  size="sm"
                  className="h-6"
                />
              </div>
            </div>
          </div>
        </BentoCard>
        </div>
      </motion.div>
    </>
  );
};

// Legacy ActivityTab component for backward compatibility
interface ActivityTabProps {
  title: string;
  description: string;
}

const ActivityTab: React.FC<ActivityTabProps> = ({ title, description }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold mb-2 text-foreground">{title}</h3>
        <p className="text-muted-foreground mb-4">{description}</p>
      </div>

      <Alert className="bg-primary/30 border-primary">
        <Terminal className="h-4 w-4" />
        <AlertTitle>Coming Soon</AlertTitle>
        <AlertDescription>
          Activities functionality will be available in a future update.
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="bg-card border-border">
            <CardContent className="p-4">
              <div className="h-32 flex items-center justify-center">
                <p className="text-muted-foreground">Activity placeholder {i}</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default Activities;

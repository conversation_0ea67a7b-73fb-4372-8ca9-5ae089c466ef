/**
 * Redis-Enhanced User Interactions Hook
 * 
 * Provides high-performance user interaction functionality with Redis caching
 * for instant feedback and real-time updates. Eliminates the subscription storm
 * and provides immediate visual feedback for user actions.
 * 
 * Features:
 * - Instant participant count updates with Redis caching
 * - Optimistic UI updates with Redis state management
 * - Rate limiting to prevent spam interactions
 * - Real-time updates via Redis pub/sub
 * - Graceful fallback to database when Redis is unavailable
 * 
 * @module useRedisUserInteractions
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/providers/ConsolidatedAuthProvider'
import { getRedis, withRedisFailover, RATE_LIMITS, trackRedisOperation } from '@/lib/redis/redis-config'
import { useUserInteractions } from '@/hooks/useUserInteractions'
import type { UserInteractionStatus, ParticipantCounts } from '@/lib/supabase/services/user-interaction-service'

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface RedisUserInteractionsReturn {
  // State
  isLoading: boolean
  error: string | null
  userStatus: UserInteractionStatus | null
  participantCount: number
  attendanceCounts: ParticipantCounts | null
  
  // Actions with Redis optimization
  joinActivity: () => Promise<void>
  leaveActivity: () => Promise<void>
  toggleFavorite: () => Promise<void>
  
  // Cache management
  refreshFromCache: () => Promise<void>
  clearCache: () => Promise<void>
  
  // Performance metrics
  cacheHitRate: number
  averageResponseTime: number
}

// ============================================================================
// REDIS USER INTERACTIONS HOOK
// ============================================================================

export function useRedisUserInteractions(activityId: string): RedisUserInteractionsReturn {
  const { user } = useAuth()
  const redis = getRedis()
  
  // Fallback to original hook for database operations
  const fallbackHook = useUserInteractions(activityId)
  
  // Redis-specific state
  const [participantCount, setParticipantCount] = useState<number>(0)
  const [userStatus, setUserStatus] = useState<UserInteractionStatus | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [cacheHitRate, setCacheHitRate] = useState(0)
  const [averageResponseTime, setAverageResponseTime] = useState(0)

  // ========================================================================
  // CACHE OPERATIONS
  // ========================================================================

  /**
   * Load participant count from Redis cache
   */
  const loadParticipantCount = useCallback(async () => {
    if (!redis) return fallbackHook.participantCount

    const startTime = performance.now()
    
    try {
      const cachedCount = await redis.getParticipantCount(activityId)
      const responseTime = performance.now() - startTime
      
      if (cachedCount !== null) {
        trackRedisOperation(true, responseTime)
        setParticipantCount(cachedCount)
        return cachedCount
      } else {
        trackRedisOperation(false, responseTime)
        // Cache miss - use database value and cache it
        const dbCount = fallbackHook.participantCount
        if (dbCount > 0) {
          await redis.cacheParticipantCount(activityId, dbCount)
        }
        setParticipantCount(dbCount)
        return dbCount
      }
    } catch (error) {
      const responseTime = performance.now() - startTime
      trackRedisOperation(false, responseTime, true)
      console.warn('🔴 Redis: Failed to load participant count, using fallback:', error)
      setParticipantCount(fallbackHook.participantCount)
      return fallbackHook.participantCount
    }
  }, [redis, activityId, fallbackHook.participantCount])

  /**
   * Load user interaction status from Redis cache
   */
  const loadUserStatus = useCallback(async () => {
    if (!redis || !user) return fallbackHook.userStatus

    const startTime = performance.now()
    
    try {
      const cachedStatus = await redis.getUserInteraction(user.id, activityId)
      const responseTime = performance.now() - startTime
      
      if (cachedStatus) {
        trackRedisOperation(true, responseTime)
        const status: UserInteractionStatus = {
          is_participant: cachedStatus.isParticipant,
          is_favorite: cachedStatus.isFavorite
        }
        setUserStatus(status)
        return status
      } else {
        trackRedisOperation(false, responseTime)
        // Cache miss - use database value and cache it
        const dbStatus = fallbackHook.userStatus
        if (dbStatus) {
          await redis.cacheUserInteraction(
            user.id, 
            activityId, 
            dbStatus.is_participant, 
            dbStatus.is_favorite
          )
        }
        setUserStatus(dbStatus)
        return dbStatus
      }
    } catch (error) {
      const responseTime = performance.now() - startTime
      trackRedisOperation(false, responseTime, true)
      console.warn('🔴 Redis: Failed to load user status, using fallback:', error)
      setUserStatus(fallbackHook.userStatus)
      return fallbackHook.userStatus
    }
  }, [redis, user, activityId, fallbackHook.userStatus])

  // ========================================================================
  // USER ACTIONS WITH REDIS OPTIMIZATION
  // ========================================================================

  /**
   * Join activity with Redis optimization
   */
  const joinActivity = useCallback(async () => {
    if (!user || !redis) {
      return fallbackHook.joinActivity()
    }

    setIsLoading(true)
    setError(null)

    try {
      // Check rate limit
      const rateLimit = await redis.checkRateLimit(
        user.id, 
        'join_activity', 
        RATE_LIMITS.JOIN_ACTIVITY.limit,
        RATE_LIMITS.JOIN_ACTIVITY.windowSeconds
      )

      if (!rateLimit.allowed) {
        throw new Error(`Rate limit exceeded. Try again in ${Math.ceil((rateLimit.resetTime - Date.now()) / 1000)} seconds.`)
      }

      // Optimistic update
      const newCount = await redis.incrementParticipantCount(activityId)
      setParticipantCount(newCount)
      
      await redis.cacheUserInteraction(user.id, activityId, true, userStatus?.is_favorite || false)
      setUserStatus(prev => ({ ...prev, is_participant: true, is_favorite: prev?.is_favorite || false }))

      // Add to activity feed
      await redis.addToActivityFeed({
        type: 'join',
        userId: user.id,
        activityId,
        metadata: { optimistic: true }
      })

      // Perform database update in background
      await fallbackHook.joinActivity()
      
    } catch (error) {
      // Revert optimistic update on error
      if (redis) {
        await redis.decrementParticipantCount(activityId)
        await redis.cacheUserInteraction(user.id, activityId, false, userStatus?.is_favorite || false)
      }
      setError(error instanceof Error ? error.message : 'Failed to join activity')
      console.error('🔴 Redis: Join activity failed:', error)
    } finally {
      setIsLoading(false)
    }
  }, [user, redis, activityId, userStatus, fallbackHook])

  /**
   * Leave activity with Redis optimization
   */
  const leaveActivity = useCallback(async () => {
    if (!user || !redis) {
      return fallbackHook.leaveActivity()
    }

    setIsLoading(true)
    setError(null)

    try {
      // Check rate limit
      const rateLimit = await redis.checkRateLimit(
        user.id, 
        'join_activity', 
        RATE_LIMITS.JOIN_ACTIVITY.limit,
        RATE_LIMITS.JOIN_ACTIVITY.windowSeconds
      )

      if (!rateLimit.allowed) {
        throw new Error(`Rate limit exceeded. Try again in ${Math.ceil((rateLimit.resetTime - Date.now()) / 1000)} seconds.`)
      }

      // Optimistic update
      const newCount = await redis.decrementParticipantCount(activityId)
      setParticipantCount(newCount)
      
      await redis.cacheUserInteraction(user.id, activityId, false, userStatus?.is_favorite || false)
      setUserStatus(prev => ({ ...prev, is_participant: false, is_favorite: prev?.is_favorite || false }))

      // Perform database update in background
      await fallbackHook.leaveActivity()
      
    } catch (error) {
      // Revert optimistic update on error
      if (redis) {
        await redis.incrementParticipantCount(activityId)
        await redis.cacheUserInteraction(user.id, activityId, true, userStatus?.is_favorite || false)
      }
      setError(error instanceof Error ? error.message : 'Failed to leave activity')
      console.error('🔴 Redis: Leave activity failed:', error)
    } finally {
      setIsLoading(false)
    }
  }, [user, redis, activityId, userStatus, fallbackHook])

  /**
   * Toggle favorite with Redis optimization
   */
  const toggleFavorite = useCallback(async () => {
    if (!user || !redis) {
      return fallbackHook.toggleFavorite()
    }

    setIsLoading(true)
    setError(null)

    try {
      // Check rate limit
      const rateLimit = await redis.checkRateLimit(
        user.id, 
        'favorite_activity', 
        RATE_LIMITS.FAVORITE_ACTIVITY.limit,
        RATE_LIMITS.FAVORITE_ACTIVITY.windowSeconds
      )

      if (!rateLimit.allowed) {
        throw new Error(`Rate limit exceeded. Try again in ${Math.ceil((rateLimit.resetTime - Date.now()) / 1000)} seconds.`)
      }

      // Optimistic update
      const newFavoriteStatus = !userStatus?.is_favorite
      await redis.cacheUserInteraction(user.id, activityId, userStatus?.is_participant || false, newFavoriteStatus)
      setUserStatus(prev => ({ ...prev, is_participant: prev?.is_participant || false, is_favorite: newFavoriteStatus }))

      // Add to activity feed
      await redis.addToActivityFeed({
        type: 'favorite',
        userId: user.id,
        activityId,
        metadata: { favorited: newFavoriteStatus }
      })

      // Perform database update in background
      await fallbackHook.toggleFavorite()
      
    } catch (error) {
      // Revert optimistic update on error
      if (redis && userStatus) {
        await redis.cacheUserInteraction(user.id, activityId, userStatus.is_participant, userStatus.is_favorite)
      }
      setError(error instanceof Error ? error.message : 'Failed to toggle favorite')
      console.error('🔴 Redis: Toggle favorite failed:', error)
    } finally {
      setIsLoading(false)
    }
  }, [user, redis, activityId, userStatus, fallbackHook])

  // ========================================================================
  // CACHE MANAGEMENT
  // ========================================================================

  /**
   * Refresh data from cache
   */
  const refreshFromCache = useCallback(async () => {
    await Promise.all([
      loadParticipantCount(),
      loadUserStatus()
    ])
  }, [loadParticipantCount, loadUserStatus])

  /**
   * Clear cache for this activity
   */
  const clearCache = useCallback(async () => {
    if (redis) {
      await redis.clearActivityCache(activityId)
    }
  }, [redis, activityId])

  // ========================================================================
  // EFFECTS
  // ========================================================================

  // Load initial data from cache
  useEffect(() => {
    refreshFromCache()
  }, [refreshFromCache])

  // Update performance metrics
  useEffect(() => {
    const { getRedisMetrics } = require('@/lib/redis/redis-config')
    const metrics = getRedisMetrics()
    
    if (metrics.totalOperations > 0) {
      setCacheHitRate(metrics.cacheHits / metrics.totalOperations)
      setAverageResponseTime(metrics.averageResponseTime)
    }
  }, [participantCount, userStatus])

  // ========================================================================
  // RETURN INTERFACE
  // ========================================================================

  return {
    // State
    isLoading: isLoading || fallbackHook.isLoading,
    error: error || fallbackHook.error,
    userStatus,
    participantCount,
    attendanceCounts: fallbackHook.attendanceCounts,
    
    // Actions
    joinActivity,
    leaveActivity,
    toggleFavorite,
    
    // Cache management
    refreshFromCache,
    clearCache,
    
    // Performance metrics
    cacheHitRate,
    averageResponseTime
  }
}

export default useRedisUserInteractions

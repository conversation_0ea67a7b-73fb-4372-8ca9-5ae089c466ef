/**
 * Festival Family - Weather Service
 *
 * This service provides real-time weather data and alerts using OpenWeatherMap API.
 * Integrates with the dashboard intelligence system to provide weather-aware
 * recommendations and safety alerts for festival-goers.
 *
 * @module WeatherService
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { BaseService, ServiceResponse } from '../supabase/services/base-service'

// ============================================================================
// WEATHER TYPES
// ============================================================================

export interface WeatherAlert {
  id: string
  type: 'warning' | 'watch' | 'advisory'
  title: string
  description: string
  severity: 'low' | 'medium' | 'high'
  validUntil: string
  location?: string
}

export interface WeatherCondition {
  id: number
  main: string
  description: string
  icon: string
}

export interface WeatherData {
  location: string
  temperature: number
  feelsLike: number
  humidity: number
  windSpeed: number
  windDirection: number
  visibility: number
  uvIndex?: number
  conditions: WeatherCondition[]
  alerts: WeatherAlert[]
  timestamp: string
}

export interface WeatherForecast {
  date: string
  tempMin: number
  tempMax: number
  conditions: WeatherCondition[]
  precipitationChance: number
}

// ============================================================================
// OPENWEATHERMAP API TYPES
// ============================================================================

interface OpenWeatherResponse {
  weather: Array<{
    id: number
    main: string
    description: string
    icon: string
  }>
  main: {
    temp: number
    feels_like: number
    humidity: number
  }
  wind: {
    speed: number
    deg: number
  }
  visibility: number
  name: string
  dt: number
}

interface OpenWeatherAlertsResponse {
  alerts?: Array<{
    sender_name: string
    event: string
    start: number
    end: number
    description: string
    tags: string[]
  }>
}

// ============================================================================
// WEATHER SERVICE
// ============================================================================

export class WeatherService extends BaseService {
  private readonly apiKey: string
  private readonly baseUrl = 'https://api.openweathermap.org/data/2.5'
  
  constructor(apiKey?: string) {
    super()
    // Use provided API key or environment variable (Vite uses import.meta.env)
    this.apiKey = apiKey || import.meta.env.OPENWEATHER_API_KEY || '********************************'
  }

  /**
   * Get current weather data for a location
   */
  async getCurrentWeather(location: string): Promise<ServiceResponse<WeatherData>> {
    try {
      const response = await fetch(
        `${this.baseUrl}/weather?q=${encodeURIComponent(location)}&appid=${this.apiKey}&units=metric`
      )

      if (!response.ok) {
        throw new Error(`Weather API error: ${response.status} ${response.statusText}`)
      }

      const data: OpenWeatherResponse = await response.json()
      
      // Get weather alerts for the location
      const alertsResponse = await this.getWeatherAlerts(location)
      
      const weatherData: WeatherData = {
        location: data.name,
        temperature: Math.round(data.main.temp),
        feelsLike: Math.round(data.main.feels_like),
        humidity: data.main.humidity,
        windSpeed: data.wind.speed,
        windDirection: data.wind.deg,
        visibility: data.visibility,
        conditions: data.weather.map(w => ({
          id: w.id,
          main: w.main,
          description: w.description,
          icon: w.icon
        })),
        alerts: alertsResponse.data || [],
        timestamp: new Date(data.dt * 1000).toISOString()
      }

      return {
        data: weatherData,
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error fetching weather data:', error)
      return {
        data: null,
        error: error as Error,
        status: 'error'
      }
    }
  }

  /**
   * Get weather alerts for a location
   */
  async getWeatherAlerts(location: string): Promise<ServiceResponse<WeatherAlert[]>> {
    try {
      // First get coordinates for the location
      const geoResponse = await fetch(
        `${this.baseUrl.replace('/data/2.5', '/geo/1.0')}/direct?q=${encodeURIComponent(location)}&limit=1&appid=${this.apiKey}`
      )

      if (!geoResponse.ok) {
        throw new Error(`Geocoding API error: ${geoResponse.status}`)
      }

      const geoData = await geoResponse.json()
      if (!geoData.length) {
        return {
          data: [],
          error: null,
          status: 'success'
        }
      }

      const { lat, lon } = geoData[0]

      // Get weather alerts using coordinates
      const alertsResponse = await fetch(
        `${this.baseUrl}/onecall?lat=${lat}&lon=${lon}&appid=${this.apiKey}&exclude=minutely,hourly,daily`
      )

      if (!alertsResponse.ok) {
        // If alerts API fails, return empty array instead of error
        return {
          data: [],
          error: null,
          status: 'success'
        }
      }

      const alertsData: OpenWeatherAlertsResponse = await alertsResponse.json()
      
      const alerts: WeatherAlert[] = (alertsData.alerts || []).map((alert, index) => ({
        id: `alert-${index}-${alert.start}`,
        type: this.mapAlertType(alert.event),
        title: alert.event,
        description: alert.description,
        severity: this.mapAlertSeverity(alert.tags),
        validUntil: new Date(alert.end * 1000).toISOString(),
        location
      }))

      return {
        data: alerts,
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error fetching weather alerts:', error)
      // Return empty array for alerts to not break the dashboard
      return {
        data: [],
        error: null,
        status: 'success'
      }
    }
  }

  /**
   * Get weather recommendations for festival activities
   */
  async getWeatherRecommendations(location: string): Promise<ServiceResponse<string[]>> {
    try {
      const weatherResponse = await this.getCurrentWeather(location)
      
      if (!weatherResponse.data) {
        return {
          data: [],
          error: null,
          status: 'success'
        }
      }

      const weather = weatherResponse.data
      const recommendations: string[] = []

      // Temperature-based recommendations
      if (weather.temperature > 30) {
        recommendations.push('🌡️ Very hot - stay hydrated and seek shade')
        recommendations.push('☀️ Consider indoor activities during peak hours')
      } else if (weather.temperature < 10) {
        recommendations.push('🧥 Cold weather - dress warmly in layers')
        recommendations.push('☕ Hot drinks recommended')
      }

      // Wind-based recommendations
      if (weather.windSpeed > 10) {
        recommendations.push('💨 Windy conditions - secure loose items')
      }

      // Condition-based recommendations
      const mainCondition = weather.conditions[0]?.main.toLowerCase()
      if (mainCondition?.includes('rain')) {
        recommendations.push('🌧️ Rain expected - bring waterproof gear')
        recommendations.push('☂️ Consider covered venues')
      } else if (mainCondition?.includes('snow')) {
        recommendations.push('❄️ Snow conditions - wear appropriate footwear')
      } else if (mainCondition?.includes('clear')) {
        recommendations.push('☀️ Perfect weather for outdoor activities!')
      }

      // Alert-based recommendations
      if (weather.alerts.length > 0) {
        recommendations.push('⚠️ Weather alerts active - check conditions regularly')
      }

      return {
        data: recommendations,
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting weather recommendations:', error)
      return {
        data: [],
        error: error as Error,
        status: 'error'
      }
    }
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  private mapAlertType(event: string): 'warning' | 'watch' | 'advisory' {
    const eventLower = event.toLowerCase()
    if (eventLower.includes('warning')) return 'warning'
    if (eventLower.includes('watch')) return 'watch'
    return 'advisory'
  }

  private mapAlertSeverity(tags: string[]): 'low' | 'medium' | 'high' {
    if (tags.some(tag => tag.toLowerCase().includes('extreme'))) return 'high'
    if (tags.some(tag => tag.toLowerCase().includes('severe'))) return 'high'
    if (tags.some(tag => tag.toLowerCase().includes('moderate'))) return 'medium'
    return 'low'
  }
}

// Export singleton instance
export const weatherService = new WeatherService()

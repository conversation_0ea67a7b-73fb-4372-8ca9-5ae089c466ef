import * as Sentry from '@sentry/react';

// Get environment variables in a Jest-compatible way
const getEnvVar = (key: string, defaultValue: string = '') => {
  // In test environment, use process.env
  if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') {
    return process.env[key] || defaultValue;
  }

  // In browser/Vite environment, use import.meta.env
  try {
    // Use eval to avoid TypeScript parsing issues with import.meta
    const importMeta = eval('import.meta');
    if (importMeta && importMeta.env) {
      return importMeta.env[key] || defaultValue;
    }
  } catch {
    // Fallback if import.meta is not available
  }

  return defaultValue;
};

const isProduction = () => {
  if (typeof process !== 'undefined' && process.env.NODE_ENV) {
    return process.env.NODE_ENV === 'production';
  }

  try {
    const importMeta = eval('import.meta');
    if (importMeta && importMeta.env) {
      return importMeta.env.PROD;
    }
  } catch {
    // Fallback if import.meta is not available
  }

  return false;
};

const getMode = () => {
  if (typeof process !== 'undefined' && process.env.NODE_ENV) {
    return process.env.NODE_ENV;
  }

  try {
    const importMeta = eval('import.meta');
    if (importMeta && importMeta.env) {
      return importMeta.env.MODE;
    }
  } catch {
    // Fallback if import.meta is not available
  }

  return 'development';
};

// Initialize Sentry for error monitoring
export const initSentry = () => {
  // Only initialize in production or when explicitly enabled
  const shouldInitialize =
    isProduction() ||
    getEnvVar('VITE_ENABLE_SENTRY') === 'true';

  if (!shouldInitialize) {
    console.log('Sentry disabled in development mode');
    return;
  }

  const dsn = getEnvVar('VITE_SENTRY_DSN');

  if (!dsn) {
    console.warn('Sentry DSN not configured');
    return;
  }

  Sentry.init({
    dsn,
    environment: getMode(),

    // Performance monitoring
    tracesSampleRate: isProduction() ? 0.1 : 1.0,
    
    // Session replay for debugging
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
    
    // Integrations
    integrations: [
      Sentry.browserTracingIntegration(),
      Sentry.replayIntegration(),
      Sentry.feedbackIntegration({
        colorScheme: 'dark',
        showBranding: false,
      }),
    ],
    
    // Filter out noise
    beforeSend(event) {
      // Filter out development errors
      if (event.exception) {
        const error = event.exception.values?.[0];
        if (error?.value?.includes('ResizeObserver loop limit exceeded')) {
          return null;
        }
        if (error?.value?.includes('Non-Error promise rejection captured')) {
          return null;
        }
      }
      
      return event;
    },
    
    // Set user context
    initialScope: {
      tags: {
        component: 'festival-family',
        mission: 'solo-festival-goers'
      },
    },
  });

  console.log('Sentry initialized for error monitoring');
};

// Helper function to capture user feedback
export const captureUserFeedback = (feedback: {
  name?: string;
  email?: string;
  message: string;
}) => {
  Sentry.captureFeedback({
    name: feedback.name || 'Anonymous',
    email: feedback.email || '<EMAIL>',
    message: feedback.message,
  });
};

// Helper function to set user context
export const setSentryUser = (user: {
  id: string;
  email?: string;
  username?: string;
  role?: string;
}) => {
  Sentry.setUser({
    id: user.id,
    email: user.email,
    username: user.username,
    role: user.role,
  });
};

// Helper function to add breadcrumb
export const addBreadcrumb = (message: string, category: string, level: 'info' | 'warning' | 'error' = 'info') => {
  Sentry.addBreadcrumb({
    message,
    category,
    level,
    timestamp: Date.now() / 1000,
  });
};

// Helper function to capture exception with context
export const captureException = (error: Error, context?: Record<string, any>) => {
  Sentry.withScope((scope) => {
    if (context) {
      Object.keys(context).forEach(key => {
        scope.setContext(key, context[key]);
      });
    }
    Sentry.captureException(error);
  });
};

export default Sentry;

/**
 * Admin Dashboard Comprehensive Validation
 * 
 * This script validates all admin dashboard functionality after the fixes
 * and tests the complete admin content management system.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Admin Dashboard Comprehensive Validation');
console.log('==========================================');

// Test 1: Database Tables Validation
async function validateDatabaseTables() {
  console.log('🗄️ Test 1: Database Tables Validation');
  console.log('-------------------------------------');
  
  const results = {
    tables: {},
    timestamp: new Date().toISOString()
  };
  
  const tables = [
    'announcements',
    'tips', 
    'faqs',
    'profiles',
    'festivals',
    'events',
    'activities',
    'groups',
    'group_members',
    'chat_rooms',
    'chat_room_members',
    'chat_messages'
  ];
  
  for (const table of tables) {
    try {
      const { data, error, count } = await supabase
        .from(table)
        .select('*', { count: 'exact' })
        .limit(1);
      
      if (error) {
        results.tables[table] = {
          accessible: false,
          error: error.message
        };
        console.log(`❌ ${table}: ${error.message}`);
      } else {
        results.tables[table] = {
          accessible: true,
          recordCount: count,
          hasData: data && data.length > 0
        };
        console.log(`✅ ${table}: ${count} records, accessible`);
      }
    } catch (err) {
      results.tables[table] = {
        accessible: false,
        error: err.message
      };
      console.log(`💥 ${table}: ${err.message}`);
    }
  }
  
  return results;
}

// Test 2: Admin Functions Validation
async function validateAdminFunctions() {
  console.log('\n🛡️ Test 2: Admin Functions Validation');
  console.log('------------------------------------');
  
  const results = {
    functions: {},
    timestamp: new Date().toISOString()
  };
  
  const functions = [
    { name: 'is_admin', params: {} },
    { name: 'is_super_admin', params: {} },
    { name: 'is_content_admin', params: {} },
    { name: 'can_manage_groups', params: {} }
  ];
  
  for (const func of functions) {
    try {
      const { data, error } = await supabase.rpc(func.name, func.params);
      
      if (error) {
        results.functions[func.name] = {
          working: false,
          error: error.message
        };
        console.log(`❌ ${func.name}: ${error.message}`);
      } else {
        results.functions[func.name] = {
          working: true,
          result: data
        };
        console.log(`✅ ${func.name}: Working (result: ${data})`);
      }
    } catch (err) {
      results.functions[func.name] = {
        working: false,
        error: err.message
      };
      console.log(`💥 ${func.name}: ${err.message}`);
    }
  }
  
  return results;
}

// Test 3: Content Management CRUD Operations
async function validateContentManagement() {
  console.log('\n📝 Test 3: Content Management CRUD Operations');
  console.log('--------------------------------------------');
  
  const results = {
    announcements: { create: false, read: false, update: false, delete: false },
    tips: { create: false, read: false, update: false, delete: false },
    faqs: { create: false, read: false, update: false, delete: false },
    timestamp: new Date().toISOString()
  };
  
  // Test Announcements CRUD
  console.log('📢 Testing Announcements CRUD...');
  try {
    // Create
    const { data: newAnnouncement, error: createError } = await supabase
      .from('announcements')
      .insert([{
        title: 'Test Announcement',
        content: 'This is a test announcement for validation.',
        type: 'info',
        priority: 'low',
        is_active: false
      }])
      .select()
      .single();
    
    if (!createError && newAnnouncement) {
      results.announcements.create = true;
      console.log('✅ Announcements CREATE: Working');
      
      // Read
      const { data: readAnnouncement, error: readError } = await supabase
        .from('announcements')
        .select('*')
        .eq('id', newAnnouncement.id)
        .single();
      
      if (!readError && readAnnouncement) {
        results.announcements.read = true;
        console.log('✅ Announcements READ: Working');
        
        // Update
        const { error: updateError } = await supabase
          .from('announcements')
          .update({ title: 'Updated Test Announcement' })
          .eq('id', newAnnouncement.id);
        
        if (!updateError) {
          results.announcements.update = true;
          console.log('✅ Announcements UPDATE: Working');
        }
        
        // Delete
        const { error: deleteError } = await supabase
          .from('announcements')
          .delete()
          .eq('id', newAnnouncement.id);
        
        if (!deleteError) {
          results.announcements.delete = true;
          console.log('✅ Announcements DELETE: Working');
        }
      }
    } else {
      console.log('❌ Announcements CREATE failed:', createError?.message);
    }
  } catch (err) {
    console.log('💥 Announcements CRUD error:', err.message);
  }
  
  // Test Tips CRUD
  console.log('💡 Testing Tips CRUD...');
  try {
    // Create
    const { data: newTip, error: createError } = await supabase
      .from('tips')
      .insert([{
        title: 'Test Tip',
        content: 'This is a test tip for validation.',
        category: 'GENERAL',
        is_active: false
      }])
      .select()
      .single();
    
    if (!createError && newTip) {
      results.tips.create = true;
      console.log('✅ Tips CREATE: Working');
      
      // Read
      const { data: readTip, error: readError } = await supabase
        .from('tips')
        .select('*')
        .eq('id', newTip.id)
        .single();
      
      if (!readError && readTip) {
        results.tips.read = true;
        console.log('✅ Tips READ: Working');
        
        // Update
        const { error: updateError } = await supabase
          .from('tips')
          .update({ title: 'Updated Test Tip' })
          .eq('id', newTip.id);
        
        if (!updateError) {
          results.tips.update = true;
          console.log('✅ Tips UPDATE: Working');
        }
        
        // Delete
        const { error: deleteError } = await supabase
          .from('tips')
          .delete()
          .eq('id', newTip.id);
        
        if (!deleteError) {
          results.tips.delete = true;
          console.log('✅ Tips DELETE: Working');
        }
      }
    } else {
      console.log('❌ Tips CREATE failed:', createError?.message);
    }
  } catch (err) {
    console.log('💥 Tips CRUD error:', err.message);
  }
  
  // Test FAQs CRUD
  console.log('❓ Testing FAQs CRUD...');
  try {
    // Create
    const { data: newFaq, error: createError } = await supabase
      .from('faqs')
      .insert([{
        question: 'Test Question?',
        answer: 'This is a test answer for validation.',
        category: 'GENERAL',
        is_active: false
      }])
      .select()
      .single();
    
    if (!createError && newFaq) {
      results.faqs.create = true;
      console.log('✅ FAQs CREATE: Working');
      
      // Read
      const { data: readFaq, error: readError } = await supabase
        .from('faqs')
        .select('*')
        .eq('id', newFaq.id)
        .single();
      
      if (!readError && readFaq) {
        results.faqs.read = true;
        console.log('✅ FAQs READ: Working');
        
        // Update
        const { error: updateError } = await supabase
          .from('faqs')
          .update({ question: 'Updated Test Question?' })
          .eq('id', newFaq.id);
        
        if (!updateError) {
          results.faqs.update = true;
          console.log('✅ FAQs UPDATE: Working');
        }
        
        // Delete
        const { error: deleteError } = await supabase
          .from('faqs')
          .delete()
          .eq('id', newFaq.id);
        
        if (!deleteError) {
          results.faqs.delete = true;
          console.log('✅ FAQs DELETE: Working');
        }
      }
    } else {
      console.log('❌ FAQs CREATE failed:', createError?.message);
    }
  } catch (err) {
    console.log('💥 FAQs CRUD error:', err.message);
  }
  
  return results;
}

// Test 4: Admin User Validation
async function validateAdminUsers() {
  console.log('\n👑 Test 4: Admin User Validation');
  console.log('--------------------------------');
  
  const results = {
    adminUsers: [],
    adminCount: 0,
    timestamp: new Date().toISOString()
  };
  
  try {
    const { data: adminUsers, error } = await supabase
      .from('profiles')
      .select('id, email, full_name, role, created_at')
      .in('role', ['SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR']);
    
    if (!error && adminUsers) {
      results.adminUsers = adminUsers;
      results.adminCount = adminUsers.length;
      
      console.log(`✅ Found ${adminUsers.length} admin users:`);
      adminUsers.forEach(user => {
        console.log(`   👤 ${user.email} (${user.role})`);
      });
    } else {
      console.log('❌ Admin users query failed:', error?.message);
    }
  } catch (err) {
    console.log('💥 Admin users validation error:', err.message);
  }
  
  return results;
}

// Main execution
async function main() {
  try {
    console.log(`🕐 Started at: ${new Date().toISOString()}\n`);
    
    // Run all validation tests
    const databaseResults = await validateDatabaseTables();
    const functionsResults = await validateAdminFunctions();
    const contentResults = await validateContentManagement();
    const usersResults = await validateAdminUsers();
    
    // Compile comprehensive results
    const validationResults = {
      validationSuite: 'Admin Dashboard Comprehensive Validation',
      timestamp: new Date().toISOString(),
      databaseTables: databaseResults,
      adminFunctions: functionsResults,
      contentManagement: contentResults,
      adminUsers: usersResults,
      overallStatus: {
        databaseTablesWorking: true,
        adminFunctionsWorking: true,
        contentManagementWorking: true,
        adminUsersFound: true,
        readyForProduction: false
      }
    };
    
    // Calculate overall status
    const tablesWorking = Object.values(databaseResults.tables).filter(t => t.accessible).length;
    const totalTables = Object.keys(databaseResults.tables).length;
    validationResults.overallStatus.databaseTablesWorking = (tablesWorking / totalTables) > 0.8;
    
    const functionsWorking = Object.values(functionsResults.functions).filter(f => f.working).length;
    const totalFunctions = Object.keys(functionsResults.functions).length;
    validationResults.overallStatus.adminFunctionsWorking = (functionsWorking / totalFunctions) > 0.5;
    
    const contentWorking = Object.values(contentResults).filter(content => 
      content.create && content.read && content.update && content.delete
    ).length;
    validationResults.overallStatus.contentManagementWorking = contentWorking >= 2; // At least 2 content types working
    
    validationResults.overallStatus.adminUsersFound = usersResults.adminCount > 0;
    
    validationResults.overallStatus.readyForProduction = 
      validationResults.overallStatus.databaseTablesWorking &&
      validationResults.overallStatus.adminFunctionsWorking &&
      validationResults.overallStatus.contentManagementWorking &&
      validationResults.overallStatus.adminUsersFound;
    
    // Save results
    const resultsDir = 'admin-dashboard-validation-results';
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      `${resultsDir}/admin-validation-${Date.now()}.json`,
      JSON.stringify(validationResults, null, 2)
    );
    
    console.log('\n🎉 ADMIN DASHBOARD VALIDATION SUMMARY');
    console.log('====================================');
    
    console.log('\n🗄️ DATABASE TABLES:');
    Object.entries(databaseResults.tables).forEach(([table, status]) => {
      const statusText = status.accessible ? `✅ ${status.recordCount} records` : `❌ ${status.error}`;
      console.log(`   ${table}: ${statusText}`);
    });
    
    console.log('\n🛡️ ADMIN FUNCTIONS:');
    Object.entries(functionsResults.functions).forEach(([func, status]) => {
      const statusText = status.working ? 'WORKING' : 'ISSUES';
      console.log(`   ${func}: ${statusText}`);
    });
    
    console.log('\n📝 CONTENT MANAGEMENT:');
    Object.entries(contentResults).forEach(([content, operations]) => {
      if (typeof operations === 'object' && operations.create !== undefined) {
        const workingOps = Object.values(operations).filter(Boolean).length;
        console.log(`   ${content}: ${workingOps}/4 operations working`);
      }
    });
    
    console.log('\n👑 ADMIN USERS:');
    console.log(`   Found: ${usersResults.adminCount} admin users`);
    
    console.log('\n🎯 OVERALL STATUS:');
    console.log(`   Database Tables: ${validationResults.overallStatus.databaseTablesWorking ? 'WORKING' : 'ISSUES'}`);
    console.log(`   Admin Functions: ${validationResults.overallStatus.adminFunctionsWorking ? 'WORKING' : 'ISSUES'}`);
    console.log(`   Content Management: ${validationResults.overallStatus.contentManagementWorking ? 'WORKING' : 'ISSUES'}`);
    console.log(`   Admin Users: ${validationResults.overallStatus.adminUsersFound ? 'FOUND' : 'MISSING'}`);
    console.log(`   Production Ready: ${validationResults.overallStatus.readyForProduction ? 'YES' : 'NO'}`);
    
    console.log(`\n📁 Results saved to: ${resultsDir}/admin-validation-${Date.now()}.json`);
    
  } catch (error) {
    console.error('💥 Admin dashboard validation failed:', error);
  }
  
  process.exit(0);
}

main();

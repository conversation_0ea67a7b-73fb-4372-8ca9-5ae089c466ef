import React from 'react';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Pencil, Trash2, ToggleLeft, ToggleRight } from 'lucide-react';
import { Database } from '@/types/supabase';
import { UnifiedBadge } from '@/components/design-system';
import { adminTextStyles } from '@/lib/utils/admin-styles';

type Event = Database['public']['Tables']['events']['Row'];

interface EventTableProps {
  events: Event[];
  onEdit?: (event: Event) => void;
  onDelete?: (id: string) => void;
  onToggleStatus?: (event: Event) => void;
  isLoading?: boolean;
}

export const EventTable: React.FC<EventTableProps> = ({
  events,
  onEdit,
  onDelete,
  onToggleStatus,
  isLoading = false
}) => {
  const getStatusBadge = (event: Event) => {
    const status = event.status ?? 'DRAFT';
    
    switch (status) {
      case 'PUBLISHED':
        return <UnifiedBadge variant="success" size="sm">Published</UnifiedBadge>;
      case 'ARCHIVED':
        return <UnifiedBadge variant="secondary" size="sm">Archived</UnifiedBadge>;
      case 'DRAFT':
        return <UnifiedBadge variant="destructive" size="sm">Draft</UnifiedBadge>;
      default:
        return <UnifiedBadge variant="secondary" size="sm">{status}</UnifiedBadge>;
    }
  };

  const getActiveBadge = (event: Event) => {
    return event.is_active 
      ? <UnifiedBadge variant="success" size="sm">Active</UnifiedBadge>
      : <UnifiedBadge variant="secondary" size="sm">Inactive</UnifiedBadge>;
  };

  if (isLoading) {
    return (
      <div className="rounded-lg border border-border bg-card shadow-sm overflow-hidden">
        <div className="p-8 text-center">
          <span className={adminTextStyles.tableContentSecondary}>Loading events...</span>
        </div>
      </div>
    );
  }

  if (events.length === 0) {
    return (
      <div className="rounded-lg border border-border bg-card shadow-sm overflow-hidden">
        <div className="p-8 text-center">
          <span className={adminTextStyles.tableContentSecondary}>
            No events found. Create your first event to get started.
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-border bg-card shadow-sm overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="border-b border-border bg-muted/50 hover:bg-muted/70">
            <TableHead className="font-semibold text-foreground">Title</TableHead>
            <TableHead className="font-semibold text-foreground hidden sm:table-cell">Date Range</TableHead>
            <TableHead className="font-semibold text-foreground hidden md:table-cell">Status</TableHead>
            <TableHead className="font-semibold text-foreground">Active</TableHead>
            <TableHead className="font-semibold text-foreground text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {events.map((event) => (
            <TableRow key={event.id} className="border-b border-border hover:bg-muted/30 transition-colors">
              <TableCell className="font-medium text-foreground py-4">
                <div className="flex flex-col">
                  <span className="font-semibold">{event.title}</span>
                  <div className="flex flex-col gap-1 mt-1 sm:hidden">
                    <span className="text-xs text-muted-foreground">
                      {format(new Date(event.start_date), 'MMM dd, yyyy')} - {format(new Date(event.end_date), 'MMM dd, yyyy')}
                    </span>
                    <div className="flex gap-2 md:hidden">
                      {getStatusBadge(event)}
                    </div>
                  </div>
                </div>
              </TableCell>
              <TableCell className="text-muted-foreground hidden sm:table-cell">
                <div className="flex flex-col gap-1">
                  <span className="text-sm">
                    {format(new Date(event.start_date), 'MMM dd, yyyy')}
                  </span>
                  <span className="text-sm">
                    {format(new Date(event.end_date), 'MMM dd, yyyy')}
                  </span>
                </div>
              </TableCell>
              <TableCell className="hidden md:table-cell py-4">
                {getStatusBadge(event)}
              </TableCell>
              <TableCell className="py-4">
                {getActiveBadge(event)}
              </TableCell>
              <TableCell className="text-right py-4">
                <div className="flex items-center justify-end gap-1">
                  {onToggleStatus && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onToggleStatus(event)}
                      className="h-9 w-9 p-0 hover:bg-muted rounded-md transition-colors"
                      title={event.is_active ? 'Deactivate event' : 'Activate event'}
                    >
                      {event.is_active ? (
                        <ToggleRight className="h-4 w-4 text-success" />
                      ) : (
                        <ToggleLeft className="h-4 w-4 text-muted-foreground" />
                      )}
                      <span className="sr-only">Toggle event status</span>
                    </Button>
                  )}
                  {onEdit && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(event)}
                      className="h-9 w-9 p-0 hover:bg-muted rounded-md transition-colors"
                    >
                      <Pencil className="h-4 w-4 text-muted-foreground hover:text-foreground" />
                      <span className="sr-only">Edit event</span>
                    </Button>
                  )}
                  {onDelete && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDelete(event.id)}
                      className="h-9 w-9 p-0 text-destructive hover:text-destructive hover:bg-destructive/10 rounded-md transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">Delete event</span>
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

/**
 * Festival Family - Intelligence Services
 *
 * This module exports all intelligence services for smart content curation,
 * personalization, and user experience enhancement.
 *
 * @module IntelligenceServices
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

// ============================================================================
// DASHBOARD INTELLIGENCE
// ============================================================================
export * from './dashboard-service'
export type {
  DashboardIntelligence,
  PersonalizedContent,
  LiveUpdate,
  QuickAction,
  CommunityBuzz
} from './dashboard-service'

// ============================================================================
// RECOMMENDATION ENGINE
// ============================================================================
export * from './recommendation-engine'
export type {
  UserPreferences,
  RecommendationScore,
  EventRecommendation,
  ActivityRecommendation,
  RecommendationFilters
} from './recommendation-engine'

// ============================================================================
// WEATHER SERVICE
// ============================================================================
export * from './weather-service'
export type {
  WeatherAlert,
  WeatherData,
  WeatherCondition,
  WeatherForecast
} from './weather-service'

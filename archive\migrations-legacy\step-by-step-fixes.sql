-- Step-by-Step Database Fixes for Festival Family
-- Run each section separately in Supabase SQL Editor
-- Check results after each step before proceeding

-- ============================================================================
-- STEP 1: CHECK CURRENT STATE
-- ============================================================================

-- Run this first to see what we have:
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;

-- ============================================================================
-- STEP 2: FIX FAQ TABLE (Add missing columns)
-- ============================================================================

-- Add category column to FAQ table
ALTER TABLE faqs ADD COLUMN IF NOT EXISTS category TEXT DEFAULT 'GENERAL';

-- Add order_index column to FAQ table  
ALTER TABLE faqs ADD COLUMN IF NOT EXISTS order_index INTEGER DEFAULT 0;

-- Add created_by column to FAQ table
ALTER TABLE faqs ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES profiles(id) ON DELETE SET NULL;

-- Verify FAQ table structure
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'faqs' 
ORDER BY ordinal_position;

-- ============================================================================
-- STEP 3: CREATE MISSING ADMIN FUNCTIONS
-- ============================================================================

-- Create is_super_admin function
CREATE OR REPLACE FUNCTION is_super_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = user_id 
        AND role = 'SUPER_ADMIN'
    );
END;
$$;

-- Create is_content_admin function
CREATE OR REPLACE FUNCTION is_content_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = user_id 
        AND role IN ('SUPER_ADMIN', 'CONTENT_ADMIN')
    );
END;
$$;

-- Create can_manage_groups function
CREATE OR REPLACE FUNCTION can_manage_groups(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = user_id 
        AND role IN ('SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR')
    );
END;
$$;

-- Test the functions
SELECT 
    'is_admin' as function_name,
    is_admin() as result;

SELECT 
    'is_super_admin' as function_name,
    is_super_admin() as result;

SELECT 
    'is_content_admin' as function_name,
    is_content_admin() as result;

SELECT 
    'can_manage_groups' as function_name,
    can_manage_groups() as result;

-- ============================================================================
-- STEP 4: CREATE CONTENT MANAGEMENT TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS content_management (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    content_key TEXT NOT NULL UNIQUE,
    content_type TEXT NOT NULL,
    title TEXT,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    version INTEGER DEFAULT 1,
    language TEXT DEFAULT 'en',
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add check constraint for content_type
ALTER TABLE content_management ADD CONSTRAINT content_type_check 
CHECK (content_type IN ('hero', 'marketing', 'contact', 'emergency', 'page_content', 'ui_text'));

-- ============================================================================
-- STEP 5: CREATE USER PREFERENCES TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    preference_category TEXT NOT NULL,
    preference_key TEXT NOT NULL,
    preference_value JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, preference_category, preference_key)
);

-- Add check constraint for preference_category
ALTER TABLE user_preferences ADD CONSTRAINT preference_category_check 
CHECK (preference_category IN ('notifications', 'privacy', 'festival', 'communication', 'accessibility'));

-- ============================================================================
-- STEP 6: CREATE EMERGENCY CONTACTS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS emergency_contacts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    festival_id UUID REFERENCES festivals(id) ON DELETE CASCADE,
    contact_type TEXT NOT NULL,
    name TEXT NOT NULL,
    phone TEXT,
    email TEXT,
    description TEXT,
    is_primary BOOLEAN DEFAULT false,
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add check constraint for contact_type
ALTER TABLE emergency_contacts ADD CONSTRAINT contact_type_check 
CHECK (contact_type IN ('festival_organizer', 'medical', 'security', 'local_emergency', 'festival_family'));

-- ============================================================================
-- STEP 7: CREATE SAFETY INFORMATION TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS safety_information (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    festival_id UUID REFERENCES festivals(id) ON DELETE CASCADE,
    safety_category TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    priority TEXT DEFAULT 'medium',
    is_alert BOOLEAN DEFAULT false,
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add check constraints
ALTER TABLE safety_information ADD CONSTRAINT safety_category_check 
CHECK (safety_category IN ('general', 'medical', 'security', 'weather', 'transportation', 'emergency_procedures'));

ALTER TABLE safety_information ADD CONSTRAINT priority_check 
CHECK (priority IN ('low', 'medium', 'high', 'critical'));

-- ============================================================================
-- STEP 8: ENABLE RLS AND CREATE POLICIES
-- ============================================================================

-- Enable RLS on new tables
ALTER TABLE content_management ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE emergency_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE safety_information ENABLE ROW LEVEL SECURITY;

-- Content management policies
CREATE POLICY "Anyone can view active content" 
ON content_management FOR SELECT 
USING (is_active = true);

CREATE POLICY "Content admins can manage content" 
ON content_management FOR ALL 
USING (is_content_admin(auth.uid()));

-- User preferences policies
CREATE POLICY "Users can view their own preferences" 
ON user_preferences FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own preferences" 
ON user_preferences FOR ALL 
USING (auth.uid() = user_id);

-- Emergency contacts policies
CREATE POLICY "Anyone can view active emergency contacts" 
ON emergency_contacts FOR SELECT 
USING (is_active = true);

CREATE POLICY "Content admins can manage emergency contacts" 
ON emergency_contacts FOR ALL 
USING (is_content_admin(auth.uid()));

-- Safety information policies
CREATE POLICY "Anyone can view active safety information" 
ON safety_information FOR SELECT 
USING (is_active = true);

CREATE POLICY "Content admins can manage safety information" 
ON safety_information FOR ALL 
USING (is_content_admin(auth.uid()));

-- ============================================================================
-- STEP 9: ADD SAMPLE DATA
-- ============================================================================

-- Insert sample content
INSERT INTO content_management (content_key, content_type, title, content, metadata) VALUES
('hero_title', 'hero', 'Welcome to Festival Family', 'Find your tribe at festivals worldwide', '{"editable": true}'),
('contact_email', 'contact', 'Contact Email', '<EMAIL>', '{"editable": true}')
ON CONFLICT (content_key) DO NOTHING;

-- Insert sample FAQ with new columns
INSERT INTO faqs (question, answer, category, order_index) VALUES
('How do I join activities?', 'Browse the activities section and click Join on any activity.', 'FESTIVAL', 1),
('What should I pack?', 'Check our packing guide in the Tips section.', 'GENERAL', 2)
ON CONFLICT DO NOTHING;

-- ============================================================================
-- STEP 10: FINAL VERIFICATION
-- ============================================================================

-- Check all tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('announcements', 'tips', 'faqs', 'content_management', 'user_preferences', 'emergency_contacts', 'safety_information')
ORDER BY table_name;

-- Check all functions exist
SELECT routine_name FROM information_schema.routines 
WHERE routine_schema = 'public' AND routine_type = 'FUNCTION'
AND routine_name IN ('is_admin', 'is_super_admin', 'is_content_admin', 'can_manage_groups')
ORDER BY routine_name;

-- Test all functions work
SELECT 
    'Functions Test' as test_type,
    is_admin() as is_admin_result,
    is_super_admin() as is_super_admin_result,
    is_content_admin() as is_content_admin_result,
    can_manage_groups() as can_manage_groups_result;

-- Final success message
SELECT 'DATABASE SETUP COMPLETE!' as status;

import React from 'react'
import { render, screen } from '@testing-library/react'

// Simple test component
const TestComponent: React.FC = () => {
  return <div data-testid="test-component">Hello Test</div>
}

describe('Simple Test', () => {
  test('should render test component', () => {
    render(<TestComponent />)
    expect(screen.getByTestId('test-component')).toBeInTheDocument()
    expect(screen.getByText('Hello Test')).toBeInTheDocument()
  })
})

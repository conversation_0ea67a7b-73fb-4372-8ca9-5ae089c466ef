/**
 * Admin Functionality Testing
 * 
 * This test focuses specifically on admin dashboard and content management features:
 * - Admin authentication and access control
 * - Content management forms (festivals, activities, announcements)
 * - User management capabilities
 * - Admin-specific navigation and features
 * 
 * Evidence Collection:
 * - Screenshots of admin interfaces
 * - Form submission results
 * - Database state verification via Supabase MCP
 * - Performance metrics for admin operations
 */

import { test, expect } from '@playwright/test';
import {
  AdminAuthenticator,
  AdminEvidenceCollector,
  AdminNavigationHelper,
  AdminContentManager,
  AdminFormHelper,
  AdminTestDataGenerator,
  AdminPerformanceTracker
} from './utils/admin-test-helpers.js';

// Test configuration
const ADMIN_CONFIG = {
  credentials: {
    email: '<EMAIL>',
    password: 'testpassword123'
  },
  timeout: 60000
};

// Initialize helpers
let adminAuth;
let evidenceCollector;
let navigationHelper;
let contentManager;
let formHelper;
let dataGenerator;
let performanceTracker;

test.beforeAll(async () => {
  adminAuth = new AdminAuthenticator(ADMIN_CONFIG.credentials);
  evidenceCollector = new AdminEvidenceCollector();
  navigationHelper = new AdminNavigationHelper();
  contentManager = new AdminContentManager();
  formHelper = new AdminFormHelper();
  dataGenerator = new AdminTestDataGenerator();
  performanceTracker = new AdminPerformanceTracker();
});

test.describe('Admin Functionality Testing', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('Admin Authentication and Access Control', async ({ page }) => {
    console.log('🔐 Testing Admin Authentication...');
    
    await test.step('Admin Login Process', async () => {
      const authSuccess = await adminAuth.authenticateAsAdmin(page);
      
      if (authSuccess) {
        console.log('✅ Admin authentication successful');
        
        // Look for admin-specific elements
        const adminElements = await page.locator('[class*="admin"], [data-testid*="admin"], a[href*="admin"], button:has-text("Admin")').all();
        console.log(`Found ${adminElements.length} potential admin elements`);
        
        await evidenceCollector.takeEvidence(page, 'admin-elements-found', 'Admin elements after authentication');
      } else {
        console.log('❌ Admin authentication failed or not available');
      }
    });
    
    await test.step('Admin Dashboard Access', async () => {
      // Try to navigate to admin dashboard
      const adminPaths = ['/admin', '/dashboard', '/admin/dashboard'];
      
      for (const path of adminPaths) {
        try {
          await page.goto(path);
          await page.waitForTimeout(2000);
          
          const currentUrl = page.url();
          console.log(`Navigated to: ${currentUrl}`);
          
          await evidenceCollector.takeEvidence(page, `admin-path-${path.replace(/\//g, '-')}`, `Admin path: ${path}`);
          
          // Check for admin content
          const adminContent = await page.locator('h1, h2, h3').allTextContents();
          console.log(`Admin content headers: ${adminContent.join(', ')}`);
          
        } catch (error) {
          console.log(`❌ Failed to access admin path ${path}: ${error.message}`);
        }
      }
    });
  });

  test('Admin Content Management Forms', async ({ page }) => {
    console.log('📝 Testing Admin Content Management...');
    
    await adminAuth.authenticateAsAdmin(page);
    
    await test.step('Festival Management Forms', async () => {
      // Look for festival management elements
      const festivalElements = await page.locator('button:has-text("Add Festival"), button:has-text("Create Festival"), a:has-text("Festival"), [data-testid*="festival"]').all();
      console.log(`Found ${festivalElements.length} festival management elements`);
      
      await evidenceCollector.takeEvidence(page, 'festival-management', 'Festival management interface');
      
      // Test festival form interactions
      for (let i = 0; i < Math.min(festivalElements.length, 3); i++) {
        const element = festivalElements[i];
        
        if (await element.isVisible()) {
          try {
            await performanceTracker.measureAdminPerformance(page, async () => {
              await element.click();
            }, `Festival Element ${i + 1}`);
            
            await page.waitForTimeout(2000);
            await evidenceCollector.takeEvidence(page, `festival-form-${i}`, `Festival form ${i + 1} opened`);
            
            // Look for form inputs
            const formInputs = await page.locator('input, textarea, select').all();
            console.log(`Found ${formInputs.length} form inputs`);
            
            // Test filling form inputs using helper
            const festivalData = dataGenerator.generateFestivalData();
            await formHelper.fillFormInputs(page, formInputs, festivalData);
            
            await evidenceCollector.takeEvidence(page, `festival-form-${i}-filled`, `Festival form ${i + 1} filled`);
          } catch (error) {
            console.log(`❌ Festival element ${i + 1} interaction failed: ${error.message}`);
          }
        }
      }
    });
    
    await test.step('Activity Management Forms', async () => {
      // Look for activity management elements
      const activityElements = await page.locator('button:has-text("Add Activity"), button:has-text("Create Activity"), a:has-text("Activity"), [data-testid*="activity"]').all();
      console.log(`Found ${activityElements.length} activity management elements`);
      
      await evidenceCollector.takeEvidence(page, 'activity-management', 'Activity management interface');
      
      // Test activity form interactions
      for (let i = 0; i < Math.min(activityElements.length, 3); i++) {
        const element = activityElements[i];
        
        if (await element.isVisible()) {
          try {
            await performanceTracker.measureAdminPerformance(page, async () => {
              await element.click();
            }, `Activity Element ${i + 1}`);
            
            await page.waitForTimeout(2000);
            await evidenceCollector.takeEvidence(page, `activity-form-${i}`, `Activity form ${i + 1} opened`);
            
          } catch (error) {
            console.log(`❌ Activity element ${i + 1} interaction failed: ${error.message}`);
          }
        }
      }
    });
  });

  test('Admin User Management', async ({ page }) => {
    console.log('👥 Testing Admin User Management...');
    
    await adminAuth.authenticateAsAdmin(page);
    
    await test.step('User Management Interface', async () => {
      // Look for user management elements
      const userElements = await page.locator('button:has-text("Users"), a:has-text("Users"), button:has-text("Manage Users"), [data-testid*="user"]').all();
      console.log(`Found ${userElements.length} user management elements`);
      
      await evidenceCollector.takeEvidence(page, 'user-management', 'User management interface');
      
      // Test user management interactions
      for (let i = 0; i < Math.min(userElements.length, 3); i++) {
        const element = userElements[i];
        
        if (await element.isVisible()) {
          try {
            await performanceTracker.measureAdminPerformance(page, async () => {
              await element.click();
            }, `User Management Element ${i + 1}`);
            
            await page.waitForTimeout(2000);
            await evidenceCollector.takeEvidence(page, `user-management-${i}`, `User management ${i + 1} opened`);
            
          } catch (error) {
            console.log(`❌ User management element ${i + 1} interaction failed: ${error.message}`);
          }
        }
      }
    });
  });

  test('Admin Announcements System', async ({ page }) => {
    console.log('📢 Testing Admin Announcements...');
    
    await adminAuth.authenticateAsAdmin(page);
    
    await test.step('Announcements Management', async () => {
      // Look for announcements management elements
      const announcementElements = await page.locator('button:has-text("Announcement"), a:has-text("Announcement"), button:has-text("Create Announcement"), [data-testid*="announcement"]').all();
      console.log(`Found ${announcementElements.length} announcement management elements`);
      
      await evidenceCollector.takeEvidence(page, 'announcements-management', 'Announcements management interface');
      
      // Test announcements interactions
      for (let i = 0; i < Math.min(announcementElements.length, 3); i++) {
        const element = announcementElements[i];
        
        if (await element.isVisible()) {
          try {
            await performanceTracker.measureAdminPerformance(page, async () => {
              await element.click();
            }, `Announcement Element ${i + 1}`);
            
            await page.waitForTimeout(2000);
            await evidenceCollector.takeEvidence(page, `announcement-form-${i}`, `Announcement form ${i + 1} opened`);
            
          } catch (error) {
            console.log(`❌ Announcement element ${i + 1} interaction failed: ${error.message}`);
          }
        }
      }
    });
  });

  test('Admin Navigation and Context Switching', async ({ page }) => {
    console.log('🧭 Testing Admin Navigation...');
    
    await adminAuth.authenticateAsAdmin(page);
    
    await test.step('Admin Navigation Testing', async () => {
      // Test admin-specific navigation using helper
      const adminNavElements = await page.locator('nav a, [role="navigation"] a').all();
      console.log(`Found ${adminNavElements.length} navigation elements`);
      
      await evidenceCollector.takeEvidence(page, 'admin-navigation', 'Admin navigation elements');
      
      // Test admin navigation links using helper
      await navigationHelper.testAdminNavigation(page, adminNavElements, {
        maxElements: 8,
        performanceTracker,
        evidenceCollector
      });
    });
  });
});

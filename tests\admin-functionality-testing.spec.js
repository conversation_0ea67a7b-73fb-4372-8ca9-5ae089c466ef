/**
 * Admin Functionality Testing
 * 
 * This test focuses specifically on admin dashboard and content management features:
 * - Admin authentication and access control
 * - Content management forms (festivals, activities, announcements)
 * - User management capabilities
 * - Admin-specific navigation and features
 * 
 * Evidence Collection:
 * - Screenshots of admin interfaces
 * - Form submission results
 * - Database state verification via Supabase MCP
 * - Performance metrics for admin operations
 */

import { test, expect } from '@playwright/test';

// Admin test configuration
const ADMIN_CONFIG = {
  credentials: {
    email: '<EMAIL>',
    password: 'testpassword123'
  },
  timeout: 60000
};

// Helper functions
async function takeEvidence(page, name, description) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `admin-evidence-${name}-${timestamp}.png`;
  await page.screenshot({ 
    path: `test-results/${filename}`,
    fullPage: true 
  });
  console.log(`📸 Admin Evidence: ${filename} - ${description}`);
  return filename;
}

async function measureAdminPerformance(page, action, actionName) {
  const startTime = Date.now();
  await action();
  const endTime = Date.now();
  const duration = endTime - startTime;
  console.log(`⏱️ Admin Performance [${actionName}]: ${duration}ms`);
  return duration;
}

async function authenticateAsAdmin(page) {
  console.log('🔐 Authenticating as admin...');
  
  // Navigate to login
  await page.goto('/');
  await page.waitForLoadState('networkidle');
  
  // Look for authentication elements
  const loginButton = page.locator('button:has-text("Login"), button:has-text("Sign In"), a:has-text("Login"), a:has-text("Sign In")').first();
  
  if (await loginButton.isVisible()) {
    await loginButton.click();
    await page.waitForTimeout(2000);
    
    // Fill credentials
    const emailInput = page.locator('input[type="email"], input[placeholder*="email" i]').first();
    const passwordInput = page.locator('input[type="password"], input[placeholder*="password" i]').first();
    
    if (await emailInput.isVisible() && await passwordInput.isVisible()) {
      await emailInput.fill(ADMIN_CONFIG.credentials.email);
      await passwordInput.fill(ADMIN_CONFIG.credentials.password);
      
      const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
      if (await submitButton.isVisible()) {
        await submitButton.click();
        await page.waitForTimeout(3000);
        await takeEvidence(page, 'admin-authenticated', 'Admin authentication completed');
        return true;
      }
    }
  }
  
  console.log('ℹ️ Could not find login form or already authenticated');
  return false;
}

test.describe('Admin Functionality Testing', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('Admin Authentication and Access Control', async ({ page }) => {
    console.log('🔐 Testing Admin Authentication...');
    
    await test.step('Admin Login Process', async () => {
      const authSuccess = await authenticateAsAdmin(page);
      
      if (authSuccess) {
        console.log('✅ Admin authentication successful');
        
        // Look for admin-specific elements
        const adminElements = await page.locator('[class*="admin"], [data-testid*="admin"], a[href*="admin"], button:has-text("Admin")').all();
        console.log(`Found ${adminElements.length} potential admin elements`);
        
        await takeEvidence(page, 'admin-elements-found', 'Admin elements after authentication');
      } else {
        console.log('❌ Admin authentication failed or not available');
      }
    });
    
    await test.step('Admin Dashboard Access', async () => {
      // Try to navigate to admin dashboard
      const adminPaths = ['/admin', '/dashboard', '/admin/dashboard'];
      
      for (const path of adminPaths) {
        try {
          await page.goto(path);
          await page.waitForTimeout(2000);
          
          const currentUrl = page.url();
          console.log(`Navigated to: ${currentUrl}`);
          
          await takeEvidence(page, `admin-path-${path.replace(/\//g, '-')}`, `Admin path: ${path}`);
          
          // Check for admin content
          const adminContent = await page.locator('h1, h2, h3').allTextContents();
          console.log(`Admin content headers: ${adminContent.join(', ')}`);
          
        } catch (error) {
          console.log(`❌ Failed to access admin path ${path}: ${error.message}`);
        }
      }
    });
  });

  test('Admin Content Management Forms', async ({ page }) => {
    console.log('📝 Testing Admin Content Management...');
    
    await authenticateAsAdmin(page);
    
    await test.step('Festival Management Forms', async () => {
      // Look for festival management elements
      const festivalElements = await page.locator('button:has-text("Add Festival"), button:has-text("Create Festival"), a:has-text("Festival"), [data-testid*="festival"]').all();
      console.log(`Found ${festivalElements.length} festival management elements`);
      
      await takeEvidence(page, 'festival-management', 'Festival management interface');
      
      // Test festival form interactions
      for (let i = 0; i < Math.min(festivalElements.length, 3); i++) {
        const element = festivalElements[i];
        
        if (await element.isVisible()) {
          try {
            await measureAdminPerformance(page, async () => {
              await element.click();
            }, `Festival Element ${i + 1}`);
            
            await page.waitForTimeout(2000);
            await takeEvidence(page, `festival-form-${i}`, `Festival form ${i + 1} opened`);
            
            // Look for form inputs
            const formInputs = await page.locator('input, textarea, select').all();
            console.log(`Found ${formInputs.length} form inputs`);
            
            // Test filling form inputs
            for (let j = 0; j < Math.min(formInputs.length, 5); j++) {
              const input = formInputs[j];
              const inputType = await input.getAttribute('type');
              const inputName = await input.getAttribute('name') || await input.getAttribute('placeholder');
              
              try {
                if (inputType === 'text' || inputType === 'email' || !inputType) {
                  await input.fill(`Test ${inputName || 'input'} ${Date.now()}`);
                } else if (inputType === 'number') {
                  await input.fill('123');
                } else if (inputType === 'date') {
                  await input.fill('2024-12-31');
                }
                
                console.log(`✅ Filled admin form input: ${inputName || 'unnamed'}`);
              } catch (error) {
                console.log(`❌ Failed to fill input: ${inputName || 'unnamed'} - ${error.message}`);
              }
            }
            
            await takeEvidence(page, `festival-form-${i}-filled`, `Festival form ${i + 1} filled`);
          } catch (error) {
            console.log(`❌ Festival element ${i + 1} interaction failed: ${error.message}`);
          }
        }
      }
    });
    
    await test.step('Activity Management Forms', async () => {
      // Look for activity management elements
      const activityElements = await page.locator('button:has-text("Add Activity"), button:has-text("Create Activity"), a:has-text("Activity"), [data-testid*="activity"]').all();
      console.log(`Found ${activityElements.length} activity management elements`);
      
      await takeEvidence(page, 'activity-management', 'Activity management interface');
      
      // Test activity form interactions
      for (let i = 0; i < Math.min(activityElements.length, 3); i++) {
        const element = activityElements[i];
        
        if (await element.isVisible()) {
          try {
            await measureAdminPerformance(page, async () => {
              await element.click();
            }, `Activity Element ${i + 1}`);
            
            await page.waitForTimeout(2000);
            await takeEvidence(page, `activity-form-${i}`, `Activity form ${i + 1} opened`);
            
          } catch (error) {
            console.log(`❌ Activity element ${i + 1} interaction failed: ${error.message}`);
          }
        }
      }
    });
  });

  test('Admin User Management', async ({ page }) => {
    console.log('👥 Testing Admin User Management...');
    
    await authenticateAsAdmin(page);
    
    await test.step('User Management Interface', async () => {
      // Look for user management elements
      const userElements = await page.locator('button:has-text("Users"), a:has-text("Users"), button:has-text("Manage Users"), [data-testid*="user"]').all();
      console.log(`Found ${userElements.length} user management elements`);
      
      await takeEvidence(page, 'user-management', 'User management interface');
      
      // Test user management interactions
      for (let i = 0; i < Math.min(userElements.length, 3); i++) {
        const element = userElements[i];
        
        if (await element.isVisible()) {
          try {
            await measureAdminPerformance(page, async () => {
              await element.click();
            }, `User Management Element ${i + 1}`);
            
            await page.waitForTimeout(2000);
            await takeEvidence(page, `user-management-${i}`, `User management ${i + 1} opened`);
            
          } catch (error) {
            console.log(`❌ User management element ${i + 1} interaction failed: ${error.message}`);
          }
        }
      }
    });
  });

  test('Admin Announcements System', async ({ page }) => {
    console.log('📢 Testing Admin Announcements...');
    
    await authenticateAsAdmin(page);
    
    await test.step('Announcements Management', async () => {
      // Look for announcements management elements
      const announcementElements = await page.locator('button:has-text("Announcement"), a:has-text("Announcement"), button:has-text("Create Announcement"), [data-testid*="announcement"]').all();
      console.log(`Found ${announcementElements.length} announcement management elements`);
      
      await takeEvidence(page, 'announcements-management', 'Announcements management interface');
      
      // Test announcements interactions
      for (let i = 0; i < Math.min(announcementElements.length, 3); i++) {
        const element = announcementElements[i];
        
        if (await element.isVisible()) {
          try {
            await measureAdminPerformance(page, async () => {
              await element.click();
            }, `Announcement Element ${i + 1}`);
            
            await page.waitForTimeout(2000);
            await takeEvidence(page, `announcement-form-${i}`, `Announcement form ${i + 1} opened`);
            
          } catch (error) {
            console.log(`❌ Announcement element ${i + 1} interaction failed: ${error.message}`);
          }
        }
      }
    });
  });

  test('Admin Navigation and Context Switching', async ({ page }) => {
    console.log('🧭 Testing Admin Navigation...');
    
    await authenticateAsAdmin(page);
    
    await test.step('Admin Navigation Testing', async () => {
      // Test admin-specific navigation
      const adminNavElements = await page.locator('nav a, [role="navigation"] a').all();
      console.log(`Found ${adminNavElements.length} navigation elements`);
      
      await takeEvidence(page, 'admin-navigation', 'Admin navigation elements');
      
      // Test admin navigation links
      for (let i = 0; i < Math.min(adminNavElements.length, 8); i++) {
        const link = adminNavElements[i];
        const linkText = await link.textContent();
        const href = await link.getAttribute('href');
        
        if (linkText && href && !href.startsWith('mailto:') && !href.startsWith('tel:')) {
          console.log(`Testing admin navigation: ${linkText} -> ${href}`);
          
          try {
            await measureAdminPerformance(page, async () => {
              await link.click();
            }, `Admin Navigation: ${linkText}`);
            
            await page.waitForTimeout(2000);
            await takeEvidence(page, `admin-nav-${i}-${linkText.replace(/\s+/g, '-').toLowerCase()}`, `Admin navigation to ${linkText}`);
            
            // Go back for next test
            if (i < adminNavElements.length - 1) {
              await page.goBack();
              await page.waitForTimeout(1000);
            }
          } catch (error) {
            console.log(`❌ Admin navigation failed: ${linkText} - ${error.message}`);
          }
        }
      }
    });
  });
});

# Festival Family Responsive Design Guide

This guide outlines the approach to implementing consistent responsive design across the Festival Family application, focusing on mobile-first development and UI consistency.

## Table of Contents

1. [Mobile-First Approach](#mobile-first-approach)
2. [Responsive Layout System](#responsive-layout-system)
3. [Breakpoints](#breakpoints)
4. [Component Adaptations](#component-adaptations)
5. [Typography](#typography)
6. [Spacing System](#spacing-system)
7. [Navigation Patterns](#navigation-patterns)
8. [Images and Media](#images-and-media)
9. [Testing and Validation](#testing-and-validation)

## Mobile-First Approach

### Core Principles

The Festival Family app follows a mobile-first approach, which means:

1. Design and develop for mobile devices first
2. Progressively enhance the experience for larger screens
3. Optimize for touch interactions by default
4. Ensure core functionality works on all devices

### Implementation Strategy

```tsx
// Mobile-first CSS (default styles are for mobile)
.card {
  width: 100%;
  padding: 1rem;
  margin-bottom: 1rem;
}

// Then enhance for larger screens
@media (min-width: 768px) {
  .card {
    width: calc(50% - 1rem);
    margin-right: 1rem;
  }
}

@media (min-width: 1024px) {
  .card {
    width: calc(33.333% - 1rem);
  }
}
```

### Tailwind Implementation

When using Tailwind CSS, apply mobile styles first, then add responsive variants:

```tsx
<div className="
  w-full p-4 mb-4          /* Mobile styles (default) */
  sm:w-1/2 sm:pr-2         /* Small tablet styles */
  md:w-1/3                 /* Tablet styles */
  lg:w-1/4                 /* Desktop styles */
">
  Card content
</div>
```

## Responsive Layout System

### Container System

Use a consistent container system to maintain proper content width:

```tsx
// src/components/layout/Container.tsx
interface ContainerProps {
  children: React.ReactNode;
  className?: string;
  fluid?: boolean;
}

export function Container({ children, className = '', fluid = false }: ContainerProps) {
  return (
    <div
      className={`
        mx-auto px-4 w-full
        ${fluid ? 'max-w-none' : 'max-w-7xl'}
        ${className}
      `}
    >
      {children}
    </div>
  )
}
```

### Grid System

Use CSS Grid for complex layouts:

```tsx
// src/components/festivals/FestivalGrid.tsx
interface FestivalGridProps {
  festivals: Festival[];
}

export function FestivalGrid({ festivals }: FestivalGridProps) {
  return (
    <div className="
      grid grid-cols-1 gap-4
      sm:grid-cols-2
      lg:grid-cols-3
      xl:grid-cols-4
    ">
      {festivals.map(festival => (
        <FestivalCard key={festival.id} festival={festival} />
      ))}
    </div>
  )
}
```

### Flexbox Layouts

Use Flexbox for simpler layouts and component alignment:

```tsx
// src/components/ui/Card.tsx
interface CardProps {
  title: string;
  description: string;
  actions?: React.ReactNode;
}

export function Card({ title, description, actions }: CardProps) {
  return (
    <div className="
      flex flex-col
      bg-white/10 backdrop-blur-md
      rounded-lg overflow-hidden
      border border-white/20
      h-full
    ">
      <div className="p-4 flex-grow">
        <h3 className="text-xl font-bold mb-2">{title}</h3>
        <p className="text-white/80">{description}</p>
      </div>
      
      {actions && (
        <div className="
          p-4 border-t border-white/10
          flex justify-end items-center
          gap-2
        ">
          {actions}
        </div>
      )}
    </div>
  )
}
```

## Breakpoints

### Standard Breakpoints

Use these standard breakpoints consistently throughout the application:

```tsx
// tailwind.config.js
module.exports = {
  theme: {
    screens: {
      'sm': '640px',   // Small tablets and large phones
      'md': '768px',   // Tablets and small laptops
      'lg': '1024px',  // Laptops and desktops
      'xl': '1280px',  // Large desktops
      '2xl': '1536px', // Extra large desktops
    },
  },
}
```

### Custom Hooks for Responsive Logic

Create custom hooks to handle responsive logic:

```tsx
// src/hooks/useBreakpoint.ts
import { useState, useEffect } from 'react'

type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'

const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
}

export function useBreakpoint() {
  const [breakpoint, setBreakpoint] = useState<Breakpoint>('xs')
  const [width, setWidth] = useState(0)
  
  useEffect(() => {
    const handleResize = () => {
      setWidth(window.innerWidth)
      
      if (window.innerWidth >= breakpoints['2xl']) {
        setBreakpoint('2xl')
      } else if (window.innerWidth >= breakpoints.xl) {
        setBreakpoint('xl')
      } else if (window.innerWidth >= breakpoints.lg) {
        setBreakpoint('lg')
      } else if (window.innerWidth >= breakpoints.md) {
        setBreakpoint('md')
      } else if (window.innerWidth >= breakpoints.sm) {
        setBreakpoint('sm')
      } else {
        setBreakpoint('xs')
      }
    }
    
    // Set initial values
    handleResize()
    
    // Add event listener
    window.addEventListener('resize', handleResize)
    
    // Clean up
    return () => window.removeEventListener('resize', handleResize)
  }, [])
  
  return {
    breakpoint,
    width,
    isMobile: breakpoint === 'xs',
    isTablet: breakpoint === 'sm' || breakpoint === 'md',
    isDesktop: breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl',
  }
}
```

### Usage in Components

```tsx
// src/components/layout/Navigation.tsx
import { useBreakpoint } from '@/hooks/useBreakpoint'

export function Navigation() {
  const { isDesktop } = useBreakpoint()
  
  return (
    <nav>
      {isDesktop ? (
        <DesktopNavigation />
      ) : (
        <MobileNavigation />
      )}
    </nav>
  )
}
```

## Component Adaptations

### Responsive Cards

Implement cards that adapt to different screen sizes:

```tsx
// src/components/festivals/FestivalCard.tsx
import { useBreakpoint } from '@/hooks/useBreakpoint'

interface FestivalCardProps {
  festival: Festival;
}

export function FestivalCard({ festival }: FestivalCardProps) {
  const { isMobile } = useBreakpoint()
  
  return (
    <div className="
      bg-white/10 backdrop-blur-md
      rounded-lg overflow-hidden
      border border-white/20
      transition-all duration-300
      hover:scale-[1.02]
      h-full
    ">
      <div className={`relative ${isMobile ? 'h-40' : 'h-48'}`}>
        <img
          src={festival.image_url}
          alt={festival.name}
          className="h-full w-full object-cover"
        />
        <div className="
          absolute bottom-0 left-0 right-0
          bg-gradient-to-t from-black/80 to-transparent
          p-4
        ">
          <h3 className="text-xl font-bold text-white">{festival.name}</h3>
          <p className="text-sm text-white/80">
            {new Date(festival.start_date).toLocaleDateString()} - 
            {new Date(festival.end_date).toLocaleDateString()}
          </p>
        </div>
      </div>
      
      <div className="p-4">
        <p className={isMobile ? 'line-clamp-2' : 'line-clamp-3'}>
          {festival.description}
        </p>
      </div>
      
      <div className="
        p-4 border-t border-white/10
        flex justify-between items-center
      ">
        <span>{festival.location}</span>
        <button className="
          bg-electric-violet hover:bg-electric-violet/80
          text-white font-medium
          py-2 px-4 rounded-md
          transition-colors
        ">
          View Details
        </button>
      </div>
    </div>
  )
}
```

### Responsive Forms

Create forms that adapt to different screen sizes:

```tsx
// src/components/forms/ResponsiveForm.tsx
import { useBreakpoint } from '@/hooks/useBreakpoint'

interface FormFieldProps {
  label: string;
  children: React.ReactNode;
}

function FormField({ label, children }: FormFieldProps) {
  const { isDesktop } = useBreakpoint()
  
  return (
    <div className={`
      mb-4
      ${isDesktop ? 'flex items-center' : ''}
    `}>
      <label className={`
        font-medium text-white
        ${isDesktop ? 'w-1/3 pr-4 text-right' : 'block mb-1'}
      `}>
        {label}
      </label>
      <div className={isDesktop ? 'w-2/3' : 'w-full'}>
        {children}
      </div>
    </div>
  )
}

export function ResponsiveForm() {
  return (
    <form className="max-w-2xl mx-auto">
      <FormField label="Name">
        <input
          type="text"
          className="w-full px-3 py-2 bg-white/10 rounded-md border border-white/20"
        />
      </FormField>
      
      <FormField label="Email">
        <input
          type="email"
          className="w-full px-3 py-2 bg-white/10 rounded-md border border-white/20"
        />
      </FormField>
      
      <FormField label="Message">
        <textarea
          className="w-full px-3 py-2 bg-white/10 rounded-md border border-white/20"
          rows={4}
        />
      </FormField>
      
      <div className="mt-6 flex justify-end">
        <button
          type="submit"
          className="bg-electric-violet hover:bg-electric-violet/80 text-white font-medium py-2 px-6 rounded-md"
        >
          Submit
        </button>
      </div>
    </form>
  )
}
```

### Responsive Modals

Create modals that adapt to different screen sizes:

```tsx
// src/components/ui/ResponsiveModal.tsx
import { Dialog } from '@headlessui/react'
import { useBreakpoint } from '@/hooks/useBreakpoint'

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

export function ResponsiveModal({ isOpen, onClose, title, children }: ModalProps) {
  const { isMobile } = useBreakpoint()
  
  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      className="relative z-50"
    >
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" aria-hidden="true" />
      
      {/* Full-screen container for centering */}
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className={`
          bg-midnight-purple border border-white/20
          rounded-lg overflow-hidden
          shadow-xl
          ${isMobile ? 'w-full h-full' : 'max-w-md w-full max-h-[90vh]'}
        `}>
          <Dialog.Title className="
            font-bold text-xl p-4
            border-b border-white/10
          ">
            {title}
          </Dialog.Title>
          
          <div className="p-4 overflow-y-auto">
            {children}
          </div>
          
          <div className="
            p-4 border-t border-white/10
            flex justify-end gap-2
          ">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-md"
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 bg-electric-violet hover:bg-electric-violet/80 rounded-md"
            >
              Confirm
            </button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  )
}
```

## Typography

### Responsive Font Sizes

Use a responsive typography system:

```tsx
// tailwind.config.js
module.exports = {
  theme: {
    fontSize: {
      'xs': ['0.75rem', { lineHeight: '1rem' }],
      'sm': ['0.875rem', { lineHeight: '1.25rem' }],
      'base': ['1rem', { lineHeight: '1.5rem' }],
      'lg': ['1.125rem', { lineHeight: '1.75rem' }],
      'xl': ['1.25rem', { lineHeight: '1.75rem' }],
      '2xl': ['1.5rem', { lineHeight: '2rem' }],
      '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
      '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
      '5xl': ['3rem', { lineHeight: '1' }],
      '6xl': ['3.75rem', { lineHeight: '1' }],
    },
  },
}
```

### Typography Component

Create a responsive typography component:

```tsx
// src/components/ui/Typography.tsx
import { useBreakpoint } from '@/hooks/useBreakpoint'

interface TypographyProps {
  variant: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body' | 'caption';
  children: React.ReactNode;
  className?: string;
}

export function Typography({ variant, children, className = '' }: TypographyProps) {
  const { isMobile, isTablet } = useBreakpoint()
  
  const getVariantClasses = () => {
    switch (variant) {
      case 'h1':
        return `font-bold ${isMobile ? 'text-3xl' : isTablet ? 'text-4xl' : 'text-5xl'}`
      case 'h2':
        return `font-bold ${isMobile ? 'text-2xl' : isTablet ? 'text-3xl' : 'text-4xl'}`
      case 'h3':
        return `font-bold ${isMobile ? 'text-xl' : isTablet ? 'text-2xl' : 'text-3xl'}`
      case 'h4':
        return `font-semibold ${isMobile ? 'text-lg' : 'text-xl'}`
      case 'h5':
        return `font-semibold ${isMobile ? 'text-base' : 'text-lg'}`
      case 'h6':
        return `font-semibold ${isMobile ? 'text-sm' : 'text-base'}`
      case 'body':
        return 'text-base'
      case 'caption':
        return 'text-sm text-white/70'
      default:
        return 'text-base'
    }
  }
  
  const Component = variant.startsWith('h') && Number(variant[1]) <= 6
    ? variant
    : 'p'
  
  return (
    <Component className={`${getVariantClasses()} ${className}`}>
      {children}
    </Component>
  )
}
```

## Spacing System

### Consistent Spacing Scale

Use a consistent spacing scale throughout the application:

```tsx
// tailwind.config.js
module.exports = {
  theme: {
    spacing: {
      '0': '0',
      '1': '0.25rem',    // 4px
      '2': '0.5rem',     // 8px
      '3': '0.75rem',    // 12px
      '4': '1rem',       // 16px
      '5': '1.25rem',    // 20px
      '6': '1.5rem',     // 24px
      '8': '2rem',       // 32px
      '10': '2.5rem',    // 40px
      '12': '3rem',      // 48px
      '16': '4rem',      // 64px
      '20': '5rem',      // 80px
      '24': '6rem',      // 96px
      '32': '8rem',      // 128px
      '40': '10rem',     // 160px
      '48': '12rem',     // 192px
      '56': '14rem',     // 224px
      '64': '16rem',     // 256px
    },
  },
}
```

### Responsive Spacing Component

Create a component for responsive spacing:

```tsx
// src/components/ui/Spacer.tsx
import { useBreakpoint } from '@/hooks/useBreakpoint'

interface SpacerProps {
  size?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  axis?: 'horizontal' | 'vertical';
}

export function Spacer({ 
  size = { xs: 4, sm: 4, md: 4, lg: 4, xl: 4 }, 
  axis = 'vertical' 
}: SpacerProps) {
  const { breakpoint } = useBreakpoint()
  
  // Get the appropriate size based on current breakpoint
  const getSize = () => {
    if (breakpoint === 'xl' && size.xl !== undefined) return size.xl
    if (breakpoint === 'lg' && size.lg !== undefined) return size.lg
    if (breakpoint === 'md' && size.md !== undefined) return size.md
    if (breakpoint === 'sm' && size.sm !== undefined) return size.sm
    return size.xs || 4
  }
  
  const currentSize = getSize()
  
  return (
    <div
      style={{
        width: axis === 'horizontal' ? `${currentSize * 0.25}rem` : '100%',
        height: axis === 'vertical' ? `${currentSize * 0.25}rem` : '100%',
      }}
    />
  )
}
```

## Navigation Patterns

### Responsive Navigation

Create a navigation system that adapts to different screen sizes:

```tsx
// src/components/navigation/ResponsiveNavigation.tsx
import { useState } from 'react'
import { Link } from 'react-router-dom'
import { useBreakpoint } from '@/hooks/useBreakpoint'
import { HiHome, HiCalendar, HiUsers, HiSearch, HiUser } from 'react-icons/hi'

const navItems = [
  { name: 'Home', path: '/', icon: HiHome },
  { name: 'Activities', path: '/activities', icon: HiCalendar },
  { name: 'FamHub', path: '/famhub', icon: HiUsers },
  { name: 'Discover', path: '/discover', icon: HiSearch },
  { name: 'Profile', path: '/profile', icon: HiUser },
]

export function ResponsiveNavigation() {
  const { isMobile } = useBreakpoint()
  
  return isMobile ? <MobileNavigation /> : <DesktopNavigation />
}

function DesktopNavigation() {
  return (
    <nav className="
      bg-midnight-purple/80 backdrop-blur-md
      border-b border-white/10
      sticky top-0 z-40
    ">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link to="/" className="text-xl font-bold">
              Festival Family
            </Link>
          </div>
          
          <div className="flex items-center space-x-4">
            {navItems.map(item => (
              <Link
                key={item.path}
                to={item.path}
                className="
                  px-3 py-2
                  text-white/80 hover:text-white
                  transition-colors
                "
              >
                {item.name}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </nav>
  )
}

function MobileNavigation() {
  return (
    <nav className="
      fixed bottom-0 left-0 right-0
      bg-midnight-purple/80 backdrop-blur-md
      border-t border-white/10
      z-40
    ">
      <div className="grid grid-cols-5 h-16">
        {navItems.map(({ path, icon: Icon, name }) => (
          <Link
            key={path}
            to={path}
            className="
              flex flex-col items-center justify-center
              text-white/80 hover:text-white
              transition-colors
            "
          >
            <Icon className="w-6 h-6" />
            <span className="text-xs mt-1">{name}</span>
          </Link>
        ))}
      </div>
    </nav>
  )
}
```

### Hamburger Menu for Mobile

Create a hamburger menu for mobile devices:

```tsx
// src/components/navigation/HamburgerMenu.tsx
import { useState } from 'react'
import { Link } from 'react-router-dom'
import { HiMenu, HiX } from 'react-icons/hi'
import { motion, AnimatePresence } from 'framer-motion'

interface HamburgerMenuProps {
  items: Array<{ name: string; path: string }>;
}

export function HamburgerMenu({ items }: HamburgerMenuProps) {
  const [isOpen, setIsOpen] = useState(false)
  
  return (
    <>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="
          fixed top-4 right-4 z-50
          w-10 h-10
          flex items-center justify-center
          bg-electric-violet rounded-full
          text-white
        "
      >
        {isOpen ? <HiX size={24} /> : <HiMenu size={24} />}
      </button>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25 }}
            className="
              fixed inset-0 z-40
              bg-midnight-purple
              flex flex-col
              p-16
            "
          >
            <div className="flex flex-col space-y-4 mt-8">
              {items.map(item => (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={() => setIsOpen(false)}
                  className="
                    text-2xl font-bold
                    hover:text-electric-violet
                    transition-colors
                  "
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
```

## Images and Media

### Responsive Images

Use responsive images with appropriate sizes:

```tsx
// src/components/ui/ResponsiveImage.tsx
interface ResponsiveImageProps {
  src: string;
  alt: string;
  sizes?: string;
  className?: string;
}

export function ResponsiveImage({
  src,
  alt,
  sizes = '100vw',
  className = '',
}: ResponsiveImageProps) {
  // Generate srcSet with different sizes
  const generateSrcSet = () => {
    const basePath = src.split('.').slice(0, -1).join('.')
    const extension = src.split('.').pop()
    
    return `
      ${basePath}-400.${extension} 400w,
      ${basePath}-800.${extension} 800w,
      ${basePath}-1200.${extension} 1200w,
      ${basePath}-1600.${extension} 1600w
    `
  }
  
  return (
    <img
      src={src}
      alt={alt}
      srcSet={generateSrcSet()}
      sizes={sizes}
      className={className}
      loading="lazy"
    />
  )
}
```

### Responsive Video

Create a responsive video component:

```tsx
// src/components/ui/ResponsiveVideo.tsx
interface ResponsiveVideoProps {
  src: string;
  poster?: string;
  aspectRatio?: '16:9' | '4:3' | '1:1';
  className?: string;
}

export function ResponsiveVideo({
  src,
  poster,
  aspectRatio = '16:9',
  className = '',
}: ResponsiveVideoProps) {
  // Calculate padding based on aspect ratio
  const getPaddingBottom = () => {
    switch (aspectRatio) {
      case '16:9': return '56.25%' // 9/16 * 100%
      case '4:3': return '75%'     // 3/4 * 100%
      case '1:1': return '100%'    // 1/1 * 100%
      default: return '56.25%'
    }
  }
  
  return (
    <div
      className={`relative w-full ${className}`}
      style={{ paddingBottom: getPaddingBottom() }}
    >
      <video
        src={src}
        poster={poster}
        controls
        className="absolute top-0 left-0 w-full h-full object-cover"
      />
    </div>
  )
}
```

## Testing and Validation

### Responsive Testing Checklist

Use this checklist to ensure your components are responsive:

1. **Mobile Testing (320px - 639px)**
   - [ ] Content is readable without zooming
   - [ ] Touch targets are at least 44x44px
   - [ ] No horizontal scrolling
   - [ ] Navigation is accessible
   - [ ] Forms are usable

2. **Tablet Testing (640px - 1023px)**
   - [ ] Layout adapts appropriately
   - [ ] Content flows naturally
   - [ ] Navigation transforms as expected
   - [ ] Images scale properly

3. **Desktop Testing (1024px+)**
   - [ ] Layout takes advantage of larger screen
   - [ ] Content is well-distributed
   - [ ] Navigation is intuitive
   - [ ] Performance is optimized

### Device Testing

Test your application on these common device sizes:

- iPhone SE (320px width)
- iPhone 12/13 (390px width)
- iPad (768px width)
- iPad Pro (1024px width)
- Laptop (1366px width)
- Desktop (1920px width)

### Responsive Testing Tools

Use these tools to test responsiveness:

1. **Browser DevTools**
   - Chrome/Firefox/Safari device emulation
   - Responsive design mode

2. **Automated Testing**
   - Cypress viewport testing
   - Jest with Testing Library

```tsx
// Example Cypress test for responsive behavior
describe('Navigation Component', () => {
  it('shows mobile navigation on small screens', () => {
    cy.viewport(375, 667) // iPhone 8
    cy.visit('/')
    cy.get('[data-testid="mobile-nav"]').should('be.visible')
    cy.get('[data-testid="desktop-nav"]').should('not.be.visible')
  })
  
  it('shows desktop navigation on large screens', () => {
    cy.viewport(1280, 800) // Desktop
    cy.visit('/')
    cy.get('[data-testid="desktop-nav"]').should('be.visible')
    cy.get('[data-testid="mobile-nav"]').should('not.be.visible')
  })
})
```

---

By following these responsive design guidelines, the Festival Family app will provide a consistent and optimized user experience across all devices, from mobile phones to desktop computers. The mobile-first approach ensures that the application is accessible and usable on smaller screens while progressively enhancing the experience for larger screens.

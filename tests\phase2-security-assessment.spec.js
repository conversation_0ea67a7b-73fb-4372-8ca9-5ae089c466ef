/**
 * Phase 2: Security Vulnerability Assessment
 * 
 * Comprehensive testing of the three critical security vulnerabilities:
 * 1. XSS payload sanitization at server level
 * 2. Privilege escalation prevention
 * 3. Server-side rate limiting for authentication
 * 
 * Evidence-based testing with screenshots and detailed logging
 */

import { test, expect } from '@playwright/test';
import { promises as fs } from 'fs';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'phase2-security-assessment-evidence';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

// XSS test payloads
const XSS_PAYLOADS = [
  '<script>alert("XSS")</script>',
  'javascript:alert("XSS")',
  '<img src="x" onerror="alert(\'XSS\')">',
  '<svg onload="alert(\'XSS\')">',
  '"><script>alert("XSS")</script>',
  '<iframe src="javascript:alert(\'XSS\')"></iframe>'
];

// Ensure evidence directory exists
test.beforeAll(async () => {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory created: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory already exists: ${EVIDENCE_DIR}`);
  }
});

test.describe('Phase 2: Security Vulnerability Assessment', () => {
  
  test('2.1 XSS Vulnerability Assessment', async ({ page }) => {
    console.log('🧪 Testing XSS vulnerability at server level...');
    
    // Login as admin
    await page.goto(`${APP_URL}/auth`);
    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/01-admin-logged-in.png`,
      fullPage: true 
    });
    
    // Navigate to profile page to test XSS
    await page.goto(`${APP_URL}/profile`);
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/02-profile-page.png`,
      fullPage: true 
    });
    
    const xssResults = [];
    
    // Test each XSS payload
    for (let i = 0; i < XSS_PAYLOADS.length; i++) {
      const payload = XSS_PAYLOADS[i];
      console.log(`🔍 Testing XSS payload ${i + 1}/${XSS_PAYLOADS.length}: ${payload.substring(0, 30)}...`);
      
      try {
        // Try to inject XSS in bio field
        const bioField = page.locator('textarea[name="bio"], input[name="bio"], textarea[placeholder*="bio"], input[placeholder*="bio"]').first();
        
        if (await bioField.isVisible()) {
          await bioField.clear();
          await bioField.fill(payload);
          
          // Submit the form
          await page.click('button[type="submit"], button:has-text("Save"), button:has-text("Update")');
          await page.waitForTimeout(2000);
          
          // Check if payload was stored
          const storedValue = await bioField.inputValue();
          const wasStored = storedValue === payload;
          
          xssResults.push({
            payload: payload.substring(0, 50),
            wasStored,
            storedValue: storedValue.substring(0, 50),
            timestamp: new Date().toISOString()
          });
          
          console.log(`   ${wasStored ? '⚠️ VULNERABLE' : '✅ PROTECTED'}: Payload ${wasStored ? 'stored' : 'sanitized'}`);
          
          await page.screenshot({ 
            path: `${EVIDENCE_DIR}/03-xss-test-${i + 1}.png`,
            fullPage: true 
          });
        } else {
          console.log('   ⚠️ Bio field not found, trying alternative approach');
          xssResults.push({
            payload: payload.substring(0, 50),
            error: 'Bio field not accessible',
            timestamp: new Date().toISOString()
          });
        }
        
      } catch (error) {
        console.log(`   ✅ XSS test caused error (potentially blocked): ${error.message}`);
        xssResults.push({
          payload: payload.substring(0, 50),
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }
    
    // Save XSS test results
    await fs.writeFile(
      `${EVIDENCE_DIR}/xss-vulnerability-results.json`,
      JSON.stringify({
        testType: 'XSS Vulnerability Assessment',
        totalPayloads: XSS_PAYLOADS.length,
        results: xssResults,
        summary: {
          vulnerablePayloads: xssResults.filter(r => r.wasStored).length,
          protectedPayloads: xssResults.filter(r => !r.wasStored && !r.error).length,
          erroredPayloads: xssResults.filter(r => r.error).length
        },
        timestamp: new Date().toISOString()
      }, null, 2)
    );
    
    console.log(`📊 XSS Assessment: ${xssResults.filter(r => r.wasStored).length} vulnerable, ${xssResults.filter(r => !r.wasStored && !r.error).length} protected`);
  });

  test('2.2 Privilege Escalation Assessment', async ({ page }) => {
    console.log('🔐 Testing privilege escalation vulnerability...');
    
    // Login as admin
    await page.goto(`${APP_URL}/auth`);
    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    // Navigate to admin dashboard
    await page.goto(`${APP_URL}/admin`);
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/04-admin-dashboard.png`,
      fullPage: true 
    });
    
    // Try to access user management
    const privilegeResults = {
      adminAccess: true,
      userManagementAccess: false,
      roleChangeAttempts: [],
      timestamp: new Date().toISOString()
    };
    
    // Look for user management functionality
    try {
      await page.goto(`${APP_URL}/admin/users`);
      await page.waitForLoadState('networkidle');
      
      const hasUserList = await page.locator('table, .user-list, [data-testid="user-list"]').isVisible();
      privilegeResults.userManagementAccess = hasUserList;
      
      await page.screenshot({ 
        path: `${EVIDENCE_DIR}/05-user-management.png`,
        fullPage: true 
      });
      
      console.log(`📋 User management access: ${hasUserList ? 'Available' : 'Not found'}`);
      
    } catch (error) {
      console.log(`⚠️ User management page error: ${error.message}`);
      privilegeResults.userManagementError = error.message;
    }
    
    // Test direct database manipulation (if possible through UI)
    console.log('🔍 Testing role escalation through UI...');
    
    // This would test if there are any UI elements that allow role changes
    const roleChangeElements = await page.locator('select[name*="role"], input[name*="role"], button:has-text("Change Role")').count();
    
    if (roleChangeElements > 0) {
      console.log(`⚠️ Found ${roleChangeElements} role change UI elements`);
      privilegeResults.roleChangeUIFound = true;
    } else {
      console.log('✅ No role change UI elements found');
      privilegeResults.roleChangeUIFound = false;
    }
    
    // Save privilege escalation results
    await fs.writeFile(
      `${EVIDENCE_DIR}/privilege-escalation-results.json`,
      JSON.stringify(privilegeResults, null, 2)
    );
    
    console.log(`🔐 Privilege escalation assessment completed`);
  });

  test('2.3 Rate Limiting Assessment', async ({ page }) => {
    console.log('⏱️ Testing server-side rate limiting...');
    
    await page.goto(`${APP_URL}/auth`);
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/06-auth-page-rate-test.png`,
      fullPage: true 
    });
    
    const rateLimitResults = {
      attempts: [],
      rateLimitDetected: false,
      averageResponseTime: 0,
      timestamp: new Date().toISOString()
    };
    
    // Test rapid authentication attempts
    console.log('🔍 Testing rapid authentication attempts...');
    
    for (let i = 0; i < 5; i++) {
      const startTime = Date.now();
      
      try {
        await page.fill('input[type="email"]', '<EMAIL>');
        await page.fill('input[type="password"]', 'wrongpassword');
        await page.click('button[type="submit"]');
        
        // Wait for response
        await page.waitForTimeout(1000);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        // Check for rate limit messages
        const hasRateLimitMessage = await page.locator('text*="rate limit", text*="too many", text*="slow down"').isVisible();
        
        rateLimitResults.attempts.push({
          attempt: i + 1,
          duration,
          rateLimitDetected: hasRateLimitMessage,
          timestamp: new Date().toISOString()
        });
        
        if (hasRateLimitMessage) {
          rateLimitResults.rateLimitDetected = true;
          console.log(`   ✅ Rate limit detected at attempt ${i + 1}`);
          
          await page.screenshot({ 
            path: `${EVIDENCE_DIR}/07-rate-limit-detected.png`,
            fullPage: true 
          });
          break;
        } else {
          console.log(`   Attempt ${i + 1}: ${duration}ms - No rate limit`);
        }
        
        // Clear form for next attempt
        await page.reload();
        await page.waitForLoadState('networkidle');
        
      } catch (error) {
        console.log(`   Error on attempt ${i + 1}: ${error.message}`);
        rateLimitResults.attempts.push({
          attempt: i + 1,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }
    
    // Calculate average response time
    const validAttempts = rateLimitResults.attempts.filter(a => a.duration);
    if (validAttempts.length > 0) {
      rateLimitResults.averageResponseTime = validAttempts.reduce((sum, a) => sum + a.duration, 0) / validAttempts.length;
    }
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/08-rate-limit-final.png`,
      fullPage: true 
    });
    
    // Save rate limiting results
    await fs.writeFile(
      `${EVIDENCE_DIR}/rate-limiting-results.json`,
      JSON.stringify(rateLimitResults, null, 2)
    );
    
    console.log(`⏱️ Rate limiting: ${rateLimitResults.rateLimitDetected ? 'DETECTED' : 'NOT DETECTED'}`);
    console.log(`📊 Average response time: ${rateLimitResults.averageResponseTime.toFixed(2)}ms`);
  });

  test('2.4 Admin Functionality Preservation', async ({ page }) => {
    console.log('🛡️ Verifying admin functionality preservation...');
    
    // Login as admin
    await page.goto(`${APP_URL}/auth`);
    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    const adminPreservationResults = {
      loginSuccessful: false,
      adminDashboardAccess: false,
      adminRoutes: [],
      timestamp: new Date().toISOString()
    };
    
    // Verify login was successful
    const currentUrl = page.url();
    adminPreservationResults.loginSuccessful = !currentUrl.includes('/auth');
    
    console.log(`🔐 Admin login: ${adminPreservationResults.loginSuccessful ? 'SUCCESS' : 'FAILED'}`);
    
    // Test admin dashboard access
    try {
      await page.goto(`${APP_URL}/admin`);
      await page.waitForLoadState('networkidle');
      
      const hasAdminContent = await page.locator('h1, h2, h3').isVisible();
      adminPreservationResults.adminDashboardAccess = hasAdminContent;
      
      await page.screenshot({ 
        path: `${EVIDENCE_DIR}/09-admin-preservation-check.png`,
        fullPage: true 
      });
      
      console.log(`📊 Admin dashboard: ${hasAdminContent ? 'ACCESSIBLE' : 'BLOCKED'}`);
      
    } catch (error) {
      console.log(`❌ Admin dashboard error: ${error.message}`);
      adminPreservationResults.adminDashboardError = error.message;
    }
    
    // Test various admin routes
    const adminRoutes = ['/admin', '/admin/users', '/admin/events', '/admin/festivals'];
    
    for (const route of adminRoutes) {
      try {
        await page.goto(`${APP_URL}${route}`);
        await page.waitForLoadState('networkidle');
        
        const isAccessible = !page.url().includes('/auth') && !page.url().includes('/error');
        
        adminPreservationResults.adminRoutes.push({
          route,
          accessible: isAccessible,
          finalUrl: page.url()
        });
        
        console.log(`📋 ${route}: ${isAccessible ? 'ACCESSIBLE' : 'BLOCKED'}`);
        
      } catch (error) {
        adminPreservationResults.adminRoutes.push({
          route,
          accessible: false,
          error: error.message
        });
      }
    }
    
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/10-admin-final-state.png`,
      fullPage: true 
    });
    
    // Save admin preservation results
    await fs.writeFile(
      `${EVIDENCE_DIR}/admin-preservation-results.json`,
      JSON.stringify(adminPreservationResults, null, 2)
    );
    
    console.log(`🛡️ Admin preservation: ${adminPreservationResults.loginSuccessful && adminPreservationResults.adminDashboardAccess ? 'SUCCESS' : 'ISSUES DETECTED'}`);
  });

  // Generate comprehensive Phase 2 report
  test.afterAll(async () => {
    console.log('📊 Generating Phase 2 Security Assessment Report...');
    
    const phase2Report = {
      testSuite: 'Phase 2: Security Vulnerability Assessment',
      timestamp: new Date().toISOString(),
      vulnerabilitiesTested: [
        'XSS payload sanitization at server level',
        'Privilege escalation prevention', 
        'Server-side rate limiting for authentication'
      ],
      evidenceFiles: [
        '01-admin-logged-in.png',
        '02-profile-page.png',
        '03-xss-test-*.png',
        '04-admin-dashboard.png',
        '05-user-management.png',
        '06-auth-page-rate-test.png',
        '07-rate-limit-detected.png',
        '08-rate-limit-final.png',
        '09-admin-preservation-check.png',
        '10-admin-final-state.png'
      ],
      dataFiles: [
        'xss-vulnerability-results.json',
        'privilege-escalation-results.json',
        'rate-limiting-results.json',
        'admin-preservation-results.json'
      ],
      summary: 'Comprehensive security vulnerability assessment with evidence-based findings'
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/phase2-security-assessment-report.json`,
      JSON.stringify(phase2Report, null, 2)
    );
    
    console.log('✅ Phase 2 Security Assessment completed with comprehensive evidence');
    console.log(`📁 Evidence saved to: ${EVIDENCE_DIR}/`);
  });
});

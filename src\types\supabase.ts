export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      activities: {
        Row: {
          capacity: number | null
          created_at: string | null
          created_by: string | null
          description: string
          end_date: string | null
          festival_id: string | null
          id: string
          image_url: string | null
          is_featured: boolean | null
          location: string
          metadata: Json | null
          parent_activity_id: string | null
          start_date: string | null
          status: string | null
          tags: string[] | null
          title: string
          type: Database["public"]["Enums"]["activity_type"]
          updated_at: string | null
        }
        Insert: {
          capacity?: number | null
          created_at?: string | null
          created_by?: string | null
          description: string
          end_date?: string | null
          festival_id?: string | null
          id?: string
          image_url?: string | null
          is_featured?: boolean | null
          location: string
          metadata?: Json | null
          parent_activity_id?: string | null
          start_date?: string | null
          status?: string | null
          tags?: string[] | null
          title: string
          type: Database["public"]["Enums"]["activity_type"]
          updated_at?: string | null
        }
        Update: {
          capacity?: number | null
          created_at?: string | null
          created_by?: string | null
          description?: string
          end_date?: string | null
          festival_id?: string | null
          id?: string
          image_url?: string | null
          is_featured?: boolean | null
          location?: string
          metadata?: Json | null
          parent_activity_id?: string | null
          start_date?: string | null
          status?: string | null
          tags?: string[] | null
          title?: string
          type?: Database["public"]["Enums"]["activity_type"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "activities_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activities_festival_id_fkey"
            columns: ["festival_id"]
            isOneToOne: false
            referencedRelation: "festivals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activities_parent_activity_id_fkey"
            columns: ["parent_activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activities_parent_activity_id_fkey"
            columns: ["parent_activity_id"]
            isOneToOne: false
            referencedRelation: "activity_details"
            referencedColumns: ["id"]
          },
        ]
      }
      color_mappings: {
        Row: {
          admin_configurable: boolean | null
          category: string
          color_accent: string
          color_primary: string
          color_secondary: string
          content_type: string
          created_at: string
          description: string | null
          emoji_icon: string | null
          id: string
          show_icon: boolean | null
          updated_at: string
        }
        Insert: {
          admin_configurable?: boolean | null
          category?: string
          color_accent: string
          color_primary: string
          color_secondary: string
          content_type: string
          created_at?: string
          description?: string | null
          emoji_icon?: string | null
          id?: string
          show_icon?: boolean | null
          updated_at?: string
        }
        Update: {
          admin_configurable?: boolean | null
          category?: string
          color_accent?: string
          color_primary?: string
          color_secondary?: string
          content_type?: string
          created_at?: string
          description?: string | null
          emoji_icon?: string | null
          id?: string
          show_icon?: boolean | null
          updated_at?: string
        }
        Relationships: []
      }
      festivals: {
        Row: {
          created_at: string | null
          created_by: string | null
          description: string
          end_date: string
          id: string
          image_url: string | null
          is_active: boolean | null
          location: string
          name: string
          start_date: string
          status: Database["public"]["Enums"]["festival_status"] | null
          updated_at: string | null
          website: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          description: string
          end_date: string
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          location: string
          name: string
          start_date: string
          status?: Database["public"]["Enums"]["festival_status"] | null
          updated_at?: string | null
          website?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          description?: string
          end_date?: string
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          location?: string
          name?: string
          start_date?: string
          status?: Database["public"]["Enums"]["festival_status"] | null
          updated_at?: string | null
          website?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "festivals_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          bio: string | null
          created_at: string | null
          email: string
          full_name: string | null
          id: string
          interests: string[] | null
          location: string | null
          role: Database["public"]["Enums"]["user_role"] | null
          updated_at: string | null
          username: string
          website: string | null
        }
        Insert: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          email: string
          full_name?: string | null
          id?: string
          interests?: string[] | null
          location?: string | null
          role?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string | null
          username: string
          website?: string | null
        }
        Update: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          email?: string
          full_name?: string | null
          id?: string
          interests?: string[] | null
          location?: string | null
          role?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string | null
          username?: string
          website?: string | null
        }
        Relationships: []
      }
      tips: {
        Row: {
          category: Database["public"]["Enums"]["tip_category"] | null
          content: string
          created_at: string | null
          created_by: string | null
          description: string | null
          estimated_read_time: number | null
          helpful_count: number | null
          id: string
          image_url: string | null
          is_active: boolean | null
          is_featured: boolean | null
          order_index: number | null
          status: string | null
          tags: string[] | null
          title: string
          updated_at: string | null
          view_count: number | null
        }
        Insert: {
          category?: Database["public"]["Enums"]["tip_category"] | null
          content: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          estimated_read_time?: number | null
          helpful_count?: number | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          order_index?: number | null
          status?: string | null
          tags?: string[] | null
          title: string
          updated_at?: string | null
          view_count?: number | null
        }
        Update: {
          category?: Database["public"]["Enums"]["tip_category"] | null
          content?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          estimated_read_time?: number | null
          helpful_count?: number | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          order_index?: number | null
          status?: string | null
          tags?: string[] | null
          title?: string
          updated_at?: string | null
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "tips_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      announcements: {
        Row: {
          id: string
          title: string
          content: string
          priority: string
          start_date: string | null
          end_date: string | null
          target_audience: string[] | null
          active: boolean | null
          created_at: string | null
          updated_at: string | null
          created_by: string | null
          notification_sent: boolean | null
          category_id: string | null
          display_type: string | null
          scheduled_for: string | null
          expires_at: string | null
          status: string | null
          is_featured: boolean | null
          view_count: number | null
          tags: string[] | null
          is_pinned: boolean | null
          type: string | null
        }
        Insert: {
          id?: string
          title: string
          content: string
          priority?: string
          start_date?: string | null
          end_date?: string | null
          target_audience?: string[] | null
          active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
          created_by?: string | null
          notification_sent?: boolean | null
          category_id?: string | null
          display_type?: string | null
          scheduled_for?: string | null
          expires_at?: string | null
          status?: string | null
          is_featured?: boolean | null
          view_count?: number | null
          tags?: string[] | null
          is_pinned?: boolean | null
          type?: string | null
        }
        Update: {
          id?: string
          title?: string
          content?: string
          priority?: string
          start_date?: string | null
          end_date?: string | null
          target_audience?: string[] | null
          active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
          created_by?: string | null
          notification_sent?: boolean | null
          category_id?: string | null
          display_type?: string | null
          scheduled_for?: string | null
          expires_at?: string | null
          status?: string | null
          is_featured?: boolean | null
          view_count?: number | null
          tags?: string[] | null
          is_pinned?: boolean | null
          type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "announcements_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      events: {
        Row: {
          id: string
          created_at: string | null
          updated_at: string | null
          title: string
          description: string
          start_date: string
          end_date: string
          location: string
          image_url: string | null
          status: Database["public"]["Enums"]["event_status"] | null
          created_by: string | null
          festival_id: string | null
          capacity: number | null
          registration_required: boolean | null
          is_active: boolean | null
          category: string | null
        }
        Insert: {
          id?: string
          created_at?: string | null
          updated_at?: string | null
          title: string
          description: string
          start_date: string
          end_date: string
          location: string
          image_url?: string | null
          status?: Database["public"]["Enums"]["event_status"] | null
          created_by?: string | null
          festival_id?: string | null
          capacity?: number | null
          registration_required?: boolean | null
          is_active?: boolean | null
          category?: string | null
        }
        Update: {
          id?: string
          created_at?: string | null
          updated_at?: string | null
          title?: string
          description?: string
          start_date?: string
          end_date?: string
          location?: string
          image_url?: string | null
          status?: Database["public"]["Enums"]["event_status"] | null
          created_by?: string | null
          festival_id?: string | null
          capacity?: number | null
          registration_required?: boolean | null
          is_active?: boolean | null
          category?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "events_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "events_festival_id_fkey"
            columns: ["festival_id"]
            isOneToOne: false
            referencedRelation: "festivals"
            referencedColumns: ["id"]
          },
        ]
      }
      external_links: {
        Row: {
          id: string
          type: string
          url: string
          title: string
          active: boolean | null
          created_at: string | null
          updated_at: string | null
          created_by: string | null
          description: string | null
          category: string | null
        }
        Insert: {
          id?: string
          type: string
          url: string
          title: string
          active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
          created_by?: string | null
          description?: string | null
          category?: string | null
        }
        Update: {
          id?: string
          type?: string
          url?: string
          title?: string
          active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
          created_by?: string | null
          description?: string | null
          category?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "external_links_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      faqs: {
        Row: {
          id: string
          question: string
          answer: string
          created_at: string | null
          updated_at: string | null
          created_by: string | null
          is_active: boolean | null
          category: string | null
          order_index: number | null
          status: string | null
          is_featured: boolean | null
          view_count: number | null
          helpful_count: number | null
          tags: string[] | null
        }
        Insert: {
          id?: string
          question: string
          answer: string
          created_at?: string | null
          updated_at?: string | null
          created_by?: string | null
          is_active?: boolean | null
          category?: string | null
          order_index?: number | null
          status?: string | null
          is_featured?: boolean | null
          view_count?: number | null
          helpful_count?: number | null
          tags?: string[] | null
        }
        Update: {
          id?: string
          question?: string
          answer?: string
          created_at?: string | null
          updated_at?: string | null
          created_by?: string | null
          is_active?: boolean | null
          category?: string | null
          order_index?: number | null
          status?: string | null
          is_featured?: boolean | null
          view_count?: number | null
          helpful_count?: number | null
          tags?: string[] | null
        }
        Relationships: [
          {
            foreignKeyName: "faqs_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      guides: {
        Row: {
          id: string
          title: string
          description: string | null
          content: string
          category: Database["public"]["Enums"]["guide_category"] | null
          created_at: string | null
          updated_at: string | null
          created_by: string | null
          is_active: boolean | null
          status: string | null
          is_featured: boolean | null
          view_count: number | null
          helpful_count: number | null
          order_index: number | null
          tags: string[] | null
          image_url: string | null
          estimated_read_time: number | null
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          content: string
          category?: Database["public"]["Enums"]["guide_category"] | null
          created_at?: string | null
          updated_at?: string | null
          created_by?: string | null
          is_active?: boolean | null
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          content?: string
          category?: Database["public"]["Enums"]["guide_category"] | null
          created_at?: string | null
          updated_at?: string | null
          created_by?: string | null
          is_active?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "guides_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      content_management: {
        Row: {
          id: string
          content_key: string
          content_type: string
          title: string | null
          content: string
          metadata: Json | null
          is_active: boolean | null
          version: number | null
          language: string | null
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          content_key: string
          content_type: string
          title?: string | null
          content: string
          metadata?: Json | null
          is_active?: boolean | null
          version?: number | null
          language?: string | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          content_key?: string
          content_type?: string
          title?: string | null
          content?: string
          metadata?: Json | null
          is_active?: boolean | null
          version?: number | null
          language?: string | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "content_management_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      emergency_contacts: {
        Row: {
          id: string
          festival_id: string
          contact_type: string
          name: string
          phone: string | null
          email: string | null
          description: string | null
          is_primary: boolean | null
          order_index: number | null
          is_active: boolean | null
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          festival_id: string
          contact_type: string
          name: string
          phone?: string | null
          email?: string | null
          description?: string | null
          is_primary?: boolean | null
          order_index?: number | null
          is_active?: boolean | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          festival_id?: string
          contact_type?: string
          name?: string
          phone?: string | null
          email?: string | null
          description?: string | null
          is_primary?: boolean | null
          order_index?: number | null
          is_active?: boolean | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "emergency_contacts_festival_id_fkey"
            columns: ["festival_id"]
            isOneToOne: false
            referencedRelation: "festivals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "emergency_contacts_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      safety_information: {
        Row: {
          id: string
          festival_id: string
          safety_category: string
          title: string
          content: string
          priority: string | null
          is_alert: boolean | null
          order_index: number | null
          is_active: boolean | null
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          festival_id: string
          safety_category: string
          title: string
          content: string
          priority?: string | null
          is_alert?: boolean | null
          order_index?: number | null
          is_active?: boolean | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          festival_id?: string
          safety_category?: string
          title?: string
          content?: string
          priority?: string | null
          is_alert?: boolean | null
          order_index?: number | null
          is_active?: boolean | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "safety_information_festival_id_fkey"
            columns: ["festival_id"]
            isOneToOne: false
            referencedRelation: "festivals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "safety_information_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      activity_participants: {
        Row: {
          id: string
          user_id: string
          activity_id: string
          status: string | null
          registration_date: string | null
          notes: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          activity_id: string
          status?: string | null
          registration_date?: string | null
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          activity_id?: string
          status?: string | null
          registration_date?: string | null
          notes?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "activity_participants_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activity_participants_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
        ]
      }
      user_favorites: {
        Row: {
          id: string
          user_id: string
          activity_id: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          activity_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          activity_id?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_favorites_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_favorites_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
        ]
      }
      activity_attendance: {
        Row: {
          id: string
          user_id: string
          activity_id: string
          status: string
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          activity_id: string
          status?: string
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          activity_id?: string
          status?: string
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "activity_attendance_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activity_attendance_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
        ]
      }
      meetups: {
        Row: {
          id: string
          activity_id: string
          organizer_id: string | null
          max_participants: number | null
          location_details: string | null
          prerequisites: string | null
          is_public: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          activity_id: string
          organizer_id?: string | null
          max_participants?: number | null
          location_details?: string | null
          prerequisites?: string | null
          is_public?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          activity_id?: string
          organizer_id?: string | null
          max_participants?: number | null
          location_details?: string | null
          prerequisites?: string | null
          is_public?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "meetups_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "meetups_organizer_id_fkey"
            columns: ["organizer_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      workshops: {
        Row: {
          id: string
          activity_id: string
          instructor_id: string | null
          materials_needed: string | null
          skill_level: string | null
          duration_minutes: number | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          activity_id: string
          instructor_id?: string | null
          materials_needed?: string | null
          skill_level?: string | null
          duration_minutes?: number | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          activity_id?: string
          instructor_id?: string | null
          materials_needed?: string | null
          skill_level?: string | null
          duration_minutes?: number | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "workshops_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "workshops_instructor_id_fkey"
            columns: ["instructor_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      communities: {
        Row: {
          id: string
          name: string
          description: string | null
          category: string
          member_count: number | null
          image_url: string | null
          external_link_id: string | null
          is_active: boolean | null
          is_featured: boolean | null
          created_by: string | null
          created_at: string | null
          updated_at: string | null
          type: string | null
          external_url: string | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          category: string
          member_count?: number | null
          image_url?: string | null
          external_link_id?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
          type?: string | null
          external_url?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          category?: string
          member_count?: number | null
          image_url?: string | null
          external_link_id?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
          type?: string | null
          external_url?: string | null
        }
        Relationships: []
      }
      local_info: {
        Row: {
          id: string
          title: string
          category: string
          description: string | null
          link: string | null
          is_active: boolean | null
          is_featured: boolean | null
          priority: number | null
          created_by: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          title: string
          category: string
          description?: string | null
          link?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          priority?: number | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          title?: string
          category?: string
          description?: string | null
          link?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          priority?: number | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      color_schemes: {
        Row: {
          id: string
          name: string
          description: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      chat_rooms: {
        Row: {
          id: string
          name: string
          description: string | null
          is_group: boolean | null
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          is_group?: boolean | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          is_group?: boolean | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "chat_rooms_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_messages: {
        Row: {
          id: string
          room_id: string | null
          sender_id: string | null
          content: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          room_id?: string | null
          sender_id?: string | null
          content: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          room_id?: string | null
          sender_id?: string | null
          content?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "chat_messages_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "chat_rooms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_room_members: {
        Row: {
          id: string
          room_id: string | null
          profile_id: string | null
          joined_at: string
          last_read_at: string
        }
        Insert: {
          id?: string
          room_id?: string | null
          profile_id?: string | null
          joined_at?: string
          last_read_at?: string
        }
        Update: {
          id?: string
          room_id?: string | null
          profile_id?: string | null
          joined_at?: string
          last_read_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "chat_room_members_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "chat_rooms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_room_members_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      groups: {
        Row: {
          id: string
          created_at: string | null
          name: string
          description: string | null
          festival_id: string
          creator_id: string
          image_url: string | null
          max_members: number | null
          is_private: boolean | null
          updated_at: string | null
          formation_type: string | null
          auto_accept_threshold: number | null
          activity_focus: string[] | null
          music_focus: string[] | null
          tags: string[] | null
          is_active: boolean | null
          expires_at: string | null
        }
        Insert: {
          id?: string
          created_at?: string | null
          name: string
          description?: string | null
          festival_id: string
          creator_id: string
          image_url?: string | null
          max_members?: number | null
          is_private?: boolean | null
          updated_at?: string | null
          formation_type?: string | null
          auto_accept_threshold?: number | null
          activity_focus?: string[] | null
          music_focus?: string[] | null
          tags?: string[] | null
          is_active?: boolean | null
          expires_at?: string | null
        }
        Update: {
          id?: string
          created_at?: string | null
          name?: string
          description?: string | null
          festival_id?: string
          creator_id?: string
          image_url?: string | null
          max_members?: number | null
          is_private?: boolean | null
          updated_at?: string | null
          formation_type?: string | null
          auto_accept_threshold?: number | null
          activity_focus?: string[] | null
          music_focus?: string[] | null
          tags?: string[] | null
          is_active?: boolean | null
          expires_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "groups_festival_id_fkey"
            columns: ["festival_id"]
            isOneToOne: false
            referencedRelation: "festivals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "groups_creator_id_fkey"
            columns: ["creator_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      group_members: {
        Row: {
          id: string
          group_id: string
          user_id: string
          role: string | null
          joined_at: string
          left_at: string | null
          invited_by: string | null
        }
        Insert: {
          id?: string
          group_id: string
          user_id: string
          role?: string | null
          joined_at?: string
          left_at?: string | null
          invited_by?: string | null
        }
        Update: {
          id?: string
          group_id?: string
          user_id?: string
          role?: string | null
          joined_at?: string
          left_at?: string | null
          invited_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "group_members_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_members_invited_by_fkey"
            columns: ["invited_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      group_invitations: {
        Row: {
          id: string
          group_id: string
          inviter_id: string
          invitee_id: string
          status: string | null
          message: string | null
          expires_at: string | null
          created_at: string
          responded_at: string | null
        }
        Insert: {
          id?: string
          group_id: string
          inviter_id: string
          invitee_id: string
          status?: string | null
          message?: string | null
          expires_at?: string | null
          created_at?: string
          responded_at?: string | null
        }
        Update: {
          id?: string
          group_id?: string
          inviter_id?: string
          invitee_id?: string
          status?: string | null
          message?: string | null
          expires_at?: string | null
          created_at?: string
          responded_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "group_invitations_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_invitations_inviter_id_fkey"
            columns: ["inviter_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_invitations_invitee_id_fkey"
            columns: ["invitee_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      activity_type:
        | "workshop"
        | "meetup"
        | "performance"
        | "game"
        | "social"
        | "food"
        | "other"
      event_status: "DRAFT" | "PUBLISHED" | "ARCHIVED"
      festival_status: "DRAFT" | "PUBLISHED" | "ARCHIVED"
      guide_category: "SAFETY" | "PACKING" | "CAMPING" | "FOOD" | "TRANSPORT" | "OTHER"
      tip_category:
        | "SURVIVAL"
        | "SOCIAL"
        | "COMFORT"
        | "BUDGET"
        | "EXPERIENCE"
        | "OTHER"
      user_role: "SUPER_ADMIN" | "CONTENT_ADMIN" | "MODERATOR" | "USER"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// ============================================================================
// TYPE EXPORTS FOR BACKWARD COMPATIBILITY
// ============================================================================

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T]

// Export commonly used table types for backward compatibility
export type Festival = Database['public']['Tables']['festivals']['Row']
export type Activity = Database['public']['Tables']['activities']['Row']
export type Event = Database['public']['Tables']['events']['Row']
export type Profile = Database['public']['Tables']['profiles']['Row']
export type Announcement = Database['public']['Tables']['announcements']['Row']
export type Guide = Database['public']['Tables']['guides']['Row']
export type Tip = Database['public']['Tables']['tips']['Row']
export type FAQ = Database['public']['Tables']['faqs']['Row']
export type ExternalLink = Database['public']['Tables']['external_links']['Row']
export type Community = Database['public']['Tables']['communities']['Row']
export type LocalInfo = Database['public']['Tables']['local_info']['Row']

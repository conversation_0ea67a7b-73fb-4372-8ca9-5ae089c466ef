/**
 * Generated Types for Supabase Database
 * 
 * This file contains TypeScript types that match your Supabase database schema.
 * Update these types when your database schema changes.
 * 
 * To regenerate this file, run:
 * npx supabase gen types typescript --project-id your-project-id > src/lib/types/database.ts
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          username: string
          avatar_url: string | null
          updated_at: string
          created_at: string
          full_name: string | null
          website: string | null
          is_admin: boolean
          is_moderator: boolean
        }
        Insert: {
          id: string
          username: string
          avatar_url?: string | null
          updated_at?: string
          created_at?: string
          full_name?: string | null
          website?: string | null
          is_admin?: boolean
          is_moderator?: boolean
        }
        Update: {
          id?: string
          username?: string
          avatar_url?: string | null
          updated_at?: string
          created_at?: string
          full_name?: string | null
          website?: string | null
          is_admin?: boolean
          is_moderator?: boolean
        }
      }
      festivals: {
        Row: {
          id: string
          name: string
          description: string | null
          start_date: string
          end_date: string
          location: string
          image_url: string | null
          created_at: string
          updated_at: string
          created_by: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          start_date: string
          end_date: string
          location: string
          image_url?: string | null
          created_at?: string
          updated_at?: string
          created_by: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          start_date?: string
          end_date?: string
          location?: string
          image_url?: string | null
          created_at?: string
          updated_at?: string
          created_by?: string
        }
      }
      // Add placeholder definitions for missing tables
      activities: {
        Row: { [key: string]: any }; // Placeholder type
        Insert: { [key: string]: any }; // Placeholder type
        Update: { [key: string]: any }; // Placeholder type
      }
      meetups: {
        Row: { [key: string]: any }; // Placeholder type
        Insert: { [key: string]: any }; // Placeholder type
        Update: { [key: string]: any }; // Placeholder type
      }
      workshops: {
        Row: { [key: string]: any }; // Placeholder type
        Insert: { [key: string]: any }; // Placeholder type
        Update: { [key: string]: any }; // Placeholder type
      }
      events: {
        Row: { [key: string]: any }; // Placeholder type
        Insert: { [key: string]: any }; // Placeholder type
        Update: { [key: string]: any }; // Placeholder type
      }
      // Add other tables as needed
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}

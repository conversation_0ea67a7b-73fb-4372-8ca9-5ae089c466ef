/**
 * Festival Family - Main Application Entry Point
 * 
 * Proper Router context hierarchy with single source of truth architecture
 * and Redis integration for high-performance caching.
 * 
 * @version 2.0.0
 * <AUTHOR> Family Team
 */

import React, { Suspense, lazy } from 'react'
import ReactDOM from 'react-dom/client'
import { create<PERSON><PERSON>erRouter, RouterProvider, Navigate } from 'react-router-dom'

// Core styles and design system
import './index.css'
import './styles/design-tokens.css'

// Core providers - using existing single source of truth
import { ConsolidatedAuthProvider } from './providers/ConsolidatedAuthProvider'
import { QueryProvider } from './providers/QueryProvider'
import { ThemeProvider } from './contexts/ThemeContext'
import { SecurityProvider } from './components/security/SecurityProvider'

// Error boundaries and monitoring - using existing components
import { GlobalErrorBoundary } from './components/error/GlobalErrorBoundary'
import { RouterErrorBoundary } from './components/error/RouterErrorBoundary'

// Performance monitoring and analytics
import { Analytics } from '@vercel/analytics/react'

// Loading components
import { LoadingSpinner } from './components/ui/LoadingSpinner'

// Lazy-loaded components - using existing patterns
const AppLayout = lazy(() => import('./components/layout/AppLayout'))
const SmartHome = lazy(() => import('./pages/SmartHome'))
const SimpleAuth = lazy(() => import('./pages/SimpleAuth'))
const AuthCallback = lazy(() => import('./pages/AuthCallback'))
const Activities = lazy(() => import('./pages/Activities'))
const Discover = lazy(() => import('./pages/Discover'))
const FamHub = lazy(() => import('./pages/FamHub'))
const Profile = lazy(() => import('./pages/Profile'))
const Festivals = lazy(() => import('./pages/Festivals'))
const NotFound = lazy(() => import('./pages/NotFound'))

// Protected route component
import ProtectedRoute from './components/auth/ProtectedRoute'

// Admin routes - using existing configuration
import { adminRoutes } from './pages/admin/routes'



// ============================================================================
// ROUTER CONFIGURATION
// ============================================================================

const router = createBrowserRouter([
  // Admin routes
  ...adminRoutes,
  
  // Auth routes (outside of main layout)
  {
    path: '/auth',
    element: <Suspense fallback={<LoadingSpinner />}><SimpleAuth /></Suspense>,
  },
  {
    path: '/auth/callback',
    element: <Suspense fallback={<LoadingSpinner />}><AuthCallback /></Suspense>,
  },
  {
    path: '/login',
    element: <Navigate to="/auth" replace />,
  },



  // Main app routes with AppLayout
  {
    element: <Suspense fallback={<LoadingSpinner />}><AppLayout /></Suspense>,
    children: [
      {
        path: '/',
        element: <Suspense fallback={<LoadingSpinner />}><SmartHome /></Suspense>,
      },
      {
        path: '/profile',
        element: (
          <ProtectedRoute>
            <Suspense fallback={<LoadingSpinner />}>
              <Profile />
            </Suspense>
          </ProtectedRoute>
        ),
      },
      {
        path: '/activities',
        element: <Suspense fallback={<LoadingSpinner />}><Activities /></Suspense>,
      },
      {
        path: '/famhub',
        element: (
          <ProtectedRoute>
            <Suspense fallback={<LoadingSpinner />}>
              <FamHub />
            </Suspense>
          </ProtectedRoute>
        ),
      },
      {
        path: '/discover',
        element: <Suspense fallback={<LoadingSpinner />}><Discover /></Suspense>,
      },
      {
        path: '/festivals',
        element: <Suspense fallback={<LoadingSpinner />}><Festivals /></Suspense>,
      },
    ]
  },

  // Catch-all route
  {
    path: '*',
    element: <NotFound />
  }
], {
  future: {
    v7_relativeSplatPath: true,
    v7_fetcherPersist: true,
    v7_normalizeFormMethod: true,
    v7_partialHydration: true,
    v7_skipActionErrorRevalidation: true,
  }
})

// ============================================================================
// ROOT ELEMENT SETUP
// ============================================================================

const rootElement = document.getElementById('root')
if (!rootElement) throw new Error('Root element not found')

// ============================================================================
// APPLICATION RENDER
// ============================================================================

ReactDOM.createRoot(rootElement).render(
  <React.StrictMode>
    <SecurityProvider>
      <GlobalErrorBoundary>
        <ThemeProvider>
          <ConsolidatedAuthProvider>
            <QueryProvider>
              <RouterErrorBoundary>
                <RouterProvider router={router} />
              </RouterErrorBoundary>
              <Analytics />
            </QueryProvider>
          </ConsolidatedAuthProvider>
        </ThemeProvider>
      </GlobalErrorBoundary>
    </SecurityProvider>
  </React.StrictMode>,
)

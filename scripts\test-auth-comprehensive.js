#!/usr/bin/env node

/**
 * Comprehensive Authentication Testing Script
 * 
 * This script tests the complete authentication system including
 * email confirmation handling and various authentication scenarios.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Test authentication system capabilities
 */
async function testAuthCapabilities() {
  console.log('🔍 Testing Authentication System Capabilities');
  console.log('============================================');
  
  const results = {
    connection: false,
    registration: false,
    emailConfirmationRequired: false,
    existingUserLogin: false,
    sessionManagement: false,
    profileIntegration: false,
    errorHandling: false,
    securityFeatures: false
  };

  // Test 1: Database Connection
  console.log('\n1️⃣ Testing Database Connection...');
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);

    if (!error) {
      console.log('✅ Database connection successful');
      results.connection = true;
    } else {
      console.log(`❌ Database connection failed: ${error.message}`);
    }
  } catch (error) {
    console.log(`❌ Database connection error: ${error.message}`);
  }

  // Test 2: Registration Flow
  console.log('\n2️⃣ Testing Registration Flow...');
  const testEmail = `test.${Date.now()}@gmail.com`;
  try {
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: 'TestPassword123!',
      options: {
        data: {
          full_name: 'Test User',
          username: testEmail.split('@')[0]
        }
      }
    });

    if (!error && data.user) {
      console.log('✅ Registration successful');
      console.log(`   User ID: ${data.user.id}`);
      console.log(`   Email confirmed: ${data.user.email_confirmed_at ? 'Yes' : 'No'}`);
      results.registration = true;
      
      if (!data.user.email_confirmed_at) {
        console.log('📧 Email confirmation required');
        results.emailConfirmationRequired = true;
      }
    } else {
      console.log(`❌ Registration failed: ${error?.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.log(`❌ Registration error: ${error.message}`);
  }

  // Test 3: Check for Existing Users
  console.log('\n3️⃣ Testing Existing User Detection...');
  try {
    // Try to register the same email again
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: 'TestPassword123!'
    });

    if (error && error.message.includes('already registered')) {
      console.log('✅ Duplicate registration properly handled');
      results.existingUserLogin = true;
    } else if (!error) {
      console.log('⚠️  Duplicate registration allowed (might be expected behavior)');
    }
  } catch (error) {
    console.log(`❌ Existing user test error: ${error.message}`);
  }

  // Test 4: Session Management
  console.log('\n4️⃣ Testing Session Management...');
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (!error) {
      console.log('✅ Session management working');
      if (session) {
        console.log(`   Active session found for user: ${session.user.id}`);
      } else {
        console.log('   No active session (expected for new registration)');
      }
      results.sessionManagement = true;
    } else {
      console.log(`❌ Session management failed: ${error.message}`);
    }
  } catch (error) {
    console.log(`❌ Session management error: ${error.message}`);
  }

  // Test 5: Profile Integration
  console.log('\n5️⃣ Testing Profile Integration...');
  try {
    // Check if profiles table exists and is accessible
    const { data, error } = await supabase
      .from('profiles')
      .select('id, username, role')
      .limit(5);

    if (!error) {
      console.log('✅ Profile integration working');
      console.log(`   Found ${data?.length || 0} existing profiles`);
      if (data && data.length > 0) {
        console.log(`   Sample profile roles: ${data.map(p => p.role).join(', ')}`);
      }
      results.profileIntegration = true;
    } else {
      console.log(`❌ Profile integration failed: ${error.message}`);
    }
  } catch (error) {
    console.log(`❌ Profile integration error: ${error.message}`);
  }

  // Test 6: Error Handling
  console.log('\n6️⃣ Testing Error Handling...');
  try {
    // Test with invalid credentials
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'wrongpassword'
    });

    if (error) {
      console.log('✅ Error handling working');
      console.log(`   Error message: ${error.message}`);
      results.errorHandling = true;
    } else {
      console.log('❌ Error handling not working - invalid login succeeded');
    }
  } catch (error) {
    console.log(`✅ Error handling working (caught exception): ${error.message}`);
    results.errorHandling = true;
  }

  // Test 7: Security Features
  console.log('\n7️⃣ Testing Security Features...');
  try {
    // Test password requirements
    const { data, error } = await supabase.auth.signUp({
      email: `weak.${Date.now()}@gmail.com`,
      password: '123' // Weak password
    });

    if (error && error.message.includes('Password')) {
      console.log('✅ Password security enforced');
      console.log(`   Security message: ${error.message}`);
      results.securityFeatures = true;
    } else if (error) {
      console.log(`⚠️  Other security error: ${error.message}`);
      results.securityFeatures = true;
    } else {
      console.log('⚠️  Weak password accepted - consider strengthening requirements');
    }
  } catch (error) {
    console.log(`❌ Security test error: ${error.message}`);
  }

  return results;
}

/**
 * Generate comprehensive report
 */
function generateReport(results) {
  console.log('\n📊 COMPREHENSIVE AUTHENTICATION ASSESSMENT');
  console.log('==========================================');
  
  const tests = [
    { name: 'Database Connection', key: 'connection', weight: 2 },
    { name: 'User Registration', key: 'registration', weight: 3 },
    { name: 'Email Confirmation', key: 'emailConfirmationRequired', weight: 2 },
    { name: 'Existing User Handling', key: 'existingUserLogin', weight: 1 },
    { name: 'Session Management', key: 'sessionManagement', weight: 2 },
    { name: 'Profile Integration', key: 'profileIntegration', weight: 2 },
    { name: 'Error Handling', key: 'errorHandling', weight: 1 },
    { name: 'Security Features', key: 'securityFeatures', weight: 1 }
  ];

  let totalWeight = 0;
  let passedWeight = 0;

  tests.forEach(test => {
    const status = results[test.key] ? '✅ PASS' : '❌ FAIL';
    console.log(`${test.name}: ${status} (Weight: ${test.weight})`);
    
    totalWeight += test.weight;
    if (results[test.key]) {
      passedWeight += test.weight;
    }
  });

  const weightedScore = (passedWeight / totalWeight * 100).toFixed(1);
  console.log(`\nWeighted Score: ${passedWeight}/${totalWeight} (${weightedScore}%)`);

  // Map to authentication checkpoints
  const authCheckpoints = 12;
  const completedCheckpoints = Math.round((passedWeight / totalWeight) * authCheckpoints);
  
  console.log(`\n📈 PRODUCTION READINESS UPDATE:`);
  console.log(`Authentication checkpoints: ${completedCheckpoints}/${authCheckpoints} (${(completedCheckpoints/authCheckpoints*100).toFixed(1)}%)`);

  // Assessment
  if (weightedScore >= 80) {
    console.log('\n✅ Authentication system is production-ready!');
  } else if (weightedScore >= 60) {
    console.log('\n⚠️  Authentication system is functional but needs improvements');
  } else {
    console.log('\n❌ Authentication system needs significant work before production');
  }

  // Recommendations
  console.log('\n🎯 RECOMMENDATIONS:');
  if (!results.connection) {
    console.log('- Fix database connection issues');
  }
  if (!results.registration) {
    console.log('- Resolve user registration problems');
  }
  if (results.emailConfirmationRequired) {
    console.log('- Configure email confirmation for production');
    console.log('- Set up SMTP service for email delivery');
  }
  if (!results.profileIntegration) {
    console.log('- Fix profile creation and management');
  }
  if (!results.securityFeatures) {
    console.log('- Strengthen password requirements');
    console.log('- Review security configurations');
  }

  return {
    score: weightedScore,
    checkpoints: completedCheckpoints,
    recommendations: tests.filter(t => !results[t.key]).map(t => t.name)
  };
}

// Run comprehensive testing
testAuthCapabilities()
  .then(generateReport)
  .then((summary) => {
    console.log('\n🏁 Comprehensive authentication testing completed');
    console.log(`Final Score: ${summary.score}% (${summary.checkpoints}/12 checkpoints)`);
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Comprehensive testing failed:', error);
    process.exit(1);
  });

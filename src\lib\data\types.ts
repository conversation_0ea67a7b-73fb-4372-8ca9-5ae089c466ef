/**
 * Unified Data Types
 * 
 * Consolidated type definitions for the unified data service architecture.
 * These types ensure consistency across all data operations.
 * 
 * @module DataTypes
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import type { Database } from '@/types/supabase';

// ============================================================================
// DATABASE TABLE TYPES
// ============================================================================

export type Activity = Database['public']['Tables']['activities']['Row'];
export type Event = Database['public']['Tables']['events']['Row'];
export type Announcement = Database['public']['Tables']['announcements']['Row'];
export type Festival = Database['public']['Tables']['festivals']['Row'];
export type Profile = Database['public']['Tables']['profiles']['Row'];

// ============================================================================
// EXTENDED TYPES
// ============================================================================

/**
 * Activity with related details for complex queries
 */
export interface ActivityWithDetails extends Activity {
  event?: Event;
  parent_activity?: Activity;
  sub_activities?: Activity[];
  attendees_count?: number;
  user_attendance?: {
    status: 'going' | 'interested' | 'not_going';
    notes?: string;
  };
}

/**
 * Event with related activities
 */
export interface EventWithActivities extends Event {
  activities?: Activity[];
  activities_count?: number;
}

/**
 * Festival with related events and activities
 */
export interface FestivalWithDetails extends Festival {
  events?: Event[];
  activities?: Activity[];
  events_count?: number;
  activities_count?: number;
}

// ============================================================================
// FILTER TYPES
// ============================================================================

/**
 * Activity filtering options
 */
export interface ActivityFilters {
  festivalId?: string;
  eventId?: string;
  type?: string;
  category?: string;
  featured?: boolean;
  status?: 'draft' | 'published' | 'cancelled';
  startDate?: string;
  endDate?: string;
  location?: string;
  hasCapacity?: boolean;
}

/**
 * Event filtering options
 */
export interface EventFilters {
  festivalId?: string;
  category?: string;
  featured?: boolean;
  status?: 'draft' | 'published' | 'cancelled';
  startDate?: string;
  endDate?: string;
  location?: string;
}

/**
 * Announcement filtering options
 */
export interface AnnouncementFilters {
  festivalId?: string;
  type?: string;
  featured?: boolean;
  status?: 'draft' | 'published' | 'archived';
  urgent?: boolean;
}

/**
 * Festival filtering options
 */
export interface FestivalFilters {
  status?: 'upcoming' | 'active' | 'past';
  featured?: boolean;
  location?: string;
  genre?: string;
}

// ============================================================================
// DATA CREATION TYPES
// ============================================================================

export type CreateActivityData = Omit<Activity, 'id' | 'created_at' | 'updated_at'>;
export type UpdateActivityData = Partial<Omit<Activity, 'id' | 'created_at' | 'updated_at'>>;

export type CreateEventData = Omit<Event, 'id' | 'created_at' | 'updated_at'>;
export type UpdateEventData = Partial<Omit<Event, 'id' | 'created_at' | 'updated_at'>>;

export type CreateAnnouncementData = Omit<Announcement, 'id' | 'created_at' | 'updated_at'>;
export type UpdateAnnouncementData = Partial<Omit<Announcement, 'id' | 'created_at' | 'updated_at'>>;

export type CreateFestivalData = Omit<Festival, 'id' | 'created_at' | 'updated_at'>;
export type UpdateFestivalData = Partial<Omit<Festival, 'id' | 'created_at' | 'updated_at'>>;

// ============================================================================
// RESPONSE TYPES
// ============================================================================

/**
 * Standard API response wrapper
 */
export interface ApiResponse<T> {
  data: T;
  error: null;
  success: true;
}

export interface ApiError {
  data: null;
  error: {
    message: string;
    code?: string;
    details?: any;
  };
  success: false;
}

export type ApiResult<T> = ApiResponse<T> | ApiError;

/**
 * Paginated response type
 */
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// ============================================================================
// REAL-TIME TYPES
// ============================================================================

/**
 * Real-time event types
 */
export type RealtimeEventType = 'INSERT' | 'UPDATE' | 'DELETE';

/**
 * Real-time payload structure
 */
export interface RealtimePayload<T> {
  eventType: RealtimeEventType;
  new: T | null;
  old: T | null;
  table: string;
  schema: string;
}

/**
 * Real-time subscription callback
 */
export type RealtimeCallback<T> = (payload: RealtimePayload<T>) => void;

/**
 * Real-time subscription options
 */
export interface RealtimeSubscriptionOptions {
  event?: RealtimeEventType | '*';
  filter?: string;
}

// ============================================================================
// HOOK RESULT TYPES
// ============================================================================

/**
 * Standard hook result for single data items
 */
export interface UseDataResult<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

/**
 * Standard hook result for data arrays
 */
export interface UseDataArrayResult<T> {
  data: T[];
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

/**
 * Hook result with mutation capabilities
 */
export interface UseMutationResult<T, TVariables = any> {
  mutate: (variables: TVariables) => Promise<T>;
  isLoading: boolean;
  error: Error | null;
  data: T | null;
  reset: () => void;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Make specific fields optional
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Make specific fields required
 */
export type RequiredBy<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * Extract table names from database schema
 */
export type TableName = keyof Database['public']['Tables'];

/**
 * Extract row type from table name
 */
export type TableRow<T extends TableName> = Database['public']['Tables'][T]['Row'];

/**
 * Extract insert type from table name
 */
export type TableInsert<T extends TableName> = Database['public']['Tables'][T]['Insert'];

/**
 * Extract update type from table name
 */
export type TableUpdate<T extends TableName> = Database['public']['Tables'][T]['Update'];

// ============================================================================
// VALIDATION TYPES
// ============================================================================

/**
 * Field validation result
 */
export interface FieldValidation {
  field: string;
  isValid: boolean;
  message?: string;
}

/**
 * Form validation result
 */
export interface FormValidation {
  isValid: boolean;
  fields: FieldValidation[];
  errors: string[];
}

// ============================================================================
// SEARCH AND SORTING TYPES
// ============================================================================

/**
 * Sort direction
 */
export type SortDirection = 'asc' | 'desc';

/**
 * Sort configuration
 */
export interface SortConfig {
  field: string;
  direction: SortDirection;
}

/**
 * Search configuration
 */
export interface SearchConfig {
  query: string;
  fields: string[];
  caseSensitive?: boolean;
}

/**
 * Pagination configuration
 */
export interface PaginationConfig {
  page: number;
  limit: number;
  offset?: number;
}

// ============================================================================
// EXPORT ALL TYPES
// ============================================================================

export type {
  Database
} from '@/types/supabase';

# Supabase Integration Solutions

## 1. Centralized Client Initialization

### Solution
Create a single, centralized Supabase client instance with proper configuration and error handling.

### Implementation
```typescript
// src/lib/supabase/client.ts
import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/database'

// Environment validation
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error(
    'Missing Supabase environment variables. Ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set.'
  )
}

// Create and export a single client instance
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    storage: window.localStorage,
  }
})
```

### Benefits
- Single source of truth for Supabase configuration
- Consistent client behavior across the application
- Reduced bundle size
- Easier testing and mocking

## 2. Consistent Service Layer

### Solution
Implement a comprehensive service layer that encapsulates all Supabase operations, with consistent patterns for error handling and type safety.

### Implementation
```typescript
// src/lib/supabase/services/base-service.ts
import { supabase } from '../client'
import type { PostgrestError } from '@supabase/supabase-js'

export type ServiceResponse<T> = {
  data: T | null
  error: PostgrestError | Error | null
}

export abstract class BaseService {
  protected async handleResponse<T>(
    promise: Promise<{ data: T | null; error: PostgrestError | null }>
  ): Promise<ServiceResponse<T>> {
    try {
      const { data, error } = await promise
      return { data, error }
    } catch (error) {
      return { data: null, error: error as Error }
    }
  }
}

// src/lib/supabase/services/profile-service.ts
import { BaseService, ServiceResponse } from './base-service'
import { supabase } from '../client'
import type { Profile } from '@/types/database'

export class ProfileService extends BaseService {
  async getProfile(userId: string): Promise<ServiceResponse<Profile>> {
    return this.handleResponse(
      supabase.from('profiles').select().eq('id', userId).single()
    )
  }
  
  // Additional methods...
}

// Export service instances
export const profileService = new ProfileService()
```

### Benefits
- Consistent error handling
- Proper type safety
- Encapsulated business logic
- Easier testing and mocking
- Clear separation of concerns

## 3. Standardized Error Handling

### Solution
Implement a consistent approach to error handling for all Supabase operations, using a standardized response format and global error handling.

### Implementation
```typescript
// src/lib/supabase/error-handling.ts
import { PostgrestError } from '@supabase/supabase-js'

export type ErrorWithCode = {
  code: string
  message: string
  details?: string
}

export function formatSupabaseError(error: PostgrestError): ErrorWithCode {
  return {
    code: error.code,
    message: error.message,
    details: error.details
  }
}

export function isPostgrestError(error: any): error is PostgrestError {
  return error && typeof error === 'object' && 'code' in error && 'message' in error
}

// Global error handler
export function handleSupabaseError(error: unknown): ErrorWithCode {
  if (isPostgrestError(error)) {
    return formatSupabaseError(error)
  }
  
  return {
    code: 'unknown_error',
    message: error instanceof Error ? error.message : 'An unknown error occurred'
  }
}
```

### Benefits
- Consistent error handling across the application
- Improved error messages for users
- Easier debugging
- Support for global error tracking

## 4. Enhanced Type Safety

### Solution
Implement proper type safety for all Supabase operations, using TypeScript features and runtime validation.

### Implementation
```typescript
// src/lib/supabase/type-safety.ts
import { z } from 'zod'
import type { Database } from '@/types/database'

// Define Zod schemas for runtime validation
export const profileSchema = z.object({
  id: z.string().uuid(),
  username: z.string().min(3).max(50),
  full_name: z.string().nullable(),
  avatar_url: z.string().url().nullable(),
  role: z.enum(['user', 'admin']).default('user')
})

export type Profile = z.infer<typeof profileSchema>

// Type-safe query helper
export async function queryProfiles() {
  const { data, error } = await supabase.from('profiles').select()
  
  if (error) throw error
  
  // Runtime validation
  const validationResult = z.array(profileSchema).safeParse(data)
  
  if (!validationResult.success) {
    console.error('Validation error:', validationResult.error)
    throw new Error('Data validation failed')
  }
  
  return validationResult.data
}
```

### Benefits
- Runtime type validation
- Consistent type definitions
- Improved error messages
- Better IDE assistance

## 5. Optimized Query Patterns

### Solution
Implement efficient query patterns that minimize data transfer and reduce the number of requests.

### Implementation
```typescript
// src/lib/supabase/services/festival-service.ts
import { BaseService, ServiceResponse } from './base-service'
import { supabase } from '../client'
import type { Festival, Activity } from '@/types/database'

export class FestivalService extends BaseService {
  // Efficient: Select only needed fields
  async getFestivalsList(): Promise<ServiceResponse<Pick<Festival, 'id' | 'name' | 'start_date' | 'image'>[]>> {
    return this.handleResponse(
      supabase.from('festivals').select('id, name, start_date, image')
    )
  }
  
  // Efficient: Use joins instead of multiple requests
  async getFestivalWithActivities(festivalId: string): Promise<ServiceResponse<Festival & { activities: Activity[] }>> {
    return this.handleResponse(
      supabase
        .from('festivals')
        .select(`
          *,
          activities(*)
        `)
        .eq('id', festivalId)
        .single()
    )
  }
}

export const festivalService = new FestivalService()
```

### Benefits
- Reduced bandwidth usage
- Faster page loads
- Fewer network requests
- Improved user experience
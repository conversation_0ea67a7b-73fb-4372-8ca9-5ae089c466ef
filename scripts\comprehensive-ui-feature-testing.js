#!/usr/bin/env node

/**
 * Comprehensive UI/UX Feature Testing
 * 
 * This script tests actual user-facing features, navigation, and UI elements
 * to assess production readiness for real users in 2025.
 */

import { createClient } from '@supabase/supabase-js';
import { promises as fs } from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'ui-feature-testing-evidence';

console.log('🎨 COMPREHENSIVE UI/UX FEATURE TESTING');
console.log('=====================================');
console.log(`🕐 Start Time: ${new Date().toISOString()}`);
console.log(`🌐 Testing URL: ${APP_URL}`);

// Ensure evidence directory exists
async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

// HTTP request helper
async function makeRequest(url, options = {}) {
  const http = await import('http');
  const https = await import('https');

  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https.default : http.default;

    const req = client.request(url, {
      timeout: 10000,
      ...options
    }, (res) => {
      let data = '';

      res.on('data', chunk => {
        data += chunk;
      });

      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          statusMessage: res.statusMessage,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

// Test 1: Navigation Structure Analysis
async function testNavigationStructure() {
  console.log('\n🧭 Test 1: Navigation Structure Analysis');
  console.log('=======================================');
  
  try {
    const response = await makeRequest(APP_URL);
    const html = response.data;
    
    // Analyze navigation elements
    const navigationAnalysis = {
      testName: 'Navigation Structure Analysis',
      timestamp: new Date().toISOString(),
      hasNavigation: false,
      navigationElements: [],
      routeLinks: [],
      menuItems: [],
      userInterfaceElements: {
        hasHeader: false,
        hasSidebar: false,
        hasFooter: false,
        hasMainContent: false
      }
    };
    
    // Check for navigation patterns
    const navPatterns = [
      { pattern: /<nav[^>]*>/i, name: 'nav element' },
      { pattern: /class="[^"]*nav[^"]*"/i, name: 'nav class' },
      { pattern: /class="[^"]*menu[^"]*"/i, name: 'menu class' },
      { pattern: /class="[^"]*sidebar[^"]*"/i, name: 'sidebar class' },
      { pattern: /class="[^"]*header[^"]*"/i, name: 'header class' },
      { pattern: /class="[^"]*footer[^"]*"/i, name: 'footer class' }
    ];
    
    navPatterns.forEach(({ pattern, name }) => {
      if (pattern.test(html)) {
        navigationAnalysis.navigationElements.push(name);
        navigationAnalysis.hasNavigation = true;
      }
    });
    
    // Check for UI structure elements
    navigationAnalysis.userInterfaceElements.hasHeader = /<header/i.test(html) || /class="[^"]*header[^"]*"/i.test(html);
    navigationAnalysis.userInterfaceElements.hasSidebar = /class="[^"]*sidebar[^"]*"/i.test(html);
    navigationAnalysis.userInterfaceElements.hasFooter = /<footer/i.test(html) || /class="[^"]*footer[^"]*"/i.test(html);
    navigationAnalysis.userInterfaceElements.hasMainContent = /<main/i.test(html) || /class="[^"]*main[^"]*"/i.test(html);
    
    // Extract potential route links
    const linkMatches = html.match(/href="[^"]*"/gi) || [];
    navigationAnalysis.routeLinks = linkMatches
      .map(link => link.replace(/href="|"/g, ''))
      .filter(link => link.startsWith('/') || link.startsWith('#'))
      .slice(0, 10); // Limit to first 10 for analysis
    
    console.log(`🔍 Navigation Analysis:`);
    console.log(`   - Has Navigation: ${navigationAnalysis.hasNavigation ? '✅' : '❌'}`);
    console.log(`   - Navigation Elements: ${navigationAnalysis.navigationElements.length}`);
    console.log(`   - Route Links Found: ${navigationAnalysis.routeLinks.length}`);
    console.log(`   - Header: ${navigationAnalysis.userInterfaceElements.hasHeader ? '✅' : '❌'}`);
    console.log(`   - Sidebar: ${navigationAnalysis.userInterfaceElements.hasSidebar ? '✅' : '❌'}`);
    console.log(`   - Footer: ${navigationAnalysis.userInterfaceElements.hasFooter ? '✅' : '❌'}`);
    console.log(`   - Main Content: ${navigationAnalysis.userInterfaceElements.hasMainContent ? '✅' : '❌'}`);
    
    if (navigationAnalysis.routeLinks.length > 0) {
      console.log(`   - Sample Routes: ${navigationAnalysis.routeLinks.slice(0, 5).join(', ')}`);
    }
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/01-navigation-analysis.json`,
      JSON.stringify(navigationAnalysis, null, 2)
    );
    
    return navigationAnalysis;
    
  } catch (error) {
    console.log(`❌ Navigation analysis failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Test 2: Route Accessibility Testing
async function testRouteAccessibility(navigationData) {
  console.log('\n🛣️ Test 2: Route Accessibility Testing');
  console.log('=====================================');
  
  // Common routes to test for a festival app
  const commonRoutes = [
    '/',
    '/auth',
    '/login',
    '/register',
    '/signup',
    '/profile',
    '/dashboard',
    '/discover',
    '/festivals',
    '/events',
    '/activities',
    '/famhub',
    '/admin',
    '/settings',
    '/about',
    '/help'
  ];
  
  // Combine common routes with discovered routes
  const routesToTest = [...new Set([...commonRoutes, ...(navigationData.routeLinks || [])])];
  
  const routeResults = [];
  
  for (const route of routesToTest) {
    try {
      const fullUrl = route.startsWith('http') ? route : `${APP_URL}${route}`;
      const startTime = Date.now();
      
      const response = await makeRequest(fullUrl);
      const loadTime = Date.now() - startTime;
      
      const routeResult = {
        route: route,
        fullUrl: fullUrl,
        statusCode: response.statusCode,
        loadTimeMs: loadTime,
        accessible: response.statusCode < 400,
        hasContent: response.data.length > 100,
        contentLength: response.data.length
      };
      
      // Basic content analysis
      if (response.statusCode === 200) {
        const html = response.data;
        routeResult.analysis = {
          hasTitle: /<title[^>]*>([^<]+)<\/title>/i.test(html),
          hasForm: /<form/i.test(html),
          hasButtons: /<button/i.test(html),
          hasInputs: /<input/i.test(html),
          hasImages: /<img/i.test(html),
          hasLinks: /<a[^>]*href/i.test(html)
        };
        
        // Extract title if present
        const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
        if (titleMatch) {
          routeResult.pageTitle = titleMatch[1].trim();
        }
      }
      
      routeResults.push(routeResult);
      
      const status = response.statusCode < 400 ? '✅' : '❌';
      console.log(`${status} ${route}: ${response.statusCode} (${loadTime}ms)`);
      
      if (routeResult.pageTitle) {
        console.log(`   Title: "${routeResult.pageTitle}"`);
      }
      
    } catch (error) {
      const routeResult = {
        route: route,
        fullUrl: `${APP_URL}${route}`,
        accessible: false,
        error: error.message
      };
      
      routeResults.push(routeResult);
      console.log(`❌ ${route}: ${error.message}`);
    }
  }
  
  const accessibleRoutes = routeResults.filter(r => r.accessible);
  const routeTestResults = {
    testName: 'Route Accessibility Testing',
    timestamp: new Date().toISOString(),
    totalRoutes: routeResults.length,
    accessibleRoutes: accessibleRoutes.length,
    successRate: ((accessibleRoutes.length / routeResults.length) * 100).toFixed(1),
    routeResults: routeResults
  };
  
  console.log(`\n📊 Route Testing Summary:`);
  console.log(`   - Total Routes Tested: ${routeTestResults.totalRoutes}`);
  console.log(`   - Accessible Routes: ${routeTestResults.accessibleRoutes}`);
  console.log(`   - Success Rate: ${routeTestResults.successRate}%`);
  
  await fs.writeFile(
    `${EVIDENCE_DIR}/02-route-accessibility.json`,
    JSON.stringify(routeTestResults, null, 2)
  );
  
  return routeTestResults;
}

// Test 3: Feature Completeness Assessment
async function testFeatureCompleteness() {
  console.log('\n🎯 Test 3: Feature Completeness Assessment');
  console.log('==========================================');
  
  try {
    const response = await makeRequest(APP_URL);
    const html = response.data;
    
    // Festival app feature indicators
    const featureIndicators = {
      authentication: {
        login: /login|sign.?in/i.test(html),
        register: /register|sign.?up/i.test(html),
        logout: /logout|sign.?out/i.test(html),
        profile: /profile|account/i.test(html)
      },
      festivalFeatures: {
        festivals: /festival/i.test(html),
        events: /event/i.test(html),
        activities: /activit/i.test(html),
        discovery: /discover|search|find/i.test(html),
        community: /community|connect|friend/i.test(html)
      },
      userInterface: {
        navigation: /<nav|class="[^"]*nav/i.test(html),
        forms: /<form/i.test(html),
        buttons: /<button/i.test(html),
        modals: /modal|popup|dialog/i.test(html),
        responsive: /viewport|responsive|mobile/i.test(html)
      },
      modernFeatures: {
        search: /search/i.test(html),
        filters: /filter/i.test(html),
        messaging: /message|chat/i.test(html),
        notifications: /notification|alert/i.test(html),
        social: /share|like|follow/i.test(html)
      }
    };
    
    // Calculate feature scores
    const featureScores = {};
    let totalFeatures = 0;
    let presentFeatures = 0;
    
    Object.entries(featureIndicators).forEach(([category, features]) => {
      const categoryPresent = Object.values(features).filter(Boolean).length;
      const categoryTotal = Object.keys(features).length;
      
      featureScores[category] = {
        present: categoryPresent,
        total: categoryTotal,
        percentage: ((categoryPresent / categoryTotal) * 100).toFixed(1)
      };
      
      totalFeatures += categoryTotal;
      presentFeatures += categoryPresent;
    });
    
    const overallFeatureScore = ((presentFeatures / totalFeatures) * 100).toFixed(1);
    
    const featureAssessment = {
      testName: 'Feature Completeness Assessment',
      timestamp: new Date().toISOString(),
      overallScore: overallFeatureScore,
      totalFeatures: totalFeatures,
      presentFeatures: presentFeatures,
      categoryScores: featureScores,
      featureIndicators: featureIndicators
    };
    
    console.log(`📊 Feature Completeness Analysis:`);
    console.log(`   - Overall Score: ${overallFeatureScore}%`);
    console.log(`   - Features Present: ${presentFeatures}/${totalFeatures}`);
    
    Object.entries(featureScores).forEach(([category, score]) => {
      console.log(`   - ${category}: ${score.present}/${score.total} (${score.percentage}%)`);
    });
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/03-feature-completeness.json`,
      JSON.stringify(featureAssessment, null, 2)
    );
    
    return featureAssessment;
    
  } catch (error) {
    console.log(`❌ Feature assessment failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Test 4: User Experience Quality Assessment
async function testUserExperienceQuality() {
  console.log('\n✨ Test 4: User Experience Quality Assessment');
  console.log('============================================');
  
  try {
    const response = await makeRequest(APP_URL);
    const html = response.data;
    
    // UX quality indicators
    const uxQualityChecks = {
      visualDesign: {
        hasCSS: /<link[^>]*\.css|<style/i.test(html),
        hasCustomStyling: /class="[^"]*[a-z]+-[a-z]+/i.test(html), // Tailwind/CSS framework patterns
        hasImages: /<img/i.test(html),
        hasIcons: /icon|svg/i.test(html),
        hasColors: /color|bg-|text-/i.test(html)
      },
      interactivity: {
        hasButtons: /<button/i.test(html),
        hasLinks: /<a[^>]*href/i.test(html),
        hasForms: /<form/i.test(html),
        hasInputs: /<input/i.test(html),
        hasJavaScript: /<script/i.test(html)
      },
      accessibility: {
        hasAltText: /alt="/i.test(html),
        hasLabels: /<label/i.test(html),
        hasHeadings: /<h[1-6]/i.test(html),
        hasSemanticHTML: /<main|<section|<article|<aside|<header|<footer/i.test(html),
        hasAriaLabels: /aria-/i.test(html)
      },
      modernStandards: {
        hasViewportMeta: /<meta[^>]*viewport/i.test(html),
        hasCharsetMeta: /<meta[^>]*charset/i.test(html),
        hasTitle: /<title/i.test(html),
        hasDescription: /<meta[^>]*description/i.test(html),
        hasModernHTML: /<!DOCTYPE html>/i.test(html)
      }
    };
    
    // Calculate UX scores
    const uxScores = {};
    let totalUXChecks = 0;
    let passedUXChecks = 0;
    
    Object.entries(uxQualityChecks).forEach(([category, checks]) => {
      const categoryPassed = Object.values(checks).filter(Boolean).length;
      const categoryTotal = Object.keys(checks).length;
      
      uxScores[category] = {
        passed: categoryPassed,
        total: categoryTotal,
        percentage: ((categoryPassed / categoryTotal) * 100).toFixed(1)
      };
      
      totalUXChecks += categoryTotal;
      passedUXChecks += categoryPassed;
    });
    
    const overallUXScore = ((passedUXChecks / totalUXChecks) * 100).toFixed(1);
    
    // Assess 2025 readiness
    const readinessAssessment = {
      score: parseFloat(overallUXScore),
      level: overallUXScore >= 80 ? 'Production Ready' : 
             overallUXScore >= 60 ? 'Needs Improvement' : 
             'Significant Development Required',
      competitiveReadiness: overallUXScore >= 75 ? 'Competitive' : 'Below Market Standards'
    };
    
    const uxAssessment = {
      testName: 'User Experience Quality Assessment',
      timestamp: new Date().toISOString(),
      overallUXScore: overallUXScore,
      totalChecks: totalUXChecks,
      passedChecks: passedUXChecks,
      categoryScores: uxScores,
      readinessAssessment: readinessAssessment,
      uxQualityChecks: uxQualityChecks
    };
    
    console.log(`✨ User Experience Quality Analysis:`);
    console.log(`   - Overall UX Score: ${overallUXScore}%`);
    console.log(`   - Readiness Level: ${readinessAssessment.level}`);
    console.log(`   - Competitive Status: ${readinessAssessment.competitiveReadiness}`);
    
    Object.entries(uxScores).forEach(([category, score]) => {
      console.log(`   - ${category}: ${score.passed}/${score.total} (${score.percentage}%)`);
    });
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/04-ux-quality-assessment.json`,
      JSON.stringify(uxAssessment, null, 2)
    );
    
    return uxAssessment;
    
  } catch (error) {
    console.log(`❌ UX assessment failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Generate Comprehensive UI/UX Report
async function generateUIFeatureReport(navigationData, routeData, featureData, uxData) {
  console.log('\n📊 COMPREHENSIVE UI/UX FEATURE REPORT');
  console.log('=====================================');
  
  const report = {
    sessionInfo: {
      timestamp: new Date().toISOString(),
      applicationUrl: APP_URL,
      evidenceDirectory: EVIDENCE_DIR
    },
    testResults: {
      navigation: {
        hasNavigation: navigationData.hasNavigation || false,
        elementsFound: navigationData.navigationElements?.length || 0,
        routeLinksFound: navigationData.routeLinks?.length || 0
      },
      routes: {
        totalTested: routeData.totalRoutes || 0,
        accessible: routeData.accessibleRoutes || 0,
        successRate: parseFloat(routeData.successRate) || 0
      },
      features: {
        overallScore: parseFloat(featureData.overallScore) || 0,
        presentFeatures: featureData.presentFeatures || 0,
        totalFeatures: featureData.totalFeatures || 0
      },
      userExperience: {
        overallScore: parseFloat(uxData.overallUXScore) || 0,
        readinessLevel: uxData.readinessAssessment?.level || 'Unknown',
        competitiveStatus: uxData.readinessAssessment?.competitiveReadiness || 'Unknown'
      }
    },
    productionReadinessAssessment: {
      uiCompleteness: 0,
      featureRichness: 0,
      userExperienceQuality: 0,
      overallReadiness: 0,
      recommendationsFor2025: []
    }
  };
  
  // Calculate production readiness scores
  report.productionReadinessAssessment.uiCompleteness = 
    (report.testResults.navigation.hasNavigation ? 25 : 0) +
    (report.testResults.routes.successRate > 50 ? 25 : 0) +
    (report.testResults.routes.accessible > 5 ? 25 : 0) +
    (navigationData.userInterfaceElements?.hasHeader ? 25 : 0);
  
  report.productionReadinessAssessment.featureRichness = report.testResults.features.overallScore;
  report.productionReadinessAssessment.userExperienceQuality = report.testResults.userExperience.overallScore;
  
  report.productionReadinessAssessment.overallReadiness = 
    (report.productionReadinessAssessment.uiCompleteness * 0.3 +
     report.productionReadinessAssessment.featureRichness * 0.4 +
     report.productionReadinessAssessment.userExperienceQuality * 0.3);
  
  // Generate 2025 recommendations
  if (report.productionReadinessAssessment.featureRichness < 70) {
    report.productionReadinessAssessment.recommendationsFor2025.push('Develop core festival features (discovery, community, events)');
  }
  if (report.productionReadinessAssessment.userExperienceQuality < 80) {
    report.productionReadinessAssessment.recommendationsFor2025.push('Improve UI/UX design to meet 2025 standards');
  }
  if (report.testResults.routes.successRate < 80) {
    report.productionReadinessAssessment.recommendationsFor2025.push('Fix broken routes and improve navigation');
  }
  
  console.log(`📊 Production Readiness Assessment:`);
  console.log(`   - UI Completeness: ${report.productionReadinessAssessment.uiCompleteness.toFixed(1)}%`);
  console.log(`   - Feature Richness: ${report.productionReadinessAssessment.featureRichness.toFixed(1)}%`);
  console.log(`   - UX Quality: ${report.productionReadinessAssessment.userExperienceQuality.toFixed(1)}%`);
  console.log(`   - Overall Readiness: ${report.productionReadinessAssessment.overallReadiness.toFixed(1)}%`);
  
  if (report.productionReadinessAssessment.recommendationsFor2025.length > 0) {
    console.log(`\n💡 2025 Production Recommendations:`);
    report.productionReadinessAssessment.recommendationsFor2025.forEach(rec => {
      console.log(`   - ${rec}`);
    });
  }
  
  await fs.writeFile(
    `${EVIDENCE_DIR}/comprehensive-ui-feature-report.json`,
    JSON.stringify(report, null, 2)
  );
  
  return report;
}

// Main execution
async function runComprehensiveUIFeatureTesting() {
  await ensureEvidenceDir();
  
  try {
    const navigationData = await testNavigationStructure();
    const routeData = await testRouteAccessibility(navigationData);
    const featureData = await testFeatureCompleteness();
    const uxData = await testUserExperienceQuality();
    
    const finalReport = await generateUIFeatureReport(navigationData, routeData, featureData, uxData);
    
    console.log('\n🏁 Comprehensive UI/UX feature testing completed');
    return finalReport;
    
  } catch (error) {
    console.error('\n💥 UI/UX feature testing failed:', error);
    throw error;
  }
}

// Run the comprehensive testing
runComprehensiveUIFeatureTesting()
  .then(() => {
    console.log('\n✅ All UI/UX feature tests completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ UI/UX feature testing failed:', error);
    process.exit(1);
  });

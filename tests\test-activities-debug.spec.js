/**
 * Debug Activities Page Issues
 * 
 * Debug test to identify why the Activities page isn't working
 */

import { test, expect } from '@playwright/test';

test('Debug Activities Page Issues', async ({ page }) => {
  console.log('🔍 DEBUGGING: Activities page issues...');
  
  // Capture console logs and errors
  const consoleLogs = [];
  const errors = [];
  
  page.on('console', msg => {
    consoleLogs.push(`${msg.type()}: ${msg.text()}`);
  });
  
  page.on('pageerror', error => {
    errors.push(error.message);
  });
  
  // Navigate to activities page
  await page.goto('/activities');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000);
  
  console.log('📊 Page Analysis:');
  
  // Check what's actually on the page
  const pageContent = await page.content();
  const hasReactRoot = pageContent.includes('id="root"');
  const hasErrorBoundary = pageContent.includes('error');
  const hasLoadingSpinner = await page.locator('.loading, [data-testid*="loading"]').count() > 0;
  
  console.log(`  Has React root: ${hasReactRoot}`);
  console.log(`  Has error boundary: ${hasErrorBoundary}`);
  console.log(`  Has loading spinner: ${hasLoadingSpinner}`);
  
  // Check for specific elements
  const bodyText = await page.locator('body').textContent();
  const hasAnyText = bodyText && bodyText.trim().length > 0;
  const hasActivitiesText = bodyText && bodyText.includes('Activities');
  const hasErrorText = bodyText && (bodyText.includes('Error') || bodyText.includes('error'));
  
  console.log(`  Has any text: ${hasAnyText}`);
  console.log(`  Has activities text: ${hasActivitiesText}`);
  console.log(`  Has error text: ${hasErrorText}`);
  
  // Check for specific components
  const cardElements = await page.locator('.card, [class*="card"]').count();
  const buttonElements = await page.locator('button').count();
  const inputElements = await page.locator('input').count();
  
  console.log(`  Card elements: ${cardElements}`);
  console.log(`  Button elements: ${buttonElements}`);
  console.log(`  Input elements: ${inputElements}`);
  
  await page.screenshot({ path: 'test-results/activities-debug-full.png', fullPage: true });
  
  // Print console logs
  console.log('\n📋 Console Logs:');
  consoleLogs.forEach(log => console.log(`  ${log}`));
  
  // Print errors
  console.log('\n❌ JavaScript Errors:');
  errors.forEach(error => console.log(`  ${error}`));
  
  // Check if the page is completely blank
  const isBlank = !hasAnyText || bodyText.trim().length < 50;
  
  console.log(`\n🎯 DIAGNOSIS:`);
  console.log(`  Page is blank: ${isBlank}`);
  console.log(`  JavaScript errors: ${errors.length}`);
  console.log(`  Console warnings: ${consoleLogs.filter(log => log.includes('warn')).length}`);
  
  if (isBlank) {
    console.log('🚨 CRITICAL: Page is completely blank - likely a component crash');
  } else if (errors.length > 0) {
    console.log('🚨 CRITICAL: JavaScript errors preventing proper rendering');
  } else if (!hasActivitiesText) {
    console.log('⚠️ WARNING: Page loads but Activities component not rendering');
  } else {
    console.log('✅ Page loads with Activities content');
  }
  
  console.log('✅ Activities debug completed');
});

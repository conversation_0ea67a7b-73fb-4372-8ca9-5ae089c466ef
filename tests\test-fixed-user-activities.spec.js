/**
 * Test Fixed User Activities Page
 * 
 * Tests the user Activities page after fixing database integration
 */

import { test, expect } from '@playwright/test';

test('Test Fixed User Activities Page', async ({ page }) => {
  console.log('🧪 Testing fixed user Activities page...');
  
  // Navigate to activities page as unauthenticated user
  await page.goto('/activities');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000); // Give time for database loading
  
  console.log('📝 Checking Activities page...');
  
  // Check if page loads correctly
  const pageLoaded = !page.url().includes('404') && !page.url().includes('error');
  const hasTitle = await page.locator('h1:has-text("Festival Activities")').count() > 0;
  const hasSearchBar = await page.locator('input[placeholder*="Search"]').count() > 0;
  const hasTabs = await page.locator('[role="tablist"]').count() > 0;
  
  console.log(`Page assessment:`);
  console.log(`  Page loaded: ${pageLoaded}`);
  console.log(`  Has title: ${hasTitle}`);
  console.log(`  Has search bar: ${hasSearchBar}`);
  console.log(`  Has tabs: ${hasTabs}`);
  
  await page.screenshot({ path: 'test-results/fixed-user-activities.png', fullPage: true });
  
  // Check for activity content
  const activityCards = await page.locator('.card, [data-testid*="activity"]').count();
  const hasActivityContent = await page.locator('text="Pipeline Test Activity", text="Schema Fixed Test Activity"').count() > 0;
  const hasNoActivitiesMessage = await page.locator('text="No activities found"').count() > 0;
  
  console.log(`Activity content:`);
  console.log(`  Activity cards: ${activityCards}`);
  console.log(`  Has test activities: ${hasActivityContent}`);
  console.log(`  Has no activities message: ${hasNoActivitiesMessage}`);
  
  // Test tab functionality
  console.log('🔗 Testing tab functionality...');
  
  const tabs = ['meetup', 'daily', 'challenges', 'upcoming'];
  for (const tab of tabs) {
    const tabButton = page.locator(`[value="${tab}"]`);
    if (await tabButton.isVisible()) {
      await tabButton.click();
      await page.waitForTimeout(1000);
      
      const tabActive = await tabButton.getAttribute('data-state') === 'active';
      console.log(`  ${tab} tab: ${tabActive ? 'working' : 'not working'}`);
    }
  }
  
  await page.screenshot({ path: 'test-results/fixed-user-activities-tabs.png', fullPage: true });
  
  // Test search functionality
  console.log('🔍 Testing search functionality...');
  
  const searchInput = page.locator('input[placeholder*="Search"]');
  if (await searchInput.isVisible()) {
    await searchInput.fill('test');
    await page.waitForTimeout(1000);
    
    const searchResults = await page.locator('.card').count();
    console.log(`  Search results for "test": ${searchResults} cards`);
    
    await searchInput.clear();
    await page.waitForTimeout(1000);
  }
  
  // Final assessment
  const activitiesWorking = pageLoaded && hasTitle && (activityCards > 0 || hasNoActivitiesMessage);
  
  console.log(`\n🎯 FINAL ASSESSMENT:`);
  console.log(`  Activities page working: ${activitiesWorking ? '✅ YES' : '❌ NO'}`);
  console.log(`  Database integration: ${activityCards > 0 ? '✅ WORKING' : '⚠️ NO DATA'}`);
  console.log(`  User interface: ${hasTitle && hasSearchBar && hasTabs ? '✅ WORKING' : '❌ BROKEN'}`);
  
  if (activitiesWorking) {
    console.log('🎉 SUCCESS: User Activities page is now working with real database data!');
  } else {
    console.log('❌ ISSUE: User Activities page still needs work');
  }
  
  console.log('✅ Fixed user Activities test completed');
});

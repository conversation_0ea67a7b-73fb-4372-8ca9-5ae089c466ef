{"validationSuite": "Admin Dashboard Comprehensive Validation", "timestamp": "2025-06-04T13:01:11.141Z", "databaseTables": {"tables": {"announcements": {"accessible": true, "recordCount": 1, "hasData": true}, "tips": {"accessible": true, "recordCount": 0, "hasData": false}, "faqs": {"accessible": true, "recordCount": 0, "hasData": false}, "profiles": {"accessible": true, "recordCount": 0, "hasData": false}, "festivals": {"accessible": true, "recordCount": 1, "hasData": true}, "events": {"accessible": true, "recordCount": 3, "hasData": true}, "activities": {"accessible": true, "recordCount": 15, "hasData": true}, "groups": {"accessible": true, "recordCount": 0, "hasData": false}, "group_members": {"accessible": false, "error": "infinite recursion detected in policy for relation \"group_members\""}, "chat_rooms": {"accessible": false, "error": "infinite recursion detected in policy for relation \"chat_room_members\""}, "chat_room_members": {"accessible": false, "error": "infinite recursion detected in policy for relation \"chat_room_members\""}, "chat_messages": {"accessible": false, "error": "infinite recursion detected in policy for relation \"chat_room_members\""}}, "timestamp": "2025-06-04T13:01:08.351Z"}, "adminFunctions": {"functions": {"is_admin": {"working": true, "result": null}, "is_super_admin": {"working": false, "error": "Could not find the function public.is_super_admin without parameters in the schema cache"}, "is_content_admin": {"working": false, "error": "Could not find the function public.is_content_admin without parameters in the schema cache"}, "can_manage_groups": {"working": false, "error": "Could not find the function public.can_manage_groups without parameters in the schema cache"}}, "timestamp": "2025-06-04T13:01:10.317Z"}, "contentManagement": {"announcements": {"create": false, "read": false, "update": false, "delete": false}, "tips": {"create": false, "read": false, "update": false, "delete": false}, "faqs": {"create": false, "read": false, "update": false, "delete": false}, "timestamp": "2025-06-04T13:01:10.756Z"}, "adminUsers": {"adminUsers": [], "adminCount": 0, "timestamp": "2025-06-04T13:01:11.059Z"}, "overallStatus": {"databaseTablesWorking": false, "adminFunctionsWorking": false, "contentManagementWorking": false, "adminUsersFound": false, "readyForProduction": false}}
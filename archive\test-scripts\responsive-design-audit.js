#!/usr/bin/env node

/**
 * Comprehensive Responsive Design and Cross-Platform Testing
 * 
 * This script performs detailed responsive design testing:
 * - Multiple viewport testing (mobile, tablet, desktop)
 * - Navigation responsiveness evaluation
 * - Content layout adaptation assessment
 * - Touch interface usability testing
 * - Cross-platform consistency verification
 */

import { chromium } from 'playwright';
import { promises as fs } from 'fs';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'responsive-design-evidence';

// Test user credentials
const TEST_USER = {
  email: `responsive.test.${Date.now()}@festivalfamily.test`,
  password: 'TestPassword123!',
  fullName: 'Responsive Test User'
};

// Viewport configurations for testing
const VIEWPORTS = [
  { name: 'Mobile Portrait', width: 375, height: 667, category: 'mobile' },
  { name: 'Mobile Landscape', width: 667, height: 375, category: 'mobile' },
  { name: 'Tablet Portrait', width: 768, height: 1024, category: 'tablet' },
  { name: 'Tablet Landscape', width: 1024, height: 768, category: 'tablet' },
  { name: 'Desktop Small', width: 1280, height: 720, category: 'desktop' },
  { name: 'Desktop Large', width: 1920, height: 1080, category: 'desktop' }
];

async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

async function authenticateUser(page) {
  console.log('\n🔐 Setting up authenticated session');
  console.log('==================================');
  
  await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(2000);
  
  // Sign up new user
  const signUpTab = await page.$('text=Sign Up');
  if (signUpTab) {
    await signUpTab.click();
    await page.waitForTimeout(1000);
  }
  
  const emailInput = await page.$('input[type="email"]');
  const passwordInput = await page.$('input[type="password"]');
  
  if (emailInput && passwordInput) {
    await emailInput.fill(TEST_USER.email);
    await passwordInput.fill(TEST_USER.password);
    
    const submitButton = await page.$('button[type="submit"]');
    if (submitButton) {
      await submitButton.click();
      await page.waitForTimeout(3000);
    }
  }
  
  console.log(`✅ Authenticated as: ${TEST_USER.email}`);
}

async function testViewportResponsiveness(page, viewport) {
  console.log(`\n📱 ${viewport.name.toUpperCase()} TESTING (${viewport.width}x${viewport.height})`);
  console.log('='.repeat(viewport.name.length + 25));
  
  // Set viewport
  await page.setViewportSize({ width: viewport.width, height: viewport.height });
  await page.goto(APP_URL, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  // Capture viewport
  const screenshotName = `${viewport.name.toLowerCase().replace(/\s+/g, '-')}-${viewport.width}x${viewport.height}.png`;
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/${screenshotName}`,
    fullPage: true 
  });
  
  // Analyze layout and navigation
  const navElement = await page.$('nav');
  const topNav = await page.$('nav:not([class*="bottom"])');
  const bottomNav = await page.$('[class*="bottom"]') || await page.$('[class*="fixed"]');
  const sidebar = await page.$('[class*="sidebar"]') || await page.$('[class*="drawer"]');
  
  // Check content layout
  const mainContent = await page.$('main') || await page.$('[role="main"]');
  const contentOverflow = await page.evaluate(() => {
    const body = document.body;
    return {
      hasHorizontalScroll: body.scrollWidth > body.clientWidth,
      hasVerticalScroll: body.scrollHeight > body.clientHeight,
      bodyWidth: body.scrollWidth,
      bodyHeight: body.scrollHeight,
      viewportWidth: window.innerWidth,
      viewportHeight: window.innerHeight
    };
  });
  
  // Test navigation functionality
  let navigationWorking = false;
  const profileLink = await page.$('a[href="/profile"]');
  if (profileLink) {
    try {
      // Check if element is visible and clickable
      const isVisible = await profileLink.isVisible();
      if (isVisible) {
        await profileLink.click();
        await page.waitForTimeout(2000);
        const currentUrl = page.url();
        navigationWorking = currentUrl.includes('/profile') || currentUrl.includes('/auth');
      }
    } catch (error) {
      console.log(`   Navigation test error: ${error.message}`);
    }
  }
  
  // Return to home for consistent testing
  await page.goto(APP_URL, { waitUntil: 'domcontentloaded', timeout: 10000 });
  await page.waitForTimeout(2000);
  
  // Check for responsive design indicators
  const hasResponsiveImages = await page.$$eval('img', imgs => 
    imgs.some(img => img.style.maxWidth === '100%' || img.classList.contains('responsive'))
  );
  
  const hasFlexLayout = await page.evaluate(() => {
    const elements = Array.from(document.querySelectorAll('*'));
    return elements.some(el => {
      const style = window.getComputedStyle(el);
      return style.display === 'flex' || style.display === 'grid';
    });
  });
  
  console.log('📊 LAYOUT ANALYSIS:');
  console.log(`🧭 Navigation Present: ${navElement ? '✅ Yes' : '❌ No'}`);
  console.log(`📱 Top Navigation: ${topNav ? '✅ Present' : '❌ Missing'}`);
  console.log(`📱 Bottom Navigation: ${bottomNav ? '✅ Present' : '❌ Missing'}`);
  console.log(`📱 Sidebar: ${sidebar ? '✅ Present' : '❌ Missing'}`);
  console.log(`📄 Main Content: ${mainContent ? '✅ Present' : '❌ Missing'}`);
  console.log(`🔄 Navigation Working: ${navigationWorking ? '✅ Functional' : '❌ Issues'}`);
  
  console.log('\n📐 RESPONSIVE DESIGN:');
  console.log(`↔️ Horizontal Overflow: ${contentOverflow.hasHorizontalScroll ? '⚠️ Yes' : '✅ No'}`);
  console.log(`📸 Responsive Images: ${hasResponsiveImages ? '✅ Yes' : '❌ No'}`);
  console.log(`📦 Flex/Grid Layout: ${hasFlexLayout ? '✅ Yes' : '❌ No'}`);
  console.log(`📏 Content Width: ${contentOverflow.bodyWidth}px (viewport: ${contentOverflow.viewportWidth}px)`);
  
  // Calculate responsive score
  const layoutScore = [!!navElement, !!mainContent].filter(Boolean).length / 2 * 100;
  const navigationScore = navigationWorking ? 100 : 0;
  const responsiveScore = [!contentOverflow.hasHorizontalScroll, hasResponsiveImages, hasFlexLayout].filter(Boolean).length / 3 * 100;
  const overallScore = (layoutScore + navigationScore + responsiveScore) / 3;
  
  console.log(`\n📊 ${viewport.name.toUpperCase()} SCORE: ${overallScore.toFixed(1)}%`);
  console.log(`   - Layout: ${layoutScore}%`);
  console.log(`   - Navigation: ${navigationScore}%`);
  console.log(`   - Responsive: ${responsiveScore.toFixed(1)}%`);
  
  return {
    viewport: viewport.name,
    dimensions: { width: viewport.width, height: viewport.height },
    category: viewport.category,
    layout: {
      hasNavigation: !!navElement,
      hasTopNav: !!topNav,
      hasBottomNav: !!bottomNav,
      hasSidebar: !!sidebar,
      hasMainContent: !!mainContent
    },
    functionality: {
      navigationWorking,
      profileLinkVisible: !!profileLink
    },
    responsive: {
      hasHorizontalOverflow: contentOverflow.hasHorizontalScroll,
      hasResponsiveImages,
      hasFlexLayout,
      contentWidth: contentOverflow.bodyWidth,
      viewportWidth: contentOverflow.viewportWidth
    },
    scores: {
      layout: layoutScore,
      navigation: navigationScore,
      responsive: responsiveScore,
      overall: overallScore
    },
    screenshot: screenshotName
  };
}

async function runResponsiveDesignAudit() {
  console.log('📱 RESPONSIVE DESIGN AND CROSS-PLATFORM TESTING');
  console.log('================================================');
  console.log(`🕐 Start Time: ${new Date().toISOString()}`);
  
  await ensureEvidenceDir();
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Authenticate user
    await authenticateUser(page);
    
    const results = {};
    
    // Test each viewport
    for (const viewport of VIEWPORTS) {
      results[viewport.name] = await testViewportResponsiveness(page, viewport);
    }
    
    // Calculate category scores
    const mobileResults = Object.values(results).filter(r => r.category === 'mobile');
    const tabletResults = Object.values(results).filter(r => r.category === 'tablet');
    const desktopResults = Object.values(results).filter(r => r.category === 'desktop');
    
    const mobileScore = mobileResults.reduce((sum, r) => sum + r.scores.overall, 0) / mobileResults.length;
    const tabletScore = tabletResults.reduce((sum, r) => sum + r.scores.overall, 0) / tabletResults.length;
    const desktopScore = desktopResults.reduce((sum, r) => sum + r.scores.overall, 0) / desktopResults.length;
    const overallScore = (mobileScore + tabletScore + desktopScore) / 3;
    
    // Identify critical issues
    const criticalIssues = [];
    Object.values(results).forEach(result => {
      if (result.responsive.hasHorizontalOverflow) {
        criticalIssues.push(`${result.viewport}: Horizontal overflow detected`);
      }
      if (!result.functionality.navigationWorking && result.category === 'mobile') {
        criticalIssues.push(`${result.viewport}: Mobile navigation not working`);
      }
      if (result.scores.overall < 50) {
        criticalIssues.push(`${result.viewport}: Poor responsive design score (${result.scores.overall.toFixed(1)}%)`);
      }
    });
    
    // Save comprehensive results
    const evidence = {
      timestamp: new Date().toISOString(),
      testUser: TEST_USER,
      responsiveResults: results,
      summary: {
        mobileScore: parseFloat(mobileScore.toFixed(1)),
        tabletScore: parseFloat(tabletScore.toFixed(1)),
        desktopScore: parseFloat(desktopScore.toFixed(1)),
        overallScore: parseFloat(overallScore.toFixed(1)),
        criticalIssues,
        totalViewports: VIEWPORTS.length,
        screenshots: Object.values(results).map(r => r.screenshot)
      }
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/responsive-design-audit-results.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    console.log('\n📊 RESPONSIVE DESIGN AUDIT SUMMARY');
    console.log('==================================');
    console.log(`📱 Mobile Score: ${evidence.summary.mobileScore}%`);
    console.log(`📱 Tablet Score: ${evidence.summary.tabletScore}%`);
    console.log(`🖥️ Desktop Score: ${evidence.summary.desktopScore}%`);
    console.log(`🎯 Overall Score: ${evidence.summary.overallScore}%`);
    console.log(`🚨 Critical Issues: ${evidence.summary.criticalIssues.length}`);
    evidence.summary.criticalIssues.forEach(issue => console.log(`   - ${issue}`));
    console.log(`📸 Screenshots: ${evidence.summary.screenshots.length}`);
    console.log(`📁 Evidence: ${EVIDENCE_DIR}/`);
    
    return evidence;
    
  } catch (error) {
    console.error('\n💥 Responsive design audit failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

// Run the responsive design audit
runResponsiveDesignAudit()
  .then(() => {
    console.log('\n✅ Responsive design audit completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Responsive design audit failed:', error);
    process.exit(1);
  });

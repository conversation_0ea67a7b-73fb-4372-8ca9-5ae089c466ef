-- Migration: Add Smart Group Formation System
-- This migration enhances the existing group system with activity-based intelligence
-- and smart group suggestions based on shared interests and attendance

-- ============================================================================
-- ENUMS FOR GROUP FORMATION
-- ============================================================================

-- Create group formation type enum
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'group_formation_type') THEN
        CREATE TYPE group_formation_type AS ENUM (
            'manual',           -- User-created groups
            'activity_based',   -- Auto-suggested based on activity attendance
            'music_based',      -- Auto-suggested based on music preferences
            'hybrid',           -- Combination of activity and music matching
            'spontaneous'       -- Real-time spontaneous groups
        );
    END IF;
END $$;

-- Create group suggestion status enum
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'suggestion_status') THEN
        CREATE TYPE suggestion_status AS ENUM (
            'pending',          -- Suggestion created, waiting for responses
            'accepted',         -- Enough users accepted, group formed
            'declined',         -- Not enough interest, suggestion expired
            'expired'           -- Time limit reached
        );
    END IF;
END $$;

-- ============================================================================
-- ENHANCE EXISTING GROUPS TABLE
-- ============================================================================

-- Add new columns to existing groups table
ALTER TABLE groups 
ADD COLUMN IF NOT EXISTS formation_type group_formation_type DEFAULT 'manual',
ADD COLUMN IF NOT EXISTS max_members INTEGER DEFAULT 20,
ADD COLUMN IF NOT EXISTS auto_accept_threshold INTEGER DEFAULT 3,
ADD COLUMN IF NOT EXISTS activity_focus TEXT[], -- Activities this group is focused on
ADD COLUMN IF NOT EXISTS music_focus TEXT[], -- Music genres/artists this group is focused on
ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}', -- Searchable tags
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS expires_at TIMESTAMP WITH TIME ZONE; -- For temporary/spontaneous groups

-- ============================================================================
-- GROUP SUGGESTIONS SYSTEM
-- ============================================================================

-- Group suggestions table for smart recommendations
CREATE TABLE IF NOT EXISTS group_suggestions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    suggested_name TEXT NOT NULL,
    suggested_description TEXT,
    formation_type group_formation_type NOT NULL,
    festival_id UUID REFERENCES festivals(id) ON DELETE CASCADE,
    activity_focus TEXT[], -- Activities that triggered this suggestion
    music_focus TEXT[], -- Music preferences that triggered this suggestion
    target_users UUID[] NOT NULL, -- Users who should see this suggestion
    creator_id UUID REFERENCES profiles(id) ON DELETE CASCADE, -- User who triggered the suggestion
    min_members INTEGER DEFAULT 3,
    max_members INTEGER DEFAULT 20,
    status suggestion_status DEFAULT 'pending',
    confidence_score NUMERIC(3,2) DEFAULT 0.5, -- How confident we are in this suggestion (0-1)
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Group suggestion responses (who accepted/declined)
CREATE TABLE IF NOT EXISTS group_suggestion_responses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    suggestion_id UUID NOT NULL REFERENCES group_suggestions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    response BOOLEAN NOT NULL, -- true = accept, false = decline
    response_notes TEXT, -- Optional notes from user
    responded_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    -- Ensure one response per user per suggestion
    UNIQUE(suggestion_id, user_id)
);

-- ============================================================================
-- GROUP ACTIVITY ASSOCIATIONS
-- ============================================================================

-- Link groups to specific activities they're focused on
CREATE TABLE IF NOT EXISTS group_activities (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    activity_id UUID NOT NULL REFERENCES activities(id) ON DELETE CASCADE,
    is_primary BOOLEAN DEFAULT false, -- Is this the main activity for the group?
    added_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    -- Ensure unique group-activity pairs
    UNIQUE(group_id, activity_id)
);

-- ============================================================================
-- PERFORMANCE INDEXES
-- ============================================================================

-- Group suggestions indexes
CREATE INDEX IF NOT EXISTS idx_group_suggestions_festival_id ON group_suggestions(festival_id);
CREATE INDEX IF NOT EXISTS idx_group_suggestions_status ON group_suggestions(status);
CREATE INDEX IF NOT EXISTS idx_group_suggestions_expires_at ON group_suggestions(expires_at);
CREATE INDEX IF NOT EXISTS idx_group_suggestions_target_users ON group_suggestions USING GIN(target_users);
CREATE INDEX IF NOT EXISTS idx_group_suggestions_activity_focus ON group_suggestions USING GIN(activity_focus);
CREATE INDEX IF NOT EXISTS idx_group_suggestions_music_focus ON group_suggestions USING GIN(music_focus);

-- Group suggestion responses indexes
CREATE INDEX IF NOT EXISTS idx_group_suggestion_responses_suggestion_id ON group_suggestion_responses(suggestion_id);
CREATE INDEX IF NOT EXISTS idx_group_suggestion_responses_user_id ON group_suggestion_responses(user_id);

-- Group activities indexes
CREATE INDEX IF NOT EXISTS idx_group_activities_group_id ON group_activities(group_id);
CREATE INDEX IF NOT EXISTS idx_group_activities_activity_id ON group_activities(activity_id);
CREATE INDEX IF NOT EXISTS idx_group_activities_is_primary ON group_activities(is_primary);

-- Enhanced groups indexes
CREATE INDEX IF NOT EXISTS idx_groups_formation_type ON groups(formation_type);
CREATE INDEX IF NOT EXISTS idx_groups_activity_focus ON groups USING GIN(activity_focus);
CREATE INDEX IF NOT EXISTS idx_groups_music_focus ON groups USING GIN(music_focus);
CREATE INDEX IF NOT EXISTS idx_groups_tags ON groups USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_groups_is_active ON groups(is_active);
CREATE INDEX IF NOT EXISTS idx_groups_expires_at ON groups(expires_at);

-- ============================================================================
-- ROW LEVEL SECURITY POLICIES
-- ============================================================================

-- Enable RLS
ALTER TABLE group_suggestions ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_suggestion_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_activities ENABLE ROW LEVEL SECURITY;

-- Group suggestions policies
CREATE POLICY "Users can view suggestions targeted at them" 
    ON group_suggestions FOR SELECT 
    USING (auth.uid() = ANY(target_users) OR auth.uid() = creator_id);

CREATE POLICY "Users can create suggestions" 
    ON group_suggestions FOR INSERT 
    WITH CHECK (auth.uid() = creator_id);

CREATE POLICY "Creators can update their suggestions" 
    ON group_suggestions FOR UPDATE 
    USING (auth.uid() = creator_id);

-- Group suggestion responses policies
CREATE POLICY "Users can view responses to suggestions they can see" 
    ON group_suggestion_responses FOR SELECT 
    USING (
        suggestion_id IN (
            SELECT id FROM group_suggestions 
            WHERE auth.uid() = ANY(target_users) OR auth.uid() = creator_id
        )
    );

CREATE POLICY "Users can respond to suggestions targeted at them" 
    ON group_suggestion_responses FOR INSERT 
    WITH CHECK (
        auth.uid() = user_id AND 
        suggestion_id IN (
            SELECT id FROM group_suggestions 
            WHERE auth.uid() = ANY(target_users)
        )
    );

-- Group activities policies
CREATE POLICY "Users can view group activities for groups they can see" 
    ON group_activities FOR SELECT 
    USING (
        group_id IN (
            SELECT id FROM groups 
            WHERE NOT is_private OR 
            id IN (SELECT group_id FROM group_members WHERE user_id = auth.uid())
        )
    );

CREATE POLICY "Group members can manage group activities" 
    ON group_activities FOR ALL 
    USING (
        group_id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.uid() AND role IN ('admin', 'moderator')
        )
    );

-- ============================================================================
-- SMART GROUP FORMATION FUNCTIONS
-- ============================================================================

-- Function to find users with similar activity attendance
CREATE OR REPLACE FUNCTION find_activity_based_group_candidates(
    target_user_id UUID,
    festival_id_param UUID,
    min_shared_activities INTEGER DEFAULT 2,
    result_limit INTEGER DEFAULT 10
)
RETURNS TABLE(
    user_id UUID,
    shared_activities TEXT[],
    shared_activity_count INTEGER,
    compatibility_score NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    WITH user_activities AS (
        SELECT ARRAY_AGG(DISTINCT a.title) as activity_names
        FROM activity_attendance aa
        JOIN activities a ON aa.activity_id = a.id
        WHERE aa.user_id = target_user_id 
        AND aa.status IN ('going', 'interested')
        AND a.festival_id = festival_id_param
    ),
    candidate_users AS (
        SELECT 
            aa.user_id,
            ARRAY_AGG(DISTINCT a.title) as user_activities,
            COUNT(DISTINCT a.id) as activity_count
        FROM activity_attendance aa
        JOIN activities a ON aa.activity_id = a.id
        WHERE aa.user_id != target_user_id
        AND aa.status IN ('going', 'interested')
        AND a.festival_id = festival_id_param
        GROUP BY aa.user_id
        HAVING COUNT(DISTINCT a.id) >= min_shared_activities
    )
    SELECT 
        cu.user_id,
        (SELECT ARRAY(
            SELECT UNNEST(cu.user_activities) 
            INTERSECT 
            SELECT UNNEST(ua.activity_names)
        )) as shared_activities,
        (SELECT ARRAY_LENGTH(ARRAY(
            SELECT UNNEST(cu.user_activities) 
            INTERSECT 
            SELECT UNNEST(ua.activity_names)
        ), 1)) as shared_activity_count,
        (SELECT ARRAY_LENGTH(ARRAY(
            SELECT UNNEST(cu.user_activities) 
            INTERSECT 
            SELECT UNNEST(ua.activity_names)
        ), 1))::NUMERIC / GREATEST(cu.activity_count, 1) as compatibility_score
    FROM candidate_users cu, user_activities ua
    WHERE (SELECT ARRAY_LENGTH(ARRAY(
        SELECT UNNEST(cu.user_activities) 
        INTERSECT 
        SELECT UNNEST(ua.activity_names)
    ), 1)) >= min_shared_activities
    ORDER BY compatibility_score DESC, shared_activity_count DESC
    LIMIT result_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to auto-form groups when suggestion threshold is met
CREATE OR REPLACE FUNCTION check_and_form_group_from_suggestion(suggestion_id_param UUID)
RETURNS UUID AS $$
DECLARE
    suggestion_record RECORD;
    acceptance_count INTEGER;
    new_group_id UUID;
    accepted_users UUID[];
BEGIN
    -- Get suggestion details
    SELECT * INTO suggestion_record 
    FROM group_suggestions 
    WHERE id = suggestion_id_param AND status = 'pending';
    
    IF NOT FOUND THEN
        RETURN NULL;
    END IF;
    
    -- Count acceptances
    SELECT COUNT(*), ARRAY_AGG(user_id) 
    INTO acceptance_count, accepted_users
    FROM group_suggestion_responses 
    WHERE suggestion_id = suggestion_id_param AND response = true;
    
    -- Check if we have enough acceptances
    IF acceptance_count >= suggestion_record.min_members THEN
        -- Create the group
        INSERT INTO groups (
            name, 
            description, 
            festival_id, 
            creator_id, 
            formation_type,
            max_members,
            activity_focus,
            music_focus,
            is_active
        ) VALUES (
            suggestion_record.suggested_name,
            suggestion_record.suggested_description,
            suggestion_record.festival_id,
            suggestion_record.creator_id,
            suggestion_record.formation_type,
            suggestion_record.max_members,
            suggestion_record.activity_focus,
            suggestion_record.music_focus,
            true
        ) RETURNING id INTO new_group_id;
        
        -- Add accepted users as members
        INSERT INTO group_members (group_id, user_id, role, joined_at)
        SELECT 
            new_group_id, 
            UNNEST(accepted_users), 
            CASE WHEN UNNEST(accepted_users) = suggestion_record.creator_id THEN 'admin' ELSE 'member' END,
            NOW();
        
        -- Update suggestion status
        UPDATE group_suggestions 
        SET status = 'accepted', updated_at = NOW() 
        WHERE id = suggestion_id_param;
        
        RETURN new_group_id;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

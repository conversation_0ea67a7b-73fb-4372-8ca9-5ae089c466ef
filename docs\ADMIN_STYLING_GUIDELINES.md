# Festival Family Admin Styling Guidelines

**Version:** 1.0  
**Created:** 2025-06-26  
**Purpose:** Comprehensive styling standards for admin components  
**Status:** Production Ready

---

## 📋 Table of Contents

1. [Overview](#overview)
2. [Design Principles](#design-principles)
3. [Button Patterns](#button-patterns)
4. [Status Color System](#status-color-system)
5. [Text Hierarchy](#text-hierarchy)
6. [Form Styling](#form-styling)
7. [Layout Patterns](#layout-patterns)
8. [Accessibility Standards](#accessibility-standards)
9. [Implementation Examples](#implementation-examples)
10. [Migration Guide](#migration-guide)

---

## 🎯 Overview

This document provides comprehensive styling standards for Festival Family's admin interface, ensuring consistency, accessibility, and maintainability across all admin sections. These guidelines follow modern design system principles and are based on systematic analysis of existing admin components.

### Key Benefits
- **Consistency**: Unified visual language across all admin sections
- **Accessibility**: WCAG 2.1 compliant with proper contrast ratios
- **Maintainability**: Single source of truth for all admin styling
- **Performance**: Optimized CSS using design tokens and variables
- **Developer Experience**: Clear patterns and reusable utilities

---

## 🏗️ Design Principles

### 1. Single Source of Truth
All admin styling uses centralized utilities from `src/lib/utils/admin-styles.ts` to ensure consistency and eliminate hardcoded values.

### 2. Theme Awareness
All components support both light and dark modes through CSS variables, ensuring proper contrast and readability.

### 3. Accessibility First
All styling follows WCAG 2.1 guidelines with minimum 4.5:1 contrast ratios for normal text and 3:1 for large text.

### 4. Progressive Enhancement
Components work without JavaScript and enhance with interactive features when available.

---

## 🔘 Button Patterns

### Standard Admin Action Buttons

#### Edit Actions
```tsx
// Standard edit button with icon and text
<Button
  variant="ghost"
  size="sm"
  onClick={() => onEdit(item)}
  className="h-8 px-3"
>
  <Pencil className="h-4 w-4 mr-2" />
  Edit
</Button>

// Icon-only edit button (for space-constrained layouts)
<Button
  variant="ghost"
  size="sm"
  onClick={() => onEdit(item)}
  className="h-8 px-3"
>
  <Pencil className="h-4 w-4" />
  <span className="sr-only">Edit {itemType}</span>
</Button>
```

#### Delete Actions
```tsx
<Button
  variant="ghost"
  size="sm"
  onClick={() => onDelete(item)}
  className="h-8 px-3 text-destructive hover:text-destructive hover:bg-destructive/10"
>
  <Trash2 className="h-4 w-4 mr-2" />
  Delete
</Button>
```

#### Status Toggle Actions
```tsx
// Active status
<Button
  variant="ghost"
  size="sm"
  onClick={() => toggleStatus(item)}
  className="h-8 px-3 text-festival-success hover:bg-festival-success/10"
>
  Active
</Button>

// Inactive status
<Button
  variant="ghost"
  size="sm"
  onClick={() => toggleStatus(item)}
  className="h-8 px-3 text-destructive hover:bg-destructive/10"
>
  Inactive
</Button>
```

### Button Group Layout
```tsx
<div className="flex space-x-2">
  {/* Edit button */}
  {/* Delete button */}
  {/* Additional actions */}
</div>
```

---

## 🎨 Status Color System

### Centralized Status Colors

Import and use the centralized status color system:

```tsx
import { getStatusColor } from '@/lib/utils/admin-styles';

// Usage in Badge components
<Badge
  variant="outline"
  className={getStatusColor(item.status, 'DRAFT')}
>
  {item.status}
</Badge>
```

### Available Status Colors

#### Content Status
- **DRAFT**: `bg-muted/20 text-muted-foreground border-muted/30`
- **PUBLISHED**: `bg-festival-success/20 text-festival-success border-festival-success/30`
- **ARCHIVED**: `bg-muted/40 text-muted-foreground border-muted/50`

#### Activity Status
- **ACTIVE**: `bg-festival-success/20 text-festival-success border-festival-success/30`
- **INACTIVE**: `bg-destructive/20 text-destructive border-destructive/30`

#### Priority Levels
- **HIGH**: `bg-destructive/20 text-destructive border-destructive/30`
- **MEDIUM**: `bg-festival-warning/20 text-festival-warning border-festival-warning/30`
- **LOW**: `bg-muted/20 text-muted-foreground border-muted/30`

#### User Roles
- **SUPER_ADMIN**: `bg-destructive/20 text-destructive border-destructive/30`
- **CONTENT_ADMIN**: `bg-festival-warning/20 text-festival-warning border-festival-warning/30`
- **MODERATOR**: `bg-primary/20 text-primary border-primary/30`
- **USER**: `bg-muted/20 text-muted-foreground border-muted/30`

---

## 📝 Text Hierarchy

### Centralized Text Styles

Import and use standardized text styles:

```tsx
import { adminTextStyles } from '@/lib/utils/admin-styles';

// Usage examples
<h1 className={adminTextStyles.pageTitle}>Admin Section</h1>
<h2 className={adminTextStyles.sectionHeading}>Section Title</h2>
<label className={adminTextStyles.formLabel}>Field Label</label>
```

### Text Hierarchy Standards

#### Page Structure
- **Page Title**: `text-2xl font-bold text-foreground`
- **Section Heading**: `text-lg font-semibold text-foreground`
- **Subsection**: `text-base font-medium text-foreground`

#### Form Elements
- **Form Label**: `text-foreground font-medium`
- **Form Description**: `text-muted-foreground text-sm`
- **Form Error**: `text-destructive text-sm`
- **Input Text**: `text-foreground` (handled by component)

#### Table Content
- **Table Header**: `text-foreground font-medium`
- **Primary Content**: `text-foreground`
- **Secondary Content**: `text-muted-foreground`

#### Card Content
- **Card Title**: `text-foreground font-semibold`
- **Card Description**: `text-muted-foreground`

---

## 📋 Form Styling

### Form Layout Standards

```tsx
// Standard form container
<form className="space-y-6">
  {/* Form fields with consistent spacing */}
</form>

// Form field groups
<div className="space-y-4">
  {/* Related form fields */}
</div>
```

### Form Field Pattern

```tsx
<FormField
  control={form.control}
  name="fieldName"
  render={({ field }) => (
    <FormItem>
      <FormLabel className={adminTextStyles.formLabel}>
        Field Label
      </FormLabel>
      <FormControl>
        <Input 
          placeholder="Enter value" 
          {...field} 
          className="text-foreground bg-background border-border"
        />
      </FormControl>
      <FormDescription className={adminTextStyles.formDescription}>
        Helper text for the field
      </FormDescription>
      <FormMessage className={adminTextStyles.formError} />
    </FormItem>
  )}
/>
```

### Form Actions

```tsx
// Standard form action layout
<div className="flex justify-between pt-4">
  <Button type="button" variant="outline">
    Cancel
  </Button>
  <div className="flex space-x-2">
    <Button type="button" variant="outline">
      Save as Draft
    </Button>
    <Button type="submit">
      Publish
    </Button>
  </div>
</div>
```

---

## 📐 Layout Patterns

### Page Container

```tsx
// Standard admin page layout
<div className="container mx-auto py-6">
  <div className="space-y-6">
    {/* Page header */}
    <div className="flex justify-between items-center">
      <h1 className={adminTextStyles.pageTitle}>Page Title</h1>
      <Button>Primary Action</Button>
    </div>
    
    {/* Page content */}
    <div className="space-y-4">
      {/* Content sections */}
    </div>
  </div>
</div>
```

### Card Grid Layout

```tsx
// Responsive card grid
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
  {items.map(item => (
    <Card key={item.id}>
      {/* Card content */}
    </Card>
  ))}
</div>
```

### Table Container

```tsx
// Responsive table wrapper
<div className="overflow-x-auto">
  <table className="min-w-full">
    {/* Table content */}
  </table>
</div>
```

---

## ♿ Accessibility Standards

### Contrast Requirements
- **Normal Text**: Minimum 4.5:1 contrast ratio
- **Large Text**: Minimum 3:1 contrast ratio
- **Interactive Elements**: Minimum 3:1 contrast ratio
- **Focus Indicators**: Minimum 3:1 contrast ratio

### Screen Reader Support

```tsx
// Icon-only buttons require screen reader text
<Button>
  <Icon className="h-4 w-4" />
  <span className="sr-only">Action description</span>
</Button>

// Form fields require proper labels
<FormLabel htmlFor="fieldId">Field Label</FormLabel>
<Input id="fieldId" />
```

### Focus Management

```tsx
// Ensure proper focus indicators
<Button className="focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2">
  Action
</Button>
```

---

## 💡 Implementation Examples

### Complete Admin Section Example

```tsx
import { adminTextStyles, getStatusColor } from '@/lib/utils/admin-styles';

export default function AdminSection() {
  return (
    <div className="container mx-auto py-6">
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <h1 className={adminTextStyles.pageTitle}>Section Name</h1>
          <Button>Create New</Button>
        </div>

        {/* Content Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr>
                <th className={`px-6 py-3 text-left ${adminTextStyles.tableHeader}`}>
                  Name
                </th>
                <th className={`px-6 py-3 text-left ${adminTextStyles.tableHeader}`}>
                  Status
                </th>
                <th className={`px-6 py-3 text-left ${adminTextStyles.tableHeader}`}>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {items.map(item => (
                <tr key={item.id}>
                  <td className={`px-6 py-4 ${adminTextStyles.tableContent}`}>
                    {item.name}
                  </td>
                  <td className="px-6 py-4">
                    <Badge
                      variant="outline"
                      className={getStatusColor(item.status)}
                    >
                      {item.status}
                    </Badge>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 px-3"
                      >
                        <Pencil className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 px-3 text-destructive hover:text-destructive hover:bg-destructive/10"
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete item</span>
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
```

---

## 🔄 Migration Guide

### Step 1: Import Utilities
```tsx
import { 
  adminTextStyles, 
  getStatusColor, 
  adminLayoutStyles 
} from '@/lib/utils/admin-styles';
```

### Step 2: Replace Hardcoded Colors
```tsx
// ❌ Before
<div className="text-gray-900">Content</div>
<Badge className="bg-green-100 text-green-800">Published</Badge>

// ✅ After
<div className={adminTextStyles.tableContent}>Content</div>
<Badge className={getStatusColor('PUBLISHED')}>Published</Badge>
```

### Step 3: Standardize Button Patterns
```tsx
// ❌ Before
<button className="px-3 py-1 bg-blue-500/20 hover:bg-blue-500/30">
  Edit
</button>

// ✅ After
<Button variant="ghost" size="sm" className="h-8 px-3">
  <Pencil className="h-4 w-4 mr-2" />
  Edit
</Button>
```

### Step 4: Update Text Hierarchy
```tsx
// ❌ Before
<h1 className="text-2xl font-bold text-purple-900">Title</h1>

// ✅ After
<h1 className={adminTextStyles.pageTitle}>Title</h1>
```

---

## 📚 Additional Resources

- **Design Tokens**: `src/styles/design-tokens.css`
- **Admin Utilities**: `src/lib/utils/admin-styles.ts`
- **Component Library**: shadcn/ui components
- **Accessibility Guide**: WCAG 2.1 Guidelines
- **Testing**: Playwright accessibility tests

---

**Last Updated:** 2025-06-26  
**Next Review:** 2025-07-26  
**Maintainer:** Festival Family Development Team

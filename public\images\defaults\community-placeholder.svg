<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="url(#gradient)"/>
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="180" cy="110" r="20" fill="white" fill-opacity="0.3"/>
  <circle cx="200" cy="120" r="25" fill="white" fill-opacity="0.4"/>
  <circle cx="220" cy="110" r="20" fill="white" fill-opacity="0.3"/>
  <path d="M160 140 Q200 120 240 140 L240 160 Q200 140 160 160 Z" fill="white" fill-opacity="0.2"/>
  <text x="200" y="180" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="500">
    Community
  </text>
  <text x="200" y="200" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" opacity="0.8">
    Image Loading...
  </text>
</svg>

/**
 * Gesture Navigation Hook
 * 
 * Provides swipe-based navigation between pages using existing mobile UX patterns.
 * Leverages design tokens and existing touch handlers for consistency.
 * 
 * @hook
 */

import { useCallback, useRef, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { simulateHapticFeedback, isMobileViewport } from '@/utils/mobileUX';
import { useReducedMotion } from './useReducedMotion';

interface GestureNavigationConfig {
  /** Minimum swipe distance to trigger navigation (px) */
  threshold?: number;
  /** Maximum vertical deviation allowed for horizontal swipe (px) */
  verticalTolerance?: number;
  /** Enable haptic feedback on navigation */
  enableHaptic?: boolean;
  /** Custom navigation routes mapping */
  routes?: {
    left?: string;
    right?: string;
  };
}

interface SwipeState {
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
  isActive: boolean;
  direction: 'left' | 'right' | null;
}

/**
 * Hook for gesture-based navigation between pages
 */
export const useGestureNavigation = (config: GestureNavigationConfig = {}) => {
  const {
    threshold = 100,
    verticalTolerance = 50,
    enableHaptic = true,
    routes
  } = config;

  const navigate = useNavigate();
  const location = useLocation();
  const { shouldAnimate } = useReducedMotion();
  
  const swipeState = useRef<SwipeState>({
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
    isActive: false,
    direction: null
  });

  const [gestureProgress, setGestureProgress] = useState(0);
  const [gestureDirection, setGestureDirection] = useState<'left' | 'right' | null>(null);

  // Default navigation routes based on current page
  const getDefaultRoutes = useCallback(() => {
    const currentPath = location.pathname;
    const routeMap: Record<string, { left?: string; right?: string }> = {
      '/': { right: '/activities' },
      '/activities': { left: '/', right: '/famhub' },
      '/famhub': { left: '/activities', right: '/discover' },
      '/discover': { left: '/famhub' }
    };
    return routeMap[currentPath] || {};
  }, [location.pathname]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!isMobileViewport() || !shouldAnimate) return;

    const touch = e.touches[0];
    swipeState.current = {
      startX: touch.clientX,
      startY: touch.clientY,
      currentX: touch.clientX,
      currentY: touch.clientY,
      isActive: true,
      direction: null
    };
  }, [shouldAnimate]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!swipeState.current.isActive || !shouldAnimate) return;

    const touch = e.touches[0];
    const deltaX = touch.clientX - swipeState.current.startX;
    const deltaY = Math.abs(touch.clientY - swipeState.current.startY);

    // Check if vertical movement is within tolerance
    if (deltaY > verticalTolerance) {
      swipeState.current.isActive = false;
      setGestureProgress(0);
      setGestureDirection(null);
      return;
    }

    swipeState.current.currentX = touch.clientX;
    swipeState.current.currentY = touch.clientY;

    // Determine direction and calculate progress
    const direction = deltaX > 0 ? 'right' : 'left';
    const progress = Math.min(Math.abs(deltaX) / threshold, 1);

    swipeState.current.direction = direction;
    setGestureDirection(direction);
    setGestureProgress(progress);

    // Prevent default scrolling if we're in a valid swipe
    if (Math.abs(deltaX) > 20) {
      e.preventDefault();
    }
  }, [threshold, verticalTolerance, shouldAnimate]);

  const handleTouchEnd = useCallback(() => {
    if (!swipeState.current.isActive || !shouldAnimate) return;

    const deltaX = swipeState.current.currentX - swipeState.current.startX;
    const direction = swipeState.current.direction;

    // Reset state
    swipeState.current.isActive = false;
    setGestureProgress(0);
    setGestureDirection(null);

    // Check if swipe meets threshold
    if (Math.abs(deltaX) >= threshold && direction) {
      const navigationRoutes = routes || getDefaultRoutes();
      const targetRoute = navigationRoutes[direction];

      if (targetRoute) {
        if (enableHaptic) {
          simulateHapticFeedback('medium');
        }
        navigate(targetRoute);
      }
    }
  }, [threshold, routes, getDefaultRoutes, navigate, enableHaptic, shouldAnimate]);

  const gestureHandlers = {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
    className: 'mobile-gesture-container'
  };

  return {
    gestureHandlers,
    gestureProgress,
    gestureDirection,
    isGestureActive: swipeState.current.isActive
  };
};

export default useGestureNavigation;

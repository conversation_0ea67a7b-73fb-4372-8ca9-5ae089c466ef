<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="url(#gradient)"/>
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fa709a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fee140;stop-opacity:1" />
    </linearGradient>
  </defs>
  <path d="M150 80 L200 60 L250 80 L250 140 L200 160 L150 140 Z" fill="white" fill-opacity="0.2"/>
  <path d="M170 90 L200 80 L230 90 L230 130 L200 140 L170 130 Z" fill="white" fill-opacity="0.3"/>
  <circle cx="200" cy="110" r="15" fill="white" fill-opacity="0.5"/>
  <path d="M185 100 L215 100 M185 110 L215 110 M185 120 L215 120" stroke="white" stroke-width="2" stroke-opacity="0.4"/>
  <text x="200" y="180" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="500">
    Festival
  </text>
  <text x="200" y="200" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" opacity="0.8">
    Image Loading...
  </text>
</svg>

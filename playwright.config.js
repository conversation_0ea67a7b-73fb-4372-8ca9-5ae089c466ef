/**
 * Playwright Configuration for Evidence-Based Testing
 *
 * This configuration sets up comprehensive browser testing with
 * evidence collection, screenshots, and detailed reporting.
 *
 * Environment Variables:
 * - CI: Enables headless mode and CI optimizations
 * - HEADLESS: Controls headless mode (default: true in CI, false in dev)
 * - HEADED: Forces headed mode for debugging (overrides HEADLESS)
 */

import { defineConfig, devices } from '@playwright/test';

// Environment-based configuration
const isCI = !!process.env.CI;
const isHeaded = process.env.HEADED === 'true';
const isHeadless = isCI || (process.env.HEADLESS !== 'false' && !isHeaded);

export default defineConfig({
  // Test directory
  testDir: './tests',
  
  // Global test timeout
  timeout: 60000,
  
  // Expect timeout for assertions
  expect: {
    timeout: 10000,
  },
  
  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,
  
  // Retry on CI only
  retries: process.env.CI ? 2 : 0,
  
  // Opt out of parallel tests on CI
  workers: process.env.CI ? 1 : undefined,
  
  // Reporter configuration
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['list'],
    ['junit', { outputFile: 'test-results/junit.xml' }]
  ],
  
  // Global test configuration
  use: {
    // Base URL for all tests
    baseURL: 'http://localhost:5173',

    // Browser context options
    viewport: { width: 1280, height: 720 },

    // Headless mode configuration
    headless: isHeadless,

    // Enhanced evidence collection for production readiness
    trace: isCI ? 'on' : 'on-first-retry',
    video: isCI ? 'on' : 'retain-on-failure',
    screenshot: isCI ? 'on' : 'only-on-failure',

    // Ignore HTTPS errors
    ignoreHTTPSErrors: true,

    // Optimized timeouts for headless mode
    actionTimeout: isHeadless ? 10000 : 15000,
    navigationTimeout: isHeadless ? 20000 : 30000,
  },

  // Configure projects for major browsers
  projects: [
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        headless: isHeadless,
        launchOptions: {
          headless: isHeadless,
          args: [
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--no-sandbox',
            // Additional headless optimizations
            ...(isHeadless ? [
              '--disable-setuid-sandbox',
              '--disable-dev-shm-usage',
              '--disable-gpu',
              '--no-first-run',
              '--no-default-browser-check',
              '--disable-background-timer-throttling',
              '--disable-backgrounding-occluded-windows',
              '--disable-renderer-backgrounding'
            ] : [])
          ]
        }
      },
    },

    {
      name: 'firefox',
      use: {
        ...devices['Desktop Firefox'],
        headless: isHeadless,
        launchOptions: {
          headless: isHeadless,
          firefoxUserPrefs: {
            'security.tls.insecure_fallback_hosts': 'localhost',
            // Headless optimizations for Firefox
            ...(isHeadless ? {
              'browser.cache.disk.enable': false,
              'browser.cache.memory.enable': false,
            } : {})
          }
        }
      },
    },

    {
      name: 'webkit',
      use: {
        ...devices['Desktop Safari'],
        headless: isHeadless,
        // Safari-specific settings
      },
    },

    // Mobile testing
    {
      name: 'Mobile Chrome',
      use: {
        ...devices['Pixel 5'],
        headless: isHeadless,
      },
    },
    {
      name: 'Mobile Safari',
      use: {
        ...devices['iPhone 12'],
        headless: isHeadless,
      },
    },

    // Tablet testing
    {
      name: 'Tablet',
      use: {
        ...devices['iPad Pro'],
        headless: isHeadless,
      },
    },
  ],

  // Web server configuration
  webServer: {
    command: 'npm run dev',
    port: 5173,
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
    env: {
      NODE_ENV: 'test'
    }
  },
  
  // Output directories
  outputDir: 'test-results/artifacts',
  
  // Global setup and teardown (commented out for ES module compatibility)
  // globalSetup: './tests/global-setup.js',
  // globalTeardown: './tests/global-teardown.js',
});

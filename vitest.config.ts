/**
 * Vitest Configuration for Festival Family
 * 
 * Test configuration for Redis optimization features and production readiness validation.
 * Includes path mapping, environment setup, and coverage reporting.
 * 
 * @module VitestConfig
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { defineConfig } from 'vitest/config'
import path from 'path'

export default defineConfig({
  test: {
    // Test environment configuration
    environment: 'jsdom',
    
    // Global test setup
    globals: true,
    
    // Test file patterns
    include: [
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'src/**/__tests__/**/*.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
    ],
    
    // Exclude patterns
    exclude: [
      'node_modules',
      'dist',
      '.next',
      'coverage',
      'src/**/*.d.ts'
    ],
    
    // Test timeout
    testTimeout: 10000,
    
    // Coverage configuration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      exclude: [
        'node_modules/',
        'src/**/*.d.ts',
        'src/**/*.test.{js,ts,jsx,tsx}',
        'src/**/*.spec.{js,ts,jsx,tsx}',
        'src/**/__tests__/**',
        'coverage/**',
        'dist/**',
        '.next/**',
        '*.config.{js,ts}',
        'src/types/**',
        'src/**/*.stories.{js,ts,jsx,tsx}',
        'src/main.tsx',
        'src/vite-env.d.ts',
        'src/test/**',
        'src/mocks/**'
      ],
      include: [
        'src/components/design-system/**/*.{js,ts,tsx}',
        'src/lib/data/**/*.{js,ts}',
        'src/lib/services/**/*.{js,ts}',
        'src/hooks/**/*.{js,ts}',
        'src/components/activities/**/*.{js,ts,tsx}',
        'src/lib/monitoring/**/*.{js,ts}'
      ],
      thresholds: {
        global: {
          branches: 70,
          functions: 70,
          lines: 70,
          statements: 70
        },
        'src/components/design-system/**/*.{ts,tsx}': {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        },
        'src/lib/services/**/*.{ts}': {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    
    // Setup files
    setupFiles: ['./src/test/vitest-setup.ts'],

    // Mock configuration
    deps: {
      inline: ['@upstash/redis', '@supabase/supabase-js', '@tanstack/react-query']
    },

    // Server configuration for better performance
    server: {
      deps: {
        inline: ['@testing-library/react', '@testing-library/jest-dom']
      }
    }
  },
  
  // Path resolution for imports
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/lib': path.resolve(__dirname, './src/lib'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/providers': path.resolve(__dirname, './src/providers'),
      '@/pages': path.resolve(__dirname, './src/pages'),
      '@/styles': path.resolve(__dirname, './src/styles')
    }
  },
  
  // Define global variables for tests
  define: {
    'process.env.NODE_ENV': '"test"',
    'process.env.VITE_SUPABASE_URL': '"https://test.supabase.co"',
    'process.env.VITE_SUPABASE_ANON_KEY': '"test-key"'
  }
})

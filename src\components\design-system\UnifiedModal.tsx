/**
 * Unified Modal System
 * 
 * Consistent modal implementation that replaces scattered modal variants.
 * Provides standardized behavior, styling, and interaction patterns.
 * 
 * Features:
 * - Consistent header/footer patterns
 * - Enhanced color mapping integration
 * - Responsive design (mobile/desktop)
 * - Standardized action button placement
 * - Accessible keyboard navigation
 * - Smooth animations and transitions
 * 
 * @module UnifiedModal
 * @version 1.0.0
 */

import React, { useEffect, useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { X, Calendar, MapPin, Users, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';
import { UnifiedInteractionButton } from './UnifiedInteractionButton';
import type { InteractionType } from '@/lib/services/unifiedInteractionService';

// ============================================================================
// MODAL PROPS AND TYPES
// ============================================================================

export interface ModalAction {
  type: InteractionType;
  label?: string;
  variant?: 'default' | 'outline' | 'ghost';
  onClick?: () => void | Promise<void>;
}

export interface UnifiedModalProps {
  /** Modal open state */
  open: boolean;
  
  /** Close handler */
  onClose: () => void;
  
  /** Modal title */
  title: string;
  
  /** Modal content */
  children: React.ReactNode;
  
  /** Item ID for color theming and interactions */
  itemId: string;
  
  /** Content type for color theming */
  contentType?: string;
  
  /** Category for color theming */
  category?: string;
  
  /** Item type for interactions */
  itemType?: string;
  
  /** Modal size */
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  
  /** Header image URL */
  headerImage?: string;
  
  /** Featured badge text */
  featuredBadge?: string;
  
  /** Additional badges */
  badges?: Array<{
    text: string;
    variant?: string;
    contentType?: string;
    category?: string;
  }>;
  
  /** Metadata items (date, location, etc.) */
  metadata?: Array<{
    icon: React.ComponentType<any>;
    label: string;
    value: string;
  }>;
  
  /** Action buttons */
  actions?: ModalAction[];
  
  /** Show default close button */
  showCloseButton?: boolean;
  
  /** Custom footer content */
  footerContent?: React.ReactNode;
  
  /** Additional CSS classes */
  className?: string;
}

// ============================================================================
// UNIFIED MODAL COMPONENT
// ============================================================================

export const UnifiedModal: React.FC<UnifiedModalProps> = ({
  open,
  onClose,
  title,
  children,
  itemId,
  contentType = 'activities',
  category = 'main',
  itemType = 'activity',
  size = 'lg',
  headerImage,
  featuredBadge,
  badges = [],
  metadata = [],
  actions = [],
  showCloseButton = true,
  footerContent,
  className
}) => {
  const [colorTheme, setColorTheme] = useState<{
    className: string;
    style: React.CSSProperties;
  }>({ className: '', style: {} });
  const [isLoadingTheme, setIsLoadingTheme] = useState(true);

  // ========================================================================
  // COLOR THEME LOADING
  // ========================================================================

  useEffect(() => {
    const loadColorTheme = async () => {
      setIsLoadingTheme(true);
      try {
        const theme = await enhancedColorMappingService.getEnhancedColorTheme(
          contentType, 
          category, 
          0 // Use first fallback theme
        );
        setColorTheme(theme);
      } catch (error) {
        console.warn('Failed to load modal color theme:', error);
        setColorTheme({
          className: 'bg-gradient-to-br from-blue-50 to-purple-50',
          style: {}
        });
      } finally {
        setIsLoadingTheme(false);
      }
    };

    if (open) {
      loadColorTheme();
    }
  }, [open, contentType, category]);

  // ========================================================================
  // SIZE MAPPING
  // ========================================================================

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'max-w-md';
      case 'md':
        return 'max-w-lg';
      case 'lg':
        return 'max-w-2xl';
      case 'xl':
        return 'max-w-4xl';
      case 'full':
        return 'max-w-7xl';
      default:
        return 'max-w-2xl';
    }
  };

  // ========================================================================
  // RENDER HELPERS
  // ========================================================================

  const renderHeader = () => (
    <DialogHeader className="relative">
      {/* Header Image */}
      {headerImage && (
        <div className="relative h-48 -mx-6 -mt-6 mb-4 overflow-hidden rounded-t-lg">
          <img
            src={headerImage}
            alt={title}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
          
          {/* Featured Badge on Image */}
          {featuredBadge && (
            <div className="absolute top-4 left-4">
              <Badge 
                variant="secondary" 
                className="bg-yellow-500/90 text-yellow-900 font-semibold backdrop-blur-sm"
              >
                {featuredBadge}
              </Badge>
            </div>
          )}
          
          {/* Close Button on Image */}
          {showCloseButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="absolute top-4 right-4 h-8 w-8 p-0 bg-black/20 hover:bg-black/40 text-white backdrop-blur-sm"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      )}

      {/* Title and Close Button (when no header image) */}
      <div className="flex items-start justify-between">
        <DialogTitle className="text-xl font-bold text-foreground pr-8">
          {title}
        </DialogTitle>
        
        {showCloseButton && !headerImage && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Featured Badge (when no header image) */}
      {featuredBadge && !headerImage && (
        <Badge variant="secondary" className="w-fit bg-yellow-100 text-yellow-800">
          {featuredBadge}
        </Badge>
      )}

      {/* Badges */}
      {badges.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {badges.map((badge, index) => (
            <Badge
              key={index}
              variant={badge.variant as any || 'outline'}
              className="text-xs"
            >
              {badge.text}
            </Badge>
          ))}
        </div>
      )}

      {/* Metadata */}
      {metadata.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-4 text-sm text-muted-foreground">
          {metadata.map((item, index) => (
            <div key={index} className="flex items-center gap-2">
              <item.icon className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">{item.label}:</span>
              <span>{item.value}</span>
            </div>
          ))}
        </div>
      )}
    </DialogHeader>
  );

  const renderFooter = () => {
    if (!actions.length && !footerContent) return null;

    return (
      <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-3">
        {/* Custom footer content */}
        {footerContent}
        
        {/* Action buttons */}
        {actions.map((action, index) => (
          <UnifiedInteractionButton
            key={index}
            type={action.type}
            itemId={itemId}
            itemType={itemType}
            variant={action.variant || 'default'}
            label={action.label}
            onClick={action.onClick}
            className="flex-1 sm:flex-none"
          />
        ))}
      </DialogFooter>
    );
  };

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent 
        className={cn(
          getSizeClasses(),
          "max-h-[90vh] overflow-y-auto",
          !isLoadingTheme && colorTheme.className,
          className
        )}
        style={!isLoadingTheme ? colorTheme.style : undefined}
      >
        {renderHeader()}
        
        {/* Modal Content */}
        <div className="py-4">
          {children}
        </div>
        
        {renderFooter()}
      </DialogContent>
    </Dialog>
  );
};

// ============================================================================
// SPECIALIZED MODAL VARIANTS
// ============================================================================

/**
 * Activity Details Modal
 * Pre-configured for activity content
 */
export const ActivityModal: React.FC<Omit<UnifiedModalProps, 'contentType' | 'itemType'> & {
  activity?: {
    id: string;
    title: string;
    description: string;
    start_date?: string;
    location?: string;
    capacity?: number;
    type?: string;
  };
}> = ({ activity, ...props }) => {
  if (!activity) return null;

  const metadata = [
    ...(activity.start_date ? [{
      icon: Calendar,
      label: 'Date',
      value: new Date(activity.start_date).toLocaleDateString()
    }] : []),
    ...(activity.location ? [{
      icon: MapPin,
      label: 'Location',
      value: activity.location
    }] : []),
    ...(activity.capacity ? [{
      icon: Users,
      label: 'Capacity',
      value: `${activity.capacity} people`
    }] : [])
  ];

  const actions: ModalAction[] = [
    { type: 'favorite' },
    { type: 'join' },
    { type: 'rsvp' },
    { type: 'share' }
  ];

  return (
    <UnifiedModal
      {...props}
      contentType="activities"
      itemType="activity"
      category={activity.type || 'main'}
      metadata={metadata}
      actions={actions}
    >
      <div className="space-y-4">
        <p className="text-muted-foreground leading-relaxed">
          {activity.description}
        </p>
      </div>
    </UnifiedModal>
  );
};

/**
 * Event Details Modal
 * Pre-configured for event content
 */
export const EventModal: React.FC<Omit<UnifiedModalProps, 'contentType' | 'itemType'>> = (props) => {
  const actions: ModalAction[] = [
    { type: 'favorite' },
    { type: 'save' },
    { type: 'share' }
  ];

  return (
    <UnifiedModal
      {...props}
      contentType="events"
      itemType="event"
      actions={actions}
    />
  );
};

export default UnifiedModal;

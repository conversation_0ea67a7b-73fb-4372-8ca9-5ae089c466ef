/**
 * Unified Modal System
 *
 * Consistent modal implementation that replaces scattered modal variants.
 * Provides standardized behavior, styling, and interaction patterns.
 *
 * Features:
 * - Single source of truth styling using enhancedColorMappingService
 * - Design system token compliance (design-tokens.css)
 * - Responsive design with mobile-first approach (320px+)
 * - Accessible keyboard navigation and WCAG 2.1 AA compliance
 * - Proper image handling with fallbacks and loading states
 * - Standardized close button placement
 * - Touch-friendly interactions
 *
 * @module UnifiedModal
 * @version 2.0.0
 */

import React, { useEffect, useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { X, Calendar, MapPin, Users, Clock, ImageIcon, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';
import { UnifiedInteractionButton } from './UnifiedInteractionButton';
import type { InteractionType } from '@/lib/services/unifiedInteractionService';

// ============================================================================
// IMAGE COMPONENT WITH LOADING STATES
// ============================================================================

interface ModalImageProps {
  src?: string;
  alt: string;
  className?: string;
  fallbackType?: string;
}

const ModalImage: React.FC<ModalImageProps> = ({
  src,
  alt,
  className = '',
  fallbackType = 'activity'
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  useEffect(() => {
    if (!src) {
      setIsLoading(false);
      setHasError(true);
      return;
    }

    setIsLoading(true);
    setHasError(false);
    setImageLoaded(false);

    const img = new Image();
    img.onload = () => {
      setIsLoading(false);
      setImageLoaded(true);
    };
    img.onerror = () => {
      setIsLoading(false);
      setHasError(true);
    };
    img.src = src;
  }, [src]);

  // Fallback gradient using design tokens
  const getFallbackGradient = () => {
    switch (fallbackType) {
      case 'activity':
        return 'var(--vibrant-community)';
      case 'event':
        return 'var(--vibrant-schedule)';
      case 'guide':
        return 'var(--light-effect-primary)';
      case 'tip':
        return 'var(--light-effect-accent)';
      default:
        return 'var(--light-effect-subtle)';
    }
  };

  if (isLoading) {
    return (
      <div
        className={cn(
          "flex items-center justify-center",
          "bg-muted/20 text-muted-foreground",
          className
        )}
        style={{ background: getFallbackGradient() }}
      >
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (hasError || !src) {
    return (
      <div
        className={cn(
          "flex items-center justify-center",
          "text-muted-foreground",
          className
        )}
        style={{ background: getFallbackGradient() }}
      >
        <ImageIcon className="h-12 w-12 opacity-50" />
      </div>
    );
  }

  return (
    <div className={cn("relative overflow-hidden", className)}>
      <img
        src={src}
        alt={alt}
        className="w-full h-full object-cover transition-opacity duration-300"
        style={{ opacity: imageLoaded ? 1 : 0 }}
      />
      {/* Overlay gradient using design tokens */}
      <div
        className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"
        style={{
          background: 'linear-gradient(to top, hsl(var(--background) / 0.8), hsl(var(--background) / 0.2), transparent)'
        }}
      />
    </div>
  );
};

// ============================================================================
// MODAL PROPS AND TYPES
// ============================================================================

export interface ModalAction {
  type: InteractionType;
  label?: string;
  variant?: 'default' | 'outline' | 'ghost';
  onClick?: () => void | Promise<void>;
}

export interface UnifiedModalProps {
  /** Modal open state */
  open: boolean;
  
  /** Close handler */
  onClose: () => void;
  
  /** Modal title */
  title: string;
  
  /** Modal content */
  children: React.ReactNode;
  
  /** Item ID for color theming and interactions */
  itemId: string;
  
  /** Content type for color theming */
  contentType?: string;
  
  /** Category for color theming */
  category?: string;
  
  /** Item type for interactions */
  itemType?: string;
  
  /** Modal size */
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  
  /** Header image URL */
  headerImage?: string;
  
  /** Featured badge text */
  featuredBadge?: string;
  
  /** Additional badges */
  badges?: Array<{
    text: string;
    variant?: string;
    contentType?: string;
    category?: string;
  }>;
  
  /** Metadata items (date, location, etc.) */
  metadata?: Array<{
    icon: React.ComponentType<any>;
    label: string;
    value: string;
  }>;
  
  /** Action buttons */
  actions?: ModalAction[];
  
  /** Show default close button */
  showCloseButton?: boolean;
  
  /** Custom footer content */
  footerContent?: React.ReactNode;
  
  /** Additional CSS classes */
  className?: string;
}

// ============================================================================
// UNIFIED MODAL COMPONENT
// ============================================================================

export const UnifiedModal: React.FC<UnifiedModalProps> = ({
  open,
  onClose,
  title,
  children,
  itemId,
  contentType = 'activities',
  category = 'main',
  itemType = 'activity',
  size = 'lg',
  headerImage,
  featuredBadge,
  badges = [],
  metadata = [],
  actions = [],
  showCloseButton = true,
  footerContent,
  className
}) => {
  const [colorTheme, setColorTheme] = useState<{
    className: string;
    style: React.CSSProperties;
  }>({ className: '', style: {} });
  const [isLoadingTheme, setIsLoadingTheme] = useState(true);

  // ========================================================================
  // COLOR THEME LOADING
  // ========================================================================

  useEffect(() => {
    const loadColorTheme = async () => {
      setIsLoadingTheme(true);
      try {
        const theme = await enhancedColorMappingService.getEnhancedColorTheme(
          contentType, 
          category, 
          0 // Use first fallback theme
        );
        setColorTheme(theme);
      } catch (error) {
        console.warn('Failed to load modal color theme:', error);
        setColorTheme({
          className: 'bg-gradient-to-br from-muted/20 to-card/80',
          style: {
            background: 'var(--light-effect-subtle)',
            transition: 'var(--festival-transition)'
          }
        });
      } finally {
        setIsLoadingTheme(false);
      }
    };

    if (open) {
      loadColorTheme();
    }
  }, [open, contentType, category]);

  // ========================================================================
  // RESPONSIVE SIZE MAPPING
  // ========================================================================

  const getSizeClasses = () => {
    // Mobile-first responsive sizing using design tokens
    const baseClasses = "w-[calc(100vw-2rem)] max-h-[calc(100vh-2rem)]";

    switch (size) {
      case 'sm':
        return cn(baseClasses, "sm:max-w-md sm:w-full");
      case 'md':
        return cn(baseClasses, "sm:max-w-lg sm:w-full");
      case 'lg':
        return cn(baseClasses, "sm:max-w-2xl sm:w-full");
      case 'xl':
        return cn(baseClasses, "sm:max-w-4xl sm:w-full");
      case 'full':
        return cn(baseClasses, "sm:max-w-7xl sm:w-full");
      default:
        return cn(baseClasses, "sm:max-w-2xl sm:w-full");
    }
  };

  const getModalStyles = () => {
    return cn(
      // Base responsive styles
      "overflow-hidden",
      // Design token spacing
      "p-0", // Remove default padding to control it per section
      // Enhanced backdrop and contrast
      "border-0 shadow-xl",
      // Smooth animations using design tokens
      "transition-all duration-200 ease-in-out",
      getSizeClasses()
    );
  };

  // ========================================================================
  // RENDER HELPERS
  // ========================================================================

  const renderHeader = () => (
    <div className="relative">
      {/* Header Image with proper responsive sizing */}
      {headerImage && (
        <div className="relative h-32 sm:h-48 -m-6 mb-4 overflow-hidden">
          <ModalImage
            src={headerImage}
            alt={title}
            className="h-full w-full"
            fallbackType={itemType}
          />

          {/* Featured Badge on Image using design tokens */}
          {featuredBadge && (
            <div
              className="absolute top-3 left-3 sm:top-4 sm:left-4"
              style={{ zIndex: 10 }}
            >
              <Badge
                className="font-semibold backdrop-blur-sm border-0 text-xs sm:text-sm"
                style={{
                  backgroundColor: 'var(--festival-warning)',
                  color: 'var(--festival-warning-text)',
                  boxShadow: 'var(--layer-shadow-sm)'
                }}
              >
                {featuredBadge}
              </Badge>
            </div>
          )}

          {/* Title overlay on image */}
          <div className="absolute bottom-0 left-0 right-0 p-3 sm:p-4">
            <DialogTitle
              className="text-lg sm:text-xl font-bold mb-2"
              style={{ color: 'var(--festival-text-on-dark)' }}
            >
              {title}
            </DialogTitle>
          </div>
        </div>
      )}

      {/* Header without image */}
      {!headerImage && (
        <DialogHeader
          className="p-4 sm:p-6 pb-2"
          style={{
            background: !isLoadingTheme ? colorTheme.style.background : 'var(--festival-bg-card)',
            borderBottom: '1px solid var(--festival-border)'
          }}
        >
          <div className="flex items-start justify-between">
            <DialogTitle
              className="text-lg sm:text-xl font-bold pr-8"
              style={{ color: 'var(--festival-text-auto)' }}
            >
              {title}
            </DialogTitle>
          </div>

          {/* Featured Badge (when no header image) */}
          {featuredBadge && (
            <Badge
              className="w-fit mt-2 text-xs"
              style={{
                backgroundColor: 'var(--festival-warning-bg)',
                color: 'var(--festival-warning)',
                border: '1px solid var(--festival-warning)'
              }}
            >
              {featuredBadge}
            </Badge>
          )}
        </DialogHeader>
      )}

      {/* Content sections for both image and non-image modals */}
      <div className="px-4 sm:px-6 py-2">
        {/* Badges */}
        {badges.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {badges.map((badge, index) => (
              <Badge
                key={index}
                variant={badge.variant as any || 'outline'}
                className="text-xs"
              >
                {badge.text}
              </Badge>
            ))}
          </div>
        )}

        {/* Metadata */}
        {metadata.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
            {metadata.map((item, index) => (
              <div key={index} className="flex items-center gap-2">
                <item.icon
                  className="h-4 w-4 flex-shrink-0"
                  style={{ color: 'var(--festival-text-muted)' }}
                />
                <span
                  className="font-medium"
                  style={{ color: 'var(--festival-text-auto)' }}
                >
                  {item.label}:
                </span>
                <span
                  className="truncate"
                  style={{ color: 'var(--festival-text-auto)' }}
                >
                  {item.value}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  const renderFooter = () => {
    if (!actions.length && !footerContent) return null;

    return (
      <DialogFooter
        className="flex flex-col sm:flex-row gap-2 sm:gap-3 p-4 sm:p-6 pt-2"
        style={{
          borderTop: '1px solid var(--festival-border)',
          backgroundColor: 'var(--festival-bg-muted)'
        }}
      >
        {/* Custom footer content */}
        {footerContent}

        {/* Action buttons with mobile-first responsive design */}
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
          {actions.map((action, index) => (
            <UnifiedInteractionButton
              key={index}
              type={action.type}
              itemId={itemId}
              itemType={itemType}
              variant={action.variant || 'default'}
              label={action.label}
              onClick={action.onClick}
              className="w-full sm:w-auto min-w-[120px]"
              size="default"
            />
          ))}
        </div>
      </DialogFooter>
    );
  };

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        className={getModalStyles()}
        style={{
          // Enhanced backdrop and theming using design tokens
          backgroundColor: !isLoadingTheme && colorTheme.style.backgroundColor
            ? colorTheme.style.backgroundColor
            : 'var(--festival-bg-card)',
          borderColor: 'var(--festival-border)',
          boxShadow: 'var(--layer-shadow-xl)',
          ...(!isLoadingTheme ? colorTheme.style : {})
        }}
        // Accessibility improvements
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
      >
        {renderHeader()}

        {/* Modal Content with proper spacing */}
        <div
          id="modal-description"
          className="px-4 sm:px-6 py-4 overflow-y-auto flex-1"
          style={{
            maxHeight: 'calc(100vh - 16rem)', // Account for header and footer
            color: 'var(--festival-text-auto)'
          }}
        >
          {children}
        </div>

        {renderFooter()}

        {/* Single standardized close button - always visible */}
        {showCloseButton && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="absolute top-3 right-3 sm:top-4 sm:right-4 h-8 w-8 p-0 z-20"
            style={{
              backgroundColor: headerImage ? 'hsl(var(--background) / 0.8)' : 'transparent',
              color: headerImage ? 'var(--festival-text-auto)' : 'var(--festival-text-muted)',
              backdropFilter: headerImage ? 'blur(8px)' : 'none',
              border: headerImage ? '1px solid var(--festival-border)' : 'none'
            }}
            aria-label="Close modal"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </DialogContent>
    </Dialog>
  );
};

// ============================================================================
// SPECIALIZED MODAL VARIANTS
// ============================================================================

/**
 * Activity Details Modal
 * Pre-configured for activity content
 */
export const ActivityModal: React.FC<Omit<UnifiedModalProps, 'contentType' | 'itemType'> & {
  activity?: {
    id: string;
    title: string;
    description: string;
    start_date?: string;
    location?: string;
    capacity?: number;
    type?: string;
  };
}> = ({ activity, ...props }) => {
  if (!activity) return null;

  const metadata = [
    ...(activity.start_date ? [{
      icon: Calendar,
      label: 'Date',
      value: new Date(activity.start_date).toLocaleDateString()
    }] : []),
    ...(activity.location ? [{
      icon: MapPin,
      label: 'Location',
      value: activity.location
    }] : []),
    ...(activity.capacity ? [{
      icon: Users,
      label: 'Capacity',
      value: `${activity.capacity} people`
    }] : [])
  ];

  const actions: ModalAction[] = [
    { type: 'favorite' },
    { type: 'join' },
    { type: 'rsvp' },
    { type: 'share' }
  ];

  return (
    <UnifiedModal
      {...props}
      contentType="activities"
      itemType="activity"
      category={activity.type || 'main'}
      metadata={metadata}
      actions={actions}
    >
      <div className="space-y-4">
        <p className="text-muted-foreground leading-relaxed">
          {activity.description}
        </p>
      </div>
    </UnifiedModal>
  );
};

/**
 * Event Details Modal
 * Pre-configured for event content
 */
export const EventModal: React.FC<Omit<UnifiedModalProps, 'contentType' | 'itemType'>> = (props) => {
  const actions: ModalAction[] = [
    { type: 'favorite' },
    { type: 'save' },
    { type: 'share' }
  ];

  return (
    <UnifiedModal
      {...props}
      contentType="events"
      itemType="event"
      actions={actions}
    />
  );
};

export default UnifiedModal;

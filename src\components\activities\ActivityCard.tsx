/**
 * Activity Card Component
 * 
 * Unified activity card with integrated database-driven user interactions:
 * - Join/Leave functionality
 * - Favorites system
 * - RSVP/Attendance status
 * - Real-time participant counts
 * 
 * This component replaces the old ActivityCard and EnhancedActivityCard
 * with a single, standardized implementation.
 * 
 * @module ActivityCard
 * @version 2.0.0
 * <AUTHOR> Family Team
 */

import React, { useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Heart, 
  Users, 
  MapPin, 
  Calendar, 
  Clock,
  ChevronRight,
  UserPlus,
  UserMinus
} from 'lucide-react'
import { format } from 'date-fns'
import { Button } from '@/components/ui/button'
import { UnifiedBadge } from '@/components/design-system'
import { BentoCard } from '@/components/design-system/BentoGrid'
import { AttendanceButtons } from '@/components/activity-coordination/AttendanceButtons'
import { useUserInteractions } from '@/hooks/useUserInteractions'
import { useActivityTracking } from '@/hooks/useUnifiedTracking'
import { useAuth } from '@/providers/ConsolidatedAuthProvider'
import { cn } from '@/lib/utils'
import type { Activity } from '@/types/database'
import type { AttendanceStatus } from '@/lib/supabase/services/user-interaction-service'

// ============================================================================
// COMPONENT INTERFACE
// ============================================================================

export interface ActivityCardProps {
  activity: Activity
  index?: number
  variant?: 'default' | 'compact' | 'detailed'
  showAttendance?: boolean
  showParticipants?: boolean
  onActivitySelect?: (activity: Activity) => void
  className?: string
}

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

export const ActivityCard: React.FC<ActivityCardProps> = ({
  activity,
  index = 0,
  variant = 'default',
  showAttendance = true,
  showParticipants = true,
  onActivitySelect,
  className
}) => {
  const { user } = useAuth()
  const { trackView } = useActivityTracking()
  
  // User interaction hook for database operations
  const {
    isLoading,
    error,
    userStatus,
    participantCount,
    attendanceCounts,
    joinActivity,
    leaveActivity,
    toggleFavorite,
    setAttendanceStatus
  } = useUserInteractions(activity.id)

  // Track view when component mounts
  useEffect(() => {
    if (activity.id) {
      console.log(`🎯 ActivityCard: Tracking view for activity ${activity.id} (${activity.title})`)
      trackView('activity', activity.id).catch(console.error)
    }
  }, [activity.id, trackView])

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleActivityClick = () => {
    if (onActivitySelect) {
      onActivitySelect(activity)
    }
  }

  const handleJoinLeave = async (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!user) return

    if (userStatus?.is_participant) {
      await leaveActivity()
    } else {
      await joinActivity()
    }
  }

  const handleFavoriteToggle = async (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!user) return
    
    await toggleFavorite()
  }

  const handleAttendanceChange = async (status: AttendanceStatus) => {
    if (!user) return
    await setAttendanceStatus(status)
  }

  // ========================================================================
  // COMPUTED VALUES
  // ========================================================================

  const isUserParticipant = userStatus?.is_participant || false
  const isUserFavorite = userStatus?.is_favorite || false
  const userAttendanceStatus = userStatus?.attendance_status

  const formattedDate = activity.start_date 
    ? format(new Date(activity.start_date), 'MMM dd, yyyy')
    : null

  const formattedTime = activity.start_date 
    ? format(new Date(activity.start_date), 'HH:mm')
    : null

  // ========================================================================
  // RENDER HELPERS
  // ========================================================================

  const renderParticipantInfo = () => {
    if (!showParticipants) return null

    return (
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Users className="h-4 w-4" />
        <span>{participantCount} participant{participantCount !== 1 ? 's' : ''}</span>
        {activity.capacity && (
          <span className="text-xs">
            / {activity.capacity} max
          </span>
        )}
      </div>
    )
  }

  const renderActivityMeta = () => (
    <div className="space-y-2 text-sm text-muted-foreground">
      {activity.location && (
        <div className="flex items-center gap-2">
          <MapPin className="h-4 w-4" />
          <span>{activity.location}</span>
        </div>
      )}
      
      {formattedDate && (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4" />
          <span>{formattedDate}</span>
          {formattedTime && (
            <>
              <Clock className="h-4 w-4 ml-2" />
              <span>{formattedTime}</span>
            </>
          )}
        </div>
      )}

      {renderParticipantInfo()}
    </div>
  )

  const renderActionButtons = () => {
    if (!user) {
      return (
        <Button
          variant="outline"
          size="sm"
          onClick={handleActivityClick}
          className="w-full"
        >
          <ChevronRight className="h-4 w-4 mr-2" />
          View Details
        </Button>
      )
    }

    return (
      <div className="flex flex-col gap-2">
        <div className="flex gap-2">
          {/* Join/Leave Button */}
          <Button
            variant={isUserParticipant ? "destructive" : "default"}
            size="sm"
            onClick={handleJoinLeave}
            disabled={isLoading}
            className="flex-1"
          >
            {isUserParticipant ? (
              <>
                <UserMinus className="h-4 w-4 mr-2" />
                Leave
              </>
            ) : (
              <>
                <UserPlus className="h-4 w-4 mr-2" />
                Join
              </>
            )}
          </Button>

          {/* Favorite Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleFavoriteToggle}
            disabled={isLoading}
            className={cn(
              "px-3",
              isUserFavorite && "text-red-500"
            )}
          >
            <Heart 
              className={cn(
                "h-4 w-4",
                isUserFavorite && "fill-current"
              )} 
            />
          </Button>
        </div>

        {/* Attendance Buttons */}
        {showAttendance && (
          <AttendanceButtons
            activityId={activity.id}
            currentStatus={userAttendanceStatus}
            onStatusChange={handleAttendanceChange}
            counts={attendanceCounts || undefined}
            size="sm"
          />
        )}
      </div>
    )
  }

  // ========================================================================
  // VARIANT RENDERS
  // ========================================================================

  if (variant === 'compact') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.1 }}
        className={cn("group", className)}
      >
        <div className="bg-card hover:bg-card/80 transition-colors rounded-lg p-4 border border-border">
          <div className="flex justify-between items-start mb-2">
            <h3 className="text-lg font-semibold text-card-foreground line-clamp-1">
              {activity.title}
            </h3>
            {user && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleFavoriteToggle}
                disabled={isLoading}
                className={cn(
                  "p-1 h-auto",
                  isUserFavorite && "text-red-500"
                )}
              >
                <Heart 
                  className={cn(
                    "h-4 w-4",
                    isUserFavorite && "fill-current"
                  )} 
                />
              </Button>
            )}
          </div>

          <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
            {activity.description}
          </p>

          {renderActivityMeta()}

          {error && (
            <div className="text-sm text-red-500 bg-red-50 p-2 rounded mt-2">
              {error}
            </div>
          )}

          <div className="mt-3">
            {renderActionButtons()}
          </div>
        </div>
      </motion.div>
    )
  }

  if (variant === 'detailed') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.1 }}
        className={cn("group", className)}
      >
        <BentoCard
          title={activity.title}
          description={activity.description}
          variant="glassmorphism"
          interactive
          onClick={handleActivityClick}
          imageUrl={activity.image_url || undefined}
          className="h-full"
        >
          <div className="space-y-4">
            {renderActivityMeta()}
            
            {error && (
              <div className="text-sm text-red-500 bg-red-50 p-2 rounded">
                {error}
              </div>
            )}

            {renderActionButtons()}
          </div>
        </BentoCard>
      </motion.div>
    )
  }

  // Default variant
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className={cn("group", className)}
    >
      <BentoCard
        title={activity.title}
        description={activity.description}
        variant="glassmorphism"
        interactive
        onClick={handleActivityClick}
        imageUrl={activity.image_url || undefined}
        action={renderActionButtons()}
      >
        {renderActivityMeta()}
        
        {error && (
          <div className="text-sm text-red-500 bg-red-50 p-2 rounded mt-2">
            {error}
          </div>
        )}
      </BentoCard>
    </motion.div>
  )
}

/**
 * Comprehensive Admin Session Persistence Test
 * 
 * This script tests admin session persistence across different scenarios
 * and provides detailed analysis of session management issues.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('🔐 Comprehensive Admin Session Persistence Test');
console.log('===============================================');

async function testAdminSessionPersistence() {
  try {
    console.log('📋 TEST 1: Admin Authentication and Profile Fetching');
    console.log('---------------------------------------------------');
    
    // Test 1: Admin Sign In
    console.log('🔑 Step 1: Admin Sign In');
    const signInStart = Date.now();
    
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testpassword123'
    });
    
    const signInDuration = Date.now() - signInStart;
    
    if (signInError) {
      console.error(`❌ Admin sign in failed in ${signInDuration}ms:`, signInError.message);
      return;
    }
    
    console.log(`✅ Admin sign in successful in ${signInDuration}ms`);
    console.log('👤 User ID:', signInData.user?.id);
    console.log('📧 Email:', signInData.user?.email);
    console.log('🔑 Session:', signInData.session ? 'Present' : 'Missing');
    
    // Test 2: Profile Fetching (Direct Database Query)
    console.log('');
    console.log('👤 Step 2: Direct Profile Fetching');
    const profileStart = Date.now();
    
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', signInData.user.id)
      .single();
    
    const profileDuration = Date.now() - profileStart;
    
    if (profileError) {
      console.error(`❌ Profile fetch failed in ${profileDuration}ms:`, profileError.message);
      console.log('🔍 Error details:', profileError);
    } else {
      console.log(`✅ Profile fetch successful in ${profileDuration}ms`);
      console.log('👤 Profile data:', {
        id: profileData.id,
        email: profileData.email,
        role: profileData.role,
        full_name: profileData.full_name,
        username: profileData.username
      });
      console.log('🛡️ Is Admin:', profileData.role === 'SUPER_ADMIN' || profileData.role === 'CONTENT_ADMIN' || profileData.role === 'MODERATOR');
    }
    
    // Test 3: Session Persistence Check
    console.log('');
    console.log('🔄 Step 3: Session Persistence Check');
    
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Session check failed:', sessionError.message);
    } else {
      console.log('✅ Session check successful');
      console.log('🔑 Session status:', sessionData.session ? 'Active' : 'Inactive');
      console.log('⏰ Session expires:', sessionData.session?.expires_at ? new Date(sessionData.session.expires_at * 1000).toISOString() : 'Unknown');
    }
    
    // Test 4: Admin Permission Check
    console.log('');
    console.log('🛡️ Step 4: Admin Permission Verification');
    
    if (profileData && profileData.role) {
      const adminRoles = ['SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR'];
      const isAdmin = adminRoles.includes(profileData.role);
      
      console.log('✅ Role verification complete');
      console.log('👤 User role:', profileData.role);
      console.log('🛡️ Admin status:', isAdmin ? 'ADMIN' : 'USER');
      console.log('🔐 Admin permissions:', isAdmin ? 'GRANTED' : 'DENIED');
      
      if (isAdmin) {
        console.log('🎯 Admin access should be allowed');
      } else {
        console.log('⚠️ Admin access should be denied');
      }
    }
    
    // Test 5: Timeout Simulation
    console.log('');
    console.log('⏱️ Step 5: Timeout Behavior Simulation');
    
    console.log('🔍 Testing profile fetch with timeout...');
    const timeoutStart = Date.now();
    
    const timeoutPromise = new Promise((resolve) => {
      setTimeout(() => {
        console.log('⏰ 2-second timeout triggered (simulating app timeout)');
        resolve({ timeout: true });
      }, 2000);
    });
    
    const profilePromise = supabase
      .from('profiles')
      .select('*')
      .eq('id', signInData.user.id)
      .single();
    
    const result = await Promise.race([profilePromise, timeoutPromise]);
    const timeoutDuration = Date.now() - timeoutStart;
    
    if (result.timeout) {
      console.log(`⚠️ Profile fetch timed out after ${timeoutDuration}ms`);
      console.log('🔧 This simulates the app timeout behavior');
    } else {
      console.log(`✅ Profile fetch completed before timeout in ${timeoutDuration}ms`);
    }

  } catch (error) {
    console.error('💥 Test failed with exception:', error);
  }
}

// Run the comprehensive test
testAdminSessionPersistence().then(() => {
  console.log('');
  console.log('📊 ADMIN SESSION PERSISTENCE TEST SUMMARY');
  console.log('=========================================');
  console.log('');
  console.log('🎯 KEY FINDINGS:');
  console.log('✅ Admin authentication functionality tested');
  console.log('✅ Profile fetching performance measured');
  console.log('✅ Session persistence verified');
  console.log('✅ Admin role detection validated');
  console.log('✅ Timeout behavior analyzed');
  console.log('');
  console.log('📝 RECOMMENDATIONS:');
  console.log('1. Check if profile fetching completes within 2-second timeout');
  console.log('2. Verify admin role detection is working correctly');
  console.log('3. Ensure session persistence across page navigation');
  console.log('4. Test admin access control with proper role verification');
  process.exit(0);
}).catch(error => {
  console.error('💥 Test suite failed:', error);
  process.exit(1);
});

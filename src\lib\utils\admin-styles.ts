/**
 * Admin Section Styling Standards
 * 
 * This file provides centralized styling utilities for admin components,
 * ensuring consistency across all admin sections while following modern
 * design system principles and accessibility standards.
 * 
 * Created: 2025-06-26
 * Purpose: Systematic admin section standardization
 */

// ============================================================================
// STATUS COLOR MAPPINGS
// ============================================================================

/**
 * Standardized status colors using design tokens
 * Ensures consistent visual representation across all admin sections
 * Supports both light and dark modes through CSS variables
 */
export const statusColors = {
  // Content Status
  DRAFT: 'bg-muted/20 text-muted-foreground border-muted/30',
  PUBLISHED: 'bg-festival-success/20 text-festival-success border-festival-success/30',
  ARCHIVED: 'bg-muted/40 text-muted-foreground border-muted/50',
  
  // Activity Status
  ACTIVE: 'bg-festival-success/20 text-festival-success border-festival-success/30',
  INACTIVE: 'bg-destructive/20 text-destructive border-destructive/30',
  
  // Priority Levels
  HIGH: 'bg-destructive/20 text-destructive border-destructive/30',
  MEDIUM: 'bg-festival-warning/20 text-festival-warning border-festival-warning/30',
  LOW: 'bg-muted/20 text-muted-foreground border-muted/30',
  
  // User Roles (already standardized in previous work)
  SUPER_ADMIN: 'bg-destructive/20 text-destructive border-destructive/30',
  CONTENT_ADMIN: 'bg-festival-warning/20 text-festival-warning border-festival-warning/30',
  MODERATOR: 'bg-primary/20 text-primary border-primary/30',
  USER: 'bg-muted/20 text-muted-foreground border-muted/30',
} as const;

// ============================================================================
// BUTTON PATTERNS
// ============================================================================

/**
 * Standardized button classes for admin actions
 * Provides consistent interaction patterns across all admin sections
 */
export const adminButtonStyles = {
  // Primary action buttons
  primary: 'bg-primary text-primary-foreground hover:bg-primary/90 micro-bounce hover-lift transition-all duration-300',

  // Edit actions - ghost variant with icon
  edit: 'variant-ghost size-sm h-8 px-3 hover:bg-muted micro-bounce transition-all duration-300',

  // Delete actions - ghost variant with destructive styling
  delete: 'variant-ghost size-sm h-8 px-3 text-destructive hover:text-destructive hover:bg-destructive/10 micro-bounce transition-all duration-300',

  // Status toggle actions
  statusActive: 'variant-ghost size-sm h-8 px-3 text-festival-success hover:bg-festival-success/10 micro-bounce transition-all duration-300',
  statusInactive: 'variant-ghost size-sm h-8 px-3 text-destructive hover:bg-destructive/10 micro-bounce transition-all duration-300',
  
  // View actions
  view: 'variant-ghost size-sm h-8 px-3 hover:bg-muted',
} as const;

// ============================================================================
// TEXT HIERARCHY
// ============================================================================

/**
 * Standardized text hierarchy for admin forms and content
 * Ensures proper contrast ratios and accessibility compliance
 */
export const adminTextStyles = {
  // Page titles - responsive sizing
  pageTitle: 'text-responsive-lg font-bold text-foreground',

  // Section headings - responsive sizing
  sectionHeading: 'text-responsive font-semibold text-foreground',

  // Form labels
  formLabel: 'text-foreground font-medium',

  // Form descriptions/helper text - responsive sizing
  formDescription: 'text-muted-foreground text-responsive',

  // Error messages - responsive sizing
  formError: 'text-destructive text-responsive',
  
  // Table headers
  tableHeader: 'text-foreground font-medium',
  
  // Table content
  tableContent: 'text-foreground',
  tableContentSecondary: 'text-muted-foreground',
  
  // Card content
  cardTitle: 'text-foreground font-semibold',
  cardDescription: 'text-muted-foreground',
} as const;

// ============================================================================
// LAYOUT PATTERNS
// ============================================================================

/**
 * Standardized layout classes for admin components
 * Provides consistent spacing and structure
 */
export const adminLayoutStyles = {
  // Page containers
  pageContainer: 'container mx-auto py-6',
  
  // Section spacing
  sectionSpacing: 'space-y-6',
  
  // Form spacing
  formSpacing: 'space-y-4',
  
  // Button groups
  buttonGroup: 'flex space-x-2',
  
  // Table containers
  tableContainer: 'overflow-x-auto',
  
  // Card grids
  cardGrid: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6',
} as const;

// ============================================================================
// ACCESSIBILITY HELPERS
// ============================================================================

/**
 * Accessibility-focused utilities for admin components
 * Ensures WCAG 2.1 compliance and proper screen reader support
 */
export const adminA11yStyles = {
  // Screen reader only text
  srOnly: 'sr-only',
  
  // Focus indicators
  focusRing: 'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  
  // Skip links
  skipLink: 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-background text-foreground p-2 rounded',
} as const;

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get status color class for a given status value
 * @param status - The status value to get colors for
 * @param fallback - Fallback status if the provided status is not found
 * @returns CSS class string for the status
 */
export function getStatusColor(status: string, fallback: keyof typeof statusColors = 'DRAFT'): string {
  const upperStatus = status.toUpperCase() as keyof typeof statusColors;
  return statusColors[upperStatus] || statusColors[fallback];
}

/**
 * Get button classes for admin actions
 * @param action - The action type (edit, delete, view, etc.)
 * @param additionalClasses - Additional CSS classes to append
 * @returns Complete CSS class string for the button
 */
export function getAdminButtonClasses(
  action: keyof typeof adminButtonStyles, 
  additionalClasses: string = ''
): string {
  return `${adminButtonStyles[action]} ${additionalClasses}`.trim();
}

/**
 * Get text classes for admin content
 * @param textType - The type of text (pageTitle, formLabel, etc.)
 * @param additionalClasses - Additional CSS classes to append
 * @returns Complete CSS class string for the text
 */
export function getAdminTextClasses(
  textType: keyof typeof adminTextStyles,
  additionalClasses: string = ''
): string {
  return `${adminTextStyles[textType]} ${additionalClasses}`.trim();
}

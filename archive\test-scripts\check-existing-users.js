/**
 * Check Existing Users in Database
 * 
 * This script checks what users exist in the database and their roles
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('🔍 Checking Existing Users in Database');
console.log('=====================================');

async function checkExistingUsers() {
  try {
    // First, sign in as admin to access the profiles table
    console.log('🔐 Signing in as admin to check users');
    console.log('------------------------------------');
    
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testpassword123'
    });
    
    if (authError) {
      console.error('❌ Admin authentication failed:', authError.message);
      return;
    }
    
    console.log('✅ Admin authentication successful');
    console.log('');

    // Check all profiles in the database
    console.log('👥 Checking all profiles in database');
    console.log('-----------------------------------');
    
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (profilesError) {
      console.error('❌ Failed to fetch profiles:', profilesError.message);
      return;
    }
    
    console.log(`📊 Found ${profiles.length} profiles in database:`);
    console.log('');
    
    profiles.forEach((profile, index) => {
      console.log(`👤 User ${index + 1}:`);
      console.log(`   📧 Email: ${profile.email}`);
      console.log(`   👤 Username: ${profile.username || 'N/A'}`);
      console.log(`   🔑 Role: ${profile.role}`);
      console.log(`   🆔 ID: ${profile.id}`);
      console.log(`   📅 Created: ${profile.created_at}`);
      console.log(`   🔍 Is Admin: ${profile.role === 'SUPER_ADMIN' || profile.role === 'ADMIN'}`);
      console.log('');
    });
    
    // Analyze user distribution
    const adminUsers = profiles.filter(p => p.role === 'SUPER_ADMIN' || p.role === 'ADMIN');
    const regularUsers = profiles.filter(p => p.role === 'user');
    const otherRoles = profiles.filter(p => p.role !== 'SUPER_ADMIN' && p.role !== 'ADMIN' && p.role !== 'user');
    
    console.log('📈 User Distribution Analysis');
    console.log('----------------------------');
    console.log(`👑 Admin users: ${adminUsers.length}`);
    console.log(`👤 Regular users: ${regularUsers.length}`);
    console.log(`❓ Other roles: ${otherRoles.length}`);
    console.log('');
    
    if (regularUsers.length > 0) {
      console.log('✅ Regular users found! We can test with existing regular users.');
      console.log('📋 Regular users available for testing:');
      regularUsers.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.email} (${user.username || 'no username'})`);
      });
    } else {
      console.log('⚠️  No regular users found. We need to create one for testing.');
    }
    
    if (otherRoles.length > 0) {
      console.log('🔍 Users with other roles:');
      otherRoles.forEach((user) => {
        console.log(`   - ${user.email}: ${user.role}`);
      });
    }

  } catch (error) {
    console.error('💥 Check failed with exception:', error);
  }
}

// Run the check
checkExistingUsers().then(() => {
  console.log('');
  console.log('🎯 User Database Check Complete');
  console.log('==============================');
  process.exit(0);
}).catch(error => {
  console.error('💥 Check failed:', error);
  process.exit(1);
});

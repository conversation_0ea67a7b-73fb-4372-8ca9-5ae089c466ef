#!/usr/bin/env node

/**
 * Comprehensive Admin Functionality Testing
 * 
 * This script performs complete manual testing verification of admin functionality:
 * 1. Admin authentication testing
 * 2. Role-based access control verification
 * 3. Systematic testing of all admin dashboard sections
 * 4. Evidence-based verification with screenshots
 * 5. Complete admin user journey validation
 */

import { chromium } from 'playwright';
import { promises as fs } from 'fs';
import path from 'path';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'comprehensive-admin-evidence';

// Admin credentials from user confirmation
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

// Test results structure
const testResults = {
  timestamp: new Date().toISOString(),
  adminCredentials: ADMIN_CREDENTIALS,
  authenticationTests: {},
  accessControlTests: {},
  dashboardTests: {},
  navigationTests: {},
  functionalityTests: {},
  userJourneyTests: {},
  issues: [],
  screenshots: [],
  consoleErrors: [],
  summary: {}
};

async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

async function captureScreenshot(page, name, description) {
  const filename = `${String(testResults.screenshots.length + 1).padStart(2, '0')}-${name}.png`;
  const filepath = path.join(EVIDENCE_DIR, filename);
  
  await page.screenshot({ 
    path: filepath, 
    fullPage: true,
    animations: 'disabled'
  });
  
  testResults.screenshots.push({
    filename,
    description,
    timestamp: new Date().toISOString()
  });
  
  console.log(`📸 Screenshot captured: ${filename} - ${description}`);
  return filename;
}

async function captureConsoleErrors(page) {
  page.on('console', msg => {
    if (msg.type() === 'error') {
      testResults.consoleErrors.push({
        message: msg.text(),
        timestamp: new Date().toISOString()
      });
    }
  });
}

async function testAdminAuthentication(page) {
  console.log('\n🔐 PHASE 1: Admin Authentication Testing');
  console.log('==========================================');
  
  try {
    // Navigate to auth page
    console.log('📍 Navigating to authentication page...');
    await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded', timeout: 15000 });
    await page.waitForTimeout(2000);
    
    await captureScreenshot(page, 'auth-page', 'Authentication page loaded');
    
    // Check if already signed in
    const currentUrl = page.url();
    if (!currentUrl.includes('/auth')) {
      console.log('✅ User already authenticated, signing out first...');
      await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded' });
      await page.waitForTimeout(2000);
    }
    
    // Fill in admin credentials
    console.log('📝 Entering admin credentials...');
    console.log(`   Email: ${ADMIN_CREDENTIALS.email}`);
    
    // Look for email input
    const emailInput = await page.locator('input[type="email"]').first();
    const passwordInput = await page.locator('input[type="password"]').first();
    
    if (await emailInput.isVisible()) {
      await emailInput.fill(ADMIN_CREDENTIALS.email);
      await passwordInput.fill(ADMIN_CREDENTIALS.password);
      
      await captureScreenshot(page, 'credentials-entered', 'Admin credentials entered');
      
      // Submit form
      console.log('🚀 Submitting login form...');
      const submitButton = await page.locator('button[type="submit"]').first();
      await submitButton.click();
      
      // Wait for navigation or error
      await page.waitForTimeout(3000);
      
      const finalUrl = page.url();
      console.log(`📍 Final URL after login: ${finalUrl}`);
      
      if (finalUrl.includes('/auth')) {
        // Still on auth page, check for errors
        const errorMessage = await page.locator('[role="alert"], .error, .text-red').first().textContent().catch(() => null);
        testResults.authenticationTests.loginSuccess = false;
        testResults.authenticationTests.error = errorMessage || 'Login failed - remained on auth page';
        testResults.issues.push('Admin login failed - credentials may be invalid or user may not exist');
        console.log('❌ Admin login failed');
      } else {
        testResults.authenticationTests.loginSuccess = true;
        testResults.authenticationTests.redirectUrl = finalUrl;
        console.log('✅ Admin login successful');
      }
      
      await captureScreenshot(page, 'post-login', 'After login attempt');
      
    } else {
      testResults.issues.push('Email input not found on auth page');
      console.log('❌ Could not find email input on auth page');
    }
    
  } catch (error) {
    testResults.authenticationTests.error = error.message;
    testResults.issues.push(`Authentication test error: ${error.message}`);
    console.log(`❌ Authentication test error: ${error.message}`);
  }
}

async function testRoleBasedAccessControl(page) {
  console.log('\n👑 PHASE 2: Role-Based Access Control Testing');
  console.log('==============================================');
  
  try {
    // Check for admin navigation elements
    console.log('🔍 Checking for admin navigation elements...');
    
    const adminNavElements = {
      adminLink: await page.locator('a[href*="admin"], a:has-text("Admin")').count(),
      adminButton: await page.locator('button:has-text("Admin")').count(),
      adminMenu: await page.locator('[data-testid="admin-nav"], .admin-nav').count()
    };
    
    testResults.accessControlTests.adminNavElements = adminNavElements;
    console.log(`   Admin links found: ${adminNavElements.adminLink}`);
    console.log(`   Admin buttons found: ${adminNavElements.adminButton}`);
    console.log(`   Admin menus found: ${adminNavElements.adminMenu}`);
    
    // Try to access admin dashboard directly
    console.log('🚪 Testing direct admin dashboard access...');
    await page.goto(`${APP_URL}/admin`, { waitUntil: 'domcontentloaded', timeout: 10000 });
    await page.waitForTimeout(2000);
    
    const adminUrl = page.url();
    const adminPageContent = await page.textContent('body');
    
    testResults.accessControlTests.directAccess = {
      url: adminUrl,
      hasAdminContent: adminPageContent.toLowerCase().includes('admin'),
      hasAccessDenied: adminPageContent.toLowerCase().includes('access denied') || 
                       adminPageContent.toLowerCase().includes('unauthorized'),
      pageTitle: await page.title()
    };
    
    await captureScreenshot(page, 'admin-direct-access', 'Direct admin dashboard access attempt');
    
    if (adminUrl.includes('/admin')) {
      console.log('✅ Admin dashboard accessible');
      testResults.accessControlTests.adminAccessible = true;
    } else {
      console.log('❌ Admin dashboard not accessible - redirected');
      testResults.accessControlTests.adminAccessible = false;
    }
    
  } catch (error) {
    testResults.accessControlTests.error = error.message;
    testResults.issues.push(`Access control test error: ${error.message}`);
    console.log(`❌ Access control test error: ${error.message}`);
  }
}

async function testAdminDashboardSections(page) {
  console.log('\n📊 PHASE 3: Admin Dashboard Sections Testing');
  console.log('=============================================');
  
  if (!testResults.accessControlTests.adminAccessible) {
    console.log('⏭️ Skipping dashboard tests - admin not accessible');
    testResults.dashboardTests.skipped = true;
    testResults.dashboardTests.reason = 'Admin dashboard not accessible';
    return;
  }
  
  try {
    // Ensure we're on admin dashboard
    await page.goto(`${APP_URL}/admin`, { waitUntil: 'domcontentloaded' });
    await page.waitForTimeout(2000);
    
    await captureScreenshot(page, 'admin-dashboard', 'Admin dashboard main page');
    
    // Test admin navigation sections
    const adminSections = [
      { name: 'Dashboard', selectors: ['a[href*="dashboard"]', 'a:has-text("Dashboard")'] },
      { name: 'Users', selectors: ['a[href*="users"]', 'a:has-text("Users")'] },
      { name: 'Content', selectors: ['a[href*="content"]', 'a:has-text("Content")'] },
      { name: 'Analytics', selectors: ['a[href*="analytics"]', 'a:has-text("Analytics")'] },
      { name: 'Settings', selectors: ['a[href*="settings"]', 'a:has-text("Settings")'] }
    ];
    
    testResults.dashboardTests.sections = {};
    
    for (const section of adminSections) {
      console.log(`🔍 Testing ${section.name} section...`);
      
      let sectionLink = null;
      for (const selector of section.selectors) {
        const element = page.locator(selector).first();
        if (await element.count() > 0) {
          sectionLink = element;
          break;
        }
      }
      
      const sectionResult = {
        name: section.name,
        linkFound: !!sectionLink,
        accessible: false,
        error: null
      };
      
      if (sectionLink) {
        try {
          await sectionLink.click();
          await page.waitForTimeout(2000);
          
          const sectionUrl = page.url();
          sectionResult.accessible = true;
          sectionResult.url = sectionUrl;
          
          await captureScreenshot(page, `admin-${section.name.toLowerCase()}`, `Admin ${section.name} section`);
          
          console.log(`   ✅ ${section.name} section accessible`);
          
          // Go back to main admin page
          await page.goto(`${APP_URL}/admin`, { waitUntil: 'domcontentloaded' });
          await page.waitForTimeout(1000);
          
        } catch (error) {
          sectionResult.error = error.message;
          console.log(`   ❌ ${section.name} section error: ${error.message}`);
        }
      } else {
        console.log(`   ⚠️ ${section.name} section link not found`);
      }
      
      testResults.dashboardTests.sections[section.name] = sectionResult;
    }
    
  } catch (error) {
    testResults.dashboardTests.error = error.message;
    testResults.issues.push(`Dashboard test error: ${error.message}`);
    console.log(`❌ Dashboard test error: ${error.message}`);
  }
}

async function testCompleteUserJourney(page) {
  console.log('\n🚀 PHASE 4: Complete Admin User Journey Testing');
  console.log('================================================');
  
  try {
    // Test complete journey: Login → Dashboard → Sections → Logout
    console.log('🔄 Testing complete admin user journey...');
    
    const journey = {
      steps: [],
      success: true,
      totalTime: Date.now()
    };
    
    // Step 1: Navigate to home
    journey.steps.push({ step: 'Navigate to home', timestamp: Date.now() });
    await page.goto(APP_URL, { waitUntil: 'domcontentloaded' });
    await page.waitForTimeout(1000);
    
    // Step 2: Access admin (if accessible)
    if (testResults.accessControlTests.adminAccessible) {
      journey.steps.push({ step: 'Access admin dashboard', timestamp: Date.now() });
      await page.goto(`${APP_URL}/admin`, { waitUntil: 'domcontentloaded' });
      await page.waitForTimeout(2000);
    }
    
    // Step 3: Test logout
    journey.steps.push({ step: 'Test logout', timestamp: Date.now() });
    const logoutButton = page.locator('button:has-text("Sign Out"), button:has-text("Logout"), a:has-text("Sign Out")').first();
    
    if (await logoutButton.count() > 0) {
      await logoutButton.click();
      await page.waitForTimeout(2000);
      
      const finalUrl = page.url();
      journey.logoutSuccess = !finalUrl.includes('/admin') && 
                             (finalUrl.includes('/auth') || finalUrl === `${APP_URL}/`);
      
      await captureScreenshot(page, 'post-logout', 'After logout');
    } else {
      journey.logoutSuccess = false;
      journey.logoutError = 'Logout button not found';
    }
    
    journey.totalTime = Date.now() - journey.totalTime;
    testResults.userJourneyTests = journey;
    
    console.log(`✅ User journey test completed in ${journey.totalTime}ms`);
    
  } catch (error) {
    testResults.userJourneyTests.error = error.message;
    testResults.issues.push(`User journey test error: ${error.message}`);
    console.log(`❌ User journey test error: ${error.message}`);
  }
}

async function generateSummary() {
  console.log('\n📋 PHASE 5: Test Summary Generation');
  console.log('===================================');
  
  const summary = {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    issues: testResults.issues.length,
    screenshots: testResults.screenshots.length,
    consoleErrors: testResults.consoleErrors.length
  };
  
  // Count test results
  if (testResults.authenticationTests.loginSuccess !== undefined) {
    summary.totalTests++;
    if (testResults.authenticationTests.loginSuccess) summary.passedTests++;
    else summary.failedTests++;
  }
  
  if (testResults.accessControlTests.adminAccessible !== undefined) {
    summary.totalTests++;
    if (testResults.accessControlTests.adminAccessible) summary.passedTests++;
    else summary.failedTests++;
  }
  
  if (testResults.dashboardTests.sections) {
    const sectionCount = Object.keys(testResults.dashboardTests.sections).length;
    summary.totalTests += sectionCount;
    summary.passedTests += Object.values(testResults.dashboardTests.sections)
      .filter(section => section.accessible).length;
    summary.failedTests += Object.values(testResults.dashboardTests.sections)
      .filter(section => !section.accessible).length;
  }
  
  summary.successRate = summary.totalTests > 0 ? 
    Math.round((summary.passedTests / summary.totalTests) * 100) : 0;
  
  testResults.summary = summary;
  
  console.log(`📊 Test Summary:`);
  console.log(`   Total Tests: ${summary.totalTests}`);
  console.log(`   Passed: ${summary.passedTests}`);
  console.log(`   Failed: ${summary.failedTests}`);
  console.log(`   Success Rate: ${summary.successRate}%`);
  console.log(`   Issues Found: ${summary.issues}`);
  console.log(`   Screenshots: ${summary.screenshots}`);
  console.log(`   Console Errors: ${summary.consoleErrors}`);
}

async function saveResults() {
  const resultsFile = path.join(EVIDENCE_DIR, 'comprehensive-admin-test-results.json');
  await fs.writeFile(resultsFile, JSON.stringify(testResults, null, 2));
  console.log(`💾 Test results saved: ${resultsFile}`);
}

async function runComprehensiveAdminTest() {
  console.log('🎯 COMPREHENSIVE ADMIN FUNCTIONALITY TESTING');
  console.log('============================================');
  console.log(`🌐 Testing URL: ${APP_URL}`);
  console.log(`👤 Admin Email: ${ADMIN_CREDENTIALS.email}`);
  console.log(`📁 Evidence Directory: ${EVIDENCE_DIR}`);
  
  await ensureEvidenceDir();
  
  const browser = await chromium.launch({ 
    headless: false,  // Set to false to see the browser
    slowMo: 1000      // Slow down for better observation
  });
  
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  });
  
  const page = await context.newPage();
  await captureConsoleErrors(page);
  
  try {
    await testAdminAuthentication(page);
    await testRoleBasedAccessControl(page);
    await testAdminDashboardSections(page);
    await testCompleteUserJourney(page);
    await generateSummary();
    await saveResults();
    
    console.log('\n🎉 COMPREHENSIVE ADMIN TESTING COMPLETE');
    console.log('=======================================');
    console.log(`📊 Success Rate: ${testResults.summary.successRate}%`);
    console.log(`📁 Evidence saved in: ${EVIDENCE_DIR}`);
    
    if (testResults.issues.length > 0) {
      console.log('\n⚠️ Issues Found:');
      testResults.issues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Test execution error:', error);
  } finally {
    await browser.close();
  }
}

// Run the comprehensive test
runComprehensiveAdminTest().catch(console.error);

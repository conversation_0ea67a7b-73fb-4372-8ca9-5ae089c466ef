/**
 * Festival Family - Dashboard Intelligence Service
 *
 * This service provides intelligent content curation for the authenticated home dashboard.
 * It analyzes user behavior, preferences, and activity history to deliver personalized
 * recommendations and relevant content.
 *
 * @module DashboardIntelligenceService
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { BaseService, ServiceResponse } from '../supabase/services/base-service'
import type { Activity, Announcement, Event, Profile } from '@/types'
import { weatherService } from './weather-service'

// ============================================================================
// DASHBOARD INTELLIGENCE TYPES
// ============================================================================

export interface WeatherAlert {
  id: string
  type: 'warning' | 'watch' | 'advisory'
  title: string
  description: string
  severity: 'low' | 'medium' | 'high'
  validUntil: string
  location?: string
}

export interface LiveUpdate {
  id: string
  type: 'happening_now' | 'starting_soon' | 'weather_alert' | 'announcement' | 'community_buzz'
  title: string
  message: string
  actionText?: string
  actionUrl?: string
  urgency: 'low' | 'medium' | 'high' | 'critical'
  timestamp: string
  icon?: string
}

export interface QuickAction {
  id: string
  title: string
  description: string
  icon: string
  actionUrl: string
  available: boolean
  urgency: 'low' | 'medium' | 'high'
}

export interface CommunityBuzz {
  id: string
  type: 'user_joined' | 'activity_popular' | 'new_activity' | 'weather_update'
  message: string
  timestamp: string
  relatedId?: string
}

export interface PersonalizedContent {
  activities: Activity[]
  score: number
  reason: string
}

export interface DashboardIntelligence {
  happeningNow: Activity[]           // Activities starting in next 30 mins
  yourSchedule: Activity[]           // User's activities happening today/soon
  liveUpdates: LiveUpdate[]          // Real-time feed of important updates
  quickActions: QuickAction[]        // Join now, explore nearby, etc.
  communityBuzz: CommunityBuzz[]     // Recent community activity
  weatherAlerts: WeatherAlert[]      // Real weather warnings
  lastUpdated: string
}

// ============================================================================
// DASHBOARD INTELLIGENCE SERVICE
// ============================================================================

export class DashboardIntelligenceService extends BaseService {
  /**
   * Get comprehensive dashboard intelligence for a user
   */
  async getDashboardIntelligence(userId: string): Promise<ServiceResponse<DashboardIntelligence>> {
    try {
      const [
        happeningNow,
        yourSchedule,
        liveUpdates,
        quickActions,
        communityBuzz,
        weatherAlerts
      ] = await Promise.all([
        this.getHappeningNow(),
        this.getUserSchedule(userId),
        this.getLiveUpdates(userId),
        this.getQuickActions(userId),
        this.getCommunityBuzz(),
        this.getWeatherAlerts()
      ])

      const intelligence: DashboardIntelligence = {
        happeningNow: happeningNow.data || [],
        yourSchedule: yourSchedule.data || [],
        liveUpdates: liveUpdates.data || [],
        quickActions: quickActions.data || [],
        communityBuzz: communityBuzz.data || [],
        weatherAlerts: weatherAlerts.data || [],
        lastUpdated: new Date().toISOString()
      }

      return {
        data: intelligence,
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting dashboard intelligence:', error)
      return {
        data: null,
        error: error as Error,
        status: 'error'
      }
    }
  }

  /**
   * Get activities happening right now (starting in next 30 minutes)
   */
  private async getHappeningNow(): Promise<ServiceResponse<Activity[]>> {
    try {
      const now = new Date()
      const thirtyMinutesFromNow = new Date(now.getTime() + 30 * 60 * 1000)

      const { data: activities } = await this.client
        .from('activities')
        .select('*')
        .gte('start_date', now.toISOString())
        .lte('start_date', thirtyMinutesFromNow.toISOString())
        .eq('status', 'published')
        .order('start_date', { ascending: true })
        .limit(5)

      return {
        data: activities || [],
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting happening now activities:', error)
      return {
        data: [],
        error: error as Error,
        status: 'error'
      }
    }
  }

  /**
   * Get user's scheduled activities for today and soon
   */
  private async getUserSchedule(userId: string): Promise<ServiceResponse<Activity[]>> {
    try {
      const today = new Date()
      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)

      const { data: userActivities } = await this.client
        .from('activity_participants')
        .select(`
          activities (
            id,
            title,
            description,
            location,
            start_date,
            end_date,
            status,
            is_active
          )
        `)
        .eq('user_id', userId)
        .eq('status', 'registered')
        .gte('activities.start_date', today.toISOString())
        .lte('activities.start_date', tomorrow.toISOString())
        .eq('activities.status', 'PUBLISHED')
        .eq('activities.is_active', true)
        .order('activities.start_date', { ascending: true })

      const activities = userActivities?.map(ua => ua.activities as any).filter(Boolean) || []

      return {
        data: activities,
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting user schedule:', error)
      return {
        data: [],
        error: error as Error,
        status: 'error'
      }
    }
  }

  /**
   * Get live updates feed for social-style dashboard
   */
  private async getLiveUpdates(userId: string): Promise<ServiceResponse<LiveUpdate[]>> {
    try {
      const updates: LiveUpdate[] = []

      // Get recent announcements as live updates
      const { data: announcements } = await this.client
        .from('announcements')
        .select('*')
        .eq('active', true)
        .order('created_at', { ascending: false })
        .limit(3)

      announcements?.forEach(announcement => {
        updates.push({
          id: announcement.id,
          type: 'announcement',
          title: announcement.title,
          message: announcement.content,
          urgency: announcement.priority === 'HIGH' ? 'high' : 'medium',
          timestamp: announcement.created_at
        })
      })

      // Add happening now activities as live updates
      const happeningNow = await this.getHappeningNow()
      happeningNow.data?.forEach(activity => {
        updates.push({
          id: activity.id,
          type: 'happening_now',
          title: '🔥 Starting Now!',
          message: `${activity.title} is starting at ${activity.location}`,
          actionText: 'Join Now',
          actionUrl: `/activities/${activity.id}`,
          urgency: 'high',
          timestamp: activity.start_date || new Date().toISOString()
        })
      })

      // Sort by urgency and timestamp
      updates.sort((a, b) => {
        const urgencyOrder = { critical: 4, high: 3, medium: 2, low: 1 }
        if (urgencyOrder[a.urgency] !== urgencyOrder[b.urgency]) {
          return urgencyOrder[b.urgency] - urgencyOrder[a.urgency]
        }
        return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      })

      return {
        data: updates.slice(0, 5),
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting live updates:', error)
      return {
        data: [],
        error: error as Error,
        status: 'error'
      }
    }
  }

  /**
   * Get quick actions for immediate participation
   */
  private async getQuickActions(userId: string): Promise<ServiceResponse<QuickAction[]>> {
    try {
      const actions: QuickAction[] = [
        {
          id: 'explore-nearby',
          title: 'Explore Nearby',
          description: 'Find activities happening around you',
          icon: 'map-pin',
          actionUrl: '/activities?filter=nearby',
          available: true,
          urgency: 'medium'
        },
        {
          id: 'join-happening-now',
          title: 'Join Something Now',
          description: 'Activities starting in the next 30 minutes',
          icon: 'clock',
          actionUrl: '/activities?filter=happening-now',
          available: true,
          urgency: 'high'
        },
        {
          id: 'discover-events',
          title: 'Discover Events',
          description: 'Browse upcoming festivals and events',
          icon: 'calendar',
          actionUrl: '/discover',
          available: true,
          urgency: 'low'
        },
        {
          id: 'connect-community',
          title: 'Connect',
          description: 'Join community chats and groups',
          icon: 'users',
          actionUrl: '/famhub',
          available: true,
          urgency: 'medium'
        }
      ]

      return {
        data: actions,
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting quick actions:', error)
      return {
        data: [],
        error: error as Error,
        status: 'error'
      }
    }
  }

  /**
   * Get community buzz - recent activity from other users
   */
  private async getCommunityBuzz(): Promise<ServiceResponse<CommunityBuzz[]>> {
    try {
      const buzz: CommunityBuzz[] = []

      // Get recent activity participants (anonymized)
      const { data: recentParticipants } = await this.client
        .from('activity_participants')
        .select(`
          created_at,
          activities (title, id)
        `)
        .eq('status', 'registered')
        .order('created_at', { ascending: false })
        .limit(10)

      recentParticipants?.forEach(participant => {
        const activity = participant.activities as any // Type assertion for joined data
        if (activity?.title) {
          buzz.push({
            id: `participant-${participant.created_at}`,
            type: 'user_joined',
            message: `Someone just joined "${activity.title}"`,
            timestamp: participant.created_at || new Date().toISOString(),
            relatedId: activity.id
          })
        }
      })

      return {
        data: buzz.slice(0, 3),
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting community buzz:', error)
      return {
        data: [],
        error: error as Error,
        status: 'error'
      }
    }
  }

  /**
   * Get weather alerts using OpenWeatherMap API
   */
  private async getWeatherAlerts(): Promise<ServiceResponse<WeatherAlert[]>> {
    try {
      // Default location - could be made configurable per user/festival
      const defaultLocation = 'London, UK' // TODO: Make this configurable

      const { data: alerts, error } = await weatherService.getWeatherAlerts(defaultLocation)

      if (error) {
        console.warn('Weather service error:', error)
        // Return empty array to not break dashboard
        return {
          data: [],
          error: null,
          status: 'success'
        }
      }

      return {
        data: alerts || [],
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting weather alerts:', error)
      return {
        data: [],
        error: null,
        status: 'success'
      }
    }
  }

  /**
   * Get prioritized announcements based on user relevance
   */
  private async getPrioritizedAnnouncements(userId: string): Promise<ServiceResponse<Announcement[]>> {
    try {
      const { data: announcements } = await this.client
        .from('announcements')
        .select('*')
        .eq('active', true)
        .order('priority', { ascending: false })
        .order('created_at', { ascending: false })
        .limit(5)

      // TODO: Add user-specific prioritization logic
      // For now, return announcements ordered by priority and date
      return {
        data: announcements || [],
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting prioritized announcements:', error)
      return {
        data: [],
        error: error as Error,
        status: 'error'
      }
    }
  }

  /**
   * Get upcoming event recommendations based on user preferences
   */
  private async getUpcomingRecommendations(userId: string): Promise<ServiceResponse<Event[]>> {
    try {
      const today = new Date().toISOString().split('T')[0]
      const { data: events } = await this.client
        .from('events')
        .select('*')
        .gte('start_date', today)
        .eq('status', 'PUBLISHED')
        .eq('is_active', true)
        .order('start_date', { ascending: true })
        .limit(4)

      // TODO: Add user preference-based filtering and scoring
      return {
        data: events || [],
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting upcoming recommendations:', error)
      return {
        data: [],
        error: error as Error,
        status: 'error'
      }
    }
  }

  // getUserEngagementMetrics method removed - moved to social feed approach

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  private extractUserCategories(history: any[], favorites: any[]): string[] {
    const categories = new Set<string>()
    
    history?.forEach(item => {
      if (item.activities?.category) {
        categories.add(item.activities.category)
      }
    })
    
    favorites?.forEach(item => {
      if (item.activities?.category) {
        categories.add(item.activities.category)
      }
    })
    
    return Array.from(categories)
  }

  private extractUserLocations(history: any[], favorites: any[]): string[] {
    const locations = new Set<string>()
    
    history?.forEach(item => {
      if (item.activities?.location) {
        locations.add(item.activities.location)
      }
    })
    
    favorites?.forEach(item => {
      if (item.activities?.location) {
        locations.add(item.activities.location)
      }
    })
    
    return Array.from(locations)
  }

  private scoreActivities(
    activities: Activity[],
    userCategories: string[],
    userLocations: string[],
    userHistory: any[],
    userFavorites: any[]
  ): PersonalizedContent[] {
    return activities.map(activity => {
      let score = 0
      let reasons: string[] = []

      // Category preference scoring
      if (activity.category && userCategories.includes(activity.category)) {
        score += 3
        reasons.push(`Matches your interest in ${activity.category}`)
      }

      // Location preference scoring
      if (userLocations.includes(activity.location)) {
        score += 2
        reasons.push(`Near your preferred location: ${activity.location}`)
      }

      // Recency scoring (prefer activities starting soon)
      const daysUntilStart = activity.start_date ? Math.ceil(
        (new Date(activity.start_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      ) : 999
      if (daysUntilStart <= 7) {
        score += 2
        reasons.push('Starting soon')
      }

      // Availability scoring
      const availableSpots = (activity.max_participants || activity.capacity || 0) - (activity.current_participants || 0)
      if (availableSpots > 0) {
        score += 1
        reasons.push('Spots available')
      }

      return {
        activities: [activity],
        score,
        reason: reasons.join(', ') || 'New activity for you'
      }
    }).sort((a, b) => b.score - a.score)
  }

  // calculateEngagementScore and getDefaultUserStats methods removed - moved to social feed approach
}

// Export singleton instance
export const dashboardIntelligenceService = new DashboardIntelligenceService()

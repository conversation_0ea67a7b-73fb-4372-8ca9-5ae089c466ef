/**
 * Test Users Management
 * 
 * This test verifies that the users management system works correctly
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

test('Test Users Management System', async ({ page }) => {
  console.log('🧪 Testing users management system...');
  
  const loginSuccess = await loginAsAdmin(page);
  console.log(`Login success: ${loginSuccess}`);
  
  if (loginSuccess) {
    // Navigate to users management
    await page.goto('/admin/users');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    console.log('📝 Checking users management interface...');
    
    // Check if users management elements exist
    const hasUserCards = await page.locator('.card, [data-testid*="user"]').count() > 0;
    const hasUserList = await page.locator('.user-item, .user-card').count() > 0;
    const hasDropdownMenus = await page.locator('button[aria-haspopup="menu"]').count() > 0;
    const hasRoleActions = await page.locator('button:has-text("Set as"), [role="menuitem"]').count() > 0;
    
    console.log(`Users management elements found:`);
    console.log(`  User cards: ${hasUserCards}`);
    console.log(`  User list: ${hasUserList}`);
    console.log(`  Dropdown menus: ${hasDropdownMenus}`);
    console.log(`  Role actions: ${hasRoleActions}`);
    
    await page.screenshot({ path: 'test-results/users-management.png', fullPage: true });
    
    // Test role management functionality
    if (hasDropdownMenus) {
      console.log('🔗 Testing role management functionality...');
      
      // Click first dropdown menu
      const firstDropdown = page.locator('button[aria-haspopup="menu"]').first();
      await firstDropdown.click();
      await page.waitForTimeout(1000);
      
      // Check if role options are available
      const hasUserOption = await page.locator('text="Set as User"').count() > 0;
      const hasModeratorOption = await page.locator('text="Set as Moderator"').count() > 0;
      const hasContentAdminOption = await page.locator('text="Set as Content Admin"').count() > 0;
      
      console.log(`Role management options:`);
      console.log(`  Set as User: ${hasUserOption}`);
      console.log(`  Set as Moderator: ${hasModeratorOption}`);
      console.log(`  Set as Content Admin: ${hasContentAdminOption}`);
      
      await page.screenshot({ path: 'test-results/users-management-dropdown.png', fullPage: true });
      
      // Close dropdown by clicking elsewhere
      await page.click('h1');
      await page.waitForTimeout(500);
      
      console.log('✅ Role management functionality tested');
    }
    
    // Test user information display
    console.log('📋 Testing user information display...');
    
    const userEmails = await page.locator('text=@').count();
    const userRoles = await page.locator('.badge, [class*="badge"]').count();
    const userJoinDates = await page.locator('text="Joined"').count();
    
    console.log(`User information display:`);
    console.log(`  User emails: ${userEmails}`);
    console.log(`  User roles: ${userRoles}`);
    console.log(`  Join dates: ${userJoinDates}`);
    
    console.log('✅ Users management test completed');
  }
});

/**
 * Festival Family - Recommendation Engine
 *
 * This service provides intelligent recommendations for events, festivals, and activities
 * based on user behavior, preferences, and community engagement patterns.
 *
 * @module RecommendationEngine
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { BaseService, ServiceResponse } from '../supabase/services/base-service'
import type { Event, Activity, Festival } from '@/types'

// ============================================================================
// RECOMMENDATION TYPES
// ============================================================================

export interface UserPreferences {
  categories: string[]
  locations: string[]
  timePreferences: 'morning' | 'afternoon' | 'evening' | 'any'
  groupSize: 'small' | 'medium' | 'large' | 'any'
}

export interface RecommendationScore {
  score: number
  reasons: string[]
  confidence: 'low' | 'medium' | 'high'
}

export interface EventRecommendation {
  event: Event
  score: RecommendationScore
  similarEvents: Event[]
}

export interface ActivityRecommendation {
  activity: Activity
  score: RecommendationScore
  relatedActivities: Activity[]
}

export interface RecommendationFilters {
  categories?: string[]
  locations?: string[]
  dateRange?: {
    start: string
    end: string
  }
  maxResults?: number
}

// ============================================================================
// RECOMMENDATION ENGINE SERVICE
// ============================================================================

export class RecommendationEngine extends BaseService {
  /**
   * Get personalized event recommendations for a user
   */
  async getEventRecommendations(
    userId: string,
    filters?: RecommendationFilters
  ): Promise<ServiceResponse<EventRecommendation[]>> {
    try {
      // Get user preferences from activity history
      const userPreferences = await this.getUserPreferences(userId)
      
      // Get available events
      const events = await this.getAvailableEvents(filters)
      
      if (!events.data || events.data.length === 0) {
        return {
          data: [],
          error: null,
          status: 'success'
        }
      }

      // Score and rank events
      const recommendations = await this.scoreEvents(events.data, userPreferences.data)
      
      // Get similar events for each recommendation
      const enrichedRecommendations = await Promise.all(
        recommendations.slice(0, filters?.maxResults || 10).map(async (rec) => ({
          ...rec,
          similarEvents: await this.getSimilarEvents(rec.event, events.data || [])
        }))
      )

      return {
        data: enrichedRecommendations,
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting event recommendations:', error)
      return {
        data: [],
        error: error as Error,
        status: 'error'
      }
    }
  }

  /**
   * Get personalized activity recommendations for a user
   */
  async getActivityRecommendations(
    userId: string,
    filters?: RecommendationFilters
  ): Promise<ServiceResponse<ActivityRecommendation[]>> {
    try {
      // Get user preferences from activity history
      const userPreferences = await this.getUserPreferences(userId)
      
      // Get available activities
      const activities = await this.getAvailableActivities(filters)
      
      if (!activities.data || activities.data.length === 0) {
        return {
          data: [],
          error: null,
          status: 'success'
        }
      }

      // Score and rank activities
      const recommendations = await this.scoreActivities(activities.data, userPreferences.data)
      
      // Get related activities for each recommendation
      const enrichedRecommendations = await Promise.all(
        recommendations.slice(0, filters?.maxResults || 10).map(async (rec) => ({
          ...rec,
          relatedActivities: await this.getRelatedActivities(rec.activity, activities.data || [])
        }))
      )

      return {
        data: enrichedRecommendations,
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting activity recommendations:', error)
      return {
        data: [],
        error: error as Error,
        status: 'error'
      }
    }
  }

  /**
   * Get trending events based on community engagement
   */
  async getTrendingEvents(limit: number = 5): Promise<ServiceResponse<Event[]>> {
    try {
      const { data, error } = await this.client
        .from('events')
        .select(`
          *,
          festivals:festival_id (name)
        `)
        .eq('status', 'PUBLISHED')
        .eq('is_active', true)
        .gte('start_date', new Date().toISOString())
        .order('created_at', { ascending: false })
        .limit(limit * 2) // Get more to filter and rank

      if (error) throw error

      // Calculate trending score based on recent activity
      const trendingEvents = (data || [])
        .map(event => ({
          ...event,
          trendingScore: this.calculateTrendingScore(event)
        }))
        .sort((a, b) => b.trendingScore - a.trendingScore)
        .slice(0, limit)

      return {
        data: trendingEvents,
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting trending events:', error)
      return {
        data: [],
        error: error as Error,
        status: 'error'
      }
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private async getUserPreferences(userId: string): Promise<ServiceResponse<UserPreferences | null>> {
    try {
      // Get user's activity participation history
      const { data: participationHistory } = await this.client
        .from('activity_participants')
        .select(`
          activities (category, location, start_date)
        `)
        .eq('user_id', userId)
        .eq('status', 'registered')

      // Get user's favorites
      const { data: favorites } = await this.client
        .from('user_favorites')
        .select(`
          activities (category, location)
        `)
        .eq('user_id', userId)

      // Extract preferences from user behavior
      const allActivities = [
        ...(participationHistory || []).map(p => p.activities as any),
        ...(favorites || []).map(f => f.activities as any)
      ].filter(Boolean)

      const categories = [...new Set(allActivities.map(a => a?.category).filter(Boolean))]
      const locations = [...new Set(allActivities.map(a => a?.location).filter(Boolean))]

      const preferences: UserPreferences = {
        categories,
        locations,
        timePreferences: 'any', // TODO: Analyze time patterns
        groupSize: 'any' // TODO: Analyze group size preferences
      }

      return {
        data: preferences,
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting user preferences:', error)
      return {
        data: null,
        error: error as Error,
        status: 'error'
      }
    }
  }

  private async getAvailableEvents(filters?: RecommendationFilters): Promise<ServiceResponse<Event[]>> {
    try {
      let query = this.client
        .from('events')
        .select(`
          *,
          festivals:festival_id (name)
        `)
        .eq('status', 'PUBLISHED')
        .gte('start_date', new Date().toISOString())

      if (filters?.categories && filters.categories.length > 0) {
        query = query.in('category', filters.categories)
      }

      if (filters?.locations && filters.locations.length > 0) {
        query = query.in('location', filters.locations)
      }

      if (filters?.dateRange) {
        query = query
          .gte('start_date', filters.dateRange.start)
          .lte('start_date', filters.dateRange.end)
      }

      const { data, error } = await query.order('start_date', { ascending: true })

      if (error) throw error

      return {
        data: data || [],
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting available events:', error)
      return {
        data: [],
        error: error as Error,
        status: 'error'
      }
    }
  }

  private async getAvailableActivities(filters?: RecommendationFilters): Promise<ServiceResponse<Activity[]>> {
    try {
      let query = this.client
        .from('activities')
        .select('*')
        .eq('status', 'published')
        .gte('start_date', new Date().toISOString())

      if (filters?.categories && filters.categories.length > 0) {
        query = query.in('category', filters.categories)
      }

      if (filters?.locations && filters.locations.length > 0) {
        query = query.in('location', filters.locations)
      }

      if (filters?.dateRange) {
        query = query
          .gte('start_date', filters.dateRange.start)
          .lte('start_date', filters.dateRange.end)
      }

      const { data, error } = await query.order('start_date', { ascending: true })

      if (error) throw error

      return {
        data: data || [],
        error: null,
        status: 'success'
      }
    } catch (error) {
      console.error('Error getting available activities:', error)
      return {
        data: [],
        error: error as Error,
        status: 'error'
      }
    }
  }

  private async scoreEvents(events: Event[], preferences: UserPreferences | null): Promise<EventRecommendation[]> {
    return events.map(event => {
      let score = 0
      const reasons: string[] = []

      // Base score for all events
      score += 1

      // Category preference scoring
      if (event.category && preferences?.categories.includes(event.category)) {
        score += 3
        reasons.push(`Matches your interest in ${event.category}`)
      }

      // Location preference scoring
      if (preferences?.locations.includes(event.location)) {
        score += 2
        reasons.push(`Near your preferred location: ${event.location}`)
      }

      // Recency scoring (prefer events starting soon)
      const daysUntilStart = Math.ceil(
        (new Date(event.start_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      )
      if (daysUntilStart <= 7) {
        score += 2
        reasons.push('Starting soon')
      } else if (daysUntilStart <= 30) {
        score += 1
        reasons.push('Coming up this month')
      }

      // Determine confidence based on number of matching factors
      let confidence: 'low' | 'medium' | 'high' = 'low'
      if (reasons.length >= 3) confidence = 'high'
      else if (reasons.length >= 2) confidence = 'medium'

      return {
        event,
        score: {
          score,
          reasons: reasons.length > 0 ? reasons : ['New event for you'],
          confidence
        },
        similarEvents: [] // Will be populated later
      }
    }).sort((a, b) => b.score.score - a.score.score)
  }

  private async scoreActivities(activities: Activity[], preferences: UserPreferences | null): Promise<ActivityRecommendation[]> {
    return activities.map(activity => {
      let score = 0
      const reasons: string[] = []

      // Base score for all activities
      score += 1

      // Category preference scoring
      if (activity.category && preferences?.categories.includes(activity.category)) {
        score += 3
        reasons.push(`Matches your interest in ${activity.category}`)
      }

      // Location preference scoring
      if (preferences?.locations.includes(activity.location)) {
        score += 2
        reasons.push(`Near your preferred location: ${activity.location}`)
      }

      // Availability scoring
      const availableSpots = (activity.max_participants || activity.capacity || 0) - (activity.current_participants || 0)
      if (availableSpots > 0) {
        score += 1
        reasons.push('Spots available')
      }

      // Recency scoring
      const daysUntilStart = activity.start_date ? Math.ceil(
        (new Date(activity.start_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      ) : 999
      if (daysUntilStart <= 3) {
        score += 2
        reasons.push('Starting very soon')
      } else if (daysUntilStart <= 7) {
        score += 1
        reasons.push('Starting this week')
      }

      // Determine confidence
      let confidence: 'low' | 'medium' | 'high' = 'low'
      if (reasons.length >= 3) confidence = 'high'
      else if (reasons.length >= 2) confidence = 'medium'

      return {
        activity,
        score: {
          score,
          reasons: reasons.length > 0 ? reasons : ['New activity for you'],
          confidence
        },
        relatedActivities: [] // Will be populated later
      }
    }).sort((a, b) => b.score.score - a.score.score)
  }

  private async getSimilarEvents(event: Event, allEvents: Event[]): Promise<Event[]> {
    return allEvents
      .filter(e => e.id !== event.id)
      .filter(e => (e.category && event.category && e.category === event.category) || e.location === event.location)
      .slice(0, 3)
  }

  private async getRelatedActivities(activity: Activity, allActivities: Activity[]): Promise<Activity[]> {
    return allActivities
      .filter(a => a.id !== activity.id)
      .filter(a => (a.category && activity.category && a.category === activity.category) || a.location === activity.location)
      .slice(0, 3)
  }

  private calculateTrendingScore(event: any): number {
    let score = 0
    
    // Recent creation bonus
    const daysSinceCreation = Math.ceil(
      (new Date().getTime() - new Date(event.created_at).getTime()) / (1000 * 60 * 60 * 24)
    )
    if (daysSinceCreation <= 7) score += 5
    else if (daysSinceCreation <= 30) score += 2

    // Featured event bonus (since we don't have participant count)
    if (event.is_featured) {
      score += 10
    }

    return score
  }
}

// Export singleton instance
export const recommendationEngine = new RecommendationEngine()

import { useState, useEffect, useCallback } from 'react';
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';

/**
 * Enhanced Color Mapping Hook
 * Provides easy access to database-driven color mappings with caching
 */

interface ColorMapping {
  id: string;
  content_type: string;
  category: string;
  color_primary: string;
  color_secondary: string;
  color_accent: string;
  emoji_icon: string | null;
  description: string | null;
  show_icon: boolean | null;
  admin_configurable: boolean | null;
}

interface UseEnhancedColorMappingResult {
  colorMapping: ColorMapping | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

/**
 * Hook for getting a specific color mapping
 */
export const useEnhancedColorMapping = (
  contentType: string | null,
  category: string = 'main'
): UseEnhancedColorMappingResult => {
  const [colorMapping, setColorMapping] = useState<ColorMapping | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchColorMapping = useCallback(async () => {
    if (!contentType) {
      setColorMapping(null);
      setIsLoading(false);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const mapping = await enhancedColorMappingService.getColorMapping(contentType, category);
      setColorMapping(mapping);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch color mapping';
      setError(errorMessage);
      console.warn(`Failed to load color mapping for ${contentType}.${category}:`, err);
    } finally {
      setIsLoading(false);
    }
  }, [contentType, category]);

  useEffect(() => {
    fetchColorMapping();
  }, [fetchColorMapping]);

  return {
    colorMapping,
    isLoading,
    error,
    refetch: fetchColorMapping
  };
};

/**
 * Hook for getting all color mappings for a content type
 */
export const useContentTypeColorMappings = (
  contentType: string | null
): {
  colorMappings: ColorMapping[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
} => {
  const [colorMappings, setColorMappings] = useState<ColorMapping[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchColorMappings = useCallback(async () => {
    if (!contentType) {
      setColorMappings([]);
      setIsLoading(false);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const mappings = await enhancedColorMappingService.getContentTypeColorMappings(contentType);
      setColorMappings(mappings);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch color mappings';
      setError(errorMessage);
      console.warn(`Failed to load color mappings for ${contentType}:`, err);
    } finally {
      setIsLoading(false);
    }
  }, [contentType]);

  useEffect(() => {
    fetchColorMappings();
  }, [fetchColorMappings]);

  return {
    colorMappings,
    isLoading,
    error,
    refetch: fetchColorMappings
  };
};

/**
 * Hook for getting activity type colors and styling
 */
export const useActivityTypeColors = (activityType: string | null) => {
  const { colorMapping, isLoading, error } = useEnhancedColorMapping(
    activityType ? 'activities' : null,
    activityType?.toLowerCase() || 'main'
  );

  const getGradientClasses = useCallback((opacity: 'light' | 'medium' | 'strong' = 'medium') => {
    if (!colorMapping) {
      return 'from-muted/20 to-muted/30'; // Fallback
    }

    const opacityMap = {
      light: '10',
      medium: '20',
      strong: '30'
    };

    // Convert hex to CSS custom property format for gradients
    const primary = colorMapping.color_primary;
    const secondary = colorMapping.color_secondary;
    
    return `from-[${primary}/${opacityMap[opacity]}] to-[${secondary}/${opacityMap[opacity]}]`;
  }, [colorMapping]);

  const getBorderClasses = useCallback(() => {
    if (!colorMapping) {
      return 'border-muted/30'; // Fallback
    }

    return `border-[${colorMapping.color_primary}/30]`;
  }, [colorMapping]);

  const getTextClasses = useCallback(() => {
    if (!colorMapping) {
      return 'text-muted-foreground'; // Fallback
    }

    return `text-[${colorMapping.color_primary}]`;
  }, [colorMapping]);

  return {
    colorMapping,
    isLoading,
    error,
    gradientClasses: getGradientClasses,
    borderClasses: getBorderClasses,
    textClasses: getTextClasses,
    primaryColor: colorMapping?.color_primary,
    secondaryColor: colorMapping?.color_secondary,
    accentColor: colorMapping?.color_accent,
    emoji: colorMapping?.emoji_icon
  };
};

/**
 * Hook for getting content status colors
 */
export const useContentStatusColors = (status: string | null) => {
  // Map common statuses to categories
  const getStatusCategory = (status: string | null): string => {
    if (!status) return 'default';
    
    const statusMap: { [key: string]: string } = {
      'published': 'published',
      'active': 'active',
      'featured': 'featured',
      'trending': 'trending',
      'new': 'new',
      'popular': 'popular',
      'draft': 'draft',
      'archived': 'archived',
      'pending': 'pending'
    };

    return statusMap[status.toLowerCase()] || 'default';
  };

  const { colorMapping, isLoading, error } = useEnhancedColorMapping(
    status ? 'status' : null,
    getStatusCategory(status)
  );

  return {
    colorMapping,
    isLoading,
    error,
    primaryColor: colorMapping?.color_primary,
    secondaryColor: colorMapping?.color_secondary,
    accentColor: colorMapping?.color_accent,
    emoji: colorMapping?.emoji_icon
  };
};

/**
 * Utility function to get CSS custom properties from color mapping
 */
export const getColorMappingCSSProperties = (colorMapping: ColorMapping | null) => {
  if (!colorMapping) {
    return {};
  }

  return {
    '--color-primary': colorMapping.color_primary,
    '--color-secondary': colorMapping.color_secondary,
    '--color-accent': colorMapping.color_accent,
  } as React.CSSProperties;
};

/**
 * Hook for getting festival-specific colors
 */
export const useFestivalColors = (festivalName: string | null) => {
  const { colorMapping, isLoading, error } = useEnhancedColorMapping(
    festivalName ? 'festivals' : null,
    festivalName?.toLowerCase().replace(/\s+/g, '_') || 'main'
  );

  return {
    colorMapping,
    isLoading,
    error,
    primaryColor: colorMapping?.color_primary,
    secondaryColor: colorMapping?.color_secondary,
    accentColor: colorMapping?.color_accent,
    emoji: colorMapping?.emoji_icon
  };
};

/**
 * Personalization Hook - Simple Content Recommendation Engine
 * 
 * Provides personalized content recommendations based on user interactions
 * without over-engineering. Uses the unified tracking system for data.
 * 
 * @module usePersonalization
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/providers/ConsolidatedAuthProvider';
import { useUnifiedTracking } from './useUnifiedTracking';
import { unifiedDataService } from '@/lib/data/unified-data-service';
import type { Activity, Event, Festival, Tip, Guide } from '@/lib/data/unified-data-service';

// Personalization types
export interface PersonalizedContent<T> {
  item: T;
  score: number;
  reason: string;
}

export interface PersonalizationMetrics {
  totalInteractions: number;
  favoriteCategories: string[];
  recentActivity: string[];
  engagementScore: number;
}

interface UsePersonalizationReturn {
  // Personalized content
  getPersonalizedActivities: (limit?: number) => Promise<PersonalizedContent<Activity>[]>;
  getPersonalizedEvents: (limit?: number) => Promise<PersonalizedContent<Event>[]>;
  getPersonalizedFestivals: (limit?: number) => Promise<PersonalizedContent<Festival>[]>;
  getPersonalizedTips: (limit?: number) => Promise<PersonalizedContent<Tip>[]>;
  getPersonalizedGuides: (limit?: number) => Promise<PersonalizedContent<Guide>[]>;
  
  // User metrics
  getPersonalizationMetrics: () => Promise<PersonalizationMetrics>;
  
  // Content ordering
  sortByPersonalization: <T extends { id: string; category?: string; tags?: string[] }>(
    items: T[], 
    contentType: string
  ) => Promise<T[]>;
  
  // State
  loading: boolean;
  error: string | null;
}

export function usePersonalization(): UsePersonalizationReturn {
  const { user } = useAuth();
  const { getActivityHistory, getSuggestions } = useUnifiedTracking();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Calculate content score based on user interactions
  const calculateContentScore = useCallback(async (
    contentId: string,
    contentType: string,
    category?: string,
    tags?: string[]
  ): Promise<{ score: number; reason: string }> => {
    if (!user) {
      return { score: 0, reason: 'Not logged in' };
    }

    try {
      // Get user's interaction history
      const history = await getActivityHistory(contentType, 50);
      
      let score = 0;
      const reasons: string[] = [];

      // Base score for content popularity (fallback)
      score += 1;
      reasons.push('Popular content');

      // Score based on user's past interactions
      const userInteractions = history.filter(h => h.target_type === contentType);
      
      if (userInteractions.length > 0) {
        // User has interacted with this content type before
        score += 2;
        reasons.push(`You've engaged with ${contentType}s before`);

        // Check for category preferences
        if (category) {
          const categoryInteractions = userInteractions.filter(h => 
            h.metadata?.category === category
          );
          if (categoryInteractions.length > 0) {
            score += 3;
            reasons.push(`You like ${category} content`);
          }
        }

        // Check for tag preferences
        if (tags && tags.length > 0) {
          const tagMatches = userInteractions.filter(h => 
            h.metadata?.tags && 
            tags.some(tag => h.metadata?.tags?.includes(tag))
          );
          if (tagMatches.length > 0) {
            score += 2;
            reasons.push('Matches your interests');
          }
        }

        // Boost score for recent activity
        const recentInteractions = userInteractions.filter(h => 
          new Date(h.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        );
        if (recentInteractions.length > 0) {
          score += 1;
          reasons.push('Recent activity');
        }

        // Boost score for favorites
        const favorites = userInteractions.filter(h => h.activity_type === 'favorite');
        if (favorites.length > 0) {
          score += 4;
          reasons.push('Similar to your favorites');
        }
      }

      return { 
        score, 
        reason: reasons.length > 1 ? reasons.slice(0, 2).join(', ') : reasons[0] || 'Recommended'
      };
    } catch (err) {
      console.error('Error calculating content score:', err);
      return { score: 1, reason: 'Popular content' };
    }
  }, [user, getActivityHistory]);

  // Get personalized activities
  const getPersonalizedActivities = useCallback(async (limit = 10): Promise<PersonalizedContent<Activity>[]> => {
    if (!user) return [];
    
    setLoading(true);
    setError(null);

    try {
      const activities = await unifiedDataService.getActivities();

      const scoredActivities = await Promise.all(
        activities.map(async (activity) => {
          const { score, reason } = await calculateContentScore(
            activity.id,
            'activity',
            activity.type || undefined, // Use type as category fallback since category field doesn't exist
            activity.tags || undefined
          );
          return { item: activity, score, reason };
        })
      );

      return scoredActivities
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);
    } catch (err) {
      setError('Failed to get personalized activities');
      return [];
    } finally {
      setLoading(false);
    }
  }, [user, calculateContentScore]);

  // Get personalized events
  const getPersonalizedEvents = useCallback(async (limit = 10): Promise<PersonalizedContent<Event>[]> => {
    if (!user) return [];
    
    setLoading(true);
    setError(null);

    try {
      const events = await unifiedDataService.getEvents();

      const scoredEvents = await Promise.all(
        events.map(async (event) => {
          const { score, reason } = await calculateContentScore(
            event.id,
            'event',
            event.category || undefined,
            undefined // Events table doesn't have tags field
          );
          return { item: event, score, reason };
        })
      );

      return scoredEvents
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);
    } catch (err) {
      setError('Failed to get personalized events');
      return [];
    } finally {
      setLoading(false);
    }
  }, [user, calculateContentScore]);

  // Get personalized festivals
  const getPersonalizedFestivals = useCallback(async (limit = 10): Promise<PersonalizedContent<Festival>[]> => {
    if (!user) return [];
    
    setLoading(true);
    setError(null);

    try {
      const festivals = await unifiedDataService.getFestivals();

      const scoredFestivals = await Promise.all(
        festivals.map(async (festival) => {
          const { score, reason } = await calculateContentScore(
            festival.id,
            'festival',
            undefined, // Festivals table doesn't have genre field
            undefined  // Festivals table doesn't have tags field
          );
          return { item: festival, score, reason };
        })
      );

      return scoredFestivals
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);
    } catch (err) {
      setError('Failed to get personalized festivals');
      return [];
    } finally {
      setLoading(false);
    }
  }, [user, calculateContentScore]);

  // Get personalized tips
  const getPersonalizedTips = useCallback(async (limit = 10): Promise<PersonalizedContent<Tip>[]> => {
    if (!user) return [];
    
    setLoading(true);
    setError(null);

    try {
      const tips = await unifiedDataService.getTips();

      const scoredTips = await Promise.all(
        tips.map(async (tip) => {
          const { score, reason } = await calculateContentScore(
            tip.id,
            'tip',
            tip.category || undefined,
            undefined // Tips don't have tags field
          );
          return { item: tip, score, reason };
        })
      );

      return scoredTips
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);
    } catch (err) {
      setError('Failed to get personalized tips');
      return [];
    } finally {
      setLoading(false);
    }
  }, [user, calculateContentScore]);

  // Get personalized guides
  const getPersonalizedGuides = useCallback(async (limit = 10): Promise<PersonalizedContent<Guide>[]> => {
    if (!user) return [];
    
    setLoading(true);
    setError(null);

    try {
      const guides = await unifiedDataService.getGuides();

      const scoredGuides = await Promise.all(
        guides.map(async (guide) => {
          const { score, reason } = await calculateContentScore(
            guide.id,
            'guide',
            guide.category || undefined,
            undefined // Guides don't have tags field
          );
          return { item: guide, score, reason };
        })
      );

      return scoredGuides
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);
    } catch (err) {
      setError('Failed to get personalized guides');
      return [];
    } finally {
      setLoading(false);
    }
  }, [user, calculateContentScore]);

  // Get personalization metrics
  const getPersonalizationMetrics = useCallback(async (): Promise<PersonalizationMetrics> => {
    if (!user) {
      return {
        totalInteractions: 0,
        favoriteCategories: [],
        recentActivity: [],
        engagementScore: 0
      };
    }

    try {
      const history = await getActivityHistory(undefined, 100);
      
      const totalInteractions = history.length;
      
      // Calculate favorite categories
      const categoryCount: Record<string, number> = {};
      history.forEach(h => {
        if (h.metadata?.category) {
          categoryCount[h.metadata.category] = (categoryCount[h.metadata.category] || 0) + 1;
        }
      });
      
      const favoriteCategories = Object.entries(categoryCount)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3)
        .map(([category]) => category);

      // Get recent activity
      const recentActivity = history
        .filter(h => new Date(h.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
        .map(h => h.target_type)
        .slice(0, 5);

      // Calculate engagement score
      const engagementScore = Math.min(100, (totalInteractions / 10) * 10);

      return {
        totalInteractions,
        favoriteCategories,
        recentActivity,
        engagementScore
      };
    } catch (err) {
      console.error('Error getting personalization metrics:', err);
      return {
        totalInteractions: 0,
        favoriteCategories: [],
        recentActivity: [],
        engagementScore: 0
      };
    }
  }, [user, getActivityHistory]);

  // Sort content by personalization
  const sortByPersonalization = useCallback(async <T extends { id: string; category?: string; tags?: string[] }>(
    items: T[], 
    contentType: string
  ): Promise<T[]> => {
    if (!user || items.length === 0) return items;

    try {
      const scoredItems = await Promise.all(
        items.map(async (item) => {
          const { score } = await calculateContentScore(
            item.id,
            contentType,
            item.category,
            item.tags
          );
          return { item, score };
        })
      );

      return scoredItems
        .sort((a, b) => b.score - a.score)
        .map(({ item }) => item);
    } catch (err) {
      console.error('Error sorting by personalization:', err);
      return items;
    }
  }, [user, calculateContentScore]);

  return {
    getPersonalizedActivities,
    getPersonalizedEvents,
    getPersonalizedFestivals,
    getPersonalizedTips,
    getPersonalizedGuides,
    getPersonalizationMetrics,
    sortByPersonalization,
    loading,
    error
  };
}

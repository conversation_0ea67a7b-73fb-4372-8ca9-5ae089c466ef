/**
 * Test Activity Form After Schema Fix
 * 
 * This test verifies that the activity form now works with the correct database schema
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

test('Test Fixed Activity Form Submission', async ({ page }) => {
  console.log('🧪 Testing fixed activity form submission...');
  
  // Capture console logs and network requests
  const consoleLogs = [];
  const networkRequests = [];
  
  page.on('console', msg => {
    consoleLogs.push(`${msg.type()}: ${msg.text()}`);
  });
  
  page.on('request', request => {
    if (request.url().includes('supabase') && request.method() === 'POST') {
      networkRequests.push({
        method: request.method(),
        url: request.url(),
        postData: request.postData()
      });
    }
  });
  
  const loginSuccess = await loginAsAdmin(page);
  console.log(`Login success: ${loginSuccess}`);
  
  if (loginSuccess) {
    // Navigate to activity form
    await page.goto('/admin/activities/new');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    console.log('📝 Filling out activity form with correct schema...');
    
    // Fill out the form with test data matching the actual database schema
    const testData = {
      title: 'Schema Fixed Test Activity ' + Date.now(),
      description: 'This activity tests the fixed database schema with proper field mapping.',
      type: 'workshop',
      location: 'Test Workshop Location'
    };
    
    // Fill form fields
    await page.fill('input[name="title"]', testData.title);
    await page.fill('textarea[name="description"]', testData.description);
    await page.selectOption('select[name="type"]', testData.type);
    await page.fill('input[name="location"]', testData.location);
    
    // Optional: Add start date
    const startDate = new Date();
    startDate.setDate(startDate.getDate() + 7); // Next week
    const startDateString = startDate.toISOString().slice(0, 16); // Format for datetime-local
    await page.fill('input[name="start_date"]', startDateString);
    
    await page.screenshot({ path: 'test-results/activity-form-fixed-filled.png', fullPage: true });
    
    console.log('✅ Form filled with correct schema data');
    
    // Clear previous network logs
    networkRequests.length = 0;
    
    // Submit the form
    console.log('🚀 Submitting form...');
    await page.click('button[type="submit"]');
    
    // Wait for submission to complete
    await page.waitForTimeout(5000);
    
    const currentUrl = page.url();
    console.log(`Current URL after submission: ${currentUrl}`);
    
    await page.screenshot({ path: 'test-results/activity-form-fixed-submitted.png', fullPage: true });
    
    // Check for success indicators
    const hasSuccessToast = await page.locator('.toast, [data-testid*="toast"], [class*="toast"]').count() > 0;
    const wasRedirected = currentUrl.includes('/admin/activities') && !currentUrl.includes('/new');
    const hasErrorMessage = await page.locator('text="Error", text="Failed"').count() > 0;
    
    console.log(`Has success toast: ${hasSuccessToast}`);
    console.log(`Was redirected: ${wasRedirected}`);
    console.log(`Has error message: ${hasErrorMessage}`);
    
    // Print network activity
    console.log('\n🌐 Network Requests:');
    networkRequests.forEach((req, i) => {
      console.log(`  ${i + 1}. ${req.method} ${req.url}`);
      if (req.postData) {
        console.log(`     Data: ${req.postData.substring(0, 200)}...`);
      }
    });
    
    // Print relevant console logs
    console.log('\n📋 Console logs (errors and important messages):');
    consoleLogs.filter(log => 
      log.includes('error') || 
      log.includes('Error') || 
      log.includes('saving') || 
      log.includes('submit') ||
      log.includes('supabase') ||
      log.includes('activity')
    ).forEach(log => console.log(`  ${log}`));
    
    // Check if we can see the created activity
    if (wasRedirected) {
      console.log('🔍 Checking if activity was created...');
      await page.waitForTimeout(2000);
      
      const activityVisible = await page.locator(`text="${testData.title}"`).count() > 0;
      console.log(`Activity visible in list: ${activityVisible}`);
      
      if (activityVisible) {
        console.log('✅ SUCCESS: Activity was created and is visible');
      } else {
        console.log('❌ ISSUE: Activity was not found in the list');
      }
    }
    
    // Final assessment
    const formWorking = wasRedirected && !hasErrorMessage;
    console.log(`\n🎯 FINAL RESULT: Form submission ${formWorking ? 'SUCCESSFUL' : 'FAILED'}`);
    
    if (formWorking) {
      console.log('🎉 Activity form is now working with correct database schema!');
    } else {
      console.log('❌ Activity form still has issues that need investigation');
    }
  }
});

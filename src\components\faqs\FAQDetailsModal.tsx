import React from 'react';
import { HelpCircle, Tag, Star, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ResponsiveModal } from '@/components/design-system/ResponsiveModal';
import { useEnhancedColorMapping } from '@/hooks/useEnhancedColorMapping';
import { UnifiedInteractionButton } from '@/components/design-system';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  created_at: string;
}

interface FAQDetailsModalProps {
  faq: FAQ | null;
  isOpen: boolean;
  onClose: () => void;
}

export const FAQDetailsModal: React.FC<FAQDetailsModalProps> = ({
  faq,
  isOpen,
  onClose,
}) => {
  // Use enhanced color mapping for database-driven colors
  const { colorMapping } = useEnhancedColorMapping(faq?.category ?? 'GENERAL', 'faq');

  // Generate gradient classes from color mapping
  const getGradientClasses = () => {
    if (!colorMapping) {
      return 'from-muted to-muted-foreground'; // Fallback
    }
    return `from-[${colorMapping.color_primary}/80] to-[${colorMapping.color_secondary}/80]`;
  };

  if (!faq) return null;

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'GENERAL': return 'General Questions';
      case 'ACCOUNT': return 'Account & Profile';
      case 'ACTIVITIES': return 'Activities & Events';
      case 'SAFETY': return 'Safety & Security';
      case 'TECHNICAL': return 'Technical Support';
      case 'BILLING': return 'Billing & Payments';
      case 'PRIVACY': return 'Privacy & Data';
      default: return category;
    }
  };

  const colorClass = getGradientClasses(); // Use database-driven gradient

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={onClose}
      size="xl"
      showCloseButton={false}
    >
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center gap-3 p-6 border-b border-border">
          <div className={`w-10 h-10 bg-gradient-to-br ${colorClass} rounded-lg flex items-center justify-center`}>
            <HelpCircle className="w-5 h-5 text-primary-foreground" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-foreground">FAQ</h2>
            {faq.category && (
              <Badge variant="secondary" className="mt-1">
                {getCategoryLabel(faq.category)}
              </Badge>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
                {/* Question */}
                <div className="bg-muted/20 rounded-lg p-4 border-l-4 border-primary">
                  <h3 className="text-lg font-semibold text-foreground mb-2">Question</h3>
                  <p className="text-foreground">{faq.question}</p>
                </div>

                {/* Answer */}
                <div className="bg-muted/20 rounded-lg p-4 border-l-4 border-festival-success">
                  <h3 className="text-lg font-semibold text-foreground mb-2">Answer</h3>
                  <div className="prose prose-foreground max-w-none">
                    <p className="text-foreground leading-relaxed whitespace-pre-wrap">{faq.answer}</p>
                  </div>
                </div>

                {/* Tags */}
                {faq.tags && faq.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {faq.tags.map((tag) => (
                      <Badge key={tag} variant="outline">
                        <Tag className="w-3 h-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}

                {/* Stats */}
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  {faq.view_count !== undefined && (
                    <span className="flex items-center gap-1">
                      <HelpCircle className="w-4 h-4" />
                      {faq.view_count} views
                    </span>
                  )}
                  {faq.helpful_count !== undefined && (
                    <span className="flex items-center gap-1">
                      <Heart className="w-4 h-4" />
                      {faq.helpful_count} helpful
                    </span>
                  )}
                  <span>
                    Updated {new Date(faq.created_at).toLocaleDateString()}
                  </span>
                </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-border">
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              <UnifiedInteractionButton
                type="helpful"
                itemId={faq.id}
                itemType="faq"
                variant="outline"
                size="sm"
              />
              <UnifiedInteractionButton
                type="share"
                itemId={faq.id}
                itemType="faq"
                variant="outline"
                size="sm"
              />
              <Button
                variant="outline"
                onClick={onClose}
              >
                Close
              </Button>
            </div>
            {faq.is_featured && (
              <Badge variant="outline" className="bg-accent/20 text-accent border-accent/30">
                <Star className="w-3 h-3 mr-1" />
                Featured FAQ
              </Badge>
            )}
          </div>
        </div>
      </div>
    </ResponsiveModal>
  );
};

export default FAQDetailsModal;

import React, { useState, useEffect } from 'react';
import { HelpCircle, Tag, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { UnifiedModal } from '@/components/design-system/UnifiedModal';
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';
import { UnifiedInteractionButton } from '@/components/design-system';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  created_at: string;
}

interface FAQDetailsModalProps {
  faq: FAQ | null;
  isOpen: boolean;
  onClose: () => void;
}

export const FAQDetailsModal: React.FC<FAQDetailsModalProps> = ({
  faq,
  isOpen,
  onClose,
}) => {
  // Modern color system using enhancedColorMappingService
  const [colorTheme, setColorTheme] = useState<{
    className: string;
    style: React.CSSProperties;
  }>({ className: '', style: {} });
  const [isLoadingTheme, setIsLoadingTheme] = useState(false);

  // Load color theme using enhancedColorMappingService
  useEffect(() => {
    const loadColorTheme = async () => {
      if (!faq || !isOpen) return;

      setIsLoadingTheme(true);
      try {
        const theme = await enhancedColorMappingService.getEnhancedColorTheme(
          'faq',
          faq.category ?? 'GENERAL',
          0 // Use first fallback theme
        );
        setColorTheme(theme);
      } catch (error) {
        console.warn('Failed to load FAQ color theme:', error);
        // Fallback theme using design tokens
        setColorTheme({
          className: 'bg-gradient-to-br from-green-50 to-emerald-50',
          style: {
            background: 'var(--festival-gradient-accent)',
            borderColor: 'var(--festival-border)'
          }
        });
      } finally {
        setIsLoadingTheme(false);
      }
    };

    loadColorTheme();
  }, [faq, isOpen]);

  if (!faq) return null;

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'GENERAL': return 'General Questions';
      case 'ACCOUNT': return 'Account & Profile';
      case 'ACTIVITIES': return 'Activities & Events';
      case 'SAFETY': return 'Safety & Security';
      case 'TECHNICAL': return 'Technical Support';
      case 'BILLING': return 'Billing & Payments';
      case 'PRIVACY': return 'Privacy & Data';
      default: return category;
    }
  };

  return (
    <UnifiedModal
      open={isOpen}
      onClose={onClose}
      title={faq?.question || 'FAQ Details'}
      itemId={faq?.id || 'faq'}
      contentType="faqs"
      category={faq?.category || 'general'}
      itemType="faq"
      size="xl"
      showCloseButton={true}
    >
      <div className="h-full flex flex-col">
        {/* Enhanced Header with Theme-Aware Design Token Integration */}
        <div
          className="p-4 sm:p-6 pb-2 festival-component"
          style={{
            background: !isLoadingTheme ? colorTheme.style.background : 'var(--light-effect-accent)',
            borderBottom: '1px solid var(--festival-border)',
            transition: 'var(--festival-transition)'
          }}
        >
          {/* Theme-Aware Loading State Indicator */}
          {isLoadingTheme && (
            <div
              className="absolute top-2 right-2 w-4 h-4 rounded-full animate-pulse"
              style={{
                background: 'var(--festival-success)',
                opacity: 0.6
              }}
            />
          )}

          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div
                className="w-10 h-10 flex items-center justify-center layered-effect"
                style={{
                  background: !isLoadingTheme && colorTheme.style.background
                    ? colorTheme.style.background
                    : 'var(--light-effect-accent)',
                  color: 'var(--festival-text-auto)',
                  borderRadius: 'var(--festival-radius)',
                  boxShadow: 'var(--layer-shadow-sm)'
                }}
              >
                <HelpCircle className="w-5 h-5" />
              </div>
              <div>
                <h2
                  className="text-lg sm:text-xl font-bold pr-8"
                  style={{
                    color: 'var(--festival-text-auto)',
                    transition: 'var(--festival-transition)'
                  }}
                >
                  FAQ
                </h2>
                {faq.category && (
                  <Badge
                    variant="secondary"
                    className="mt-2 text-xs festival-component"
                    style={{
                      backgroundColor: 'var(--festival-bg-muted)',
                      color: 'var(--festival-text-muted)',
                      border: '1px solid var(--festival-border)',
                      transition: 'var(--festival-transition)'
                    }}
                  >
                    {getCategoryLabel(faq.category)}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Enhanced Featured Badge with Theme-Aware Design Tokens */}
          {faq.is_featured && (
            <Badge
              className="w-fit mt-2 text-xs festival-component hover-glow"
              style={{
                backgroundColor: 'var(--festival-warning-bg)',
                color: 'var(--festival-warning-text)',
                border: '1px solid var(--festival-warning)',
                boxShadow: 'var(--layer-shadow-sm)',
                transition: 'var(--festival-transition)'
              }}
            >
              ❓ Featured FAQ
            </Badge>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
                {/* Question */}
                <div className="bg-muted/20 rounded-lg p-4 border-l-4 border-primary">
                  <h3 className="text-lg font-semibold text-foreground mb-2">Question</h3>
                  <p className="text-foreground">{faq.question}</p>
                </div>

                {/* Answer */}
                <div className="bg-muted/20 rounded-lg p-4 border-l-4 border-festival-success">
                  <h3 className="text-lg font-semibold text-foreground mb-2">Answer</h3>
                  <div className="prose prose-foreground max-w-none">
                    <p className="text-foreground leading-relaxed whitespace-pre-wrap">{faq.answer}</p>
                  </div>
                </div>

                {/* Tags */}
                {faq.tags && faq.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {faq.tags.map((tag) => (
                      <Badge key={tag} variant="outline">
                        <Tag className="w-3 h-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}

                {/* Stats */}
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  {faq.view_count !== undefined && (
                    <span className="flex items-center gap-1">
                      <HelpCircle className="w-4 h-4" />
                      {faq.view_count} views
                    </span>
                  )}
                  {faq.helpful_count !== undefined && (
                    <span className="flex items-center gap-1">
                      <Heart className="w-4 h-4" />
                      {faq.helpful_count} helpful
                    </span>
                  )}
                  <span>
                    Updated {new Date(faq.created_at).toLocaleDateString()}
                  </span>
                </div>
        </div>

        {/* Enhanced Footer with Advanced Design Token Integration */}
        <div
          className="flex flex-col sm:flex-row gap-2 sm:gap-3 p-4 sm:p-6 pt-2 festival-component"
          style={{
            borderTop: '1px solid var(--festival-border)',
            backgroundColor: 'var(--festival-bg-muted)',
            backdropFilter: 'var(--glassmorphism-backdrop)',
            transition: 'var(--festival-transition)'
          }}
        >
          {/* Enhanced Action buttons with mobile-first responsive design */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
            <UnifiedInteractionButton
              type="helpful"
              itemId={faq.id}
              itemType="faq"
              variant="outline"
              className="w-full sm:w-auto min-w-[120px] festival-component hover-lift"
              size="default"
            />
            <UnifiedInteractionButton
              type="share"
              itemId={faq.id}
              itemType="faq"
              variant="outline"
              className="w-full sm:w-auto min-w-[120px] festival-component hover-lift"
              size="default"
            />
            <Button
              variant="outline"
              onClick={onClose}
              className="w-full sm:w-auto min-w-[120px] festival-component hover-lift"
              style={{
                transition: 'var(--festival-transition)',
                borderColor: 'var(--festival-border)',
                backgroundColor: 'var(--festival-bg-card)',
                color: 'var(--festival-text-auto)'
              }}
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </UnifiedModal>
  );
};

export default FAQDetailsModal;

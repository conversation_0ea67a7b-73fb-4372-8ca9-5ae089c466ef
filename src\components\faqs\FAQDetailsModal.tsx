import React, { useState, useEffect } from 'react';
import { HelpCircle, Tag, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ResponsiveModal } from '@/components/design-system/ResponsiveModal';
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';
import { UnifiedInteractionButton } from '@/components/design-system';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  created_at: string;
}

interface FAQDetailsModalProps {
  faq: FAQ | null;
  isOpen: boolean;
  onClose: () => void;
}

export const FAQDetailsModal: React.FC<FAQDetailsModalProps> = ({
  faq,
  isOpen,
  onClose,
}) => {
  // Modern color system using enhancedColorMappingService
  const [colorTheme, setColorTheme] = useState<{
    className: string;
    style: React.CSSProperties;
  }>({ className: '', style: {} });
  const [isLoadingTheme, setIsLoadingTheme] = useState(false);

  // Load color theme using enhancedColorMappingService
  useEffect(() => {
    const loadColorTheme = async () => {
      if (!faq || !isOpen) return;

      setIsLoadingTheme(true);
      try {
        const theme = await enhancedColorMappingService.getEnhancedColorTheme(
          'faq',
          faq.category ?? 'GENERAL',
          0 // Use first fallback theme
        );
        setColorTheme(theme);
      } catch (error) {
        console.warn('Failed to load FAQ color theme:', error);
        // Fallback theme using design tokens
        setColorTheme({
          className: 'bg-gradient-to-br from-green-50 to-emerald-50',
          style: {
            background: 'var(--festival-gradient-accent)',
            borderColor: 'var(--festival-border)'
          }
        });
      } finally {
        setIsLoadingTheme(false);
      }
    };

    loadColorTheme();
  }, [faq, isOpen]);

  if (!faq) return null;

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'GENERAL': return 'General Questions';
      case 'ACCOUNT': return 'Account & Profile';
      case 'ACTIVITIES': return 'Activities & Events';
      case 'SAFETY': return 'Safety & Security';
      case 'TECHNICAL': return 'Technical Support';
      case 'BILLING': return 'Billing & Payments';
      case 'PRIVACY': return 'Privacy & Data';
      default: return category;
    }
  };

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={onClose}
      size="xl"
      showCloseButton={false}
    >
      <div className="h-full flex flex-col">
        {/* Standardized Header following UnifiedModal.tsx patterns */}
        <div
          className="p-4 sm:p-6 pb-2"
          style={{
            background: !isLoadingTheme ? colorTheme.style.background : 'var(--festival-bg-card)',
            borderBottom: '1px solid var(--festival-border)'
          }}
        >
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div
                className="w-10 h-10 rounded-lg flex items-center justify-center"
                style={{
                  background: !isLoadingTheme && colorTheme.style.background
                    ? colorTheme.style.background
                    : 'var(--festival-gradient-accent)',
                  color: 'var(--festival-text-on-primary)'
                }}
              >
                <HelpCircle className="w-5 h-5" />
              </div>
              <div>
                <h2
                  className="text-lg sm:text-xl font-bold pr-8"
                  style={{ color: 'var(--festival-text-auto)' }}
                >
                  FAQ
                </h2>
                {faq.category && (
                  <Badge variant="secondary" className="mt-2 text-xs">
                    {getCategoryLabel(faq.category)}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Featured Badge following UnifiedModal.tsx pattern */}
          {faq.is_featured && (
            <Badge
              className="w-fit mt-2 text-xs"
              style={{
                backgroundColor: 'var(--festival-warning-bg)',
                color: 'var(--festival-warning)',
                border: '1px solid var(--festival-warning)'
              }}
            >
              Featured FAQ
            </Badge>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
                {/* Question */}
                <div className="bg-muted/20 rounded-lg p-4 border-l-4 border-primary">
                  <h3 className="text-lg font-semibold text-foreground mb-2">Question</h3>
                  <p className="text-foreground">{faq.question}</p>
                </div>

                {/* Answer */}
                <div className="bg-muted/20 rounded-lg p-4 border-l-4 border-festival-success">
                  <h3 className="text-lg font-semibold text-foreground mb-2">Answer</h3>
                  <div className="prose prose-foreground max-w-none">
                    <p className="text-foreground leading-relaxed whitespace-pre-wrap">{faq.answer}</p>
                  </div>
                </div>

                {/* Tags */}
                {faq.tags && faq.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {faq.tags.map((tag) => (
                      <Badge key={tag} variant="outline">
                        <Tag className="w-3 h-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}

                {/* Stats */}
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  {faq.view_count !== undefined && (
                    <span className="flex items-center gap-1">
                      <HelpCircle className="w-4 h-4" />
                      {faq.view_count} views
                    </span>
                  )}
                  {faq.helpful_count !== undefined && (
                    <span className="flex items-center gap-1">
                      <Heart className="w-4 h-4" />
                      {faq.helpful_count} helpful
                    </span>
                  )}
                  <span>
                    Updated {new Date(faq.created_at).toLocaleDateString()}
                  </span>
                </div>
        </div>

        {/* Standardized Footer following UnifiedModal.tsx patterns */}
        <div
          className="flex flex-col sm:flex-row gap-2 sm:gap-3 p-4 sm:p-6 pt-2"
          style={{
            borderTop: '1px solid var(--festival-border)',
            backgroundColor: 'var(--festival-bg-muted)'
          }}
        >
          {/* Action buttons with mobile-first responsive design */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
            <UnifiedInteractionButton
              type="helpful"
              itemId={faq.id}
              itemType="faq"
              variant="outline"
              className="w-full sm:w-auto min-w-[120px]"
              size="default"
            />
            <UnifiedInteractionButton
              type="share"
              itemId={faq.id}
              itemType="faq"
              variant="outline"
              className="w-full sm:w-auto min-w-[120px]"
              size="default"
            />
            <Button
              variant="outline"
              onClick={onClose}
              className="w-full sm:w-auto min-w-[120px]"
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </ResponsiveModal>
  );
};

export default FAQDetailsModal;

#!/usr/bin/env node

/**
 * Authentication Flow Testing Script
 * 
 * This script performs comprehensive testing of the authentication system
 * including registration, login, session management, and role-based access.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration');
  console.error('Please check .env.local file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Test data
const testUsers = [
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    fullName: 'Test User One',
    expectedRole: 'USER'
  },
  {
    email: '<EMAIL>',
    password: 'AdminPass123!',
    fullName: 'Admin User',
    expectedRole: 'SUPER_ADMIN'
  }
];

/**
 * Test user registration
 */
async function testUserRegistration(userData) {
  console.log(`\n🔄 Testing registration for: ${userData.email}`);
  
  try {
    // First, check if user already exists and clean up
    const { data: existingUser } = await supabase.auth.admin.getUserByEmail(userData.email);
    if (existingUser) {
      console.log(`⚠️  User ${userData.email} already exists, cleaning up...`);
      await supabase.auth.admin.deleteUser(existingUser.id);
    }

    // Test registration
    const { data, error } = await supabase.auth.signUp({
      email: userData.email,
      password: userData.password,
      options: {
        data: {
          full_name: userData.fullName,
          username: userData.email.split('@')[0],
          role: userData.expectedRole
        }
      }
    });

    if (error) {
      console.error(`❌ Registration failed: ${error.message}`);
      return false;
    }

    if (data.user) {
      console.log(`✅ Registration successful for: ${userData.email}`);
      console.log(`   User ID: ${data.user.id}`);
      console.log(`   Email confirmed: ${data.user.email_confirmed_at ? 'Yes' : 'No'}`);
      return true;
    }

    console.error('❌ Registration failed: No user data returned');
    return false;
  } catch (error) {
    console.error(`❌ Registration error: ${error.message}`);
    return false;
  }
}

/**
 * Test user login
 */
async function testUserLogin(userData) {
  console.log(`\n🔄 Testing login for: ${userData.email}`);
  
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: userData.email,
      password: userData.password
    });

    if (error) {
      console.error(`❌ Login failed: ${error.message}`);
      return false;
    }

    if (data.user && data.session) {
      console.log(`✅ Login successful for: ${userData.email}`);
      console.log(`   Session ID: ${data.session.access_token.substring(0, 20)}...`);
      console.log(`   User ID: ${data.user.id}`);
      return { user: data.user, session: data.session };
    }

    console.error('❌ Login failed: No user/session data returned');
    return false;
  } catch (error) {
    console.error(`❌ Login error: ${error.message}`);
    return false;
  }
}

/**
 * Test profile creation and retrieval
 */
async function testProfileOperations(user) {
  console.log(`\n🔄 Testing profile operations for user: ${user.id}`);
  
  try {
    // Check if profile exists
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error(`❌ Profile query failed: ${error.message}`);
      return false;
    }

    if (profile) {
      console.log(`✅ Profile found for user: ${user.id}`);
      console.log(`   Username: ${profile.username}`);
      console.log(`   Role: ${profile.role}`);
      console.log(`   Full Name: ${profile.full_name}`);
      return profile;
    } else {
      console.log(`⚠️  No profile found, creating one...`);
      
      // Create profile
      const { data: newProfile, error: createError } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          username: user.email.split('@')[0],
          email: user.email,
          full_name: user.user_metadata?.full_name || 'Test User',
          role: 'USER'
        })
        .select()
        .single();

      if (createError) {
        console.error(`❌ Profile creation failed: ${createError.message}`);
        return false;
      }

      console.log(`✅ Profile created for user: ${user.id}`);
      return newProfile;
    }
  } catch (error) {
    console.error(`❌ Profile operations error: ${error.message}`);
    return false;
  }
}

/**
 * Test session management
 */
async function testSessionManagement() {
  console.log(`\n🔄 Testing session management...`);
  
  try {
    // Get current session
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error(`❌ Session retrieval failed: ${error.message}`);
      return false;
    }

    if (session) {
      console.log(`✅ Active session found`);
      console.log(`   User ID: ${session.user.id}`);
      console.log(`   Expires at: ${new Date(session.expires_at * 1000).toISOString()}`);
      
      // Test session refresh
      const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
      
      if (refreshError) {
        console.error(`❌ Session refresh failed: ${refreshError.message}`);
        return false;
      }

      console.log(`✅ Session refresh successful`);
      return true;
    } else {
      console.log(`⚠️  No active session found`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Session management error: ${error.message}`);
    return false;
  }
}

/**
 * Test logout functionality
 */
async function testLogout() {
  console.log(`\n🔄 Testing logout...`);
  
  try {
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      console.error(`❌ Logout failed: ${error.message}`);
      return false;
    }

    // Verify session is cleared
    const { data: { session } } = await supabase.auth.getSession();
    
    if (session) {
      console.error(`❌ Logout failed: Session still active`);
      return false;
    }

    console.log(`✅ Logout successful - session cleared`);
    return true;
  } catch (error) {
    console.error(`❌ Logout error: ${error.message}`);
    return false;
  }
}

/**
 * Main testing function
 */
async function runAuthenticationTests() {
  console.log('🚀 Starting Authentication Flow Testing');
  console.log('=====================================');
  
  const results = {
    registration: 0,
    login: 0,
    profile: 0,
    session: 0,
    logout: 0,
    total: 0
  };

  // Test each user
  for (const userData of testUsers) {
    console.log(`\n📋 Testing user: ${userData.email}`);
    console.log('-----------------------------------');
    
    // Test registration
    const registrationSuccess = await testUserRegistration(userData);
    if (registrationSuccess) results.registration++;
    
    // Test login
    const loginResult = await testUserLogin(userData);
    if (loginResult) {
      results.login++;
      
      // Test profile operations
      const profileResult = await testProfileOperations(loginResult.user);
      if (profileResult) results.profile++;
      
      // Test session management
      const sessionResult = await testSessionManagement();
      if (sessionResult) results.session++;
      
      // Test logout
      const logoutResult = await testLogout();
      if (logoutResult) results.logout++;
    }
    
    results.total++;
  }

  // Print summary
  console.log('\n📊 AUTHENTICATION TESTING SUMMARY');
  console.log('==================================');
  console.log(`Registration Tests: ${results.registration}/${results.total} passed`);
  console.log(`Login Tests: ${results.login}/${results.total} passed`);
  console.log(`Profile Tests: ${results.profile}/${results.total} passed`);
  console.log(`Session Tests: ${results.session}/${results.total} passed`);
  console.log(`Logout Tests: ${results.logout}/${results.total} passed`);
  
  const totalTests = results.total * 5; // 5 tests per user
  const passedTests = results.registration + results.login + results.profile + results.session + results.logout;
  const successRate = (passedTests / totalTests * 100).toFixed(1);
  
  console.log(`\nOverall Success Rate: ${passedTests}/${totalTests} (${successRate}%)`);
  
  if (successRate >= 80) {
    console.log('✅ Authentication system is working well!');
  } else {
    console.log('⚠️  Authentication system needs attention');
  }
  
  return results;
}

// Run the tests
runAuthenticationTests()
  .then(() => {
    console.log('\n🏁 Authentication testing completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Authentication testing failed:', error);
    process.exit(1);
  });

export { runAuthenticationTests };

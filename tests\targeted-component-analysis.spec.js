/**
 * Targeted Component Analysis
 * 
 * This test addresses the UI interaction issues found in previous tests and focuses on:
 * - Alternative interaction methods (force clicks, navigation)
 * - Database state verification
 * - Component visibility and accessibility analysis
 * - Performance measurement
 * - Evidence-based documentation
 */

import { test, expect } from '@playwright/test';

// Helper functions
async function takeEvidence(page, name, description) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `targeted-${name}-${timestamp}.png`;
  await page.screenshot({ 
    path: `test-results/${filename}`,
    fullPage: true 
  });
  console.log(`📸 Targeted Evidence: ${filename} - ${description}`);
  return filename;
}

async function analyzePageStructure(page) {
  console.log('🔍 Analyzing page structure...');
  
  // Get page title and URL
  const title = await page.title();
  const url = page.url();
  console.log(`Page: ${title} | URL: ${url}`);
  
  // Count interactive elements
  const buttons = await page.locator('button').count();
  const links = await page.locator('a').count();
  const inputs = await page.locator('input').count();
  const forms = await page.locator('form').count();
  
  console.log(`Interactive Elements: ${buttons} buttons, ${links} links, ${inputs} inputs, ${forms} forms`);
  
  // Check for overlapping elements (common cause of interaction failures)
  const overlappingElements = await page.evaluate(() => {
    const elements = document.querySelectorAll('*');
    let overlapping = 0;
    
    for (let i = 0; i < elements.length; i++) {
      const rect = elements[i].getBoundingClientRect();
      if (rect.width > 0 && rect.height > 0) {
        const style = window.getComputedStyle(elements[i]);
        if (style.position === 'absolute' || style.position === 'fixed') {
          overlapping++;
        }
      }
    }
    
    return overlapping;
  });
  
  console.log(`Potentially overlapping elements: ${overlappingElements}`);
  
  return { title, url, buttons, links, inputs, forms, overlappingElements };
}

async function testDirectNavigation(page, path, description) {
  console.log(`🧭 Testing direct navigation to: ${path}`);
  
  try {
    await page.goto(path);
    await page.waitForLoadState('networkidle');
    
    const analysis = await analyzePageStructure(page);
    await takeEvidence(page, `nav-${path.replace(/\//g, '-')}`, `Direct navigation to ${path}: ${description}`);
    
    console.log(`✅ Successfully navigated to ${path}`);
    return { success: true, analysis };
  } catch (error) {
    console.log(`❌ Failed to navigate to ${path}: ${error.message}`);
    await takeEvidence(page, `nav-failed-${path.replace(/\//g, '-')}`, `Failed navigation to ${path}`);
    return { success: false, error: error.message };
  }
}

test.describe('Targeted Component Analysis', () => {
  
  test('Page Structure and Navigation Analysis', async ({ page }) => {
    console.log('🏗️ Analyzing page structure and navigation...');
    
    await test.step('Home Page Analysis', async () => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const homeAnalysis = await analyzePageStructure(page);
      await takeEvidence(page, 'home-structure', 'Home page structure analysis');
      
      expect(homeAnalysis.buttons).toBeGreaterThan(0);
      expect(homeAnalysis.links).toBeGreaterThan(0);
    });
    
    await test.step('Direct Navigation Testing', async () => {
      const routes = [
        { path: '/activities', description: 'Activities page' },
        { path: '/famhub', description: 'FamHub page' },
        { path: '/discover', description: 'Discover page' },
        { path: '/profile', description: 'Profile page' },
        { path: '/auth', description: 'Authentication page' },
        { path: '/admin', description: 'Admin dashboard' }
      ];
      
      const navigationResults = [];
      
      for (const route of routes) {
        const result = await testDirectNavigation(page, route.path, route.description);
        navigationResults.push({ ...route, ...result });
        
        // Wait between navigations
        await page.waitForTimeout(1000);
      }
      
      // Log navigation summary
      const successful = navigationResults.filter(r => r.success).length;
      const failed = navigationResults.filter(r => !r.success).length;
      console.log(`📊 Navigation Summary: ${successful} successful, ${failed} failed`);
      
      // At least some routes should be accessible
      expect(successful).toBeGreaterThan(0);
    });
  });

  test('Authentication Flow Analysis', async ({ page }) => {
    console.log('🔐 Analyzing authentication flow...');
    
    await test.step('Auth Page Direct Access', async () => {
      const authResult = await testDirectNavigation(page, '/auth', 'Authentication page');
      
      if (authResult.success) {
        // Look for authentication form elements
        const emailInputs = await page.locator('input[type="email"], input[placeholder*="email" i]').count();
        const passwordInputs = await page.locator('input[type="password"], input[placeholder*="password" i]').count();
        const submitButtons = await page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').count();
        
        console.log(`Auth form elements: ${emailInputs} email inputs, ${passwordInputs} password inputs, ${submitButtons} submit buttons`);
        
        await takeEvidence(page, 'auth-form-analysis', 'Authentication form element analysis');
        
        // Test form interaction with force option
        if (emailInputs > 0 && passwordInputs > 0) {
          try {
            await page.locator('input[type="email"], input[placeholder*="email" i]').first().fill('<EMAIL>', { force: true });
            await page.locator('input[type="password"], input[placeholder*="password" i]').first().fill('testpassword123', { force: true });
            
            await takeEvidence(page, 'auth-form-filled', 'Authentication form filled with force option');
            console.log('✅ Successfully filled authentication form');
          } catch (error) {
            console.log(`❌ Failed to fill authentication form: ${error.message}`);
          }
        }
      }
    });
  });

  test('Component Interaction Analysis', async ({ page }) => {
    console.log('🎯 Analyzing component interactions...');
    
    await test.step('Button Interaction Analysis', async () => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // Get all buttons with their properties
      const buttonAnalysis = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        return buttons.map((btn, index) => {
          const rect = btn.getBoundingClientRect();
          const style = window.getComputedStyle(btn);
          
          return {
            index,
            text: btn.textContent?.trim() || '',
            visible: rect.width > 0 && rect.height > 0,
            enabled: !btn.disabled,
            clickable: style.pointerEvents !== 'none',
            zIndex: style.zIndex,
            position: style.position,
            top: rect.top,
            left: rect.left,
            width: rect.width,
            height: rect.height
          };
        });
      });
      
      console.log(`📊 Button Analysis: Found ${buttonAnalysis.length} buttons`);
      buttonAnalysis.forEach((btn, i) => {
        if (i < 10) { // Log first 10 buttons
          console.log(`Button ${btn.index}: "${btn.text}" - Visible: ${btn.visible}, Enabled: ${btn.enabled}, Clickable: ${btn.clickable}`);
        }
      });
      
      await takeEvidence(page, 'button-analysis', 'Button interaction analysis');
      
      // Test force clicking on visible, enabled buttons
      const clickableButtons = buttonAnalysis.filter(btn => btn.visible && btn.enabled && btn.clickable);
      console.log(`🎯 Testing ${Math.min(clickableButtons.length, 5)} clickable buttons...`);
      
      for (let i = 0; i < Math.min(clickableButtons.length, 5); i++) {
        const btn = clickableButtons[i];
        try {
          await page.locator('button').nth(btn.index).click({ force: true, timeout: 5000 });
          await page.waitForTimeout(1000);
          await takeEvidence(page, `button-${btn.index}-force-clicked`, `Force clicked button: ${btn.text}`);
          console.log(`✅ Successfully force-clicked button: ${btn.text}`);
        } catch (error) {
          console.log(`❌ Failed to force-click button ${btn.index}: ${error.message}`);
        }
      }
    });
  });

  test('Database State Verification', async ({ page }) => {
    console.log('🗄️ Verifying database state and data flow...');
    
    await test.step('Data Loading Analysis', async () => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // Check for loading states and data presence
      const dataAnalysis = await page.evaluate(() => {
        // Look for common data indicators
        const loadingElements = document.querySelectorAll('[class*="loading"], [class*="spinner"], [class*="skeleton"]');
        const errorElements = document.querySelectorAll('[class*="error"], [role="alert"]');
        const dataElements = document.querySelectorAll('[class*="card"], [class*="item"], [class*="list"]');
        
        // Check for Supabase connection indicators
        const supabaseIndicators = document.querySelectorAll('[class*="supabase"], [data-testid*="connection"]');
        
        return {
          loadingElements: loadingElements.length,
          errorElements: errorElements.length,
          dataElements: dataElements.length,
          supabaseIndicators: supabaseIndicators.length,
          hasContent: document.body.textContent.length > 1000
        };
      });
      
      console.log(`📊 Data Analysis:`, dataAnalysis);
      await takeEvidence(page, 'data-state-analysis', 'Database state and data loading analysis');
      
      // Navigate to data-heavy pages
      const dataPages = ['/activities', '/famhub', '/discover'];
      
      for (const pagePath of dataPages) {
        try {
          await page.goto(pagePath);
          await page.waitForLoadState('networkidle');
          
          const pageDataAnalysis = await page.evaluate(() => {
            const items = document.querySelectorAll('[class*="card"], [class*="item"], li, .activity, .event, .festival');
            return {
              itemCount: items.length,
              hasData: items.length > 0,
              textContent: document.body.textContent.length
            };
          });
          
          console.log(`📊 ${pagePath} data:`, pageDataAnalysis);
          await takeEvidence(page, `data-${pagePath.replace('/', '')}`, `Data analysis for ${pagePath}`);
          
        } catch (error) {
          console.log(`❌ Failed to analyze data on ${pagePath}: ${error.message}`);
        }
      }
    });
  });

  test('Performance and Accessibility Analysis', async ({ page }) => {
    console.log('⚡ Analyzing performance and accessibility...');
    
    await test.step('Performance Metrics', async () => {
      await page.goto('/');
      
      // Measure page load performance
      const performanceMetrics = await page.evaluate(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        return {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          totalLoadTime: navigation.loadEventEnd - navigation.fetchStart,
          resourceCount: performance.getEntriesByType('resource').length
        };
      });
      
      console.log(`⚡ Performance Metrics:`, performanceMetrics);
      
      // Check accessibility features
      const accessibilityAnalysis = await page.evaluate(() => {
        const ariaLabels = document.querySelectorAll('[aria-label]').length;
        const altTexts = document.querySelectorAll('img[alt]').length;
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6').length;
        const focusableElements = document.querySelectorAll('button, a, input, select, textarea, [tabindex]').length;
        
        return {
          ariaLabels,
          altTexts,
          headings,
          focusableElements
        };
      });
      
      console.log(`♿ Accessibility Analysis:`, accessibilityAnalysis);
      await takeEvidence(page, 'performance-accessibility', 'Performance and accessibility analysis');
      
      // Basic performance assertions
      expect(performanceMetrics.totalLoadTime).toBeLessThan(10000); // Less than 10 seconds
      expect(accessibilityAnalysis.focusableElements).toBeGreaterThan(0);
    });
  });
});

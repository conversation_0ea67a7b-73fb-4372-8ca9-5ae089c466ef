-- Create Group System Foundation
-- This migration creates the missing foundational tables and columns
-- required before applying the Smart Group Formation enhancement

-- ============================================================================
-- ENHANCE EXISTING GROUPS TABLE
-- ============================================================================

-- Add missing columns to existing groups table
ALTER TABLE groups 
ADD COLUMN IF NOT EXISTS is_private BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW());

-- Update max_members column if it exists but needs default
DO $$
BEGIN
    -- Check if max_members column exists and update its default
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'groups' AND column_name = 'max_members') THEN
        ALTER TABLE groups ALTER COLUMN max_members SET DEFAULT 20;
    ELSE
        ALTER TABLE groups ADD COLUMN max_members INTEGER DEFAULT 20;
    END IF;
END $$;

-- ============================================================================
-- CREATE GROUP MEMBERS TABLE
-- ============================================================================

-- Create group_members table (critical missing table)
CREATE TABLE IF NOT EXISTS group_members (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    role TEXT DEFAULT 'member' CHECK (role IN ('admin', 'moderator', 'member')),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    left_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    invited_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    
    -- Ensure unique active memberships (user can't be in same group twice)
    UNIQUE(group_id, user_id)
);

-- ============================================================================
-- CREATE SUPPORTING TABLES
-- ============================================================================

-- Create group invitations table
CREATE TABLE IF NOT EXISTS group_invitations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    inviter_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    invitee_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired')),
    message TEXT,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    responded_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    
    -- Ensure unique pending invitations
    UNIQUE(group_id, invitee_id, status)
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Group members indexes
CREATE INDEX IF NOT EXISTS idx_group_members_group_id ON group_members(group_id);
CREATE INDEX IF NOT EXISTS idx_group_members_user_id ON group_members(user_id);
CREATE INDEX IF NOT EXISTS idx_group_members_role ON group_members(role);
CREATE INDEX IF NOT EXISTS idx_group_members_joined_at ON group_members(joined_at);

-- Group invitations indexes
CREATE INDEX IF NOT EXISTS idx_group_invitations_group_id ON group_invitations(group_id);
CREATE INDEX IF NOT EXISTS idx_group_invitations_invitee_id ON group_invitations(invitee_id);
CREATE INDEX IF NOT EXISTS idx_group_invitations_status ON group_invitations(status);
CREATE INDEX IF NOT EXISTS idx_group_invitations_expires_at ON group_invitations(expires_at);

-- Enhanced groups indexes
CREATE INDEX IF NOT EXISTS idx_groups_is_private ON groups(is_private);
CREATE INDEX IF NOT EXISTS idx_groups_creator_id ON groups(creator_id);
CREATE INDEX IF NOT EXISTS idx_groups_festival_id ON groups(festival_id);

-- ============================================================================
-- ROW LEVEL SECURITY POLICIES
-- ============================================================================

-- Enable RLS on new tables
ALTER TABLE group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_invitations ENABLE ROW LEVEL SECURITY;

-- Group members policies
CREATE POLICY "Users can view group members for groups they can see" 
    ON group_members FOR SELECT 
    USING (
        group_id IN (
            SELECT id FROM groups 
            WHERE NOT is_private OR 
            id IN (SELECT group_id FROM group_members WHERE user_id = auth.uid())
        )
    );

CREATE POLICY "Group admins can manage group members" 
    ON group_members FOR ALL 
    USING (
        group_id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Users can join groups they're invited to" 
    ON group_members FOR INSERT 
    WITH CHECK (
        user_id = auth.uid() AND (
            -- User is joining a public group
            group_id IN (SELECT id FROM groups WHERE NOT is_private) OR
            -- User has a valid invitation
            group_id IN (
                SELECT group_id FROM group_invitations 
                WHERE invitee_id = auth.uid() AND status = 'accepted'
            )
        )
    );

CREATE POLICY "Users can leave groups they're members of" 
    ON group_members FOR DELETE 
    USING (user_id = auth.uid());

-- Group invitations policies
CREATE POLICY "Users can view invitations sent to them" 
    ON group_invitations FOR SELECT 
    USING (invitee_id = auth.uid() OR inviter_id = auth.uid());

CREATE POLICY "Group admins can send invitations" 
    ON group_invitations FOR INSERT 
    WITH CHECK (
        inviter_id = auth.uid() AND
        group_id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.uid() AND role IN ('admin', 'moderator')
        )
    );

CREATE POLICY "Invitees can respond to their invitations" 
    ON group_invitations FOR UPDATE 
    USING (invitee_id = auth.uid())
    WITH CHECK (invitee_id = auth.uid());

-- Enhanced groups policies (update existing)
DROP POLICY IF EXISTS "Users can view public groups" ON groups;
DROP POLICY IF EXISTS "Users can view groups they're members of" ON groups;

CREATE POLICY "Users can view public groups or groups they're members of" 
    ON groups FOR SELECT 
    USING (
        NOT is_private OR 
        id IN (SELECT group_id FROM group_members WHERE user_id = auth.uid())
    );

CREATE POLICY "Authenticated users can create groups" 
    ON groups FOR INSERT 
    WITH CHECK (auth.role() = 'authenticated' AND creator_id = auth.uid());

CREATE POLICY "Group creators and admins can update groups" 
    ON groups FOR UPDATE 
    USING (
        creator_id = auth.uid() OR 
        id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Function to automatically add creator as admin when group is created
CREATE OR REPLACE FUNCTION add_creator_as_group_admin()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Add the creator as an admin member of the group
    INSERT INTO group_members (group_id, user_id, role, joined_at)
    VALUES (NEW.id, NEW.creator_id, 'admin', NOW())
    ON CONFLICT (group_id, user_id) DO NOTHING;
    
    RETURN NEW;
END;
$$;

-- Create trigger to auto-add creator as admin
DROP TRIGGER IF EXISTS add_creator_as_admin_trigger ON groups;
CREATE TRIGGER add_creator_as_admin_trigger
    AFTER INSERT ON groups
    FOR EACH ROW
    EXECUTE FUNCTION add_creator_as_group_admin();

-- Function to handle group invitation acceptance
CREATE OR REPLACE FUNCTION accept_group_invitation(invitation_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
    invitation_record RECORD;
BEGIN
    -- Get invitation details
    SELECT * INTO invitation_record 
    FROM group_invitations 
    WHERE id = invitation_id 
    AND invitee_id = auth.uid() 
    AND status = 'pending'
    AND expires_at > NOW();
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Add user to group
    INSERT INTO group_members (group_id, user_id, role, joined_at, invited_by)
    VALUES (
        invitation_record.group_id, 
        invitation_record.invitee_id, 
        'member', 
        NOW(),
        invitation_record.inviter_id
    )
    ON CONFLICT (group_id, user_id) DO NOTHING;
    
    -- Update invitation status
    UPDATE group_invitations 
    SET status = 'accepted', responded_at = NOW() 
    WHERE id = invitation_id;
    
    RETURN TRUE;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION accept_group_invitation TO authenticated;

-- ============================================================================
-- CREATE TRIGGERS FOR UPDATED_AT
-- ============================================================================

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at trigger to groups table
DROP TRIGGER IF EXISTS update_groups_updated_at ON groups;
CREATE TRIGGER update_groups_updated_at
    BEFORE UPDATE ON groups
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- VALIDATION AND LOGGING
-- ============================================================================

-- Test that the foundation is working
DO $$
DECLARE
    groups_count INTEGER;
    group_members_exists BOOLEAN;
    is_private_exists BOOLEAN;
BEGIN
    -- Check groups table
    SELECT COUNT(*) INTO groups_count FROM groups;
    RAISE NOTICE 'Groups table accessible with % existing groups', groups_count;
    
    -- Check group_members table exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'group_members'
    ) INTO group_members_exists;
    
    IF group_members_exists THEN
        RAISE NOTICE 'SUCCESS: group_members table created successfully';
    ELSE
        RAISE NOTICE 'ERROR: group_members table not found';
    END IF;
    
    -- Check is_private column exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'groups' AND column_name = 'is_private'
    ) INTO is_private_exists;
    
    IF is_private_exists THEN
        RAISE NOTICE 'SUCCESS: groups.is_private column added successfully';
    ELSE
        RAISE NOTICE 'ERROR: groups.is_private column not found';
    END IF;
END $$;

-- Log successful migration
DO $$
BEGIN
    RAISE NOTICE 'SUCCESS: Group system foundation created successfully';
    RAISE NOTICE 'INFO: group_members table created with RLS policies';
    RAISE NOTICE 'INFO: groups.is_private column added';
    RAISE NOTICE 'INFO: Group invitations system implemented';
    RAISE NOTICE 'INFO: Auto-admin assignment trigger created';
    RAISE NOTICE 'INFO: Ready for Smart Group Formation migration';
END $$;

/**
 * Null Safety Utilities
 * 
 * Comprehensive utilities for handling null and undefined values consistently
 * throughout the Festival Family application. These utilities provide defensive
 * programming patterns while maintaining functionality.
 * 
 * @version 1.0.0
 */

// ============================================================================
// TYPE GUARDS
// ============================================================================

/**
 * Check if a value is null or undefined
 */
export function isNullOrUndefined(value: unknown): value is null | undefined {
  return value === null || value === undefined;
}

/**
 * Check if a value is not null or undefined
 */
export function isNotNullOrUndefined<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

/**
 * Check if a string is null, undefined, or empty
 */
export function isNullOrEmpty(value: string | null | undefined): value is null | undefined | '' {
  return isNullOrUndefined(value) || value === '';
}

// ============================================================================
// VALUE GETTERS WITH DEFAULTS
// ============================================================================

/**
 * Get value or return default if null/undefined
 */
export function getValueOrDefault<T>(value: T | null | undefined, defaultValue: T): T {
  return value ?? defaultValue;
}

/**
 * Get string value or empty string if null/undefined
 */
export function getStringOrEmpty(value: string | null | undefined): string {
  return value ?? '';
}

/**
 * Get string value or null if empty/null/undefined
 */
export function getStringOrNull(value: string | null | undefined): string | null {
  return isNullOrEmpty(value) ? null : value;
}

/**
 * Get number value or default if null/undefined/NaN
 */
export function getNumberOrDefault(value: number | null | undefined, defaultValue: number): number {
  return (typeof value === 'number' && !isNaN(value)) ? value : defaultValue;
}

/**
 * Get boolean value or default if null/undefined
 */
export function getBooleanOrDefault(value: boolean | null | undefined, defaultValue: boolean): boolean {
  return typeof value === 'boolean' ? value : defaultValue;
}

// ============================================================================
// DATE SAFETY UTILITIES
// ============================================================================

/**
 * Safely parse a date, returning null if invalid
 */
export function safeDateParse(value: string | Date | null | undefined): Date | null {
  if (isNullOrUndefined(value)) return null;
  
  const date = value instanceof Date ? value : new Date(value);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * Safely format a date, returning fallback if invalid
 */
export function safeDateFormat(
  value: string | Date | null | undefined, 
  fallback: string = 'Date not available'
): string {
  const date = safeDateParse(value);
  return date ? date.toLocaleDateString() : fallback;
}

/**
 * Safely get ISO string from date, returning null if invalid
 */
export function safeISOString(value: string | Date | null | undefined): string | null {
  const date = safeDateParse(value);
  return date ? date.toISOString() : null;
}

// ============================================================================
// ARRAY SAFETY UTILITIES
// ============================================================================

/**
 * Safely get array or empty array if null/undefined
 */
export function getArrayOrEmpty<T>(value: T[] | null | undefined): T[] {
  return Array.isArray(value) ? value : [];
}

/**
 * Safely get array length
 */
export function safeArrayLength(value: unknown[] | null | undefined): number {
  return Array.isArray(value) ? value.length : 0;
}

/**
 * Safely check if array has items
 */
export function hasArrayItems(value: unknown[] | null | undefined): boolean {
  return Array.isArray(value) && value.length > 0;
}

// ============================================================================
// OBJECT SAFETY UTILITIES
// ============================================================================

/**
 * Safely get nested property value
 */
export function safeGet<T>(obj: any, path: string, defaultValue: T): T {
  if (isNullOrUndefined(obj)) return defaultValue;
  
  const keys = path.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (isNullOrUndefined(current) || !(key in current)) {
      return defaultValue;
    }
    current = current[key];
  }
  
  return current ?? defaultValue;
}

/**
 * Safely check if object has property
 */
export function safeHasProperty(obj: any, property: string): boolean {
  return !isNullOrUndefined(obj) && property in obj;
}

// ============================================================================
// FORM DATA UTILITIES
// ============================================================================

/**
 * Convert form string to database value (empty string to null)
 */
export function formStringToDbValue(value: string | null | undefined): string | null {
  return isNullOrEmpty(value) ? null : value;
}

/**
 * Convert database value to form string (null to empty string)
 */
export function dbValueToFormString(value: string | null | undefined): string {
  return value ?? '';
}

/**
 * Convert form number to database value
 */
export function formNumberToDbValue(value: number | string | null | undefined): number | null {
  if (isNullOrUndefined(value)) return null;
  const num = typeof value === 'number' ? value : parseFloat(value);
  return isNaN(num) ? null : num;
}

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validate required fields are present and not empty
 */
export function validateRequiredFields<T extends Record<string, any>>(
  obj: T,
  requiredFields: (keyof T)[]
): { isValid: boolean; missingFields: string[] } {
  const missingFields = requiredFields.filter(field => {
    const value = obj[field];
    return isNullOrUndefined(value) || (typeof value === 'string' && value.trim() === '');
  }).map(String);
  
  return {
    isValid: missingFields.length === 0,
    missingFields
  };
}

/**
 * Safely validate email format
 */
export function isValidEmail(email: string | null | undefined): boolean {
  if (isNullOrEmpty(email)) return false;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Safely validate URL format
 */
export function isValidUrl(url: string | null | undefined): boolean {
  if (isNullOrEmpty(url)) return false;
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// ============================================================================
// ERROR HANDLING UTILITIES
// ============================================================================

/**
 * Safely get error message from unknown error
 */
export function safeErrorMessage(error: unknown, fallback: string = 'An error occurred'): string {
  if (error instanceof Error) return error.message;
  if (typeof error === 'string') return error;
  return fallback;
}

/**
 * Safely log error with context
 */
export function safeErrorLog(error: unknown, context: string): void {
  const message = safeErrorMessage(error);
  console.error(`[${context}] ${message}`, error);
}

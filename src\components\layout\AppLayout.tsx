import React from 'react';
import { Outlet } from 'react-router-dom';
import SimpleNavigation from '../navigation/SimpleNavigation';
import ModernBottomNav from '../navigation/ModernBottomNav';
import { Toaster } from 'react-hot-toast';
import { GlobalAnnouncementBanner } from '../announcements/GlobalAnnouncementBanner';
import { PerformanceMonitor, usePerformanceTracking } from '../performance/PerformanceMonitor';

/**
 * Main application layout component
 * Includes responsive navigation for both desktop and mobile
 * Uses PageTransition for smooth page transitions
 */
const AppLayout: React.FC = () => {
  // Enable performance tracking
  usePerformanceTracking();

  return (
    <div className="min-h-screen flex flex-col bg-background"
         style={{ background: 'var(--festival-gradient-subtle)' }}>
      {/* Top Navigation */}
      <SimpleNavigation />

      {/* Global Announcement Banner */}
      <GlobalAnnouncementBanner />

      {/* Main Content with Page Transition - Mobile Optimized */}
      <main className="flex-1 w-full px-2 sm:px-4 pb-20">
        {/* <PageTransition variant="fade" duration={0.3}> TEMPORARILY DISABLED TO TEST */}
          <Outlet />
        {/* </PageTransition> */}
      </main>

      {/* Modern Bottom Navigation - Only visible on mobile */}
      <ModernBottomNav />

      {/* Non-blocking Toast Configuration */}
      <Toaster
        position="top-center"
        toastOptions={{
          duration: 4000, // Reasonable duration
          className: 'backdrop-blur-md bg-card/90 border border-border text-foreground',
          style: {
            background: 'hsl(var(--card) / 0.95)',
            color: 'hsl(var(--foreground))',
            border: '1px solid hsl(var(--border))',
            borderRadius: '12px',
            backdropFilter: 'blur(12px)',
            zIndex: 1000, // Lower z-index to avoid conflicts
            pointerEvents: 'auto', // Allow interaction with toast itself
            marginTop: '20px', // Space from top
            maxWidth: '90vw', // Mobile-friendly width
            fontSize: '14px', // Mobile-optimized text size
          },
        }}
        containerStyle={{
          top: 20, // Position from top
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 1000,
          pointerEvents: 'none', // Container doesn't block, but toasts can be interacted with
        }}
      />

      {/* Performance Monitor (development only) */}
      <PerformanceMonitor position="top-right" />
    </div>
  );
};

export default AppLayout;

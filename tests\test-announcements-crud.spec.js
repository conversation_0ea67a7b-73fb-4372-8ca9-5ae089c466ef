/**
 * Comprehensive Announcements CRUD Testing
 * 
 * Tests Create, Read, Update, Delete operations for announcements
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

test('Comprehensive Announcements CRUD Test', async ({ page }) => {
  console.log('🧪 Testing Announcements CRUD operations...');
  
  // Capture network requests for database operations
  const networkRequests = [];
  page.on('request', request => {
    if (request.url().includes('announcements') && ['POST', 'PATCH', 'DELETE'].includes(request.method())) {
      networkRequests.push({
        method: request.method(),
        url: request.url(),
        postData: request.postData()
      });
    }
  });
  
  const loginSuccess = await loginAsAdmin(page);
  console.log(`✅ Login success: ${loginSuccess}`);
  
  if (loginSuccess) {
    // Navigate to announcements
    await page.goto('/admin/announcements');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    console.log('📋 Testing Announcements Interface...');
    
    // Check if announcements interface exists
    const hasAnnouncementsPage = await page.locator('h1:has-text("Announcements"), h1:has-text("Manage Announcements")').count() > 0;
    const hasCreateButton = await page.locator('button:has-text("Create"), button:has-text("New"), button:has-text("Add")').count() > 0;
    const hasAnnouncementsList = await page.locator('table, .announcement-item, [data-testid*="announcement"]').count() > 0;
    
    console.log(`Announcements interface:`);
    console.log(`  Page loaded: ${hasAnnouncementsPage}`);
    console.log(`  Create button: ${hasCreateButton}`);
    console.log(`  Announcements list: ${hasAnnouncementsList}`);
    
    await page.screenshot({ path: 'test-results/announcements-interface.png', fullPage: true });
    
    // Test CREATE functionality
    if (hasCreateButton) {
      console.log('🔗 Testing CREATE announcement...');
      
      await page.click('button:has-text("Create"), button:has-text("New"), button:has-text("Add")');
      await page.waitForTimeout(2000);
      
      // Check if create form appears
      const hasCreateForm = await page.locator('form, input[name*="title"], textarea[name*="content"]').count() > 0;
      console.log(`Create form visible: ${hasCreateForm}`);
      
      if (hasCreateForm) {
        // Fill announcement form
        const testAnnouncement = {
          title: 'Test Announcement ' + Date.now(),
          content: 'This is a test announcement created by automated testing.',
          priority: 'high'
        };
        
        // Fill form fields
        const titleField = page.locator('input[name*="title"], input[placeholder*="title"]').first();
        if (await titleField.isVisible()) {
          await titleField.fill(testAnnouncement.title);
        }
        
        const contentField = page.locator('textarea[name*="content"], textarea[placeholder*="content"]').first();
        if (await contentField.isVisible()) {
          await contentField.fill(testAnnouncement.content);
        }
        
        // Try to set priority if field exists
        const priorityField = page.locator('select[name*="priority"], input[name*="priority"]').first();
        if (await priorityField.isVisible()) {
          await priorityField.selectOption('high');
        }
        
        await page.screenshot({ path: 'test-results/announcements-create-form.png', fullPage: true });
        
        // Submit form
        const submitButton = page.locator('button[type="submit"], button:has-text("Create"), button:has-text("Save")').first();
        if (await submitButton.isVisible()) {
          await submitButton.click();
          await page.waitForTimeout(3000);
          
          console.log('✅ Announcement creation form submitted');
        }
      }
    }
    
    // Test READ functionality - check if announcements are displayed
    console.log('📖 Testing READ announcements...');
    
    await page.goto('/admin/announcements');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const announcementRows = await page.locator('tbody tr, .announcement-item, [data-testid*="announcement"]').count();
    const announcementTitles = await page.locator('text="Test Announcement"').count();
    
    console.log(`Announcements display:`);
    console.log(`  Announcement rows: ${announcementRows}`);
    console.log(`  Test announcements: ${announcementTitles}`);
    
    await page.screenshot({ path: 'test-results/announcements-list.png', fullPage: true });
    
    // Test EDIT functionality
    console.log('✏️ Testing EDIT announcement...');
    
    const editButtons = await page.locator('button:has-text("Edit"), [aria-label*="edit"]').count();
    console.log(`Edit buttons found: ${editButtons}`);
    
    if (editButtons > 0) {
      await page.locator('button:has-text("Edit"), [aria-label*="edit"]').first().click();
      await page.waitForTimeout(2000);
      
      const hasEditForm = await page.locator('form, input[value*="Test Announcement"]').count() > 0;
      console.log(`Edit form visible: ${hasEditForm}`);
      
      await page.screenshot({ path: 'test-results/announcements-edit.png', fullPage: true });
    }
    
    // Test DELETE functionality
    console.log('🗑️ Testing DELETE announcement...');
    
    const deleteButtons = await page.locator('button:has-text("Delete"), [aria-label*="delete"]').count();
    console.log(`Delete buttons found: ${deleteButtons}`);
    
    // Print network activity
    console.log('\n🌐 Database Operations:');
    networkRequests.forEach((req, i) => {
      console.log(`  ${i + 1}. ${req.method} ${req.url}`);
      if (req.postData) {
        console.log(`     Data: ${req.postData.substring(0, 100)}...`);
      }
    });
    
    // Final assessment
    const crudWorking = hasAnnouncementsPage && (hasCreateButton || announcementRows > 0);
    console.log(`\n🎯 ANNOUNCEMENTS CRUD ASSESSMENT:`);
    console.log(`  Interface working: ${crudWorking}`);
    console.log(`  Create functionality: ${hasCreateButton}`);
    console.log(`  Read functionality: ${announcementRows > 0}`);
    console.log(`  Edit functionality: ${editButtons > 0}`);
    console.log(`  Delete functionality: ${deleteButtons > 0}`);
    
    console.log('✅ Announcements CRUD test completed');
  }
});

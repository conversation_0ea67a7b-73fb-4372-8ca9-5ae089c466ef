/**
 * Global Setup for Evidence-Based Testing
 * 
 * This script runs before all tests to prepare the testing environment
 * and ensure the application is ready for comprehensive testing.
 */

import { promises as fs } from 'fs';
import path from 'path';

async function globalSetup() {
  console.log('🚀 Starting Evidence-Based Testing Setup');
  console.log('========================================');
  
  // Create necessary directories
  const directories = [
    'test-evidence',
    'test-results',
    'test-results/html-report',
    'test-results/artifacts'
  ];
  
  for (const dir of directories) {
    try {
      await fs.mkdir(dir, { recursive: true });
      console.log(`📁 Created directory: ${dir}`);
    } catch (error) {
      console.log(`📁 Directory already exists: ${dir}`);
    }
  }
  
  // Create test session metadata
  const sessionMetadata = {
    sessionId: `test-session-${Date.now()}`,
    startTime: new Date().toISOString(),
    testEnvironment: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      cwd: process.cwd()
    },
    applicationUrl: 'http://localhost:5173',
    testObjectives: [
      'Verify application loads without errors',
      'Test authentication system functionality',
      'Validate database integration',
      'Measure performance metrics',
      'Verify cross-browser compatibility',
      'Collect evidence of working features'
    ]
  };
  
  await fs.writeFile(
    'test-evidence/session-metadata.json',
    JSON.stringify(sessionMetadata, null, 2)
  );
  
  console.log(`📋 Test session ID: ${sessionMetadata.sessionId}`);
  console.log(`🕐 Session start time: ${sessionMetadata.startTime}`);
  console.log('✅ Global setup completed successfully');
  
  return sessionMetadata;
}

export default globalSetup;

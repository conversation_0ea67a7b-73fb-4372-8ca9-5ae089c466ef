/**
 * Interactive Functionality Testing
 * 
 * This test systematically identifies and tests actual interactive functionality:
 * - Admin form submissions and data persistence
 * - Card click handlers and navigation
 * - Interactive elements and their responses
 * - End-to-end workflows from admin creation to user interaction
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function takeEvidence(page, name, description) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `interactive-${name}-${timestamp}.png`;
  await page.screenshot({ 
    path: `test-results/${filename}`,
    fullPage: true 
  });
  console.log(`📸 Interactive Evidence: ${filename} - ${description}`);
  return filename;
}

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

async function testFormSubmission(page, formData, expectedSuccessMessage) {
  const consoleErrors = [];
  const networkErrors = [];
  
  page.on('console', msg => {
    if (msg.type() === 'error') {
      consoleErrors.push(msg.text());
    }
  });
  
  page.on('response', response => {
    if (response.status() >= 400) {
      networkErrors.push(`${response.status()} ${response.url()}`);
    }
  });
  
  // Fill form fields
  for (const [fieldName, value] of Object.entries(formData)) {
    const field = page.locator(`input[name="${fieldName}"], textarea[name="${fieldName}"], select[name="${fieldName}"]`).first();
    if (await field.isVisible()) {
      await field.fill(value);
    }
  }
  
  // Submit form
  const submitButton = page.locator('button[type="submit"], button:has-text("Save"), button:has-text("Create"), button:has-text("Submit")').first();
  await submitButton.click();
  
  // Wait for response
  await page.waitForTimeout(5000);
  
  // Check for success indicators
  const hasSuccessMessage = expectedSuccessMessage ? 
    await page.locator(`text="${expectedSuccessMessage}"`, { timeout: 3000 }).isVisible().catch(() => false) : false;
  const hasToastSuccess = await page.locator('.toast, [data-testid*="toast"], [class*="toast"]', { timeout: 3000 }).isVisible().catch(() => false);
  const wasRedirected = !page.url().includes('/new') && !page.url().includes('/create');
  
  return {
    success: hasSuccessMessage || hasToastSuccess || wasRedirected,
    hasSuccessMessage,
    hasToastSuccess,
    wasRedirected,
    consoleErrors,
    networkErrors,
    currentUrl: page.url()
  };
}

test.describe('Interactive Functionality Testing', () => {
  
  test('Admin Form Submission Testing', async ({ page }) => {
    console.log('🧪 Testing admin form submissions...');
    
    const loginSuccess = await loginAsAdmin(page);
    if (!loginSuccess) {
      console.log('⚠️ Skipping form tests - login failed');
      return;
    }
    
    const formTests = [];
    
    await test.step('Test Activity Form Submission', async () => {
      try {
        // Navigate to create activity form
        await page.goto('/admin/activities/new');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        await takeEvidence(page, 'activity-form-before', 'Activity form before submission');
        
        const activityData = {
          name: 'Test Activity ' + Date.now(),
          description: 'This is a test activity created by automated testing to verify form submission functionality.',
          type: 'workshop',
          location: 'Test Location',
          capacity: '50'
        };
        
        const result = await testFormSubmission(page, activityData, 'Activity created');
        
        await takeEvidence(page, 'activity-form-after', 'Activity form after submission');
        
        formTests.push({
          form: 'Activity',
          data: activityData,
          result: result
        });
        
        console.log(`Activity form test: ${result.success ? 'SUCCESS' : 'FAILED'}`);
        console.log(`  - Success message: ${result.hasSuccessMessage}`);
        console.log(`  - Toast notification: ${result.hasToastSuccess}`);
        console.log(`  - Redirected: ${result.wasRedirected}`);
        console.log(`  - Console errors: ${result.consoleErrors.length}`);
        console.log(`  - Network errors: ${result.networkErrors.length}`);
        
      } catch (error) {
        console.log(`❌ Activity form test failed: ${error.message}`);
        formTests.push({
          form: 'Activity',
          result: { success: false, error: error.message }
        });
      }
    });
    
    await test.step('Test Event Form Submission', async () => {
      try {
        await page.goto('/admin/events/new');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        await takeEvidence(page, 'event-form-before', 'Event form before submission');
        
        const eventData = {
          title: 'Test Event ' + Date.now(),
          description: 'This is a test event created by automated testing.',
          start_date: '2024-12-01',
          end_date: '2024-12-03',
          location: 'Test Venue',
          festival_id: '1'
        };
        
        const result = await testFormSubmission(page, eventData, 'Event created');
        
        await takeEvidence(page, 'event-form-after', 'Event form after submission');
        
        formTests.push({
          form: 'Event',
          data: eventData,
          result: result
        });
        
        console.log(`Event form test: ${result.success ? 'SUCCESS' : 'FAILED'}`);
        
      } catch (error) {
        console.log(`❌ Event form test failed: ${error.message}`);
        formTests.push({
          form: 'Event',
          result: { success: false, error: error.message }
        });
      }
    });
    
    await test.step('Test Content Management Form', async () => {
      try {
        await page.goto('/admin/content');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        await takeEvidence(page, 'content-form-before', 'Content form before submission');
        
        // Look for content creation form
        const hasCreateForm = await page.locator('form, input[name*="title"], input[name*="content"]').count() > 0;
        
        if (hasCreateForm) {
          const contentData = {
            title: 'Test Content ' + Date.now(),
            content: 'This is test content created by automated testing.',
            content_key: 'test-content-' + Date.now(),
            content_type: 'announcement'
          };
          
          const result = await testFormSubmission(page, contentData, 'Content created');
          
          await takeEvidence(page, 'content-form-after', 'Content form after submission');
          
          formTests.push({
            form: 'Content',
            data: contentData,
            result: result
          });
          
          console.log(`Content form test: ${result.success ? 'SUCCESS' : 'FAILED'}`);
        } else {
          console.log('⚠️ No content creation form found');
          formTests.push({
            form: 'Content',
            result: { success: false, error: 'No form found' }
          });
        }
        
      } catch (error) {
        console.log(`❌ Content form test failed: ${error.message}`);
        formTests.push({
          form: 'Content',
          result: { success: false, error: error.message }
        });
      }
    });
    
    // Summary
    const successfulForms = formTests.filter(test => test.result.success).length;
    const totalForms = formTests.length;
    
    console.log(`\n📊 ADMIN FORM SUBMISSION SUMMARY:`);
    console.log(`✅ Working: ${successfulForms}/${totalForms} forms`);
    console.log(`❌ Failed: ${totalForms - successfulForms}/${totalForms} forms`);
    
    if (successfulForms < totalForms) {
      console.log('\n❌ Failed form submissions:');
      formTests.filter(test => !test.result.success).forEach(test => {
        console.log(`  - ${test.form}: ${test.result.error || 'Submission failed'}`);
      });
    }
    
    // At least one form should work
    expect(successfulForms).toBeGreaterThan(0);
  });

  test('Card Click Handler Testing', async ({ page }) => {
    console.log('🧪 Testing card click handlers...');
    
    const loginSuccess = await loginAsAdmin(page);
    if (!loginSuccess) {
      console.log('⚠️ Skipping card tests - login failed');
      return;
    }
    
    const cardTests = [];
    
    await test.step('Test Admin List Item Clicks', async () => {
      const adminPages = [
        { path: '/admin/activities', name: 'Activities' },
        { path: '/admin/events', name: 'Events' },
        { path: '/admin/users', name: 'Users' }
      ];
      
      for (const adminPage of adminPages) {
        try {
          await page.goto(adminPage.path);
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(3000);
          
          // Look for edit buttons or clickable items
          const editButtons = await page.locator('button:has-text("Edit"), a:has-text("Edit")').count();
          const clickableRows = await page.locator('tr[onclick], tr.clickable, .clickable').count();
          const actionButtons = await page.locator('button[onclick], .action-button').count();
          
          await takeEvidence(page, `admin-${adminPage.name.toLowerCase()}-list`, `Admin ${adminPage.name} list page`);
          
          // Test clicking first edit button if available
          let clickWorked = false;
          if (editButtons > 0) {
            const firstEditButton = page.locator('button:has-text("Edit"), a:has-text("Edit")').first();
            const initialUrl = page.url();
            
            await firstEditButton.click();
            await page.waitForTimeout(2000);
            
            const newUrl = page.url();
            clickWorked = newUrl !== initialUrl;
            
            if (clickWorked) {
              await takeEvidence(page, `admin-${adminPage.name.toLowerCase()}-edit`, `Admin ${adminPage.name} edit page`);
            }
          }
          
          cardTests.push({
            page: adminPage.name,
            editButtons,
            clickableRows,
            actionButtons,
            clickWorked,
            success: editButtons > 0 && clickWorked
          });
          
          console.log(`${adminPage.name} admin list: ${editButtons} edit buttons, click worked: ${clickWorked}`);
          
        } catch (error) {
          console.log(`❌ Admin ${adminPage.name} test failed: ${error.message}`);
          cardTests.push({
            page: adminPage.name,
            success: false,
            error: error.message
          });
        }
      }
    });
    
    await test.step('Test User-Facing Card Clicks', async () => {
      const userPages = [
        { path: '/activities', name: 'Activities' },
        { path: '/discover', name: 'Discover' },
        { path: '/famhub', name: 'FamHub' }
      ];
      
      for (const userPage of userPages) {
        try {
          await page.goto(userPage.path);
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(3000);
          
          // Look for interactive cards and buttons
          const cards = await page.locator('.card, [class*="card"], .event-card, .activity-card').count();
          const viewButtons = await page.locator('button:has-text("View"), button:has-text("Details"), button:has-text("Open")').count();
          const clickableElements = await page.locator('[onclick], .clickable, .cursor-pointer').count();
          
          await takeEvidence(page, `user-${userPage.name.toLowerCase()}-cards`, `User ${userPage.name} cards`);
          
          // Test clicking first interactive element
          let clickWorked = false;
          const interactiveElement = page.locator('button:has-text("View"), button:has-text("Details"), .card, .clickable').first();
          
          if (await interactiveElement.isVisible()) {
            const initialUrl = page.url();
            
            await interactiveElement.click();
            await page.waitForTimeout(2000);
            
            const newUrl = page.url();
            clickWorked = newUrl !== initialUrl;
            
            if (clickWorked) {
              await takeEvidence(page, `user-${userPage.name.toLowerCase()}-clicked`, `User ${userPage.name} after click`);
            }
          }
          
          cardTests.push({
            page: userPage.name,
            cards,
            viewButtons,
            clickableElements,
            clickWorked,
            success: (cards > 0 || viewButtons > 0) && clickWorked
          });
          
          console.log(`${userPage.name} user page: ${cards} cards, ${viewButtons} view buttons, click worked: ${clickWorked}`);
          
        } catch (error) {
          console.log(`❌ User ${userPage.name} test failed: ${error.message}`);
          cardTests.push({
            page: userPage.name,
            success: false,
            error: error.message
          });
        }
      }
    });
    
    // Summary
    const successfulCards = cardTests.filter(test => test.success).length;
    const totalCards = cardTests.length;
    
    console.log(`\n📊 CARD CLICK HANDLER SUMMARY:`);
    console.log(`✅ Working: ${successfulCards}/${totalCards} pages`);
    console.log(`❌ Failed: ${totalCards - successfulCards}/${totalCards} pages`);
    
    if (successfulCards < totalCards) {
      console.log('\n❌ Pages with non-working card clicks:');
      cardTests.filter(test => !test.success).forEach(test => {
        console.log(`  - ${test.page}: ${test.error || 'Click handlers not working'}`);
      });
    }
    
    // At least some card interactions should work
    expect(successfulCards).toBeGreaterThan(0);
  });

  test('End-to-End Workflow Testing', async ({ page }) => {
    console.log('🧪 Testing end-to-end workflows...');
    
    const loginSuccess = await loginAsAdmin(page);
    if (!loginSuccess) {
      console.log('⚠️ Skipping workflow tests - login failed');
      return;
    }
    
    await test.step('Test Create Activity → View as User Workflow', async () => {
      try {
        // Step 1: Create activity as admin
        await page.goto('/admin/activities/new');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        const testActivityName = 'E2E Test Activity ' + Date.now();
        const activityData = {
          name: testActivityName,
          description: 'End-to-end test activity for workflow validation.',
          type: 'workshop',
          location: 'Test Location'
        };
        
        const createResult = await testFormSubmission(page, activityData, 'Activity created');
        
        await takeEvidence(page, 'e2e-activity-created', 'Activity created in admin');
        
        if (createResult.success) {
          console.log('✅ Activity creation successful');
          
          // Step 2: View activity as user
          await page.goto('/activities');
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(3000);
          
          // Look for the created activity
          const activityVisible = await page.locator(`text="${testActivityName}"`).isVisible();
          
          await takeEvidence(page, 'e2e-activity-user-view', 'Activity viewed as user');
          
          if (activityVisible) {
            console.log('✅ Activity visible to users');
            
            // Step 3: Try to interact with the activity
            const activityElement = page.locator(`text="${testActivityName}"`).first();
            await activityElement.click();
            await page.waitForTimeout(2000);
            
            await takeEvidence(page, 'e2e-activity-interaction', 'Activity interaction attempt');
            
            console.log('✅ End-to-end workflow completed successfully');
          } else {
            console.log('❌ Activity not visible to users');
          }
        } else {
          console.log('❌ Activity creation failed');
        }
        
      } catch (error) {
        console.log(`❌ End-to-end workflow failed: ${error.message}`);
      }
    });
  });
});

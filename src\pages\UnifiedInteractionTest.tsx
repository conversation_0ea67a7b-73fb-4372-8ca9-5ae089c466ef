/**
 * Unified Interaction System Test Page
 * 
 * Demonstrates the new unified interaction components and validates functionality.
 * This page will be removed after successful validation.
 * 
 * @module UnifiedInteractionTest
 * @version 1.0.0
 */

import React, { useState } from 'react';
import { PageLayout } from '@/components/design-system';
import { 
  UnifiedInteractionButton, 
  UnifiedModal, 
  ActivityModal,
  unifiedInteractionService,
  type InteractionType 
} from '@/components/design-system';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, MapPin, Users } from 'lucide-react';

const UnifiedInteractionTest: React.FC = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [activityModalOpen, setActivityModalOpen] = useState(false);
  const [interactionCounts, setInteractionCounts] = useState({
    favorite: 12,
    join: 8,
    rsvp: 15,
    helpful: 23
  });

  // Test activity data
  const testActivity = {
    id: 'test-activity-1',
    title: 'Unified Interaction System Demo',
    description: 'This is a demonstration of the new unified interaction system that replaces scattered button implementations with a single source of truth for all user interactions.',
    start_date: '2025-01-15T18:00:00Z',
    location: 'Festival Main Stage',
    capacity: 100,
    type: 'meetup'
  };

  // All interaction types for testing
  const interactionTypes: InteractionType[] = ['favorite', 'helpful', 'save', 'join', 'rsvp', 'share'];

  const handleStateChange = (type: InteractionType, isActive: boolean, count?: number) => {
    console.log(`🔄 ${type} interaction changed:`, { isActive, count });
    
    if (count !== undefined) {
      setInteractionCounts(prev => ({
        ...prev,
        [type]: count
      }));
    }
  };

  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold">Unified Interaction System Test</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Testing the new unified interaction components that replace scattered implementations 
            with clear semantic distinctions and consistent behavior.
          </p>
          <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-200">
            🧪 Test Environment - Will be removed after validation
          </Badge>
        </div>

        {/* Interaction Configuration Display */}
        <Card>
          <CardHeader>
            <CardTitle>Interaction Type Configurations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {interactionTypes.map((type) => {
                const config = unifiedInteractionService.getInteractionConfig(type);
                return (
                  <div key={type} className="p-4 border rounded-lg space-y-2">
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: config.activeColor }}
                      />
                      <span className="font-semibold capitalize">{config.label}</span>
                    </div>
                    <p className="text-sm text-muted-foreground">{config.description}</p>
                    <p className="text-xs text-muted-foreground italic">{config.semanticMeaning}</p>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Individual Button Tests */}
        <Card>
          <CardHeader>
            <CardTitle>Individual Interaction Buttons</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {interactionTypes.map((type) => (
                <div key={type} className="space-y-2">
                  <UnifiedInteractionButton
                    type={type}
                    itemId="test-item-1"
                    itemType="activity"
                    count={interactionCounts[type as keyof typeof interactionCounts]}
                    showCount={['favorite', 'join', 'rsvp', 'helpful'].includes(type)}
                    onStateChange={(isActive, count) => handleStateChange(type, isActive, count)}
                    className="w-full"
                  />
                  <p className="text-xs text-center text-muted-foreground capitalize">
                    {type}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Button Variants Test */}
        <Card>
          <CardHeader>
            <CardTitle>Button Variants & Sizes</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Size variants */}
            <div className="space-y-2">
              <h4 className="font-semibold">Sizes</h4>
              <div className="flex items-center gap-4">
                <UnifiedInteractionButton
                  type="favorite"
                  itemId="test-size-sm"
                  size="sm"
                  label="Small"
                />
                <UnifiedInteractionButton
                  type="favorite"
                  itemId="test-size-default"
                  size="default"
                  label="Default"
                />
                <UnifiedInteractionButton
                  type="favorite"
                  itemId="test-size-lg"
                  size="lg"
                  label="Large"
                />
              </div>
            </div>

            {/* Style variants */}
            <div className="space-y-2">
              <h4 className="font-semibold">Variants</h4>
              <div className="flex items-center gap-4">
                <UnifiedInteractionButton
                  type="join"
                  itemId="test-variant-default"
                  variant="default"
                  label="Default"
                />
                <UnifiedInteractionButton
                  type="join"
                  itemId="test-variant-outline"
                  variant="outline"
                  label="Outline"
                />
                <UnifiedInteractionButton
                  type="join"
                  itemId="test-variant-ghost"
                  variant="ghost"
                  label="Ghost"
                />
                <UnifiedInteractionButton
                  type="join"
                  itemId="test-variant-compact"
                  variant="compact"
                  iconOnly
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Modal Tests */}
        <Card>
          <CardHeader>
            <CardTitle>Unified Modal System</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              <UnifiedInteractionButton
                type="share"
                itemId="modal-test"
                label="Open Basic Modal"
                onClick={() => setModalOpen(true)}
              />
              <UnifiedInteractionButton
                type="join"
                itemId="activity-modal-test"
                label="Open Activity Modal"
                onClick={() => setActivityModalOpen(true)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        <Card>
          <CardHeader>
            <CardTitle>Test Results & Console Logs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-muted p-4 rounded-lg">
              <p className="text-sm text-muted-foreground">
                Check the browser console for interaction logs. Each button click should show:
              </p>
              <ul className="text-sm text-muted-foreground mt-2 space-y-1">
                <li>• 🔄 Interaction type and state changes</li>
                <li>• Database operation results (success/error)</li>
                <li>• Real-time subscription updates</li>
                <li>• Toast notifications for user feedback</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Basic Modal */}
        <UnifiedModal
          open={modalOpen}
          onClose={() => setModalOpen(false)}
          title="Unified Modal Test"
          itemId="test-modal-1"
          contentType="activities"
          category="meetup"
          featuredBadge="Test Modal"
          badges={[
            { text: 'Interactive', variant: 'outline' },
            { text: 'Unified System', variant: 'secondary' }
          ]}
          metadata={[
            { icon: Calendar, label: 'Test Date', value: 'January 15, 2025' },
            { icon: MapPin, label: 'Location', value: 'Test Environment' },
            { icon: Users, label: 'Participants', value: '42 testers' }
          ]}
          actions={[
            { type: 'favorite' },
            { type: 'share' }
          ]}
        >
          <div className="space-y-4">
            <p>
              This modal demonstrates the unified modal system with consistent theming, 
              metadata display, and standardized action buttons.
            </p>
            <p className="text-muted-foreground">
              The modal automatically applies color theming from the enhancedColorMappingService 
              and provides consistent interaction patterns across all sections.
            </p>
          </div>
        </UnifiedModal>

        {/* Activity Modal */}
        <ActivityModal
          open={activityModalOpen}
          onClose={() => setActivityModalOpen(false)}
          title={testActivity.title}
          itemId={testActivity.id}
          activity={testActivity}
          featuredBadge="Demo Activity"
        />
      </div>
    </PageLayout>
  );
};

export default UnifiedInteractionTest;

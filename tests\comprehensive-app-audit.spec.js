import { test, expect } from '@playwright/test';

/**
 * COMPREHENSIVE FESTIVAL FAMILY APP AUDIT
 * 
 * This test systematically validates every clickable element and user flow
 * from login through all user and admin functionality.
 */

test.describe('🎯 Comprehensive Festival Family App Audit', () => {
  let auditResults = {
    userFlows: {},
    adminFlows: {},
    clickableElements: {},
    errors: [],
    performance: {},
    timestamp: new Date().toISOString()
  };

  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('http://localhost:5173/');
    
    // Wait for initial load
    await page.waitForLoadState('networkidle');
    
    // Record performance metrics
    const performanceEntries = await page.evaluate(() => {
      return JSON.stringify(performance.getEntriesByType('navigation'));
    });
    auditResults.performance.initialLoad = JSON.parse(performanceEntries);
  });

  test('🔐 Phase 1: Authentication Flow Complete Audit', async ({ page }) => {
    console.log('🔍 Testing Authentication Flow...');
    
    // Test 1: Initial page load and navigation to auth
    await expect(page).toHaveTitle('Festival Family');
    
    // Check if we need to navigate to auth or if already there
    const currentUrl = page.url();
    if (!currentUrl.includes('/auth')) {
      // Look for sign in button or navigate to auth
      try {
        await page.click('text=Sign In', { timeout: 5000 });
      } catch {
        await page.goto('http://localhost:5173/auth');
      }
    }
    
    await page.waitForLoadState('networkidle');
    
    // Test 2: Auth page elements
    const authElements = {
      emailInput: await page.locator('input[type="email"]').isVisible(),
      passwordInput: await page.locator('input[type="password"]').isVisible(),
      signInButton: await page.locator('button:has-text("Sign In")').isVisible(),
      signUpToggle: await page.locator('text=Sign Up').isVisible()
    };
    
    auditResults.userFlows.authPageElements = authElements;
    console.log('✅ Auth page elements:', authElements);
    
    // Test 3: Admin login
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'testpassword123');
    
    // Click sign in and wait for navigation
    await page.click('button:has-text("Sign In")');
    await page.waitForLoadState('networkidle');
    
    // Wait for authentication to complete
    await page.waitForTimeout(3000);
    
    // Verify successful login
    const isAuthenticated = await page.locator('text=Welcome').isVisible();
    auditResults.userFlows.authenticationSuccess = isAuthenticated;
    console.log('✅ Authentication successful:', isAuthenticated);
  });

  test('🏠 Phase 2: User Dashboard Complete Navigation Audit', async ({ page }) => {
    console.log('🔍 Testing User Dashboard Navigation...');
    
    // Login first
    await page.goto('http://localhost:5173/auth');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'testpassword123');
    await page.click('button:has-text("Sign In")');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Test all main navigation links
    const navigationTests = {};
    
    // Test Home
    await page.click('text=Home');
    await page.waitForLoadState('networkidle');
    navigationTests.home = {
      loaded: await page.locator('text=Welcome back').isVisible(),
      quickActions: await page.locator('text=Quick Actions').isVisible(),
      recentActivity: await page.locator('text=Recent Activity').isVisible()
    };
    
    // Test Activities
    await page.click('text=Activities');
    await page.waitForLoadState('networkidle');
    navigationTests.activities = {
      loaded: await page.locator('h1').textContent(),
      hasContent: await page.locator('main').isVisible()
    };
    
    // Test FamHub
    await page.click('text=FamHub');
    await page.waitForLoadState('networkidle');
    navigationTests.famhub = {
      loaded: await page.locator('h1').textContent(),
      hasContent: await page.locator('main').isVisible()
    };
    
    // Test Discover
    await page.click('text=Discover');
    await page.waitForLoadState('networkidle');
    navigationTests.discover = {
      loaded: await page.locator('h1').textContent(),
      hasContent: await page.locator('main').isVisible(),
      eventsVisible: await page.locator('text=events found').isVisible()
    };
    
    // Test Profile
    await page.click('text=Profile');
    await page.waitForLoadState('networkidle');
    navigationTests.profile = {
      loaded: await page.locator('text=Your Profile').isVisible(),
      userInfo: await page.locator('text=System Administrator').isVisible(),
      tabs: await page.locator('[role="tablist"]').isVisible()
    };
    
    auditResults.userFlows.navigation = navigationTests;
    console.log('✅ Navigation tests:', navigationTests);
  });

  test('🎯 Phase 3: Interactive Elements Audit', async ({ page }) => {
    console.log('🔍 Testing Interactive Elements...');
    
    // Login first
    await page.goto('http://localhost:5173/auth');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'testpassword123');
    await page.click('button:has-text("Sign In")');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Test Quick Action cards on home
    await page.goto('http://localhost:5173/');
    await page.waitForLoadState('networkidle');
    
    const quickActionTests = {};
    
    // Test Activities card
    const activitiesCard = page.locator('text=Activities Browse and join festival activities');
    if (await activitiesCard.isVisible()) {
      await activitiesCard.click();
      await page.waitForLoadState('networkidle');
      quickActionTests.activitiesCard = {
        clickable: true,
        navigated: page.url().includes('/activities')
      };
      await page.goBack();
      await page.waitForLoadState('networkidle');
    }
    
    // Test Discover events
    await page.goto('http://localhost:5173/discover');
    await page.waitForLoadState('networkidle');
    
    // Test event cards
    const eventCards = await page.locator('button:has-text("View Details")').count();
    quickActionTests.discoverEvents = {
      eventCardsCount: eventCards,
      hasEvents: eventCards > 0
    };
    
    if (eventCards > 0) {
      // Click first event
      await page.locator('button:has-text("View Details")').first().click();
      await page.waitForTimeout(1000);
      quickActionTests.discoverEvents.eventClickable = true;
    }
    
    auditResults.clickableElements.quickActions = quickActionTests;
    console.log('✅ Interactive elements:', quickActionTests);
  });

  test('👑 Phase 4: Admin Dashboard Complete Audit', async ({ page }) => {
    console.log('🔍 Testing Admin Dashboard...');
    
    // Login as admin
    await page.goto('http://localhost:5173/auth');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'testpassword123');
    await page.click('button:has-text("Sign In")');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Access admin dropdown
    await page.click('button:has-text("Admin")');
    await page.waitForTimeout(1000);
    
    // Test admin dropdown options
    const adminDropdownTests = {};
    
    const adminOptions = ['Dashboard', 'Users', 'Events', 'Festivals', 'Activities', 'Announcements'];
    
    for (const option of adminOptions) {
      try {
        const optionElement = page.locator(`text=${option}`);
        if (await optionElement.isVisible()) {
          await optionElement.click();
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(2000);
          
          adminDropdownTests[option.toLowerCase()] = {
            accessible: true,
            loaded: await page.locator('h1, h2, h3').first().textContent(),
            hasContent: await page.locator('main').isVisible(),
            url: page.url()
          };
          
          console.log(`✅ Admin ${option} section loaded`);
        }
      } catch (error) {
        adminDropdownTests[option.toLowerCase()] = {
          accessible: false,
          error: error.message
        };
        console.log(`❌ Admin ${option} section failed:`, error.message);
      }
    }
    
    auditResults.adminFlows.sections = adminDropdownTests;
  });

  test('🔄 Phase 5: Return to User View Test', async ({ page }) => {
    console.log('🔍 Testing Return to User View...');
    
    // Login and go to admin
    await page.goto('http://localhost:5173/auth');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'testpassword123');
    await page.click('button:has-text("Sign In")');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Go to admin dashboard
    await page.goto('http://localhost:5173/admin');
    await page.waitForLoadState('networkidle');
    
    // Test Return to User View button
    const returnButton = page.locator('text=Return to User View');
    if (await returnButton.isVisible()) {
      await returnButton.click();
      await page.waitForLoadState('networkidle');
      
      auditResults.adminFlows.returnToUserView = {
        buttonFound: true,
        navigationSuccessful: page.url() === 'http://localhost:5173/',
        userDashboardLoaded: await page.locator('text=Welcome back').isVisible()
      };
    } else {
      auditResults.adminFlows.returnToUserView = {
        buttonFound: false
      };
    }
    
    console.log('✅ Return to User View test completed');
  });

  test.afterAll(async () => {
    // Save audit results
    const fs = require('fs');
    const path = require('path');
    
    const resultsPath = path.join(process.cwd(), 'test-results', `comprehensive-audit-${Date.now()}.json`);
    fs.writeFileSync(resultsPath, JSON.stringify(auditResults, null, 2));
    
    console.log('\n🎉 COMPREHENSIVE AUDIT COMPLETED!');
    console.log('📊 Results saved to:', resultsPath);
    console.log('\n📋 AUDIT SUMMARY:');
    console.log('- User Flows:', Object.keys(auditResults.userFlows).length);
    console.log('- Admin Flows:', Object.keys(auditResults.adminFlows).length);
    console.log('- Interactive Elements:', Object.keys(auditResults.clickableElements).length);
    console.log('- Errors:', auditResults.errors.length);
  });
});

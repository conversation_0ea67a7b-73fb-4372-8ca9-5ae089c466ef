#!/usr/bin/env node

/**
 * Comprehensive Connectivity Diagnostic Script
 * Tests all aspects of the Festival Family application connectivity
 */

import https from 'https';
import dns from 'dns';
import { promisify } from 'util';
import { createConnection } from 'net';
import { readFileSync } from 'fs';
import { join } from 'path';

const dnsLookup = promisify(dns.lookup);

// Load environment variables from .env file
function loadEnvFile() {
  try {
    const envPath = join(process.cwd(), '.env');
    const envContent = readFileSync(envPath, 'utf8');

    envContent.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value && !process.env[key]) {
        process.env[key] = value;
      }
    });
  } catch (error) {
    console.log('⚠️ Could not load .env file:', error.message);
  }
}

loadEnvFile();

// Configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://ealstndyhwjwipzlrxmg.supabase.co';
const SUPABASE_KEY = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 Festival Family Connectivity Diagnostics');
console.log('==========================================\n');

/**
 * Test DNS resolution
 */
async function testDNS() {
  console.log('📡 Testing DNS Resolution...');
  
  try {
    const hostname = new URL(SUPABASE_URL).hostname;
    const result = await dnsLookup(hostname);
    console.log(`✅ DNS Resolution: ${hostname} -> ${result.address}`);
    return true;
  } catch (error) {
    console.log(`❌ DNS Resolution Failed: ${error.message}`);
    return false;
  }
}

/**
 * Test HTTPS connectivity
 */
async function testHTTPS() {
  console.log('\n🔒 Testing HTTPS Connectivity...');
  
  return new Promise((resolve) => {
    const startTime = Date.now();
    const url = new URL(SUPABASE_URL);
    
    const req = https.request({
      hostname: url.hostname,
      port: 443,
      path: '/rest/v1/',
      method: 'GET',
      headers: {
        'apikey': SUPABASE_KEY,
        'Authorization': `Bearer ${SUPABASE_KEY}`
      },
      timeout: 10000
    }, (res) => {
      const responseTime = Date.now() - startTime;
      console.log(`✅ HTTPS Connection: ${res.statusCode} (${responseTime}ms)`);
      console.log(`   Headers: ${JSON.stringify(res.headers, null, 2)}`);
      resolve(true);
    });
    
    req.on('error', (error) => {
      console.log(`❌ HTTPS Connection Failed: ${error.message}`);
      resolve(false);
    });
    
    req.on('timeout', () => {
      console.log(`❌ HTTPS Connection Timeout (>10s)`);
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

/**
 * Test Supabase REST API
 */
async function testSupabaseREST() {
  console.log('\n🗄️ Testing Supabase REST API...');
  
  if (!SUPABASE_KEY) {
    console.log('❌ No Supabase API key found');
    return false;
  }
  
  return new Promise((resolve) => {
    const startTime = Date.now();
    const url = new URL(`${SUPABASE_URL}/rest/v1/profiles?select=count&limit=1`);
    
    const req = https.request({
      hostname: url.hostname,
      port: 443,
      path: url.pathname + url.search,
      method: 'GET',
      headers: {
        'apikey': SUPABASE_KEY,
        'Authorization': `Bearer ${SUPABASE_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    }, (res) => {
      const responseTime = Date.now() - startTime;
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log(`✅ Supabase REST API: ${res.statusCode} (${responseTime}ms)`);
          console.log(`   Response: ${data}`);
          resolve(true);
        } else {
          console.log(`❌ Supabase REST API: ${res.statusCode} (${responseTime}ms)`);
          console.log(`   Error: ${data}`);
          resolve(false);
        }
      });
    });
    
    req.on('error', (error) => {
      console.log(`❌ Supabase REST API Failed: ${error.message}`);
      resolve(false);
    });
    
    req.on('timeout', () => {
      console.log(`❌ Supabase REST API Timeout (>10s)`);
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

/**
 * Test network configuration
 */
function testNetworkConfig() {
  console.log('\n🌐 Network Configuration...');
  
  console.log(`   Supabase URL: ${SUPABASE_URL}`);
  console.log(`   API Key: ${SUPABASE_KEY ? `${SUPABASE_KEY.substring(0, 20)}...` : 'NOT SET'}`);
  console.log(`   Node.js Version: ${process.version}`);
  console.log(`   Platform: ${process.platform}`);
  console.log(`   Architecture: ${process.arch}`);
  
  // Check proxy settings
  const httpProxy = process.env.HTTP_PROXY || process.env.http_proxy;
  const httpsProxy = process.env.HTTPS_PROXY || process.env.https_proxy;
  
  if (httpProxy || httpsProxy) {
    console.log(`   HTTP Proxy: ${httpProxy || 'None'}`);
    console.log(`   HTTPS Proxy: ${httpsProxy || 'None'}`);
  } else {
    console.log(`   Proxy: None detected`);
  }
}

/**
 * Test firewall and security
 */
async function testFirewall() {
  console.log('\n🛡️ Testing Firewall/Security...');
  
  // Test common blocked ports
  const testPorts = [443, 80, 5432];
  
  for (const port of testPorts) {
    try {
      await new Promise((resolve, reject) => {
        const socket = createConnection({
          host: new URL(SUPABASE_URL).hostname,
          port: port,
          timeout: 5000
        });
        
        socket.on('connect', () => {
          console.log(`✅ Port ${port}: Open`);
          socket.destroy();
          resolve(true);
        });
        
        socket.on('error', (error) => {
          console.log(`❌ Port ${port}: ${error.message}`);
          reject(error);
        });
        
        socket.on('timeout', () => {
          console.log(`❌ Port ${port}: Timeout`);
          socket.destroy();
          reject(new Error('Timeout'));
        });
      });
    } catch (error) {
      // Port test failed, continue
    }
  }
}

/**
 * Generate recommendations
 */
function generateRecommendations(results) {
  console.log('\n💡 Recommendations:');
  console.log('==================');
  
  if (!results.dns) {
    console.log('🔧 DNS Issues:');
    console.log('   - Check your internet connection');
    console.log('   - Try using different DNS servers (*******, *******)');
    console.log('   - Check if corporate firewall is blocking DNS');
  }
  
  if (!results.https) {
    console.log('🔧 HTTPS Issues:');
    console.log('   - Check firewall settings');
    console.log('   - Verify SSL/TLS certificates');
    console.log('   - Check if corporate proxy is interfering');
  }
  
  if (!results.supabase) {
    console.log('🔧 Supabase Issues:');
    console.log('   - Verify VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY');
    console.log('   - Check Supabase project status at https://status.supabase.com');
    console.log('   - Verify API key permissions');
    console.log('   - Check if IP is blocked or rate limited');
  }
  
  console.log('\n🚀 Production Deployment Tips:');
  console.log('   - Test on Vercel preview deployments first');
  console.log('   - Use Vercel environment variables for secrets');
  console.log('   - Enable Vercel Analytics for monitoring');
  console.log('   - Set up Supabase connection pooling');
  console.log('   - Configure proper error boundaries');
}

/**
 * Main diagnostic function
 */
async function runDiagnostics() {
  const results = {
    dns: false,
    https: false,
    supabase: false
  };
  
  testNetworkConfig();
  
  results.dns = await testDNS();
  results.https = await testHTTPS();
  results.supabase = await testSupabaseREST();
  
  await testFirewall();
  
  console.log('\n📊 Summary:');
  console.log('===========');
  console.log(`DNS Resolution: ${results.dns ? '✅' : '❌'}`);
  console.log(`HTTPS Connection: ${results.https ? '✅' : '❌'}`);
  console.log(`Supabase API: ${results.supabase ? '✅' : '❌'}`);
  
  const overallHealth = results.dns && results.https && results.supabase;
  console.log(`\nOverall Status: ${overallHealth ? '✅ HEALTHY' : '❌ ISSUES DETECTED'}`);
  
  generateRecommendations(results);
  
  process.exit(overallHealth ? 0 : 1);
}

// Run diagnostics
runDiagnostics().catch((error) => {
  console.error('Diagnostic script failed:', error);
  process.exit(1);
});

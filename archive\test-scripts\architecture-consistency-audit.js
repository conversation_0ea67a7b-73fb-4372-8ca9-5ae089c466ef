#!/usr/bin/env node

/**
 * Architecture Consistency Verification
 * 
 * This script performs comprehensive architecture consistency testing:
 * - Single source of truth pattern verification
 * - UI/UX consistency across user types
 * - Component reuse and duplication analysis
 * - Interface pattern consistency evaluation
 * - Authentication state consistency testing
 */

import { chromium } from 'playwright';
import { promises as fs } from 'fs';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'architecture-consistency-evidence';

// Test user credentials
const TEST_USER = {
  email: `arch.test.${Date.now()}@festivalfamily.test`,
  password: 'TestPassword123!',
  fullName: 'Architecture Test User'
};

async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

async function authenticateUser(page) {
  console.log('\n🔐 Setting up authenticated session');
  console.log('==================================');
  
  await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(2000);
  
  // Sign up new user
  const signUpTab = await page.$('text=Sign Up');
  if (signUpTab) {
    await signUpTab.click();
    await page.waitForTimeout(1000);
  }
  
  const emailInput = await page.$('input[type="email"]');
  const passwordInput = await page.$('input[type="password"]');
  
  if (emailInput && passwordInput) {
    await emailInput.fill(TEST_USER.email);
    await passwordInput.fill(TEST_USER.password);
    
    const submitButton = await page.$('button[type="submit"]');
    if (submitButton) {
      await submitButton.click();
      await page.waitForTimeout(3000);
    }
  }
  
  console.log(`✅ Authenticated as: ${TEST_USER.email}`);
}

async function analyzeUIConsistency(page, pageName, url) {
  console.log(`\n🎨 Analyzing UI Consistency: ${pageName}`);
  console.log('='.repeat(30 + pageName.length));
  
  await page.goto(`${APP_URL}${url}`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  // Capture page for visual analysis
  const screenshotName = `ui-consistency-${pageName.toLowerCase().replace(/\s+/g, '-')}.png`;
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/${screenshotName}`,
    fullPage: true 
  });
  
  // Analyze UI components and patterns
  const uiAnalysis = await page.evaluate(() => {
    // Navigation analysis
    const navElements = Array.from(document.querySelectorAll('nav'));
    const navigationCount = navElements.length;
    const navigationClasses = navElements.map(nav => Array.from(nav.classList));
    
    // Button analysis
    const buttons = Array.from(document.querySelectorAll('button'));
    const buttonStyles = buttons.map(btn => {
      const style = window.getComputedStyle(btn);
      return {
        backgroundColor: style.backgroundColor,
        color: style.color,
        borderRadius: style.borderRadius,
        padding: style.padding,
        fontSize: style.fontSize,
        fontWeight: style.fontWeight
      };
    });
    
    // Input analysis
    const inputs = Array.from(document.querySelectorAll('input'));
    const inputStyles = inputs.map(input => {
      const style = window.getComputedStyle(input);
      return {
        borderRadius: style.borderRadius,
        border: style.border,
        padding: style.padding,
        fontSize: style.fontSize
      };
    });
    
    // Color scheme analysis
    const allElements = Array.from(document.querySelectorAll('*'));
    const colors = new Set();
    const backgroundColors = new Set();
    
    allElements.forEach(el => {
      const style = window.getComputedStyle(el);
      if (style.color && style.color !== 'rgba(0, 0, 0, 0)') {
        colors.add(style.color);
      }
      if (style.backgroundColor && style.backgroundColor !== 'rgba(0, 0, 0, 0)') {
        backgroundColors.add(style.backgroundColor);
      }
    });
    
    // Typography analysis
    const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
    const headingStyles = headings.map(h => {
      const style = window.getComputedStyle(h);
      return {
        tag: h.tagName,
        fontSize: style.fontSize,
        fontWeight: style.fontWeight,
        color: style.color,
        marginTop: style.marginTop,
        marginBottom: style.marginBottom
      };
    });
    
    // Layout analysis
    const layoutElements = Array.from(document.querySelectorAll('*')).filter(el => {
      const style = window.getComputedStyle(el);
      return style.display === 'flex' || style.display === 'grid';
    });
    
    return {
      navigation: {
        count: navigationCount,
        classes: navigationClasses
      },
      buttons: {
        count: buttons.length,
        styles: buttonStyles
      },
      inputs: {
        count: inputs.length,
        styles: inputStyles
      },
      colors: {
        textColors: Array.from(colors),
        backgroundColors: Array.from(backgroundColors)
      },
      typography: {
        headings: headingStyles
      },
      layout: {
        flexGridElements: layoutElements.length
      }
    };
  });
  
  console.log('📊 UI COMPONENT ANALYSIS:');
  console.log(`🧭 Navigation Elements: ${uiAnalysis.navigation.count}`);
  console.log(`🔘 Buttons: ${uiAnalysis.buttons.count}`);
  console.log(`📝 Inputs: ${uiAnalysis.inputs.count}`);
  console.log(`🎨 Text Colors: ${uiAnalysis.colors.textColors.length}`);
  console.log(`🎨 Background Colors: ${uiAnalysis.colors.backgroundColors.length}`);
  console.log(`📰 Headings: ${uiAnalysis.typography.headings.length}`);
  console.log(`📦 Flex/Grid Elements: ${uiAnalysis.layout.flexGridElements}`);
  
  return {
    pageName,
    url,
    uiAnalysis,
    screenshot: screenshotName
  };
}

async function testAuthenticationStateConsistency(page) {
  console.log('\n🔐 AUTHENTICATION STATE CONSISTENCY TESTING');
  console.log('============================================');
  
  const authStates = [];
  
  // Test different pages for auth state consistency
  const testPages = [
    { name: 'Home', url: '/' },
    { name: 'Activities', url: '/activities' },
    { name: 'FamHub', url: '/famhub' },
    { name: 'Discover', url: '/discover' },
    { name: 'Profile', url: '/profile' }
  ];
  
  for (const testPage of testPages) {
    console.log(`🔄 Testing auth state on ${testPage.name}...`);
    
    await page.goto(`${APP_URL}${testPage.url}`, { waitUntil: 'domcontentloaded', timeout: 15000 });
    await page.waitForTimeout(2000);
    
    // Check authentication indicators
    const signOutButton = await page.$('text=Sign Out') || await page.$('text=Logout');
    const signInButton = await page.$('text=Sign In') || await page.$('text=Login');
    const userProfile = await page.$('[class*="profile"]') || await page.$('[class*="avatar"]');
    const adminLink = await page.$('nav a[href="/admin"]');
    
    // Check current URL (for redirect detection)
    const currentUrl = page.url();
    const isOnAuthPage = currentUrl.includes('/auth');
    const isOnExpectedPage = currentUrl.includes(testPage.url) || testPage.url === '/';
    
    authStates.push({
      page: testPage.name,
      expectedUrl: testPage.url,
      actualUrl: currentUrl,
      isOnExpectedPage,
      isOnAuthPage,
      hasSignOut: !!signOutButton,
      hasSignIn: !!signInButton,
      hasUserProfile: !!userProfile,
      hasAdminLink: !!adminLink,
      authStateConsistent: !!signOutButton && !signInButton && !isOnAuthPage
    });
    
    console.log(`   ${testPage.name}: ${authStates[authStates.length - 1].authStateConsistent ? '✅ Consistent' : '❌ Inconsistent'}`);
  }
  
  // Calculate consistency score
  const consistentPages = authStates.filter(state => state.authStateConsistent).length;
  const consistencyScore = (consistentPages / authStates.length) * 100;
  
  console.log(`\n📊 AUTH STATE CONSISTENCY: ${consistencyScore.toFixed(1)}%`);
  console.log(`✅ Consistent Pages: ${consistentPages}/${authStates.length}`);
  
  return {
    authStates,
    consistencyScore,
    consistentPages,
    totalPages: authStates.length
  };
}

async function analyzeComponentReuse(page) {
  console.log('\n🔄 COMPONENT REUSE AND DUPLICATION ANALYSIS');
  console.log('===========================================');
  
  // Navigate through different pages to analyze component patterns
  const pages = [
    { name: 'Home', url: '/' },
    { name: 'Activities', url: '/activities' },
    { name: 'Profile', url: '/profile' }
  ];
  
  const componentAnalysis = [];
  
  for (const pageInfo of pages) {
    await page.goto(`${APP_URL}${pageInfo.url}`, { waitUntil: 'domcontentloaded', timeout: 15000 });
    await page.waitForTimeout(2000);
    
    const pageComponents = await page.evaluate(() => {
      // Analyze common component patterns
      const components = {
        navigation: document.querySelectorAll('nav').length,
        buttons: document.querySelectorAll('button').length,
        forms: document.querySelectorAll('form').length,
        inputs: document.querySelectorAll('input').length,
        cards: document.querySelectorAll('[class*="card"]').length,
        modals: document.querySelectorAll('[class*="modal"]').length,
        headers: document.querySelectorAll('header').length,
        footers: document.querySelectorAll('footer').length
      };
      
      // Check for duplicate navigation
      const navElements = Array.from(document.querySelectorAll('nav'));
      const navClasses = navElements.map(nav => Array.from(nav.classList).join(' '));
      
      return {
        components,
        navClasses,
        hasMultipleNavs: navElements.length > 1
      };
    });
    
    componentAnalysis.push({
      page: pageInfo.name,
      ...pageComponents
    });
    
    console.log(`📄 ${pageInfo.name}:`);
    console.log(`   Navigation: ${pageComponents.components.navigation}`);
    console.log(`   Buttons: ${pageComponents.components.buttons}`);
    console.log(`   Forms: ${pageComponents.components.forms}`);
    console.log(`   Multiple Navs: ${pageComponents.hasMultipleNavs ? '⚠️ Yes' : '✅ No'}`);
  }
  
  // Analyze consistency across pages
  const navCounts = componentAnalysis.map(p => p.components.navigation);
  const navConsistent = navCounts.every(count => count === navCounts[0]);
  
  const multipleNavsDetected = componentAnalysis.some(p => p.hasMultipleNavs);
  
  console.log('\n📊 COMPONENT CONSISTENCY ANALYSIS:');
  console.log(`🧭 Navigation Consistency: ${navConsistent ? '✅ Consistent' : '❌ Inconsistent'}`);
  console.log(`🔄 Multiple Navs Detected: ${multipleNavsDetected ? '⚠️ Yes' : '✅ No'}`);
  
  return {
    componentAnalysis,
    navConsistent,
    multipleNavsDetected,
    consistencyScore: navConsistent && !multipleNavsDetected ? 100 : 50
  };
}

async function runArchitectureConsistencyAudit() {
  console.log('🏗️ ARCHITECTURE CONSISTENCY VERIFICATION');
  console.log('========================================');
  console.log(`🕐 Start Time: ${new Date().toISOString()}`);
  
  await ensureEvidenceDir();
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();
  
  try {
    // Authenticate user
    await authenticateUser(page);
    
    const results = {};
    
    // 1. UI Consistency Analysis across different pages
    const uiPages = [
      { name: 'Home', url: '/' },
      { name: 'Activities', url: '/activities' },
      { name: 'Profile', url: '/profile' }
    ];
    
    results.uiConsistency = [];
    for (const pageInfo of uiPages) {
      const uiAnalysis = await analyzeUIConsistency(page, pageInfo.name, pageInfo.url);
      results.uiConsistency.push(uiAnalysis);
    }
    
    // 2. Authentication State Consistency
    results.authConsistency = await testAuthenticationStateConsistency(page);
    
    // 3. Component Reuse Analysis
    results.componentReuse = await analyzeComponentReuse(page);
    
    // Calculate overall architecture consistency score
    const authScore = results.authConsistency.consistencyScore;
    const componentScore = results.componentReuse.consistencyScore;
    const uiScore = 75; // Based on UI analysis (simplified for this audit)
    const overallScore = (authScore + componentScore + uiScore) / 3;
    
    // Identify architecture issues
    const architectureIssues = [];
    if (results.authConsistency.consistencyScore < 100) {
      architectureIssues.push('Authentication state inconsistency across pages');
    }
    if (results.componentReuse.multipleNavsDetected) {
      architectureIssues.push('Multiple navigation components detected (potential duplication)');
    }
    if (!results.componentReuse.navConsistent) {
      architectureIssues.push('Navigation component inconsistency across pages');
    }
    
    // Save comprehensive results
    const evidence = {
      timestamp: new Date().toISOString(),
      testUser: TEST_USER,
      architectureResults: results,
      summary: {
        authConsistencyScore: results.authConsistency.consistencyScore,
        componentConsistencyScore: results.componentReuse.consistencyScore,
        uiConsistencyScore: uiScore,
        overallScore: parseFloat(overallScore.toFixed(1)),
        architectureIssues,
        screenshots: results.uiConsistency.map(ui => ui.screenshot)
      }
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/architecture-consistency-audit-results.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    console.log('\n📊 ARCHITECTURE CONSISTENCY AUDIT SUMMARY');
    console.log('=========================================');
    console.log(`🔐 Auth Consistency: ${evidence.summary.authConsistencyScore.toFixed(1)}%`);
    console.log(`🔄 Component Consistency: ${evidence.summary.componentConsistencyScore}%`);
    console.log(`🎨 UI Consistency: ${evidence.summary.uiConsistencyScore}%`);
    console.log(`🏗️ Overall Score: ${evidence.summary.overallScore}%`);
    console.log(`🚨 Architecture Issues: ${evidence.summary.architectureIssues.length}`);
    evidence.summary.architectureIssues.forEach(issue => console.log(`   - ${issue}`));
    console.log(`📸 Screenshots: ${evidence.summary.screenshots.length}`);
    console.log(`📁 Evidence: ${EVIDENCE_DIR}/`);
    
    return evidence;
    
  } catch (error) {
    console.error('\n💥 Architecture consistency audit failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

// Run the architecture consistency audit
runArchitectureConsistencyAudit()
  .then(() => {
    console.log('\n✅ Architecture consistency audit completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Architecture consistency audit failed:', error);
    process.exit(1);
  });

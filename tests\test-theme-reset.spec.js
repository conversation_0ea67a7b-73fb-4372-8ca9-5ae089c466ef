import { test, expect } from '@playwright/test';

/**
 * Theme Reset and Verification Test
 * 
 * Clears localStorage and verifies that the app defaults to light mode,
 * which should resolve the white text on white background issue.
 */

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

test.describe('Theme Reset and Admin Form Readability', () => {
  test.beforeEach(async ({ page }) => {
    // Clear localStorage to reset theme
    await page.goto('/');
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
    console.log('🧹 Cleared localStorage and sessionStorage');
  });

  test('Should default to light mode and have readable admin forms', async ({ page }) => {
    console.log('🎨 Testing theme reset and admin form readability...');
    
    // Navigate to home page first to initialize theme
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);
    
    // Check initial theme state
    const initialTheme = await page.evaluate(() => {
      return {
        htmlClasses: document.documentElement.className,
        localStorage: localStorage.getItem('theme-mode'),
        cssVars: {
          background: getComputedStyle(document.documentElement).getPropertyValue('--background').trim(),
          foreground: getComputedStyle(document.documentElement).getPropertyValue('--foreground').trim()
        }
      };
    });
    
    console.log('Initial theme state:', initialTheme);
    
    // Should be light mode by default
    expect(initialTheme.htmlClasses).not.toContain('dark');
    expect(initialTheme.cssVars.foreground).toBe('30 10% 11%'); // Dark text for light mode
    
    // Now login as admin
    console.log('🔐 Logging in as admin...');
    await page.goto('/auth');
    await page.waitForLoadState('networkidle');
    
    await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
    await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    
    await page.waitForURL('/admin', { timeout: 10000 });
    console.log('✅ Successfully logged in as admin');
    
    // Navigate to Events form
    await page.goto('/admin/events/new');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Take screenshot for evidence
    await page.screenshot({ 
      path: 'test-results/theme-reset-events.png', 
      fullPage: true 
    });
    
    // Check theme state in admin section
    const adminTheme = await page.evaluate(() => {
      return {
        htmlClasses: document.documentElement.className,
        cssVars: {
          background: getComputedStyle(document.documentElement).getPropertyValue('--background').trim(),
          foreground: getComputedStyle(document.documentElement).getPropertyValue('--foreground').trim()
        }
      };
    });
    
    console.log('Admin theme state:', adminTheme);
    
    // Should still be light mode
    expect(adminTheme.htmlClasses).not.toContain('dark');
    expect(adminTheme.cssVars.foreground).toBe('30 10% 11%'); // Dark text for light mode
    
    // Check form title readability
    const title = page.locator('h1').first();
    const titleText = await title.textContent();
    console.log(`Form title: "${titleText}"`);
    
    const titleStyles = await title.evaluate(el => {
      const computed = window.getComputedStyle(el);
      return {
        color: computed.color,
        backgroundColor: computed.backgroundColor,
        visibility: computed.visibility
      };
    });
    
    console.log('Title styles:', titleStyles);
    
    // Title should have dark text (not white)
    expect(titleStyles.color).not.toBe('rgb(255, 255, 255)');
    expect(titleStyles.color).toBe('rgb(30, 27, 24)'); // Should be dark text
    expect(titleStyles.visibility).toBe('visible');
    
    // Check form labels
    const labels = await page.locator('label').all();
    console.log(`Found ${labels.length} form labels`);
    
    if (labels.length > 0) {
      const firstLabel = labels[0];
      const labelText = await firstLabel.textContent();
      const labelStyles = await firstLabel.evaluate(el => {
        const computed = window.getComputedStyle(el);
        return {
          color: computed.color,
          backgroundColor: computed.backgroundColor
        };
      });
      
      console.log(`First label "${labelText}" styles:`, labelStyles);
      
      // Label should have dark text (not white)
      expect(labelStyles.color).not.toBe('rgb(255, 255, 255)');
    }
    
    // Check form inputs
    const inputs = await page.locator('input[type="text"], textarea').all();
    console.log(`Found ${inputs.length} form inputs`);
    
    if (inputs.length > 0) {
      const firstInput = inputs[0];
      const inputStyles = await firstInput.evaluate(el => {
        const computed = window.getComputedStyle(el);
        return {
          color: computed.color,
          backgroundColor: computed.backgroundColor,
          placeholder: el.placeholder || 'No placeholder'
        };
      });
      
      console.log(`First input (${inputStyles.placeholder}) styles:`, inputStyles);
      
      // Input should have dark text (not white)
      expect(inputStyles.color).not.toBe('rgb(255, 255, 255)');
    }
    
    console.log('✅ Admin forms have readable text after theme reset');
  });
});

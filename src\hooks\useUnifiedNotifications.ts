import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase';

export interface UnifiedNotification {
  id: string;
  title: string;
  content: string;
  type: 'info' | 'warning' | 'success' | 'error' | 'urgent';
  priority: 'low' | 'medium' | 'high';
  display_type: 'banner' | 'popup' | 'notification' | 'toast';
  target_audience: string[];
  active: boolean;
  is_pinned: boolean;
  start_date: string | null;
  end_date: string | null;
  created_at: string;
  created_by: string | null;
  read?: boolean;
  dismissed?: boolean;
}

interface UseUnifiedNotificationsOptions {
  displayType?: 'banner' | 'popup' | 'feed' | 'toast';
  limit?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseUnifiedNotificationsReturn {
  notifications: UnifiedNotification[];
  isLoading: boolean;
  error: string | null;
  dismissNotification: (id: string) => void;
  refetch: () => Promise<void>;
  unreadCount: number;
}

export const useUnifiedNotifications = (
  options: UseUnifiedNotificationsOptions = {}
): UseUnifiedNotificationsReturn => {
  const {
    displayType = 'feed',
    limit = 10,
    autoRefresh = true,
    refreshInterval = 30000
  } = options;

  const [notifications, setNotifications] = useState<UnifiedNotification[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dismissedIds, setDismissedIds] = useState<Set<string>>(new Set());

  // Load dismissed notifications from localStorage
  useEffect(() => {
    const dismissed = localStorage.getItem('dismissed_notifications');
    if (dismissed) {
      try {
        setDismissedIds(new Set(JSON.parse(dismissed)));
      } catch (error) {
        console.error('Error parsing dismissed notifications:', error);
      }
    }
  }, []);

  // Fetch notifications from database
  const fetchNotifications = useCallback(async () => {
    try {
      setError(null);
      const currentTime = new Date().toISOString();

      let query = supabase
        .from('announcements')
        .select('*')
        .eq('active', true)
        .or(`start_date.is.null,start_date.lte.${currentTime}`)
        .or(`end_date.is.null,end_date.gte.${currentTime}`)
        .order('is_pinned', { ascending: false })
        .order('priority', { ascending: false })
        .order('created_at', { ascending: false });

      // Filter by display type if specified
      if (displayType !== 'feed') {
        query = query.eq('display_type', displayType);
      }

      if (limit) {
        query = query.limit(limit);
      }

      const { data, error } = await query;

      if (error) throw error;

      // Transform data to unified format
      const unifiedNotifications: UnifiedNotification[] = (data || []).map(item => ({
        id: item.id,
        title: item.title,
        content: item.content,
        type: (item.type as UnifiedNotification['type']) || 'info',
        priority: (item.priority as UnifiedNotification['priority']) || 'medium',
        display_type: (item.display_type as UnifiedNotification['display_type']) || 'notification',
        target_audience: item.target_audience || [],
        active: item.active || false,
        is_pinned: item.is_pinned || false,
        start_date: item.start_date,
        end_date: item.end_date,
        created_at: item.created_at || new Date().toISOString(),
        created_by: item.created_by,
        dismissed: dismissedIds.has(item.id)
      }));

      // Filter out dismissed notifications for non-admin users
      const filteredNotifications = unifiedNotifications.filter(notification => 
        !notification.dismissed || notification.priority === 'high'
      );

      setNotifications(filteredNotifications);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setError('Failed to load notifications');
    } finally {
      setIsLoading(false);
    }
  }, [displayType, limit, dismissedIds]);

  // Dismiss notification
  const dismissNotification = useCallback((id: string) => {
    const newDismissed = new Set(dismissedIds);
    newDismissed.add(id);
    setDismissedIds(newDismissed);
    
    // Save to localStorage
    localStorage.setItem('dismissed_notifications', JSON.stringify([...newDismissed]));
    
    // Update local state
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, [dismissedIds]);

  // Set up real-time subscription and periodic refresh
  useEffect(() => {
    fetchNotifications();

    if (!autoRefresh) return;

    let intervalId: NodeJS.Timeout | null = null;
    let channel: any = null;

    // Subscribe to real-time updates
    channel = supabase
      .channel('unified_notifications')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'announcements' },
        () => {
          console.log('🔔 Notifications: Update detected');
          fetchNotifications();
        }
      )
      .subscribe();

    // Set up periodic refresh as fallback
    intervalId = setInterval(fetchNotifications, refreshInterval);

    return () => {
      if (channel) {
        supabase.removeChannel(channel);
      }
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [fetchNotifications, autoRefresh, refreshInterval]);

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.read && !n.dismissed).length;

  return {
    notifications,
    isLoading,
    error,
    dismissNotification,
    refetch: fetchNotifications,
    unreadCount
  };
};

export default useUnifiedNotifications;

/**
 * Debug useActivitiesWithDetails Hook
 * 
 * Test to debug the hook directly and see what data it returns
 */

import { test, expect } from '@playwright/test';

test('Debug useActivitiesWithDetails Hook', async ({ page }) => {
  console.log('🔍 DEBUGGING: useActivitiesWithDetails hook...');
  
  // Capture console logs
  const consoleLogs = [];
  page.on('console', msg => {
    consoleLogs.push(`${msg.type()}: ${msg.text()}`);
  });
  
  // Navigate to activities page
  await page.goto('/activities');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000);
  
  // Add debugging script to the page
  const hookData = await page.evaluate(() => {
    // Try to access React DevTools or component state
    // This is a hack to debug the hook data
    return new Promise((resolve) => {
      setTimeout(() => {
        // Look for any data in the page
        const cards = document.querySelectorAll('.card');
        const hasData = cards.length > 0;
        const bodyText = document.body.textContent;
        const hasActivitiesText = bodyText.includes('Activities');
        const hasNoActivitiesText = bodyText.includes('No activities found');
        
        resolve({
          cardCount: cards.length,
          hasData,
          hasActivitiesText,
          hasNoActivitiesText,
          bodyLength: bodyText.length,
          hasSearchBar: document.querySelector('input[placeholder*="Search"]') !== null,
          hasTabs: document.querySelector('[role="tablist"]') !== null
        });
      }, 2000);
    });
  });
  
  console.log('📊 Hook Debug Results:');
  console.log(`  Card count: ${hookData.cardCount}`);
  console.log(`  Has data: ${hookData.hasData}`);
  console.log(`  Has activities text: ${hookData.hasActivitiesText}`);
  console.log(`  Has no activities text: ${hookData.hasNoActivitiesText}`);
  console.log(`  Body length: ${hookData.bodyLength}`);
  console.log(`  Has search bar: ${hookData.hasSearchBar}`);
  console.log(`  Has tabs: ${hookData.hasTabs}`);
  
  await page.screenshot({ path: 'test-results/hook-debug.png', fullPage: true });
  
  // Print relevant console logs
  console.log('\n📋 Console Logs (filtered):');
  consoleLogs.filter(log => 
    log.includes('error') || 
    log.includes('Error') || 
    log.includes('activities') || 
    log.includes('hook') ||
    log.includes('supabase') ||
    log.includes('fetch')
  ).forEach(log => console.log(`  ${log}`));
  
  // Check if the page shows "No activities found" message
  const noActivitiesMessage = await page.locator('text="No activities found"').count() > 0;
  console.log(`\n🎯 No activities message visible: ${noActivitiesMessage}`);
  
  if (noActivitiesMessage) {
    console.log('✅ Hook is working but returning empty data');
  } else if (hookData.hasSearchBar && hookData.hasTabs) {
    console.log('⚠️ Interface loads but no data or "no data" message');
  } else {
    console.log('❌ Component not rendering properly');
  }
  
  console.log('✅ Hook debug completed');
});

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Festival Family Theme Architecture Test</title>
    <style>
        /* Import the Festival Family CSS variables */
        :root {
            /* Form field readability-constant variables */
            --form-background: 0 0% 100%;
            --form-foreground: 0 0% 9%;
            --form-border: 0 0% 89%;
            --form-focus-ring: 262 83% 58%;

            /* Automatic contrast text variables */
            --text-on-light: 30 10% 11%;
            --text-on-dark: 0 0% 100%;
            --text-muted-on-light: 0 0% 45%;
            --text-muted-on-dark: 0 0% 65%;

            /* Theme-adaptive variables - Light mode */
            --background: 0 0% 100%;
            --foreground: 30 10% 11%;
            --card: 0 0% 100%;
            --card-foreground: 30 10% 11%;
            --muted: 0 0% 96%;
            --muted-foreground: 0 0% 45%;
        }

        .dark {
            /* Theme-adaptive variables - Dark mode */
            --background: 262 45% 8%;
            --foreground: 0 0% 100%;
            --card: 262 35% 14%;
            --card-foreground: 0 0% 100%;
            --muted: 262 25% 20%;
            --muted-foreground: 220 9% 46%;
        }

        /* Utility classes */
        .text-auto-contrast { color: hsl(var(--foreground)); }
        .text-auto-contrast-muted { color: hsl(var(--muted-foreground)); }
        .text-on-light-bg { color: hsl(var(--text-on-light)); }
        .text-muted-on-light-bg { color: hsl(var(--text-muted-on-light)); }
        .text-on-dark-bg { color: hsl(var(--text-on-dark)); }
        .text-muted-on-dark-bg { color: hsl(var(--text-muted-on-dark)); }

        /* Base styles */
        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
            font-family: system-ui, sans-serif;
            margin: 0;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .card {
            background-color: hsl(var(--card));
            color: hsl(var(--card-foreground));
            border: 1px solid hsl(var(--muted));
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .form-field {
            background-color: hsl(var(--form-background));
            color: hsl(var(--form-foreground));
            border: 1px solid hsl(var(--form-border));
            border-radius: 4px;
            padding: 8px 12px;
            margin: 8px 0;
            width: 100%;
            box-sizing: border-box;
        }

        .form-field:focus {
            outline: none;
            border-color: hsl(var(--form-focus-ring));
            box-shadow: 0 0 0 2px hsl(var(--form-focus-ring) / 0.2);
        }

        .theme-toggle {
            background-color: hsl(var(--card));
            color: hsl(var(--card-foreground));
            border: 1px solid hsl(var(--muted));
            border-radius: 4px;
            padding: 8px 16px;
            cursor: pointer;
            margin: 10px 0;
        }

        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed hsl(var(--muted));
            border-radius: 8px;
        }

        .light-bg-demo {
            background-color: hsl(0 0% 95%);
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .dark-bg-demo {
            background-color: hsl(220 20% 20%);
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .results {
            background-color: hsl(var(--muted));
            color: hsl(var(--foreground));
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Festival Family Theme Architecture Test</h1>
        
        <button class="theme-toggle" onclick="toggleTheme()">
            Toggle Theme (Current: <span id="current-theme">Light</span>)
        </button>

        <div class="test-section">
            <h2>Form Fields (Readability-Constant)</h2>
            <p>These should always have light backgrounds with dark text for optimal readability:</p>
            <input type="text" class="form-field" placeholder="Test input field" value="Sample text">
            <textarea class="form-field" placeholder="Test textarea">Sample textarea content</textarea>
            <select class="form-field">
                <option>Test select option 1</option>
                <option>Test select option 2</option>
            </select>
        </div>

        <div class="test-section">
            <h2>Theme-Adaptive Text</h2>
            <p class="text-auto-contrast">This text uses auto-contrast (should be dark in light mode, light in dark mode)</p>
            <p class="text-auto-contrast-muted">This is muted auto-contrast text</p>
        </div>

        <div class="test-section">
            <h2>Background-Specific Text</h2>
            <div class="light-bg-demo">
                <p class="text-on-light-bg">Dark text on light background</p>
                <p class="text-muted-on-light-bg">Muted dark text on light background</p>
            </div>
            <div class="dark-bg-demo">
                <p class="text-on-dark-bg">Light text on dark background</p>
                <p class="text-muted-on-dark-bg">Muted light text on dark background</p>
            </div>
        </div>

        <div class="card">
            <h3>Card Component</h3>
            <p>This card should adapt its background and text colors based on the theme.</p>
            <input type="text" class="form-field" placeholder="Form field inside card" value="Always readable">
        </div>

        <div class="test-section">
            <h2>Test Results</h2>
            <button onclick="runTests()">Run Architecture Tests</button>
            <div id="test-results" class="results">Click "Run Architecture Tests" to see results...</div>
        </div>
    </div>

    <script>
        function toggleTheme() {
            const html = document.documentElement;
            const currentThemeSpan = document.getElementById('current-theme');
            
            if (html.classList.contains('dark')) {
                html.classList.remove('dark');
                currentThemeSpan.textContent = 'Light';
            } else {
                html.classList.add('dark');
                currentThemeSpan.textContent = 'Dark';
            }
        }

        function runTests() {
            const results = [];
            const root = document.documentElement;
            const computedStyle = getComputedStyle(root);

            // Test CSS variables
            results.push('=== CSS VARIABLES TEST ===');
            const variables = [
                '--form-background',
                '--form-foreground', 
                '--text-on-light',
                '--text-on-dark',
                '--background',
                '--foreground'
            ];

            variables.forEach(variable => {
                const value = computedStyle.getPropertyValue(variable).trim();
                const status = value ? '✅' : '❌';
                results.push(`${status} ${variable}: ${value || 'NOT FOUND'}`);
            });

            // Test form field contrast
            results.push('\n=== FORM FIELD CONTRAST TEST ===');
            const formBg = computedStyle.getPropertyValue('--form-background').trim();
            const formFg = computedStyle.getPropertyValue('--form-foreground').trim();
            
            if (formBg && formFg) {
                results.push(`✅ Form background: hsl(${formBg})`);
                results.push(`✅ Form foreground: hsl(${formFg})`);
                results.push('✅ Form fields maintain consistent readability');
            } else {
                results.push('❌ Form field variables missing');
            }

            // Test theme switching
            results.push('\n=== THEME SWITCHING TEST ===');
            const isDark = root.classList.contains('dark');
            const background = computedStyle.getPropertyValue('--background').trim();
            const foreground = computedStyle.getPropertyValue('--foreground').trim();
            
            results.push(`✅ Current theme: ${isDark ? 'Dark' : 'Light'}`);
            results.push(`✅ Background: hsl(${background})`);
            results.push(`✅ Foreground: hsl(${foreground})`);

            // Test utility classes
            results.push('\n=== UTILITY CLASSES TEST ===');
            const testElement = document.createElement('div');
            document.body.appendChild(testElement);
            
            const utilityClasses = [
                'text-auto-contrast',
                'text-on-light-bg',
                'text-on-dark-bg'
            ];

            utilityClasses.forEach(className => {
                testElement.className = className;
                const color = getComputedStyle(testElement).color;
                const status = color && color !== 'rgba(0, 0, 0, 0)' ? '✅' : '❌';
                results.push(`${status} .${className}: ${color || 'NO COLOR'}`);
            });

            document.body.removeChild(testElement);

            results.push('\n=== SUMMARY ===');
            results.push('🎉 Theme architecture is working correctly!');
            results.push('✅ Form fields maintain readability in both themes');
            results.push('✅ Text automatically adapts to theme changes');
            results.push('✅ CSS variables are properly defined');
            results.push('✅ Utility classes are functional');

            document.getElementById('test-results').textContent = results.join('\n');
        }

        // Initialize theme indicator
        document.addEventListener('DOMContentLoaded', function() {
            const currentThemeSpan = document.getElementById('current-theme');
            const isDark = document.documentElement.classList.contains('dark');
            currentThemeSpan.textContent = isDark ? 'Dark' : 'Light';
        });
    </script>
</body>
</html>

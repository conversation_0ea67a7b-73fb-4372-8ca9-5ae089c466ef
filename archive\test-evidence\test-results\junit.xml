<testsuites id="" name="" tests="60" failures="22" skipped="0" errors="0" time="106.745293">
<testsuite name="database-verification.spec.ts" timestamp="2025-06-04T15:27:06.735Z" hostname="chromium" tests="10" failures="3" skipped="0" time="64.404" errors="0">
<testcase name="Festival Family Database Verification › Database Connectivity Test" classname="database-verification.spec.ts" time="5.722">
<failure message="database-verification.spec.ts:9:3 Database Connectivity Test" type="FAILURE">
<![CDATA[  [chromium] › database-verification.spec.ts:9:3 › Festival Family Database Verification › Database Connectivity Test 

    Error: expect(received).toContain(expected) // indexOf

    Expected substring: "Database connection successful"
    Received string:    "🔄 Testing connectivity..."

      30 |     
      31 |     // Verify connection is successful
    > 32 |     expect(result).toContain('Database connection successful');
         |                    ^
      33 |     
      34 |     console.log('✅ Database Connectivity Result:', result);
      35 |   });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:32:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-chromium\video.webm]]

[[ATTACHMENT|artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Table Existence Check" classname="database-verification.spec.ts" time="4.117">
<system-out>
<![CDATA[📋 Table Existence Results:
✅ profiles
                    ✅ 0 records
🆕 New Tables Status:
❌ content_management
❌ user_preferences
❌ emergency_contacts
❌ announcements
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Admin Functions Test" classname="database-verification.spec.ts" time="14.525">
<failure message="database-verification.spec.ts:95:3 Admin Functions Test" type="FAILURE">
<![CDATA[  [chromium] › database-verification.spec.ts:95:3 › Festival Family Database Verification › Admin Functions Test 

    Error: expect(received).toMatch(expected)

    Expected pattern: /(is_admin|is_super_admin|is_content_admin|can_manage_groups)/
    Received string:  "🔄 Testing admin functions..."

      114 |     
      115 |     // Verify at least some admin functions work
    > 116 |     expect(result).toMatch(/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/);
          |                    ^
      117 |   });
      118 |
      119 |   test('CRUD Operations Test', async ({ page }) => {
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:116:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[⚙️ Admin Functions Result: 🔄 Testing admin functions...

[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-chromium\video.webm]]

[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › CRUD Operations Test" classname="database-verification.spec.ts" time="3.762">
<failure message="database-verification.spec.ts:119:3 CRUD Operations Test" type="FAILURE">
<![CDATA[  [chromium] › database-verification.spec.ts:119:3 › Festival Family Database Verification › CRUD Operations Test 

    Error: expect(received).toMatch(expected)

    Expected pattern: /(READ|CREATE|UPDATE|DELETE)/
    Received string:  "🔄 Testing CRUD operations..."

      138 |     
      139 |     // Verify CRUD operations are accessible
    > 140 |     expect(result).toMatch(/(READ|CREATE|UPDATE|DELETE)/);
          |                    ^
      141 |   });
      142 |
      143 |   test('Profile System Test', async ({ page }) => {
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:140:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔄 CRUD Operations Result: 🔄 Testing CRUD operations...

[[ATTACHMENT|artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-chromium\video.webm]]

[[ATTACHMENT|artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Profile System Test" classname="database-verification.spec.ts" time="4.67">
<system-out>
<![CDATA[👤 Profile System Result: 🔄 Testing profile system...
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Admin Dashboard Authentication" classname="database-verification.spec.ts" time="2.751">
<system-out>
<![CDATA[🔐 Admin Authentication Result: ⚠️ No active session. Please sign in to test admin functionality.

🔗 Sign in at: http://localhost:5173/auth
⚠️ User needs to sign in for admin testing
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Admin Functions Status" classname="database-verification.spec.ts" time="5.207">
<system-out>
<![CDATA[⚙️ Admin Functions Status:
✅ is_admin
                        ✅ null
❌ is_super_admin
                        ❌ Error
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Content Management Test" classname="database-verification.spec.ts" time="6.109">
<system-out>
<![CDATA[📝 Content Management Result: 🔄 Testing content management...
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Emergency Management Test" classname="database-verification.spec.ts" time="12.092">
<system-out>
<![CDATA[🚨 Emergency Management Result: 🔄 Testing emergency management...
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Application Accessibility Test" classname="database-verification.spec.ts" time="5.449">
<system-out>
<![CDATA[🎪 Main Application Title: Festival Family
✅ Admin dashboard accessible
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="database-verification.spec.ts" timestamp="2025-06-04T15:27:06.735Z" hostname="firefox" tests="10" failures="10" skipped="0" time="0.445" errors="0">
<testcase name="Festival Family Database Verification › Database Connectivity Test" classname="database-verification.spec.ts" time="0.027">
<failure message="database-verification.spec.ts:9:3 Database Connectivity Test" type="FAILURE">
<![CDATA[  [firefox] › database-verification.spec.ts:9:3 › Festival Family Database Verification › Database Connectivity Test 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Table Existence Check" classname="database-verification.spec.ts" time="0.021">
<failure message="database-verification.spec.ts:37:3 Table Existence Check" type="FAILURE">
<![CDATA[  [firefox] › database-verification.spec.ts:37:3 › Festival Family Database Verification › Table Existence Check 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\database-verification-Fest-1c2ed-ation-Table-Existence-Check-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\database-verification-Fest-1c2ed-ation-Table-Existence-Check-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Admin Functions Test" classname="database-verification.spec.ts" time="0.025">
<failure message="database-verification.spec.ts:95:3 Admin Functions Test" type="FAILURE">
<![CDATA[  [firefox] › database-verification.spec.ts:95:3 › Festival Family Database Verification › Admin Functions Test 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › CRUD Operations Test" classname="database-verification.spec.ts" time="0.017">
<failure message="database-verification.spec.ts:119:3 CRUD Operations Test" type="FAILURE">
<![CDATA[  [firefox] › database-verification.spec.ts:119:3 › Festival Family Database Verification › CRUD Operations Test 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Profile System Test" classname="database-verification.spec.ts" time="0.078">
<failure message="database-verification.spec.ts:143:3 Profile System Test" type="FAILURE">
<![CDATA[  [firefox] › database-verification.spec.ts:143:3 › Festival Family Database Verification › Profile System Test 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\database-verification-Fest-c6ffe-ication-Profile-System-Test-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\database-verification-Fest-c6ffe-ication-Profile-System-Test-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Admin Dashboard Authentication" classname="database-verification.spec.ts" time="0.028">
<failure message="database-verification.spec.ts:173:3 Admin Dashboard Authentication" type="FAILURE">
<![CDATA[  [firefox] › database-verification.spec.ts:173:3 › Festival Family Admin Dashboard Tests › Admin Dashboard Authentication 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\database-verification-Fest-7c452-in-Dashboard-Authentication-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\database-verification-Fest-7c452-in-Dashboard-Authentication-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Admin Functions Status" classname="database-verification.spec.ts" time="0.068">
<failure message="database-verification.spec.ts:201:3 Admin Functions Status" type="FAILURE">
<![CDATA[  [firefox] › database-verification.spec.ts:201:3 › Festival Family Admin Dashboard Tests › Admin Functions Status 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\database-verification-Fest-b636f-ests-Admin-Functions-Status-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\database-verification-Fest-b636f-ests-Admin-Functions-Status-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Content Management Test" classname="database-verification.spec.ts" time="0.048">
<failure message="database-verification.spec.ts:237:3 Content Management Test" type="FAILURE">
<![CDATA[  [firefox] › database-verification.spec.ts:237:3 › Festival Family Admin Dashboard Tests › Content Management Test 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\database-verification-Fest-8075f-sts-Content-Management-Test-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\database-verification-Fest-8075f-sts-Content-Management-Test-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Emergency Management Test" classname="database-verification.spec.ts" time="0.112">
<failure message="database-verification.spec.ts:258:3 Emergency Management Test" type="FAILURE">
<![CDATA[  [firefox] › database-verification.spec.ts:258:3 › Festival Family Admin Dashboard Tests › Emergency Management Test 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\database-verification-Fest-6d4c2-s-Emergency-Management-Test-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\database-verification-Fest-6d4c2-s-Emergency-Management-Test-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Application Accessibility Test" classname="database-verification.spec.ts" time="0.021">
<failure message="database-verification.spec.ts:279:3 Application Accessibility Test" type="FAILURE">
<![CDATA[  [firefox] › database-verification.spec.ts:279:3 › Festival Family Admin Dashboard Tests › Application Accessibility Test 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\database-verification-Fest-eb12f-lication-Accessibility-Test-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\database-verification-Fest-eb12f-lication-Accessibility-Test-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="database-verification.spec.ts" timestamp="2025-06-04T15:27:06.735Z" hostname="webkit" tests="10" failures="2" skipped="0" time="69.12" errors="0">
<testcase name="Festival Family Database Verification › Database Connectivity Test" classname="database-verification.spec.ts" time="8.139">
<system-out>
<![CDATA[✅ Database Connectivity Result: ✅ Database connection successful!
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Table Existence Check" classname="database-verification.spec.ts" time="4.762">
<system-out>
<![CDATA[📋 Table Existence Results:
✅ profiles
                    ✅ 0 records
🆕 New Tables Status:
❌ content_management
❌ user_preferences
❌ emergency_contacts
❌ announcements
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Admin Functions Test" classname="database-verification.spec.ts" time="6.204">
<failure message="database-verification.spec.ts:95:3 Admin Functions Test" type="FAILURE">
<![CDATA[  [webkit] › database-verification.spec.ts:95:3 › Festival Family Database Verification › Admin Functions Test 

    Error: expect(received).toMatch(expected)

    Expected pattern: /(is_admin|is_super_admin|is_content_admin|can_manage_groups)/
    Received string:  "🔄 Testing admin functions..."

      114 |     
      115 |     // Verify at least some admin functions work
    > 116 |     expect(result).toMatch(/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/);
          |                    ^
      117 |   });
      118 |
      119 |   test('CRUD Operations Test', async ({ page }) => {
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:116:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[⚙️ Admin Functions Result: 🔄 Testing admin functions...

[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-webkit\test-failed-1.png]]

[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-webkit\video.webm]]

[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › CRUD Operations Test" classname="database-verification.spec.ts" time="6.923">
<failure message="database-verification.spec.ts:119:3 CRUD Operations Test" type="FAILURE">
<![CDATA[  [webkit] › database-verification.spec.ts:119:3 › Festival Family Database Verification › CRUD Operations Test 

    Error: expect(received).toMatch(expected)

    Expected pattern: /(READ|CREATE|UPDATE|DELETE)/
    Received string:  "🔄 Testing CRUD operations..."

      138 |     
      139 |     // Verify CRUD operations are accessible
    > 140 |     expect(result).toMatch(/(READ|CREATE|UPDATE|DELETE)/);
          |                    ^
      141 |   });
      142 |
      143 |   test('Profile System Test', async ({ page }) => {
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:140:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔄 CRUD Operations Result: 🔄 Testing CRUD operations...

[[ATTACHMENT|artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-webkit\test-failed-1.png]]

[[ATTACHMENT|artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-webkit\video.webm]]

[[ATTACHMENT|artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Profile System Test" classname="database-verification.spec.ts" time="5.844">
<system-out>
<![CDATA[👤 Profile System Result: 🔄 Testing profile system...
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Admin Dashboard Authentication" classname="database-verification.spec.ts" time="7.244">
<system-out>
<![CDATA[🔐 Admin Authentication Result: ⚠️ No active session. Please sign in to test admin functionality.

🔗 Sign in at: http://localhost:5173/auth
⚠️ User needs to sign in for admin testing
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Admin Functions Status" classname="database-verification.spec.ts" time="9.423">
<system-out>
<![CDATA[⚙️ Admin Functions Status:
✅ is_admin
                        ✅ null
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Content Management Test" classname="database-verification.spec.ts" time="6.307">
<system-out>
<![CDATA[📝 Content Management Result: 🔄 Testing content management...
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Emergency Management Test" classname="database-verification.spec.ts" time="6.817">
<system-out>
<![CDATA[🚨 Emergency Management Result: 🔄 Testing emergency management...
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Application Accessibility Test" classname="database-verification.spec.ts" time="7.457">
<system-out>
<![CDATA[🎪 Main Application Title: Festival Family
✅ Admin dashboard accessible
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="database-verification.spec.ts" timestamp="2025-06-04T15:27:06.735Z" hostname="Mobile Chrome" tests="10" failures="3" skipped="0" time="61.62" errors="0">
<testcase name="Festival Family Database Verification › Database Connectivity Test" classname="database-verification.spec.ts" time="5.616">
<failure message="database-verification.spec.ts:9:3 Database Connectivity Test" type="FAILURE">
<![CDATA[  [Mobile Chrome] › database-verification.spec.ts:9:3 › Festival Family Database Verification › Database Connectivity Test 

    Error: expect(received).toContain(expected) // indexOf

    Expected substring: "Database connection successful"
    Received string:    "🔄 Testing connectivity..."

      30 |     
      31 |     // Verify connection is successful
    > 32 |     expect(result).toContain('Database connection successful');
         |                    ^
      33 |     
      34 |     console.log('✅ Database Connectivity Result:', result);
      35 |   });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:32:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-Mobile-Chrome\video.webm]]

[[ATTACHMENT|artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Table Existence Check" classname="database-verification.spec.ts" time="16.992">
<system-out>
<![CDATA[📋 Table Existence Results:
✅ profiles
                    ✅ 0 records
🆕 New Tables Status:
❌ content_management
❌ user_preferences
❌ emergency_contacts
❌ announcements
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Admin Functions Test" classname="database-verification.spec.ts" time="4.053">
<failure message="database-verification.spec.ts:95:3 Admin Functions Test" type="FAILURE">
<![CDATA[  [Mobile Chrome] › database-verification.spec.ts:95:3 › Festival Family Database Verification › Admin Functions Test 

    Error: expect(received).toMatch(expected)

    Expected pattern: /(is_admin|is_super_admin|is_content_admin|can_manage_groups)/
    Received string:  "🔄 Testing admin functions..."

      114 |     
      115 |     // Verify at least some admin functions work
    > 116 |     expect(result).toMatch(/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/);
          |                    ^
      117 |   });
      118 |
      119 |   test('CRUD Operations Test', async ({ page }) => {
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:116:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[⚙️ Admin Functions Result: 🔄 Testing admin functions...

[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Chrome\video.webm]]

[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › CRUD Operations Test" classname="database-verification.spec.ts" time="3.815">
<failure message="database-verification.spec.ts:119:3 CRUD Operations Test" type="FAILURE">
<![CDATA[  [Mobile Chrome] › database-verification.spec.ts:119:3 › Festival Family Database Verification › CRUD Operations Test 

    Error: expect(received).toMatch(expected)

    Expected pattern: /(READ|CREATE|UPDATE|DELETE)/
    Received string:  "🔄 Testing CRUD operations..."

      138 |     
      139 |     // Verify CRUD operations are accessible
    > 140 |     expect(result).toMatch(/(READ|CREATE|UPDATE|DELETE)/);
          |                    ^
      141 |   });
      142 |
      143 |   test('Profile System Test', async ({ page }) => {
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:140:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔄 CRUD Operations Result: 🔄 Testing CRUD operations...

[[ATTACHMENT|artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Mobile-Chrome\video.webm]]

[[ATTACHMENT|artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Profile System Test" classname="database-verification.spec.ts" time="7.294">
<system-out>
<![CDATA[👤 Profile System Result: 🔄 Testing profile system...
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Admin Dashboard Authentication" classname="database-verification.spec.ts" time="6.268">
<system-out>
<![CDATA[🔐 Admin Authentication Result: ⚠️ No active session. Please sign in to test admin functionality.

🔗 Sign in at: http://localhost:5173/auth
⚠️ User needs to sign in for admin testing
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Admin Functions Status" classname="database-verification.spec.ts" time="7.71">
<system-out>
<![CDATA[⚙️ Admin Functions Status:
✅ is_admin
                        ✅ null
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Content Management Test" classname="database-verification.spec.ts" time="3.532">
<system-out>
<![CDATA[📝 Content Management Result: 🔄 Testing content management...
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Emergency Management Test" classname="database-verification.spec.ts" time="3.287">
<system-out>
<![CDATA[🚨 Emergency Management Result: 🔄 Testing emergency management...
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Application Accessibility Test" classname="database-verification.spec.ts" time="3.053">
<system-out>
<![CDATA[🎪 Main Application Title: Festival Family
✅ Admin dashboard accessible
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="database-verification.spec.ts" timestamp="2025-06-04T15:27:06.735Z" hostname="Mobile Safari" tests="10" failures="1" skipped="0" time="59.498" errors="0">
<testcase name="Festival Family Database Verification › Database Connectivity Test" classname="database-verification.spec.ts" time="7.7">
<system-out>
<![CDATA[✅ Database Connectivity Result: ✅ Database connection successful!
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Table Existence Check" classname="database-verification.spec.ts" time="4.368">
<system-out>
<![CDATA[📋 Table Existence Results:
✅ profiles
                    ✅ 0 records
🆕 New Tables Status:
❌ content_management
❌ user_preferences
❌ emergency_contacts
❌ announcements
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Admin Functions Test" classname="database-verification.spec.ts" time="5.62">
<failure message="database-verification.spec.ts:95:3 Admin Functions Test" type="FAILURE">
<![CDATA[  [Mobile Safari] › database-verification.spec.ts:95:3 › Festival Family Database Verification › Admin Functions Test 

    Error: expect(received).toMatch(expected)

    Expected pattern: /(is_admin|is_super_admin|is_content_admin|can_manage_groups)/
    Received string:  "🔄 Testing admin functions..."

      114 |     
      115 |     // Verify at least some admin functions work
    > 116 |     expect(result).toMatch(/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/);
          |                    ^
      117 |   });
      118 |
      119 |   test('CRUD Operations Test', async ({ page }) => {
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:116:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[⚙️ Admin Functions Result: 🔄 Testing admin functions...

[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Safari\video.webm]]

[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › CRUD Operations Test" classname="database-verification.spec.ts" time="6.57">
<system-out>
<![CDATA[🔄 CRUD Operations Result: ✅ Announcements READ: Working (1 records)
❌ Announcements CREATE: Could not find the 'is_active' column of 'announcements' in the schema cache
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Profile System Test" classname="database-verification.spec.ts" time="3.793">
<system-out>
<![CDATA[👤 Profile System Result: 🔄 Testing profile system...
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Admin Dashboard Authentication" classname="database-verification.spec.ts" time="4.748">
<system-out>
<![CDATA[🔐 Admin Authentication Result: ⚠️ No active session. Please sign in to test admin functionality.

🔗 Sign in at: http://localhost:5173/auth
⚠️ User needs to sign in for admin testing
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Admin Functions Status" classname="database-verification.spec.ts" time="5.871">
<system-out>
<![CDATA[⚙️ Admin Functions Status:
✅ is_admin
                        ✅ null
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Content Management Test" classname="database-verification.spec.ts" time="5.92">
<system-out>
<![CDATA[📝 Content Management Result: 🔄 Testing content management...
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Emergency Management Test" classname="database-verification.spec.ts" time="6.647">
<system-out>
<![CDATA[🚨 Emergency Management Result: 🔄 Testing emergency management...
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Application Accessibility Test" classname="database-verification.spec.ts" time="8.261">
<system-out>
<![CDATA[🎪 Main Application Title: Festival Family
✅ Admin dashboard accessible
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="database-verification.spec.ts" timestamp="2025-06-04T15:27:06.735Z" hostname="Tablet" tests="10" failures="3" skipped="0" time="63.614" errors="0">
<testcase name="Festival Family Database Verification › Database Connectivity Test" classname="database-verification.spec.ts" time="5.329">
<failure message="database-verification.spec.ts:9:3 Database Connectivity Test" type="FAILURE">
<![CDATA[  [Tablet] › database-verification.spec.ts:9:3 › Festival Family Database Verification › Database Connectivity Test 

    Error: expect(received).toContain(expected) // indexOf

    Expected substring: "Database connection successful"
    Received string:    "🔄 Testing connectivity..."

      30 |     
      31 |     // Verify connection is successful
    > 32 |     expect(result).toContain('Database connection successful');
         |                    ^
      33 |     
      34 |     console.log('✅ Database Connectivity Result:', result);
      35 |   });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:32:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-Tablet\test-failed-1.png]]

[[ATTACHMENT|artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-Tablet\video.webm]]

[[ATTACHMENT|artifacts\database-verification-Fest-8d422--Database-Connectivity-Test-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Table Existence Check" classname="database-verification.spec.ts" time="4.117">
<system-out>
<![CDATA[📋 Table Existence Results:
✅ profiles
                    ✅ 0 records
🆕 New Tables Status:
❌ content_management
❌ user_preferences
❌ emergency_contacts
❌ announcements
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Admin Functions Test" classname="database-verification.spec.ts" time="15.566">
<failure message="database-verification.spec.ts:95:3 Admin Functions Test" type="FAILURE">
<![CDATA[  [Tablet] › database-verification.spec.ts:95:3 › Festival Family Database Verification › Admin Functions Test 

    Error: expect(received).toMatch(expected)

    Expected pattern: /(is_admin|is_super_admin|is_content_admin|can_manage_groups)/
    Received string:  "🔄 Testing admin functions..."

      114 |     
      115 |     // Verify at least some admin functions work
    > 116 |     expect(result).toMatch(/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/);
          |                    ^
      117 |   });
      118 |
      119 |   test('CRUD Operations Test', async ({ page }) => {
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:116:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[⚙️ Admin Functions Result: 🔄 Testing admin functions...

[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Tablet\test-failed-1.png]]

[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Tablet\video.webm]]

[[ATTACHMENT|artifacts\database-verification-Fest-3a444-cation-Admin-Functions-Test-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › CRUD Operations Test" classname="database-verification.spec.ts" time="3.785">
<failure message="database-verification.spec.ts:119:3 CRUD Operations Test" type="FAILURE">
<![CDATA[  [Tablet] › database-verification.spec.ts:119:3 › Festival Family Database Verification › CRUD Operations Test 

    Error: expect(received).toMatch(expected)

    Expected pattern: /(READ|CREATE|UPDATE|DELETE)/
    Received string:  "🔄 Testing CRUD operations..."

      138 |     
      139 |     // Verify CRUD operations are accessible
    > 140 |     expect(result).toMatch(/(READ|CREATE|UPDATE|DELETE)/);
          |                    ^
      141 |   });
      142 |
      143 |   test('Profile System Test', async ({ page }) => {
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:140:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔄 CRUD Operations Result: 🔄 Testing CRUD operations...

[[ATTACHMENT|artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Tablet\test-failed-1.png]]

[[ATTACHMENT|artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Tablet\video.webm]]

[[ATTACHMENT|artifacts\database-verification-Fest-821aa-cation-CRUD-Operations-Test-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Festival Family Database Verification › Profile System Test" classname="database-verification.spec.ts" time="3.592">
<system-out>
<![CDATA[👤 Profile System Result: 🔄 Testing profile system...
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Admin Dashboard Authentication" classname="database-verification.spec.ts" time="2.68">
<system-out>
<![CDATA[🔐 Admin Authentication Result: ⚠️ No active session. Please sign in to test admin functionality.

🔗 Sign in at: http://localhost:5173/auth
⚠️ User needs to sign in for admin testing
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Admin Functions Status" classname="database-verification.spec.ts" time="5.716">
<system-out>
<![CDATA[⚙️ Admin Functions Status:
✅ is_admin
                        ✅ null
❌ is_super_admin
                        ❌ Error
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Content Management Test" classname="database-verification.spec.ts" time="5.927">
<system-out>
<![CDATA[📝 Content Management Result: 🔄 Testing content management...
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Emergency Management Test" classname="database-verification.spec.ts" time="6.832">
<system-out>
<![CDATA[🚨 Emergency Management Result: 🔄 Testing emergency management...
]]>
</system-out>
</testcase>
<testcase name="Festival Family Admin Dashboard Tests › Application Accessibility Test" classname="database-verification.spec.ts" time="10.07">
<system-out>
<![CDATA[🎪 Main Application Title: Festival Family
✅ Admin dashboard accessible
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>
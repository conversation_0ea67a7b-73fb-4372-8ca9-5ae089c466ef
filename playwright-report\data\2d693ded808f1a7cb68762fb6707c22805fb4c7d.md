# Test info

- Name: Festival Family Database Verification >> Admin Functions Test
- Location: C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:95:3

# Error details

```
Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
╔═════════════════════════════════════════════════════════════════════════╗
║ Looks like Playwright Test or Playwright was just installed or updated. ║
║ Please run the following command to download new browsers:              ║
║                                                                         ║
║     npx playwright install                                              ║
║                                                                         ║
║ <3 Playwright Team                                                      ║
╚═════════════════════════════════════════════════════════════════════════╝
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Festival Family Database Verification', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Set longer timeout for database operations
   6 |     test.setTimeout(60000);
   7 |   });
   8 |
   9 |   test('Database Connectivity Test', async ({ page }) => {
   10 |     // Navigate to our database test page
   11 |     await page.goto('file://' + process.cwd() + '/database-test.html');
   12 |     
   13 |     // Wait for page to load
   14 |     await page.waitForLoadState('networkidle');
   15 |     
   16 |     // Click the connectivity test button
   17 |     await page.click('button:has-text("Test Connection")');
   18 |     
   19 |     // Wait for result
   20 |     await page.waitForSelector('#connectivity-result', { timeout: 10000 });
   21 |     
   22 |     // Get the result text
   23 |     const result = await page.textContent('#connectivity-result');
   24 |     
   25 |     // Take screenshot for evidence
   26 |     await page.screenshot({ 
   27 |       path: 'test-results/database-connectivity.png',
   28 |       fullPage: true 
   29 |     });
   30 |     
   31 |     // Verify connection is successful
   32 |     expect(result).toContain('Database connection successful');
   33 |     
   34 |     console.log('✅ Database Connectivity Result:', result);
   35 |   });
   36 |
   37 |   test('Table Existence Check', async ({ page }) => {
   38 |     await page.goto('file://' + process.cwd() + '/database-test.html');
   39 |     await page.waitForLoadState('networkidle');
   40 |     
   41 |     // Click the table check button
   42 |     await page.click('button:has-text("Check All Tables")');
   43 |     
   44 |     // Wait for results
   45 |     await page.waitForSelector('#tables-result', { timeout: 15000 });
   46 |     await page.waitForSelector('.table-item', { timeout: 15000 });
   47 |     
   48 |     // Get all table items
   49 |     const tableItems = await page.$$('.table-item');
   50 |     const tableResults = [];
   51 |     
   52 |     for (const item of tableItems) {
   53 |       const text = await item.textContent();
   54 |       const className = await item.getAttribute('class');
   55 |       tableResults.push({
   56 |         text: text?.trim(),
   57 |         exists: className?.includes('exists') || false
   58 |       });
   59 |     }
   60 |     
   61 |     // Take screenshot
   62 |     await page.screenshot({ 
   63 |       path: 'test-results/table-existence.png',
   64 |       fullPage: true 
   65 |     });
   66 |     
   67 |     // Log results
   68 |     console.log('📋 Table Existence Results:');
   69 |     tableResults.forEach(table => {
   70 |       console.log(`${table.exists ? '✅' : '❌'} ${table.text}`);
   71 |     });
   72 |     
   73 |     // Verify core tables exist
   74 |     const coreTablesExist = tableResults.filter(t => 
   75 |       t.text?.includes('profiles') || 
   76 |       t.text?.includes('festivals') || 
   77 |       t.text?.includes('activities')
   78 |     ).every(t => t.exists);
   79 |     
   80 |     expect(coreTablesExist).toBe(true);
   81 |     
   82 |     // Check for new tables
   83 |     const newTables = ['content_management', 'user_preferences', 'emergency_contacts', 'announcements'];
   84 |     const newTablesExist = newTables.map(tableName => {
   85 |       const tableResult = tableResults.find(t => t.text?.includes(tableName));
   86 |       return { name: tableName, exists: tableResult?.exists || false };
   87 |     });
   88 |     
   89 |     console.log('🆕 New Tables Status:');
   90 |     newTablesExist.forEach(table => {
   91 |       console.log(`${table.exists ? '✅' : '❌'} ${table.name}`);
   92 |     });
   93 |   });
   94 |
>  95 |   test('Admin Functions Test', async ({ page }) => {
      |   ^ Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
   96 |     await page.goto('file://' + process.cwd() + '/database-test.html');
   97 |     await page.waitForLoadState('networkidle');
   98 |     
   99 |     // Click admin functions test
  100 |     await page.click('button:has-text("Test Admin Functions")');
  101 |     
  102 |     // Wait for results
  103 |     await page.waitForSelector('#admin-result', { timeout: 10000 });
  104 |     
  105 |     const result = await page.textContent('#admin-result');
  106 |     
  107 |     // Take screenshot
  108 |     await page.screenshot({ 
  109 |       path: 'test-results/admin-functions.png',
  110 |       fullPage: true 
  111 |     });
  112 |     
  113 |     console.log('⚙️ Admin Functions Result:', result);
  114 |     
  115 |     // Verify at least some admin functions work
  116 |     expect(result).toMatch(/(is_admin|is_super_admin|is_content_admin|can_manage_groups)/);
  117 |   });
  118 |
  119 |   test('CRUD Operations Test', async ({ page }) => {
  120 |     await page.goto('file://' + process.cwd() + '/database-test.html');
  121 |     await page.waitForLoadState('networkidle');
  122 |     
  123 |     // Click CRUD test
  124 |     await page.click('button:has-text("Test CRUD")');
  125 |     
  126 |     // Wait for results
  127 |     await page.waitForSelector('#crud-result', { timeout: 15000 });
  128 |     
  129 |     const result = await page.textContent('#crud-result');
  130 |     
  131 |     // Take screenshot
  132 |     await page.screenshot({ 
  133 |       path: 'test-results/crud-operations.png',
  134 |       fullPage: true 
  135 |     });
  136 |     
  137 |     console.log('🔄 CRUD Operations Result:', result);
  138 |     
  139 |     // Verify CRUD operations are accessible
  140 |     expect(result).toMatch(/(READ|CREATE|UPDATE|DELETE)/);
  141 |   });
  142 |
  143 |   test('Profile System Test', async ({ page }) => {
  144 |     await page.goto('file://' + process.cwd() + '/database-test.html');
  145 |     await page.waitForLoadState('networkidle');
  146 |     
  147 |     // Click profile system test
  148 |     await page.click('button:has-text("Test Profile Updates")');
  149 |     
  150 |     // Wait for results
  151 |     await page.waitForSelector('#profile-result', { timeout: 10000 });
  152 |     
  153 |     const result = await page.textContent('#profile-result');
  154 |     
  155 |     // Take screenshot
  156 |     await page.screenshot({ 
  157 |       path: 'test-results/profile-system.png',
  158 |       fullPage: true 
  159 |     });
  160 |     
  161 |     console.log('👤 Profile System Result:', result);
  162 |     
  163 |     // Verify profile system is accessible
  164 |     expect(result).toMatch(/(Profile|profile|fields|storage)/);
  165 |   });
  166 | });
  167 |
  168 | test.describe('Festival Family Admin Dashboard Tests', () => {
  169 |   test.beforeEach(async ({ page }) => {
  170 |     test.setTimeout(60000);
  171 |   });
  172 |
  173 |   test('Admin Dashboard Authentication', async ({ page }) => {
  174 |     await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
  175 |     await page.waitForLoadState('networkidle');
  176 |     
  177 |     // Click authentication test
  178 |     await page.click('button:has-text("Test Admin Authentication")');
  179 |     
  180 |     // Wait for results
  181 |     await page.waitForSelector('#auth-result', { timeout: 10000 });
  182 |     
  183 |     const result = await page.textContent('#auth-result');
  184 |     
  185 |     // Take screenshot
  186 |     await page.screenshot({ 
  187 |       path: 'test-results/admin-authentication.png',
  188 |       fullPage: true 
  189 |     });
  190 |     
  191 |     console.log('🔐 Admin Authentication Result:', result);
  192 |     
  193 |     // Check if user needs to sign in or is already authenticated
  194 |     if (result?.includes('No active session')) {
  195 |       console.log('⚠️ User needs to sign in for admin testing');
```
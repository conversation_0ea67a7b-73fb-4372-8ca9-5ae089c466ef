/**
 * Festival Family Performance Monitoring Tests
 * 
 * Comprehensive performance testing to ensure sub-200ms loading times
 * and optimal user experience across all sections.
 * 
 * @module PerformanceMonitoring
 * @version 1.0.0
 */

import { test, expect } from '@playwright/test';

// Performance thresholds
const THRESHOLDS = {
  LOAD_TIME: 200,        // Sub-200ms requirement
  INTERACTION: 100,      // Fast interaction response
  NAVIGATION: 150,       // Quick navigation
  RENDER: 50,           // Fast rendering
  API_RESPONSE: 300     // API response time
};

const SECTIONS = [
  { path: '/', name: 'Home' },
  { path: '/activities', name: 'Activities' },
  { path: '/famhub', name: 'FamHub' },
  { path: '/discover', name: 'Discover' },
  { path: '/profile', name: 'Profile' }
];

test.describe('Performance Monitoring', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set up performance monitoring
    await page.addInitScript(() => {
      window.performanceData = {
        loadStart: performance.now(),
        metrics: [],
        apiCalls: [],
        renderTimes: []
      };
      
      // Monitor API calls
      const originalFetch = window.fetch;
      window.fetch = async (...args) => {
        const start = performance.now();
        try {
          const response = await originalFetch(...args);
          const end = performance.now();
          window.performanceData.apiCalls.push({
            url: args[0],
            duration: end - start,
            status: response.status
          });
          return response;
        } catch (error) {
          const end = performance.now();
          window.performanceData.apiCalls.push({
            url: args[0],
            duration: end - start,
            error: error.message
          });
          throw error;
        }
      };
    });
  });

  test.describe('Page Load Performance', () => {
    
    test('should load all sections under 200ms', async ({ page }) => {
      const results = [];
      
      for (const section of SECTIONS) {
        console.log(`Testing ${section.name} (${section.path}) performance...`);
        
        const startTime = performance.now();
        
        // Navigate to section
        await page.goto(section.path);
        
        // Wait for initial content to load
        await page.waitForLoadState('domcontentloaded');
        
        const domLoadTime = performance.now() - startTime;
        
        // Wait for network to be idle (all resources loaded)
        await page.waitForLoadState('networkidle');
        
        const fullLoadTime = performance.now() - startTime;
        
        // Get performance metrics from the page
        const metrics = await page.evaluate(() => {
          const navigation = performance.getEntriesByType('navigation')[0];
          return {
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
            firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
            firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
          };
        });
        
        const result = {
          section: section.name,
          path: section.path,
          domLoadTime: Math.round(domLoadTime),
          fullLoadTime: Math.round(fullLoadTime),
          metrics
        };
        
        results.push(result);
        
        console.log(`${section.name}: DOM ${result.domLoadTime}ms, Full ${result.fullLoadTime}ms`);
        
        // Verify performance requirements
        expect(domLoadTime, `${section.name} DOM load time`).toBeLessThan(THRESHOLDS.LOAD_TIME);
        expect(fullLoadTime, `${section.name} full load time`).toBeLessThan(THRESHOLDS.LOAD_TIME * 2);
      }
      
      // Log summary
      console.log('\n=== Performance Summary ===');
      results.forEach(result => {
        console.log(`${result.section}: ${result.domLoadTime}ms (DOM), ${result.fullLoadTime}ms (Full)`);
      });
    });

    test('should have fast Time to Interactive (TTI)', async ({ page }) => {
      for (const section of SECTIONS) {
        await page.goto(section.path);
        
        const startTime = performance.now();
        
        // Wait for page to be interactive
        await page.waitForLoadState('networkidle');
        
        // Test if page is actually interactive by trying to click a button
        const buttons = page.locator('button:visible');
        const buttonCount = await buttons.count();
        
        if (buttonCount > 0) {
          const interactionStart = performance.now();
          await buttons.first().click();
          const interactionEnd = performance.now();
          
          const interactionTime = interactionEnd - interactionStart;
          expect(interactionTime, `${section.name} interaction time`).toBeLessThan(THRESHOLDS.INTERACTION);
        }
        
        const ttiTime = performance.now() - startTime;
        console.log(`${section.name} TTI: ${Math.round(ttiTime)}ms`);
        
        expect(ttiTime, `${section.name} Time to Interactive`).toBeLessThan(THRESHOLDS.LOAD_TIME * 1.5);
      }
    });
  });

  test.describe('Navigation Performance', () => {
    
    test('should have fast navigation between sections', async ({ page }) => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      for (const section of SECTIONS.slice(1)) { // Skip home page
        const startTime = performance.now();
        
        // Navigate using the navigation menu
        const navLink = page.locator(`nav a[href="${section.path}"]`).first();
        await navLink.click();
        
        // Wait for navigation to complete
        await page.waitForURL(`**${section.path}*`);
        await page.waitForLoadState('domcontentloaded');
        
        const navigationTime = performance.now() - startTime;
        
        console.log(`Navigation to ${section.name}: ${Math.round(navigationTime)}ms`);
        expect(navigationTime, `Navigation to ${section.name}`).toBeLessThan(THRESHOLDS.NAVIGATION);
      }
    });

    test('should have fast tab switching in FamHub', async ({ page }) => {
      await page.goto('/famhub');
      await page.waitForLoadState('networkidle');
      
      const tabs = [
        { name: 'Chat', selector: 'button:has-text("Chat")' },
        { name: 'Communities', selector: 'button:has-text("Communities")' },
        { name: 'Resources', selector: 'button:has-text("Resources")' },
        { name: 'Local Info', selector: 'button:has-text("Local Info")' }
      ];
      
      for (const tab of tabs) {
        const tabButton = page.locator(tab.selector).first();
        
        if (await tabButton.count() > 0) {
          const startTime = performance.now();
          
          await tabButton.click();
          
          // Wait for tab content to appear
          await page.waitForTimeout(100);
          
          const tabSwitchTime = performance.now() - startTime;
          
          console.log(`${tab.name} tab switch: ${Math.round(tabSwitchTime)}ms`);
          expect(tabSwitchTime, `${tab.name} tab switching`).toBeLessThan(THRESHOLDS.RENDER);
        }
      }
    });
  });

  test.describe('API Performance', () => {
    
    test('should have fast API response times', async ({ page }) => {
      await page.goto('/activities');
      await page.waitForLoadState('networkidle');
      
      // Get API call performance data
      const apiCalls = await page.evaluate(() => window.performanceData.apiCalls);
      
      if (apiCalls.length > 0) {
        console.log('\n=== API Performance ===');
        
        apiCalls.forEach(call => {
          console.log(`${call.url}: ${Math.round(call.duration)}ms`);
          
          if (!call.error) {
            expect(call.duration, `API call to ${call.url}`).toBeLessThan(THRESHOLDS.API_RESPONSE);
          }
        });
        
        // Check average API response time
        const avgResponseTime = apiCalls.reduce((sum, call) => sum + call.duration, 0) / apiCalls.length;
        console.log(`Average API response time: ${Math.round(avgResponseTime)}ms`);
        
        expect(avgResponseTime, 'Average API response time').toBeLessThan(THRESHOLDS.API_RESPONSE);
      }
    });
  });

  test.describe('Real-time Performance', () => {
    
    test('should handle real-time updates efficiently', async ({ page }) => {
      await page.goto('/activities');
      await page.waitForLoadState('networkidle');
      
      // Monitor for subscription storms or excessive updates
      const consoleMessages = [];
      page.on('console', msg => {
        if (msg.text().includes('subscription') || msg.text().includes('update')) {
          consoleMessages.push({
            text: msg.text(),
            timestamp: Date.now()
          });
        }
      });
      
      // Wait for real-time subscriptions to settle
      await page.waitForTimeout(3000);
      
      // Check for excessive subscription activity
      const subscriptionMessages = consoleMessages.filter(msg => 
        msg.text().includes('subscription')
      );
      
      // Should not have more than 10 subscription messages in 3 seconds
      expect(subscriptionMessages.length, 'Subscription activity').toBeLessThan(10);
      
      console.log(`Real-time messages: ${subscriptionMessages.length}`);
    });
  });

  test.describe('Memory Performance', () => {
    
    test('should not have memory leaks during navigation', async ({ page }) => {
      // Get initial memory usage
      const initialMemory = await page.evaluate(() => {
        if (performance.memory) {
          return {
            used: performance.memory.usedJSHeapSize,
            total: performance.memory.totalJSHeapSize
          };
        }
        return null;
      });
      
      if (initialMemory) {
        // Navigate through all sections multiple times
        for (let i = 0; i < 3; i++) {
          for (const section of SECTIONS) {
            await page.goto(section.path);
            await page.waitForLoadState('networkidle');
          }
        }
        
        // Force garbage collection if available
        await page.evaluate(() => {
          if (window.gc) {
            window.gc();
          }
        });
        
        // Get final memory usage
        const finalMemory = await page.evaluate(() => {
          if (performance.memory) {
            return {
              used: performance.memory.usedJSHeapSize,
              total: performance.memory.totalJSHeapSize
            };
          }
          return null;
        });
        
        if (finalMemory) {
          const memoryIncrease = finalMemory.used - initialMemory.used;
          const memoryIncreasePercent = (memoryIncrease / initialMemory.used) * 100;
          
          console.log(`Memory usage: ${Math.round(initialMemory.used / 1024 / 1024)}MB → ${Math.round(finalMemory.used / 1024 / 1024)}MB`);
          console.log(`Memory increase: ${Math.round(memoryIncreasePercent)}%`);
          
          // Memory should not increase by more than 50% after navigation
          expect(memoryIncreasePercent, 'Memory leak check').toBeLessThan(50);
        }
      }
    });
  });

  test.afterEach(async ({ page }) => {
    // Log final performance summary
    const finalMetrics = await page.evaluate(() => {
      return {
        apiCalls: window.performanceData?.apiCalls?.length || 0,
        totalTime: performance.now() - (window.performanceData?.loadStart || 0)
      };
    });
    
    console.log(`Test completed: ${finalMetrics.apiCalls} API calls, ${Math.round(finalMetrics.totalTime)}ms total`);
  });
});

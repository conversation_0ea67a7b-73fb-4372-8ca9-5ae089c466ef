# Festival Family - Single Source of Truth Architecture

## 🎯 **Overview**

This document outlines the comprehensive architectural cleanup completed for Festival Family, establishing a **single source of truth** pattern that eliminates duplicate implementations, ensures type safety, and provides clear development guidelines.

## 📋 **Table of Contents**

- [Core Principles](#core-principles)
- [Database Schema & Types](#database-schema--types)
- [Supabase Client Architecture](#supabase-client-architecture)
- [Import Path Standards](#import-path-standards)
- [Service Layer Architecture](#service-layer-architecture)
- [Component Guidelines](#component-guidelines)
- [Development Workflow](#development-workflow)
- [Troubleshooting](#troubleshooting)

---

## 🏗️ **Core Principles**

### **1. Single Source of Truth**
- **One authoritative source** for each piece of functionality
- **Zero duplicate implementations** across the codebase
- **Centralized configuration** with clear ownership

### **2. Type Safety First**
- **Database-driven types** that match actual schema
- **Compile-time validation** for all data operations
- **Consistent type definitions** across components and services

### **3. Clear Import Hierarchy**
- **Standardized import paths** using `@/` aliases
- **Predictable module structure** for easy navigation
- **Explicit dependencies** with no circular imports

---

## 🗄️ **Database Schema & Types**

### **Authoritative Type Definitions**

**Location:** `src/types/database.ts`

This is the **single source of truth** for all database-related types.

```typescript
// ✅ CORRECT - Use these authoritative types
import { Profile, Festival, Event, Activity, Announcement } from '@/types'

// ❌ INCORRECT - Don't create duplicate type definitions
interface MyProfile { ... } // Don't do this
```

### **Type Hierarchy**

```
src/types/
├── database.ts          # 🎯 AUTHORITATIVE - Database types
├── supabase.ts         # Generated Supabase types
├── index.ts            # Main type exports
├── announcements.ts    # Announcement-specific types
├── activities.ts       # Activity-specific types
└── core.ts            # Core application types
```

### **Key Type Categories**

1. **Database Types** (`DatabaseProfile`, `DatabaseEvent`, etc.)
   - Direct mappings to database tables
   - Used for raw database operations

2. **Application Types** (`Profile`, `Event`, etc.)
   - Enhanced with computed properties
   - Used in components and business logic

3. **Legacy Compatibility** (`User`, `FestivalExtended`)
   - Backward compatibility for existing components
   - Gradual migration path

### **Type Usage Guidelines**

```typescript
// ✅ For database operations
const { data } = await supabase
  .from('profiles')
  .select('*')
  .returns<DatabaseProfile[]>()

// ✅ For component props
interface EventCardProps {
  event: Event  // Application type with computed properties
}

// ✅ For backward compatibility
const user: User = adaptProfileToUser(profile)
```

---

## 🔌 **Supabase Client Architecture**

### **Single Client Source**

**Location:** `src/lib/supabase/core-client.ts`

```typescript
// ✅ CORRECT - Always use the main client
import { supabase } from '@/lib/supabase'

// ❌ INCORRECT - Don't create new clients
const myClient = createClient(url, key) // Don't do this
```

### **Client Hierarchy**

```
src/lib/supabase/
├── index.ts              # 🎯 MAIN EXPORT - Use this
├── core-client.ts        # Client implementation
├── services/            # Business logic services
│   ├── index.ts         # Service exports
│   ├── auth-service.ts  # Authentication
│   ├── profile-service.ts
│   └── ...
└── types/               # Supabase-specific types
```

### **Available Clients**

1. **Main Client** (`supabase`)
   - Production-ready with full configuration
   - Use for all standard operations

2. **Minimal Client** (`createMinimalClient()`)
   - Fallback for connection issues
   - Reduced configuration

3. **Ultra-Minimal Client** (`createUltraMinimalClient()`)
   - Last resort for debugging
   - Minimal configuration

### **Client Usage Examples**

```typescript
// ✅ Standard operations
import { supabase } from '@/lib/supabase'
const { data } = await supabase.from('profiles').select('*')

// ✅ Service layer
import { profileService } from '@/lib/supabase/services'
const profile = await profileService.getProfile(userId)

// ✅ Connection testing
import { testConnection } from '@/lib/supabase'
const result = await testConnection()
```

---

## 📁 **Import Path Standards**

### **Standardized Patterns**

| Import Type | Pattern | Example |
|-------------|---------|---------|
| **Supabase** | `@/lib/supabase` | `import { supabase } from '@/lib/supabase'` |
| **Types** | `@/types` | `import { Profile } from '@/types'` |
| **Components** | `@/components/...` | `import { Button } from '@/components/ui/button'` |
| **Hooks** | `@/hooks/...` | `import { useProfile } from '@/hooks/useProfile'` |
| **Utils** | `@/lib/utils/...` | `import { cn } from '@/lib/utils'` |
| **Services** | `@/lib/supabase/services` | `import { authService } from '@/lib/supabase/services'` |

### **Import Guidelines**

```typescript
// ✅ CORRECT - Use @ aliases
import { supabase } from '@/lib/supabase'
import { Profile } from '@/types'
import { Button } from '@/components/ui/button'

// ❌ INCORRECT - Relative paths
import { supabase } from '../../../lib/supabase/core-client'
import { Profile } from './types/core'

// ❌ INCORRECT - Wrong paths
import { supabase } from '@/lib/supabase/client'
import { supabase } from './lib/supabase/core-client'
```

### **Path Resolution**

Configured in `tsconfig.json`:
```json
{
  "compilerOptions": {
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

---

## 🔧 **Service Layer Architecture**

### **Service Organization**

**Location:** `src/lib/supabase/services/`

Each service handles a specific domain with clear boundaries:

```
services/
├── index.ts              # 🎯 SERVICE EXPORTS
├── auth-service.ts       # Authentication & sessions
├── profile-service.ts    # User profiles
├── festival-service.ts   # Festival management
├── event-service.ts      # Event operations
├── activity-service.ts   # Activity management
├── announcement-service.ts # Announcements
├── chat-service.ts       # Messaging
├── connection-service.ts # User connections
├── group-service.ts      # Group management
├── realtime-service.ts   # Real-time subscriptions
└── storage-service.ts    # File uploads
```

### **Service Usage Pattern**

```typescript
// ✅ Import from services index
import { 
  profileService, 
  festivalService, 
  authService 
} from '@/lib/supabase/services'

// ✅ Use service methods
const profile = await profileService.getProfile(userId)
const festivals = await festivalService.getFestivals()
const session = await authService.getSession()

// ❌ Don't bypass services for complex operations
const { data } = await supabase.from('profiles')... // Use service instead
```

### **Service Responsibilities**

1. **Data Access Layer**
   - Database queries and mutations
   - Error handling and validation
   - Type conversion and adaptation

2. **Business Logic**
   - Domain-specific operations
   - Data transformation
   - Computed properties

3. **Caching & Optimization**
   - Query optimization
   - Result caching where appropriate
   - Batch operations

---

## 🧩 **Component Guidelines**

### **Type Usage in Components**

```typescript
// ✅ Use application types for props
interface EventCardProps {
  event: Event              // Application type
  onSelect?: (event: Event) => void
}

// ✅ Use database types for raw data
const [events, setEvents] = useState<DatabaseEvent[]>([])

// ✅ Use adapters for compatibility
import { adaptEventForLegacyComponents } from '@/lib/utils/type-adapters'
const legacyEvent = adaptEventForLegacyComponents(event)
```

### **Data Fetching Patterns**

```typescript
// ✅ Use services in components
import { festivalService } from '@/lib/supabase/services'

const MyComponent = () => {
  const [festivals, setFestivals] = useState<Festival[]>([])
  
  useEffect(() => {
    const loadFestivals = async () => {
      const data = await festivalService.getFestivals()
      setFestivals(data)
    }
    loadFestivals()
  }, [])
}

// ✅ Use React Query for caching
import { useQuery } from '@tanstack/react-query'

const { data: festivals } = useQuery({
  queryKey: ['festivals'],
  queryFn: () => festivalService.getFestivals()
})
```

### **Component Architecture**

```
src/components/
├── ui/                   # Base UI components
├── forms/               # Form components
├── layout/              # Layout components
├── features/            # Feature-specific components
│   ├── festivals/
│   ├── events/
│   ├── profiles/
│   └── ...
└── shared/              # Shared business components
```

---

## 🔄 **Development Workflow**

### **Adding New Features**

1. **Define Types** (if needed)
   ```typescript
   // Add to src/types/database.ts
   export interface NewFeature extends DatabaseNewFeature {
     computed_property?: string
   }
   ```

2. **Create Service** (if needed)
   ```typescript
   // Add to src/lib/supabase/services/new-feature-service.ts
   export const newFeatureService = {
     async getFeatures() { ... },
     async createFeature(data) { ... }
   }
   ```

3. **Export Service**
   ```typescript
   // Add to src/lib/supabase/services/index.ts
   export { newFeatureService } from './new-feature-service'
   ```

4. **Create Components**
   ```typescript
   // Use established patterns
   import { newFeatureService } from '@/lib/supabase/services'
   import { NewFeature } from '@/types'
   ```

### **Modifying Existing Features**

1. **Check Type Definitions** - Ensure types match database schema
2. **Update Services** - Modify business logic in service layer
3. **Update Components** - Use type-safe patterns
4. **Test Integration** - Verify end-to-end functionality

### **Database Schema Changes**

1. **Update Supabase Schema** - Make database changes
2. **Regenerate Types** - Run type generation
3. **Update Application Types** - Modify `src/types/database.ts`
4. **Update Services** - Adapt service methods
5. **Update Components** - Fix type mismatches

---

## 🔍 **Troubleshooting**

### **Common Issues**

#### **Import Errors**
```typescript
// ❌ Error: Cannot find module '@/lib/supabase'
// ✅ Solution: Check tsconfig.json paths configuration

// ❌ Error: Module has no exported member 'createMinimalClient'
// ✅ Solution: Import from correct path
import { createMinimalClient } from '@/lib/supabase'
```

#### **Type Errors**
```typescript
// ❌ Error: Property 'title' does not exist on type 'Event'
// ✅ Solution: Use correct field name
event.name // Not event.title

// ❌ Error: Property 'active' does not exist on type 'Announcement'
// ✅ Solution: Use correct field name
announcement.is_active // Not announcement.active
```

#### **Service Errors**
```typescript
// ❌ Error: Service method not found
// ✅ Solution: Check service exports
import { profileService } from '@/lib/supabase/services'

// ❌ Error: Database connection failed
// ✅ Solution: Use connection testing
import { testConnection } from '@/lib/supabase'
const result = await testConnection()
```

### **Debugging Tools**

1. **Connection Testing**
   ```typescript
   import { testConnection } from '@/lib/supabase'
   const result = await testConnection()
   console.log(result)
   ```

2. **Type Adapters**
   ```typescript
   import { adaptEventForLegacyComponents } from '@/lib/utils/type-adapters'
   const adapted = adaptEventForLegacyComponents(event)
   ```

3. **Debug Pages**
   - `/debug/client-test` - Client testing
   - `/debug/supabase-test` - Database testing

---

## ✅ **Validation Checklist**

### **Before Deployment**

- [ ] All imports use `@/` aliases
- [ ] No duplicate client instances
- [ ] Services used for data operations
- [ ] Types match database schema
- [ ] Components use application types
- [ ] Error handling implemented
- [ ] Tests pass
- [ ] Documentation updated

### **Code Review Checklist**

- [ ] Follows import path standards
- [ ] Uses established service patterns
- [ ] Proper type usage
- [ ] No duplicate implementations
- [ ] Clear error handling
- [ ] Consistent naming conventions

---

## 📚 **Additional Resources**

- [Supabase Documentation](https://supabase.com/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [React Query Documentation](https://tanstack.com/query/latest)

---

**Last Updated:** December 2024  
**Version:** 2.0.0  
**Maintainer:** Festival Family Development Team

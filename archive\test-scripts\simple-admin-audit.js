#!/usr/bin/env node

/**
 * Simple Admin Features Testing
 * 
 * This script performs focused testing of admin functionality:
 * - Admin dashboard access testing
 * - Role-based access verification
 * - Admin feature availability assessment
 */

import { chromium } from 'playwright';
import { promises as fs } from 'fs';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'simple-admin-evidence';

// Test user credentials
const TEST_USER = {
  email: `admin.test.${Date.now()}@festivalfamily.test`,
  password: 'AdminPassword123!',
  fullName: 'Admin Test User'
};

async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

async function authenticateUser(page) {
  console.log('\n🔐 Setting up authenticated session');
  console.log('==================================');
  
  await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(2000);
  
  // Sign up new user
  const signUpTab = await page.$('text=Sign Up');
  if (signUpTab) {
    await signUpTab.click();
    await page.waitForTimeout(1000);
  }
  
  const emailInput = await page.$('input[type="email"]');
  const passwordInput = await page.$('input[type="password"]');
  
  if (emailInput && passwordInput) {
    await emailInput.fill(TEST_USER.email);
    await passwordInput.fill(TEST_USER.password);
    
    const submitButton = await page.$('button[type="submit"]');
    if (submitButton) {
      await submitButton.click();
      await page.waitForTimeout(3000);
    }
  }
  
  console.log(`✅ Authenticated as: ${TEST_USER.email}`);
}

async function testAdminAccess(page) {
  console.log('\n👑 ADMIN ACCESS TESTING');
  console.log('=======================');
  
  // Navigate to home and check for admin access
  await page.goto(APP_URL, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  // Capture authenticated dashboard
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/01-authenticated-dashboard.png`,
    fullPage: true 
  });
  
  // Check for admin links in navigation
  const adminLinkInNav = await page.$('nav a[href="/admin"]');
  const adminTextInNav = await page.$('nav text=Admin');
  const adminButton = await page.$('button:has-text("Admin")');
  
  console.log('📊 ADMIN LINK DETECTION:');
  console.log(`🔗 Admin Link (href): ${adminLinkInNav ? '✅ Found' : '❌ Not Found'}`);
  console.log(`📝 Admin Text: ${adminTextInNav ? '✅ Found' : '❌ Not Found'}`);
  console.log(`🔘 Admin Button: ${adminButton ? '✅ Found' : '❌ Not Found'}`);
  
  // Try to access admin page directly
  console.log('\n🔄 Testing direct admin access...');
  await page.goto(`${APP_URL}/admin`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  const currentUrl = page.url();
  const hasAdminAccess = currentUrl.includes('/admin') && !currentUrl.includes('/auth');
  
  // Capture admin access result
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/02-admin-access-result.png`,
    fullPage: true 
  });
  
  console.log(`🎯 Admin Access Result: ${hasAdminAccess ? '✅ Accessible' : '❌ Blocked/Redirected'}`);
  console.log(`🔗 Final URL: ${currentUrl}`);
  
  return {
    navigation: {
      adminLinkInNav: !!adminLinkInNav,
      adminTextInNav: !!adminTextInNav,
      adminButton: !!adminButton
    },
    access: {
      hasAdminAccess,
      finalUrl: currentUrl
    },
    screenshots: [
      '01-authenticated-dashboard.png',
      '02-admin-access-result.png'
    ]
  };
}

async function testAdminFeatures(page, hasAccess) {
  if (!hasAccess) {
    console.log('\n⚠️ Skipping admin features test - no admin access');
    return { skipped: true, reason: 'No admin access' };
  }
  
  console.log('\n🛠️ ADMIN FEATURES TESTING');
  console.log('=========================');
  
  // Ensure we're on admin page
  await page.goto(`${APP_URL}/admin`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  // Capture admin dashboard
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/03-admin-dashboard.png`,
    fullPage: true 
  });
  
  // Check for admin features
  const features = {
    users: await page.$('text=Users') || await page.$('a[href*="users"]'),
    festivals: await page.$('text=Festivals') || await page.$('a[href*="festivals"]'),
    events: await page.$('text=Events') || await page.$('a[href*="events"]'),
    activities: await page.$('text=Activities') || await page.$('a[href*="activities"]'),
    faqs: await page.$('text=FAQs') || await page.$('a[href*="faqs"]'),
    guides: await page.$('text=Guides') || await page.$('a[href*="guides"]'),
    tips: await page.$('text=Tips') || await page.$('a[href*="tips"]'),
    announcements: await page.$('text=Announcements') || await page.$('a[href*="announcements"]')
  };
  
  // Test one admin section (Users)
  let userManagementWorking = false;
  if (features.users) {
    console.log('🔄 Testing Users management...');
    try {
      await features.users.click();
      await page.waitForTimeout(2000);
      
      const usersUrl = page.url();
      userManagementWorking = usersUrl.includes('/admin/users');
      
      // Capture users page
      await page.screenshot({ 
        path: `${EVIDENCE_DIR}/04-admin-users.png`,
        fullPage: true 
      });
      
      console.log(`   Users page: ${userManagementWorking ? '✅ Working' : '❌ Not Working'}`);
    } catch (error) {
      console.log(`   Users page: ❌ Error - ${error.message}`);
    }
  }
  
  console.log('\n📊 ADMIN FEATURES ANALYSIS:');
  console.log(`👥 Users Management: ${features.users ? '✅ Available' : '❌ Missing'}`);
  console.log(`🎪 Festivals Management: ${features.festivals ? '✅ Available' : '❌ Missing'}`);
  console.log(`📅 Events Management: ${features.events ? '✅ Available' : '❌ Missing'}`);
  console.log(`🎯 Activities Management: ${features.activities ? '✅ Available' : '❌ Missing'}`);
  console.log(`❓ FAQs Management: ${features.faqs ? '✅ Available' : '❌ Missing'}`);
  console.log(`📖 Guides Management: ${features.guides ? '✅ Available' : '❌ Missing'}`);
  console.log(`💡 Tips Management: ${features.tips ? '✅ Available' : '❌ Missing'}`);
  console.log(`📢 Announcements: ${features.announcements ? '✅ Available' : '❌ Missing'}`);
  console.log(`🔄 Users Functionality: ${userManagementWorking ? '✅ Working' : '❌ Not Working'}`);
  
  // Calculate feature score
  const availableFeatures = Object.values(features).filter(Boolean).length;
  const totalFeatures = Object.keys(features).length;
  const featureScore = (availableFeatures / totalFeatures) * 100;
  const functionalityScore = userManagementWorking ? 100 : 0;
  const overallScore = (featureScore + functionalityScore) / 2;
  
  console.log(`\n📊 ADMIN FEATURES SCORE: ${overallScore.toFixed(1)}%`);
  console.log(`   - Feature Availability: ${featureScore.toFixed(1)}%`);
  console.log(`   - Functionality: ${functionalityScore}%`);
  
  return {
    features: Object.fromEntries(
      Object.entries(features).map(([key, value]) => [key, !!value])
    ),
    functionality: {
      userManagementWorking
    },
    scores: {
      featureAvailability: featureScore,
      functionality: functionalityScore,
      overall: overallScore
    },
    screenshots: [
      '03-admin-dashboard.png',
      ...(userManagementWorking ? ['04-admin-users.png'] : [])
    ]
  };
}

async function runSimpleAdminAudit() {
  console.log('👑 SIMPLE ADMIN FEATURES AUDIT');
  console.log('==============================');
  console.log(`🕐 Start Time: ${new Date().toISOString()}`);
  
  await ensureEvidenceDir();
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();
  
  try {
    // Authenticate user
    await authenticateUser(page);
    
    const results = {};
    
    // Test Admin Access
    results.adminAccess = await testAdminAccess(page);
    
    // Test Admin Features
    results.adminFeatures = await testAdminFeatures(page, results.adminAccess.access.hasAdminAccess);
    
    // Calculate overall admin system score
    const accessScore = results.adminAccess.access.hasAdminAccess ? 100 : 0;
    const navigationScore = (results.adminAccess.navigation.adminLinkInNav || results.adminAccess.navigation.adminTextInNav) ? 100 : 0;
    const featureScore = results.adminFeatures.scores?.overall || 0;
    const overallScore = (accessScore + navigationScore + featureScore) / 3;
    
    // Save comprehensive results
    const evidence = {
      timestamp: new Date().toISOString(),
      testUser: TEST_USER,
      adminResults: results,
      summary: {
        adminAccessible: results.adminAccess.access.hasAdminAccess,
        adminNavigationPresent: results.adminAccess.navigation.adminLinkInNav || results.adminAccess.navigation.adminTextInNav,
        featureScore: featureScore,
        overallScore: parseFloat(overallScore.toFixed(1)),
        screenshots: [
          ...results.adminAccess.screenshots,
          ...(results.adminFeatures.screenshots || [])
        ]
      }
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/simple-admin-audit-results.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    console.log('\n📊 ADMIN AUDIT SUMMARY');
    console.log('======================');
    console.log(`🎯 Admin Access: ${evidence.summary.adminAccessible ? '✅ Working' : '❌ Not Working'}`);
    console.log(`🧭 Admin Navigation: ${evidence.summary.adminNavigationPresent ? '✅ Present' : '❌ Missing'}`);
    console.log(`🛠️ Feature Score: ${evidence.summary.featureScore.toFixed(1)}%`);
    console.log(`📊 Overall Score: ${evidence.summary.overallScore}%`);
    console.log(`📸 Screenshots: ${evidence.summary.screenshots.length}`);
    console.log(`📁 Evidence: ${EVIDENCE_DIR}/`);
    
    return evidence;
    
  } catch (error) {
    console.error('\n💥 Simple admin audit failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

// Run the simple admin audit
runSimpleAdminAudit()
  .then(() => {
    console.log('\n✅ Simple admin audit completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Simple admin audit failed:', error);
    process.exit(1);
  });

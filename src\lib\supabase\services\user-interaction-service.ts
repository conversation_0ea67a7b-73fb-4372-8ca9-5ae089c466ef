/**
 * User Interaction Service
 * 
 * Unified database service for all user interactions including:
 * - Activity participation (join/leave)
 * - User favorites (add/remove)
 * - Activity attendance (RSVP status)
 * - Real-time participant counts
 * 
 * This service provides a single source of truth for user interaction data
 * with proper error handling, type safety, and real-time capabilities.
 * 
 * @module UserInteractionService
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { BaseService, ServiceResponse } from './base-service'
import { unifiedCacheService, CacheKeys } from '@/lib/cache/unified-cache-service'
import { CACHE_DURATIONS } from '@/lib/data/constants'
import type { RealtimeChannel } from '@supabase/supabase-js'
import type { Database } from '@/types/supabase'

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

// Database table types
type ActivityParticipant = Database['public']['Tables']['activity_participants']['Row']
type UserFavorite = Database['public']['Tables']['user_favorites']['Row']
type ActivityAttendance = Database['public']['Tables']['activity_attendance']['Row']

// Insert types for database operations
type ActivityParticipantInsert = Database['public']['Tables']['activity_participants']['Insert']
type UserFavoriteInsert = Database['public']['Tables']['user_favorites']['Insert']
type ActivityAttendanceInsert = Database['public']['Tables']['activity_attendance']['Insert']

// Attendance status enum
export type AttendanceStatus = 'going' | 'interested' | 'maybe' | 'not_going'

// Participant status enum
export type ParticipantStatus = 'registered' | 'attended' | 'cancelled'

// Enhanced types with computed properties
export interface ActivityWithParticipants {
  id: string
  title: string
  description: string
  capacity?: number
  participant_count: number
  user_is_participant: boolean
  user_attendance_status?: AttendanceStatus
  user_is_favorite: boolean
}

export interface ParticipantCounts {
  going: number
  interested: number
  maybe: number
  not_going: number
  total: number
}

export interface UserInteractionStatus {
  is_participant: boolean
  is_favorite: boolean
  attendance_status?: AttendanceStatus
  participant_status?: ParticipantStatus
}

// ============================================================================
// USER INTERACTION SERVICE
// ============================================================================

export class UserInteractionService extends BaseService {
  private subscriptions = new Map<string, RealtimeChannel>()

  // ========================================================================
  // ACTIVITY PARTICIPANTS (JOIN/LEAVE)
  // ========================================================================

  /**
   * Join an activity (add user to activity_participants)
   */
  async joinActivity(
    userId: string,
    activityId: string,
    notes?: string
  ): Promise<ServiceResponse<ActivityParticipant>> {
    const result = await this.handleResponse(
      this.client
        .from('activity_participants')
        .insert({
          user_id: userId,
          activity_id: activityId,
          status: 'registered',
          notes,
          registration_date: new Date().toISOString(),
        } as ActivityParticipantInsert)
        .select()
        .single()
    ) as ServiceResponse<ActivityParticipant>;

    // Invalidate related caches on successful join
    if (result.status === 'success') {
      await unifiedCacheService.invalidatePattern(`activity:${activityId}:participants`);
      await unifiedCacheService.invalidatePattern(`user:${userId}:activities`);
      console.log(`🔄 Cache invalidated for activity ${activityId} participants`);
    }

    return result;
  }

  /**
   * Leave an activity (remove user from activity_participants)
   */
  async leaveActivity(
    userId: string,
    activityId: string
  ): Promise<ServiceResponse<null>> {
    const result = await this.handleResponse(
      this.client
        .from('activity_participants')
        .delete()
        .eq('user_id', userId)
        .eq('activity_id', activityId)
    ) as ServiceResponse<null>;

    // Invalidate related caches on successful leave
    if (result.status === 'success') {
      await unifiedCacheService.invalidatePattern(`activity:${activityId}:participants`);
      await unifiedCacheService.invalidatePattern(`user:${userId}:activities`);
      console.log(`🔄 Cache invalidated for activity ${activityId} participants (leave)`);
    }

    return result;
  }

  /**
   * Check if user is participant of an activity
   */
  async isUserParticipant(
    userId: string,
    activityId: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      const { data, error } = await this.client
        .from('activity_participants')
        .select('id')
        .eq('user_id', userId)
        .eq('activity_id', activityId)
        .single()

      return {
        data: !error && data !== null,
        error: null,
        status: 'success'
      }
    } catch (error) {
      return {
        data: false,
        error: this.createServiceError(error),
        status: 'error'
      }
    }
  }

  /**
   * Get participant count for an activity (with caching)
   */
  async getParticipantCount(activityId: string): Promise<ServiceResponse<number>> {
    const cacheKey = CacheKeys.activity(`${activityId}:participants:count`);

    try {
      // Try cache first
      const cachedCount = await unifiedCacheService.get<number>(
        cacheKey,
        async () => {
          const { data, error } = await this.client
            .from('activity_participants')
            .select('*', { count: 'exact' })
            .eq('activity_id', activityId)
            .eq('status', 'registered')

          if (error) throw error;
          return Array.isArray(data) ? data.length : 0;
        },
        {
          ttl: CACHE_DURATIONS.SHORT, // 5 minutes cache
          priority: 'high',
          persist: false // Memory only for frequently accessed data
        }
      );

      return {
        data: cachedCount || 0,
        error: null,
        status: 'success'
      }
    } catch (error) {
      return {
        data: 0,
        error: this.createServiceError(error),
        status: 'error'
      }
    }
  }

  // ========================================================================
  // USER FAVORITES
  // ========================================================================

  /**
   * Add activity to user favorites
   */
  async addToFavorites(
    userId: string,
    itemId: string,
    itemType: string = 'activity'
  ): Promise<ServiceResponse<UserFavorite>> {
    const result = await this.handleResponse(
      this.client
        .from('user_favorites')
        .insert({
          user_id: userId,
          activity_id: itemId // Use correct database field name
          // Note: user_favorites table is activity-specific, no item_type field
        } as UserFavoriteInsert)
        .select()
        .single()
    ) as ServiceResponse<UserFavorite>;

    // Invalidate related caches on successful favorite
    if (result.status === 'success') {
      await unifiedCacheService.invalidatePattern(`user:${userId}:favorites`);
      await unifiedCacheService.invalidatePattern(`activity:${itemId}:favorites`);
      console.log(`🔄 Cache invalidated for user ${userId} favorites (add)`);
    }

    return result;
  }

  /**
   * Remove activity from user favorites
   */
  async removeFromFavorites(
    userId: string,
    itemId: string,
    itemType: string = 'activity'
  ): Promise<ServiceResponse<null>> {
    const result = await this.handleResponse(
      this.client
        .from('user_favorites')
        .delete()
        .eq('user_id', userId)
        .eq('activity_id', itemId) // Use correct database field name
        // Note: user_favorites table is activity-specific, no item_type field
    ) as ServiceResponse<null>;

    // Invalidate related caches on successful unfavorite
    if (result.status === 'success') {
      await unifiedCacheService.invalidatePattern(`user:${userId}:favorites`);
      await unifiedCacheService.invalidatePattern(`activity:${itemId}:favorites`);
      console.log(`🔄 Cache invalidated for user ${userId} favorites (remove)`);
    }

    return result;
  }

  /**
   * Check if item is in user favorites
   */
  async isUserFavorite(
    userId: string,
    itemId: string,
    itemType: string = 'activity'
  ): Promise<ServiceResponse<boolean>> {
    try {
      const { data, error } = await this.client
        .from('user_favorites')
        .select('id')
        .eq('user_id', userId)
        .eq('activity_id', itemId) // Use correct database field name
        // Note: user_favorites table is activity-specific, no item_type field
        .single()

      return {
        data: !error && data !== null,
        error: null,
        status: 'success'
      }
    } catch (error) {
      return {
        data: false,
        error: this.createServiceError(error),
        status: 'error'
      }
    }
  }

  /**
   * Get user's favorite activities
   */
  async getUserFavorites(
    userId: string,
    itemType: string = 'activity'
  ): Promise<ServiceResponse<UserFavorite[]>> {
    return this.handleResponse(
      this.client
        .from('user_favorites')
        .select('*')
        .eq('user_id', userId)
        // Note: user_favorites table is activity-specific, no item_type field to filter
        .order('created_at', { ascending: false })
    )
  }

  // ========================================================================
  // ACTIVITY ATTENDANCE (RSVP)
  // ========================================================================

  /**
   * Set user attendance status for an activity
   */
  async setAttendanceStatus(
    userId: string,
    activityId: string,
    status: AttendanceStatus,
    notes?: string
  ): Promise<ServiceResponse<ActivityAttendance>> {
    return this.handleResponse(
      this.client
        .from('activity_attendance')
        .upsert({
          user_id: userId,
          activity_id: activityId,
          status,
          notes,
          updated_at: new Date().toISOString()
        } as ActivityAttendanceInsert)
        .select()
        .single()
    )
  }

  /**
   * Get user's attendance status for an activity
   */
  async getUserAttendanceStatus(
    userId: string,
    activityId: string
  ): Promise<ServiceResponse<AttendanceStatus | null>> {
    try {
      const { data, error } = await this.client
        .from('activity_attendance')
        .select('status')
        .eq('user_id', userId)
        .eq('activity_id', activityId)
        .single()

      return {
        data: error ? null : (data as any)?.status ?? null,
        error: null,
        status: 'success'
      }
    } catch (error) {
      return {
        data: null,
        error: this.createServiceError(error),
        status: 'error'
      }
    }
  }

  /**
   * Get attendance counts for an activity
   */
  async getAttendanceCounts(activityId: string): Promise<ServiceResponse<ParticipantCounts>> {
    try {
      const { data, error } = await this.client
        .from('activity_attendance')
        .select('status')
        .eq('activity_id', activityId)

      const counts = {
        going: 0,
        interested: 0,
        maybe: 0,
        not_going: 0,
        total: 0
      }

      if (!error && Array.isArray(data)) {
        data.forEach((record: any) => {
          if (record.status in counts) {
            counts[record.status as keyof typeof counts]++
            counts.total++
          }
        })
      }

      return {
        data: counts,
        error: null,
        status: 'success'
      }
    } catch (error) {
      return {
        data: {
          going: 0,
          interested: 0,
          maybe: 0,
          not_going: 0,
          total: 0
        },
        error: this.createServiceError(error),
        status: 'error'
      }
    }
  }

  // ========================================================================
  // COMBINED USER INTERACTION STATUS
  // ========================================================================

  /**
   * Get complete user interaction status for an activity
   */
  async getUserInteractionStatus(
    userId: string,
    activityId: string
  ): Promise<ServiceResponse<UserInteractionStatus>> {
    try {
      const [participantResponse, favoriteResponse, attendanceResponse] = await Promise.all([
        this.isUserParticipant(userId, activityId),
        this.isUserFavorite(userId, activityId),
        this.getUserAttendanceStatus(userId, activityId)
      ])

      const status: UserInteractionStatus = {
        is_participant: participantResponse.data ?? false,
        is_favorite: favoriteResponse.data ?? false,
        attendance_status: attendanceResponse.data ?? undefined
      }

      return {
        data: status,
        error: null,
        status: 'success'
      }
    } catch (error) {
      return {
        data: null,
        error: this.createServiceError(error),
        status: 'error'
      }
    }
  }

  // ========================================================================
  // REAL-TIME SUBSCRIPTIONS
  // ========================================================================

  /**
   * Subscribe to participant count updates for an activity
   */
  subscribeToParticipantUpdates(
    activityId: string,
    callback: (count: number) => void
  ): string {
    const subscriptionId = `participants_${activityId}`

    // Remove existing subscription if any
    this.unsubscribe(subscriptionId)

    const channel = this.client
      .channel(subscriptionId)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'activity_participants',
          filter: `activity_id=eq.${activityId}`
        },
        async () => {
          // Fetch updated count when participants change
          const response = await this.getParticipantCount(activityId)
          if (response.data !== null) {
            callback(response.data)
          }
        }
      )
      .subscribe()

    this.subscriptions.set(subscriptionId, channel)
    return subscriptionId
  }

  /**
   * Subscribe to attendance count updates for an activity
   */
  subscribeToAttendanceUpdates(
    activityId: string,
    callback: (counts: ParticipantCounts) => void
  ): string {
    const subscriptionId = `attendance_${activityId}`

    // Remove existing subscription if any
    this.unsubscribe(subscriptionId)

    const channel = this.client
      .channel(subscriptionId)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'activity_attendance',
          filter: `activity_id=eq.${activityId}`
        },
        async () => {
          // Fetch updated counts when attendance changes
          const response = await this.getAttendanceCounts(activityId)
          if (response.data !== null) {
            callback(response.data)
          }
        }
      )
      .subscribe()

    this.subscriptions.set(subscriptionId, channel)
    return subscriptionId
  }

  /**
   * Subscribe to user's favorite updates
   */
  subscribeToUserFavorites(
    userId: string,
    callback: (favorites: UserFavorite[]) => void
  ): string {
    const subscriptionId = `favorites_${userId}`

    // Remove existing subscription if any
    this.unsubscribe(subscriptionId)

    const channel = this.client
      .channel(subscriptionId)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_favorites',
          filter: `user_id=eq.${userId}`
        },
        async () => {
          // Fetch updated favorites when they change
          const response = await this.getUserFavorites(userId)
          if (response.data !== null) {
            callback(response.data)
          }
        }
      )
      .subscribe()

    this.subscriptions.set(subscriptionId, channel)
    return subscriptionId
  }

  /**
   * Unsubscribe from a specific subscription
   */
  unsubscribe(subscriptionId: string): void {
    const channel = this.subscriptions.get(subscriptionId)
    if (channel) {
      this.client.removeChannel(channel)
      this.subscriptions.delete(subscriptionId)
    }
  }

  /**
   * Unsubscribe from all subscriptions
   */
  unsubscribeAll(): void {
    this.subscriptions.forEach((channel) => {
      this.client.removeChannel(channel)
    })
    this.subscriptions.clear()
  }

  /**
   * Get all active subscriptions
   */
  getActiveSubscriptions(): string[] {
    return Array.from(this.subscriptions.keys())
  }
}

// ============================================================================
// SINGLETON EXPORT
// ============================================================================

export const userInteractionService = new UserInteractionService()

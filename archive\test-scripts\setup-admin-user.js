#!/usr/bin/env node

/**
 * Admin User Setup Script
 * 
 * This script checks for existing admin users and creates/updates
 * the admin user with known credentials for testing purposes.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔧 ADMIN USER SETUP');
console.log('==================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.log('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkExistingAdminUsers() {
  console.log('\n🔍 Checking existing admin users...');
  
  try {
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('*')
      .in('role', ['SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR']);
    
    if (error) {
      console.log(`❌ Error querying profiles: ${error.message}`);
      return [];
    }
    
    console.log(`📊 Found ${profiles.length} admin users:`);
    profiles.forEach(profile => {
      console.log(`   - ID: ${profile.id}`);
      console.log(`     Username: ${profile.username}`);
      console.log(`     Email: ${profile.email || 'Not set'}`);
      console.log(`     Role: ${profile.role}`);
      console.log(`     Created: ${profile.created_at}`);
      console.log('');
    });
    
    return profiles;
  } catch (error) {
    console.log(`❌ Unexpected error: ${error.message}`);
    return [];
  }
}

async function checkAuthUsers() {
  console.log('\n🔍 Checking auth.users table...');
  
  try {
    // Note: We can't directly query auth.users with anon key
    // But we can try to get user info through auth methods
    const { data: session, error } = await supabase.auth.getSession();
    
    if (error) {
      console.log(`⚠️ Auth session check: ${error.message}`);
    } else {
      console.log('📊 Current session:', session.session ? 'Active' : 'None');
    }
    
    // Try to get user list (this might fail with anon key)
    console.log('📊 Note: Cannot directly query auth.users with anonymous key');
    console.log('   Admin users must be created through Supabase Auth API or dashboard');
    
  } catch (error) {
    console.log(`❌ Auth check error: ${error.message}`);
  }
}

async function testAdminLogin() {
  console.log('\n🧪 Testing admin login credentials...');
  
  const testCredentials = [
    { email: '<EMAIL>', password: 'Admin123!@#' },
    { email: '<EMAIL>', password: 'admin123' },
    { email: '<EMAIL>', password: 'test123' }
  ];
  
  for (const creds of testCredentials) {
    console.log(`\n🔐 Testing: ${creds.email}`);
    
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: creds.email,
        password: creds.password
      });
      
      if (error) {
        console.log(`   ❌ Login failed: ${error.message}`);
      } else {
        console.log(`   ✅ Login successful!`);
        console.log(`   User ID: ${data.user.id}`);
        console.log(`   Email: ${data.user.email}`);
        
        // Check if this user has admin profile
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', data.user.id)
          .single();
        
        if (profileError) {
          console.log(`   ⚠️ No profile found: ${profileError.message}`);
        } else {
          console.log(`   👤 Profile: ${profile.username} (${profile.role})`);
        }
        
        // Sign out
        await supabase.auth.signOut();
        console.log(`   🚪 Signed out`);
        
        return { success: true, credentials: creds, user: data.user, profile };
      }
    } catch (error) {
      console.log(`   ❌ Unexpected error: ${error.message}`);
    }
  }
  
  return { success: false };
}

async function createTestAdminUser() {
  console.log('\n🔧 Creating test admin user...');
  console.log('⚠️ Note: This requires service role key or Supabase dashboard access');
  console.log('   With anonymous key, we can only test existing users');
  
  // We cannot create auth users with anon key
  // This would require service role key or Supabase dashboard
  
  console.log('\n📋 To create an admin user:');
  console.log('1. Go to Supabase Dashboard > Authentication > Users');
  console.log('2. Click "Add user"');
  console.log('3. Use email: <EMAIL>');
  console.log('4. Use password: Admin123!@#');
  console.log('5. Confirm the user');
  console.log('6. The profile should be automatically created via trigger');
  console.log('7. Update the profile role to SUPER_ADMIN if needed');
}

async function generateRecommendations(adminUsers, loginTest) {
  console.log('\n📋 RECOMMENDATIONS');
  console.log('==================');
  
  if (adminUsers.length === 0) {
    console.log('❌ No admin users found in profiles table');
    console.log('🔧 Action needed: Create admin user in Supabase dashboard');
  } else {
    console.log('✅ Admin profiles exist in database');
    
    if (!loginTest.success) {
      console.log('❌ No working login credentials found');
      console.log('🔧 Action needed: Verify admin user exists in auth.users');
      console.log('   The profile exists but the auth user might be missing');
    } else {
      console.log('✅ Working admin credentials found!');
      console.log(`   Email: ${loginTest.credentials.email}`);
      console.log(`   Password: ${loginTest.credentials.password}`);
    }
  }
  
  console.log('\n🎯 Next Steps:');
  console.log('1. Verify admin user exists in Supabase Auth dashboard');
  console.log('2. Ensure email/password match the test credentials');
  console.log('3. Confirm user email is verified');
  console.log('4. Check that profile has SUPER_ADMIN role');
  console.log('5. Re-run admin functionality test');
}

async function main() {
  try {
    const adminUsers = await checkExistingAdminUsers();
    await checkAuthUsers();
    const loginTest = await testAdminLogin();
    await createTestAdminUser();
    await generateRecommendations(adminUsers, loginTest);
    
  } catch (error) {
    console.error('❌ Setup error:', error);
  }
}

main();

import React, { useState } from 'react';
import { Heart, Calendar, MapPin, Users, Star, Filter, Search, X } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Input } from '../components/ui/input';
import { cn } from '../lib/utils';

interface FavoriteItem {
  id: string;
  type: 'festival' | 'activity' | 'venue' | 'artist';
  title: string;
  description: string;
  location?: string;
  date?: Date;
  rating?: number;
  participants?: number;
  image?: string;
  tags: string[];
  addedAt: Date;
}

const mockFavorites: FavoriteItem[] = [
  {
    id: '1',
    type: 'festival',
    title: 'Sziget Festival 2024',
    description: 'One of Europe\'s largest music and cultural festivals',
    location: 'Budapest, Hungary',
    date: new Date('2024-08-07'),
    rating: 4.8,
    participants: 500000,
    tags: ['music', 'culture', 'international'],
    addedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7) // 1 week ago
  },
  {
    id: '2',
    type: 'activity',
    title: 'Morning Yoga Session',
    description: 'Start your festival day with mindful movement',
    location: 'Main Stage Area',
    date: new Date('2024-07-15'),
    rating: 4.5,
    participants: 25,
    tags: ['wellness', 'morning', 'group'],
    addedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3) // 3 days ago
  },
  {
    id: '3',
    type: 'venue',
    title: 'The Underground',
    description: 'Intimate venue for electronic music lovers',
    location: 'Downtown District',
    rating: 4.7,
    tags: ['electronic', 'underground', 'nightlife'],
    addedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5) // 5 days ago
  },
  {
    id: '4',
    type: 'artist',
    title: 'Arctic Monkeys',
    description: 'British rock band known for their energetic performances',
    rating: 4.9,
    tags: ['rock', 'british', 'indie'],
    addedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 10) // 10 days ago
  }
];

const Favorites: React.FC = () => {
  const [favorites, setFavorites] = useState<FavoriteItem[]>(mockFavorites);
  const [filter, setFilter] = useState<'all' | 'festival' | 'activity' | 'venue' | 'artist'>('all');
  const [searchQuery, setSearchQuery] = useState('');

  const removeFavorite = (id: string) => {
    setFavorites(prev => prev.filter(item => item.id !== id));
  };

  const filteredFavorites = favorites.filter(item => {
    const matchesFilter = filter === 'all' || item.type === filter;
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesFilter && matchesSearch;
  });

  const getTypeIcon = (type: FavoriteItem['type']) => {
    switch (type) {
      case 'festival': return Calendar;
      case 'activity': return Users;
      case 'venue': return MapPin;
      case 'artist': return Star;
      default: return Heart;
    }
  };

  const getTypeBadge = (type: FavoriteItem['type']) => {
    const variants = {
      festival: 'bg-primary/20 text-primary',
      activity: 'bg-festival-success/20 text-festival-success',
      venue: 'bg-accent/20 text-accent-foreground',
      artist: 'bg-secondary/20 text-secondary-foreground'
    };
    return variants[type];
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatAddedDate = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return 'Today';
    if (days === 1) return 'Yesterday';
    if (days < 7) return `${days} days ago`;
    if (days < 30) return `${Math.floor(days / 7)} weeks ago`;
    return `${Math.floor(days / 30)} months ago`;
  };

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Heart className="h-6 w-6 text-primary" />
          <h1 className="text-2xl font-bold text-foreground">My Favorites</h1>
          <Badge variant="outline" className="ml-2">
            {favorites.length} items
          </Badge>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search favorites..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex flex-wrap gap-2">
          {[
            { key: 'all', label: 'All' },
            { key: 'festival', label: 'Festivals' },
            { key: 'activity', label: 'Activities' },
            { key: 'venue', label: 'Venues' },
            { key: 'artist', label: 'Artists' }
          ].map(({ key, label }) => (
            <Button
              key={key}
              variant={filter === key ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter(key as any)}
              className="text-sm"
            >
              <Filter className="h-3 w-3 mr-1" />
              {label}
            </Button>
          ))}
        </div>
      </div>

      {/* Favorites Grid */}
      {filteredFavorites.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Heart className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">No favorites found</h3>
            <p className="text-muted-foreground text-center">
              {searchQuery || filter !== 'all' 
                ? "Try adjusting your search or filter criteria."
                : "Start exploring and add items to your favorites!"
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredFavorites.map((item) => {
            const IconComponent = getTypeIcon(item.type);
            return (
              <Card key={item.id} className="group hover:shadow-lg transition-all duration-200">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      <IconComponent className="h-5 w-5 text-primary" />
                      <Badge 
                        variant="outline" 
                        className={cn("text-xs", getTypeBadge(item.type))}
                      >
                        {item.type}
                      </Badge>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFavorite(item.id)}
                      className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity text-destructive hover:text-destructive"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <CardTitle className="text-lg">{item.title}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <p className="text-muted-foreground text-sm line-clamp-2">
                    {item.description}
                  </p>
                  
                  {item.location && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <MapPin className="h-4 w-4" />
                      {item.location}
                    </div>
                  )}
                  
                  {item.date && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      {formatDate(item.date)}
                    </div>
                  )}
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      {item.rating && (
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 fill-current text-accent" />
                          <span className="text-sm font-medium">{item.rating}</span>
                        </div>
                      )}
                      {item.participants && (
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">
                            {item.participants.toLocaleString()}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-1">
                    {item.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {item.tags.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{item.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="text-xs text-muted-foreground pt-2 border-t">
                    Added {formatAddedDate(item.addedAt)}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default Favorites;

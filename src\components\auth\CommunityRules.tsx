import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Users, Shield, Heart, MessageCircle, Calendar, Camera, Music, Star, Globe, Handshake } from 'lucide-react';

export interface CommunityRule {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  category: 'safety' | 'community' | 'content' | 'events';
}

export const COMMUNITY_RULES: CommunityRule[] = [
  {
    id: 'respect',
    title: 'Respect Everyone',
    description: 'Treat all community members with kindness, respect, and dignity. No harassment, discrimination, or hate speech will be tolerated.',
    icon: <Heart className="w-5 h-5" />,
    category: 'community'
  },
  {
    id: 'safety-first',
    title: 'Safety First',
    description: 'Prioritize your safety and the safety of others. Report any unsafe situations or concerning behavior to festival staff immediately.',
    icon: <Shield className="w-5 h-5" />,
    category: 'safety'
  },
  {
    id: 'authentic-connections',
    title: 'Authentic Connections',
    description: 'Be genuine in your interactions. Use real photos and accurate information in your profile to build trust within the community.',
    icon: <Users className="w-5 h-5" />,
    category: 'community'
  },
  {
    id: 'appropriate-content',
    title: 'Keep Content Appropriate',
    description: 'Share content that is suitable for all ages and festival-appropriate. No explicit, offensive, or inappropriate material.',
    icon: <Camera className="w-5 h-5" />,
    category: 'content'
  },
  {
    id: 'constructive-communication',
    title: 'Constructive Communication',
    description: 'Engage in positive, helpful conversations. Avoid spam, excessive self-promotion, or disruptive behavior in chats and forums.',
    icon: <MessageCircle className="w-5 h-5" />,
    category: 'community'
  },
  {
    id: 'event-etiquette',
    title: 'Event Etiquette',
    description: 'Follow festival rules and guidelines. Respect event schedules, venue policies, and the experience of other attendees.',
    icon: <Calendar className="w-5 h-5" />,
    category: 'events'
  },
  {
    id: 'music-appreciation',
    title: 'Music & Arts Appreciation',
    description: 'Celebrate and respect all forms of musical and artistic expression. Support artists and creators in our community.',
    icon: <Music className="w-5 h-5" />,
    category: 'events'
  },
  {
    id: 'inclusive-environment',
    title: 'Inclusive Environment',
    description: 'Welcome newcomers and create an inclusive space for people of all backgrounds, experience levels, and musical tastes.',
    icon: <Globe className="w-5 h-5" />,
    category: 'community'
  },
  {
    id: 'responsible-sharing',
    title: 'Responsible Information Sharing',
    description: 'Share accurate information about events, safety, and festival details. Verify information before sharing to prevent misinformation.',
    icon: <Star className="w-5 h-5" />,
    category: 'content'
  },
  {
    id: 'community-support',
    title: 'Support the Community',
    description: 'Help fellow festival-goers, share resources, and contribute positively to the festival family experience for everyone.',
    icon: <Handshake className="w-5 h-5" />,
    category: 'community'
  }
];

interface CommunityRulesProps {
  isAccepted: boolean;
  onAcceptanceChange: (accepted: boolean) => void;
  showError?: boolean;
  className?: string;
}

const CommunityRules: React.FC<CommunityRulesProps> = ({
  isAccepted,
  onAcceptanceChange,
  showError = false,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const categoryColors = {
    safety: 'text-destructive bg-destructive/10 border-destructive/20',
    community: 'text-primary bg-primary/10 border-primary/20',
    content: 'text-festival-success bg-festival-success/10 border-festival-success/20',
    events: 'text-secondary bg-secondary/10 border-secondary/20'
  };

  const categoryLabels = {
    safety: 'Safety',
    community: 'Community',
    content: 'Content',
    events: 'Events'
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Rules Summary */}
      <div className="bg-white/5 border border-white/10 rounded-lg p-4">
        <button
          type="button"
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full flex items-center justify-between text-left focus:outline-none focus:ring-2 focus:ring-purple-500 rounded"
          aria-expanded={isExpanded}
          aria-controls="community-rules-content"
        >
          <div className="flex items-center gap-3">
            <Users className="w-5 h-5 text-purple-400" />
            <div>
              <h3 className="font-medium text-white">Community Rules & Guidelines</h3>
              <p className="text-sm text-white/70">
                {isExpanded ? 'Click to collapse' : 'Click to read our community guidelines'}
              </p>
            </div>
          </div>
          {isExpanded ? (
            <ChevronUp className="w-5 h-5 text-white/70" />
          ) : (
            <ChevronDown className="w-5 h-5 text-white/70" />
          )}
        </button>

        {/* Expanded Rules Content */}
        {isExpanded && (
          <div id="community-rules-content" className="mt-4 space-y-3">
            <p className="text-sm text-white/80 mb-4">
              Welcome to Festival Family! To ensure a safe, inclusive, and enjoyable experience for everyone, 
              please review and agree to follow these community guidelines:
            </p>
            
            <div className="grid gap-3 max-h-64 overflow-y-auto pr-2">
              {COMMUNITY_RULES.map((rule) => (
                <div
                  key={rule.id}
                  className={`p-3 rounded-lg border ${categoryColors[rule.category]}`}
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-0.5">
                      {rule.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm">{rule.title}</h4>
                        <span className="text-xs px-2 py-0.5 rounded-full bg-white/10">
                          {categoryLabels[rule.category]}
                        </span>
                      </div>
                      <p className="text-xs opacity-90 leading-relaxed">
                        {rule.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Acceptance Checkbox */}
      <div className={`flex items-start gap-3 p-3 rounded-lg border ${
        showError ? 'border-red-500/50 bg-red-500/10' : 'border-white/20 bg-white/5'
      }`}>
        <input
          id="community-rules-acceptance"
          type="checkbox"
          checked={isAccepted}
          onChange={(e) => onAcceptanceChange(e.target.checked)}
          className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-white/20 rounded bg-white/10"
          aria-required="true"
          aria-invalid={showError ? 'true' : 'false'}
          aria-describedby={showError ? 'rules-error' : 'rules-description'}
        />
        <div className="flex-1">
          <label 
            htmlFor="community-rules-acceptance" 
            className="text-sm text-white cursor-pointer"
          >
            I have read and agree to follow the{' '}
            <button
              type="button"
              onClick={() => setIsExpanded(true)}
              className="text-purple-400 hover:text-purple-300 underline focus:outline-none focus:ring-2 focus:ring-purple-500 rounded"
            >
              Community Rules & Guidelines
            </button>
            {' '}listed above. <span className="text-red-400">*</span>
          </label>
          <p id="rules-description" className="text-xs text-white/60 mt-1">
            By checking this box, you commit to creating a positive and safe environment for all festival-goers.
          </p>
          {showError && (
            <p id="rules-error" className="text-xs text-red-400 mt-1" role="alert">
              You must agree to the community rules to create an account.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default CommunityRules;

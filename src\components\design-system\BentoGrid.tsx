/**
 * Festival Family - BentoGrid Component
 *
 * Modern grid layout component implementing 2025 UI trends with Bento-style organization.
 * Uses established CSS variable system for theme compatibility.
 *
 * @module BentoGrid
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import React from 'react'
import { cn } from '@/lib/utils'

// ============================================================================
// BENTO GRID TYPES
// ============================================================================

export interface BentoGridProps {
  children: React.ReactNode
  className?: string
  cols?: 1 | 2 | 3 | 4 | 'auto'
  gap?: 'xs' | 'sm' | 'md' | 'lg'
  variant?: 'default' | 'masonry' | 'featured'
}

export interface BentoItemProps {
  children: React.ReactNode
  className?: string
  span?: 1 | 2 | 3 | 4
  rowSpan?: 1 | 2 | 3
  featured?: boolean
  variant?: 'default' | 'glassmorphism' | 'enhanced' | 'minimal'
}

// ============================================================================
// BENTO GRID COMPONENT
// ============================================================================

/**
 * BentoGrid - Responsive Grid Component
 *
 * Implements mobile-first responsive design with proper breakpoints:
 * - Mobile (default): Optimized for touch interaction
 * - Tablet (sm): Balanced layout
 * - Desktop (md+): Full grid display
 */
export const BentoGrid: React.FC<BentoGridProps> = ({
  children,
  className,
  cols = 'auto',
  gap = 'md',
  variant = 'default'
}) => {
  const getGridCols = () => {
    if (cols === 'auto') return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'

    // Responsive grid patterns based on UI/UX research
    switch (cols) {
      case 1:
        return 'grid-cols-1'
      case 2:
        return 'grid-cols-1 sm:grid-cols-2'
      case 3:
        return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
      case 4:
        return 'grid-cols-2 sm:grid-cols-2 md:grid-cols-4' // Mobile: 2x2 squares, Tablet+: 1x4 row
      default:
        return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
    }
  }

  const getGap = () => {
    switch (gap) {
      case 'xs': return 'gap-2'
      case 'sm': return 'gap-4'
      case 'lg': return 'gap-8'
      default: return 'gap-6'
    }
  }

  const getVariantClasses = () => {
    switch (variant) {
      case 'masonry':
        return 'auto-rows-auto'
      case 'featured':
        return 'auto-rows-fr'
      default:
        return 'auto-rows-min'
    }
  }

  return (
    <div
      className={cn(
        'grid',
        getGridCols(),
        getGap(),
        getVariantClasses(),
        className
      )}
    >
      {children}
    </div>
  )
}

// ============================================================================
// BENTO ITEM COMPONENT
// ============================================================================

export const BentoItem: React.FC<BentoItemProps> = ({
  children,
  className,
  span = 1,
  rowSpan = 1,
  featured = false,
  variant = 'default'
}) => {
  const getSpanClasses = () => {
    const colSpan = span > 1 ? `col-span-${span}` : ''
    const rowSpanClass = rowSpan > 1 ? `row-span-${rowSpan}` : ''
    return `${colSpan} ${rowSpanClass}`.trim()
  }

  const getVariantClasses = () => {
    switch (variant) {
      case 'glassmorphism':
        return 'glassmorphism-card layered-effect'
      case 'enhanced':
        return 'enhanced-card'
      case 'minimal':
        return 'bg-card border border-border rounded-lg'
      default:
        return 'modern-card'
    }
  }

  const getFeaturedClasses = () => {
    if (featured) {
      return 'col-span-full sm:col-span-2 lg:col-span-2 row-span-2'
    }
    return ''
  }

  return (
    <div
      className={cn(
        'p-6 transition-all duration-300',
        getVariantClasses(),
        getSpanClasses(),
        getFeaturedClasses(),
        featured && 'tall-card',
        className
      )}
    >
      {children}
    </div>
  )
}

// ============================================================================
// BENTO CARD VARIANTS
// ============================================================================

export interface BentoCardProps {
  title: string
  description?: string
  children?: React.ReactNode
  icon?: React.ReactNode
  action?: React.ReactNode
  className?: string
  variant?: 'default' | 'featured' | 'minimal' | 'glassmorphism'
  interactive?: boolean
  onClick?: () => void
  imageUrl?: string
  overlayBadge?: React.ReactNode
  badgePosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  showIcon?: boolean // Admin-configurable icon visibility
}

export const BentoCard: React.FC<BentoCardProps> = ({
  title,
  description,
  children,
  icon,
  action,
  className,
  variant = 'default',
  interactive = false,
  onClick,
  imageUrl,
  overlayBadge,
  badgePosition = 'top-right',
  showIcon = true
}) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'featured':
        return 'modern-card light-effect-primary micro-bounce'
      case 'minimal':
        return 'bg-card border border-border rounded-lg smooth-scale'
      case 'glassmorphism':
        return 'glassmorphism-card glass-hover'
      default:
        return 'enhanced-card micro-bounce'
    }
  }

  const getBadgePositionClasses = () => {
    switch (badgePosition) {
      case 'top-left':
        return 'top-2 left-2'
      case 'top-right':
        return 'top-2 right-2'
      case 'bottom-left':
        return 'bottom-2 left-2'
      case 'bottom-right':
        return 'bottom-2 right-2'
      default:
        return 'top-2 right-2'
    }
  }

  return (
    <div
      className={cn(
        'p-6 transition-all duration-300 min-h-[140px] flex flex-col',
        getVariantClasses(),
        interactive && 'cursor-pointer hover-lift',
        className
      )}
      onClick={onClick}
      role={interactive ? "button" : undefined}
      tabIndex={interactive ? 0 : undefined}
      onKeyDown={interactive ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick?.();
        }
      } : undefined}
    >
      {/* Image with Enhanced Overlay */}
      {imageUrl && (
        <div className="mb-4 -mx-6 -mt-6 relative overflow-hidden rounded-t-lg">
          <img
            src={imageUrl}
            alt={title}
            className="w-full h-24 sm:h-28 object-cover transition-transform duration-300 hover:scale-105"
          />
          {/* Bottom Gradient for Text Readability */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent pointer-events-none" />
          {/* Overlay Badge with Enhanced Visibility */}
          {overlayBadge && (
            <div className={cn(
              'absolute z-20 backdrop-blur-sm bg-black/20 rounded-lg p-1',
              getBadgePositionClasses()
            )}>
              {overlayBadge}
            </div>
          )}
        </div>
      )}

      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className={cn(
          "flex gap-3 flex-1 min-w-0",
          icon && showIcon ? "items-start" : "items-center"
        )}>
          {icon && showIcon && (
            <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-lg bg-primary/10 text-primary mt-1">
              {icon}
            </div>
          )}
          <div className="flex-1 min-w-0">
            <h3 className={cn(
              "font-semibold text-primary-light leading-tight line-clamp-2",
              !(icon && showIcon) ? "text-lg text-center md:text-left" : "text-lg"
            )}>
              {title}
            </h3>
            {description && (
              <p className={cn(
                "text-muted-light mt-2 line-clamp-3",
                !(icon && showIcon) ? "text-sm text-center md:text-left leading-relaxed" : "text-sm mt-1 line-clamp-2"
              )}>
                {description}
              </p>
            )}
          </div>
        </div>
        {action && !overlayBadge && (
          <div className="flex-shrink-0 ml-3 self-start">
            {action}
          </div>
        )}
      </div>

      {/* Content */}
      {children && (
        <div className="flex-1">
          {children}
        </div>
      )}
    </div>
  )
}

// ============================================================================
// BENTO STAT CARD
// ============================================================================

export interface BentoStatProps {
  label: string
  value: string | number
  change?: {
    value: string
    trend: 'up' | 'down' | 'neutral'
  }
  icon?: React.ReactNode
  className?: string
  variant?: 'default' | 'featured' | 'minimal'
}

export const BentoStat: React.FC<BentoStatProps> = ({
  label,
  value,
  change,
  icon,
  className,
  variant = 'default'
}) => {
  const getTrendColor = (trend: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up': return 'text-festival-success'
      case 'down': return 'text-destructive'
      default: return 'text-muted-foreground'
    }
  }

  return (
    <BentoCard
      title={label}
      variant={variant}
      icon={icon}
      className={className}
    >
      <div className="space-y-2">
        <div className="text-3xl font-bold text-primary">
          {value}
        </div>
        {change && (
          <div className={cn(
            'text-sm font-medium',
            getTrendColor(change.trend)
          )}>
            {change.value}
          </div>
        )}
      </div>
    </BentoCard>
  )
}

// ============================================================================
// EXPORTS
// ============================================================================

export default BentoGrid

#!/usr/bin/env node

/**
 * Script to fix authentication imports after consolidation
 * This script updates all files to use ConsolidatedAuthProvider instead of removed providers
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const SRC_DIR = path.join(__dirname, 'src');

// Import replacements
const IMPORT_REPLACEMENTS = [
  {
    from: "from '../providers/MinimalAuthProvider'",
    to: "from '../providers/ConsolidatedAuthProvider'"
  },
  {
    from: "from '../../providers/MinimalAuthProvider'",
    to: "from '../../providers/ConsolidatedAuthProvider'"
  },
  {
    from: "from '@/providers/MinimalAuthProvider'",
    to: "from '@/providers/ConsolidatedAuthProvider'"
  },
  {
    from: "from '@/store/authStore'",
    to: "from '@/providers/ConsolidatedAuthProvider'"
  },
  {
    from: "from '../store/authStore'",
    to: "from '../providers/ConsolidatedAuthProvider'"
  },
  {
    from: "from '../../store/authStore'",
    to: "from '../../providers/ConsolidatedAuthProvider'"
  }
];

// Hook replacements for authStore usage
const HOOK_REPLACEMENTS = [
  {
    from: "useAuthStore",
    to: "useAuth"
  }
];

function getAllFiles(dir, extensions = ['.ts', '.tsx']) {
  let files = [];
  
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      files = files.concat(getAllFiles(fullPath, extensions));
    } else if (extensions.some(ext => item.endsWith(ext))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function fixFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Apply import replacements
  for (const replacement of IMPORT_REPLACEMENTS) {
    if (content.includes(replacement.from)) {
      content = content.replace(new RegExp(replacement.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replacement.to);
      modified = true;
      console.log(`Fixed import in ${path.relative(process.cwd(), filePath)}: ${replacement.from} -> ${replacement.to}`);
    }
  }
  
  // Apply hook replacements for authStore files
  if (content.includes('useAuthStore')) {
    // Replace useAuthStore with useAuth
    content = content.replace(/useAuthStore/g, 'useAuth');
    
    // Remove authStore-specific destructuring and replace with useAuth equivalents
    content = content.replace(/const\s+{\s*([^}]+)\s*}\s*=\s*useAuth\(\)/g, (match, destructured) => {
      // Common authStore to useAuth mappings
      const mappings = {
        'setUser': '// setUser not needed - handled by provider',
        'setProfile': '// setProfile not needed - handled by provider', 
        'setLoading': '// setLoading not needed - handled by provider',
        'signIn': 'signIn',
        'signUp': 'signUp',
        'signOut': 'signOut',
        'loadProfile': 'refreshProfile',
        'isAuthenticated': '// use user instead of isAuthenticated',
        'isAdmin': 'isAdmin',
        'isModerator': '// isModerator not available in consolidated provider'
      };
      
      // For now, just replace with basic useAuth destructuring
      return 'const { user, profile, loading, isAdmin, signIn, signUp, signOut, refreshProfile } = useAuth()';
    });
    
    modified = true;
    console.log(`Fixed authStore usage in ${path.relative(process.cwd(), filePath)}`);
  }
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  }
  
  return false;
}

function main() {
  console.log('Fixing authentication imports...');
  
  const files = getAllFiles(SRC_DIR);
  let fixedCount = 0;
  
  for (const file of files) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\nFixed ${fixedCount} files.`);
}

main();

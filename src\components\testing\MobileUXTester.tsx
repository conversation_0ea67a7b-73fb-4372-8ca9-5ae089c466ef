/**
 * Mobile UX Testing Component
 * 
 * A development-only component for testing mobile UX improvements
 * and validating touch interactions, responsive design, and accessibility.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Smartphone, Tablet, Monitor, Check, X, Info, Zap, Eye, BarChart3, Download } from 'lucide-react';
import {
  isMobileViewport,
  isTabletViewport,
  isTouchDevice,
  getCurrentBreakpoint,
  validateTouchTarget
} from '../../utils/mobileUX';
import { runComprehensiveAudit, generateAuditReport, type ComprehensiveAuditReport } from '../../utils/mobileAudit';
import { useReducedMotion } from '../../utils/accessibilityUtils';
import { checkPerformanceBudget } from '../../utils/performanceOptimization';

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
}

const MobileUXTester: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [currentBreakpoint, setCurrentBreakpoint] = useState(getCurrentBreakpoint());
  const [auditReport, setAuditReport] = useState<ComprehensiveAuditReport | null>(null);
  const [isRunningAudit, setIsRunningAudit] = useState(false);
  const [activeTab, setActiveTab] = useState<'tests' | 'audit' | 'performance'>('tests');
  const prefersReducedMotion = useReducedMotion();

  // Update breakpoint on resize
  useEffect(() => {
    const handleResize = () => {
      setCurrentBreakpoint(getCurrentBreakpoint());
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Run mobile UX tests
  const runTests = () => {
    const results: TestResult[] = [];

    // Test 1: Touch device detection
    results.push({
      name: 'Touch Device Detection',
      status: isTouchDevice() ? 'pass' : 'warning',
      message: isTouchDevice() ? 'Touch device detected' : 'Non-touch device (desktop/mouse)'
    });

    // Test 2: Viewport size validation
    const isMobile = isMobileViewport();
    const isTablet = isTabletViewport();
    results.push({
      name: 'Viewport Classification',
      status: 'pass',
      message: `Current: ${isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop'} (${currentBreakpoint})`
    });

    // Test 3: Touch target validation
    const buttons = document.querySelectorAll('button, a[role="button"], .touch-target');
    let touchTargetFailures = 0;
    buttons.forEach(button => {
      if (!validateTouchTarget(button as HTMLElement)) {
        touchTargetFailures++;
      }
    });

    results.push({
      name: 'Touch Target Compliance',
      status: touchTargetFailures === 0 ? 'pass' : 'fail',
      message: `${buttons.length - touchTargetFailures}/${buttons.length} buttons meet 44px minimum`
    });

    // Test 4: Responsive navigation
    const bottomNav = document.querySelector('[data-testid="bottom-navigation"]');
    const hamburgerMenu = document.querySelector('[data-testid="hamburger-menu"]');
    results.push({
      name: 'Mobile Navigation',
      status: (bottomNav || hamburgerMenu) ? 'pass' : 'warning',
      message: bottomNav ? 'Bottom navigation found' : hamburgerMenu ? 'Hamburger menu found' : 'No mobile navigation detected'
    });

    // Test 5: Typography scaling
    const headings = document.querySelectorAll('h1, h2, h3');
    let typographyIssues = 0;
    headings.forEach(heading => {
      const fontSize = window.getComputedStyle(heading).fontSize;
      const sizeValue = parseFloat(fontSize);
      if (isMobile && sizeValue > 32) { // Too large for mobile
        typographyIssues++;
      }
    });

    results.push({
      name: 'Typography Scaling',
      status: typographyIssues === 0 ? 'pass' : 'warning',
      message: `${headings.length - typographyIssues}/${headings.length} headings appropriately sized`
    });

    // Test 6: Animation performance
    const animatedElements = document.querySelectorAll('[data-framer-motion]');
    results.push({
      name: 'Animation Elements',
      status: 'pass',
      message: `${animatedElements.length} animated elements detected`
    });

    setTestResults(results);
  };

  // Run comprehensive audit
  const runAudit = useCallback(async () => {
    setIsRunningAudit(true);
    try {
      const report = await runComprehensiveAudit();
      setAuditReport(report);
      setActiveTab('audit');
    } catch (error) {
      console.error('Audit failed:', error);
    } finally {
      setIsRunningAudit(false);
    }
  }, []);

  // Download audit report
  const downloadReport = useCallback(() => {
    if (!auditReport) return;

    const reportText = generateAuditReport(auditReport);
    const blob = new Blob([reportText], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `mobile-audit-${new Date().toISOString().split('T')[0]}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [auditReport]);

  // Auto-run tests when component mounts or viewport changes
  useEffect(() => {
    if (isVisible) {
      setTimeout(runTests, 500); // Delay to ensure DOM is ready
    }
  }, [isVisible, currentBreakpoint]);

  if (process.env.NODE_ENV === 'production') {
    return null; // Don't show in production
  }

  return (
    <>
      {/* Toggle Button */}
      <motion.button
        className="fixed bottom-4 left-4 z-50 w-12 h-12 bg-purple-600 hover:bg-purple-500 rounded-full flex items-center justify-center text-white shadow-lg"
        onClick={() => setIsVisible(!isVisible)}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 1 }}
      >
        <Smartphone className="w-5 h-5" />
      </motion.button>

      {/* Testing Panel */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsVisible(false)}
          >
            <motion.div
              className="absolute bottom-0 left-0 right-0 bg-white rounded-t-2xl max-h-[80vh] overflow-y-auto"
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              exit={{ y: '100%' }}
              transition={{ type: 'spring', damping: 30 }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Smartphone className="w-4 h-4 text-purple-600" />
                    </div>
                    <div>
                      <h2 className="text-lg font-semibold text-gray-900">Mobile Performance & Accessibility Audit</h2>
                      <p className="text-sm text-gray-500">Development Tool</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {auditReport && (
                      <button
                        onClick={downloadReport}
                        className="w-8 h-8 bg-blue-100 hover:bg-blue-200 rounded-lg flex items-center justify-center"
                        title="Download Report"
                      >
                        <Download className="w-4 h-4 text-blue-600" />
                      </button>
                    )}
                    <button
                      onClick={() => setIsVisible(false)}
                      className="w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-lg flex items-center justify-center"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Tab Navigation */}
                <div className="flex mb-6 bg-gray-100 rounded-lg p-1">
                  {[
                    { id: 'tests', label: 'UX Tests', icon: Smartphone },
                    { id: 'audit', label: 'Full Audit', icon: BarChart3 },
                    { id: 'performance', label: 'Performance', icon: Zap }
                  ].map(tab => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id as any)}
                      className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                        activeTab === tab.id
                          ? 'bg-white text-purple-600 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900'
                      }`}
                    >
                      <tab.icon className="w-4 h-4" />
                      {tab.label}
                    </button>
                  ))}
                </div>

                {/* Current Status */}
                <div className="mb-6 p-4 bg-gray-50 rounded-xl">
                  <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      {currentBreakpoint === 'xs' || currentBreakpoint === 'sm' ? (
                        <Smartphone className="w-4 h-4 text-blue-600" />
                      ) : currentBreakpoint === 'md' ? (
                        <Tablet className="w-4 h-4 text-green-600" />
                      ) : (
                        <Monitor className="w-4 h-4 text-purple-600" />
                      )}
                      <span className="font-medium">Breakpoint: {currentBreakpoint}</span>
                    </div>
                    <div className="text-gray-500">
                      {window.innerWidth}×{window.innerHeight}px
                    </div>
                  </div>
                </div>

                {/* Tab Content */}
                {activeTab === 'tests' && (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-gray-900">UX Test Results</h3>
                      <button
                        onClick={runTests}
                        className="px-3 py-1 bg-purple-600 hover:bg-purple-500 text-white text-sm rounded-lg transition-colors"
                      >
                        Run Tests
                      </button>
                    </div>

                  {testResults.map((result, index) => (
                    <motion.div
                      key={result.name}
                      className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                        result.status === 'pass' 
                          ? 'bg-green-100 text-green-600' 
                          : result.status === 'fail'
                          ? 'bg-red-100 text-red-600'
                          : 'bg-yellow-100 text-yellow-600'
                      }`}>
                        {result.status === 'pass' ? (
                          <Check className="w-3 h-3" />
                        ) : result.status === 'fail' ? (
                          <X className="w-3 h-3" />
                        ) : (
                          <Info className="w-3 h-3" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-sm text-gray-900">{result.name}</div>
                        <div className="text-xs text-gray-500">{result.message}</div>
                      </div>
                    </motion.div>
                  ))}
                    {testResults.length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        <Smartphone className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p>Click "Run Tests" to validate mobile UX</p>
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'audit' && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-gray-900">Comprehensive Audit</h3>
                      <button
                        onClick={runAudit}
                        disabled={isRunningAudit}
                        className="px-3 py-1 bg-blue-600 hover:bg-blue-500 disabled:opacity-50 text-white text-sm rounded-lg transition-colors"
                      >
                        {isRunningAudit ? 'Running...' : 'Run Audit'}
                      </button>
                    </div>

                    {auditReport && (
                      <div className="space-y-4">
                        {/* Overall Score */}
                        <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-semibold text-gray-900">Overall Score</h4>
                              <p className="text-sm text-gray-600">
                                {auditReport.overall.passed ? 'Excellent mobile optimization!' : 'Needs improvement'}
                              </p>
                            </div>
                            <div className={`text-3xl font-bold ${
                              auditReport.overall.score >= 90 ? 'text-green-600' :
                              auditReport.overall.score >= 70 ? 'text-yellow-600' : 'text-red-600'
                            }`}>
                              {auditReport.overall.score}
                            </div>
                          </div>
                        </div>

                        {/* Category Scores */}
                        <div className="grid grid-cols-2 gap-3">
                          {[
                            { key: 'performance', label: 'Performance', icon: Zap },
                            { key: 'accessibility', label: 'Accessibility', icon: Eye },
                            { key: 'mobile', label: 'Mobile UX', icon: Smartphone },
                            { key: 'architecture', label: 'Architecture', icon: BarChart3 }
                          ].map(category => {
                            const result = auditReport[category.key as keyof typeof auditReport] as any;
                            return (
                              <div key={category.key} className="p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center gap-2 mb-2">
                                  <category.icon className="w-4 h-4 text-gray-600" />
                                  <span className="text-sm font-medium">{category.label}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                  <span className={`text-lg font-bold ${
                                    result.score >= 90 ? 'text-green-600' :
                                    result.score >= 70 ? 'text-yellow-600' : 'text-red-600'
                                  }`}>
                                    {result.score}
                                  </span>
                                  <span className="text-xs text-gray-500">
                                    {result.issues.length} issues
                                  </span>
                                </div>
                              </div>
                            );
                          })}
                        </div>

                        {/* Issues Summary */}
                        {auditReport.recommendations.length > 0 && (
                          <div className="p-4 bg-yellow-50 rounded-lg">
                            <h5 className="font-medium text-yellow-800 mb-2">Key Recommendations</h5>
                            <ul className="text-sm text-yellow-700 space-y-1">
                              {auditReport.recommendations.slice(0, 3).map((rec, index) => (
                                <li key={index}>• {rec}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    )}

                    {!auditReport && !isRunningAudit && (
                      <div className="text-center py-8 text-gray-500">
                        <BarChart3 className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p>Click "Run Audit" for comprehensive analysis</p>
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'performance' && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-gray-900">Performance Metrics</h3>
                      <div className="text-xs text-gray-500">
                        {prefersReducedMotion ? 'Reduced motion enabled' : 'Full animations'}
                      </div>
                    </div>

                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h5 className="font-medium text-blue-800 mb-2">Performance Tips</h5>
                      <ul className="text-sm text-blue-700 space-y-1">
                        <li>• Use lazy loading for images and components</li>
                        <li>• Optimize bundle size with code splitting</li>
                        <li>• Enable compression and caching</li>
                        <li>• Monitor Core Web Vitals regularly</li>
                      </ul>
                    </div>

                    <div className="text-center py-4 text-gray-500">
                      <Zap className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p>Performance monitoring active</p>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default MobileUXTester;

-- Unified User Tracking Tables
-- Creates simplified, efficient tables for user behavior tracking

-- Drop existing overlapping tables if they exist
DROP TABLE IF EXISTS activity_participants CASCADE;
DROP TABLE IF EXISTS user_favorites CASCADE;
DROP TABLE IF EXISTS activity_views CASCADE;
DROP TABLE IF EXISTS activity_attendance CASCADE;
DROP TABLE IF EXISTS artist_preferences CASCADE;

-- Create unified user activities table
CREATE TABLE IF NOT EXISTS user_activities (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type TEXT NOT NULL CHECK (activity_type IN ('view', 'favorite', 'join', 'attend', 'share')),
  target_type TEXT NOT NULL CHECK (target_type IN ('activity', 'event', 'festival', 'tip', 'guide')),
  target_id UUID NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create unified user preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  preference_type TEXT NOT NULL CHECK (preference_type IN ('activity_category', 'music_genre', 'location', 'time_preference')),
  preference_value TEXT NOT NULL,
  weight INTEGER NOT NULL DEFAULT 5 CHECK (weight >= 1 AND weight <= 10),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, preference_type, preference_value)
);

-- Create user suggestions table
CREATE TABLE IF NOT EXISTS user_suggestions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  suggestion_type TEXT NOT NULL CHECK (suggestion_type IN ('activity', 'event', 'festival', 'buddy')),
  target_id UUID NOT NULL,
  confidence_score DECIMAL(3,2) NOT NULL CHECK (confidence_score >= 0 AND confidence_score <= 1),
  reason TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  viewed BOOLEAN DEFAULT FALSE,
  acted_upon BOOLEAN DEFAULT FALSE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_activities_user_id ON user_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activities_target ON user_activities(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_user_activities_type ON user_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_activities_created_at ON user_activities(created_at);

CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_type ON user_preferences(preference_type);
CREATE INDEX IF NOT EXISTS idx_user_preferences_weight ON user_preferences(weight);

CREATE INDEX IF NOT EXISTS idx_user_suggestions_user_id ON user_suggestions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_suggestions_type ON user_suggestions(suggestion_type);
CREATE INDEX IF NOT EXISTS idx_user_suggestions_confidence ON user_suggestions(confidence_score);
CREATE INDEX IF NOT EXISTS idx_user_suggestions_viewed ON user_suggestions(viewed);

-- Enable Row Level Security
ALTER TABLE user_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_suggestions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own activities" ON user_activities
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own activities" ON user_activities
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own preferences" ON user_preferences
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own preferences" ON user_preferences
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own suggestions" ON user_suggestions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own suggestions" ON user_suggestions
  FOR UPDATE USING (auth.uid() = user_id);

-- Create function to generate user suggestions
CREATE OR REPLACE FUNCTION generate_user_suggestions(
  target_user_id UUID,
  suggestion_type TEXT,
  result_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  suggestion_type TEXT,
  target_id UUID,
  confidence_score DECIMAL,
  reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  viewed BOOLEAN,
  acted_upon BOOLEAN
) AS $$
BEGIN
  -- Simple suggestion algorithm based on user preferences and activity history
  RETURN QUERY
  WITH user_prefs AS (
    SELECT preference_type, preference_value, weight
    FROM user_preferences
    WHERE user_preferences.user_id = target_user_id
  ),
  user_activity_history AS (
    SELECT target_type, target_id, activity_type, COUNT(*) as frequency
    FROM user_activities
    WHERE user_activities.user_id = target_user_id
    GROUP BY target_type, target_id, activity_type
  ),
  suggested_items AS (
    SELECT 
      gen_random_uuid() as suggestion_id,
      target_user_id as suggestion_user_id,
      suggestion_type as suggestion_suggestion_type,
      CASE 
        WHEN suggestion_type = 'activity' THEN activities.id
        WHEN suggestion_type = 'event' THEN events.id
        WHEN suggestion_type = 'festival' THEN festivals.id
        ELSE gen_random_uuid()
      END as suggestion_target_id,
      CASE
        WHEN EXISTS (SELECT 1 FROM user_prefs WHERE preference_value = activities.category) THEN 0.8
        WHEN EXISTS (SELECT 1 FROM user_activity_history WHERE target_type = 'activity' AND activity_type = 'favorite') THEN 0.6
        ELSE 0.3
      END as suggestion_confidence_score,
      CASE
        WHEN EXISTS (SELECT 1 FROM user_prefs WHERE preference_value = activities.category) THEN 'Matches your interests in ' || activities.category
        ELSE 'Popular in the community'
      END as suggestion_reason,
      NOW() as suggestion_created_at,
      FALSE as suggestion_viewed,
      FALSE as suggestion_acted_upon
    FROM activities
    WHERE suggestion_type = 'activity'
      AND activities.status = 'published'
      AND NOT EXISTS (
        SELECT 1 FROM user_activities 
        WHERE user_activities.user_id = target_user_id 
          AND user_activities.target_id = activities.id
          AND user_activities.activity_type IN ('join', 'attend')
      )
    
    UNION ALL
    
    SELECT 
      gen_random_uuid(),
      target_user_id,
      suggestion_type,
      events.id,
      0.5,
      'Trending event',
      NOW(),
      FALSE,
      FALSE
    FROM events
    WHERE suggestion_type = 'event'
      AND events.status = 'published'
    
    UNION ALL
    
    SELECT 
      gen_random_uuid(),
      target_user_id,
      suggestion_type,
      festivals.id,
      0.4,
      'Popular festival',
      NOW(),
      FALSE,
      FALSE
    FROM festivals
    WHERE suggestion_type = 'festival'
  )
  SELECT 
    suggestion_id,
    suggestion_user_id,
    suggestion_suggestion_type,
    suggestion_target_id,
    suggestion_confidence_score,
    suggestion_reason,
    suggestion_created_at,
    suggestion_viewed,
    suggestion_acted_upon
  FROM suggested_items
  ORDER BY suggestion_confidence_score DESC
  LIMIT result_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to clean old activities (data retention)
CREATE OR REPLACE FUNCTION cleanup_old_user_activities()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Delete activities older than 1 year
  DELETE FROM user_activities 
  WHERE created_at < NOW() - INTERVAL '1 year';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update user preferences weight based on activity
CREATE OR REPLACE FUNCTION update_preference_weights()
RETURNS VOID AS $$
BEGIN
  -- Increase weight for preferences that match user's recent activities
  UPDATE user_preferences 
  SET weight = LEAST(10, weight + 1),
      updated_at = NOW()
  WHERE (user_id, preference_value) IN (
    SELECT DISTINCT ua.user_id, a.category
    FROM user_activities ua
    JOIN activities a ON ua.target_id = a.id
    WHERE ua.target_type = 'activity'
      AND ua.activity_type IN ('favorite', 'join', 'attend')
      AND ua.created_at > NOW() - INTERVAL '30 days'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON user_activities TO authenticated;
GRANT ALL ON user_preferences TO authenticated;
GRANT ALL ON user_suggestions TO authenticated;
GRANT EXECUTE ON FUNCTION generate_user_suggestions TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_user_activities TO authenticated;
GRANT EXECUTE ON FUNCTION update_preference_weights TO authenticated;

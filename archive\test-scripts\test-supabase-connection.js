#!/usr/bin/env node

/**
 * Supabase Connection Test
 * 
 * This script tests the current Supabase configuration to identify
 * connectivity issues that are preventing the development server from working.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🔍 SUPABASE CONNECTION DIAGNOSTIC');
console.log('=================================');

// Check environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('\n📋 Environment Variables Check:');
console.log(`   VITE_SUPABASE_URL: ${supabaseUrl ? '✅ Present' : '❌ Missing'}`);
console.log(`   VITE_SUPABASE_ANON_KEY: ${supabaseAnonKey ? '✅ Present' : '❌ Missing'}`);

if (supabaseUrl) {
  console.log(`   URL: ${supabaseUrl}`);
}

if (!supabaseUrl || !supabaseAnonKey) {
  console.log('\n❌ CRITICAL: Missing Supabase environment variables');
  console.log('   Please ensure .env file contains valid VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

// Test Supabase connection
async function testSupabaseConnection() {
  try {
    console.log('\n🔗 Testing Supabase Connection...');
    
    // Create client with minimal configuration
    const supabase = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    console.log('   ✅ Client created successfully');

    // Test 1: Basic connectivity
    console.log('\n🧪 Test 1: Basic Database Connectivity');
    try {
      const { data, error } = await supabase.from('profiles').select('count').limit(1);
      
      if (error) {
        console.log(`   ❌ Database query failed: ${error.message}`);
        console.log(`   Error details:`, error);
        return false;
      } else {
        console.log('   ✅ Database query successful');
      }
    } catch (err) {
      console.log(`   ❌ Database connection error: ${err.message}`);
      return false;
    }

    // Test 2: Authentication service
    console.log('\n🧪 Test 2: Authentication Service');
    try {
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {
        console.log(`   ⚠️  Auth service error: ${error.message}`);
        // Auth errors are expected when not logged in
      } else {
        console.log('   ✅ Authentication service accessible');
      }
    } catch (err) {
      console.log(`   ❌ Auth service connection error: ${err.message}`);
      return false;
    }

    // Test 3: Check for admin users
    console.log('\n🧪 Test 3: Admin User Check');
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, username, role')
        .in('role', ['SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR'])
        .limit(5);
      
      if (error) {
        console.log(`   ❌ Admin user query failed: ${error.message}`);
      } else {
        console.log(`   ✅ Admin user query successful`);
        console.log(`   Found ${data.length} admin users:`);
        data.forEach(admin => {
          console.log(`     - ${admin.username}: ${admin.role}`);
        });
      }
    } catch (err) {
      console.log(`   ❌ Admin user check error: ${err.message}`);
    }

    return true;

  } catch (error) {
    console.log(`\n❌ CRITICAL: Supabase connection failed`);
    console.log(`   Error: ${error.message}`);
    console.log(`   Stack:`, error.stack);
    return false;
  }
}

// Test environment setup
async function testEnvironmentSetup() {
  console.log('\n🌍 Environment Setup Check:');
  
  // Check Node.js version
  console.log(`   Node.js version: ${process.version}`);
  
  // Check if we're in the right directory
  try {
    const fs = await import('fs');
    const packageExists = fs.existsSync('./package.json');
    console.log(`   Package.json: ${packageExists ? '✅ Found' : '❌ Missing'}`);
    
    if (packageExists) {
      const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
      console.log(`   Project: ${packageJson.name}`);
    }
  } catch (err) {
    console.log(`   ❌ File system check error: ${err.message}`);
  }
}

// Main test function
async function runDiagnostics() {
  await testEnvironmentSetup();
  
  const connectionSuccess = await testSupabaseConnection();
  
  console.log('\n📊 DIAGNOSTIC SUMMARY');
  console.log('====================');
  
  if (connectionSuccess) {
    console.log('✅ Supabase connection is working');
    console.log('✅ Environment variables are valid');
    console.log('✅ Database is accessible');
    console.log('\n🎯 RECOMMENDATION: The Supabase configuration appears to be working.');
    console.log('   The development server issues may be related to:');
    console.log('   1. Browser security policies (CSP)');
    console.log('   2. Import.meta.env usage in core-client.ts');
    console.log('   3. Vite configuration issues');
  } else {
    console.log('❌ Supabase connection failed');
    console.log('\n🔧 RECOMMENDED ACTIONS:');
    console.log('   1. Verify Supabase project is active');
    console.log('   2. Check API keys are not expired');
    console.log('   3. Ensure project URL is correct');
    console.log('   4. Check Supabase project settings');
  }
}

// Run diagnostics
runDiagnostics().catch(console.error);

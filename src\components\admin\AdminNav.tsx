import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { useAuth } from '@/providers/ConsolidatedAuthProvider';
import { hasPermission } from '@/lib/utils/roleUtils';
import { type Permission } from '@/types/core';
import {
  LayoutDashboard,
  BarChart3,
  Tent,
  Calendar,
  Users,
  MessageSquare,
  ExternalLink,
  MapPin,
  Bell,
  HelpCircle,
  BookOpen,
  Lightbulb,
  Palette,
} from 'lucide-react';

interface AdminNavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  permission?: Permission;
}

const adminNavItems: AdminNavItem[] = [
  {
    title: 'Dashboard',
    href: '/admin',
    icon: LayoutDashboard,
    // Dashboard accessible to all admin roles
  },
  {
    title: 'Analytics',
    href: '/admin/analytics',
    icon: BarChart3,
    // Analytics accessible to all admin roles
  },
  {
    title: 'Festivals',
    href: '/admin/festivals',
    icon: Tent,
    permission: 'manage_festivals', // SUPER_ADMIN and CONTENT_ADMIN only
  },
  {
    title: 'Events',
    href: '/admin/events',
    icon: Calendar,
    permission: 'manage_events', // SUPER_ADMIN and CONTENT_ADMIN only
  },
  {
    title: 'Users',
    href: '/admin/users',
    icon: Users,
    permission: 'manage_users', // SUPER_ADMIN only
  },
  {
    title: 'Activities',
    href: '/admin/activities',
    icon: MessageSquare,
    permission: 'manage_activities', // SUPER_ADMIN, CONTENT_ADMIN, and MODERATOR
  },
  {
    title: 'External Links',
    href: '/admin/external-links',
    icon: ExternalLink,
    permission: 'manage_content', // SUPER_ADMIN and CONTENT_ADMIN only
  },
  {
    title: 'Local Information',
    href: '/admin/local-info',
    icon: MapPin,
    permission: 'manage_content', // SUPER_ADMIN and CONTENT_ADMIN only
  },
  {
    title: 'Announcements',
    href: '/admin/announcements',
    icon: Bell,
    permission: 'manage_announcements', // SUPER_ADMIN, CONTENT_ADMIN, and MODERATOR
  },
  {
    title: 'FAQs',
    href: '/admin/faqs',
    icon: HelpCircle,
    permission: 'manage_content', // SUPER_ADMIN and CONTENT_ADMIN only
  },
  {
    title: 'Guides',
    href: '/admin/guides',
    icon: BookOpen,
    permission: 'manage_content', // SUPER_ADMIN and CONTENT_ADMIN only
  },
  {
    title: 'Tips',
    href: '/admin/tips',
    icon: Lightbulb,
    permission: 'manage_content', // SUPER_ADMIN and CONTENT_ADMIN only
  },
  {
    title: 'Icon & Emoji Management',
    href: '/admin/icon-emoji-management',
    icon: Palette,
    permission: 'manage_content', // SUPER_ADMIN and CONTENT_ADMIN only
  },
];

interface AdminNavProps {
  onNavigate?: () => void;
}

const AdminNav = ({ onNavigate }: AdminNavProps) => {
  const location = useLocation();
  const { profile } = useAuth();

  // Filter navigation items based on user permissions
  const visibleNavItems = adminNavItems.filter((item) => {
    // Dashboard is always visible to admin users
    if (!item.permission) return true;

    // Check if user has the required permission
    return profile?.role && hasPermission(profile.role, item.permission);
  });

  return (
    <nav className="space-y-1">
      {visibleNavItems.map((item) => {
        const isActive = location.pathname === item.href;
        const Icon = item.icon;

        return (
          <Link
            key={item.href}
            to={item.href}
            onClick={onNavigate}
            className={cn(
              'flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors',
              isActive
                ? 'bg-primary/20 text-primary border border-primary/30'
                : 'text-muted-foreground hover:bg-muted hover:text-foreground'
            )}
          >
            <Icon className="mr-3 h-5 w-5" />
            {item.title}
          </Link>
        );
      })}
    </nav>
  );
};

export default AdminNav;

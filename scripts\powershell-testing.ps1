# PowerShell Evidence-Based Testing Script
# This script uses PowerShell-compatible commands to test the application

Write-Host "🔍 EVIDENCE-BASED TESTING - POWERSHELL APPROACH" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

$AppUrl = "http://localhost:5173"
$EvidenceDir = "test-evidence-ps"
$TestStartTime = Get-Date

# Create evidence directory
if (!(Test-Path $EvidenceDir)) {
    New-Item -ItemType Directory -Path $EvidenceDir
    Write-Host "📁 Created evidence directory: $EvidenceDir" -ForegroundColor Green
}

# Test 1: Application Accessibility
Write-Host "`n🌐 Test 1: Application Accessibility" -ForegroundColor Yellow
Write-Host "====================================" -ForegroundColor Yellow

try {
    $StartTime = Get-Date
    $Response = Invoke-WebRequest -Uri $AppUrl -TimeoutSec 30 -UseBasicParsing
    $LoadTime = (Get-Date) - $StartTime
    
    $Evidence1 = @{
        TestName = "Application Accessibility"
        Timestamp = $TestStartTime.ToString("yyyy-MM-dd HH:mm:ss")
        Url = $AppUrl
        StatusCode = $Response.StatusCode
        StatusDescription = $Response.StatusDescription
        LoadTimeMs = [math]::Round($LoadTime.TotalMilliseconds, 2)
        ContentLength = $Response.Content.Length
        ContentType = $Response.Headers["Content-Type"]
        Success = $true
    }
    
    # Save HTML content for analysis
    $Response.Content | Out-File -FilePath "$EvidenceDir/01-page-content.html" -Encoding UTF8
    
    Write-Host "✅ Application accessible" -ForegroundColor Green
    Write-Host "   Status: $($Response.StatusCode) $($Response.StatusDescription)" -ForegroundColor White
    Write-Host "   Load Time: $([math]::Round($LoadTime.TotalMilliseconds, 2))ms" -ForegroundColor White
    Write-Host "   Content Size: $($Response.Content.Length) characters" -ForegroundColor White
    
} catch {
    $Evidence1 = @{
        TestName = "Application Accessibility"
        Timestamp = $TestStartTime.ToString("yyyy-MM-dd HH:mm:ss")
        Url = $AppUrl
        Success = $false
        Error = $_.Exception.Message
    }
    
    Write-Host "❌ Application not accessible: $($_.Exception.Message)" -ForegroundColor Red
}

# Save evidence
$Evidence1 | ConvertTo-Json -Depth 3 | Out-File -FilePath "$EvidenceDir/01-accessibility-evidence.json" -Encoding UTF8

# Test 2: HTML Content Analysis
Write-Host "`n📄 Test 2: HTML Content Analysis" -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Yellow

if ($Evidence1.Success) {
    $HtmlContent = Get-Content "$EvidenceDir/01-page-content.html" -Raw
    
    # Check for key elements
    $HasTitle = $HtmlContent -match "<title>(.*?)</title>"
    $HasReact = $HtmlContent -match "react|React"
    $HasVite = $HtmlContent -match "vite|Vite"
    $HasSupabase = $HtmlContent -match "supabase|Supabase"
    $HasRootDiv = $HtmlContent -match 'id="root"'
    
    # Extract title if present
    $PageTitle = if ($Matches[1]) { $Matches[1] } else { "No title found" }
    
    $Evidence2 = @{
        TestName = "HTML Content Analysis"
        Timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        PageTitle = $PageTitle
        HasTitle = $HasTitle
        HasReact = $HasReact
        HasVite = $HasVite
        HasSupabase = $HasSupabase
        HasRootDiv = $HasRootDiv
        ContentAnalysis = @{
            TotalLength = $HtmlContent.Length
            LineCount = ($HtmlContent -split "`n").Count
        }
    }
    
    Write-Host "📋 Page Title: '$PageTitle'" -ForegroundColor White
    Write-Host "🔍 Content Analysis:" -ForegroundColor White
    Write-Host "   - Has Title Tag: $(if($HasTitle){'✅'}else{'❌'})" -ForegroundColor White
    Write-Host "   - React References: $(if($HasReact){'✅'}else{'❌'})" -ForegroundColor White
    Write-Host "   - Vite References: $(if($HasVite){'✅'}else{'❌'})" -ForegroundColor White
    Write-Host "   - Supabase References: $(if($HasSupabase){'✅'}else{'❌'})" -ForegroundColor White
    Write-Host "   - Root Div Present: $(if($HasRootDiv){'✅'}else{'❌'})" -ForegroundColor White
    
    $Evidence2 | ConvertTo-Json -Depth 3 | Out-File -FilePath "$EvidenceDir/02-content-analysis.json" -Encoding UTF8
}

# Test 3: Network Performance
Write-Host "`n⚡ Test 3: Network Performance" -ForegroundColor Yellow
Write-Host "==============================" -ForegroundColor Yellow

$PerformanceTests = @()

for ($i = 1; $i -le 3; $i++) {
    try {
        $StartTime = Get-Date
        $Response = Invoke-WebRequest -Uri $AppUrl -TimeoutSec 30 -UseBasicParsing
        $LoadTime = (Get-Date) - $StartTime
        
        $PerformanceTests += @{
            TestRun = $i
            LoadTimeMs = [math]::Round($LoadTime.TotalMilliseconds, 2)
            StatusCode = $Response.StatusCode
            Success = $true
        }
        
        Write-Host "🔄 Load $i`: $([math]::Round($LoadTime.TotalMilliseconds, 2))ms" -ForegroundColor White
        
    } catch {
        $PerformanceTests += @{
            TestRun = $i
            Success = $false
            Error = $_.Exception.Message
        }
        Write-Host "❌ Load $i`: Failed - $($_.Exception.Message)" -ForegroundColor Red
    }
}

$SuccessfulTests = $PerformanceTests | Where-Object { $_.Success }
if ($SuccessfulTests.Count -gt 0) {
    $AverageLoadTime = ($SuccessfulTests | Measure-Object -Property LoadTimeMs -Average).Average
    Write-Host "📊 Average Load Time: $([math]::Round($AverageLoadTime, 2))ms" -ForegroundColor Green
}

$Evidence3 = @{
    TestName = "Network Performance"
    Timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
    PerformanceTests = $PerformanceTests
    AverageLoadTime = if ($SuccessfulTests.Count -gt 0) { [math]::Round($AverageLoadTime, 2) } else { $null }
    SuccessfulTests = $SuccessfulTests.Count
    TotalTests = $PerformanceTests.Count
}

$Evidence3 | ConvertTo-Json -Depth 3 | Out-File -FilePath "$EvidenceDir/03-performance-evidence.json" -Encoding UTF8

# Test 4: API Endpoint Testing
Write-Host "`n🔌 Test 4: API Endpoint Testing" -ForegroundColor Yellow
Write-Host "===============================" -ForegroundColor Yellow

# Read Supabase configuration
$EnvFile = ".env.local"
if (Test-Path $EnvFile) {
    $EnvContent = Get-Content $EnvFile
    $SupabaseUrl = ($EnvContent | Where-Object { $_ -match "VITE_SUPABASE_URL=" }) -replace "VITE_SUPABASE_URL=", ""
    $SupabaseKey = ($EnvContent | Where-Object { $_ -match "VITE_SUPABASE_ANON_KEY=" }) -replace "VITE_SUPABASE_ANON_KEY=", ""
    
    if ($SupabaseUrl -and $SupabaseKey) {
        Write-Host "🔗 Testing Supabase API: $SupabaseUrl" -ForegroundColor White
        
        try {
            $Headers = @{
                "apikey" = $SupabaseKey
                "Authorization" = "Bearer $SupabaseKey"
            }
            
            $ApiResponse = Invoke-WebRequest -Uri "$SupabaseUrl/rest/v1/" -Headers $Headers -TimeoutSec 15 -UseBasicParsing
            
            Write-Host "✅ Supabase API accessible" -ForegroundColor Green
            Write-Host "   Status: $($ApiResponse.StatusCode)" -ForegroundColor White
            
            $Evidence4 = @{
                TestName = "API Endpoint Testing"
                Timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
                SupabaseUrl = $SupabaseUrl
                ApiAccessible = $true
                StatusCode = $ApiResponse.StatusCode
            }
            
        } catch {
            Write-Host "⚠️ Supabase API test: $($_.Exception.Message)" -ForegroundColor Yellow
            
            $Evidence4 = @{
                TestName = "API Endpoint Testing"
                Timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
                SupabaseUrl = $SupabaseUrl
                ApiAccessible = $false
                Error = $_.Exception.Message
            }
        }
    } else {
        Write-Host "⚠️ Supabase configuration not found in .env.local" -ForegroundColor Yellow
        $Evidence4 = @{
            TestName = "API Endpoint Testing"
            Timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
            ConfigurationFound = $false
        }
    }
} else {
    Write-Host "⚠️ .env.local file not found" -ForegroundColor Yellow
    $Evidence4 = @{
        TestName = "API Endpoint Testing"
        Timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        EnvFileFound = $false
    }
}

$Evidence4 | ConvertTo-Json -Depth 3 | Out-File -FilePath "$EvidenceDir/04-api-testing-evidence.json" -Encoding UTF8

# Generate Final Report
Write-Host "`n📊 FINAL EVIDENCE-BASED TESTING REPORT" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

$TestEndTime = Get-Date
$TotalDuration = $TestEndTime - $TestStartTime

$FinalReport = @{
    SessionInfo = @{
        StartTime = $TestStartTime.ToString("yyyy-MM-dd HH:mm:ss")
        EndTime = $TestEndTime.ToString("yyyy-MM-dd HH:mm:ss")
        DurationSeconds = [math]::Round($TotalDuration.TotalSeconds, 2)
    }
    ApplicationUrl = $AppUrl
    EvidenceDirectory = $EvidenceDir
    TestResults = @{
        ApplicationAccessible = $Evidence1.Success
        ContentAnalysisCompleted = $Evidence2 -ne $null
        PerformanceTestsCompleted = $Evidence3.TotalTests
        ApiTestingCompleted = $Evidence4 -ne $null
    }
    EvidenceFiles = @(
        "01-page-content.html",
        "01-accessibility-evidence.json",
        "02-content-analysis.json", 
        "03-performance-evidence.json",
        "04-api-testing-evidence.json"
    )
}

$FinalReport | ConvertTo-Json -Depth 4 | Out-File -FilePath "$EvidenceDir/final-report.json" -Encoding UTF8

Write-Host "✅ Testing completed successfully" -ForegroundColor Green
Write-Host "📁 Evidence saved in: $EvidenceDir" -ForegroundColor White
Write-Host "⏱️ Total duration: $([math]::Round($TotalDuration.TotalSeconds, 2)) seconds" -ForegroundColor White
Write-Host "📄 Evidence files: $($FinalReport.EvidenceFiles.Count)" -ForegroundColor White

# List evidence files
Write-Host "`n📋 Evidence Files Generated:" -ForegroundColor White
Get-ChildItem $EvidenceDir | ForEach-Object {
    Write-Host "   - $($_.Name)" -ForegroundColor Gray
}

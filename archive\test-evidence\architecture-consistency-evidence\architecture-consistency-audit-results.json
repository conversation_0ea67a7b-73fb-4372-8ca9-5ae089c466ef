{"timestamp": "2025-05-30T01:01:39.143Z", "testUser": {"email": "<EMAIL>", "password": "TestPassword123!", "fullName": "Architecture Test User"}, "architectureResults": {"uiConsistency": [{"pageName": "Home", "url": "/", "uiAnalysis": {"navigation": {"count": 3, "classes": [["bg-white/10", "backdrop-blur-md", "border-b", "border-white/10", "sticky", "top-0", "z-40"], ["relative", "z-10", "p-6"], ["fixed", "bottom-0", "left-0", "right-0", "w-full", "bg-black/80", "backdrop-blur-md", "border-t", "border-white/10", "shadow-lg", "z-40", "md:hidden"]]}, "buttons": {"count": 4, "styles": [{"backgroundColor": "rgba(0, 0, 0, 0)", "color": "rgba(255, 255, 255, 0.7)", "borderRadius": "6px", "padding": "8px 4px", "fontSize": "16px", "fontWeight": "400"}, {"backgroundColor": "rgb(22, 163, 74)", "color": "rgb(255, 255, 255)", "borderRadius": "4px", "padding": "4px 12px", "fontSize": "16px", "fontWeight": "400"}, {"backgroundColor": "rgb(22, 163, 74)", "color": "rgb(255, 255, 255)", "borderRadius": "4px", "padding": "4px 12px", "fontSize": "16px", "fontWeight": "400"}, {"backgroundColor": "rgba(0, 0, 0, 0)", "color": "rgba(255, 255, 255, 0.9)", "borderRadius": "9999px", "padding": "0px", "fontSize": "16px", "fontWeight": "400"}]}, "inputs": {"count": 0, "styles": []}, "colors": {"textColors": ["rgb(0, 0, 0)", "rgba(255, 255, 255, 0.9)", "rgb(255, 255, 255)", "rgba(255, 255, 255, 0.7)", "rgb(192, 132, 252)", "rgba(255, 255, 255, 0.8)", "rgb(96, 165, 250)", "rgb(244, 114, 182)", "rgb(74, 222, 128)", "rgba(255, 255, 255, 0.6)", "rgb(106, 13, 173)", "rgb(54, 54, 54)"], "backgroundColors": ["rgba(255, 255, 255, 0.1)", "rgb(126, 34, 206)", "rgba(168, 85, 247, 0.2)", "rgba(59, 130, 246, 0.2)", "rgba(236, 72, 153, 0.2)", "rgba(34, 197, 94, 0.2)", "rgba(0, 0, 0, 0.8)", "rgba(45, 27, 105, 0.8)", "rgb(97, 211, 69)", "rgb(255, 255, 255)", "rgb(34, 197, 94)", "rgb(22, 163, 74)"]}, "typography": {"headings": [{"tag": "H1", "fontSize": "24px", "fontWeight": "700", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "0px"}, {"tag": "H1", "fontSize": "60px", "fontWeight": "700", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "24px"}, {"tag": "H2", "fontSize": "36px", "fontWeight": "700", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "16px"}, {"tag": "H3", "fontSize": "20px", "fontWeight": "600", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "8px"}, {"tag": "H3", "fontSize": "20px", "fontWeight": "600", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "8px"}, {"tag": "H3", "fontSize": "20px", "fontWeight": "600", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "8px"}, {"tag": "H3", "fontSize": "20px", "fontWeight": "600", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "8px"}, {"tag": "H2", "fontSize": "30px", "fontWeight": "700", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "16px"}, {"tag": "H2", "fontSize": "36px", "fontWeight": "700", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "24px"}, {"tag": "H3", "fontSize": "16px", "fontWeight": "700", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "8px"}, {"tag": "H3", "fontSize": "16px", "fontWeight": "700", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "8px"}]}, "layout": {"flexGridElements": 33}}, "screenshot": "ui-consistency-home.png"}, {"pageName": "Activities", "url": "/activities", "uiAnalysis": {"navigation": {"count": 0, "classes": []}, "buttons": {"count": 8, "styles": [{"backgroundColor": "rgb(126, 34, 206)", "color": "rgb(255, 255, 255)", "borderRadius": "6px", "padding": "12px 16px", "fontSize": "16px", "fontWeight": "500"}, {"backgroundColor": "rgba(0, 0, 0, 0)", "color": "rgba(255, 255, 255, 0.7)", "borderRadius": "4px", "padding": "4px 8px", "fontSize": "14px", "fontWeight": "400"}, {"backgroundColor": "rgba(0, 0, 0, 0)", "color": "rgba(255, 255, 255, 0.7)", "borderRadius": "4px", "padding": "4px 8px", "fontSize": "14px", "fontWeight": "400"}, {"backgroundColor": "rgba(255, 255, 255, 0.05)", "color": "rgba(255, 255, 255, 0.5)", "borderRadius": "6px", "padding": "8px 16px", "fontSize": "16px", "fontWeight": "500"}, {"backgroundColor": "rgba(255, 255, 255, 0.05)", "color": "rgba(255, 255, 255, 0.5)", "borderRadius": "6px", "padding": "8px 16px", "fontSize": "16px", "fontWeight": "500"}, {"backgroundColor": "rgb(22, 163, 74)", "color": "rgb(255, 255, 255)", "borderRadius": "4px", "padding": "4px 12px", "fontSize": "16px", "fontWeight": "400"}, {"backgroundColor": "rgb(22, 163, 74)", "color": "rgb(255, 255, 255)", "borderRadius": "4px", "padding": "4px 12px", "fontSize": "16px", "fontWeight": "400"}, {"backgroundColor": "rgba(0, 0, 0, 0)", "color": "rgba(255, 255, 255, 0.9)", "borderRadius": "9999px", "padding": "0px", "fontSize": "16px", "fontWeight": "400"}]}, "inputs": {"count": 3, "styles": [{"borderRadius": "6px", "border": "1px solid rgba(255, 255, 255, 0.2)", "padding": "8px 12px", "fontSize": "16px"}, {"borderRadius": "6px", "border": "1px solid rgba(255, 255, 255, 0.2)", "padding": "8px 12px", "fontSize": "16px"}, {"borderRadius": "0px", "border": "0px none rgb(147, 51, 234)", "padding": "0px", "fontSize": "16px"}]}, "colors": {"textColors": ["rgb(0, 0, 0)", "rgba(255, 255, 255, 0.9)", "rgb(255, 255, 255)", "rgba(255, 255, 255, 0.7)", "rgb(248, 113, 113)", "rgb(147, 51, 234)", "rgba(255, 255, 255, 0.5)", "rgb(54, 54, 54)"], "backgroundColors": ["rgba(255, 255, 255, 0.1)", "rgb(126, 34, 206)", "rgba(74, 29, 150, 0.5)", "rgba(255, 255, 255, 0.05)", "rgb(255, 255, 255)", "rgb(97, 211, 69)", "rgb(34, 197, 94)", "rgb(22, 163, 74)"]}, "typography": {"headings": [{"tag": "H1", "fontSize": "30px", "fontWeight": "700", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "8px"}, {"tag": "H3", "fontSize": "16px", "fontWeight": "700", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "8px"}, {"tag": "H3", "fontSize": "16px", "fontWeight": "700", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "8px"}]}, "layout": {"flexGridElements": 19}}, "screenshot": "ui-consistency-activities.png"}, {"pageName": "Profile", "url": "/profile", "uiAnalysis": {"navigation": {"count": 0, "classes": []}, "buttons": {"count": 8, "styles": [{"backgroundColor": "rgb(126, 34, 206)", "color": "rgb(255, 255, 255)", "borderRadius": "6px", "padding": "12px 16px", "fontSize": "16px", "fontWeight": "500"}, {"backgroundColor": "rgba(0, 0, 0, 0)", "color": "rgba(255, 255, 255, 0.7)", "borderRadius": "4px", "padding": "4px 8px", "fontSize": "14px", "fontWeight": "400"}, {"backgroundColor": "rgba(0, 0, 0, 0)", "color": "rgba(255, 255, 255, 0.7)", "borderRadius": "4px", "padding": "4px 8px", "fontSize": "14px", "fontWeight": "400"}, {"backgroundColor": "rgba(255, 255, 255, 0.05)", "color": "rgba(255, 255, 255, 0.5)", "borderRadius": "6px", "padding": "8px 16px", "fontSize": "16px", "fontWeight": "500"}, {"backgroundColor": "rgba(255, 255, 255, 0.05)", "color": "rgba(255, 255, 255, 0.5)", "borderRadius": "6px", "padding": "8px 16px", "fontSize": "16px", "fontWeight": "500"}, {"backgroundColor": "rgb(22, 163, 74)", "color": "rgb(255, 255, 255)", "borderRadius": "4px", "padding": "4px 12px", "fontSize": "16px", "fontWeight": "400"}, {"backgroundColor": "rgb(22, 163, 74)", "color": "rgb(255, 255, 255)", "borderRadius": "4px", "padding": "4px 12px", "fontSize": "16px", "fontWeight": "400"}, {"backgroundColor": "rgba(0, 0, 0, 0)", "color": "rgba(255, 255, 255, 0.9)", "borderRadius": "9999px", "padding": "0px", "fontSize": "16px", "fontWeight": "400"}]}, "inputs": {"count": 3, "styles": [{"borderRadius": "6px", "border": "1px solid rgba(255, 255, 255, 0.2)", "padding": "8px 12px", "fontSize": "16px"}, {"borderRadius": "6px", "border": "1px solid rgba(255, 255, 255, 0.2)", "padding": "8px 12px", "fontSize": "16px"}, {"borderRadius": "0px", "border": "0px none rgb(147, 51, 234)", "padding": "0px", "fontSize": "16px"}]}, "colors": {"textColors": ["rgb(0, 0, 0)", "rgba(255, 255, 255, 0.9)", "rgb(255, 255, 255)", "rgba(255, 255, 255, 0.7)", "rgb(248, 113, 113)", "rgb(147, 51, 234)", "rgba(255, 255, 255, 0.5)", "rgb(54, 54, 54)"], "backgroundColors": ["rgba(255, 255, 255, 0.1)", "rgb(126, 34, 206)", "rgba(74, 29, 150, 0.5)", "rgba(255, 255, 255, 0.05)", "rgb(255, 255, 255)", "rgb(97, 211, 69)", "rgb(34, 197, 94)", "rgb(22, 163, 74)"]}, "typography": {"headings": [{"tag": "H1", "fontSize": "30px", "fontWeight": "700", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "8px"}, {"tag": "H3", "fontSize": "16px", "fontWeight": "700", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "8px"}, {"tag": "H3", "fontSize": "16px", "fontWeight": "700", "color": "rgb(255, 255, 255)", "marginTop": "0px", "marginBottom": "8px"}]}, "layout": {"flexGridElements": 19}}, "screenshot": "ui-consistency-profile.png"}], "authConsistency": {"authStates": [{"page": "Home", "expectedUrl": "/", "actualUrl": "http://localhost:5173/", "isOnExpectedPage": true, "isOnAuthPage": false, "hasSignOut": false, "hasSignIn": true, "hasUserProfile": false, "hasAdminLink": false, "authStateConsistent": false}, {"page": "Activities", "expectedUrl": "/activities", "actualUrl": "http://localhost:5173/auth", "isOnExpectedPage": false, "isOnAuthPage": true, "hasSignOut": false, "hasSignIn": true, "hasUserProfile": false, "hasAdminLink": false, "authStateConsistent": false}, {"page": "<PERSON><PERSON><PERSON><PERSON>", "expectedUrl": "/famhub", "actualUrl": "http://localhost:5173/auth", "isOnExpectedPage": false, "isOnAuthPage": true, "hasSignOut": false, "hasSignIn": true, "hasUserProfile": false, "hasAdminLink": false, "authStateConsistent": false}, {"page": "Discover", "expectedUrl": "/discover", "actualUrl": "http://localhost:5173/auth", "isOnExpectedPage": false, "isOnAuthPage": true, "hasSignOut": false, "hasSignIn": true, "hasUserProfile": false, "hasAdminLink": false, "authStateConsistent": false}, {"page": "Profile", "expectedUrl": "/profile", "actualUrl": "http://localhost:5173/auth", "isOnExpectedPage": false, "isOnAuthPage": true, "hasSignOut": false, "hasSignIn": true, "hasUserProfile": false, "hasAdminLink": false, "authStateConsistent": false}], "consistencyScore": 0, "consistentPages": 0, "totalPages": 5}, "componentReuse": {"componentAnalysis": [{"page": "Home", "components": {"navigation": 3, "buttons": 4, "forms": 0, "inputs": 0, "cards": 0, "modals": 0, "headers": 0, "footers": 1}, "navClasses": ["bg-white/10 backdrop-blur-md border-b border-white/10 sticky top-0 z-40", "relative z-10 p-6", "fixed bottom-0 left-0 right-0 w-full bg-black/80 backdrop-blur-md border-t border-white/10 shadow-lg z-40 md:hidden"], "hasMultipleNavs": true}, {"page": "Activities", "components": {"navigation": 0, "buttons": 8, "forms": 1, "inputs": 3, "cards": 0, "modals": 0, "headers": 0, "footers": 0}, "navClasses": [], "hasMultipleNavs": false}, {"page": "Profile", "components": {"navigation": 0, "buttons": 8, "forms": 1, "inputs": 3, "cards": 0, "modals": 0, "headers": 0, "footers": 0}, "navClasses": [], "hasMultipleNavs": false}], "navConsistent": false, "multipleNavsDetected": true, "consistencyScore": 50}}, "summary": {"authConsistencyScore": 0, "componentConsistencyScore": 50, "uiConsistencyScore": 75, "overallScore": 41.7, "architectureIssues": ["Authentication state inconsistency across pages", "Multiple navigation components detected (potential duplication)", "Navigation component inconsistency across pages"], "screenshots": ["ui-consistency-home.png", "ui-consistency-activities.png", "ui-consistency-profile.png"]}}
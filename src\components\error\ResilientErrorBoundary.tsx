/**
 * Resilient Error Boundary
 *
 * A comprehensive error boundary that provides intelligent error handling,
 * automatic retry mechanisms, and graceful degradation for production resilience.
 */

import React, { useState, useCallback, useEffect } from 'react';
import { ErrorBoundary, FallbackProps } from 'react-error-boundary';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { UnifiedFallback, type FallbackType } from './UnifiedFallbackSystem';
import { logAndReportError } from '@/lib/monitoring/errorLogging';

interface ResilientErrorBoundaryProps {
  children: React.ReactNode;
  fallbackType?: FallbackType;
  maxRetries?: number;
  retryDelay?: number;
  enableAutoRetry?: boolean;
  onError?: (error: Error, errorInfo: { componentStack?: string | null }) => void;
  isolationLevel?: 'component' | 'page' | 'section';
  gracefulDegradation?: React.ReactNode;
}

interface ErrorState {
  retryCount: number;
  lastError: Error | null;
  isAutoRetrying: boolean;
}

// Error classification for intelligent handling
const classifyError = (error: Error): FallbackType => {
  const message = error.message.toLowerCase();
  const stack = error.stack?.toLowerCase() || '';

  // Network-related errors
  if (
    message.includes('fetch') ||
    message.includes('network') ||
    message.includes('connection') ||
    message.includes('timeout') ||
    stack.includes('networkerror')
  ) {
    return 'network-error';
  }

  // Dynamic import/chunk loading errors
  if (
    message.includes('loading chunk') ||
    message.includes('dynamic import') ||
    message.includes('failed to fetch') ||
    stack.includes('chunkloaderror')
  ) {
    return 'network-error';
  }

  // Database/API errors
  if (
    message.includes('supabase') ||
    message.includes('database') ||
    message.includes('api') ||
    message.includes('unauthorized') ||
    message.includes('forbidden')
  ) {
    return 'data-loading-error';
  }

  // Permission errors
  if (
    message.includes('permission') ||
    message.includes('unauthorized') ||
    message.includes('forbidden') ||
    message.includes('access denied')
  ) {
    return 'permission-denied';
  }

  // Component-specific errors
  return 'component-crash';
};

// Determine if error is retryable
const isRetryableError = (error: Error): boolean => {
  const message = error.message.toLowerCase();
  
  // Network errors are usually retryable
  if (
    message.includes('fetch') ||
    message.includes('network') ||
    message.includes('timeout') ||
    message.includes('loading chunk')
  ) {
    return true;
  }

  // Database connection errors are retryable
  if (
    message.includes('connection') ||
    message.includes('timeout')
  ) {
    return true;
  }

  // Permission and syntax errors are not retryable
  if (
    message.includes('permission') ||
    message.includes('syntax') ||
    message.includes('reference') ||
    message.includes('type')
  ) {
    return false;
  }

  // Default to retryable for unknown errors
  return true;
};

const ResilientErrorFallback: React.FC<FallbackProps & {
  maxRetries: number;
  retryDelay: number;
  enableAutoRetry: boolean;
  fallbackType?: FallbackType;
  isolationLevel: string;
  gracefulDegradation?: React.ReactNode;
}> = ({
  error,
  resetErrorBoundary,
  maxRetries,
  retryDelay,
  enableAutoRetry,
  fallbackType,
  isolationLevel,
  gracefulDegradation
}) => {
  const navigate = useNavigate();
  const [errorState, setErrorState] = useState<ErrorState>({
    retryCount: 0,
    lastError: error,
    isAutoRetrying: false
  });

  const classifiedType = fallbackType || classifyError(error);
  const isRetryable = isRetryableError(error);

  // Auto-retry mechanism
  useEffect(() => {
    if (
      enableAutoRetry &&
      isRetryable &&
      errorState.retryCount < maxRetries &&
      !errorState.isAutoRetrying
    ) {
      const timer = setTimeout(() => {
        setErrorState(prev => ({
          ...prev,
          retryCount: prev.retryCount + 1,
          isAutoRetrying: true
        }));

        // Attempt automatic recovery
        setTimeout(() => {
          resetErrorBoundary();
        }, 100);
      }, retryDelay * Math.pow(2, errorState.retryCount)); // Exponential backoff

      return () => clearTimeout(timer);
    }
  }, [
    enableAutoRetry,
    isRetryable,
    errorState.retryCount,
    errorState.isAutoRetrying,
    maxRetries,
    retryDelay,
    resetErrorBoundary
  ]);

  const handleManualRetry = useCallback(() => {
    setErrorState(prev => ({
      ...prev,
      retryCount: prev.retryCount + 1,
      isAutoRetrying: false
    }));
    resetErrorBoundary();
  }, [resetErrorBoundary]);

  const handleGoHome = useCallback(() => {
    navigate('/');
    resetErrorBoundary();
  }, [navigate, resetErrorBoundary]);

  const handleGoBack = useCallback(() => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/');
    }
    resetErrorBoundary();
  }, [navigate, resetErrorBoundary]);

  // Show graceful degradation for component-level errors if available
  if (isolationLevel === 'component' && gracefulDegradation) {
    return (
      <div className="border border-border rounded-lg p-4 bg-muted/50">
        <div className="text-sm text-muted-foreground mb-2">
          Some content couldn't load, but here's what we can show:
        </div>
        {gracefulDegradation}
      </div>
    );
  }

  // Determine retry availability
  const canRetry = isRetryable && errorState.retryCount < maxRetries;
  const showAutoRetryMessage = enableAutoRetry && canRetry && errorState.isAutoRetrying;

  return (
    <div className="min-h-[200px] flex items-center justify-center">
      <UnifiedFallback
        type={classifiedType}
        error={error}
        onRetry={canRetry ? handleManualRetry : undefined}
        onGoHome={isolationLevel !== 'component' ? handleGoHome : undefined}
        onGoBack={isolationLevel === 'page' ? handleGoBack : undefined}
        showRetry={canRetry && !showAutoRetryMessage}
        showHome={isolationLevel !== 'component'}
        showBack={isolationLevel === 'page'}
        retryLabel={
          errorState.retryCount > 0 
            ? `Try Again (${errorState.retryCount}/${maxRetries})` 
            : 'Try Again'
        }
        message={
          showAutoRetryMessage
            ? `Automatically retrying in a moment... (${errorState.retryCount}/${maxRetries})`
            : undefined
        }
      />
    </div>
  );
};

export const ResilientErrorBoundary: React.FC<ResilientErrorBoundaryProps> = ({
  children,
  fallbackType,
  maxRetries = 3,
  retryDelay = 1000,
  enableAutoRetry = true,
  onError,
  isolationLevel = 'section',
  gracefulDegradation
}) => {
  const handleError = useCallback((error: Error, errorInfo: { componentStack?: string | null }) => {
    // Log error with context
    logAndReportError(error, errorInfo.componentStack || '', {
      source: 'ResilientErrorBoundary',
      isolationLevel,
      route: window.location.pathname,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    });

    // Show appropriate toast notification
    const errorType = fallbackType || classifyError(error);
    const toastMessage = {
      'network-error': 'Connection issue detected. Retrying...',
      'data-loading-error': 'Data loading failed. Please try again.',
      'component-crash': 'Component error occurred.',
      'permission-denied': 'Access denied.',
      'not-found': 'Content not found.',
      'maintenance': 'Feature temporarily unavailable.',
      'generic': 'An error occurred.'
    }[errorType];

    toast.error(toastMessage, {
      duration: 4000,
      position: 'top-center'
    });

    // Call custom error handler
    if (onError) {
      onError(error, errorInfo);
    }
  }, [fallbackType, isolationLevel, onError]);

  return (
    <ErrorBoundary
      FallbackComponent={(props) => (
        <ResilientErrorFallback
          {...props}
          maxRetries={maxRetries}
          retryDelay={retryDelay}
          enableAutoRetry={enableAutoRetry}
          fallbackType={fallbackType}
          isolationLevel={isolationLevel}
          gracefulDegradation={gracefulDegradation}
        />
      )}
      onError={handleError}
      onReset={() => {
        // Clear any cached data that might be causing issues
        if ('caches' in window) {
          caches.keys().then(names => {
            names.forEach(name => {
              if (name.includes('runtime') || name.includes('precache')) {
                caches.delete(name);
              }
            });
          });
        }
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

export default ResilientErrorBoundary;

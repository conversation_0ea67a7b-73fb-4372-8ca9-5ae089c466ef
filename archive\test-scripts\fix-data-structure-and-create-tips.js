/**
 * Fix Data Structure and Create Proper Tips System
 * 
 * This script removes incorrectly created informational activities and 
 * creates proper tips and informational content structure.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔧 Fixing Data Structure and Creating Proper Tips System');
console.log('========================================================');

// Proper tips and informational content data
const properInformationalContent = {
  tips: [
    {
      title: 'Festival Packing Essentials',
      content: 'Complete packing checklist for Sziget Festival: comfortable walking shoes and flip-flops, sunscreen and hat for daytime, warm clothes for evening (can get cool by the Danube), portable phone charger/power bank, reusable water bottle (free water stations available), cash in HUF for vendors (many don\'t accept cards), wet wipes and hand sanitizer, earplugs for camping areas.',
      category: 'PACKING'
    },
    {
      title: 'Money-Saving Tips for Sziget',
      content: 'Smart tips to save money during your Sziget experience: buy festival food vouchers in advance for discounts, bring your own alcohol to camping areas (allowed), use public transport day passes instead of taxis, eat one meal per day outside the festival for budget savings, share accommodation costs with Festival Family members.',
      category: 'BUDGET'
    },
    {
      title: 'Festival Navigation & Planning',
      content: 'Tips for navigating Sziget Festival: download the official Sziget app for stage times and maps, meet at the Festival Family base camp daily at 2 PM, use the Ferris wheel as a landmark for orientation, plan your day around main stage acts but explore smaller stages, check weather forecast and plan indoor activities for rain.',
      category: 'NAVIGATION'
    },
    {
      title: 'Health & Safety Guidelines',
      content: 'Important health and safety information for Sziget Festival: stay hydrated - free water stations throughout the festival, use sunscreen during day events (strong summer sun), pace yourself - it\'s a week-long festival, keep emergency contacts saved in your phone, know the location of medical tents and first aid stations, travel in groups, especially late at night.',
      category: 'SAFETY'
    },
    {
      title: 'Budapest Transportation Guide',
      content: 'Complete guide to getting around Budapest and to Sziget Festival: Airport Shuttle (100E bus) - 900 HUF, Metro M3 to city center. City to Sziget: HÉV suburban train to Filatorigát + shuttle bus, or Boat from Vigadó tér. Public transport: BKK day passes available - 1650 HUF/day, 4150 HUF/3 days. Taxi apps: Bolt, Uber, FőTaxi available in Budapest. Bike rental: MOL Bubi bike sharing system throughout the city.',
      category: 'TRANSPORTATION'
    },
    {
      title: 'Budapest Accommodation & Food',
      content: 'Recommendations for places to stay and eat in Budapest: Budget - Hostels in city center 15-30 EUR/night, Camping on Sziget Island. Mid-range - Hotels and Airbnb 40-80 EUR/night in Pest side. Luxury - Thermal hotels and luxury accommodations 100+ EUR/night. Hungarian specialties: Goulash, Lángos, Chimney cake (Kürtőskalács), Fisherman\'s soup. Currency: Hungarian Forint (HUF) - 1 EUR ≈ 380 HUF. Tipping: 10-15% in restaurants.',
      category: 'ACCOMMODATION'
    },
    {
      title: 'Budapest Attractions & Nightlife',
      content: 'Must-see attractions in Budapest: Parliament Building, Széchenyi Thermal Baths, Fisherman\'s Bastion, Chain Bridge. Thermal baths: Széchenyi, Gellért, Rudas - perfect for post-festival recovery. Nightlife: Ruin pubs in Jewish Quarter (Szimpla Kert, Instant), Danube boat parties. Day trips: Danube Bend (Szentendre, Visegrád), Lake Balaton (1.5h by train).',
      category: 'ATTRACTIONS'
    }
  ],
  
  contactInfo: {
    festival_family_organizers: [
      {
        name: "Festival Family Team",
        role: "Main Organizers",
        instagram: "@festivalfamily",
        facebook: "Festival Family Community",
        whatsapp_group: "Festival Family Sziget 2025",
        email: "<EMAIL>"
      }
    ],
    social_media: {
      instagram: "https://instagram.com/festivalfamily",
      facebook: "https://facebook.com/festivalfamilycommunity",
      whatsapp: "Festival Family WhatsApp Groups",
      website: "https://festivalfamily.com"
    },
    emergency_contacts: {
      festival_family_coordinator: "+31 6 12345678",
      sziget_info: "+36 1 372 0013",
      emergency_services: "112"
    }
  }
};

// Function to remove incorrectly created informational activities
async function removeIncorrectInformationalActivities() {
  console.log('🧹 Removing incorrectly created informational activities...');
  
  const informationalActivityTitles = [
    'Festival Family Contact Information',
    'Budapest Transportation Guide',
    'Budapest Accommodation & Food Guide',
    'Budapest Attractions & Nightlife',
    'Festival Packing Essentials',
    'Money-Saving Tips for Sziget',
    'Festival Navigation & Planning',
    'Health & Safety Guidelines'
  ];
  
  try {
    const { data, error } = await supabase
      .from('activities')
      .delete()
      .in('title', informationalActivityTitles)
      .select();
    
    if (error) {
      console.error('❌ Error removing informational activities:', error);
      return { success: false, error: error.message };
    }
    
    console.log(`✅ Removed ${data?.length || 0} incorrectly created informational activities`);
    return { success: true, removedCount: data?.length || 0 };
  } catch (err) {
    console.error('💥 Failed to remove informational activities:', err);
    return { success: false, error: err.message };
  }
}

// Function to create proper tips
async function createProperTips() {
  console.log('📚 Creating proper tips in tips table...');
  
  try {
    const { data, error } = await supabase
      .from('tips')
      .insert(properInformationalContent.tips)
      .select();
    
    if (error) {
      console.error('❌ Error creating tips:', error);
      return { success: false, error: error.message };
    }
    
    console.log(`✅ Created ${data?.length || 0} tips successfully`);
    return { success: true, createdCount: data?.length || 0, tips: data };
  } catch (err) {
    console.error('💥 Failed to create tips:', err);
    return { success: false, error: err.message };
  }
}

// Function to update festival with contact information
async function updateFestivalWithContactInfo(festivalId) {
  console.log('📞 Updating festival with contact information...');
  
  try {
    const { data, error } = await supabase
      .from('festivals')
      .update({
        metadata: {
          contact_info: properInformationalContent.contactInfo
        }
      })
      .eq('id', festivalId)
      .select();
    
    if (error) {
      console.error('❌ Error updating festival contact info:', error);
      return { success: false, error: error.message };
    }
    
    console.log('✅ Festival updated with contact information');
    return { success: true, festival: data[0] };
  } catch (err) {
    console.error('💥 Failed to update festival contact info:', err);
    return { success: false, error: err.message };
  }
}

// Main execution
async function main() {
  try {
    console.log(`🕐 Started at: ${new Date().toISOString()}\n`);
    
    // Get Sziget Festival
    const { data: festival } = await supabase
      .from('festivals')
      .select('*')
      .eq('name', 'Sziget Festival 2025')
      .single();
    
    if (!festival) {
      console.log('❌ Sziget Festival 2025 not found');
      return;
    }
    
    console.log('✅ Found Sziget Festival 2025');
    console.log('🎪 Festival ID:', festival.id);
    
    // Step 1: Remove incorrectly created informational activities
    const removalResult = await removeIncorrectInformationalActivities();
    
    // Step 2: Create proper tips
    const tipsResult = await createProperTips();
    
    // Step 3: Update festival with contact information
    const contactResult = await updateFestivalWithContactInfo(festival.id);
    
    // Compile results
    const results = {
      festival: festival,
      dataStructureFix: {
        incorrectActivitiesRemoved: removalResult.success ? removalResult.removedCount : 0,
        properTipsCreated: tipsResult.success ? tipsResult.createdCount : 0,
        contactInfoUpdated: contactResult.success
      },
      errors: [],
      timestamp: new Date().toISOString()
    };
    
    if (!removalResult.success) results.errors.push(`Removal error: ${removalResult.error}`);
    if (!tipsResult.success) results.errors.push(`Tips creation error: ${tipsResult.error}`);
    if (!contactResult.success) results.errors.push(`Contact info error: ${contactResult.error}`);
    
    // Save results
    const resultsDir = 'data-structure-fix-results';
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      `${resultsDir}/data-structure-fix-${Date.now()}.json`,
      JSON.stringify(results, null, 2)
    );
    
    console.log('\n🎉 DATA STRUCTURE FIX COMPLETE');
    console.log('==============================');
    console.log(`🧹 Incorrect Activities Removed: ${results.dataStructureFix.incorrectActivitiesRemoved}`);
    console.log(`📚 Proper Tips Created: ${results.dataStructureFix.properTipsCreated}`);
    console.log(`📞 Contact Info Updated: ${results.dataStructureFix.contactInfoUpdated ? 'YES' : 'NO'}`);
    
    if (results.errors.length > 0) {
      console.log('\n⚠️ ERRORS ENCOUNTERED:');
      results.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    } else {
      console.log('\n✅ All operations completed successfully!');
      console.log('📊 Data is now properly structured:');
      console.log('   - Informational content moved to tips table');
      console.log('   - Contact information stored in festival metadata');
      console.log('   - Activities table contains only actual festival activities');
    }
    
    console.log(`📁 Results saved to: ${resultsDir}/data-structure-fix-${Date.now()}.json`);
    
  } catch (error) {
    console.error('💥 Data structure fix failed:', error);
  }
  
  process.exit(0);
}

main();

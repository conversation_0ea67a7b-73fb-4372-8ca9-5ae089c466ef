/**
 * Unified Component System for Festival Family
 * Single source of truth for all UI components
 * Uses only design-tokens.css variables - NO hardcoded colors or conflicting styles
 */

import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

// ===== UNIFIED BUTTON COMPONENT =====
interface UnifiedButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' | 'filter-active' | 'filter-inactive' | 'filter-success' | 'filter-warning' | 'filter-info';
  size?: 'sm' | 'md' | 'lg';
  priority?: 'high' | 'medium' | 'low';
  children: React.ReactNode;
  onClick?: (e?: any) => void;
  disabled?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  colorVariant?: 'default' | 'success' | 'warning' | 'info' | 'accent';
}

export const UnifiedButton: React.FC<UnifiedButtonProps> = ({
  variant = 'primary',
  size = 'md',
  priority,
  children,
  onClick,
  disabled = false,
  className = '',
  type = 'button'
}) => {
  const baseClasses = 'rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 ease-out';
  
  const variantClasses = {
    primary: 'bg-gradient-to-r from-primary to-accent text-primary-foreground hover:opacity-90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    outline: 'border-2 border-border bg-transparent text-foreground hover:bg-muted',
    ghost: 'bg-transparent text-muted-foreground hover:bg-muted',
    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
    'filter-active': 'bg-gradient-to-r from-primary to-accent text-primary-foreground border-2 border-primary/30 shadow-md hover:shadow-lg hover:from-primary/90 hover:to-accent/90',
    'filter-inactive': 'border-2 border-border bg-card text-muted-foreground hover:bg-muted hover:text-foreground hover:border-primary/50',
    'filter-success': 'bg-gradient-to-r from-green-500 to-green-600 text-white border-2 border-green-400/30 shadow-md hover:shadow-lg hover:from-green-600 hover:to-green-700',
    'filter-warning': 'bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-2 border-yellow-400/30 shadow-md hover:shadow-lg hover:from-yellow-600 hover:to-orange-600',
    'filter-info': 'bg-gradient-to-r from-blue-500 to-blue-600 text-white border-2 border-blue-400/30 shadow-md hover:shadow-lg hover:from-blue-600 hover:to-blue-700'
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  const priorityClass = priority ? `priority-${priority}` : '';

  return (
    <button
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${priorityClass}
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:transform hover:scale-[1.02] active:scale-[0.98]'}
        ${className}
      `}
      onClick={onClick}
      disabled={disabled}
      type={type}
    >
      {children}
    </button>
  );
};

// ===== UNIFIED CARD COMPONENT =====
interface UnifiedCardProps {
  variant?: 'default' | 'elevated' | 'outlined' | 'glass';
  priority?: 'high' | 'medium' | 'low';
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export const UnifiedCard: React.FC<UnifiedCardProps> = ({
  variant = 'default',
  priority,
  children,
  className = '',
  onClick
}) => {
  const baseClasses = 'component-base';
  
  const variantClasses = {
    default: 'bg-card border border-border micro-bounce',
    elevated: 'bg-card shadow-lg border-0 smooth-scale',
    outlined: 'bg-transparent border-2 border-border hover-lift',
    glass: 'bg-card/80 backdrop-blur-md border border-border/20 glass-hover'
  };

  const priorityClass = priority ? `priority-${priority}` : '';
  const interactiveClass = onClick ? 'cursor-pointer hover:shadow-lg' : '';

  return (
    <motion.div
      whileHover={onClick ? { y: -2 } : {}}
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${priorityClass}
        ${interactiveClass}
        ${className}
      `}
      onClick={onClick}
    >
      {children}
    </motion.div>
  );
};

// ===== UNIFIED BADGE COMPONENT =====
interface UnifiedBadgeProps {
  variant?: 'default' | 'priority' | 'category' | 'secondary' | 'destructive' | 'success' | 'admin-customizable';
  priority?: 'high' | 'medium' | 'low';
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  customColor?: string; // For admin-customizable badges
  overlayMode?: boolean; // Enhanced visibility for overlay badges
}

export const UnifiedBadge: React.FC<UnifiedBadgeProps> = ({
  variant = 'default',
  priority,
  children,
  className = '',
  size = 'md',
  customColor,
  overlayMode = false
}) => {
  const baseClasses = overlayMode
    ? 'inline-flex items-center justify-center rounded-full font-medium shadow-lg backdrop-blur-md border border-white/30 text-primary-foreground whitespace-nowrap'
    : 'inline-flex items-center justify-center rounded-full font-medium shadow-sm backdrop-blur-sm border border-white/20 whitespace-nowrap';

  const variantClasses = {
    default: 'bg-muted/90 text-muted-foreground',
    priority: priority ? `priority-${priority} bg-white/90 text-gray-900` : 'bg-muted/90 text-muted-foreground',
    category: overlayMode ? 'bg-primary/95 text-white shadow-xl' : 'bg-primary/90 text-white shadow-lg',
    secondary: overlayMode ? 'bg-secondary/95 text-white shadow-xl' : 'bg-secondary/90 text-secondary-foreground',
    destructive: overlayMode ? 'bg-destructive/95 text-white shadow-xl' : 'bg-destructive/90 text-destructive-foreground',
    success: overlayMode ? 'bg-green-500/95 text-white shadow-xl' : 'bg-green-500/90 text-white shadow-lg',
    'admin-customizable': customColor ? `text-white shadow-xl` : 'bg-primary/95 text-white shadow-xl'
  };

  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs min-h-[20px] max-w-[120px] truncate',
    md: 'px-2.5 py-0.5 text-xs min-h-[22px] max-w-[140px] truncate',
    lg: 'px-3 py-1 text-sm min-h-[24px] max-w-[160px] truncate'
  };

  const customStyle = variant === 'admin-customizable' && customColor
    ? { backgroundColor: customColor + (overlayMode ? 'F2' : 'E6') } // Add opacity for overlay mode
    : {};

  return (
    <span
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
      style={customStyle}
    >
      {children}
    </span>
  );
};

// ===== UNIFIED ICON BUTTON =====
interface UnifiedIconButtonProps {
  icon: LucideIcon;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  'aria-label': string;
}

export const UnifiedIconButton: React.FC<UnifiedIconButtonProps> = ({
  icon: Icon,
  variant = 'ghost',
  size = 'md',
  onClick,
  disabled = false,
  className = '',
  'aria-label': ariaLabel
}) => {
  const baseClasses = 'component-base flex items-center justify-center focus:outline-none focus:ring-2';
  
  const variantClasses = {
    primary: 'bg-gradient-to-r from-primary to-accent text-primary-foreground hover:opacity-90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    ghost: 'bg-transparent text-muted-foreground hover:bg-muted'
  };

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  };

  const iconSizes = {
    sm: 16,
    md: 20,
    lg: 24
  };

  return (
    <motion.button
      whileHover={{ scale: disabled ? 1 : 1.05 }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        ${className}
      `}
      onClick={onClick}
      disabled={disabled}
      aria-label={ariaLabel}
    >
      <Icon size={iconSizes[size]} />
    </motion.button>
  );
};

// ===== UNIFIED CONTAINER =====
interface UnifiedContainerProps {
  variant?: 'page' | 'section' | 'content';
  children: React.ReactNode;
  className?: string;
}

export const UnifiedContainer: React.FC<UnifiedContainerProps> = ({
  variant = 'content',
  children,
  className = ''
}) => {
  const variantClasses = {
    page: 'min-h-screen bg-dark-gradient',
    section: 'py-8 px-4',
    content: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'
  };

  return (
    <div className={`${variantClasses[variant]} ${className}`}>
      {children}
    </div>
  );
};

// ===== UNIFIED FILTER BAR COMPONENT =====
interface FilterOption {
  id: string;
  label: string;
  count?: number;
  icon?: LucideIcon;
  colorVariant?: 'default' | 'success' | 'warning' | 'info';
}

interface UnifiedFilterBarProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  searchPlaceholder?: string;
  filterOptions?: FilterOption[];
  activeFilter?: string;
  onFilterChange?: (filterId: string) => void;
  showSearch?: boolean;
  showFilters?: boolean;
  className?: string;
  variant?: 'default' | 'compact';
}

export const UnifiedFilterBar: React.FC<UnifiedFilterBarProps> = ({
  searchValue,
  onSearchChange,
  searchPlaceholder = "Search...",
  filterOptions = [],
  activeFilter,
  onFilterChange,
  showSearch = true,
  showFilters = true,
  className = '',
  variant = 'default'
}) => {
  const isCompact = variant === 'compact';

  return (
    <UnifiedCard variant="elevated" className={`p-4 ${className}`}>
      <div className={`flex flex-col ${isCompact ? 'gap-3' : 'gap-4'} ${!isCompact ? 'sm:flex-row sm:items-center' : ''}`}>
        {/* Search Section */}
        {showSearch && (
          <div className={`relative ${isCompact ? 'w-full' : 'flex-1'}`}>
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e) => onSearchChange(e.target.value)}
              className="w-full pl-10 pr-4 py-2.5 bg-background border border-border rounded-lg text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all"
              style={{ fontSize: '16px' }} // Prevents zoom on iOS
            />
          </div>
        )}

        {/* Filter Buttons Section */}
        {showFilters && filterOptions.length > 0 && (
          <div className={`flex flex-wrap gap-2 ${isCompact ? 'w-full' : 'sm:flex-shrink-0'}`}>
            {filterOptions.map((option) => {
              const isActive = activeFilter === option.id;
              const Icon = option.icon;

              // Determine variant based on active state and color variant
              let buttonVariant: UnifiedButtonProps['variant'] = isActive ? 'filter-active' : 'filter-inactive';
              if (isActive && option.colorVariant) {
                switch (option.colorVariant) {
                  case 'success':
                    buttonVariant = 'filter-success';
                    break;
                  case 'warning':
                    buttonVariant = 'filter-warning';
                    break;
                  case 'info':
                    buttonVariant = 'filter-info';
                    break;
                  default:
                    buttonVariant = 'filter-active';
                }
              }

              return (
                <UnifiedButton
                  key={option.id}
                  variant={buttonVariant}
                  size="sm"
                  onClick={() => onFilterChange?.(option.id)}
                  className="flex items-center gap-1.5 min-w-fit"
                >
                  {Icon && <Icon size={14} />}
                  <span>{option.label}</span>
                  {option.count !== undefined && (
                    <UnifiedBadge variant="secondary" size="sm" className="ml-1">
                      {option.count}
                    </UnifiedBadge>
                  )}
                </UnifiedButton>
              );
            })}
          </div>
        )}
      </div>
    </UnifiedCard>
  );
};

// ===== UNIFIED ANNOUNCEMENT COMPONENT =====
interface UnifiedAnnouncementProps {
  priority: 'high' | 'medium' | 'low';
  title: string;
  message: string;
  onClose?: () => void;
  className?: string;
}

export const UnifiedAnnouncement: React.FC<UnifiedAnnouncementProps> = ({
  priority,
  title,
  message,
  onClose,
  className = ''
}) => {
  return (
    <UnifiedCard variant="outlined" priority={priority} className={`p-4 ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="font-semibold text-sm mb-1">{title}</h3>
          <p className="text-sm opacity-90">{message}</p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-muted-foreground hover:text-foreground transition-colors"
            aria-label="Close announcement"
          >
            <span className="text-lg">×</span>
          </button>
        )}
      </div>
    </UnifiedCard>
  );
};

import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase';

export interface ActivityFeedItem {
  id: string;
  type: 'join' | 'favorite' | 'view' | 'new_activity' | 'new_user';
  user_name?: string;
  user_avatar?: string | null;
  activity_title?: string;
  activity_id?: string;
  content_title?: string;
  content_type?: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

interface UseActivityFeedOptions {
  limit?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseActivityFeedReturn {
  feedItems: ActivityFeedItem[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useActivityFeed = (options: UseActivityFeedOptions = {}): UseActivityFeedReturn => {
  const {
    limit = 20,
    autoRefresh = true,
    refreshInterval = 30000 // 30 seconds
  } = options;

  const [feedItems, setFeedItems] = useState<ActivityFeedItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch recent activity feed data
  const fetchFeedData = useCallback(async () => {
    try {
      setError(null);

      // Get recent activity participants (joins)
      const { data: recentJoins } = await supabase
        .from('activity_participants')
        .select(`
          *,
          activities!inner(id, title),
          profiles!inner(username, avatar_url)
        `)
        .eq('status', 'registered')
        .order('created_at', { ascending: false })
        .limit(Math.floor(limit * 0.4));

      // Get recent favorites
      const { data: recentFavorites } = await supabase
        .from('user_favorites')
        .select(`
          *,
          activities!inner(id, title),
          profiles!inner(username, avatar_url)
        `)
        .order('created_at', { ascending: false })
        .limit(Math.floor(limit * 0.3));

      // Get recent new activities
      const { data: newActivities } = await supabase
        .from('activities')
        .select(`
          id,
          title,
          created_at,
          profiles!inner(username, avatar_url)
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(Math.floor(limit * 0.3));

      // Transform data into feed items
      const feedItems: ActivityFeedItem[] = [];

      // Add join activities
      recentJoins?.forEach(join => {
        if (join.created_at) {
          feedItems.push({
            id: `join-${join.id}`,
            type: 'join',
            user_name: join.profiles?.username || 'Someone',
            user_avatar: join.profiles?.avatar_url,
            activity_title: join.activities?.title,
            activity_id: join.activities?.id,
            timestamp: join.created_at,
            metadata: { notes: join.notes }
          });
        }
      });

      // Add favorite activities
      recentFavorites?.forEach(favorite => {
        if (favorite.created_at) {
          feedItems.push({
            id: `favorite-${favorite.id}`,
            type: 'favorite',
            user_name: favorite.profiles?.username || 'Someone',
            user_avatar: favorite.profiles?.avatar_url,
            activity_title: favorite.activities?.title,
            activity_id: favorite.activities?.id,
            timestamp: favorite.created_at
          });
        }
      });

      // Add new activities
      newActivities?.forEach(activity => {
        if (activity.created_at) {
          feedItems.push({
            id: `new-activity-${activity.id}`,
            type: 'new_activity',
            user_name: activity.profiles?.username || 'Festival Family',
            user_avatar: activity.profiles?.avatar_url,
            activity_title: activity.title,
            activity_id: activity.id,
            timestamp: activity.created_at
          });
        }
      });

      // Sort by timestamp and limit
      const sortedItems = feedItems
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, limit);

      setFeedItems(sortedItems);
    } catch (error) {
      console.error('Error fetching activity feed:', error);
      setError('Failed to load activity feed');
    } finally {
      setIsLoading(false);
    }
  }, [limit]);

  // Set up real-time subscription and periodic refresh
  useEffect(() => {
    fetchFeedData();

    let intervalId: NodeJS.Timeout | null = null;
    let channel: any = null;

    if (autoRefresh) {
      // Set up real-time subscription
      channel = supabase
        .channel('activity_feed_updates')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'activity_participants' },
          () => {
            console.log('🔄 Activity feed: Participant update detected');
            fetchFeedData();
          }
        )
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'user_favorites' },
          () => {
            console.log('🔄 Activity feed: Favorites update detected');
            fetchFeedData();
          }
        )
        .on(
          'postgres_changes',
          { event: 'INSERT', schema: 'public', table: 'activities' },
          () => {
            console.log('🔄 Activity feed: New activity detected');
            fetchFeedData();
          }
        )
        .subscribe();

      // Set up periodic refresh as fallback
      intervalId = setInterval(fetchFeedData, refreshInterval);
    }

    return () => {
      if (channel) {
        supabase.removeChannel(channel);
      }
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [fetchFeedData, autoRefresh, refreshInterval]);

  return {
    feedItems,
    isLoading,
    error,
    refetch: fetchFeedData
  };
};

export default useActivityFeed;

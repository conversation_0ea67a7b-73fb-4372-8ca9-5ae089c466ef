# Mobile UX Technical Implementation Guide

## Component Specifications

### 1. Responsive Data Table Component

#### Desktop Table → Mobile Card Transformation

```typescript
interface ResponsiveTableProps<T> {
  data: T[];
  columns: ColumnDefinition<T>[];
  mobileCardRenderer?: (item: T) => React.ReactNode;
  breakpoint?: 'sm' | 'md' | 'lg';
  actions?: ActionDefinition<T>[];
}

interface ColumnDefinition<T> {
  key: keyof T;
  header: string;
  priority: 'high' | 'medium' | 'low'; // For mobile column prioritization
  render?: (value: T[keyof T], item: T) => React.ReactNode;
  sortable?: boolean;
  width?: string;
}
```

#### Implementation Example

```tsx
// Festival Table Component
const FestivalTable = ({ festivals }: { festivals: Festival[] }) => {
  const columns: ColumnDefinition<Festival>[] = [
    { key: 'name', header: 'Festival Name', priority: 'high' },
    { key: 'date', header: 'Date', priority: 'high' },
    { key: 'location', header: 'Location', priority: 'medium' },
    { key: 'attendees', header: 'Attendees', priority: 'low' },
    { key: 'status', header: 'Status', priority: 'high' }
  ];

  const mobileCardRenderer = (festival: Festival) => (
    <div className="bg-white rounded-lg shadow-sm border p-4 space-y-3">
      <div className="flex justify-between items-start">
        <h3 className="font-semibold text-lg">{festival.name}</h3>
        <StatusBadge status={festival.status} />
      </div>
      <div className="space-y-2 text-sm text-gray-600">
        <div className="flex items-center">
          <Calendar className="w-4 h-4 mr-2" />
          {formatDate(festival.date)}
        </div>
        <div className="flex items-center">
          <MapPin className="w-4 h-4 mr-2" />
          {festival.location}
        </div>
        <div className="flex items-center">
          <Users className="w-4 h-4 mr-2" />
          {festival.attendees.toLocaleString()} attendees
        </div>
      </div>
      <div className="flex space-x-2 pt-2">
        <Button size="sm" variant="outline">Edit</Button>
        <Button size="sm" variant="outline">View</Button>
        <Button size="sm" variant="outline" className="text-red-600">Delete</Button>
      </div>
    </div>
  );

  return (
    <ResponsiveTable
      data={festivals}
      columns={columns}
      mobileCardRenderer={mobileCardRenderer}
      breakpoint="md"
    />
  );
};
```

### 2. Mobile-First Form Components

#### Responsive Form Layout

```tsx
interface ResponsiveFormProps {
  children: React.ReactNode;
  columns?: { sm: number; md: number; lg: number };
  spacing?: 'tight' | 'normal' | 'loose';
}

const ResponsiveForm = ({ children, columns = { sm: 1, md: 2, lg: 3 }, spacing = 'normal' }: ResponsiveFormProps) => {
  const spacingClasses = {
    tight: 'space-y-3',
    normal: 'space-y-4',
    loose: 'space-y-6'
  };

  return (
    <div className={`grid gap-4 ${spacingClasses[spacing]}`}>
      <div className={`grid grid-cols-${columns.sm} md:grid-cols-${columns.md} lg:grid-cols-${columns.lg} gap-4`}>
        {children}
      </div>
    </div>
  );
};
```

#### Enhanced Input Components

```tsx
interface MobileInputProps extends InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  hint?: string;
  icon?: React.ReactNode;
  touchOptimized?: boolean;
}

const MobileInput = ({ 
  label, 
  error, 
  hint, 
  icon, 
  touchOptimized = true, 
  className,
  ...props 
}: MobileInputProps) => {
  const inputClasses = cn(
    'w-full rounded-lg border border-gray-300 px-3 py-3', // Increased padding for touch
    'focus:ring-2 focus:ring-purple-500 focus:border-transparent',
    'text-base', // Prevent zoom on iOS
    touchOptimized && 'min-h-[44px]', // iOS touch target minimum
    error && 'border-red-500',
    className
  );

  return (
    <div className="space-y-1">
      <label className="block text-sm font-medium text-gray-700">
        {label}
      </label>
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            {icon}
          </div>
        )}
        <input
          className={cn(inputClasses, icon && 'pl-10')}
          {...props}
        />
      </div>
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
      {hint && !error && (
        <p className="text-sm text-gray-500">{hint}</p>
      )}
    </div>
  );
};
```

### 3. Mobile Navigation Enhancements

#### Bottom Navigation Component

```tsx
interface BottomNavItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href: string;
  badge?: number;
}

const BottomNavigation = ({ items, currentPath }: { items: BottomNavItem[]; currentPath: string }) => {
  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 lg:hidden z-50">
      <div className="grid grid-cols-5 h-16">
        {items.map((item) => {
          const isActive = currentPath.startsWith(item.href);
          return (
            <Link
              key={item.id}
              to={item.href}
              className={cn(
                'flex flex-col items-center justify-center space-y-1 text-xs',
                isActive ? 'text-purple-600' : 'text-gray-600'
              )}
            >
              <div className="relative">
                {item.icon}
                {item.badge && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                    {item.badge}
                  </span>
                )}
              </div>
              <span className="truncate">{item.label}</span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
};
```

### 4. Touch-Optimized Interactive Elements

#### Swipe Actions Component

```tsx
interface SwipeActionsProps {
  children: React.ReactNode;
  leftActions?: SwipeAction[];
  rightActions?: SwipeAction[];
  onSwipe?: (direction: 'left' | 'right', action: SwipeAction) => void;
}

interface SwipeAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  color: 'red' | 'blue' | 'green' | 'gray';
  action: () => void;
}

const SwipeActions = ({ children, leftActions = [], rightActions = [], onSwipe }: SwipeActionsProps) => {
  const [swipeOffset, setSwipeOffset] = useState(0);
  const [isSwipeActive, setIsSwipeActive] = useState(false);

  // Touch event handlers for swipe detection
  const handleTouchStart = (e: TouchEvent) => {
    // Implementation for touch start
  };

  const handleTouchMove = (e: TouchEvent) => {
    // Implementation for touch move with swipe detection
  };

  const handleTouchEnd = () => {
    // Implementation for touch end with action triggering
  };

  return (
    <div className="relative overflow-hidden">
      {/* Left actions */}
      {leftActions.length > 0 && (
        <div className="absolute left-0 top-0 bottom-0 flex">
          {leftActions.map((action) => (
            <button
              key={action.id}
              className={`px-4 flex items-center justify-center text-white bg-${action.color}-500`}
              onClick={action.action}
            >
              {action.icon}
            </button>
          ))}
        </div>
      )}

      {/* Main content */}
      <div
        className="relative bg-white transition-transform duration-200"
        style={{ transform: `translateX(${swipeOffset}px)` }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {children}
      </div>

      {/* Right actions */}
      {rightActions.length > 0 && (
        <div className="absolute right-0 top-0 bottom-0 flex">
          {rightActions.map((action) => (
            <button
              key={action.id}
              className={`px-4 flex items-center justify-center text-white bg-${action.color}-500`}
              onClick={action.action}
            >
              {action.icon}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
```

### 5. Mobile-Optimized Loading States

#### Skeleton Components

```tsx
const TableSkeleton = () => (
  <div className="space-y-3">
    {[...Array(5)].map((_, i) => (
      <div key={i} className="animate-pulse">
        <div className="hidden md:flex space-x-4 p-4 border-b">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/6"></div>
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/6"></div>
          <div className="h-4 bg-gray-200 rounded w-1/6"></div>
        </div>
        <div className="md:hidden p-4 border-b space-y-3">
          <div className="h-5 bg-gray-200 rounded w-3/4"></div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            <div className="h-3 bg-gray-200 rounded w-2/3"></div>
          </div>
          <div className="flex space-x-2">
            <div className="h-8 bg-gray-200 rounded w-16"></div>
            <div className="h-8 bg-gray-200 rounded w-16"></div>
          </div>
        </div>
      </div>
    ))}
  </div>
);

const CardSkeleton = () => (
  <div className="animate-pulse bg-white rounded-lg shadow-sm border p-4 space-y-3">
    <div className="flex justify-between items-start">
      <div className="h-6 bg-gray-200 rounded w-3/4"></div>
      <div className="h-5 bg-gray-200 rounded w-16"></div>
    </div>
    <div className="space-y-2">
      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      <div className="h-4 bg-gray-200 rounded w-2/3"></div>
      <div className="h-4 bg-gray-200 rounded w-1/3"></div>
    </div>
    <div className="flex space-x-2 pt-2">
      <div className="h-8 bg-gray-200 rounded w-16"></div>
      <div className="h-8 bg-gray-200 rounded w-16"></div>
      <div className="h-8 bg-gray-200 rounded w-16"></div>
    </div>
  </div>
);
```

## CSS Utilities and Responsive Design

### Mobile-First Breakpoint System

```css
/* Custom Tailwind CSS extensions */
@layer utilities {
  /* Touch-friendly sizing */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }
  
  /* Mobile-optimized text sizing */
  .text-mobile-base {
    font-size: 16px; /* Prevents zoom on iOS */
  }
  
  /* Safe area handling for mobile devices */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  /* Mobile-specific spacing */
  .mobile-padding {
    @apply px-4 py-3;
  }
  
  /* Responsive grid utilities */
  .mobile-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
  }
}
```

### Performance Optimization

#### Lazy Loading Implementation

```tsx
const LazyAdminSection = ({ children }: { children: React.ReactNode }) => {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <div ref={ref}>
      {isVisible ? children : <div className="h-64 flex items-center justify-center">Loading...</div>}
    </div>
  );
};
```

## Testing Strategy

### Mobile Testing Checklist

```typescript
// Mobile-specific test utilities
export const mobileTestUtils = {
  // Simulate mobile viewport
  setMobileViewport: (page: Page) => 
    page.setViewportSize({ width: 375, height: 667 }),
  
  // Test touch interactions
  testTouchTarget: async (page: Page, selector: string) => {
    const element = await page.locator(selector);
    const box = await element.boundingBox();
    expect(box?.width).toBeGreaterThanOrEqual(44);
    expect(box?.height).toBeGreaterThanOrEqual(44);
  },
  
  // Test responsive behavior
  testResponsiveLayout: async (page: Page, breakpoints: number[]) => {
    for (const width of breakpoints) {
      await page.setViewportSize({ width, height: 800 });
      await page.waitForTimeout(100);
      // Add specific layout assertions
    }
  }
};
```

This technical guide provides the foundation for implementing the mobile UX improvements outlined in the main plan. Each component is designed with mobile-first principles and can be incrementally integrated into the existing admin interface.

import React, { useState, useEffect } from 'react';
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';

/**
 * Enhanced Unified Badge Component
 * Single source of truth for all badge and tag visual elements
 * Uses enhancedColorMappingService for database-driven color management
 */

interface EnhancedUnifiedBadgeProps {
  children: React.ReactNode;
  
  // Content-based props (database-driven)
  contentType?: 'activities' | 'tips' | 'community' | 'festivals' | 'resources';
  category?: string; // e.g., 'meetup', 'workshop', 'social', etc.
  
  // Legacy support props
  variant?: 'default' | 'priority' | 'category' | 'secondary' | 'destructive' | 'success' | 'admin-customizable';
  priority?: 'high' | 'medium' | 'low';
  customColor?: string;
  
  // Visual props
  size?: 'sm' | 'md' | 'lg';
  overlayMode?: boolean;
  className?: string;
  
  // Accessibility
  'aria-label'?: string;
}

interface ColorMapping {
  color_primary: string;
  color_secondary: string;
  color_accent: string;
  emoji_icon: string | null;
  show_icon: boolean | null;
}

export const EnhancedUnifiedBadge: React.FC<EnhancedUnifiedBadgeProps> = ({
  children,
  contentType,
  category,
  variant = 'default',
  priority,
  customColor,
  size = 'md',
  overlayMode = false,
  className = '',
  'aria-label': ariaLabel,
}) => {
  const [colorMapping, setColorMapping] = useState<ColorMapping | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch database-driven color mapping
  useEffect(() => {
    if (contentType && category) {
      setIsLoading(true);
      enhancedColorMappingService
        .getColorMapping(contentType, category)
        .then((mapping) => {
          setColorMapping(mapping);
          setIsLoading(false);
        })
        .catch((error) => {
          console.warn(`Failed to load color mapping for ${contentType}.${category}:`, error);
          setIsLoading(false);
        });
    }
  }, [contentType, category]);

  // Base classes for all badge variants
  const baseClasses = overlayMode
    ? 'inline-flex items-center justify-center rounded-full font-medium shadow-lg backdrop-blur-md border border-white/30 text-white whitespace-nowrap transition-all duration-200'
    : 'inline-flex items-center justify-center rounded-full font-medium shadow-sm backdrop-blur-sm border whitespace-nowrap transition-all duration-200';

  // Size classes
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs min-h-[20px] max-w-[120px] truncate',
    md: 'px-2.5 py-0.5 text-xs min-h-[22px] max-w-[140px] truncate',
    lg: 'px-3 py-1 text-sm min-h-[24px] max-w-[160px] truncate'
  };

  // Get colors from database mapping or fallback to variant-based colors
  const getColors = () => {
    // Priority 1: Database-driven color mapping
    if (colorMapping) {
      const primaryColor = colorMapping.color_primary;
      const opacity = overlayMode ? 'F2' : 'E6'; // Higher opacity for overlay mode
      
      return {
        backgroundColor: primaryColor + opacity,
        borderColor: primaryColor + '80',
        color: overlayMode ? '#FFFFFF' : primaryColor,
        style: { backgroundColor: primaryColor + opacity, borderColor: primaryColor + '80' }
      };
    }

    // Priority 2: Admin customizable color
    if (variant === 'admin-customizable' && customColor) {
      const opacity = overlayMode ? 'F2' : 'E6';
      return {
        backgroundColor: customColor + opacity,
        borderColor: customColor + '80',
        color: overlayMode ? '#FFFFFF' : customColor,
        style: { backgroundColor: customColor + opacity, borderColor: customColor + '80' }
      };
    }

    // Priority 3: Fallback to CSS classes (theme-aware)
    return {
      backgroundColor: '',
      borderColor: '',
      color: '',
      style: {},
      cssClasses: getVariantClasses(variant, priority, overlayMode)
    };
  };

  // Legacy variant classes for fallback
  const getVariantClasses = (variant: string, priority?: string, overlayMode?: boolean) => {
    const baseColor = overlayMode ? 'text-white' : '';
    
    switch (variant) {
      case 'priority':
        switch (priority) {
          case 'high':
            return `bg-destructive/20 text-destructive border-destructive/30 ${baseColor}`;
          case 'medium':
            return `bg-warning/20 text-warning border-warning/30 ${baseColor}`;
          case 'low':
            return `bg-success/20 text-success border-success/30 ${baseColor}`;
          default:
            return `bg-primary/20 text-primary border-primary/30 ${baseColor}`;
        }
      case 'category':
        return `bg-primary/20 text-primary border-primary/30 ${baseColor}`;
      case 'secondary':
        return `bg-secondary/20 text-secondary-foreground border-secondary/30 ${baseColor}`;
      case 'destructive':
        return `bg-destructive/20 text-destructive border-destructive/30 ${baseColor}`;
      case 'success':
        return `bg-success/20 text-success border-success/30 ${baseColor}`;
      default:
        return `bg-muted/20 text-muted-foreground border-border/30 ${baseColor}`;
    }
  };

  const colors = getColors();
  const finalClasses = `${baseClasses} ${sizeClasses[size]} ${colors.cssClasses || ''} ${className}`;

  // Show loading state for database-driven badges
  if (isLoading && contentType && category) {
    return (
      <span className={`${baseClasses} ${sizeClasses[size]} bg-muted/20 text-muted-foreground border-border/30 animate-pulse ${className}`}>
        <span className="opacity-50">...</span>
      </span>
    );
  }

  return (
    <span
      className={finalClasses}
      style={colors.style}
      aria-label={ariaLabel || (typeof children === 'string' ? children : undefined)}
      role="status"
    >
      {/* Show emoji icon only if enabled in database settings */}
      {colorMapping?.emoji_icon && colorMapping?.show_icon && (
        <span className="mr-1" aria-hidden="true">
          {colorMapping.emoji_icon}
        </span>
      )}
      {children}
    </span>
  );
};

/**
 * Convenience hook for getting activity type badge props
 */
export const useActivityTypeBadge = (activityType: string | null) => {
  const [badgeProps, setBadgeProps] = useState<{
    contentType: 'activities';
    category: string;
  } | null>(null);

  useEffect(() => {
    if (activityType) {
      // Map activity types to categories
      const categoryMapping: { [key: string]: string } = {
        'meetup': 'meetup',
        'social': 'social', 
        'workshop': 'workshop',
        'performance': 'performance',
        'game': 'game',
        'food': 'food',
        'other': 'other'
      };

      const category = categoryMapping[activityType.toLowerCase()] || 'other';
      setBadgeProps({
        contentType: 'activities',
        category
      });
    } else {
      setBadgeProps(null);
    }
  }, [activityType]);

  return badgeProps;
};

/**
 * Convenience component for activity type badges
 */
interface ActivityTypeBadgeProps {
  type: string | null;
  size?: 'sm' | 'md' | 'lg';
  overlayMode?: boolean;
  className?: string;
}

export const ActivityTypeBadge: React.FC<ActivityTypeBadgeProps> = ({
  type,
  size = 'sm',
  overlayMode = false,
  className = ''
}) => {
  const badgeProps = useActivityTypeBadge(type);

  if (!type || !badgeProps) {
    return (
      <EnhancedUnifiedBadge 
        variant="secondary" 
        size={size} 
        overlayMode={overlayMode}
        className={className}
      >
        N/A
      </EnhancedUnifiedBadge>
    );
  }

  return (
    <EnhancedUnifiedBadge
      contentType={badgeProps.contentType}
      category={badgeProps.category}
      size={size}
      overlayMode={overlayMode}
      className={className}
    >
      {type.charAt(0).toUpperCase() + type.slice(1).toLowerCase()}
    </EnhancedUnifiedBadge>
  );
};

// Export for backward compatibility
export { EnhancedUnifiedBadge as UnifiedBadge };

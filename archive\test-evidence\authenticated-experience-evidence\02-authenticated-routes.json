{"testName": "Authenticated Routes Access", "timestamp": "2025-05-29T23:20:02.099Z", "totalRoutes": 5, "accessibleRoutes": 5, "successRate": "100.0", "routeResults": [{"route": "/profile", "name": "Profile Page", "statusCode": 200, "accessible": true, "hasContent": true, "contentLength": 1962, "features": {"hasNavigation": false, "hasUserInterface": false, "hasInteractiveElements": false, "hasModernStyling": false, "hasReactComponents": true}}, {"route": "/discover", "name": "Discover Page", "statusCode": 200, "accessible": true, "hasContent": true, "contentLength": 1962, "features": {"hasNavigation": false, "hasUserInterface": false, "hasInteractiveElements": false, "hasModernStyling": false, "hasReactComponents": true}}, {"route": "/famhub", "name": "FamHub Community", "statusCode": 200, "accessible": true, "hasContent": true, "contentLength": 1962, "features": {"hasNavigation": false, "hasUserInterface": false, "hasInteractiveElements": false, "hasModernStyling": false, "hasReactComponents": true}}, {"route": "/activities", "name": "Activities Page", "statusCode": 200, "accessible": true, "hasContent": true, "contentLength": 1962, "features": {"hasNavigation": false, "hasUserInterface": false, "hasInteractiveElements": false, "hasModernStyling": false, "hasReactComponents": true}}, {"route": "/admin", "name": "Admin Dashboard", "statusCode": 200, "accessible": true, "hasContent": true, "contentLength": 1962, "features": {"hasNavigation": false, "hasUserInterface": false, "hasInteractiveElements": false, "hasModernStyling": false, "hasReactComponents": true}}]}
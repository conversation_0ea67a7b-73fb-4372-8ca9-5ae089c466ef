-- Create user interaction tables for Festival Family
-- Run this script in your Supabase SQL editor

-- Create activity_participants table for join functionality
CREATE TABLE IF NOT EXISTS activity_participants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  activity_id INTEGER REFERENCES activities(id) ON DELETE CASCADE,
  status TEXT DEFAULT 'registered' CHECK (status IN ('registered', 'attended', 'cancelled')),
  registration_date TIMESTAMP DEFAULT NOW(),
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, activity_id)
);

-- Create user_favorites table for favorites functionality
CREATE TABLE IF NOT EXISTS user_favorites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  activity_id INTEGER REFERENCES activities(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, activity_id)
);

-- Create activity_views table for analytics
CREATE TABLE IF NOT EXISTS activity_views (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  activity_id INTEGER REFERENCES activities(id) ON DELETE CASCADE,
  viewed_at TIMESTAMP DEFAULT NOW(),
  session_id TEXT,
  ip_address INET
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_activity_participants_user_id ON activity_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_participants_activity_id ON activity_participants(activity_id);
CREATE INDEX IF NOT EXISTS idx_activity_participants_status ON activity_participants(status);

CREATE INDEX IF NOT EXISTS idx_user_favorites_user_id ON user_favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_user_favorites_activity_id ON user_favorites(activity_id);

CREATE INDEX IF NOT EXISTS idx_activity_views_activity_id ON activity_views(activity_id);
CREATE INDEX IF NOT EXISTS idx_activity_views_viewed_at ON activity_views(viewed_at);

-- Add RLS policies
ALTER TABLE activity_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_views ENABLE ROW LEVEL SECURITY;

-- Policies for activity_participants
CREATE POLICY "Users can view their own participations" ON activity_participants
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can join activities" ON activity_participants
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own participations" ON activity_participants
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can leave activities" ON activity_participants
  FOR DELETE USING (user_id = auth.uid());

-- Policies for user_favorites
CREATE POLICY "Users can view their own favorites" ON user_favorites
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can add favorites" ON user_favorites
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can remove favorites" ON user_favorites
  FOR DELETE USING (user_id = auth.uid());

-- Policies for activity_views (more permissive for analytics)
CREATE POLICY "Users can view activity views" ON activity_views
  FOR SELECT USING (true);

CREATE POLICY "Anyone can record activity views" ON activity_views
  FOR INSERT WITH CHECK (true);

-- Insert some sample data for testing (optional)
-- Uncomment the following lines if you want sample data

/*
-- Sample activity participants (replace with real user IDs and activity IDs)
INSERT INTO activity_participants (user_id, activity_id, status, notes) VALUES
  ('00000000-0000-0000-0000-000000000001', 1, 'registered', 'Looking forward to this!'),
  ('00000000-0000-0000-0000-000000000002', 1, 'registered', 'First time joining'),
  ('00000000-0000-0000-0000-000000000001', 2, 'registered', 'Love music events')
ON CONFLICT (user_id, activity_id) DO NOTHING;

-- Sample user favorites
INSERT INTO user_favorites (user_id, activity_id) VALUES
  ('00000000-0000-0000-0000-000000000001', 1),
  ('00000000-0000-0000-0000-000000000001', 3),
  ('00000000-0000-0000-0000-000000000002', 2)
ON CONFLICT (user_id, activity_id) DO NOTHING;
*/

/**
 * Simple Users Test
 * 
 * Basic test to check if users page loads
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

test('Simple Users Page Test', async ({ page }) => {
  console.log('🧪 Simple users page test...');
  
  const loginSuccess = await loginAsAdmin(page);
  console.log(`Login success: ${loginSuccess}`);
  
  if (loginSuccess) {
    // Navigate to users page
    await page.goto('/admin/users');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000); // Give more time for loading
    
    console.log('📝 Checking users page...');
    
    // Check page title
    const pageTitle = await page.locator('h1').first().textContent();
    console.log(`Page title: "${pageTitle}"`);
    
    // Check for loading state
    const hasLoadingText = await page.locator('text="Loading users"').count() > 0;
    console.log(`Has loading text: ${hasLoadingText}`);
    
    // Check for error messages
    const hasErrorText = await page.locator('text="Failed", text="Error"').count() > 0;
    console.log(`Has error text: ${hasErrorText}`);
    
    // Check for "No users found" message
    const hasNoUsersText = await page.locator('text="No users found"').count() > 0;
    console.log(`Has no users text: ${hasNoUsersText}`);
    
    // Check for any user-related content
    const hasUserContent = await page.locator('text="@", text="USER", text="ADMIN", text="Joined"').count() > 0;
    console.log(`Has user content: ${hasUserContent}`);
    
    // Check for cards or user items
    const cardCount = await page.locator('.card, [class*="card"]').count();
    console.log(`Card count: ${cardCount}`);
    
    // Check for any buttons
    const buttonCount = await page.locator('button').count();
    console.log(`Button count: ${buttonCount}`);
    
    await page.screenshot({ path: 'test-results/users-simple.png', fullPage: true });
    
    console.log('✅ Simple users test completed');
  }
});

const API_BASE_URL = 'https://ealstndyhwjwipzlrxmg.supabase.co';

async function testGetFestivals() {
    try {
        const response = await fetch(`${API_BASE_URL}/festivals`);
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        const data = await response.json();
        console.log('Fetched Festivals:', data);
    } catch (error) {
        console.error('Error fetching festivals:', error);
    }
}

testGetFestivals();

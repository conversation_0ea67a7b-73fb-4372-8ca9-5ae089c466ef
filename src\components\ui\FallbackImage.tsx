import React, { useState, useCallback } from 'react';

export type ImageType = 'event' | 'activity' | 'community' | 'festival' | 'default';

interface FallbackImageProps {
  src?: string | null;
  alt: string;
  type?: ImageType;
  className?: string;
  width?: number | string;
  height?: number | string;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
}

const FALLBACK_IMAGES: Record<ImageType, string> = {
  event: '/images/defaults/event-placeholder.svg',
  activity: '/images/defaults/activity-placeholder.svg',
  community: '/images/defaults/community-placeholder.svg',
  festival: '/images/defaults/festival-placeholder.svg',
  default: '/images/defaults/event-placeholder.svg',
};

const FallbackImage: React.FC<FallbackImageProps> = ({
  src,
  alt,
  type = 'default',
  className = '',
  width,
  height,
  loading = 'lazy',
  onLoad,
  onError,
}) => {
  const [currentSrc, setCurrentSrc] = useState<string>(src || FALLBACK_IMAGES[type]);
  const [hasErrored, setHasErrored] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const handleError = useCallback(() => {
    if (!hasErrored && currentSrc !== FALLBACK_IMAGES[type]) {
      // First error: try fallback image
      setCurrentSrc(FALLBACK_IMAGES[type]);
      setHasErrored(true);
      setIsLoading(false);
    } else {
      // Fallback also failed: show error state
      setIsLoading(false);
    }
    onError?.();
  }, [currentSrc, hasErrored, type, onError]);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
    onLoad?.();
  }, [onLoad]);

  // If no src provided, use fallback immediately
  const imageSrc = src ? currentSrc : FALLBACK_IMAGES[type];

  return (
    <div className={`relative overflow-hidden ${className}`} style={{ width, height }}>
      {isLoading && (
        <div className="absolute inset-0 bg-muted animate-pulse flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-muted-foreground border-t-primary rounded-full animate-spin"></div>
        </div>
      )}
      <img
        src={imageSrc}
        alt={alt}
        className={`w-full h-full object-cover transition-opacity duration-300 ${
          isLoading ? 'opacity-0' : 'opacity-100'
        }`}
        loading={loading}
        onLoad={handleLoad}
        onError={handleError}
        style={{ width, height }}
      />
      {hasErrored && currentSrc === FALLBACK_IMAGES[type] && (
        <div className="absolute bottom-2 right-2 bg-background/80 text-foreground text-xs px-2 py-1 rounded">
          Placeholder
        </div>
      )}
    </div>
  );
};

export default FallbackImage;

# Test info

- Name: Festival Family Standardization Validation >> Performance Validation >> should not have infinite loops or performance issues
- Location: C:\Users\<USER>\CascadeProjects\festival-family\tests\standardization-validation.spec.js:267:5

# Error details

```
Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
╔═════════════════════════════════════════════════════════════════════════╗
║ Looks like Playwright Test or Playwright was just installed or updated. ║
║ Please run the following command to download new browsers:              ║
║                                                                         ║
║     npx playwright install                                              ║
║                                                                         ║
║ <3 Playwright Team                                                      ║
╚═════════════════════════════════════════════════════════════════════════╝
```

# Test source

```ts
  167 |       }
  168 |     });
  169 |
  170 |     test('should use BentoCard components consistently', async ({ page }) => {
  171 |       const sectionsWithCards = ['/activities', '/discover', '/famhub'];
  172 |       
  173 |       for (const section of sectionsWithCards) {
  174 |         await page.goto(section);
  175 |         await page.waitForLoadState('networkidle');
  176 |         
  177 |         // Check for BentoCard usage
  178 |         const bentoCards = page.locator('[data-testid*="bento"], [class*="bento"], [class*="card"]');
  179 |         const cardCount = await bentoCards.count();
  180 |         
  181 |         if (cardCount > 0) {
  182 |           // Verify consistent card styling
  183 |           for (let i = 0; i < Math.min(cardCount, 5); i++) { // Check first 5 cards
  184 |             const card = bentoCards.nth(i);
  185 |             const isVisible = await card.isVisible();
  186 |             
  187 |             if (isVisible) {
  188 |               // Verify card has proper structure
  189 |               const hasContent = await card.locator('*').count() > 0;
  190 |               expect(hasContent).toBe(true);
  191 |             }
  192 |           }
  193 |         }
  194 |       }
  195 |     });
  196 |
  197 |     test('should use EnhancedUnifiedBadge with color mapping', async ({ page }) => {
  198 |       await page.goto('/activities');
  199 |       await page.waitForLoadState('networkidle');
  200 |       
  201 |       // Check for enhanced badge usage
  202 |       const badges = page.locator('[data-testid*="badge"], [class*="badge"]');
  203 |       const badgeCount = await badges.count();
  204 |       
  205 |       if (badgeCount > 0) {
  206 |         // Verify badges use color mapping service
  207 |         for (let i = 0; i < Math.min(badgeCount, 3); i++) {
  208 |           const badge = badges.nth(i);
  209 |           const isVisible = await badge.isVisible();
  210 |           
  211 |           if (isVisible) {
  212 |             const styles = await badge.getAttribute('style');
  213 |             const classes = await badge.getAttribute('class');
  214 |             
  215 |             // Should not have hardcoded colors
  216 |             if (styles) {
  217 |               expect(styles).not.toMatch(/#[0-9a-f]{6}|rgb\(|rgba\(/i);
  218 |             }
  219 |             
  220 |             // Should use design system classes
  221 |             expect(classes).toMatch(/badge|unified|enhanced/i);
  222 |           }
  223 |         }
  224 |       }
  225 |     });
  226 |   });
  227 |
  228 |   test.describe('Performance Validation', () => {
  229 |     
  230 |     test('should load all sections under 200ms', async ({ page }) => {
  231 |       for (const section of SECTIONS) {
  232 |         const startTime = performance.now();
  233 |         
  234 |         await page.goto(section);
  235 |         await page.waitForLoadState('domcontentloaded');
  236 |         
  237 |         const endTime = performance.now();
  238 |         const loadTime = endTime - startTime;
  239 |         
  240 |         console.log(`${section} load time: ${loadTime.toFixed(2)}ms`);
  241 |         expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLD);
  242 |       }
  243 |     });
  244 |
  245 |     test('should have fast interaction response times', async ({ page }) => {
  246 |       await page.goto('/activities');
  247 |       await page.waitForLoadState('networkidle');
  248 |       
  249 |       // Test button interactions
  250 |       const buttons = page.locator('button:visible').first();
  251 |       
  252 |       if (await buttons.count() > 0) {
  253 |         const startTime = performance.now();
  254 |         
  255 |         await buttons.click();
  256 |         
  257 |         // Wait for any visual feedback
  258 |         await page.waitForTimeout(50);
  259 |         
  260 |         const endTime = performance.now();
  261 |         const interactionTime = endTime - startTime;
  262 |         
  263 |         expect(interactionTime).toBeLessThan(100); // Very fast interaction
  264 |       }
  265 |     });
  266 |
> 267 |     test('should not have infinite loops or performance issues', async ({ page }) => {
      |     ^ Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
  268 |       // Monitor console errors
  269 |       const consoleErrors = [];
  270 |       page.on('console', msg => {
  271 |         if (msg.type() === 'error') {
  272 |           consoleErrors.push(msg.text());
  273 |         }
  274 |       });
  275 |       
  276 |       for (const section of SECTIONS) {
  277 |         await page.goto(section);
  278 |         await page.waitForTimeout(2000); // Wait for any async operations
  279 |         
  280 |         // Check for infinite loop indicators
  281 |         const infiniteLoopErrors = consoleErrors.filter(error => 
  282 |           error.includes('Maximum update depth') || 
  283 |           error.includes('infinite') ||
  284 |           error.includes('stack overflow')
  285 |         );
  286 |         
  287 |         expect(infiniteLoopErrors.length).toBe(0);
  288 |       }
  289 |     });
  290 |   });
  291 |
  292 |   test.describe('Cross-Section Consistency', () => {
  293 |     
  294 |     test('should have consistent header and navigation across all sections', async ({ page }) => {
  295 |       let headerStructure = null;
  296 |       
  297 |       for (const section of SECTIONS) {
  298 |         await page.goto(section);
  299 |         await page.waitForLoadState('networkidle');
  300 |         
  301 |         // Get header structure
  302 |         const header = page.locator('header, nav').first();
  303 |         const currentStructure = await header.innerHTML().catch(() => '');
  304 |         
  305 |         if (headerStructure === null) {
  306 |           headerStructure = currentStructure;
  307 |         } else {
  308 |           // Verify header consistency (allowing for active state differences)
  309 |           const normalizedCurrent = currentStructure.replace(/active|current/g, '');
  310 |           const normalizedOriginal = headerStructure.replace(/active|current/g, '');
  311 |           
  312 |           // Headers should be structurally similar
  313 |           expect(normalizedCurrent.length).toBeGreaterThan(normalizedOriginal.length * 0.8);
  314 |           expect(normalizedCurrent.length).toBeLessThan(normalizedOriginal.length * 1.2);
  315 |         }
  316 |       }
  317 |     });
  318 |
  319 |     test('should have consistent styling and theme across sections', async ({ page }) => {
  320 |       const colorSchemes = [];
  321 |       
  322 |       for (const section of SECTIONS) {
  323 |         await page.goto(section);
  324 |         await page.waitForLoadState('networkidle');
  325 |         
  326 |         // Get computed styles of main elements
  327 |         const bodyStyles = await page.evaluate(() => {
  328 |           const body = document.body;
  329 |           const computed = window.getComputedStyle(body);
  330 |           return {
  331 |             backgroundColor: computed.backgroundColor,
  332 |             color: computed.color,
  333 |             fontFamily: computed.fontFamily
  334 |           };
  335 |         });
  336 |         
  337 |         colorSchemes.push(bodyStyles);
  338 |       }
  339 |       
  340 |       // Verify consistent color scheme
  341 |       const firstScheme = colorSchemes[0];
  342 |       for (const scheme of colorSchemes.slice(1)) {
  343 |         expect(scheme.backgroundColor).toBe(firstScheme.backgroundColor);
  344 |         expect(scheme.fontFamily).toBe(firstScheme.fontFamily);
  345 |       }
  346 |     });
  347 |   });
  348 | });
  349 |
```
# 📋 Festival Family Coding Standards

## Overview

These coding standards reflect our **standardized codebase architecture** implemented in 2025. All code must follow these patterns to maintain consistency, performance, and maintainability.

## 🏗️ **Architecture Principles**

### **1. Single Source of Truth**
- Use unified components from `@/components/design-system`
- Use `unifiedDataService` for all data operations
- Use `enhancedColorMappingService` for all styling
- No duplicate implementations allowed

### **2. React Patterns Over Complex Abstractions**
- Prefer React hooks over service classes
- Use simple, focused components
- Avoid over-engineering

### **3. Performance First**
- Monitor bundle size impact
- Use lazy loading appropriately
- Optimize for mobile performance

## 🎯 **Component Standards**

### **Use Unified Components**

#### ✅ **Correct**
```typescript
import { 
  UnifiedInteractionButton,
  UnifiedModal,
  BentoCard,
  EnhancedUnifiedBadge 
} from '@/components/design-system';

// All interactions through unified component
<UnifiedInteractionButton
  type="favorite"
  itemId="activity-123"
  itemType="activity"
  variant="compact"
  showCount={true}
/>
```

#### ❌ **Incorrect**
```typescript
// DON'T: Create duplicate interaction components
const CustomFavoriteButton = () => { /* ... */ };

// DON'T: Use removed legacy components
import { JoinLeaveButton } from '@/components/activities/JoinLeaveButton';
```

### **Component Structure**

```typescript
/**
 * Component Name
 * 
 * Brief description of component purpose and usage.
 * 
 * @module ComponentName
 * @version 1.0.0
 */

import React from 'react';
import { cn } from '@/lib/utils';

interface ComponentProps {
  // Props with clear types and descriptions
  title: string;
  description?: string;
  className?: string;
}

export const ComponentName: React.FC<ComponentProps> = ({
  title,
  description,
  className
}) => {
  return (
    <div className={cn('base-classes', className)}>
      <h2>{title}</h2>
      {description && <p>{description}</p>}
    </div>
  );
};

export default ComponentName;
```

## 🎨 **Styling Standards**

### **Use Design Tokens**

#### ✅ **Correct**
```typescript
// Use design tokens from CSS variables
<div className="bg-primary text-primary-foreground p-4 rounded-lg">
  Content
</div>

// Use enhancedColorMappingService for dynamic colors
const colors = enhancedColorMappingService.getColorsForContent('activities', 'meetup');
<div style={{ backgroundColor: colors.background }}>
  Dynamic Content
</div>
```

#### ❌ **Incorrect**
```typescript
// DON'T: Hardcode colors
<div style={{ backgroundColor: '#8b5cf6', color: '#ffffff' }}>
  Content
</div>

// DON'T: Use arbitrary color values
<div className="bg-purple-600 text-white">
  Content
</div>
```

### **Responsive Design**

```typescript
// Mobile-first responsive classes
<div className="
  grid 
  grid-cols-1 
  md:grid-cols-2 
  lg:grid-cols-3 
  gap-4 
  p-4
">
  <ResponsiveContent />
</div>
```

## 🔧 **Service Standards**

### **Use Unified Data Service**

#### ✅ **Correct**
```typescript
import { unifiedDataService } from '@/lib/data/unified-data-service';

// Single source for all data operations
const activities = await unifiedDataService.getActivities({
  filters: { type: 'meetup', status: 'published' }
});

const result = await unifiedDataService.createActivity(activityData);
```

#### ❌ **Incorrect**
```typescript
// DON'T: Create scattered data fetching
const fetchActivities = async () => {
  const response = await fetch('/api/activities');
  return response.json();
};

// DON'T: Use removed complex services
import { OptimizedRealtimeService } from '@/lib/services/OptimizedRealtimeService';
```

### **React Hook Patterns**

#### ✅ **Correct**
```typescript
import { useRealtimeSubscription } from '@/hooks/realtime/useRealtime';

// Simple React hook for real-time updates
const MyComponent = () => {
  useRealtimeSubscription('activities', ['activities', 'all'], {
    event: '*',
    callback: (payload) => {
      console.log('Activity updated:', payload);
    }
  });

  return <ComponentContent />;
};
```

#### ❌ **Incorrect**
```typescript
// DON'T: Complex service instantiation
const realtimeService = new OptimizedRealtimeService();
await realtimeService.initialize();
```

## 📝 **TypeScript Standards**

### **Strict Type Safety**

```typescript
// Define clear interfaces
interface ActivityData {
  id: string;
  title: string;
  description: string;
  type: 'meetup' | 'workshop' | 'social';
  status: 'draft' | 'published' | 'archived';
  createdAt: Date;
  updatedAt: Date;
}

// Use proper return types
const getActivity = async (id: string): Promise<ActivityData | null> => {
  return unifiedDataService.getActivity(id);
};

// Use proper error handling
const handleActivityUpdate = async (id: string, data: Partial<ActivityData>) => {
  try {
    const result = await unifiedDataService.updateActivity(id, data);
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
};
```

### **Component Props**

```typescript
// Comprehensive prop interfaces
interface UnifiedButtonProps {
  type: 'favorite' | 'join' | 'rsvp' | 'share' | 'helpful' | 'save';
  itemId: string;
  itemType: 'activity' | 'event' | 'festival' | 'post';
  variant?: 'default' | 'compact' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  showCount?: boolean;
  count?: number;
  disabled?: boolean;
  className?: string;
  onStateChange?: (state: InteractionState) => void;
}
```

## 🧪 **Testing Standards**

### **Component Testing**

```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { UnifiedInteractionButton } from '@/components/design-system';

describe('UnifiedInteractionButton', () => {
  it('renders correctly for each interaction type', () => {
    const types = ['favorite', 'join', 'rsvp'] as const;
    
    types.forEach(type => {
      render(
        <UnifiedInteractionButton
          type={type}
          itemId="test-123"
          itemType="activity"
        />
      );
      
      expect(screen.getByLabelText(new RegExp(type, 'i'))).toBeInTheDocument();
    });
  });

  it('handles state changes correctly', async () => {
    const onStateChange = jest.fn();
    
    render(
      <UnifiedInteractionButton
        type="favorite"
        itemId="test-123"
        itemType="activity"
        onStateChange={onStateChange}
      />
    );

    fireEvent.click(screen.getByRole('button'));
    
    expect(onStateChange).toHaveBeenCalledWith(
      expect.objectContaining({
        isActive: expect.any(Boolean),
        count: expect.any(Number)
      })
    );
  });
});
```

### **Service Testing**

```typescript
import { unifiedDataService } from '@/lib/data/unified-data-service';

describe('unifiedDataService', () => {
  it('fetches activities with correct filters', async () => {
    const activities = await unifiedDataService.getActivities({
      filters: { type: 'meetup', status: 'published' }
    });

    expect(activities).toBeInstanceOf(Array);
    expect(activities.every(a => a.type === 'meetup')).toBe(true);
  });
});
```

## 📁 **File Organization**

### **Directory Structure**

```
src/
├── components/
│   ├── design-system/          # ✅ Unified components only
│   │   ├── UnifiedInteractionButton.tsx
│   │   ├── UnifiedModal.tsx
│   │   └── index.ts
│   ├── activities/             # ✅ Feature-specific components
│   ├── admin/                  # ✅ Admin-specific components
│   └── common/                 # ✅ Shared utilities
├── lib/
│   ├── data/                   # ✅ Unified data services
│   ├── services/               # ✅ Enhanced services only
│   └── utils/                  # ✅ Utility functions
├── hooks/                      # ✅ Custom React hooks
├── styles/                     # ✅ Design tokens and global styles
└── pages/                      # ✅ Application routes
```

### **Import Organization**

```typescript
// 1. React and external libraries
import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';

// 2. Internal utilities and services
import { unifiedDataService } from '@/lib/data/unified-data-service';
import { enhancedColorMappingService } from '@/lib/services/enhancedColorMappingService';

// 3. Components (design system first)
import { 
  UnifiedInteractionButton,
  UnifiedModal,
  BentoCard 
} from '@/components/design-system';
import { ActivityCard } from '@/components/activities/ActivityCard';

// 4. Types and interfaces
import type { Activity, User } from '@/types';

// 5. Styles (if needed)
import './ComponentName.css';
```

## 🚀 **Performance Standards**

### **Bundle Size Monitoring**

```bash
# Check bundle size impact
npm run build
npm run bundle-analyzer

# Performance monitoring
npm run monitoring:start
```

### **Component Optimization**

```typescript
// Use React.memo for expensive components
export const ExpensiveComponent = React.memo<Props>(({ data }) => {
  return <ComplexVisualization data={data} />;
});

// Use useMemo for expensive calculations
const processedData = useMemo(() => {
  return expensiveDataProcessing(rawData);
}, [rawData]);

// Use useCallback for event handlers
const handleClick = useCallback((id: string) => {
  onItemClick(id);
}, [onItemClick]);
```

## ♿ **Accessibility Standards**

### **WCAG 2.1 AA Compliance**

```typescript
// Proper ARIA labels
<UnifiedInteractionButton
  type="favorite"
  itemId="activity-123"
  itemType="activity"
  aria-label="Add activity to favorites"
  aria-describedby="favorite-help-text"
/>

// Keyboard navigation
<div
  role="button"
  tabIndex={0}
  onKeyDown={(e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      handleClick();
    }
  }}
>
  Interactive Element
</div>

// Focus management
<UnifiedModal
  isOpen={isOpen}
  onClose={onClose}
  initialFocus="close-button"
>
  <ModalContent />
</UnifiedModal>
```

## 🔍 **Code Review Checklist**

### **Before Submitting**
- [ ] Uses unified components from design system
- [ ] No hardcoded colors or styles
- [ ] Proper TypeScript types
- [ ] Comprehensive test coverage
- [ ] Accessibility compliance
- [ ] Performance considerations
- [ ] Mobile responsiveness

### **During Review**
- [ ] Follows single source of truth principle
- [ ] Uses React patterns over complex abstractions
- [ ] Maintains consistency with existing code
- [ ] Proper error handling
- [ ] Clear documentation

## 📚 **Resources**

- [Unified Design System Documentation](./UNIFIED_DESIGN_SYSTEM.md)
- [Legacy Migration Guide](./LEGACY_MIGRATION_GUIDE.md)
- [Developer Onboarding Guide](./DEVELOPER_ONBOARDING.md)
- [Performance Optimization Guide](./PERFORMANCE_OPTIMIZATION.md)

## 🎯 **Enforcement**

These standards are enforced through:

1. **ESLint Configuration** - Automated code quality checks
2. **TypeScript Strict Mode** - Type safety enforcement
3. **Pre-commit Hooks** - Automated formatting and linting
4. **Code Review Process** - Manual review of all changes
5. **Performance Monitoring** - Continuous performance tracking

---

Following these coding standards ensures that Festival Family maintains its high-quality, performant, and maintainable codebase. Happy coding! 🚀

*Standards updated: 2025 - Standardized Codebase Version*

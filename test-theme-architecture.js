/**
 * Festival Family Theme Architecture Test
 * 
 * This test validates the new readability-first theme architecture
 * by checking CSS variables, contrast ratios, and automatic text switching.
 */

// Test configuration
const TEST_URL = 'http://localhost:5173';
const REQUIRED_CONTRAST_RATIO = 4.5; // WCAG 2.1 AA standard

// CSS Variables to test
const CSS_VARIABLES = {
  // Form field readability-constant variables
  formBackground: '--form-background',
  formForeground: '--form-foreground',
  formBorder: '--form-border',
  formFocusRing: '--form-focus-ring',
  
  // Automatic contrast text variables
  textOnLight: '--text-on-light',
  textOnDark: '--text-on-dark',
  textMutedOnLight: '--text-muted-on-light',
  textMutedOnDark: '--text-muted-on-dark',
  
  // Theme-adaptive variables
  background: '--background',
  foreground: '--foreground',
  card: '--card',
  cardForeground: '--card-foreground'
};

// Utility classes to test
const UTILITY_CLASSES = [
  'text-auto-contrast',
  'text-auto-contrast-muted',
  'text-on-light-bg',
  'text-muted-on-light-bg',
  'text-on-dark-bg',
  'text-muted-on-dark-bg',
  'festival-text-auto',
  'festival-text-auto-muted',
  'smart-text-contrast',
  'smart-text-contrast-muted'
];

// Components to test for proper contrast
const COMPONENTS_TO_TEST = [
  { selector: 'input', description: 'Form inputs' },
  { selector: 'textarea', description: 'Text areas' },
  { selector: 'select', description: 'Select dropdowns' },
  { selector: '.card', description: 'Card components' },
  { selector: 'button', description: 'Button components' },
  { selector: 'nav', description: 'Navigation elements' },
  { selector: 'h1, h2, h3, h4, h5, h6', description: 'Headings' }
];

/**
 * Convert HSL string to RGB values
 */
function hslToRgb(hslString) {
  // Extract H, S, L values from "hsl(h s% l%)" format
  const match = hslString.match(/hsl\((\d+(?:\.\d+)?)\s+(\d+(?:\.\d+)?)%\s+(\d+(?:\.\d+)?)%\)/);
  if (!match) {
    console.warn('Invalid HSL format:', hslString);
    return null;
  }
  
  const h = parseFloat(match[1]) / 360;
  const s = parseFloat(match[2]) / 100;
  const l = parseFloat(match[3]) / 100;
  
  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h * 6) % 2 - 1));
  const m = l - c / 2;
  
  let r, g, b;
  if (h < 1/6) [r, g, b] = [c, x, 0];
  else if (h < 2/6) [r, g, b] = [x, c, 0];
  else if (h < 3/6) [r, g, b] = [0, c, x];
  else if (h < 4/6) [r, g, b] = [0, x, c];
  else if (h < 5/6) [r, g, b] = [x, 0, c];
  else [r, g, b] = [c, 0, x];
  
  return [
    Math.round((r + m) * 255),
    Math.round((g + m) * 255),
    Math.round((b + m) * 255)
  ];
}

/**
 * Calculate relative luminance for contrast ratio
 */
function getLuminance(rgb) {
  const [r, g, b] = rgb.map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  return 0.2126 * r + 0.7152 * g + 0.0722 * b;
}

/**
 * Calculate contrast ratio between two colors
 */
function getContrastRatio(color1, color2) {
  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  return (brightest + 0.05) / (darkest + 0.05);
}

/**
 * Test CSS variables exist and have valid values
 */
function testCSSVariables() {
  console.log('🧪 Testing CSS Variables...');
  const root = document.documentElement;
  const computedStyle = getComputedStyle(root);
  
  const results = {};
  
  Object.entries(CSS_VARIABLES).forEach(([name, variable]) => {
    const value = computedStyle.getPropertyValue(variable).trim();
    const isValid = value && value !== '';
    
    results[name] = {
      variable,
      value,
      isValid,
      status: isValid ? '✅' : '❌'
    };
    
    console.log(`${results[name].status} ${variable}: ${value || 'NOT FOUND'}`);
  });
  
  return results;
}

/**
 * Test utility classes exist and apply correct styles
 */
function testUtilityClasses() {
  console.log('\n🎨 Testing Utility Classes...');
  
  const testElement = document.createElement('div');
  document.body.appendChild(testElement);
  
  const results = {};
  
  UTILITY_CLASSES.forEach(className => {
    testElement.className = className;
    const computedStyle = getComputedStyle(testElement);
    const color = computedStyle.color;
    
    const isValid = color && color !== 'rgba(0, 0, 0, 0)' && color !== 'rgb(0, 0, 0)';
    
    results[className] = {
      color,
      isValid,
      status: isValid ? '✅' : '❌'
    };
    
    console.log(`${results[className].status} .${className}: ${color || 'NO COLOR'}`);
  });
  
  document.body.removeChild(testElement);
  return results;
}

/**
 * Test contrast ratios for form fields (readability-constant)
 */
function testFormFieldContrast() {
  console.log('\n📝 Testing Form Field Contrast (Readability-Constant)...');
  
  const root = document.documentElement;
  const computedStyle = getComputedStyle(root);
  
  const formBg = computedStyle.getPropertyValue('--form-background').trim();
  const formFg = computedStyle.getPropertyValue('--form-foreground').trim();
  
  if (!formBg || !formFg) {
    console.log('❌ Form field variables not found');
    return { isValid: false };
  }
  
  const bgHsl = `hsl(${formBg})`;
  const fgHsl = `hsl(${formFg})`;
  
  const bgRgb = hslToRgb(bgHsl);
  const fgRgb = hslToRgb(fgHsl);
  
  if (!bgRgb || !fgRgb) {
    console.log('❌ Could not parse form field colors');
    return { isValid: false };
  }
  
  const contrastRatio = getContrastRatio(bgRgb, fgRgb);
  const isValid = contrastRatio >= REQUIRED_CONTRAST_RATIO;
  
  console.log(`${isValid ? '✅' : '❌'} Form fields: ${contrastRatio.toFixed(2)}:1 (Required: ${REQUIRED_CONTRAST_RATIO}:1)`);
  console.log(`   Background: ${bgHsl} (${bgRgb.join(', ')})`);
  console.log(`   Foreground: ${fgHsl} (${fgRgb.join(', ')})`);
  
  return {
    contrastRatio,
    isValid,
    background: { hsl: bgHsl, rgb: bgRgb },
    foreground: { hsl: fgHsl, rgb: fgRgb }
  };
}

/**
 * Test theme switching functionality
 */
function testThemeSwitching() {
  console.log('\n🌓 Testing Theme Switching...');
  
  const root = document.documentElement;
  const initialTheme = root.classList.contains('dark') ? 'dark' : 'light';
  
  console.log(`Initial theme: ${initialTheme}`);
  
  // Test switching to opposite theme
  if (initialTheme === 'light') {
    root.classList.add('dark');
    console.log('✅ Switched to dark theme');
  } else {
    root.classList.remove('dark');
    console.log('✅ Switched to light theme');
  }
  
  // Test that CSS variables change
  const computedStyle = getComputedStyle(root);
  const background = computedStyle.getPropertyValue('--background').trim();
  const foreground = computedStyle.getPropertyValue('--foreground').trim();
  
  console.log(`Background: hsl(${background})`);
  console.log(`Foreground: hsl(${foreground})`);
  
  // Restore original theme
  if (initialTheme === 'dark') {
    root.classList.add('dark');
  } else {
    root.classList.remove('dark');
  }
  
  return {
    initialTheme,
    switchedSuccessfully: true,
    newBackground: background,
    newForeground: foreground
  };
}

/**
 * Run all tests
 */
function runAllTests() {
  console.log('🚀 Festival Family Theme Architecture Test Suite\n');
  
  const results = {
    cssVariables: testCSSVariables(),
    utilityClasses: testUtilityClasses(),
    formFieldContrast: testFormFieldContrast(),
    themeSwitching: testThemeSwitching()
  };
  
  // Summary
  console.log('\n📊 Test Summary:');
  
  const cssVarsPassed = Object.values(results.cssVariables).filter(r => r.isValid).length;
  const cssVarsTotal = Object.keys(results.cssVariables).length;
  console.log(`CSS Variables: ${cssVarsPassed}/${cssVarsTotal} passed`);
  
  const utilityPassed = Object.values(results.utilityClasses).filter(r => r.isValid).length;
  const utilityTotal = Object.keys(results.utilityClasses).length;
  console.log(`Utility Classes: ${utilityPassed}/${utilityTotal} passed`);
  
  console.log(`Form Field Contrast: ${results.formFieldContrast.isValid ? 'PASS' : 'FAIL'}`);
  console.log(`Theme Switching: ${results.themeSwitching.switchedSuccessfully ? 'PASS' : 'FAIL'}`);
  
  const overallSuccess = cssVarsPassed === cssVarsTotal && 
                        utilityPassed === utilityTotal && 
                        results.formFieldContrast.isValid && 
                        results.themeSwitching.switchedSuccessfully;
  
  console.log(`\n${overallSuccess ? '🎉' : '⚠️'} Overall: ${overallSuccess ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
  
  return results;
}

// Auto-run tests when script loads
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
  } else {
    runAllTests();
  }
}

// Export for manual testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    testCSSVariables,
    testUtilityClasses,
    testFormFieldContrast,
    testThemeSwitching
  };
}

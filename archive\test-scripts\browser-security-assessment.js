/**
 * Browser-Based Security Assessment
 * 
 * This script uses <PERSON><PERSON> to test security vulnerabilities in the browser environment
 * including XSS, CSRF, session hijacking, and client-side security measures.
 */

import { chromium } from 'playwright';
import fs from 'fs';

console.log('🌐 Browser-Based Security Assessment');
console.log('===================================');

async function browserSecurityAssessment() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  const results = {
    timestamp: new Date().toISOString(),
    vulnerabilities: [],
    securityMeasures: [],
    recommendations: []
  };

  try {
    // Test 1: XSS Protection in Forms
    console.log('🧪 Test 1: XSS Protection in Browser Forms');
    console.log('------------------------------------------');
    
    await page.goto('http://localhost:5180/auth');
    await page.waitForTimeout(3000);
    
    // Test XSS in login form
    const xssPayload = '<script>alert("XSS")</script>';
    
    await page.fill('input[type="email"]', xssPayload);
    await page.fill('input[type="password"]', 'testpassword');
    
    // Check if XSS payload is reflected in DOM
    const emailValue = await page.inputValue('input[type="email"]');
    if (emailValue === xssPayload) {
      console.log('⚠️ XSS payload accepted in email field');
      results.vulnerabilities.push({
        type: 'XSS',
        location: 'Login form email field',
        severity: 'Medium',
        description: 'XSS payload accepted without sanitization'
      });
    } else {
      console.log('✅ XSS payload sanitized in email field');
      results.securityMeasures.push('Email field XSS protection');
    }
    
    // Test 2: Content Security Policy (CSP)
    console.log('');
    console.log('🛡️ Test 2: Content Security Policy Assessment');
    console.log('--------------------------------------------');
    
    const response = await page.goto('http://localhost:5180/');
    const headers = response.headers();
    
    if (headers['content-security-policy']) {
      console.log('✅ Content Security Policy detected');
      console.log('   CSP:', headers['content-security-policy']);
      results.securityMeasures.push('Content Security Policy implemented');
    } else {
      console.log('⚠️ No Content Security Policy detected');
      results.vulnerabilities.push({
        type: 'Missing CSP',
        location: 'HTTP Headers',
        severity: 'Medium',
        description: 'No Content Security Policy header found'
      });
    }
    
    // Test X-Frame-Options
    if (headers['x-frame-options']) {
      console.log('✅ X-Frame-Options header detected:', headers['x-frame-options']);
      results.securityMeasures.push('X-Frame-Options header present');
    } else {
      console.log('⚠️ No X-Frame-Options header detected');
      results.vulnerabilities.push({
        type: 'Missing X-Frame-Options',
        location: 'HTTP Headers',
        severity: 'Low',
        description: 'No X-Frame-Options header found - clickjacking risk'
      });
    }
    
    // Test X-Content-Type-Options
    if (headers['x-content-type-options']) {
      console.log('✅ X-Content-Type-Options header detected:', headers['x-content-type-options']);
      results.securityMeasures.push('X-Content-Type-Options header present');
    } else {
      console.log('⚠️ No X-Content-Type-Options header detected');
      results.vulnerabilities.push({
        type: 'Missing X-Content-Type-Options',
        location: 'HTTP Headers',
        severity: 'Low',
        description: 'No X-Content-Type-Options header found'
      });
    }
    
    // Test 3: Session Security in Browser
    console.log('');
    console.log('🔑 Test 3: Browser Session Security Assessment');
    console.log('---------------------------------------------');
    
    // Authenticate and check session storage
    await page.goto('http://localhost:5180/auth');
    await page.waitForTimeout(2000);
    
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'testpassword123');
    await page.click('button[type="submit"]');
    
    await page.waitForTimeout(5000);
    
    // Check for session tokens in localStorage/sessionStorage
    const localStorage = await page.evaluate(() => {
      const items = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        items[key] = localStorage.getItem(key);
      }
      return items;
    });
    
    const sessionStorage = await page.evaluate(() => {
      const items = {};
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        items[key] = sessionStorage.getItem(key);
      }
      return items;
    });
    
    console.log('🔍 Session storage analysis:');
    
    // Check for sensitive data in storage
    const sensitiveKeys = ['token', 'password', 'secret', 'key', 'auth'];
    let sensitiveDataFound = false;
    
    Object.keys(localStorage).forEach(key => {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        console.log(`⚠️ Sensitive data in localStorage: ${key}`);
        sensitiveDataFound = true;
      }
    });
    
    Object.keys(sessionStorage).forEach(key => {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        console.log(`⚠️ Sensitive data in sessionStorage: ${key}`);
        sensitiveDataFound = true;
      }
    });
    
    if (!sensitiveDataFound) {
      console.log('✅ No obvious sensitive data in browser storage');
      results.securityMeasures.push('Secure session storage practices');
    } else {
      results.vulnerabilities.push({
        type: 'Sensitive Data in Storage',
        location: 'Browser Storage',
        severity: 'Medium',
        description: 'Sensitive authentication data stored in browser storage'
      });
    }
    
    // Test 4: HTTPS and Secure Cookies
    console.log('');
    console.log('🔒 Test 4: HTTPS and Cookie Security Assessment');
    console.log('----------------------------------------------');
    
    const cookies = await context.cookies();
    console.log('🔍 Cookie security analysis:');
    
    if (cookies.length === 0) {
      console.log('ℹ️ No cookies found (expected for Supabase JWT auth)');
      results.securityMeasures.push('No sensitive cookies stored');
    } else {
      cookies.forEach(cookie => {
        console.log(`   Cookie: ${cookie.name}`);
        console.log(`   - Secure: ${cookie.secure}`);
        console.log(`   - HttpOnly: ${cookie.httpOnly}`);
        console.log(`   - SameSite: ${cookie.sameSite}`);
        
        if (!cookie.secure && cookie.name.toLowerCase().includes('auth')) {
          results.vulnerabilities.push({
            type: 'Insecure Cookie',
            location: `Cookie: ${cookie.name}`,
            severity: 'High',
            description: 'Authentication cookie not marked as Secure'
          });
        }
      });
    }
    
    // Test 5: Client-Side Input Validation
    console.log('');
    console.log('🧪 Test 5: Client-Side Input Validation');
    console.log('--------------------------------------');
    
    // Navigate to profile page to test input validation
    await page.goto('http://localhost:5180/profile');
    await page.waitForTimeout(3000);
    
    // Test for client-side validation bypass
    const maliciousInputs = [
      '<script>alert("XSS")</script>',
      'javascript:alert("XSS")',
      '"><script>alert("XSS")</script>',
      "'; DROP TABLE users; --"
    ];
    
    for (const input of maliciousInputs) {
      try {
        // Try to find input fields and test validation
        const inputFields = await page.$$('input[type="text"], textarea');
        
        for (const field of inputFields) {
          await field.fill(input);
          const value = await field.inputValue();
          
          if (value === input) {
            console.log(`⚠️ Malicious input accepted: ${input.substring(0, 20)}...`);
          }
        }
      } catch (error) {
        // Input validation might prevent the input
        console.log('✅ Input validation active');
      }
    }
    
    // Test 6: Console Security
    console.log('');
    console.log('🔍 Test 6: Console Security and Error Handling');
    console.log('---------------------------------------------');
    
    const consoleLogs = [];
    page.on('console', msg => {
      consoleLogs.push({
        type: msg.type(),
        text: msg.text()
      });
    });
    
    // Trigger some actions to generate console output
    await page.reload();
    await page.waitForTimeout(3000);
    
    // Check for sensitive information in console
    const sensitivePatterns = [
      /password/i,
      /secret/i,
      /token/i,
      /api[_-]?key/i,
      /private[_-]?key/i
    ];
    
    let sensitiveConsoleData = false;
    consoleLogs.forEach(log => {
      sensitivePatterns.forEach(pattern => {
        if (pattern.test(log.text)) {
          console.log(`⚠️ Sensitive data in console: ${log.text.substring(0, 50)}...`);
          sensitiveConsoleData = true;
        }
      });
    });
    
    if (!sensitiveConsoleData) {
      console.log('✅ No sensitive data detected in console logs');
      results.securityMeasures.push('Secure console logging practices');
    } else {
      results.vulnerabilities.push({
        type: 'Sensitive Console Data',
        location: 'Browser Console',
        severity: 'Medium',
        description: 'Sensitive information exposed in console logs'
      });
    }

  } catch (error) {
    console.error('💥 Browser security assessment failed:', error);
    results.vulnerabilities.push({
      type: 'Assessment Error',
      location: 'Browser Testing',
      severity: 'Unknown',
      description: error.message
    });
  } finally {
    await browser.close();
  }

  // Save results
  fs.writeFileSync('browser-security-assessment-results.json', JSON.stringify(results, null, 2));
  
  return results;
}

// Run the browser security assessment
browserSecurityAssessment().then((results) => {
  console.log('');
  console.log('📊 BROWSER SECURITY ASSESSMENT SUMMARY');
  console.log('======================================');
  console.log('');
  console.log(`🚨 Vulnerabilities Found: ${results.vulnerabilities.length}`);
  console.log(`✅ Security Measures: ${results.securityMeasures.length}`);
  console.log('');
  
  if (results.vulnerabilities.length > 0) {
    console.log('🚨 VULNERABILITIES:');
    results.vulnerabilities.forEach((vuln, index) => {
      console.log(`   ${index + 1}. ${vuln.type} (${vuln.severity})`);
      console.log(`      Location: ${vuln.location}`);
      console.log(`      Description: ${vuln.description}`);
    });
    console.log('');
  }
  
  if (results.securityMeasures.length > 0) {
    console.log('✅ SECURITY MEASURES:');
    results.securityMeasures.forEach((measure, index) => {
      console.log(`   ${index + 1}. ${measure}`);
    });
  }
  
  console.log('');
  console.log('📝 Results saved to: browser-security-assessment-results.json');
  process.exit(0);
}).catch(error => {
  console.error('💥 Browser security assessment suite failed:', error);
  process.exit(1);
});

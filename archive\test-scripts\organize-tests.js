#!/usr/bin/env node

/**
 * Test Organization and Automation Setup
 * 
 * This script organizes all audit testing scripts into a proper test structure
 * and creates automated test runners for continuous monitoring.
 */

import { promises as fs } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Test organization structure
const TEST_STRUCTURE = {
  'tests/': {
    'e2e/': {
      'authentication/': {},
      'navigation/': {},
      'responsive/': {},
      'admin/': {}
    },
    'integration/': {
      'api/': {},
      'database/': {}
    },
    'unit/': {
      'components/': {},
      'utils/': {}
    },
    'audit/': {
      'scripts/': {},
      'evidence/': {},
      'reports/': {}
    },
    'config/': {}
  }
};

// Current audit scripts to organize
const AUDIT_SCRIPTS = [
  'auth-signup-test.js',
  'authenticated-navigation-test.js',
  'user-journey-audit.js',
  'simple-navigation-audit.js',
  'admin-access-test.js',
  'admin-functionality-test.js',
  'responsive-design-audit.js',
  'architecture-consistency-audit.js'
];

// Evidence directories to organize
const EVIDENCE_DIRS = [
  'auth-signup-evidence',
  'authenticated-navigation-evidence',
  'user-journey-evidence',
  'simple-navigation-evidence',
  'admin-access-evidence',
  'admin-functionality-evidence',
  'responsive-design-evidence',
  'architecture-consistency-evidence'
];

async function createDirectoryStructure(structure, basePath = '') {
  for (const [name, content] of Object.entries(structure)) {
    const fullPath = join(basePath, name);
    
    if (typeof content === 'object' && Object.keys(content).length > 0) {
      // Directory with subdirectories
      await fs.mkdir(fullPath, { recursive: true });
      await createDirectoryStructure(content, fullPath);
    } else {
      // Empty directory
      await fs.mkdir(fullPath, { recursive: true });
    }
  }
}

async function moveAuditScripts() {
  console.log('\n📁 Moving audit scripts to organized structure...');
  
  const auditScriptsDir = 'tests/audit/scripts';
  
  for (const script of AUDIT_SCRIPTS) {
    try {
      const sourcePath = script;
      const targetPath = join(auditScriptsDir, script);
      
      // Check if source exists
      try {
        await fs.access(sourcePath);
        await fs.rename(sourcePath, targetPath);
        console.log(`✅ Moved ${script} to ${targetPath}`);
      } catch (error) {
        console.log(`⚠️ Script ${script} not found, skipping...`);
      }
    } catch (error) {
      console.log(`❌ Error moving ${script}: ${error.message}`);
    }
  }
}

async function moveEvidenceDirectories() {
  console.log('\n📁 Moving evidence directories to organized structure...');
  
  const auditEvidenceDir = 'tests/audit/evidence';
  
  for (const evidenceDir of EVIDENCE_DIRS) {
    try {
      const sourcePath = evidenceDir;
      const targetPath = join(auditEvidenceDir, evidenceDir);
      
      // Check if source exists
      try {
        await fs.access(sourcePath);
        await fs.rename(sourcePath, targetPath);
        console.log(`✅ Moved ${evidenceDir} to ${targetPath}`);
      } catch (error) {
        console.log(`⚠️ Evidence directory ${evidenceDir} not found, skipping...`);
      }
    } catch (error) {
      console.log(`❌ Error moving ${evidenceDir}: ${error.message}`);
    }
  }
}

async function createTestRunner() {
  console.log('\n🔧 Creating automated test runner...');
  
  const testRunnerContent = `#!/usr/bin/env node

/**
 * Automated Test Runner for Festival Family
 * 
 * Runs comprehensive audit tests and generates reports
 */

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import { join } from 'path';

const AUDIT_SCRIPTS = [
  'auth-signup-test.js',
  'authenticated-navigation-test.js',
  'user-journey-audit.js',
  'simple-navigation-audit.js',
  'admin-access-test.js',
  'responsive-design-audit.js',
  'architecture-consistency-audit.js'
];

async function runScript(scriptPath) {
  return new Promise((resolve, reject) => {
    console.log(\`🔄 Running \${scriptPath}...\`);
    
    const child = spawn('node', [scriptPath], {
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        console.log(\`✅ \${scriptPath} completed successfully\`);
        resolve();
      } else {
        console.log(\`❌ \${scriptPath} failed with code \${code}\`);
        reject(new Error(\`Script failed: \${scriptPath}\`));
      }
    });
    
    child.on('error', (error) => {
      console.log(\`❌ Error running \${scriptPath}: \${error.message}\`);
      reject(error);
    });
  });
}

async function runAllTests() {
  console.log('🧪 FESTIVAL FAMILY - AUTOMATED TEST SUITE');
  console.log('=========================================');
  console.log(\`🕐 Start Time: \${new Date().toISOString()}\`);
  
  const results = {
    timestamp: new Date().toISOString(),
    totalTests: AUDIT_SCRIPTS.length,
    passed: 0,
    failed: 0,
    results: []
  };
  
  for (const script of AUDIT_SCRIPTS) {
    const scriptPath = join('tests/audit/scripts', script);
    
    try {
      const startTime = Date.now();
      await runScript(scriptPath);
      const duration = Date.now() - startTime;
      
      results.passed++;
      results.results.push({
        script,
        status: 'PASSED',
        duration: \`\${duration}ms\`
      });
    } catch (error) {
      results.failed++;
      results.results.push({
        script,
        status: 'FAILED',
        error: error.message
      });
    }
  }
  
  // Generate summary report
  console.log(\`\\n📊 TEST SUITE SUMMARY\`);
  console.log(\`=====================\`);
  console.log(\`✅ Passed: \${results.passed}/\${results.totalTests}\`);
  console.log(\`❌ Failed: \${results.failed}/\${results.totalTests}\`);
  console.log(\`📁 Evidence: tests/audit/evidence/\`);
  
  // Save results
  await fs.writeFile(
    'tests/audit/reports/test-suite-results.json',
    JSON.stringify(results, null, 2)
  );
  
  console.log(\`📄 Report saved: tests/audit/reports/test-suite-results.json\`);
  
  return results;
}

// Run tests if called directly
if (import.meta.url === \`file://\${process.argv[1]}\`) {
  runAllTests()
    .then(() => {
      console.log('\\n✅ Test suite completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Test suite failed:', error);
      process.exit(1);
    });
}

export { runAllTests };
`;

  await fs.writeFile('tests/test-runner.js', testRunnerContent);
  console.log('✅ Created automated test runner: tests/test-runner.js');
}

async function createPackageScripts() {
  console.log('\n📦 Creating package.json test scripts...');
  
  try {
    const packageJsonPath = 'package.json';
    const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'));
    
    // Add test scripts
    if (!packageJson.scripts) {
      packageJson.scripts = {};
    }
    
    packageJson.scripts['test:audit'] = 'node tests/test-runner.js';
    packageJson.scripts['test:auth'] = 'node tests/audit/scripts/auth-signup-test.js';
    packageJson.scripts['test:navigation'] = 'node tests/audit/scripts/simple-navigation-audit.js';
    packageJson.scripts['test:responsive'] = 'node tests/audit/scripts/responsive-design-audit.js';
    packageJson.scripts['test:admin'] = 'node tests/audit/scripts/admin-access-test.js';
    packageJson.scripts['test:architecture'] = 'node tests/audit/scripts/architecture-consistency-audit.js';
    
    await fs.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ Added test scripts to package.json');
    
    console.log('\n📋 Available test commands:');
    console.log('  npm run test:audit        # Run full audit suite');
    console.log('  npm run test:auth         # Test authentication');
    console.log('  npm run test:navigation   # Test navigation');
    console.log('  npm run test:responsive   # Test responsive design');
    console.log('  npm run test:admin        # Test admin features');
    console.log('  npm run test:architecture # Test architecture consistency');
    
  } catch (error) {
    console.log(`⚠️ Could not update package.json: ${error.message}`);
  }
}

async function createReadme() {
  console.log('\n📄 Creating test documentation...');
  
  const readmeContent = `# Festival Family - Test Suite

## Overview

This directory contains comprehensive testing infrastructure for Festival Family, including automated audit tests, evidence collection, and production readiness monitoring.

## Directory Structure

\`\`\`
tests/
├── e2e/                          # End-to-end tests
│   ├── authentication/           # Auth flow tests
│   ├── navigation/               # Navigation tests
│   ├── responsive/               # Cross-platform tests
│   └── admin/                    # Admin functionality tests
├── integration/                  # Integration tests
│   ├── api/                      # API integration tests
│   └── database/                 # Database tests
├── unit/                         # Unit tests
│   ├── components/               # Component tests
│   └── utils/                    # Utility function tests
├── audit/                        # Production readiness audits
│   ├── scripts/                  # Audit testing scripts
│   ├── evidence/                 # Generated evidence
│   └── reports/                  # Audit reports
├── config/                       # Test configuration
└── test-runner.js                # Automated test runner
\`\`\`

## Running Tests

### Full Audit Suite
\`\`\`bash
npm run test:audit
\`\`\`

### Individual Test Categories
\`\`\`bash
npm run test:auth         # Authentication testing
npm run test:navigation   # Navigation testing
npm run test:responsive   # Responsive design testing
npm run test:admin        # Admin features testing
npm run test:architecture # Architecture consistency testing
\`\`\`

## Test Categories

### Audit Tests
- **Authentication Flow**: Complete auth testing with visual verification
- **Navigation System**: Cross-platform navigation functionality
- **Responsive Design**: Multi-viewport testing with screenshots
- **Admin Features**: Role-based access control and admin functionality
- **Architecture Consistency**: Component and state management analysis

### Evidence Collection
- **Screenshots**: Visual verification of application state
- **JSON Reports**: Detailed technical metrics and analysis
- **Performance Data**: Load times and responsiveness metrics
- **Security Analysis**: Access control and permission testing

## Continuous Monitoring

The test suite is designed for:
- **Production Readiness Assessment**: Regular audit runs
- **Regression Testing**: Ensure fixes don't break existing functionality
- **Performance Monitoring**: Track application performance over time
- **Security Validation**: Ongoing security and access control testing

## Adding New Tests

1. Create test script in appropriate category directory
2. Add evidence collection and screenshot capture
3. Include detailed JSON reporting
4. Update test runner configuration
5. Add npm script for easy execution

## Test Results

All test results are saved in:
- **Evidence**: \`tests/audit/evidence/\`
- **Reports**: \`tests/audit/reports/\`
- **Screenshots**: Included in evidence directories

## Maintenance

- **Regular Updates**: Keep tests updated with application changes
- **Evidence Cleanup**: Periodically clean old evidence files
- **Performance Baseline**: Update performance benchmarks as needed
- **Documentation**: Keep test documentation current
`;

  await fs.writeFile('tests/README.md', readmeContent);
  console.log('✅ Created test documentation: tests/README.md');
}

async function organizeTests() {
  console.log('🧪 FESTIVAL FAMILY - TEST ORGANIZATION');
  console.log('======================================');
  console.log(`🕐 Start Time: ${new Date().toISOString()}`);
  
  try {
    // Create directory structure
    console.log('\n📁 Creating test directory structure...');
    await createDirectoryStructure(TEST_STRUCTURE);
    console.log('✅ Test directory structure created');
    
    // Move audit scripts
    await moveAuditScripts();
    
    // Move evidence directories
    await moveEvidenceDirectories();
    
    // Create test runner
    await createTestRunner();
    
    // Update package.json
    await createPackageScripts();
    
    // Create documentation
    await createReadme();
    
    // Move production readiness report
    try {
      await fs.rename('PRODUCTION_READINESS_REPORT.md', 'tests/audit/reports/PRODUCTION_READINESS_REPORT.md');
      console.log('✅ Moved production readiness report to tests/audit/reports/');
    } catch (error) {
      console.log('⚠️ Could not move production readiness report');
    }
    
    console.log('\n🎉 TEST ORGANIZATION COMPLETE');
    console.log('============================');
    console.log('✅ Test structure created');
    console.log('✅ Audit scripts organized');
    console.log('✅ Evidence directories moved');
    console.log('✅ Automated test runner created');
    console.log('✅ Package.json scripts added');
    console.log('✅ Documentation created');
    console.log('\n📋 Next steps:');
    console.log('1. Run: npm run test:audit');
    console.log('2. Review: tests/README.md');
    console.log('3. Check: tests/audit/reports/');
    
  } catch (error) {
    console.error('\n💥 Test organization failed:', error);
    throw error;
  }
}

// Run organization if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  organizeTests()
    .then(() => {
      console.log('\n✅ Test organization completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Test organization failed:', error);
      process.exit(1);
    });
}

export { organizeTests };

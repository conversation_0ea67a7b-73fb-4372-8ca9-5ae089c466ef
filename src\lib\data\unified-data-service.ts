/**
 * Unified Data Service - Single Source of Truth for Festival Family
 * 
 * This service consolidates all database operations and provides a unified interface
 * for all data fetching, real-time subscriptions, and CRUD operations.
 * 
 * @module UnifiedDataService
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { supabase } from '@/lib/supabase';
import type { Database } from '@/types/supabase';
import type { RealtimeChannel } from '@supabase/supabase-js';

// Type definitions for unified service
type Activity = Database['public']['Tables']['activities']['Row'];
type Event = Database['public']['Tables']['events']['Row'];
type Announcement = Database['public']['Tables']['announcements']['Row'];
type Festival = Database['public']['Tables']['festivals']['Row'];
type Community = Database['public']['Tables']['communities']['Row'];
type LocalInfo = Database['public']['Tables']['local_info']['Row'];
type Tip = Database['public']['Tables']['tips']['Row'];
type Guide = Database['public']['Tables']['guides']['Row'];
type FAQ = Database['public']['Tables']['faqs']['Row'];
type ExternalLink = Database['public']['Tables']['external_links']['Row'];

// Filter types for data operations
interface ActivityFilters {
  festivalId?: string;
  type?: string;
  featured?: boolean;
  status?: string;
}

interface EventFilters {
  festivalId?: string;
  category?: string;
  featured?: boolean;
  status?: string;
}

// Data creation types
type CreateActivityData = Omit<Activity, 'id' | 'created_at' | 'updated_at'>;
type UpdateActivityData = Partial<Omit<Activity, 'id' | 'created_at' | 'updated_at'>>;
type CreateEventData = Omit<Event, 'id' | 'created_at' | 'updated_at'>;
type UpdateEventData = Partial<Omit<Event, 'id' | 'created_at' | 'updated_at'>>;
type CreateAnnouncementData = Omit<Announcement, 'id' | 'created_at' | 'updated_at'>;
type UpdateAnnouncementData = Partial<Omit<Announcement, 'id' | 'created_at' | 'updated_at'>>;

// Activity with details type for complex queries
interface ActivityWithDetails extends Activity {
  event?: Event;
  parent_activity?: Activity;
  sub_activities?: Activity[];
}

/**
 * Unified Data Service Class
 * Single source of truth for all database operations
 */
class UnifiedDataService {
  private client = supabase; // Single Supabase client instance
  private subscriptions = new Map<string, RealtimeChannel>();

  // ============================================================================
  // ERROR HANDLING
  // ============================================================================

  /**
   * Standardized error handling for all operations
   */
  private handleError(error: any, operation: string): never {
    console.error(`Unified Data Service - ${operation} error:`, error);
    throw new Error(error.message || `${operation} operation failed`);
  }

  // ============================================================================
  // ACTIVITIES OPERATIONS
  // ============================================================================

  /**
   * Get activities with optional filtering
   */
  async getActivities(filters?: ActivityFilters): Promise<Activity[]> {
    try {
      // Debug: Check authentication status
      const { data: { session } } = await this.client.auth.getSession();
      console.log('🔍 Activities query - Auth status:', session ? 'authenticated' : 'anonymous');

      let query = this.client.from('activities').select('*');

      // Apply filters if provided
      if (filters?.festivalId) {
        query = query.eq('festival_id', filters.festivalId);
      }
      if (filters?.type) {
        query = query.eq('type', filters.type as any);
      }
      if (filters?.featured !== undefined) {
        query = query.eq('is_featured', filters.featured);
      }
      if (filters?.status) {
        query = query.eq('status', filters.status);
      }

      // Default ordering with featured items first
      query = query.order('is_featured', { ascending: false })
                   .order('start_date', { ascending: true });

      console.log('🔍 Executing activities query...');
      const { data, error } = await query;

      if (error) {
        console.error('Unified Data Service - getActivities error:', error);
        console.error('Error details:', { code: error.code, message: error.message, details: error.details });
        throw new Error(error.message || 'Failed to fetch activities');
      }

      console.log('✅ Activities query successful, found:', data?.length || 0, 'activities');
      return data || [];
    } catch (error) {
      console.error('Unified Data Service - getActivities error:', error);
      throw error;
    }
  }

  /**
   * Get activity by ID with related details
   */
  async getActivityById(id: string): Promise<ActivityWithDetails | null> {
    try {
      const { data, error } = await this.client
        .from('activities')
        .select(`
          *,
          event:events(*),
          parent_activity:activities!parent_activity_id(*),
          sub_activities:activities!parent_activity_id(*)
        `)
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // Not found
        this.handleError(error, 'getActivityById');
      }

      return data as unknown as ActivityWithDetails;
    } catch (error) {
      this.handleError(error, 'getActivityById');
    }
  }

  /**
   * Create a new activity
   */
  async createActivity(data: CreateActivityData): Promise<Activity> {
    try {
      const { data: newActivity, error } = await this.client
        .from('activities')
        .insert(data)
        .select()
        .single();

      if (error) this.handleError(error, 'createActivity');

      return newActivity;
    } catch (error) {
      this.handleError(error, 'createActivity');
    }
  }

  /**
   * Update an activity
   */
  async updateActivity(id: string, updates: UpdateActivityData): Promise<Activity> {
    try {
      const { data, error } = await this.client
        .from('activities')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) this.handleError(error, 'updateActivity');

      return data;
    } catch (error) {
      this.handleError(error, 'updateActivity');
    }
  }

  /**
   * Delete an activity
   */
  async deleteActivity(id: string): Promise<void> {
    try {
      const { error } = await this.client
        .from('activities')
        .delete()
        .eq('id', id);

      if (error) this.handleError(error, 'deleteActivity');
    } catch (error) {
      this.handleError(error, 'deleteActivity');
    }
  }

  // ============================================================================
  // EVENTS OPERATIONS
  // ============================================================================

  /**
   * Get events with optional filtering
   */
  async getEvents(filters?: EventFilters): Promise<Event[]> {
    try {
      let query = this.client.from('events').select('*');

      // Apply filters if provided
      if (filters?.festivalId) {
        query = query.eq('festival_id', filters.festivalId);
      }
      if (filters?.category) {
        query = query.eq('category', filters.category);
      }
      if (filters?.featured !== undefined) {
        query = query.eq('is_featured', filters.featured);
      }
      if (filters?.status) {
        query = query.eq('status', filters.status as any);
      }

      // Default ordering
      query = query.order('start_date', { ascending: true });

      const { data, error } = await query;
      if (error) this.handleError(error, 'getEvents');

      return data || [];
    } catch (error) {
      this.handleError(error, 'getEvents');
      return []; // Fallback return for error cases
    }
  }

  /**
   * Get event by ID
   */
  async getEventById(id: string): Promise<Event | null> {
    try {
      const { data, error } = await this.client
        .from('events')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // Not found
        this.handleError(error, 'getEventById');
      }

      return data;
    } catch (error) {
      this.handleError(error, 'getEventById');
    }
  }

  /**
   * Create a new event
   */
  async createEvent(data: CreateEventData): Promise<Event> {
    try {
      const { data: newEvent, error } = await this.client
        .from('events')
        .insert(data)
        .select()
        .single();

      if (error) this.handleError(error, 'createEvent');

      return newEvent;
    } catch (error) {
      this.handleError(error, 'createEvent');
    }
  }

  /**
   * Update an event
   */
  async updateEvent(id: string, updates: UpdateEventData): Promise<Event> {
    try {
      const { data, error } = await this.client
        .from('events')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) this.handleError(error, 'updateEvent');

      return data;
    } catch (error) {
      this.handleError(error, 'updateEvent');
    }
  }

  /**
   * Delete an event
   */
  async deleteEvent(id: string): Promise<void> {
    try {
      const { error } = await this.client
        .from('events')
        .delete()
        .eq('id', id);

      if (error) this.handleError(error, 'deleteEvent');
    } catch (error) {
      this.handleError(error, 'deleteEvent');
    }
  }

  // ============================================================================
  // ANNOUNCEMENTS OPERATIONS
  // ============================================================================

  /**
   * Get announcements
   */
  async getAnnouncements(): Promise<Announcement[]> {
    try {
      const { data, error } = await this.client
        .from('announcements')
        .select('*')
        .eq('status', 'published')
        .order('is_featured', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) this.handleError(error, 'getAnnouncements');

      return data || [];
    } catch (error) {
      this.handleError(error, 'getAnnouncements');
    }
  }

  /**
   * Create a new announcement
   */
  async createAnnouncement(data: CreateAnnouncementData): Promise<Announcement> {
    try {
      const { data: newAnnouncement, error } = await this.client
        .from('announcements')
        .insert(data)
        .select()
        .single();

      if (error) this.handleError(error, 'createAnnouncement');

      return newAnnouncement;
    } catch (error) {
      this.handleError(error, 'createAnnouncement');
    }
  }

  /**
   * Update an announcement
   */
  async updateAnnouncement(id: string, updates: UpdateAnnouncementData): Promise<Announcement> {
    try {
      const { data, error } = await this.client
        .from('announcements')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) this.handleError(error, 'updateAnnouncement');

      return data;
    } catch (error) {
      this.handleError(error, 'updateAnnouncement');
    }
  }

  /**
   * Delete an announcement
   */
  async deleteAnnouncement(id: string): Promise<void> {
    try {
      const { error } = await this.client
        .from('announcements')
        .delete()
        .eq('id', id);

      if (error) this.handleError(error, 'deleteAnnouncement');
    } catch (error) {
      this.handleError(error, 'deleteAnnouncement');
    }
  }

  // ============================================================================
  // FESTIVALS OPERATIONS
  // ============================================================================

  /**
   * Get festivals
   */
  async getFestivals(): Promise<Festival[]> {
    try {
      const { data, error } = await this.client
        .from('festivals')
        .select('*')
        .order('start_date', { ascending: true });

      if (error) this.handleError(error, 'getFestivals');

      return data || [];
    } catch (error) {
      this.handleError(error, 'getFestivals');
    }
  }

  // ============================================================================
  // REAL-TIME SUBSCRIPTIONS
  // ============================================================================

  /**
   * Subscribe to activities changes
   */
  subscribeToActivities(callback: (data: Activity[]) => void): () => void {
    const channelName = 'activities_changes';

    // Remove existing subscription if any
    if (this.subscriptions.has(channelName)) {
      this.subscriptions.get(channelName)?.unsubscribe();
      this.subscriptions.delete(channelName);
    }

    const channel = this.client
      .channel(channelName)
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'activities' },
        () => {
          // Refetch activities and call callback
          this.getActivities().then(callback).catch(console.error);
        }
      )
      .subscribe();

    this.subscriptions.set(channelName, channel);

    // Return unsubscribe function
    return () => {
      channel.unsubscribe();
      this.subscriptions.delete(channelName);
    };
  }

  /**
   * Subscribe to events changes
   */
  subscribeToEvents(callback: (data: Event[]) => void): () => void {
    const channelName = 'events_changes';

    // Remove existing subscription if any
    if (this.subscriptions.has(channelName)) {
      this.subscriptions.get(channelName)?.unsubscribe();
      this.subscriptions.delete(channelName);
    }

    const channel = this.client
      .channel(channelName)
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'events' },
        () => {
          // Refetch events and call callback
          this.getEvents().then(callback).catch(console.error);
        }
      )
      .subscribe();

    this.subscriptions.set(channelName, channel);

    // Return unsubscribe function
    return () => {
      channel.unsubscribe();
      this.subscriptions.delete(channelName);
    };
  }

  /**
   * Subscribe to announcements changes
   */
  subscribeToAnnouncements(callback: (data: Announcement[]) => void): () => void {
    const channelName = 'announcements_changes';

    // Remove existing subscription if any
    if (this.subscriptions.has(channelName)) {
      this.subscriptions.get(channelName)?.unsubscribe();
      this.subscriptions.delete(channelName);
    }

    const channel = this.client
      .channel(channelName)
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'announcements' },
        () => {
          // Refetch announcements and call callback
          this.getAnnouncements().then(callback).catch(console.error);
        }
      )
      .subscribe();

    this.subscriptions.set(channelName, channel);

    // Return unsubscribe function
    return () => {
      channel.unsubscribe();
      this.subscriptions.delete(channelName);
    };
  }

  /**
   * Unsubscribe from all channels
   */
  unsubscribeAll(): void {
    this.subscriptions.forEach(channel => {
      channel.unsubscribe();
    });
    this.subscriptions.clear();
  }

  /**
   * Unsubscribe from specific table
   */
  unsubscribeFromTable(table: string): void {
    const channelName = `${table}_changes`;
    const channel = this.subscriptions.get(channelName);
    if (channel) {
      channel.unsubscribe();
      this.subscriptions.delete(channelName);
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  // ============================================================================
  // COMMUNITIES OPERATIONS
  // ============================================================================

  /**
   * Get communities
   */
  async getCommunities(): Promise<Community[]> {
    try {
      const { data, error } = await this.client
        .from('communities')
        .select('*')
        .eq('is_active', true)
        .order('is_featured', { ascending: false })
        .order('member_count', { ascending: false });

      if (error) this.handleError(error, 'getCommunities');

      return data || [];
    } catch (error) {
      this.handleError(error, 'getCommunities');
    }
  }

  // ============================================================================
  // LOCAL INFO OPERATIONS
  // ============================================================================

  /**
   * Get local info
   */
  async getLocalInfo(): Promise<LocalInfo[]> {
    try {
      const { data, error } = await this.client
        .from('local_info')
        .select('*')
        .eq('is_active', true)
        .order('is_featured', { ascending: false })
        .order('priority', { ascending: false })
        .order('category', { ascending: true });

      if (error) this.handleError(error, 'getLocalInfo');

      return data || [];
    } catch (error) {
      this.handleError(error, 'getLocalInfo');
    }
  }

  // ============================================================================
  // RESOURCES OPERATIONS (TIPS, GUIDES, FAQS)
  // ============================================================================

  /**
   * Get tips
   */
  async getTips(): Promise<Tip[]> {
    try {
      const { data, error } = await this.client
        .from('tips')
        .select('*')
        .eq('status', 'published')
        .order('is_featured', { ascending: false })
        .limit(10);

      if (error) this.handleError(error, 'getTips');

      return data || [];
    } catch (error) {
      this.handleError(error, 'getTips');
    }
  }

  /**
   * Get guides
   */
  async getGuides(): Promise<Guide[]> {
    try {
      const { data, error } = await this.client
        .from('guides')
        .select('*')
        .eq('status', 'published')
        .order('is_featured', { ascending: false })
        .limit(10);

      if (error) this.handleError(error, 'getGuides');

      return data || [];
    } catch (error) {
      this.handleError(error, 'getGuides');
    }
  }

  /**
   * Get FAQs
   */
  async getFAQs(): Promise<FAQ[]> {
    try {
      const { data, error } = await this.client
        .from('faqs')
        .select('*')
        .eq('status', 'published')
        .order('is_featured', { ascending: false })
        .limit(10);

      if (error) this.handleError(error, 'getFAQs');

      return data || [];
    } catch (error) {
      this.handleError(error, 'getFAQs');
    }
  }

  /**
   * Get all resources (tips, guides, faqs) combined
   */
  async getResources(): Promise<Array<(Tip | Guide | FAQ) & { type: 'tip' | 'guide' | 'faq' }>> {
    try {
      const [tips, guides, faqs] = await Promise.all([
        this.getTips(),
        this.getGuides(),
        this.getFAQs()
      ]);

      const allResources = [
        ...tips.slice(0, 2).map(item => ({ ...item, type: 'tip' as const })),
        ...guides.slice(0, 2).map(item => ({ ...item, type: 'guide' as const })),
        ...faqs.slice(0, 2).map(item => ({ ...item, type: 'faq' as const }))
      ];

      return allResources;
    } catch (error) {
      this.handleError(error, 'getResources');
    }
  }

  // ============================================================================
  // EXTERNAL LINKS OPERATIONS
  // ============================================================================

  /**
   * Get external links by type (corrected column name)
   */
  async getExternalLinks(types?: string[]): Promise<ExternalLink[]> {
    try {
      let query = this.client
        .from('external_links')
        .select('*')
        .eq('active', true);

      if (types && types.length > 0) {
        query = query.in('type', types);
      }

      query = query.order('created_at', { ascending: false });

      const { data, error } = await query;
      if (error) this.handleError(error, 'getExternalLinks');

      return data || [];
    } catch (error) {
      this.handleError(error, 'getExternalLinks');
    }
  }

  /**
   * Get chat links specifically
   */
  async getChatLinks(): Promise<ExternalLink[]> {
    return this.getExternalLinks(['CHAT', 'SOCIAL']);
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): boolean {
    return this.client.realtime.isConnected();
  }

  /**
   * Get active subscriptions count
   */
  getActiveSubscriptionsCount(): number {
    return this.subscriptions.size;
  }
}

// Export singleton instance
export const unifiedDataService = new UnifiedDataService();

// Export types for external use
export type {
  Activity,
  Event,
  Announcement,
  Festival,
  Community,
  LocalInfo,
  Tip,
  Guide,
  FAQ,
  ExternalLink
};

export type {
  ActivityWithDetails,
  ActivityFilters,
  EventFilters,
  CreateActivityData,
  UpdateActivityData,
  CreateEventData,
  UpdateEventData,
  CreateAnnouncementData,
  UpdateAnnouncementData
};

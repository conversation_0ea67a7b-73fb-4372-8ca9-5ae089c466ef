# Festival Family - Production Deployment Guide

## 🚀 **Deployment Overview**

This guide provides step-by-step instructions for deploying Festival Family to production. The application is production-ready with zero TypeScript errors, comprehensive testing, and robust error handling.

## ✅ **Pre-Deployment Checklist**

### **Code Quality Verification**
- [x] Zero TypeScript compilation errors
- [x] All tests passing (80+ tests)
- [x] ESLint warnings resolved
- [x] Security vulnerabilities addressed
- [x] Performance optimizations applied

### **Architecture Validation**
- [x] Single source of truth maintained
- [x] Consolidated authentication system
- [x] Error handling implemented
- [x] Type safety ensured
- [x] Documentation complete

### **Production Readiness**
- [x] Environment variables configured
- [x] Error monitoring setup (Sentry)
- [x] Database migrations applied
- [x] RLS policies configured
- [x] Performance monitoring enabled

## 🔧 **Environment Setup**

### **Required Environment Variables**

Create a `.env.production` file with the following variables:

```bash
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# Sentry Monitoring (Optional but Recommended)
VITE_SENTRY_DSN=https://<EMAIL>/project-id

# Application Configuration
VITE_APP_ENV=production
VITE_APP_VERSION=1.0.0
```

### **Supabase Project Configuration**

1. **Database Setup**
   ```sql
   -- Apply migrations from supabase/migrations/
   -- Ensure all tables are created with proper RLS policies
   ```

2. **Authentication Settings**
   - Enable email authentication
   - Configure redirect URLs for production domain
   - Set up email templates
   - Configure rate limiting

3. **Storage Configuration**
   - Create avatar storage bucket
   - Configure RLS policies for file access
   - Set up CDN if needed

## 🏗️ **Build Process**

### **Production Build**

```bash
# Install dependencies
npm ci

# Type checking
npm run type-check

# Run tests
npm run test:ci

# Build for production
npm run build
```

### **Build Verification**

```bash
# Verify build output
ls -la dist/

# Check bundle size
npm run build -- --analyze

# Test production build locally
npm run preview
```

## 🌐 **Deployment Platforms**

### **Vercel Deployment (Recommended)**

1. **Setup Vercel Project**
   ```bash
   npm install -g vercel
   vercel login
   vercel --prod
   ```

2. **Configure Build Settings**
   ```json
   {
     "buildCommand": "npm run build",
     "outputDirectory": "dist",
     "installCommand": "npm ci",
     "framework": "vite"
   }
   ```

3. **Environment Variables**
   - Add all production environment variables in Vercel dashboard
   - Ensure Supabase URLs are correctly configured

### **Netlify Deployment**

1. **Build Configuration** (`netlify.toml`)
   ```toml
   [build]
     command = "npm run build"
     publish = "dist"
   
   [build.environment]
     NODE_VERSION = "18"
   
   [[redirects]]
     from = "/*"
     to = "/index.html"
     status = 200
   ```

2. **Deploy**
   ```bash
   npm install -g netlify-cli
   netlify deploy --prod --dir=dist
   ```

### **Custom Server Deployment**

1. **Docker Configuration** (`Dockerfile`)
   ```dockerfile
   FROM node:18-alpine AS builder
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci
   COPY . .
   RUN npm run build
   
   FROM nginx:alpine
   COPY --from=builder /app/dist /usr/share/nginx/html
   COPY nginx.conf /etc/nginx/nginx.conf
   EXPOSE 80
   CMD ["nginx", "-g", "daemon off;"]
   ```

2. **Nginx Configuration** (`nginx.conf`)
   ```nginx
   server {
     listen 80;
     server_name localhost;
     root /usr/share/nginx/html;
     index index.html;
     
     location / {
       try_files $uri $uri/ /index.html;
     }
     
     location /api {
       proxy_pass https://your-project.supabase.co;
     }
   }
   ```

## 🔒 **Security Configuration**

### **Content Security Policy**

Add to `index.html`:
```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' https://your-project.supabase.co;
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
  font-src 'self' https://fonts.gstatic.com;
  img-src 'self' data: https:;
  connect-src 'self' https://your-project.supabase.co wss://your-project.supabase.co;
">
```

### **HTTPS Configuration**

Ensure all production deployments use HTTPS:
- Configure SSL certificates
- Redirect HTTP to HTTPS
- Update Supabase redirect URLs

### **Environment Security**

- Never commit `.env` files
- Use platform-specific secret management
- Rotate API keys regularly
- Monitor for exposed secrets

## 📊 **Monitoring Setup**

### **Sentry Error Monitoring**

1. **Configure Sentry** (`src/lib/sentry.ts`)
   ```typescript
   import * as Sentry from "@sentry/react"
   
   Sentry.init({
     dsn: import.meta.env.VITE_SENTRY_DSN,
     environment: import.meta.env.VITE_APP_ENV,
     tracesSampleRate: 1.0,
   })
   ```

2. **Error Boundary Integration**
   ```typescript
   import { ErrorBoundary } from "@sentry/react"
   
   <ErrorBoundary fallback={ErrorFallback}>
     <App />
   </ErrorBoundary>
   ```

### **Performance Monitoring**

1. **Web Vitals Tracking**
   ```typescript
   import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'
   
   getCLS(console.log)
   getFID(console.log)
   getFCP(console.log)
   getLCP(console.log)
   getTTFB(console.log)
   ```

2. **React Query DevTools**
   ```typescript
   // Only in development
   import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
   
   {process.env.NODE_ENV === 'development' && <ReactQueryDevtools />}
   ```

## 🔄 **CI/CD Pipeline**

### **GitHub Actions** (`.github/workflows/deploy.yml`)

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run type-check
      - run: npm run test:ci
      - run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run build
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

## 🧪 **Post-Deployment Testing**

### **Smoke Tests**

1. **Authentication Flow**
   - Sign up new user
   - Sign in existing user
   - Profile access
   - Sign out

2. **Core Functionality**
   - Festival browsing
   - Activity coordination
   - User connections
   - Admin access (if applicable)

3. **Error Handling**
   - Network errors
   - Invalid inputs
   - Permission errors

### **Performance Testing**

```bash
# Lighthouse CI
npm install -g @lhci/cli
lhci autorun --upload.target=temporary-public-storage

# Load testing
npm install -g artillery
artillery quick --count 10 --num 5 https://your-domain.com
```

## 📈 **Monitoring & Maintenance**

### **Health Checks**

1. **Application Health**
   - Monitor error rates
   - Track performance metrics
   - Check uptime

2. **Database Health**
   - Monitor Supabase dashboard
   - Check query performance
   - Monitor storage usage

### **Regular Maintenance**

1. **Weekly Tasks**
   - Review error logs
   - Check performance metrics
   - Update dependencies

2. **Monthly Tasks**
   - Security audit
   - Performance optimization
   - User feedback review

## 🆘 **Troubleshooting**

### **Common Issues**

1. **Build Failures**
   - Check TypeScript errors
   - Verify environment variables
   - Clear node_modules and reinstall

2. **Runtime Errors**
   - Check Sentry logs
   - Verify Supabase configuration
   - Check network connectivity

3. **Performance Issues**
   - Analyze bundle size
   - Check for memory leaks
   - Optimize images and assets

### **Rollback Procedure**

1. **Immediate Rollback**
   ```bash
   # Vercel
   vercel rollback
   
   # Netlify
   netlify sites:list
   netlify api rollbackSiteDeploy --site-id=SITE_ID
   ```

2. **Database Rollback**
   - Use Supabase backup restoration
   - Apply reverse migrations if needed

---

**Last Updated**: December 2024  
**Deployment Version**: 1.0 (Production Ready)  
**Status**: ✅ Ready for Production Deployment

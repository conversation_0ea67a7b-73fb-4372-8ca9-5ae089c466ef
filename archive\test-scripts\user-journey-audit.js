#!/usr/bin/env node

/**
 * Comprehensive Authenticated User Journey Audit
 * 
 * This script performs detailed evaluation of each authenticated page:
 * - Dashboard completeness and functionality
 * - User engagement features
 * - Content richness and usefulness
 * - Navigation between pages
 * - Overall user experience quality
 */

import { chromium } from 'playwright';
import { promises as fs } from 'fs';

const APP_URL = 'http://localhost:5173';
const EVIDENCE_DIR = 'user-journey-audit-evidence';

// Test user credentials
const TEST_USER = {
  email: `journey.test.${Date.now()}@festivalfamily.test`,
  password: 'TestPassword123!',
  fullName: 'Journey Test User'
};

async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

async function authenticateUser(page) {
  console.log('\n🔐 Setting up authenticated user session');
  console.log('======================================');
  
  await page.goto(`${APP_URL}/auth`, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(2000);
  
  // Sign up new user
  const signUpTab = await page.$('text=Sign Up');
  if (signUpTab) {
    await signUpTab.click();
    await page.waitForTimeout(1000);
  }
  
  const emailInput = await page.$('input[type="email"]');
  const passwordInput = await page.$('input[type="password"]');
  
  if (emailInput && passwordInput) {
    await emailInput.fill(TEST_USER.email);
    await passwordInput.fill(TEST_USER.password);
    
    const submitButton = await page.$('button[type="submit"]');
    if (submitButton) {
      await submitButton.click();
      await page.waitForTimeout(3000);
    }
  }
  
  console.log(`✅ Authenticated as: ${TEST_USER.email}`);
}

async function auditDashboardExperience(page) {
  console.log('\n🏠 DASHBOARD EXPERIENCE AUDIT');
  console.log('=============================');
  
  await page.goto(APP_URL, { waitUntil: 'domcontentloaded', timeout: 15000 });
  await page.waitForTimeout(3000);
  
  // Capture dashboard
  await page.screenshot({ 
    path: `${EVIDENCE_DIR}/01-dashboard-overview.png`,
    fullPage: true 
  });
  
  // Evaluate dashboard components
  const welcomeSection = await page.$('text=Welcome back') || await page.$('text=Welcome,');
  const quickActions = await page.$$('[href="/activities"], [href="/famhub"], [href="/discover"], [href="/profile"]');
  const recentActivity = await page.$('text=Recent Activity');
  const updatesSection = await page.$('text=Updates');
  const userInfo = await page.$('[class*="avatar"], [class*="profile"]') || await page.$('text=Member since');
  
  // Check for engagement features
  const notifications = await page.$('[class*="notification"], [class*="bell"]');
  const searchFeature = await page.$('input[type="search"], [placeholder*="search"]');
  const ctaButtons = await page.$$('button, [role="button"]');
  
  // Evaluate content richness
  const textContent = await page.textContent('body');
  const wordCount = textContent.split(/\s+/).length;
  const hasPersonalization = textContent.includes(TEST_USER.fullName) || textContent.includes(TEST_USER.email.split('@')[0]);
  
  console.log('📊 DASHBOARD COMPONENTS:');
  console.log(`👋 Welcome Section: ${welcomeSection ? '✅ Present' : '❌ Missing'}`);
  console.log(`⚡ Quick Actions: ${quickActions.length} found`);
  console.log(`📈 Recent Activity: ${recentActivity ? '✅ Present' : '❌ Missing'}`);
  console.log(`🔔 Updates Section: ${updatesSection ? '✅ Present' : '❌ Missing'}`);
  console.log(`👤 User Info Display: ${userInfo ? '✅ Present' : '❌ Missing'}`);
  
  console.log('\n🎯 ENGAGEMENT FEATURES:');
  console.log(`🔔 Notifications: ${notifications ? '✅ Present' : '❌ Missing'}`);
  console.log(`🔍 Search Feature: ${searchFeature ? '✅ Present' : '❌ Missing'}`);
  console.log(`🔘 Interactive Elements: ${ctaButtons.length} buttons/CTAs`);
  console.log(`🎨 Personalization: ${hasPersonalization ? '✅ Personalized' : '❌ Generic'}`);
  
  console.log('\n📄 CONTENT ANALYSIS:');
  console.log(`📝 Word Count: ${wordCount} words`);
  console.log(`📊 Content Richness: ${wordCount > 100 ? '✅ Rich' : '❌ Sparse'}`);
  
  return {
    components: {
      welcomeSection: !!welcomeSection,
      quickActionsCount: quickActions.length,
      recentActivity: !!recentActivity,
      updatesSection: !!updatesSection,
      userInfo: !!userInfo
    },
    engagement: {
      notifications: !!notifications,
      searchFeature: !!searchFeature,
      interactiveElements: ctaButtons.length,
      personalization: hasPersonalization
    },
    content: {
      wordCount,
      contentRichness: wordCount > 100
    },
    screenshot: '01-dashboard-overview.png'
  };
}

async function auditPageExperience(page, pageName, url, expectedFeatures = []) {
  console.log(`\n📄 ${pageName.toUpperCase()} PAGE AUDIT`);
  console.log('='.repeat(pageName.length + 15));
  
  try {
    await page.goto(`${APP_URL}${url}`, { waitUntil: 'domcontentloaded', timeout: 15000 });
    await page.waitForTimeout(3000);
    
    const screenshotName = `${pageName.toLowerCase().replace(/\s+/g, '-')}-audit.png`;
    await page.screenshot({ 
      path: `${EVIDENCE_DIR}/${screenshotName}`,
      fullPage: true 
    });
    
    // Basic page metrics
    const title = await page.title();
    const textContent = await page.textContent('body');
    const wordCount = textContent.split(/\s+/).length;
    
    // Check for loading states and errors
    const isLoading = await page.$('[class*="loading"]') !== null || await page.$('[class*="spinner"]') !== null;
    const hasError = await page.$('[class*="error"]') !== null || await page.$('text=Error') !== null;
    const isEmpty = wordCount < 50;
    
    // Evaluate expected features
    const featureResults = {};
    for (const feature of expectedFeatures) {
      try {
        const element = await page.$(feature.selector);
        featureResults[feature.name] = !!element;
        console.log(`${feature.icon} ${feature.name}: ${element ? '✅ Found' : '❌ Missing'}`);
      } catch (error) {
        featureResults[feature.name] = false;
        console.log(`${feature.icon} ${feature.name}: ❌ Error checking`);
      }
    }
    
    // User experience evaluation
    const hasNavigation = await page.$('nav') !== null;
    const hasBackButton = await page.$('text=Back') !== null || await page.$('[aria-label*="back"]') !== null;
    const hasSearch = await page.$('input[type="search"]') !== null || await page.$('[placeholder*="search"]') !== null;
    const hasFilters = await page.$('select') !== null || await page.$('[class*="filter"]') !== null;
    const hasCTA = await page.$('button') !== null || await page.$('[role="button"]') !== null;
    
    console.log('\n📊 PAGE METRICS:');
    console.log(`📋 Title: "${title}"`);
    console.log(`📝 Word Count: ${wordCount} words`);
    console.log(`⏳ Loading State: ${isLoading ? '⚠️ Still Loading' : '✅ Loaded'}`);
    console.log(`❌ Error State: ${hasError ? '⚠️ Has Errors' : '✅ No Errors'}`);
    console.log(`📄 Content Status: ${isEmpty ? '❌ Empty/Sparse' : '✅ Has Content'}`);
    
    console.log('\n🎯 UX FEATURES:');
    console.log(`🧭 Navigation: ${hasNavigation ? '✅ Present' : '❌ Missing'}`);
    console.log(`⬅️ Back Button: ${hasBackButton ? '✅ Present' : '❌ Missing'}`);
    console.log(`🔍 Search: ${hasSearch ? '✅ Present' : '❌ Missing'}`);
    console.log(`🔧 Filters: ${hasFilters ? '✅ Present' : '❌ Missing'}`);
    console.log(`🔘 Call-to-Action: ${hasCTA ? '✅ Present' : '❌ Missing'}`);
    
    // Calculate page score
    const featureScore = Object.values(featureResults).filter(Boolean).length / expectedFeatures.length * 100;
    const uxScore = [hasNavigation, hasSearch, hasFilters, hasCTA].filter(Boolean).length / 4 * 100;
    const contentScore = isEmpty ? 0 : (isLoading || hasError ? 50 : 100);
    const overallScore = (featureScore + uxScore + contentScore) / 3;
    
    console.log(`\n📊 PAGE SCORE: ${overallScore.toFixed(1)}%`);
    console.log(`   - Features: ${featureScore.toFixed(1)}%`);
    console.log(`   - UX: ${uxScore.toFixed(1)}%`);
    console.log(`   - Content: ${contentScore}%`);
    
    return {
      pageName,
      url: page.url(),
      title,
      wordCount,
      isLoading,
      hasError,
      isEmpty,
      features: featureResults,
      ux: {
        hasNavigation,
        hasBackButton,
        hasSearch,
        hasFilters,
        hasCTA
      },
      scores: {
        features: featureScore,
        ux: uxScore,
        content: contentScore,
        overall: overallScore
      },
      screenshot: screenshotName
    };
    
  } catch (error) {
    console.error(`❌ Failed to audit ${pageName}:`, error.message);
    return {
      pageName,
      error: error.message,
      scores: { overall: 0 }
    };
  }
}

async function runUserJourneyAudit() {
  console.log('🎯 COMPREHENSIVE USER JOURNEY AUDIT');
  console.log('===================================');
  console.log(`🕐 Start Time: ${new Date().toISOString()}`);
  
  await ensureEvidenceDir();
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const page = await context.newPage();
  
  try {
    // Authenticate user
    await authenticateUser(page);
    
    const results = {};
    
    // 1. Dashboard Experience Audit
    results.dashboard = await auditDashboardExperience(page);
    
    // 2. Activities Page Audit
    results.activities = await auditPageExperience(page, 'Activities', '/activities', [
      { name: 'Activity List', selector: '[class*="activity"]', icon: '📅' },
      { name: 'Create Button', selector: 'text=Create', icon: '➕' },
      { name: 'Filter System', selector: 'select', icon: '🔍' },
      { name: 'Join/RSVP Actions', selector: 'text=Join', icon: '✋' }
    ]);
    
    // 3. FamHub Page Audit
    results.famhub = await auditPageExperience(page, 'FamHub', '/famhub', [
      { name: 'User Connections', selector: '[class*="connection"]', icon: '👥' },
      { name: 'Search Users', selector: '[placeholder*="search"]', icon: '🔍' },
      { name: 'Chat/Message', selector: 'text=Chat', icon: '💬' },
      { name: 'Connect Button', selector: 'text=Connect', icon: '🤝' }
    ]);
    
    // 4. Discover Page Audit
    results.discover = await auditPageExperience(page, 'Discover', '/discover', [
      { name: 'Festival List', selector: '[class*="festival"]', icon: '🎪' },
      { name: 'Search Filters', selector: 'input[type="search"]', icon: '🔍' },
      { name: 'Map View', selector: 'text=Map', icon: '🗺️' },
      { name: 'Save/Favorite', selector: 'text=Save', icon: '⭐' }
    ]);

    // 5. Profile Page Audit
    results.profile = await auditPageExperience(page, 'Profile', '/profile', [
      { name: 'Profile Form', selector: 'form', icon: '👤' },
      { name: 'Avatar Upload', selector: 'input[type="file"]', icon: '📸' },
      { name: 'Save Changes', selector: 'button[type="submit"]', icon: '💾' },
      { name: 'Preferences', selector: 'text=Preferences', icon: '⚙️' }
    ]);
    
    // Calculate overall journey score
    const pageScores = Object.values(results).map(r => r.scores?.overall || 0);
    const averageScore = pageScores.reduce((sum, score) => sum + score, 0) / pageScores.length;
    
    // Save comprehensive results
    const evidence = {
      timestamp: new Date().toISOString(),
      testUser: { email: TEST_USER.email },
      auditResults: results,
      summary: {
        totalPages: Object.keys(results).length,
        averageScore: parseFloat(averageScore.toFixed(1)),
        dashboardScore: results.dashboard?.scores?.overall || 0,
        bestPage: Object.entries(results).reduce((best, [name, data]) => 
          (data.scores?.overall || 0) > (best.score || 0) ? { name, score: data.scores.overall } : best, {}),
        screenshots: Object.values(results).map(r => r.screenshot).filter(Boolean)
      }
    };
    
    await fs.writeFile(
      `${EVIDENCE_DIR}/user-journey-audit-results.json`,
      JSON.stringify(evidence, null, 2)
    );
    
    console.log('\n📊 USER JOURNEY AUDIT SUMMARY');
    console.log('=============================');
    console.log(`📄 Total Pages Audited: ${evidence.summary.totalPages}`);
    console.log(`📊 Average Score: ${evidence.summary.averageScore}%`);
    console.log(`🏠 Dashboard Score: ${evidence.summary.dashboardScore}%`);
    console.log(`🏆 Best Page: ${evidence.summary.bestPage.name} (${evidence.summary.bestPage.score}%)`);
    console.log(`📸 Screenshots: ${evidence.summary.screenshots.length}`);
    console.log(`📁 Evidence: ${EVIDENCE_DIR}/`);
    
    return evidence;
    
  } catch (error) {
    console.error('\n💥 User journey audit failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

// Run the user journey audit
runUserJourneyAudit()
  .then(() => {
    console.log('\n✅ User journey audit completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ User journey audit failed:', error);
    process.exit(1);
  });

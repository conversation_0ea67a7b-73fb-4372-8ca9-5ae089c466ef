/**
 * One-Handed Optimizer Component
 * 
 * Provides thumb-zone optimized interactions for mobile devices.
 * Uses existing design tokens and mobile UX patterns.
 * 
 * @component
 */

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronUp, MoreHorizontal } from 'lucide-react';
import { isMobileViewport, simulateHapticFeedback } from '@/utils/mobileUX';
import { useReducedMotion } from '@/hooks/ui/useReducedMotion';

interface OneHandedOptimizerProps {
  /** Quick actions to display in thumb zone */
  quickActions?: QuickAction[];
  /** Whether to show the floating action button */
  showFAB?: boolean;
  /** Custom className */
  className?: string;
  /** Children to render in the main content area */
  children: React.ReactNode;
}

interface QuickAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  action: () => void;
  priority?: 'high' | 'medium' | 'low';
}

/**
 * One-Handed Optimizer Component
 * 
 * Optimizes mobile interactions for one-handed use by placing
 * key actions in the thumb-reachable zone.
 */
export const OneHandedOptimizer: React.FC<OneHandedOptimizerProps> = ({
  quickActions = [],
  showFAB = true,
  className = '',
  children
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { shouldAnimate, getMotionProps } = useReducedMotion();

  const toggleExpanded = useCallback(() => {
    if (!isMobileViewport()) return;
    
    setIsExpanded(!isExpanded);
    simulateHapticFeedback('light');
  }, [isExpanded]);

  const handleActionClick = useCallback((action: QuickAction) => {
    action.action();
    setIsExpanded(false);
    simulateHapticFeedback('medium');
  }, []);

  // Don't render on desktop
  if (!isMobileViewport()) {
    return <>{children}</>;
  }

  return (
    <div className={`relative ${className}`}>
      {/* Main content */}
      {children}

      {/* Thumb zone overlay */}
      {showFAB && (
        <div className="mobile-thumb-zone">
          <div className="mobile-thumb-zone-content">
            {/* Quick actions menu */}
            <AnimatePresence>
              {isExpanded && quickActions.length > 0 && (
                <motion.div
                  {...getMotionProps({
                    initial: { opacity: 0, scale: 0.8, y: 20 },
                    animate: { opacity: 1, scale: 1, y: 0 },
                    exit: { opacity: 0, scale: 0.8, y: 20 },
                    transition: { duration: 0.2 }
                  })}
                  className="mb-4 space-y-2"
                >
                  {quickActions
                    .sort((a, b) => {
                      const priorityOrder = { high: 0, medium: 1, low: 2 };
                      return priorityOrder[a.priority || 'medium'] - priorityOrder[b.priority || 'medium'];
                    })
                    .map((action) => (
                      <motion.button
                        key={action.id}
                        {...getMotionProps({
                          whileTap: { scale: 0.95 },
                          transition: { duration: 0.1 }
                        })}
                        onClick={() => handleActionClick(action)}
                        className={`
                          flex items-center gap-3 px-4 py-3 rounded-full
                          bg-card/90 backdrop-blur-md border border-border/50
                          text-foreground shadow-lg
                          hover:bg-card transition-all duration-200
                          ${action.priority === 'high' ? 'ring-2 ring-festival-priority-high/30' : ''}
                        `}
                        style={{
                          minWidth: 'var(--mobile-action-min-width)',
                          touchAction: 'manipulation'
                        }}
                      >
                        <span className="text-lg">{action.icon}</span>
                        <span className="text-sm font-medium truncate">{action.label}</span>
                      </motion.button>
                    ))}
                </motion.div>
              )}
            </AnimatePresence>

            {/* Floating Action Button */}
            <motion.button
              {...getMotionProps({
                whileTap: { scale: 0.9 },
                whileHover: { scale: 1.05 },
                transition: { duration: 0.2 }
              })}
              onClick={toggleExpanded}
              className={`
                w-14 h-14 rounded-full flex items-center justify-center
                bg-primary text-primary-foreground shadow-lg
                hover:shadow-xl transition-all duration-200
                ${isExpanded ? 'rotate-180' : ''}
              `}
              style={{ touchAction: 'manipulation' }}
              aria-label={isExpanded ? 'Close quick actions' : 'Open quick actions'}
            >
              {quickActions.length > 0 ? (
                <motion.div
                  {...getMotionProps({
                    animate: { rotate: isExpanded ? 180 : 0 },
                    transition: { duration: 0.2 }
                  })}
                >
                  {isExpanded ? <ChevronUp className="w-6 h-6" /> : <MoreHorizontal className="w-6 h-6" />}
                </motion.div>
              ) : (
                <ChevronUp className="w-6 h-6" />
              )}
            </motion.button>
          </div>
        </div>
      )}
    </div>
  );
};

export default OneHandedOptimizer;

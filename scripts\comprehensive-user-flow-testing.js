#!/usr/bin/env node

/**
 * Comprehensive User Flow Testing
 * 
 * This script tests actual user flows by making API calls to Supabase
 * and verifying the complete authentication and database integration.
 */

import { createClient } from '@supabase/supabase-js';
import { promises as fs } from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const EVIDENCE_DIR = 'test-evidence-user-flows';

// Test user data
const TEST_USER = {
  email: `test.user.${Date.now()}@gmail.com`,
  password: 'TestPassword123!',
  fullName: 'Test User Evidence',
  username: `testuser${Date.now()}`
};

console.log('🔍 COMPREHENSIVE USER FLOW TESTING');
console.log('===================================');
console.log(`🕐 Start Time: ${new Date().toISOString()}`);

// Ensure evidence directory exists
async function ensureEvidenceDir() {
  try {
    await fs.mkdir(EVIDENCE_DIR, { recursive: true });
    console.log(`📁 Evidence directory ready: ${EVIDENCE_DIR}`);
  } catch (error) {
    console.log(`📁 Evidence directory exists: ${EVIDENCE_DIR}`);
  }
}

// Test 1: Supabase Connection Verification
async function testSupabaseConnection() {
  console.log('\n🔗 Test 1: Supabase Connection Verification');
  console.log('============================================');
  
  if (!supabaseUrl || !supabaseKey) {
    const evidence = {
      testName: 'Supabase Connection',
      timestamp: new Date().toISOString(),
      success: false,
      error: 'Missing Supabase configuration'
    };
    
    console.log('❌ Supabase configuration missing');
    await fs.writeFile(`${EVIDENCE_DIR}/01-supabase-connection.json`, JSON.stringify(evidence, null, 2));
    return evidence;
  }
  
  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test basic connectivity
    const startTime = Date.now();
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    const queryTime = Date.now() - startTime;
    
    const evidence = {
      testName: 'Supabase Connection',
      timestamp: new Date().toISOString(),
      supabaseUrl: supabaseUrl,
      success: !error,
      queryTimeMs: queryTime,
      error: error?.message
    };
    
    if (!error) {
      console.log('✅ Supabase connection successful');
      console.log(`⚡ Query time: ${queryTime}ms`);
      console.log(`🔗 Connected to: ${supabaseUrl}`);
    } else {
      console.log(`❌ Supabase connection failed: ${error.message}`);
    }
    
    await fs.writeFile(`${EVIDENCE_DIR}/01-supabase-connection.json`, JSON.stringify(evidence, null, 2));
    return evidence;
    
  } catch (error) {
    const evidence = {
      testName: 'Supabase Connection',
      timestamp: new Date().toISOString(),
      success: false,
      error: error.message
    };
    
    console.log(`❌ Connection test failed: ${error.message}`);
    await fs.writeFile(`${EVIDENCE_DIR}/01-supabase-connection.json`, JSON.stringify(evidence, null, 2));
    return evidence;
  }
}

// Test 2: Database Schema Verification
async function testDatabaseSchema() {
  console.log('\n🗄️ Test 2: Database Schema Verification');
  console.log('=======================================');
  
  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    const tables = ['profiles', 'festivals', 'events', 'activities', 'guides', 'tips'];
    const schemaResults = {};
    
    for (const table of tables) {
      try {
        const startTime = Date.now();
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        const queryTime = Date.now() - startTime;
        
        schemaResults[table] = {
          accessible: !error,
          queryTimeMs: queryTime,
          recordCount: data?.length || 0,
          error: error?.message
        };
        
        console.log(`📋 ${table}: ${!error ? '✅' : '❌'} (${queryTime}ms)`);
        if (data && data.length > 0) {
          console.log(`   Sample record keys: ${Object.keys(data[0]).join(', ')}`);
        }
        
      } catch (error) {
        schemaResults[table] = {
          accessible: false,
          error: error.message
        };
        console.log(`❌ ${table}: ${error.message}`);
      }
    }
    
    const evidence = {
      testName: 'Database Schema Verification',
      timestamp: new Date().toISOString(),
      tablesChecked: tables.length,
      accessibleTables: Object.values(schemaResults).filter(r => r.accessible).length,
      schemaResults
    };
    
    console.log(`📊 Schema verification: ${evidence.accessibleTables}/${evidence.tablesChecked} tables accessible`);
    
    await fs.writeFile(`${EVIDENCE_DIR}/02-database-schema.json`, JSON.stringify(evidence, null, 2));
    return evidence;
    
  } catch (error) {
    console.log(`❌ Schema verification failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Test 3: User Registration Flow
async function testUserRegistration() {
  console.log('\n👤 Test 3: User Registration Flow');
  console.log('=================================');
  
  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    console.log(`📧 Test email: ${TEST_USER.email}`);
    console.log(`👤 Test username: ${TEST_USER.username}`);
    
    const startTime = Date.now();
    const { data, error } = await supabase.auth.signUp({
      email: TEST_USER.email,
      password: TEST_USER.password,
      options: {
        data: {
          full_name: TEST_USER.fullName,
          username: TEST_USER.username
        }
      }
    });
    
    const registrationTime = Date.now() - startTime;
    
    const evidence = {
      testName: 'User Registration Flow',
      timestamp: new Date().toISOString(),
      testUser: {
        email: TEST_USER.email,
        username: TEST_USER.username,
        fullName: TEST_USER.fullName
      },
      registrationTimeMs: registrationTime,
      success: !error,
      userId: data?.user?.id,
      emailConfirmed: data?.user?.email_confirmed_at ? true : false,
      userMetadata: data?.user?.user_metadata,
      error: error?.message
    };
    
    if (!error && data.user) {
      console.log('✅ User registration successful');
      console.log(`👤 User ID: ${data.user.id}`);
      console.log(`📧 Email confirmed: ${data.user.email_confirmed_at ? 'Yes' : 'No (confirmation required)'}`);
      console.log(`⚡ Registration time: ${registrationTime}ms`);
      console.log(`📋 User metadata: ${JSON.stringify(data.user.user_metadata)}`);
    } else {
      console.log(`❌ Registration failed: ${error?.message}`);
    }
    
    await fs.writeFile(`${EVIDENCE_DIR}/03-user-registration.json`, JSON.stringify(evidence, null, 2));
    return evidence;
    
  } catch (error) {
    console.log(`❌ Registration test failed: ${error.message}`);
    const evidence = {
      testName: 'User Registration Flow',
      timestamp: new Date().toISOString(),
      success: false,
      error: error.message
    };
    
    await fs.writeFile(`${EVIDENCE_DIR}/03-user-registration.json`, JSON.stringify(evidence, null, 2));
    return evidence;
  }
}

// Test 4: Authentication System Testing
async function testAuthenticationSystem() {
  console.log('\n🔐 Test 4: Authentication System Testing');
  console.log('========================================');
  
  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test 1: Session check
    console.log('🔍 Checking current session...');
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    // Test 2: Password validation (try weak password)
    console.log('🔍 Testing password validation...');
    const { data: weakPassData, error: weakPassError } = await supabase.auth.signUp({
      email: `weak.${Date.now()}@example.com`,
      password: '123'
    });
    
    // Test 3: Invalid login attempt
    console.log('🔍 Testing invalid login handling...');
    const { data: invalidLoginData, error: invalidLoginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'wrongpassword'
    });
    
    const evidence = {
      testName: 'Authentication System Testing',
      timestamp: new Date().toISOString(),
      tests: {
        sessionCheck: {
          success: !sessionError,
          hasActiveSession: !!sessionData.session,
          error: sessionError?.message
        },
        passwordValidation: {
          weakPasswordRejected: !!weakPassError,
          error: weakPassError?.message
        },
        invalidLoginHandling: {
          properlyRejected: !!invalidLoginError,
          error: invalidLoginError?.message
        }
      }
    };
    
    console.log(`✅ Session check: ${!sessionError ? 'Working' : 'Failed'}`);
    console.log(`🔒 Password validation: ${weakPassError ? 'Working (weak password rejected)' : 'Needs improvement'}`);
    console.log(`🚫 Invalid login handling: ${invalidLoginError ? 'Working (properly rejected)' : 'Needs review'}`);
    
    await fs.writeFile(`${EVIDENCE_DIR}/04-authentication-system.json`, JSON.stringify(evidence, null, 2));
    return evidence;
    
  } catch (error) {
    console.log(`❌ Authentication system test failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Test 5: Admin User Verification
async function testAdminUserAccess() {
  console.log('\n🛡️ Test 5: Admin User Access Verification');
  console.log('==========================================');
  
  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Check for existing admin users
    const { data: adminUsers, error } = await supabase
      .from('profiles')
      .select('id, username, role')
      .in('role', ['SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR']);
    
    const evidence = {
      testName: 'Admin User Access Verification',
      timestamp: new Date().toISOString(),
      success: !error,
      adminUsersFound: adminUsers?.length || 0,
      adminUsers: adminUsers?.map(user => ({
        id: user.id,
        username: user.username,
        role: user.role
      })) || [],
      error: error?.message
    };
    
    if (!error) {
      console.log(`✅ Admin user query successful`);
      console.log(`👥 Admin users found: ${adminUsers?.length || 0}`);
      
      if (adminUsers && adminUsers.length > 0) {
        adminUsers.forEach(user => {
          console.log(`   - ${user.username}: ${user.role}`);
        });
      } else {
        console.log('⚠️  No admin users found - may need to create admin accounts');
      }
    } else {
      console.log(`❌ Admin user query failed: ${error.message}`);
    }
    
    await fs.writeFile(`${EVIDENCE_DIR}/05-admin-user-access.json`, JSON.stringify(evidence, null, 2));
    return evidence;
    
  } catch (error) {
    console.log(`❌ Admin access test failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Generate Comprehensive Report
async function generateComprehensiveReport(testResults) {
  console.log('\n📊 COMPREHENSIVE USER FLOW TESTING REPORT');
  console.log('==========================================');
  
  const report = {
    sessionInfo: {
      timestamp: new Date().toISOString(),
      testUser: TEST_USER,
      evidenceDirectory: EVIDENCE_DIR
    },
    testResults: {
      supabaseConnection: testResults.connection?.success || false,
      databaseSchema: testResults.schema?.accessibleTables > 0 || false,
      userRegistration: testResults.registration?.success || false,
      authenticationSystem: testResults.authentication?.tests?.sessionCheck?.success || false,
      adminUserAccess: testResults.adminAccess?.success || false
    },
    summary: {
      totalTests: 5,
      passedTests: 0,
      criticalIssues: [],
      recommendations: []
    }
  };
  
  // Count passed tests and identify issues
  Object.entries(report.testResults).forEach(([testName, passed]) => {
    if (passed) {
      report.summary.passedTests++;
    } else {
      report.summary.criticalIssues.push(testName);
    }
  });
  
  // Generate recommendations
  if (!report.testResults.supabaseConnection) {
    report.summary.recommendations.push('Fix Supabase connection configuration');
  }
  if (!report.testResults.userRegistration) {
    report.summary.recommendations.push('Resolve user registration issues');
  }
  if (testResults.adminAccess?.adminUsersFound === 0) {
    report.summary.recommendations.push('Create admin user accounts for production');
  }
  
  const successRate = (report.summary.passedTests / report.summary.totalTests * 100).toFixed(1);
  
  console.log(`✅ Tests Passed: ${report.summary.passedTests}/${report.summary.totalTests} (${successRate}%)`);
  console.log(`🔍 Database Tables Accessible: ${testResults.schema?.accessibleTables || 0}/${testResults.schema?.tablesChecked || 0}`);
  console.log(`👥 Admin Users Found: ${testResults.adminAccess?.adminUsersFound || 0}`);
  
  if (report.summary.criticalIssues.length > 0) {
    console.log(`⚠️  Critical Issues: ${report.summary.criticalIssues.join(', ')}`);
  }
  
  if (report.summary.recommendations.length > 0) {
    console.log('\n💡 Recommendations:');
    report.summary.recommendations.forEach(rec => {
      console.log(`   - ${rec}`);
    });
  }
  
  await fs.writeFile(`${EVIDENCE_DIR}/comprehensive-user-flow-report.json`, JSON.stringify(report, null, 2));
  
  return report;
}

// Main execution
async function runComprehensiveUserFlowTesting() {
  await ensureEvidenceDir();
  
  const testResults = {};
  
  try {
    testResults.connection = await testSupabaseConnection();
    
    if (testResults.connection.success) {
      testResults.schema = await testDatabaseSchema();
      testResults.registration = await testUserRegistration();
      testResults.authentication = await testAuthenticationSystem();
      testResults.adminAccess = await testAdminUserAccess();
    }
    
    const finalReport = await generateComprehensiveReport(testResults);
    
    console.log('\n🏁 Comprehensive user flow testing completed');
    return finalReport;
    
  } catch (error) {
    console.error('\n💥 User flow testing failed:', error);
    throw error;
  }
}

// Run the comprehensive testing
runComprehensiveUserFlowTesting()
  .then(() => {
    console.log('\n✅ All comprehensive user flow tests completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Comprehensive user flow testing failed:', error);
    process.exit(1);
  });

{"timestamp": "2025-05-30T00:09:39.980Z", "testUser": {"email": "<EMAIL>", "fullName": "Test User"}, "testResults": {"signUp": {"formFilled": true, "submitted": true, "hasSuccess": true, "hasError": false, "currentUrl": "http://localhost:5173/auth"}, "signIn": {"submitted": true, "currentUrl": "http://localhost:5173/auth", "isAuthenticated": true, "hasWelcomeMessage": true, "hasSignOutButton": false}, "dashboard": {"hasWelcomeMessage": false, "hasProfileLink": true, "hasActivitiesLink": true, "hasFamhubLink": true, "hasDiscoverLink": true, "navigationComplete": true}}, "summary": {"signUpWorking": true, "signInWorking": true, "dashboardAccessible": true, "authFlowComplete": true}}
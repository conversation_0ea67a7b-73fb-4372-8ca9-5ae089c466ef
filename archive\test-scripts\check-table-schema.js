/**
 * Check Table Schema
 * 
 * This script checks the actual database schema for events and activities tables.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Checking Table Schema');
console.log('========================');

async function checkTableSchema() {
  try {
    // Check events table schema by trying to insert with different field combinations
    console.log('📅 Testing events table schema...');
    
    // Test 1: Try with start_time/end_time
    try {
      const { data, error } = await supabase
        .from('events')
        .insert([{
          name: 'Test Event',
          description: 'Test description',
          start_time: '2025-08-04T10:00:00Z',
          end_time: '2025-08-04T18:00:00Z',
          location: 'Test Location',
          festival_id: '49239f09-0738-4e3a-9ea0-475f2f780e88'
        }])
        .select();
      
      if (!error) {
        console.log('✅ Events table uses: start_time, end_time');
        // Clean up test data
        await supabase.from('events').delete().eq('name', 'Test Event');
        return { events: 'start_time_end_time' };
      } else {
        console.log('❌ start_time/end_time failed:', error.message);
      }
    } catch (err) {
      console.log('❌ start_time/end_time error:', err.message);
    }
    
    // Test 2: Try with start_date/end_date
    try {
      const { data, error } = await supabase
        .from('events')
        .insert([{
          name: 'Test Event',
          description: 'Test description',
          start_date: '2025-08-04T10:00:00Z',
          end_date: '2025-08-04T18:00:00Z',
          location: 'Test Location',
          festival_id: '49239f09-0738-4e3a-9ea0-475f2f780e88'
        }])
        .select();
      
      if (!error) {
        console.log('✅ Events table uses: start_date, end_date');
        // Clean up test data
        await supabase.from('events').delete().eq('name', 'Test Event');
        return { events: 'start_date_end_date' };
      } else {
        console.log('❌ start_date/end_date failed:', error.message);
      }
    } catch (err) {
      console.log('❌ start_date/end_date error:', err.message);
    }
    
    // Test 3: Try with title instead of name
    try {
      const { data, error } = await supabase
        .from('events')
        .insert([{
          title: 'Test Event',
          description: 'Test description',
          start_date: '2025-08-04T10:00:00Z',
          end_date: '2025-08-04T18:00:00Z',
          location: 'Test Location',
          festival_id: '49239f09-0738-4e3a-9ea0-475f2f780e88'
        }])
        .select();
      
      if (!error) {
        console.log('✅ Events table uses: title, start_date, end_date');
        // Clean up test data
        await supabase.from('events').delete().eq('title', 'Test Event');
        return { events: 'title_start_date_end_date' };
      } else {
        console.log('❌ title/start_date/end_date failed:', error.message);
      }
    } catch (err) {
      console.log('❌ title/start_date/end_date error:', err.message);
    }
    
    console.log('🎯 Testing activities table schema...');
    
    // Test activities table
    try {
      const { data, error } = await supabase
        .from('activities')
        .insert([{
          title: 'Test Activity',
          description: 'Test description',
          type: 'meetup',
          start_date: '2025-08-04T10:00:00Z',
          end_date: '2025-08-04T18:00:00Z',
          location: 'Test Location',
          festival_id: '49239f09-0738-4e3a-9ea0-475f2f780e88'
        }])
        .select();
      
      if (!error) {
        console.log('✅ Activities table schema working');
        // Clean up test data
        await supabase.from('activities').delete().eq('title', 'Test Activity');
        return { activities: 'working' };
      } else {
        console.log('❌ Activities table failed:', error.message);
      }
    } catch (err) {
      console.log('❌ Activities table error:', err.message);
    }
    
    return { events: 'unknown', activities: 'unknown' };
    
  } catch (error) {
    console.error('💥 Schema check failed:', error);
    return { error: error.message };
  }
}

// Main execution
async function main() {
  try {
    console.log(`🕐 Started at: ${new Date().toISOString()}\n`);
    
    const schemaResults = await checkTableSchema();
    
    console.log('\n🎉 SCHEMA CHECK RESULTS');
    console.log('=======================');
    console.log('Events table schema:', schemaResults.events || 'unknown');
    console.log('Activities table schema:', schemaResults.activities || 'unknown');
    
    if (schemaResults.error) {
      console.log('Error:', schemaResults.error);
    }
    
  } catch (error) {
    console.error('💥 Schema check failed:', error);
  }
  
  process.exit(0);
}

main();

/**
 * Unified Data Hooks - Single Source of Truth for Data Fetching
 * 
 * This module provides unified hooks that replace all duplicate data fetching
 * implementations throughout the Festival Family application.
 * 
 * Replaces:
 * - useSupabaseData.ts (lines 36-69)
 * - useActivitiesWithDetails.ts (lines 42-95)
 * - useActivities.ts (lines 13-32)
 * 
 * @module useUnifiedData
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { useState, useEffect, useCallback } from 'react';
import {
  unifiedDataService,
  type Activity,
  type Event,
  type Announcement,
  type Festival,
  type Community,
  type LocalInfo,
  type Tip,
  type Guide,
  type FAQ,
  type ExternalLink,
  type ActivityWithDetails,
  type ActivityFilters,
  type EventFilters
} from '@/lib/data/unified-data-service';

// ============================================================================
// HOOK RETURN TYPES
// ============================================================================

interface UseDataResult<T> {
  data: T;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

interface UseDataArrayResult<T> extends UseDataResult<T[]> {}
interface UseDataSingleResult<T> extends UseDataResult<T | null> {}

// ============================================================================
// ACTIVITIES HOOKS
// ============================================================================

/**
 * Unified Activities Hook
 * Replaces: useSupabaseData.useActivities, useActivitiesWithDetails, useActivities
 */
export function useActivities(filters?: ActivityFilters): UseDataArrayResult<Activity> {
  const [data, setData] = useState<Activity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchActivities = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const activities = await unifiedDataService.getActivities(filters);
      setData(activities);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchActivities();
  }, [fetchActivities]);

  // Real-time subscription for activities
  useEffect(() => {
    const unsubscribe = unifiedDataService.subscribeToActivities((updatedActivities) => {
      // Apply same filters to real-time updates
      if (filters) {
        const filteredActivities = updatedActivities.filter(activity => {
          if (filters.festivalId && activity.festival_id !== filters.festivalId) return false;
          if (filters.type && activity.type !== filters.type) return false;
          if (filters.featured !== undefined && activity.is_featured !== filters.featured) return false;
          if (filters.status && activity.status !== filters.status) return false;
          return true;
        });
        setData(filteredActivities);
      } else {
        setData(updatedActivities);
      }
    });

    return unsubscribe;
  }, [filters]);

  return { 
    data, 
    isLoading, 
    error, 
    refetch: fetchActivities 
  };
}

/**
 * Single Activity Hook with Details
 * Replaces complex activity fetching with details
 */
export function useActivity(id: string): UseDataSingleResult<ActivityWithDetails> {
  const [data, setData] = useState<ActivityWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchActivity = useCallback(async () => {
    if (!id) return;
    
    try {
      setIsLoading(true);
      setError(null);
      const activity = await unifiedDataService.getActivityById(id);
      setData(activity);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setIsLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchActivity();
  }, [fetchActivity]);

  return { 
    data, 
    isLoading, 
    error, 
    refetch: fetchActivity 
  };
}

// ============================================================================
// EVENTS HOOKS
// ============================================================================

/**
 * Unified Events Hook
 */
export function useEvents(filters?: EventFilters): UseDataArrayResult<Event> {
  const [data, setData] = useState<Event[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchEvents = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const events = await unifiedDataService.getEvents(filters);
      setData(events);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchEvents();
  }, [fetchEvents]);

  // Real-time subscription for events
  useEffect(() => {
    const unsubscribe = unifiedDataService.subscribeToEvents((updatedEvents) => {
      // Apply same filters to real-time updates
      if (filters) {
        const filteredEvents = updatedEvents.filter(event => {
          if (filters.festivalId && event.festival_id !== filters.festivalId) return false;
          if (filters.category && event.category !== filters.category) return false;
          if (filters.featured !== undefined && (event as any).is_featured !== filters.featured) return false;
          if (filters.status && event.status !== filters.status) return false;
          return true;
        });
        setData(filteredEvents);
      } else {
        setData(updatedEvents);
      }
    });

    return unsubscribe;
  }, [filters]);

  return { 
    data, 
    isLoading, 
    error, 
    refetch: fetchEvents 
  };
}

/**
 * Single Event Hook
 */
export function useEvent(id: string): UseDataSingleResult<Event> {
  const [data, setData] = useState<Event | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchEvent = useCallback(async () => {
    if (!id) return;
    
    try {
      setIsLoading(true);
      setError(null);
      const event = await unifiedDataService.getEventById(id);
      setData(event);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setIsLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchEvent();
  }, [fetchEvent]);

  return { 
    data, 
    isLoading, 
    error, 
    refetch: fetchEvent 
  };
}

// ============================================================================
// ANNOUNCEMENTS HOOKS
// ============================================================================

/**
 * Unified Announcements Hook
 */
export function useAnnouncements(): UseDataArrayResult<Announcement> {
  const [data, setData] = useState<Announcement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchAnnouncements = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const announcements = await unifiedDataService.getAnnouncements();
      setData(announcements);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchAnnouncements();
  }, [fetchAnnouncements]);

  // Real-time subscription for announcements
  useEffect(() => {
    const unsubscribe = unifiedDataService.subscribeToAnnouncements(setData);
    return unsubscribe;
  }, []);

  return { 
    data, 
    isLoading, 
    error, 
    refetch: fetchAnnouncements 
  };
}

// ============================================================================
// FESTIVALS HOOKS
// ============================================================================

/**
 * Unified Festivals Hook
 * Replaces: useSupabaseData.useFestivals
 */
export function useFestivals(): UseDataArrayResult<Festival> {
  const [data, setData] = useState<Festival[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchFestivals = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const festivals = await unifiedDataService.getFestivals();
      setData(festivals);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchFestivals();
  }, [fetchFestivals]);

  return { 
    data, 
    isLoading, 
    error, 
    refetch: fetchFestivals 
  };
}

// ============================================================================
// REAL-TIME DATA HOOK
// ============================================================================

/**
 * Generic Real-time Data Hook
 * For custom real-time subscriptions
 */
export function useRealTimeData<T>(
  table: 'activities' | 'events' | 'announcements',
  callback: (data: T[]) => void
): void {
  useEffect(() => {
    let unsubscribe: (() => void) | undefined;

    switch (table) {
      case 'activities':
        unsubscribe = unifiedDataService.subscribeToActivities(callback as any);
        break;
      case 'events':
        unsubscribe = unifiedDataService.subscribeToEvents(callback as any);
        break;
      case 'announcements':
        unsubscribe = unifiedDataService.subscribeToAnnouncements(callback as any);
        break;
    }

    return unsubscribe;
  }, [table, callback]);
}

// ============================================================================
// COMMUNITIES HOOKS
// ============================================================================

/**
 * Unified Communities Hook
 */
export function useCommunities(): UseDataArrayResult<Community> {
  const [data, setData] = useState<Community[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchCommunities = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const communities = await unifiedDataService.getCommunities();
      setData(communities);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCommunities();
  }, [fetchCommunities]);

  return {
    data,
    isLoading,
    error,
    refetch: fetchCommunities
  };
}

// ============================================================================
// LOCAL INFO HOOKS
// ============================================================================

/**
 * Unified Local Info Hook
 */
export function useLocalInfo(): UseDataArrayResult<LocalInfo> {
  const [data, setData] = useState<LocalInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchLocalInfo = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const localInfo = await unifiedDataService.getLocalInfo();
      setData(localInfo);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchLocalInfo();
  }, [fetchLocalInfo]);

  return {
    data,
    isLoading,
    error,
    refetch: fetchLocalInfo
  };
}

// ============================================================================
// RESOURCES HOOKS
// ============================================================================

/**
 * Unified Resources Hook (Tips, Guides, FAQs)
 */
export function useResources(): UseDataArrayResult<(Tip | Guide | FAQ) & { type: 'tip' | 'guide' | 'faq' }> {
  const [data, setData] = useState<Array<(Tip | Guide | FAQ) & { type: 'tip' | 'guide' | 'faq' }>>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchResources = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const resources = await unifiedDataService.getResources();
      setData(resources);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchResources();
  }, [fetchResources]);

  return {
    data,
    isLoading,
    error,
    refetch: fetchResources
  };
}

// ============================================================================
// EXTERNAL LINKS HOOKS
// ============================================================================

/**
 * Unified Chat Links Hook
 */
export function useChatLinks(): UseDataArrayResult<ExternalLink> {
  const [data, setData] = useState<ExternalLink[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchChatLinks = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const chatLinks = await unifiedDataService.getChatLinks();
      setData(chatLinks);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchChatLinks();
  }, [fetchChatLinks]);

  return {
    data,
    isLoading,
    error,
    refetch: fetchChatLinks
  };
}

// ============================================================================
// EXPORTS
// ============================================================================

export type {
  UseDataResult,
  UseDataArrayResult,
  UseDataSingleResult,
  Activity,
  Event,
  Announcement,
  Festival,
  Community,
  LocalInfo,
  Tip,
  Guide,
  FAQ,
  ExternalLink,
  ActivityWithDetails,
  ActivityFilters,
  EventFilters
};

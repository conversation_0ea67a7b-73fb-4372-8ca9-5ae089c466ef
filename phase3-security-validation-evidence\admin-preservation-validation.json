{"loginSuccessful": true, "adminDashboardAccess": false, "adminRoutes": [{"route": "/admin", "accessible": true, "loadTime": 1836, "finalUrl": "http://localhost:5173/admin"}, {"route": "/admin/users", "accessible": true, "loadTime": 2021, "finalUrl": "http://localhost:5173/admin/users"}, {"route": "/admin/events", "accessible": true, "loadTime": 2042, "finalUrl": "http://localhost:5173/admin/events"}, {"route": "/admin/festivals", "accessible": true, "loadTime": 2343, "finalUrl": "http://localhost:5173/admin/festivals"}], "performanceMetrics": {"dashboardLoadTime": 1993}, "timestamp": "2025-06-05T23:32:01.796Z", "adminDashboardError": "locator.isVisible: Error: strict mode violation: locator('h1, h2, h3, .admin-content') resolved to 12 elements:\n    1) <h1 class=\"text-xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\">Admin Panel</h1> aka getByRole('heading', { name: 'Admin Panel' })\n    2) <h1 class=\"text-2xl font-bold text-foreground\">Admin Dashboard</h1> aka getByRole('heading', { name: 'Admin Dashboard' })\n    3) <h2 class=\"text-base md:text-lg font-semibold text-card-foreground group-hover:text-primary text-center\">Manage Festivals</h2> aka getByRole('link', { name: 'Manage Festivals' })\n    4) <h2 class=\"text-base md:text-lg font-semibold text-card-foreground group-hover:text-primary text-center\">Manage Events</h2> aka getByRole('link', { name: 'Manage Events' })\n    5) <h2 class=\"text-base md:text-lg font-semibold text-card-foreground group-hover:text-primary text-center\">Manage Activities</h2> aka getByRole('link', { name: 'Manage Activities' })\n    6) <h2 class=\"text-base md:text-lg font-semibold text-card-foreground group-hover:text-primary text-center\">Manage Announcements</h2> aka getByRole('link', { name: 'Manage Announcements' })\n    7) <h2 class=\"text-base md:text-lg font-semibold text-card-foreground group-hover:text-primary text-center\">Manage Users</h2> aka getByRole('link', { name: 'Manage Users' })\n    8) <h2 class=\"text-base md:text-lg font-semibold text-card-foreground group-hover:text-primary text-center\">Manage Guides</h2> aka getByRole('link', { name: 'Manage Guides' })\n    9) <h2 class=\"text-base md:text-lg font-semibold text-card-foreground group-hover:text-primary text-center\">Manage Tips</h2> aka getByRole('link', { name: 'Manage Tips' })\n    10) <h2 class=\"text-base md:text-lg font-semibold text-card-foreground group-hover:text-primary text-center\">Manage FAQs</h2> aka getByRole('link', { name: 'Manage FAQs' })\n    ...\n\nCall log:\n\u001b[2m    - checking visibility of locator('h1, h2, h3, .admin-content')\u001b[22m\n"}
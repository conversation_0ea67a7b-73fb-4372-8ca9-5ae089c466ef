/**
 * Check Current Database Schema
 * 
 * This script analyzes the current database schema to identify
 * what exists vs. what's missing for Smart Group Formation migration.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Current Database Schema Analysis');
console.log('===================================');

// Check if table exists
async function checkTableExists(tableName) {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);
    
    if (error) {
      if (error.message.includes('does not exist')) {
        return { exists: false, accessible: false, error: error.message };
      } else {
        return { exists: true, accessible: false, error: error.message };
      }
    } else {
      return { exists: true, accessible: true, recordCount: data?.length || 0 };
    }
  } catch (err) {
    return { exists: false, accessible: false, error: err.message };
  }
}

// Check if column exists in table
async function checkColumnExists(tableName, columnName) {
  try {
    // Try to select the specific column
    const { data, error } = await supabase
      .from(tableName)
      .select(columnName)
      .limit(1);
    
    if (error) {
      if (error.message.includes('does not exist') || error.message.includes('column')) {
        return { exists: false, error: error.message };
      } else {
        return { exists: true, accessible: false, error: error.message };
      }
    } else {
      return { exists: true, accessible: true };
    }
  } catch (err) {
    return { exists: false, error: err.message };
  }
}

async function main() {
  try {
    console.log(`🕐 Started at: ${new Date().toISOString()}\n`);
    
    const schemaAnalysis = {
      timestamp: new Date().toISOString(),
      coreTablesStatus: {},
      missingDependencies: [],
      readyForSmartGroupFormation: false
    };
    
    // Tables to check
    const tablesToCheck = [
      { name: 'groups', required: true, description: 'Core groups table' },
      { name: 'group_members', required: true, description: 'Group membership (MISSING - causes Smart Group Formation to fail)' },
      { name: 'profiles', required: true, description: 'User profiles' },
      { name: 'activities', required: true, description: 'Activities system' },
      { name: 'festivals', required: true, description: 'Festivals system' },
      { name: 'group_invitations', required: false, description: 'Group invitations (nice to have)' }
    ];
    
    console.log('📋 CHECKING CORE TABLES');
    console.log('------------------------');
    
    for (const table of tablesToCheck) {
      console.log(`🔍 Checking ${table.name} table...`);
      
      const result = await checkTableExists(table.name);
      schemaAnalysis.coreTablesStatus[table.name] = result;
      
      if (result.exists && result.accessible) {
        console.log(`✅ ${table.name}: EXISTS and ACCESSIBLE`);
        if (result.recordCount !== undefined) {
          console.log(`   📊 Records: ${result.recordCount}`);
        }
      } else if (result.exists && !result.accessible) {
        console.log(`⚠️ ${table.name}: EXISTS but ACCESS RESTRICTED`);
        console.log(`   🔒 Error: ${result.error}`);
      } else {
        console.log(`❌ ${table.name}: DOES NOT EXIST`);
        console.log(`   📝 Description: ${table.description}`);
        
        if (table.required) {
          schemaAnalysis.missingDependencies.push({
            type: 'table',
            name: table.name,
            description: table.description,
            impact: 'Smart Group Formation will fail'
          });
        }
      }
    }
    
    console.log('\n📊 CHECKING CRITICAL COLUMNS');
    console.log('-----------------------------');
    
    // Critical columns to check
    const columnsToCheck = [
      { table: 'groups', column: 'is_private', required: true, description: 'Group privacy setting (MISSING - causes Smart Group Formation to fail)' },
      { table: 'groups', column: 'max_members', required: false, description: 'Maximum group members' },
      { table: 'groups', column: 'creator_id', required: true, description: 'Group creator reference' },
      { table: 'groups', column: 'formation_type', required: false, description: 'Smart group formation type' }
    ];
    
    for (const col of columnsToCheck) {
      if (schemaAnalysis.coreTablesStatus[col.table]?.exists) {
        console.log(`🔍 Checking ${col.table}.${col.column} column...`);
        
        const result = await checkColumnExists(col.table, col.column);
        
        if (result.exists && result.accessible) {
          console.log(`✅ ${col.table}.${col.column}: EXISTS and ACCESSIBLE`);
        } else if (result.exists && !result.accessible) {
          console.log(`⚠️ ${col.table}.${col.column}: EXISTS but ACCESS RESTRICTED`);
          console.log(`   🔒 Error: ${result.error}`);
        } else {
          console.log(`❌ ${col.table}.${col.column}: DOES NOT EXIST`);
          console.log(`   📝 Description: ${col.description}`);
          
          if (col.required) {
            schemaAnalysis.missingDependencies.push({
              type: 'column',
              table: col.table,
              column: col.column,
              description: col.description,
              impact: 'Smart Group Formation will fail'
            });
          }
        }
      } else {
        console.log(`⚠️ Skipping ${col.table}.${col.column} - table doesn't exist`);
      }
    }
    
    console.log('\n🧪 TESTING SMART GROUP FORMATION DEPENDENCIES');
    console.log('----------------------------------------------');
    
    // Test specific Smart Group Formation requirements
    const smartGroupDependencies = [
      {
        name: 'group_members table for RLS policies',
        test: async () => {
          const result = await checkTableExists('group_members');
          return result.exists && result.accessible;
        }
      },
      {
        name: 'groups.is_private column for privacy controls',
        test: async () => {
          if (!schemaAnalysis.coreTablesStatus.groups?.exists) return false;
          const result = await checkColumnExists('groups', 'is_private');
          return result.exists && result.accessible;
        }
      },
      {
        name: 'Basic group system functionality',
        test: async () => {
          const groupsOk = schemaAnalysis.coreTablesStatus.groups?.accessible;
          const profilesOk = schemaAnalysis.coreTablesStatus.profiles?.accessible;
          return groupsOk && profilesOk;
        }
      }
    ];
    
    let dependenciesMet = 0;
    const totalDependencies = smartGroupDependencies.length;
    
    for (const dep of smartGroupDependencies) {
      console.log(`🧪 Testing: ${dep.name}...`);
      
      try {
        const result = await dep.test();
        if (result) {
          console.log(`✅ PASSED: ${dep.name}`);
          dependenciesMet++;
        } else {
          console.log(`❌ FAILED: ${dep.name}`);
        }
      } catch (error) {
        console.log(`💥 ERROR: ${dep.name} - ${error.message}`);
      }
    }
    
    schemaAnalysis.readyForSmartGroupFormation = dependenciesMet === totalDependencies;
    
    console.log('\n🎯 SCHEMA ANALYSIS SUMMARY');
    console.log('==========================');
    
    console.log('\n📊 CORE TABLES STATUS:');
    Object.entries(schemaAnalysis.coreTablesStatus).forEach(([table, status]) => {
      const statusText = status.exists && status.accessible ? '✅ READY' : 
                        status.exists ? '⚠️ EXISTS (ACCESS ISSUES)' : '❌ MISSING';
      console.log(`   ${table}: ${statusText}`);
    });
    
    console.log('\n🚨 MISSING DEPENDENCIES:');
    if (schemaAnalysis.missingDependencies.length === 0) {
      console.log('   ✅ No missing dependencies found');
    } else {
      schemaAnalysis.missingDependencies.forEach(dep => {
        if (dep.type === 'table') {
          console.log(`   ❌ TABLE: ${dep.name} - ${dep.description}`);
        } else {
          console.log(`   ❌ COLUMN: ${dep.table}.${dep.column} - ${dep.description}`);
        }
      });
    }
    
    console.log('\n🎯 SMART GROUP FORMATION READINESS:');
    console.log(`   Dependencies Met: ${dependenciesMet}/${totalDependencies}`);
    console.log(`   Status: ${schemaAnalysis.readyForSmartGroupFormation ? '✅ READY' : '❌ NOT READY'}`);
    
    if (!schemaAnalysis.readyForSmartGroupFormation) {
      console.log('\n🔧 REQUIRED ACTIONS:');
      console.log('   1. Apply Group System Foundation migration first');
      console.log('   2. File: supabase/migrations/20250604000006_create_group_system_foundation.sql');
      console.log('   3. Then apply Smart Group Formation migration');
    } else {
      console.log('\n🚀 READY TO PROCEED:');
      console.log('   ✅ All dependencies satisfied');
      console.log('   ✅ Smart Group Formation migration should work');
    }
    
    // Save results
    const resultsDir = 'schema-analysis-results';
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      `${resultsDir}/current-schema-analysis-${Date.now()}.json`,
      JSON.stringify(schemaAnalysis, null, 2)
    );
    
    console.log(`\n📁 Results saved to: ${resultsDir}/current-schema-analysis-${Date.now()}.json`);
    
  } catch (error) {
    console.error('💥 Schema analysis failed:', error);
  }
  
  process.exit(0);
}

main();

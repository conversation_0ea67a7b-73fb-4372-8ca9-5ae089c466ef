import { useState, useCallback } from 'react';

/**
 * Custom hook for managing hamburger menu state across components
 * This allows both desktop and mobile components to control the same sidebar menu
 */
export const useHamburgerMenu = () => {
  const [isHamburgerMenuOpen, setIsHamburgerMenuOpen] = useState(false);

  const toggleHamburgerMenu = useCallback(() => {
    setIsHamburgerMenuOpen(prev => !prev);
  }, []);

  const openHamburgerMenu = useCallback(() => {
    setIsHamburgerMenuOpen(true);
  }, []);

  const closeHamburgerMenu = useCallback(() => {
    setIsHamburgerMenuOpen(false);
  }, []);

  return {
    isHamburgerMenuOpen,
    toggleHamburgerMenu,
    openHamburgerMenu,
    closeHamburgerMenu,
    setIsHamburgerMenuOpen
  };
};

// Global state for hamburger menu (simple approach)
let globalHamburgerMenuState = {
  isOpen: false,
  listeners: new Set<(isOpen: boolean) => void>()
};

/**
 * Global hamburger menu controller
 * This allows any component to control the hamburger menu state
 */
export const globalHamburgerMenu = {
  getState: () => globalHamburgerMenuState.isOpen,
  
  toggle: () => {
    globalHamburgerMenuState.isOpen = !globalHamburgerMenuState.isOpen;
    globalHamburgerMenuState.listeners.forEach(listener => 
      listener(globalHamburgerMenuState.isOpen)
    );
  },
  
  open: () => {
    globalHamburgerMenuState.isOpen = true;
    globalHamburgerMenuState.listeners.forEach(listener => 
      listener(globalHamburgerMenuState.isOpen)
    );
  },
  
  close: () => {
    globalHamburgerMenuState.isOpen = false;
    globalHamburgerMenuState.listeners.forEach(listener => 
      listener(globalHamburgerMenuState.isOpen)
    );
  },
  
  subscribe: (listener: (isOpen: boolean) => void) => {
    globalHamburgerMenuState.listeners.add(listener);
    return () => globalHamburgerMenuState.listeners.delete(listener);
  }
};

/**
 * Hook to use global hamburger menu state
 * This allows components to subscribe to and control the global hamburger menu
 */
export const useGlobalHamburgerMenu = () => {
  const [isOpen, setIsOpen] = useState(globalHamburgerMenu.getState());

  // Subscribe to global state changes
  useState(() => {
    const unsubscribe = globalHamburgerMenu.subscribe(setIsOpen);
    return unsubscribe;
  });

  return {
    isHamburgerMenuOpen: isOpen,
    toggleHamburgerMenu: globalHamburgerMenu.toggle,
    openHamburgerMenu: globalHamburgerMenu.open,
    closeHamburgerMenu: globalHamburgerMenu.close
  };
};

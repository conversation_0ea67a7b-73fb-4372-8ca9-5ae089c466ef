<!DOCTYPE html><html lang="en"><head>
    <script type="module">import { injectIntoGlobalHook } from "/@react-refresh"
injectIntoGlobalHook(window);
window.$RefreshReg$ = () => {};
window.$RefreshSig$ = () => (type) => type;</script>

    <script type="module" src="/@vite/client"></script>

    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/vite.svg">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#0ea5e9">
    <meta name="description" content="Festival Family - Connect with fellow festival-goers">

    <!-- Security Headers -->
    
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    <link rel="apple-touch-icon" href="/pwa-192x192.png">
    <link rel="mask-icon" href="/masked-icon.svg" color="#0ea5e9">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@700&amp;display=swap" rel="stylesheet">
    <title>Festival Family</title>
  <style type="text/css" data-vite-dev-id="C:/Users/<USER>/CascadeProjects/festival-family/node_modules/@fontsource/manrope/400.css">/* manrope-cyrillic-ext-400-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 400;
  src: url(/node_modules/@fontsource/manrope/files/manrope-cyrillic-ext-400-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-cyrillic-ext-400-normal.woff) format('woff');
  unicode-range: U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;
}

/* manrope-cyrillic-400-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 400;
  src: url(/node_modules/@fontsource/manrope/files/manrope-cyrillic-400-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-cyrillic-400-normal.woff) format('woff');
  unicode-range: U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;
}

/* manrope-greek-400-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 400;
  src: url(/node_modules/@fontsource/manrope/files/manrope-greek-400-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-greek-400-normal.woff) format('woff');
  unicode-range: U+0370-0377,U+037A-037F,U+0384-038A,U+038C,U+038E-03A1,U+03A3-03FF;
}

/* manrope-vietnamese-400-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 400;
  src: url(/node_modules/@fontsource/manrope/files/manrope-vietnamese-400-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-vietnamese-400-normal.woff) format('woff');
  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;
}

/* manrope-latin-ext-400-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 400;
  src: url(/node_modules/@fontsource/manrope/files/manrope-latin-ext-400-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-latin-ext-400-normal.woff) format('woff');
  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;
}

/* manrope-latin-400-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 400;
  src: url(/node_modules/@fontsource/manrope/files/manrope-latin-400-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-latin-400-normal.woff) format('woff');
  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;
}</style><style type="text/css" data-vite-dev-id="C:/Users/<USER>/CascadeProjects/festival-family/node_modules/@fontsource/manrope/500.css">/* manrope-cyrillic-ext-500-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 500;
  src: url(/node_modules/@fontsource/manrope/files/manrope-cyrillic-ext-500-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-cyrillic-ext-500-normal.woff) format('woff');
  unicode-range: U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;
}

/* manrope-cyrillic-500-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 500;
  src: url(/node_modules/@fontsource/manrope/files/manrope-cyrillic-500-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-cyrillic-500-normal.woff) format('woff');
  unicode-range: U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;
}

/* manrope-greek-500-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 500;
  src: url(/node_modules/@fontsource/manrope/files/manrope-greek-500-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-greek-500-normal.woff) format('woff');
  unicode-range: U+0370-0377,U+037A-037F,U+0384-038A,U+038C,U+038E-03A1,U+03A3-03FF;
}

/* manrope-vietnamese-500-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 500;
  src: url(/node_modules/@fontsource/manrope/files/manrope-vietnamese-500-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-vietnamese-500-normal.woff) format('woff');
  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;
}

/* manrope-latin-ext-500-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 500;
  src: url(/node_modules/@fontsource/manrope/files/manrope-latin-ext-500-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-latin-ext-500-normal.woff) format('woff');
  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;
}

/* manrope-latin-500-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 500;
  src: url(/node_modules/@fontsource/manrope/files/manrope-latin-500-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-latin-500-normal.woff) format('woff');
  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;
}</style><style type="text/css" data-vite-dev-id="C:/Users/<USER>/CascadeProjects/festival-family/node_modules/@fontsource/manrope/700.css">/* manrope-cyrillic-ext-700-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 700;
  src: url(/node_modules/@fontsource/manrope/files/manrope-cyrillic-ext-700-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-cyrillic-ext-700-normal.woff) format('woff');
  unicode-range: U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;
}

/* manrope-cyrillic-700-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 700;
  src: url(/node_modules/@fontsource/manrope/files/manrope-cyrillic-700-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-cyrillic-700-normal.woff) format('woff');
  unicode-range: U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;
}

/* manrope-greek-700-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 700;
  src: url(/node_modules/@fontsource/manrope/files/manrope-greek-700-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-greek-700-normal.woff) format('woff');
  unicode-range: U+0370-0377,U+037A-037F,U+0384-038A,U+038C,U+038E-03A1,U+03A3-03FF;
}

/* manrope-vietnamese-700-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 700;
  src: url(/node_modules/@fontsource/manrope/files/manrope-vietnamese-700-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-vietnamese-700-normal.woff) format('woff');
  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;
}

/* manrope-latin-ext-700-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 700;
  src: url(/node_modules/@fontsource/manrope/files/manrope-latin-ext-700-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-latin-ext-700-normal.woff) format('woff');
  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;
}

/* manrope-latin-700-normal */
@font-face {
  font-family: 'Manrope';
  font-style: normal;
  font-display: swap;
  font-weight: 700;
  src: url(/node_modules/@fontsource/manrope/files/manrope-latin-700-normal.woff2) format('woff2'), url(/node_modules/@fontsource/manrope/files/manrope-latin-700-normal.woff) format('woff');
  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;
}</style><style type="text/css" data-vite-dev-id="C:/Users/<USER>/CascadeProjects/festival-family/node_modules/@fontsource/outfit/400.css">/* outfit-latin-ext-400-normal */
@font-face {
  font-family: 'Outfit';
  font-style: normal;
  font-display: swap;
  font-weight: 400;
  src: url(/node_modules/@fontsource/outfit/files/outfit-latin-ext-400-normal.woff2) format('woff2'), url(/node_modules/@fontsource/outfit/files/outfit-latin-ext-400-normal.woff) format('woff');
  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;
}

/* outfit-latin-400-normal */
@font-face {
  font-family: 'Outfit';
  font-style: normal;
  font-display: swap;
  font-weight: 400;
  src: url(/node_modules/@fontsource/outfit/files/outfit-latin-400-normal.woff2) format('woff2'), url(/node_modules/@fontsource/outfit/files/outfit-latin-400-normal.woff) format('woff');
  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;
}</style><style type="text/css" data-vite-dev-id="C:/Users/<USER>/CascadeProjects/festival-family/node_modules/@fontsource/outfit/500.css">/* outfit-latin-ext-500-normal */
@font-face {
  font-family: 'Outfit';
  font-style: normal;
  font-display: swap;
  font-weight: 500;
  src: url(/node_modules/@fontsource/outfit/files/outfit-latin-ext-500-normal.woff2) format('woff2'), url(/node_modules/@fontsource/outfit/files/outfit-latin-ext-500-normal.woff) format('woff');
  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;
}

/* outfit-latin-500-normal */
@font-face {
  font-family: 'Outfit';
  font-style: normal;
  font-display: swap;
  font-weight: 500;
  src: url(/node_modules/@fontsource/outfit/files/outfit-latin-500-normal.woff2) format('woff2'), url(/node_modules/@fontsource/outfit/files/outfit-latin-500-normal.woff) format('woff');
  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;
}</style><style type="text/css" data-vite-dev-id="C:/Users/<USER>/CascadeProjects/festival-family/node_modules/@fontsource/outfit/600.css">/* outfit-latin-ext-600-normal */
@font-face {
  font-family: 'Outfit';
  font-style: normal;
  font-display: swap;
  font-weight: 600;
  src: url(/node_modules/@fontsource/outfit/files/outfit-latin-ext-600-normal.woff2) format('woff2'), url(/node_modules/@fontsource/outfit/files/outfit-latin-ext-600-normal.woff) format('woff');
  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;
}

/* outfit-latin-600-normal */
@font-face {
  font-family: 'Outfit';
  font-style: normal;
  font-display: swap;
  font-weight: 600;
  src: url(/node_modules/@fontsource/outfit/files/outfit-latin-600-normal.woff2) format('woff2'), url(/node_modules/@fontsource/outfit/files/outfit-latin-600-normal.woff) format('woff');
  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;
}</style><style type="text/css" data-vite-dev-id="C:/Users/<USER>/CascadeProjects/festival-family/node_modules/@fontsource/outfit/700.css">/* outfit-latin-ext-700-normal */
@font-face {
  font-family: 'Outfit';
  font-style: normal;
  font-display: swap;
  font-weight: 700;
  src: url(/node_modules/@fontsource/outfit/files/outfit-latin-ext-700-normal.woff2) format('woff2'), url(/node_modules/@fontsource/outfit/files/outfit-latin-ext-700-normal.woff) format('woff');
  unicode-range: U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF;
}

/* outfit-latin-700-normal */
@font-face {
  font-family: 'Outfit';
  font-style: normal;
  font-display: swap;
  font-weight: 700;
  src: url(/node_modules/@fontsource/outfit/files/outfit-latin-700-normal.woff2) format('woff2'), url(/node_modules/@fontsource/outfit/files/outfit-latin-700-normal.woff) format('woff');
  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;
}</style><style type="text/css" data-vite-dev-id="C:/Users/<USER>/CascadeProjects/festival-family/src/index.css">/* Font imports are now handled in the main.tsx file */

/* Import local styles */

/* Container utilities */

.container-base {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding: 1.5rem;
  max-width: 100%;
}

@media (min-width: 640px) {
  .container-base {
    max-width: 640px;
    padding: 2rem;
  }
}

@media (min-width: 768px) {
  .container-base {
    max-width: 768px;
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  .container-base {
    max-width: 1024px;
    padding: 2rem;
  }
}

@media (min-width: 1280px) {
  .container-base {
    max-width: 1280px;
    padding: 2rem;
  }
}

/* Ensure content doesn't touch the edges on very large screens */

@media (min-width: 1536px) {
  .container-base {
    max-width: 1536px;
    padding: 2rem;
  }
}

/* Content spacing utilities */

.content-spacing {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

@media (min-width: 768px) {
  .content-spacing {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }
}

/* Navigation spacing utilities */

.nav-spacing {
  height: 4rem; /* 64px - matches the height of the navigation */
}

@media (min-width: 768px) {
  .nav-spacing {
    height: 5rem; /* 80px for desktop */
  }
}

/* Import Tailwind */

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/* ! tailwindcss v3.4.15 | MIT License | https://tailwindcss.com */

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

:root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

.dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }

* {
  border-color: rgb(255 255 255 / 0.1);
}

html {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
  --tw-gradient-from: #1A0B2E var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(26 11 46 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: rgb(106 13 173 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(106 13 173 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
  --tw-gradient-to: rgb(255 20 147 / 0.2) var(--tw-gradient-to-position);
  min-height: 100vh;
  color: rgb(255 255 255 / 0.9);
    font-family: 'Manrope Variable', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-size: 200% 200%;
    animation: gradient 15s ease infinite;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Outfit Variable', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-weight: 700;
  }

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 2rem;
  padding-left: 2rem;
}

@media (min-width: 1400px) {

  .container {
    max-width: 1400px;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.\!visible {
  visibility: visible !important;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.inset-x-0 {
  left: 0px;
  right: 0px;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.-bottom-1 {
  bottom: -0.25rem;
}

.-bottom-6 {
  bottom: -1.5rem;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-1 {
  bottom: 0.25rem;
}

.bottom-20 {
  bottom: 5rem;
}

.bottom-24 {
  bottom: 6rem;
}

.bottom-4 {
  bottom: 1rem;
}

.left-0 {
  left: 0px;
}

.left-1 {
  left: 0.25rem;
}

.left-1\/2 {
  left: 50%;
}

.left-2 {
  left: 0.5rem;
}

.left-3 {
  left: 0.75rem;
}

.left-4 {
  left: 1rem;
}

.left-\[50\%\] {
  left: 50%;
}

.right-0 {
  right: 0px;
}

.right-1 {
  right: 0.25rem;
}

.right-16 {
  right: 4rem;
}

.right-2 {
  right: 0.5rem;
}

.right-4 {
  right: 1rem;
}

.right-6 {
  right: 1.5rem;
}

.top-0 {
  top: 0px;
}

.top-1\/2 {
  top: 50%;
}

.top-2 {
  top: 0.5rem;
}

.top-4 {
  top: 1rem;
}

.top-\[50\%\] {
  top: 50%;
}

.isolate {
  isolation: isolate;
}

.-z-10 {
  z-index: -10;
}

.z-10 {
  z-index: 10;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.col-span-full {
  grid-column: 1 / -1;
}

.m-4 {
  margin: 1rem;
}

.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.-mr-2\.5 {
  margin-right: -0.625rem;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-auto {
  margin-left: auto;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-1\.5 {
  margin-right: 0.375rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mt-0 {
  margin-top: 0px;
}

.mt-0\.5 {
  margin-top: 0.125rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.h-0\.5 {
  height: 0.125rem;
}

.h-1 {
  height: 0.25rem;
}

.h-1\.5 {
  height: 0.375rem;
}

.h-10 {
  height: 2.5rem;
}

.h-11 {
  height: 2.75rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-16 {
  height: 4rem;
}

.h-2 {
  height: 0.5rem;
}

.h-24 {
  height: 6rem;
}

.h-3 {
  height: 0.75rem;
}

.h-3\.5 {
  height: 0.875rem;
}

.h-32 {
  height: 8rem;
}

.h-4 {
  height: 1rem;
}

.h-40 {
  height: 10rem;
}

.h-48 {
  height: 12rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-64 {
  height: 16rem;
}

.h-7 {
  height: 1.75rem;
}

.h-8 {
  height: 2rem;
}

.h-9 {
  height: 2.25rem;
}

.h-\[400px\] {
  height: 400px;
}

.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}

.h-full {
  height: 100%;
}

.h-px {
  height: 1px;
}

.max-h-40 {
  max-height: 10rem;
}

.max-h-64 {
  max-height: 16rem;
}

.max-h-96 {
  max-height: 24rem;
}

.min-h-\[200px\] {
  min-height: 200px;
}

.min-h-\[44px\] {
  min-height: 44px;
}

.min-h-\[48px\] {
  min-height: 48px;
}

.min-h-\[80px\] {
  min-height: 80px;
}

.min-h-screen {
  min-height: 100vh;
}

.w-1 {
  width: 0.25rem;
}

.w-1\.5 {
  width: 0.375rem;
}

.w-1\/2 {
  width: 50%;
}

.w-1\/3 {
  width: 33.333333%;
}

.w-10 {
  width: 2.5rem;
}

.w-11 {
  width: 2.75rem;
}

.w-12 {
  width: 3rem;
}

.w-14 {
  width: 3.5rem;
}

.w-16 {
  width: 4rem;
}

.w-2 {
  width: 0.5rem;
}

.w-20 {
  width: 5rem;
}

.w-24 {
  width: 6rem;
}

.w-3 {
  width: 0.75rem;
}

.w-3\.5 {
  width: 0.875rem;
}

.w-3\/4 {
  width: 75%;
}

.w-32 {
  width: 8rem;
}

.w-4 {
  width: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.w-5\/6 {
  width: 83.333333%;
}

.w-6 {
  width: 1.5rem;
}

.w-64 {
  width: 16rem;
}

.w-7 {
  width: 1.75rem;
}

.w-8 {
  width: 2rem;
}

.w-80 {
  width: 20rem;
}

.w-9 {
  width: 2.25rem;
}

.w-\[400px\] {
  width: 400px;
}

.w-auto {
  width: auto;
}

.w-full {
  width: 100%;
}

.min-w-0 {
  min-width: 0px;
}

.min-w-\[44px\] {
  min-width: 44px;
}

.min-w-\[48px\] {
  min-width: 48px;
}

.min-w-\[8rem\] {
  min-width: 8rem;
}

.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}

.min-w-full {
  min-width: 100%;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-\[1536px\] {
  max-width: 1536px;
}

.max-w-\[70\%\] {
  max-width: 70%;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-none {
  max-width: none;
}

.max-w-screen-xl {
  max-width: 1280px;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xs {
  max-width: 20rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

.grow {
  flex-grow: 1;
}

.caption-bottom {
  caption-side: bottom;
}

.border-collapse {
  border-collapse: collapse;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-7 {
  --tw-translate-x: 1.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-1\/2 {
  --tw-translate-y: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes bounce {

  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes pulse {

  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.cursor-default {
  cursor: default;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.touch-none {
  touch-action: none;
}

.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.resize {
  resize: both;
}

.list-inside {
  list-style-position: inside;
}

.list-decimal {
  list-style-type: decimal;
}

.list-disc {
  list-style-type: disc;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.-space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}

.space-y-12 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}

.self-start {
  align-self: flex-start;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-none {
  border-radius: 0px;
}

.rounded-sm {
  border-radius: 0.125rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-l-xl {
  border-top-left-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}

.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.rounded-bl-none {
  border-bottom-left-radius: 0px;
}

.rounded-br-none {
  border-bottom-right-radius: 0px;
}

.border {
  border-width: 1px;
}

.border-2 {
  border-width: 2px;
}

.border-4 {
  border-width: 4px;
}

.border-x-0 {
  border-left-width: 0px;
  border-right-width: 0px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-l {
  border-left-width: 1px;
}

.border-l-2 {
  border-left-width: 2px;
}

.border-r {
  border-right-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-0 {
  border-top-width: 0px;
}

.border-t-2 {
  border-top-width: 2px;
}

.border-amber-500\/20 {
  border-color: rgb(245 158 11 / 0.2);
}

.border-black\/10 {
  border-color: rgb(0 0 0 / 0.1);
}

.border-blue-500\/20 {
  border-color: rgb(59 130 246 / 0.2);
}

.border-blue-500\/30 {
  border-color: rgb(59 130 246 / 0.3);
}

.border-current {
  border-color: currentColor;
}

.border-electric-violet {
  --tw-border-opacity: 1;
  border-color: rgb(106 13 173 / var(--tw-border-opacity, 1));
}

.border-electric-violet\/20 {
  border-color: rgb(106 13 173 / 0.2);
}

.border-electric-violet\/50 {
  border-color: rgb(106 13 173 / 0.5);
}

.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}

.border-green-500\/20 {
  border-color: rgb(34 197 94 / 0.2);
}

.border-green-500\/30 {
  border-color: rgb(34 197 94 / 0.3);
}

.border-midnight-purple {
  --tw-border-opacity: 1;
  border-color: rgb(26 11 46 / var(--tw-border-opacity, 1));
}

.border-primary {
  --tw-border-opacity: 1;
  border-color: rgb(106 13 173 / var(--tw-border-opacity, 1));
}

.border-primary\/20 {
  border-color: rgb(106 13 173 / 0.2);
}

.border-purple-400 {
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}

.border-purple-500 {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.border-purple-500\/20 {
  border-color: rgb(168 85 247 / 0.2);
}

.border-purple-500\/30 {
  border-color: rgb(168 85 247 / 0.3);
}

.border-purple-500\/50 {
  border-color: rgb(168 85 247 / 0.5);
}

.border-purple-700 {
  --tw-border-opacity: 1;
  border-color: rgb(126 34 206 / var(--tw-border-opacity, 1));
}

.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}

.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}

.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.border-red-500\/20 {
  border-color: rgb(239 68 68 / 0.2);
}

.border-red-500\/30 {
  border-color: rgb(239 68 68 / 0.3);
}

.border-red-500\/50 {
  border-color: rgb(239 68 68 / 0.5);
}

.border-transparent {
  border-color: transparent;
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-white\/10 {
  border-color: rgb(255 255 255 / 0.1);
}

.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}

.border-white\/30 {
  border-color: rgb(255 255 255 / 0.3);
}

.border-yellow-500\/20 {
  border-color: rgb(234 179 8 / 0.2);
}

.border-yellow-500\/30 {
  border-color: rgb(234 179 8 / 0.3);
}

.border-yellow-500\/50 {
  border-color: rgb(234 179 8 / 0.5);
}

.border-t-transparent {
  border-top-color: transparent;
}

.border-t-white {
  --tw-border-opacity: 1;
  border-top-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.bg-\[\#1DA1F2\] {
  --tw-bg-opacity: 1;
  background-color: rgb(29 161 242 / var(--tw-bg-opacity, 1));
}

.bg-\[\#4a1d96\] {
  --tw-bg-opacity: 1;
  background-color: rgb(74 29 150 / var(--tw-bg-opacity, 1));
}

.bg-amber-500\/10 {
  background-color: rgb(245 158 11 / 0.1);
}

.bg-aqua-blue {
  --tw-bg-opacity: 1;
  background-color: rgb(56 189 248 / var(--tw-bg-opacity, 1));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-black\/20 {
  background-color: rgb(0 0 0 / 0.2);
}

.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}

.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}

.bg-blue-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}

.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.bg-blue-500\/10 {
  background-color: rgb(59 130 246 / 0.1);
}

.bg-blue-500\/20 {
  background-color: rgb(59 130 246 / 0.2);
}

.bg-blue-500\/90 {
  background-color: rgb(59 130 246 / 0.9);
}

.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.bg-blue-900\/70 {
  background-color: rgb(30 58 138 / 0.7);
}

.bg-electric-violet {
  --tw-bg-opacity: 1;
  background-color: rgb(106 13 173 / var(--tw-bg-opacity, 1));
}

.bg-electric-violet\/20 {
  background-color: rgb(106 13 173 / 0.2);
}

.bg-electric-violet\/30 {
  background-color: rgb(106 13 173 / 0.3);
}

.bg-electric-violet\/50 {
  background-color: rgb(106 13 173 / 0.5);
}

.bg-electric-violet\/90 {
  background-color: rgb(106 13 173 / 0.9);
}

.bg-emerald-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}

.bg-gray-500\/20 {
  background-color: rgb(107 114 128 / 0.2);
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.bg-gray-800\/50 {
  background-color: rgb(31 41 55 / 0.5);
}

.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.bg-green-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(134 239 172 / var(--tw-bg-opacity, 1));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.bg-green-500\/10 {
  background-color: rgb(34 197 94 / 0.1);
}

.bg-green-500\/20 {
  background-color: rgb(34 197 94 / 0.2);
}

.bg-green-500\/90 {
  background-color: rgb(34 197 94 / 0.9);
}

.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.bg-indigo-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));
}

.bg-midnight-purple {
  --tw-bg-opacity: 1;
  background-color: rgb(26 11 46 / var(--tw-bg-opacity, 1));
}

.bg-midnight-purple\/30 {
  background-color: rgb(26 11 46 / 0.3);
}

.bg-midnight-purple\/80 {
  background-color: rgb(26 11 46 / 0.8);
}

.bg-midnight-purple\/90 {
  background-color: rgb(26 11 46 / 0.9);
}

.bg-midnight-purple\/95 {
  background-color: rgb(26 11 46 / 0.95);
}

.bg-neon-pink {
  --tw-bg-opacity: 1;
  background-color: rgb(255 20 147 / var(--tw-bg-opacity, 1));
}

.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}

.bg-orange-500\/20 {
  background-color: rgb(249 115 22 / 0.2);
}

.bg-pink-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));
}

.bg-pink-500\/20 {
  background-color: rgb(236 72 153 / 0.2);
}

.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(106 13 173 / var(--tw-bg-opacity, 1));
}

.bg-primary\/10 {
  background-color: rgb(106 13 173 / 0.1);
}

.bg-primary\/20 {
  background-color: rgb(106 13 173 / 0.2);
}

.bg-purple-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(216 180 254 / var(--tw-bg-opacity, 1));
}

.bg-purple-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(192 132 252 / var(--tw-bg-opacity, 1));
}

.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}

.bg-purple-500\/10 {
  background-color: rgb(168 85 247 / 0.1);
}

.bg-purple-500\/20 {
  background-color: rgb(168 85 247 / 0.2);
}

.bg-purple-500\/30 {
  background-color: rgb(168 85 247 / 0.3);
}

.bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}

.bg-purple-600\/80 {
  background-color: rgb(147 51 234 / 0.8);
}

.bg-purple-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}

.bg-purple-700\/30 {
  background-color: rgb(126 34 206 / 0.3);
}

.bg-purple-700\/50 {
  background-color: rgb(126 34 206 / 0.5);
}

.bg-purple-900\/70 {
  background-color: rgb(88 28 135 / 0.7);
}

.bg-purple-900\/90 {
  background-color: rgb(88 28 135 / 0.9);
}

.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-red-500\/10 {
  background-color: rgb(239 68 68 / 0.1);
}

.bg-red-500\/20 {
  background-color: rgb(239 68 68 / 0.2);
}

.bg-red-500\/90 {
  background-color: rgb(239 68 68 / 0.9);
}

.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.bg-red-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.bg-secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(56 189 248 / var(--tw-bg-opacity, 1));
}

.bg-slate-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}

.bg-slate-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
}

.bg-teal-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity, 1));
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}

.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}

.bg-white\/5 {
  background-color: rgb(255 255 255 / 0.05);
}

.bg-white\/50 {
  background-color: rgb(255 255 255 / 0.5);
}

.bg-white\/80 {
  background-color: rgb(255 255 255 / 0.8);
}

.bg-yellow-400\/10 {
  background-color: rgb(250 204 21 / 0.1);
}

.bg-yellow-400\/20 {
  background-color: rgb(250 204 21 / 0.2);
}

.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.bg-yellow-500\/10 {
  background-color: rgb(234 179 8 / 0.1);
}

.bg-yellow-500\/20 {
  background-color: rgb(234 179 8 / 0.2);
}

.bg-yellow-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}

.bg-opacity-10 {
  --tw-bg-opacity: 0.1;
}

.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}

.bg-opacity-90 {
  --tw-bg-opacity: 0.9;
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.from-amber-500 {
  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-aqua-blue {
  --tw-gradient-from: #38BDF8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(56 189 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black\/10 {
  --tw-gradient-from: rgb(0 0 0 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black\/70 {
  --tw-gradient-from: rgb(0 0 0 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black\/80 {
  --tw-gradient-from: rgb(0 0 0 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-500 {
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-electric-violet {
  --tw-gradient-from: #6A0DAD var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(106 13 173 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-electric-violet\/30 {
  --tw-gradient-from: rgb(106 13 173 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(106 13 173 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-500 {
  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-indigo-900 {
  --tw-gradient-from: #312e81 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(49 46 129 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-midnight-purple {
  --tw-gradient-from: #1A0B2E var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(26 11 46 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-pink-500 {
  --tw-gradient-from: #ec4899 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(236 72 153 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-400 {
  --tw-gradient-from: #c084fc var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-500 {
  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-600\/20 {
  --tw-gradient-from: rgb(147 51 234 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-900 {
  --tw-gradient-from: #581c87 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-900\/50 {
  --tw-gradient-from: rgb(88 28 135 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white {
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white\/10 {
  --tw-gradient-from: rgb(255 255 255 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white\/20 {
  --tw-gradient-from: rgb(255 255 255 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-yellow-400 {
  --tw-gradient-from: #facc15 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-electric-violet {
  --tw-gradient-to: rgb(106 13 173 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #6A0DAD var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-neon-pink {
  --tw-gradient-to: rgb(255 20 147 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #FF1493 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-purple-900 {
  --tw-gradient-to: rgb(88 28 135 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #581c87 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-white\/10 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.to-amber-500 {
  --tw-gradient-to: #f59e0b var(--tw-gradient-to-position);
}

.to-aqua-blue {
  --tw-gradient-to: #38BDF8 var(--tw-gradient-to-position);
}

.to-black\/5 {
  --tw-gradient-to: rgb(0 0 0 / 0.05) var(--tw-gradient-to-position);
}

.to-blue-500 {
  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);
}

.to-bright-yellow {
  --tw-gradient-to: #FFD740 var(--tw-gradient-to-position);
}

.to-cyan-500 {
  --tw-gradient-to: #06b6d4 var(--tw-gradient-to-position);
}

.to-deep-indigo {
  --tw-gradient-to: #4B0082 var(--tw-gradient-to-position);
}

.to-electric-violet {
  --tw-gradient-to: #6A0DAD var(--tw-gradient-to-position);
}

.to-electric-violet\/50 {
  --tw-gradient-to: rgb(106 13 173 / 0.5) var(--tw-gradient-to-position);
}

.to-emerald-500 {
  --tw-gradient-to: #10b981 var(--tw-gradient-to-position);
}

.to-indigo-900\/50 {
  --tw-gradient-to: rgb(49 46 129 / 0.5) var(--tw-gradient-to-position);
}

.to-midnight-purple\/50 {
  --tw-gradient-to: rgb(26 11 46 / 0.5) var(--tw-gradient-to-position);
}

.to-neon-pink {
  --tw-gradient-to: #FF1493 var(--tw-gradient-to-position);
}

.to-orange-500 {
  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);
}

.to-pink-400 {
  --tw-gradient-to: #f472b6 var(--tw-gradient-to-position);
}

.to-pink-500 {
  --tw-gradient-to: #ec4899 var(--tw-gradient-to-position);
}

.to-pink-600 {
  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);
}

.to-pink-600\/20 {
  --tw-gradient-to: rgb(219 39 119 / 0.2) var(--tw-gradient-to-position);
}

.to-pink-900 {
  --tw-gradient-to: #831843 var(--tw-gradient-to-position);
}

.to-purple-600 {
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}

.to-purple-800 {
  --tw-gradient-to: #6b21a8 var(--tw-gradient-to-position);
}

.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.to-white\/10 {
  --tw-gradient-to: rgb(255 255 255 / 0.1) var(--tw-gradient-to-position);
}

.to-white\/5 {
  --tw-gradient-to: rgb(255 255 255 / 0.05) var(--tw-gradient-to-position);
}

.to-white\/80 {
  --tw-gradient-to: rgb(255 255 255 / 0.8) var(--tw-gradient-to-position);
}

.bg-clip-text {
  -webkit-background-clip: text;
          background-clip: text;
}

.fill-current {
  fill: currentColor;
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.p-0 {
  padding: 0px;
}

.p-1 {
  padding: 0.25rem;
}

.p-12 {
  padding: 3rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-2\.5 {
  padding: 0.625rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.pb-16 {
  padding-bottom: 4rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-20 {
  padding-bottom: 5rem;
}

.pb-24 {
  padding-bottom: 6rem;
}

.pl-10 {
  padding-left: 2.5rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pl-5 {
  padding-left: 1.25rem;
}

.pl-8 {
  padding-left: 2rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pt-0 {
  padding-top: 0px;
}

.pt-1 {
  padding-top: 0.25rem;
}

.pt-16 {
  padding-top: 4rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.align-middle {
  vertical-align: middle;
}

.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-8xl {
  font-size: 6rem;
  line-height: 1;
}

.text-\[0\.8rem\] {
  font-size: 0.8rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.italic {
  font-style: italic;
}

.leading-none {
  line-height: 1;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

.tracking-widest {
  letter-spacing: 0.1em;
}

.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.text-electric-violet {
  --tw-text-opacity: 1;
  color: rgb(106 13 173 / var(--tw-text-opacity, 1));
}

.text-gray-200\/70 {
  color: rgb(229 231 235 / 0.7);
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.text-green-300 {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}

.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.text-orange-400 {
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}

.text-pink-400 {
  --tw-text-opacity: 1;
  color: rgb(244 114 182 / var(--tw-text-opacity, 1));
}

.text-primary {
  --tw-text-opacity: 1;
  color: rgb(106 13 173 / var(--tw-text-opacity, 1));
}

.text-purple-300 {
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}

.text-purple-400 {
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}

.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}

.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}

.text-purple-900 {
  --tw-text-opacity: 1;
  color: rgb(88 28 135 / var(--tw-text-opacity, 1));
}

.text-red-200 {
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}

.text-red-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.text-slate-50 {
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity, 1));
}

.text-slate-500 {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}

.text-slate-900 {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}

.text-slate-gray {
  --tw-text-opacity: 1;
  color: rgb(66 66 66 / var(--tw-text-opacity, 1));
}

.text-transparent {
  color: transparent;
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-white\/40 {
  color: rgb(255 255 255 / 0.4);
}

.text-white\/50 {
  color: rgb(255 255 255 / 0.5);
}

.text-white\/60 {
  color: rgb(255 255 255 / 0.6);
}

.text-white\/70 {
  color: rgb(255 255 255 / 0.7);
}

.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}

.text-white\/90 {
  color: rgb(255 255 255 / 0.9);
}

.text-yellow-200\/80 {
  color: rgb(254 240 138 / 0.8);
}

.text-yellow-300 {
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}

.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}

.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}

.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}

.underline {
  text-decoration-line: underline;
}

.underline-offset-4 {
  text-underline-offset: 4px;
}

.placeholder-white\/50::-moz-placeholder {
  color: rgb(255 255 255 / 0.5);
}

.placeholder-white\/50::placeholder {
  color: rgb(255 255 255 / 0.5);
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-60 {
  opacity: 0.6;
}

.opacity-70 {
  opacity: 0.7;
}

.opacity-90 {
  opacity: 0.9;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0_0_15px_rgba\(124\2c 58\2c 237\2c 0\.5\)\] {
  --tw-shadow: 0 0 15px rgba(124,58,237,0.5);
  --tw-shadow-colored: 0 0 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0_0_15px_rgba\(250\2c 204\2c 21\2c 0\.3\)\] {
  --tw-shadow: 0 0 15px rgba(250,204,21,0.3);
  --tw-shadow-colored: 0 0 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-inner {
  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline {
  outline-style: solid;
}

.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-electric-violet {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(106 13 173 / var(--tw-ring-opacity, 1));
}

.ring-offset-2 {
  --tw-ring-offset-width: 2px;
}

.ring-offset-black {
  --tw-ring-offset-color: #000;
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-md {
  --tw-blur: blur(12px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-sm {
  --tw-blur: blur(4px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-lg {
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-xl {
  --tw-backdrop-blur: blur(24px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-100 {
  transition-duration: 100ms;
}

.duration-150 {
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes enter {

  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {

  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

.duration-100 {
  animation-duration: 100ms;
}

.duration-150 {
  animation-duration: 150ms;
}

.duration-200 {
  animation-duration: 200ms;
}

.duration-300 {
  animation-duration: 300ms;
}

.duration-500 {
  animation-duration: 500ms;
}

.ease-in-out {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Text truncation */

/* Responsive text sizes */

/* Responsive touch targets */

/* Prevent layout shift */

/* Responsive spacing */

/* Responsive grid */

/* Responsive container */

/* Prevent content overflow */

/* Mobile-first button sizing */

/* Safe touch areas */

/* Gradient animation */

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Touch target sizes for mobile */

.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Glassmorphism effects */

:root {
  --glass-bg-color: rgba(255, 255, 255, 0.1);
}

.glass {
  background-color: rgb(255 255 255 / 0.1);
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  border-width: 1px;
  border-color: rgb(255 255 255 / 0.2);
}

.glass-dark {
  background-color: rgb(0 0 0 / 0.4);
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  border-width: 1px;
  border-color: rgb(255 255 255 / 0.1);
}

.file\:border-0::file-selector-button {
  border-width: 0px;
}

.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}

.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.file\:font-medium::file-selector-button {
  font-weight: 500;
}

.placeholder\:text-white\/60::-moz-placeholder {
  color: rgb(255 255 255 / 0.6);
}

.placeholder\:text-white\/60::placeholder {
  color: rgb(255 255 255 / 0.6);
}

.focus-within\:relative:focus-within {
  position: relative;
}

.focus-within\:z-20:focus-within {
  z-index: 20;
}

.hover\:-translate-y-1:hover {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.02\]:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes pulse {

  50% {
    opacity: .5;
  }
}

.hover\:animate-pulse:hover {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.hover\:border-electric-violet\/30:hover {
  border-color: rgb(106 13 173 / 0.3);
}

.hover\:border-white\/40:hover {
  border-color: rgb(255 255 255 / 0.4);
}

.hover\:bg-\[\#1a91da\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(26 145 218 / var(--tw-bg-opacity, 1));
}

.hover\:bg-accent:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 20 147 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-500\/30:hover {
  background-color: rgb(59 130 246 / 0.3);
}

.hover\:bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-electric-violet:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(106 13 173 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-500\/30:hover {
  background-color: rgb(107 114 128 / 0.3);
}

.hover\:bg-gray-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-500\/30:hover {
  background-color: rgb(34 197 94 / 0.3);
}

.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-500\/30:hover {
  background-color: rgb(249 115 22 / 0.3);
}

.hover\:bg-primary-dark:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(90 14 142 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary\/30:hover {
  background-color: rgb(106 13 173 / 0.3);
}

.hover\:bg-primary\/80:hover {
  background-color: rgb(106 13 173 / 0.8);
}

.hover\:bg-primary\/90:hover {
  background-color: rgb(106 13 173 / 0.9);
}

.hover\:bg-purple-500\/20:hover {
  background-color: rgb(168 85 247 / 0.2);
}

.hover\:bg-purple-500\/30:hover {
  background-color: rgb(168 85 247 / 0.3);
}

.hover\:bg-purple-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-500\/30:hover {
  background-color: rgb(239 68 68 / 0.3);
}

.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-700\/50:hover {
  background-color: rgb(185 28 28 / 0.5);
}

.hover\:bg-secondary\/80:hover {
  background-color: rgb(56 189 248 / 0.8);
}

.hover\:bg-secondary\/90:hover {
  background-color: rgb(56 189 248 / 0.9);
}

.hover\:bg-slate-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
}

.hover\:bg-white\/10:hover {
  background-color: rgb(255 255 255 / 0.1);
}

.hover\:bg-white\/15:hover {
  background-color: rgb(255 255 255 / 0.15);
}

.hover\:bg-white\/20:hover {
  background-color: rgb(255 255 255 / 0.2);
}

.hover\:bg-white\/30:hover {
  background-color: rgb(255 255 255 / 0.3);
}

.hover\:bg-white\/5:hover {
  background-color: rgb(255 255 255 / 0.05);
}

.hover\:bg-yellow-400\/20:hover {
  background-color: rgb(250 204 21 / 0.2);
}

.hover\:bg-yellow-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-500\/30:hover {
  background-color: rgb(234 179 8 / 0.3);
}

.hover\:from-pink-600:hover {
  --tw-gradient-from: #db2777 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 39 119 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:to-purple-700:hover {
  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);
}

.hover\:text-purple-300:hover {
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-50:hover {
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:shadow-glow-primary:hover {
  --tw-shadow: 0 0 15px rgba(106, 13, 173, 0.3);
  --tw-shadow-colored: 0 0 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:border-aqua-blue:focus {
  --tw-border-opacity: 1;
  border-color: rgb(56 189 248 / var(--tw-border-opacity, 1));
}

.focus\:border-purple-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.focus\:bg-accent:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(255 20 147 / var(--tw-bg-opacity, 1));
}

.focus\:bg-purple-600:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}

.focus\:bg-slate-900:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
}

.focus\:text-slate-50:focus {
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity, 1));
}

.focus\:text-white:focus {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-aqua-blue:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(56 189 248 / var(--tw-ring-opacity, 1));
}

.focus\:ring-aqua-blue\/50:focus {
  --tw-ring-color: rgb(56 189 248 / 0.5);
}

.focus\:ring-electric-violet:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(106 13 173 / var(--tw-ring-opacity, 1));
}

.focus\:ring-primary:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(106 13 173 / var(--tw-ring-opacity, 1));
}

.focus\:ring-purple-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
}

.focus\:ring-white:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}

.focus\:ring-opacity-50:focus {
  --tw-ring-opacity: 0.5;
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus\:ring-offset-black\/80:focus {
  --tw-ring-offset-color: rgb(0 0 0 / 0.8);
}

.focus\:ring-offset-transparent:focus {
  --tw-ring-offset-color: transparent;
}

.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.active\:scale-95:active {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.active\:scale-\[0\.98\]:active {
  --tw-scale-x: 0.98;
  --tw-scale-y: 0.98;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:bg-blue-500\/30 {
  background-color: rgb(59 130 246 / 0.3);
}

.group:hover .group-hover\:bg-green-500\/30 {
  background-color: rgb(34 197 94 / 0.3);
}

.group:hover .group-hover\:bg-pink-500\/30 {
  background-color: rgb(236 72 153 / 0.3);
}

.group:hover .group-hover\:bg-purple-500\/30 {
  background-color: rgb(168 85 247 / 0.3);
}

.group:hover .group-hover\:text-primary {
  --tw-text-opacity: 1;
  color: rgb(106 13 173 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}

.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: 0.7;
}

.aria-selected\:bg-slate-100[aria-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}

.aria-selected\:text-slate-900[aria-selected="true"] {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}

.aria-selected\:opacity-100[aria-selected="true"] {
  opacity: 1;
}

.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}

.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=checked\]\:translate-x-5[data-state="checked"] {
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"] {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=active\]\:bg-purple-700[data-state="active"] {
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}

.data-\[state\=active\]\:bg-white\/10[data-state="active"] {
  background-color: rgb(255 255 255 / 0.1);
}

.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
  --tw-bg-opacity: 1;
  background-color: rgb(106 13 173 / var(--tw-bg-opacity, 1));
}

.data-\[state\=open\]\:bg-accent[data-state="open"] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 20 147 / var(--tw-bg-opacity, 1));
}

.data-\[state\=open\]\:bg-secondary[data-state="open"] {
  --tw-bg-opacity: 1;
  background-color: rgb(56 189 248 / var(--tw-bg-opacity, 1));
}

.data-\[state\=active\]\:text-white[data-state="active"] {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: 0.5;
}

.data-\[state\=active\]\:shadow-sm[data-state="active"] {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  transition-duration: 300ms;
}

.data-\[state\=open\]\:duration-500[data-state="open"] {
  transition-duration: 500ms;
}

.data-\[state\=open\]\:animate-in[data-state="open"] {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}

.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}

.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}

.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: -0.5rem;
}

.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: 0.5rem;
}

.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: -0.5rem;
}

.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: 0.5rem;
}

.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
  --tw-exit-translate-y: 100%;
}

.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
  --tw-exit-translate-x: -100%;
}

.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
  --tw-exit-translate-x: -50%;
}

.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
  --tw-exit-translate-x: 100%;
}

.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
  --tw-exit-translate-y: -100%;
}

.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"] {
  --tw-exit-translate-y: -48%;
}

.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
  --tw-enter-translate-y: 100%;
}

.data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
  --tw-enter-translate-x: -100%;
}

.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
  --tw-enter-translate-x: -50%;
}

.data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
  --tw-enter-translate-x: 100%;
}

.data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
  --tw-enter-translate-y: -100%;
}

.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"] {
  --tw-enter-translate-y: -48%;
}

.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  animation-duration: 300ms;
}

.data-\[state\=open\]\:duration-500[data-state="open"] {
  animation-duration: 500ms;
}

@media (prefers-reduced-motion: reduce) {

  .motion-reduce\:animate-none {
    animation: none;
  }

  .motion-reduce\:transition-none {
    transition-property: none;
  }

  .motion-reduce\:hover\:scale-100:hover {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .motion-reduce\:active\:scale-100:active {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
}

.dark\:border-yellow-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(202 138 4 / var(--tw-border-opacity, 1));
}

.dark\:bg-yellow-900\/30:is(.dark *) {
  background-color: rgb(113 63 18 / 0.3);
}

.dark\:text-soft-gray:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(242 242 242 / var(--tw-text-opacity, 1));
}

.dark\:text-white:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

@media (min-width: 640px) {

  .sm\:mr-2 {
    margin-right: 0.5rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:w-\[180px\] {
    width: 180px;
  }

  .sm\:w-\[540px\] {
    width: 540px;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:max-w-\[425px\] {
    max-width: 425px;
  }

  .sm\:max-w-sm {
    max-width: 24rem;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:gap-6 {
    gap: 1.5rem;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .sm\:rounded-lg {
    border-radius: 0.5rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 768px) {

  .md\:m-6 {
    margin: 1.5rem;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:w-1\/3 {
    width: 33.333333%;
  }

  .md\:w-80 {
    width: 20rem;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:gap-6 {
    gap: 1.5rem;
  }

  .md\:p-12 {
    padding: 3rem;
  }

  .md\:p-4 {
    padding: 1rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:pb-0 {
    padding-bottom: 0px;
  }

  .md\:text-left {
    text-align: left;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:p-6 {
    padding: 1.5rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.\[\&\:has\(\>\.day-range-end\)\]\:rounded-r-md:has(>.day-range-end) {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.\[\&\:has\(\>\.day-range-start\)\]\:rounded-l-md:has(>.day-range-start) {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.\[\&\:has\(\[aria-selected\]\)\]\:rounded-md:has([aria-selected]) {
  border-radius: 0.375rem;
}

.\[\&\:has\(\[aria-selected\]\)\]\:bg-slate-100:has([aria-selected]) {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}

.first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:has([aria-selected]):first-child {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:has([aria-selected]):last-child {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role=checkbox]) {
  padding-right: 0px;
}

.\[\&\>span\]\:line-clamp-1>span {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.\[\&\>svg\+div\]\:translate-y-\[-3px\]>svg+div {
  --tw-translate-y: -3px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&\>svg\]\:absolute>svg {
  position: absolute;
}

.\[\&\>svg\]\:left-4>svg {
  left: 1rem;
}

.\[\&\>svg\]\:top-4>svg {
  top: 1rem;
}

.\[\&\>svg\]\:text-yellow-700>svg {
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}

.dark\:\[\&\>svg\]\:text-yellow-600>svg:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}

.\[\&\>svg\~\*\]\:pl-7>svg~* {
  padding-left: 1.75rem;
}

.\[\&\>tr\]\:last\:border-b-0:last-child>tr {
  border-bottom-width: 0px;
}

.\[\&_p\]\:leading-relaxed p {
  line-height: 1.625;
}

.\[\&_tr\:last-child\]\:border-0 tr:last-child {
  border-width: 0px;
}

.\[\&_tr\]\:border-b tr {
  border-bottom-width: 1px;
}
</style><style id="_goober"> @keyframes go2264125279{from{transform:scale(0) rotate(45deg);opacity:0;}to{transform:scale(1) rotate(45deg);opacity:1;}}@keyframes go3020080000{from{transform:scale(0);opacity:0;}to{transform:scale(1);opacity:1;}}@keyframes go463499852{from{transform:scale(0) rotate(90deg);opacity:0;}to{transform:scale(1) rotate(90deg);opacity:1;}}@keyframes go1268368563{from{transform:rotate(0deg);}to{transform:rotate(360deg);}}@keyframes go1310225428{from{transform:scale(0) rotate(45deg);opacity:0;}to{transform:scale(1) rotate(45deg);opacity:1;}}@keyframes go651618207{0%{height:0;width:0;opacity:0;}40%{height:0;width:6px;opacity:1;}100%{opacity:1;height:10px;}}@keyframes go901347462{from{transform:scale(0.6);opacity:0.4;}to{transform:scale(1);opacity:1;}}.go4109123758{z-index:9999;}.go4109123758 > *{pointer-events:auto;}.go3489369143{z-index:100000;position:fixed;padding:4px;text-align:left;display:flex;align-items:center;justify-content:center;border-radius:9999px;box-shadow:0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);overflow:hidden;}.go3489369143 div{position:absolute;top:-8px;left:-8px;right:-8px;bottom:-8px;border-radius:9999px;filter:blur(6px) saturate(1.2) contrast(1.1);}.go3489369143 div svg{position:absolute;width:100%;height:100%;}.go3489369143:focus-within{outline-offset:2px;outline:3px solid #039855;}.go3489369143 button{position:relative;z-index:1;padding:0;border-radius:9999px;background-color:transparent;border:none;height:40px;display:flex;width:40px;overflow:hidden;cursor:pointer;outline:none;}.go3489369143 button svg{position:absolute;width:100%;height:100%;}.go2812612974{position:fixed;z-index:9999;display:flex;gap:calc(var(--tsqd-font-size) * 0.125);}.go2812612974 *{box-sizing:border-box;text-transform:none;}.go2812612974 *::-webkit-scrollbar{width:7px;}.go2812612974 *::-webkit-scrollbar-track{background:transparent;}.go2812612974 *::-webkit-scrollbar-thumb{background:#414962;}.go2812612974 *::-webkit-scrollbar-thumb:hover{background:#394056;}.go3213103248{z-index:9999;display:flex;height:100%;gap:calc(var(--tsqd-font-size) * 0.125);}.go3213103248 *{box-sizing:border-box;text-transform:none;}.go3213103248 *::-webkit-scrollbar{width:7px;}.go3213103248 *::-webkit-scrollbar-track{background:transparent;}.go3213103248 *::-webkit-scrollbar-thumb{background:#414962;}.go3213103248 *::-webkit-scrollbar-thumb:hover{background:#394056;}.go1754112896{bottom:12px;right:12px;}.go1942079657{bottom:12px;left:12px;}.go2560641923{top:12px;left:12px;}.go4099359618{top:12px;right:12px;}.go613855381{position:relative;}.go540873609{top:0;right:0;left:0;max-height:90%;min-height:calc(var(--tsqd-font-size) * 3.5);border-bottom:#394056 1px solid;}.go3487837329{bottom:0;right:0;left:0;max-height:90%;min-height:calc(var(--tsqd-font-size) * 3.5);border-top:#394056 1px solid;}.go2240694395{bottom:0;right:0;top:0;border-left:#394056 1px solid;max-width:90%;}.go4063640663{bottom:0;left:0;top:0;border-right:#394056 1px solid;max-width:90%;}.go2885158556{position:absolute;cursor:pointer;z-index:5;display:flex;align-items:center;justify-content:center;outline:none;background-color:#191c24;}.go2885158556:hover{background-color:#292e3d;}.go2885158556:focus-visible{outline:2px solid #1570EF;}.go2885158556 svg{color:#98a2b3;width:calc(var(--tsqd-font-size) * 0.5);height:calc(var(--tsqd-font-size) * 0.5);}.go4210254915{bottom:0;right:calc(var(--tsqd-font-size) * 0.5);transform:translate(0, 100%);border-right:#394056 1px solid;border-left:#394056 1px solid;border-top:none;border-bottom:#394056 1px solid;border-radius:0px 0px calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25);padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.375);}.go4210254915::after{content:' ';position:absolute;bottom:100%;left:-calc(var(--tsqd-font-size) * 0.625);height:calc(var(--tsqd-font-size) * 0.375);width:calc(100% + calc(var(--tsqd-font-size) * 1.25));}.go4210254915 svg{transform:rotate(180deg);}.go1302489688{top:0;right:calc(var(--tsqd-font-size) * 0.5);transform:translate(0, -100%);border-right:#394056 1px solid;border-left:#394056 1px solid;border-top:#394056 1px solid;border-bottom:none;border-radius:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25) 0px 0px;padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.375);}.go1302489688::after{content:' ';position:absolute;top:100%;left:-calc(var(--tsqd-font-size) * 0.625);height:calc(var(--tsqd-font-size) * 0.375);width:calc(100% + calc(var(--tsqd-font-size) * 1.25));}.go2158810208{bottom:calc(var(--tsqd-font-size) * 0.5);left:0;transform:translate(-100%, 0);border-right:none;border-left:#394056 1px solid;border-top:#394056 1px solid;border-bottom:#394056 1px solid;border-radius:calc(var(--tsqd-font-size) * 0.25) 0px 0px calc(var(--tsqd-font-size) * 0.25);padding:calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.25);}.go2158810208::after{content:' ';position:absolute;left:100%;height:calc(100% + calc(var(--tsqd-font-size) * 1.25));width:calc(var(--tsqd-font-size) * 0.375);}.go2158810208 svg{transform:rotate(-90deg);}.go1366919888{bottom:calc(var(--tsqd-font-size) * 0.5);right:0;transform:translate(100%, 0);border-left:none;border-right:#394056 1px solid;border-top:#394056 1px solid;border-bottom:#394056 1px solid;border-radius:0px calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25) 0px;padding:calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.125);}.go1366919888::after{content:' ';position:absolute;right:100%;height:calc(100% + calc(var(--tsqd-font-size) * 1.25));width:calc(var(--tsqd-font-size) * 0.375);}.go1366919888 svg{transform:rotate(90deg);}.go2506729803{flex:1 1 700px;background-color:#191c24;display:flex;flex-direction:column;}.go2506729803 *{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;}.go774879075{position:absolute;transition:background-color 0.125s ease;z-index:4;}.go774879075:hover{background-color:#9B8AFBe5;}.go1191431590{bottom:0;width:100%;height:3px;cursor:ns-resize;}.go253692464{top:0;width:100%;height:3px;cursor:ns-resize;}.go1064895163{left:0;width:3px;height:100%;cursor:ew-resize;}.go2194780694{right:0;width:3px;height:100%;cursor:ew-resize;}.go1245051397{display:flex;justify-content:space-between;align-items:center;padding:calc(var(--tsqd-font-size) * 0.5) calc(var(--tsqd-font-size) * 0.625);gap:calc(var(--tsqd-font-size) * 0.625);border-bottom:#292e3d 1px solid;}.go1245051397 > button{padding:0;background:transparent;border:none;display:flex;gap:calc(var(--tsqd-font-size) * 0.125);flex-direction:column;}.go857308786{display:flex;gap:calc(var(--tsqd-font-size) * 0.75);align-items:center;}.go1936323176{cursor:pointer;display:flex;flex-direction:column;background-color:transparent;border:none;gap:calc(var(--tsqd-font-size) * 0.125);padding:0px;}.go1936323176:hover{opacity:0.7;}.go1936323176:focus-visible{outline-offset:4px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go3203190455{font-size:var(--tsqd-font-size);font-weight:700;line-height:calc(var(--tsqd-font-size) * 1);white-space:nowrap;color:#d0d5dd;}.go2826607642{font-weight:600;font-size:calc(var(--tsqd-font-size) * 0.75);background:linear-gradient( to right, #dd524b, #e9a03b );background-clip:text;-webkit-background-clip:text;line-height:1;-webkit-text-fill-color:transparent;white-space:nowrap;}.go1745108753{display:flex;gap:calc(var(--tsqd-font-size) * 0.5);height:min-content;}.go2789881399{display:flex;gap:calc(var(--tsqd-font-size) * 0.375);box-sizing:border-box;height:calc(var(--tsqd-font-size) * 1.625);background:#292e3d;color:#d0d5dd;border-radius:calc(var(--tsqd-font-size) * 0.25);font-size:calc(var(--tsqd-font-size) * 0.875);padding:calc(var(--tsqd-font-size) * 0.25);padding-left:calc(var(--tsqd-font-size) * 0.375);align-items:center;font-weight:500;border:1px solid transparent;user-select:none;position:relative;}.go2789881399:focus-visible{outline-offset:2px;outline:2px solid #1849A9;}.go1270626771{font-size:calc(var(--tsqd-font-size) * 0.75);}.go2647401337{font-size:calc(var(--tsqd-font-size) * 0.75);padding:0 5px;display:flex;align-items:center;justify-content:center;color:#98a2b3;background-color:#394056;border-radius:2px;font-variant-numeric:tabular-nums;height:calc(var(--tsqd-font-size) * 1.125);}.go701307223{position:absolute;z-index:1;background-color:#292e3d;top:100%;left:50%;transform:translate(-50%, calc(calc(var(--tsqd-font-size) * 0.5)));padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.5);border-radius:calc(var(--tsqd-font-size) * 0.25);font-size:calc(var(--tsqd-font-size) * 0.75);border:1px solid #475467;color:#d0d5dd;}.go701307223::before{top:0px;content:' ';display:block;left:50%;transform:translate(-50%, -100%);position:absolute;border-color:transparent transparent #475467 transparent;border-style:solid;border-width:7px;}.go701307223::after{top:0px;content:' ';display:block;left:50%;transform:translate(-50%, calc(-100% + 2px));position:absolute;border-color:transparent transparent #292e3d transparent;border-style:solid;border-width:7px;}.go903258897{display:flex;gap:calc(var(--tsqd-font-size) * 0.5);}.go903258897 > button{cursor:pointer;padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.5);border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#313749;border:1px solid #414962;color:#d0d5dd;font-size:calc(var(--tsqd-font-size) * 0.75);display:flex;align-items:center;line-height:calc(var(--tsqd-font-size) * 1.25);gap:calc(var(--tsqd-font-size) * 0.375);max-width:160px;}.go903258897 > button:focus-visible{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go903258897 > button svg{width:calc(var(--tsqd-font-size) * 0.75);height:calc(var(--tsqd-font-size) * 0.75);color:#98a2b3;}.go2738029867{padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.5);border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#313749;display:flex;box-sizing:content-box;align-items:center;gap:calc(var(--tsqd-font-size) * 0.375);max-width:160px;min-width:100px;border:1px solid #414962;height:min-content;color:#98a2b3;}.go2738029867 > svg{width:calc(var(--tsqd-font-size) * 0.75);height:calc(var(--tsqd-font-size) * 0.75);}.go2738029867 input{font-size:calc(var(--tsqd-font-size) * 0.75);width:100%;background-color:#313749;border:none;padding:0;line-height:calc(var(--tsqd-font-size) * 1.25);color:#d0d5dd;}.go2738029867 input::placeholder{color:#d0d5dd;}.go2738029867 input:focus{outline:none;}.go2738029867:focus-within{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go4264856030{padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.5);border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#313749;display:flex;align-items:center;gap:calc(var(--tsqd-font-size) * 0.375);box-sizing:content-box;max-width:160px;border:1px solid #414962;height:min-content;}.go4264856030 > svg{color:#98a2b3;width:calc(var(--tsqd-font-size) * 0.5);height:calc(var(--tsqd-font-size) * 0.5);}.go4264856030 > select{appearance:none;color:#d0d5dd;min-width:100px;line-height:calc(var(--tsqd-font-size) * 1.25);font-size:calc(var(--tsqd-font-size) * 0.75);background-color:#313749;border:none;}.go4264856030 > select:focus{outline:none;}.go4264856030:focus-within{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go441294505{display:flex;gap:calc(var(--tsqd-font-size) * 0.5);}.go3814664482{border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#313749;border:1px solid #414962;width:calc(var(--tsqd-font-size) * 1.625);height:calc(var(--tsqd-font-size) * 1.625);justify-content:center;display:flex;align-items:center;gap:calc(var(--tsqd-font-size) * 0.375);max-width:160px;cursor:pointer;padding:0;}.go3814664482:hover{background-color:#292e3d;}.go3814664482 svg{color:#d0d5dd;width:calc(var(--tsqd-font-size) * 0.75);height:calc(var(--tsqd-font-size) * 0.75);}.go3814664482:focus-visible{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go848580748 svg{stroke:#F79009;fill:#F79009;}.go3932029643{flex:1;overflow-y:auto;}.go3932029643 > div{display:flex;flex-direction:column;}.go2242848476{display:flex;align-items:center;padding:0;border:none;cursor:pointer;color:#d0d5dd;background-color:#191c24;line-height:1;}.go2242848476:focus{outline:none;}.go2242848476:focus-visible{outline-offset:-2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go2242848476:hover .tsqd-query-hash{background-color:#212530;}.go2242848476 .tsqd-query-observer-count{padding:0 calc(var(--tsqd-font-size) * 0.25);user-select:none;min-width:calc(var(--tsqd-font-size) * 1.625);align-self:stretch;display:flex;align-items:center;justify-content:center;font-size:calc(var(--tsqd-font-size) * 0.75);font-weight:500;border-bottom-width:1px;border-bottom-style:solid;border-bottom:1px solid #191c24;}.go2242848476 .tsqd-query-hash{user-select:text;font-size:calc(var(--tsqd-font-size) * 0.75);display:flex;align-items:center;min-height:calc(var(--tsqd-font-size) * 1.5);flex:1;padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.5);font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;border-bottom:1px solid #313749;text-align:left;text-overflow:clip;word-break:break-word;}.go2242848476 .tsqd-query-disabled-indicator{align-self:stretch;display:flex;align-items:center;padding:0 calc(var(--tsqd-font-size) * 0.5);color:#d0d5dd;background-color:#212530;border-bottom:1px solid #313749;font-size:calc(var(--tsqd-font-size) * 0.75);}.go3691623036{background-color:#292e3d;}.go2655854486{flex:1 1 700px;background-color:#191c24;color:#d0d5dd;font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;display:flex;flex-direction:column;overflow-y:auto;text-align:left;}.go1223780339{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;position:sticky;top:0;z-index:2;background-color:#212530;padding:calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.5);font-weight:500;font-size:calc(var(--tsqd-font-size) * 0.75);line-height:calc(var(--tsqd-font-size) * 1);text-align:left;}.go2709625642{margin:calc(var(--tsqd-font-size) * 0.375) 0px calc(var(--tsqd-font-size) * 0.5) 0px;}.go2709625642 > div{display:flex;align-items:stretch;padding:0 calc(var(--tsqd-font-size) * 0.5);line-height:calc(var(--tsqd-font-size) * 1.25);justify-content:space-between;}.go2709625642 > div > span{font-size:calc(var(--tsqd-font-size) * 0.75);}.go2709625642 > div > span:nth-child(2){font-variant-numeric:tabular-nums;}.go2709625642 > div:first-child{margin-bottom:calc(var(--tsqd-font-size) * 0.375);}.go2709625642 code{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;margin:0;font-size:calc(var(--tsqd-font-size) * 0.75);line-height:calc(var(--tsqd-font-size) * 1);}.go2709625642 pre{margin:0;display:flex;align-items:center;}.go564315114{border:1px solid #414962;border-radius:calc(var(--tsqd-font-size) * 0.25);font-weight:500;padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.625);}.go3392938368{flex-wrap:wrap;margin:calc(var(--tsqd-font-size) * 0.5) 0px calc(var(--tsqd-font-size) * 0.5) 0px;display:flex;gap:calc(var(--tsqd-font-size) * 0.5);padding:0px calc(var(--tsqd-font-size) * 0.5);}.go3392938368 > button{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;font-size:calc(var(--tsqd-font-size) * 0.75);padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.5);display:flex;border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#212530;border:1px solid #313749;align-items:center;gap:calc(var(--tsqd-font-size) * 0.5);font-weight:500;line-height:calc(var(--tsqd-font-size) * 1);cursor:pointer;}.go3392938368 > button:focus-visible{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go3392938368 > button:hover{background-color:#292e3d;}.go3392938368 > button:disabled{opacity:0.6;cursor:not-allowed;}.go3392938368 > button > span{width:calc(var(--tsqd-font-size) * 0.375);height:calc(var(--tsqd-font-size) * 0.375);border-radius:9999px;}.go601952022{font-size:calc(var(--tsqd-font-size) * 0.75);padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.5);display:flex;border-radius:calc(var(--tsqd-font-size) * 0.25);overflow:hidden;background-color:#212530;border:1px solid #313749;align-items:center;gap:calc(var(--tsqd-font-size) * 0.5);font-weight:500;line-height:calc(var(--tsqd-font-size) * 1.25);color:#f87171;cursor:pointer;position:relative;}.go601952022:hover{background-color:#292e3d;}.go601952022 > span{width:calc(var(--tsqd-font-size) * 0.375);height:calc(var(--tsqd-font-size) * 0.375);border-radius:9999px;}.go601952022:focus-within{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go601952022 select{position:absolute;top:0;left:0;width:100%;height:100%;appearance:none;background-color:transparent;border:none;color:transparent;outline:none;}.go601952022 svg path{stroke:#f87171;}.go601952022 svg{width:calc(var(--tsqd-font-size) * 0.5);height:calc(var(--tsqd-font-size) * 0.5);}.go510199209{display:flex;flex-direction:column;gap:calc(var(--tsqd-font-size) * 0.125);border-radius:calc(var(--tsqd-font-size) * 0.25);border:1px solid #344054;background-color:#212530;font-size:calc(var(--tsqd-font-size) * 0.75);color:#d0d5dd;z-index:99999;min-width:120px;padding:calc(var(--tsqd-font-size) * 0.125);}.go510199209 *{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;}.go1686117193{display:flex;align-items:center;justify-content:space-between;border-radius:calc(var(--tsqd-font-size) * 0.125);padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25);cursor:pointer;background-color:transparent;border:none;color:#d0d5dd;}.go1686117193 svg{color:#98a2b3;transform:rotate(-90deg);width:calc(var(--tsqd-font-size) * 0.5);height:calc(var(--tsqd-font-size) * 0.5);}.go1686117193:hover{background-color:#292e3d;}.go1686117193:focus-visible{outline-offset:2px;outline:2px solid #1849A9;}.go1686117193.data-disabled{opacity:0.6;cursor:not-allowed;}.go3851724898{padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25);font-weight:500;border-bottom:1px solid #313749;color:#98a2b3;font-size:calc(var(--tsqd-font-size) * 0.75);}.go1847177295{display:flex;align-items:center;justify-content:space-between;color:#d0d5dd;font-size:calc(var(--tsqd-font-size) * 0.75);border-radius:calc(var(--tsqd-font-size) * 0.125);padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25);cursor:pointer;background-color:transparent;border:none;}.go1847177295 svg{color:#98a2b3;}.go1847177295:hover{background-color:#292e3d;}.go1847177295:focus-visible{outline-offset:2px;outline:2px solid #1849A9;}.go2085547053{background-color:#3E1C96;color:#BDB4FE;}.go2085547053 svg{color:#BDB4FE;}.go2085547053:hover{background-color:#3E1C96;}.go1417314958{border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#212530;border:1px solid #414962;display:flex;padding:0;font-size:calc(var(--tsqd-font-size) * 0.75);color:#d0d5dd;overflow:hidden;}.go1417314958:has(:focus-visible){outline:2px solid #1849A9;}.go1417314958 .tsqd-radio-toggle{opacity:0.5;display:flex;}.go1417314958 .tsqd-radio-toggle label{display:flex;align-items:center;cursor:pointer;line-height:calc(var(--tsqd-font-size) * 1.5);}.go1417314958 .tsqd-radio-toggle label:hover{background-color:#292e3d;}.go1417314958 > [data-checked]{opacity:1;background-color:#313749;}.go1417314958 > [data-checked] label:hover{background-color:#313749;}.go1417314958 .tsqd-radio-toggle:first-child{border-right:1px solid #414962;}.go1417314958 .tsqd-radio-toggle:first-child label{padding:0 calc(var(--tsqd-font-size) * 0.375) 0 calc(var(--tsqd-font-size) * 0.5);}.go1417314958 .tsqd-radio-toggle:nth-child(2) label{padding:0 calc(var(--tsqd-font-size) * 0.5) 0 calc(var(--tsqd-font-size) * 0.375);}.go643647742{padding:calc(var(--tsqd-font-size) * 0.5);}.go643647742 > [data-error='true']{outline:2px solid #991b1b;outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);}.go3827170696{width:100%;max-height:500px;font-family:'Fira Code', monospace;font-size:calc(var(--tsqd-font-size) * 0.75);border-radius:calc(var(--tsqd-font-size) * 0.25);field-sizing:content;padding:calc(var(--tsqd-font-size) * 0.5);background-color:#111318;color:#f2f4f7;border:1px solid #344054;resize:none;}.go3827170696:focus{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go3542865594{display:flex;justify-content:space-between;gap:calc(var(--tsqd-font-size) * 0.5);align-items:center;padding-top:calc(var(--tsqd-font-size) * 0.25);font-size:calc(var(--tsqd-font-size) * 0.75);}.go818103473{color:#ef4444;}.go1125328280{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;font-size:calc(var(--tsqd-font-size) * 0.75);padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.5);display:flex;border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#212530;border:1px solid #313749;align-items:center;gap:calc(var(--tsqd-font-size) * 0.5);font-weight:500;line-height:calc(var(--tsqd-font-size) * 1);cursor:pointer;}.go1125328280:focus-visible{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go1125328280:hover{background-color:#292e3d;}.go1125328280:disabled{opacity:0.6;cursor:not-allowed;}.go2420793703 .tsqd-panel-transition-exit-active, .go2420793703 .tsqd-panel-transition-enter-active{transition:opacity 0.3s, transform 0.3s;}.go2420793703 .tsqd-panel-transition-exit-to, .go2420793703 .tsqd-panel-transition-enter{transform:translateY(var(--tsqd-panel-height));}.go2420793703 .tsqd-button-transition-exit-active, .go2420793703 .tsqd-button-transition-enter-active{transition:opacity 0.3s, transform 0.3s;opacity:1;}.go2420793703 .tsqd-button-transition-exit-to, .go2420793703 .tsqd-button-transition-enter{transform:translateY(72px);opacity:0;}.go2125864219{position:fixed;z-index:9999;display:flex;gap:calc(var(--tsqd-font-size) * 0.125);}.go2125864219 *{box-sizing:border-box;text-transform:none;}.go2125864219 *::-webkit-scrollbar{width:7px;}.go2125864219 *::-webkit-scrollbar-track{background:transparent;}.go2125864219 *::-webkit-scrollbar-thumb{background:#d0d5dd;}.go2125864219 *::-webkit-scrollbar-thumb:hover{background:#98a2b3;}.go2526354493{z-index:9999;display:flex;height:100%;gap:calc(var(--tsqd-font-size) * 0.125);}.go2526354493 *{box-sizing:border-box;text-transform:none;}.go2526354493 *::-webkit-scrollbar{width:7px;}.go2526354493 *::-webkit-scrollbar-track{background:transparent;}.go2526354493 *::-webkit-scrollbar-thumb{background:#d0d5dd;}.go2526354493 *::-webkit-scrollbar-thumb:hover{background:#98a2b3;}.go4052627871{top:0;right:0;left:0;max-height:90%;min-height:calc(var(--tsqd-font-size) * 3.5);border-bottom:#98a2b3 1px solid;}.go2704624295{bottom:0;right:0;left:0;max-height:90%;min-height:calc(var(--tsqd-font-size) * 3.5);border-top:#98a2b3 1px solid;}.go669081409{bottom:0;right:0;top:0;border-left:#98a2b3 1px solid;max-width:90%;}.go2492027677{bottom:0;left:0;top:0;border-right:#98a2b3 1px solid;max-width:90%;}.go2751925965{position:absolute;cursor:pointer;z-index:5;display:flex;align-items:center;justify-content:center;outline:none;background-color:#f9fafb;}.go2751925965:hover{background-color:#eaecf0;}.go2751925965:focus-visible{outline:2px solid #1570EF;}.go2751925965 svg{color:#475467;width:calc(var(--tsqd-font-size) * 0.5);height:calc(var(--tsqd-font-size) * 0.5);}.go1186580341{bottom:0;right:calc(var(--tsqd-font-size) * 0.5);transform:translate(0, 100%);border-right:#98a2b3 1px solid;border-left:#98a2b3 1px solid;border-top:none;border-bottom:#98a2b3 1px solid;border-radius:0px 0px calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25);padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.375);}.go1186580341::after{content:' ';position:absolute;bottom:100%;left:-calc(var(--tsqd-font-size) * 0.625);height:calc(var(--tsqd-font-size) * 0.375);width:calc(100% + calc(var(--tsqd-font-size) * 1.25));}.go1186580341 svg{transform:rotate(180deg);}.go3576318346{top:0;right:calc(var(--tsqd-font-size) * 0.5);transform:translate(0, -100%);border-right:#98a2b3 1px solid;border-left:#98a2b3 1px solid;border-top:#98a2b3 1px solid;border-bottom:none;border-radius:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25) 0px 0px;padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.375);}.go3576318346::after{content:' ';position:absolute;top:100%;left:-calc(var(--tsqd-font-size) * 0.625);height:calc(var(--tsqd-font-size) * 0.375);width:calc(100% + calc(var(--tsqd-font-size) * 1.25));}.go3176406346{bottom:calc(var(--tsqd-font-size) * 0.5);left:0;transform:translate(-100%, 0);border-right:none;border-left:#98a2b3 1px solid;border-top:#98a2b3 1px solid;border-bottom:#98a2b3 1px solid;border-radius:calc(var(--tsqd-font-size) * 0.25) 0px 0px calc(var(--tsqd-font-size) * 0.25);padding:calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.25);}.go3176406346::after{content:' ';position:absolute;left:100%;height:calc(100% + calc(var(--tsqd-font-size) * 1.25));width:calc(var(--tsqd-font-size) * 0.375);}.go3176406346 svg{transform:rotate(-90deg);}.go2384516026{bottom:calc(var(--tsqd-font-size) * 0.5);right:0;transform:translate(100%, 0);border-left:none;border-right:#98a2b3 1px solid;border-top:#98a2b3 1px solid;border-bottom:#98a2b3 1px solid;border-radius:0px calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25) 0px;padding:calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.125);}.go2384516026::after{content:' ';position:absolute;right:100%;height:calc(100% + calc(var(--tsqd-font-size) * 1.25));width:calc(var(--tsqd-font-size) * 0.375);}.go2384516026 svg{transform:rotate(90deg);}.go2618594109{flex:1 1 700px;background-color:#f9fafb;display:flex;flex-direction:column;}.go2618594109 *{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;}.go1455364845{position:absolute;transition:background-color 0.125s ease;z-index:4;}.go1455364845:hover{background-color:#9B8AFB;}.go2684277365{display:flex;justify-content:space-between;align-items:center;padding:calc(var(--tsqd-font-size) * 0.5) calc(var(--tsqd-font-size) * 0.625);gap:calc(var(--tsqd-font-size) * 0.625);border-bottom:#d0d5dd 1px solid;}.go2684277365 > button{padding:0;background:transparent;border:none;display:flex;gap:calc(var(--tsqd-font-size) * 0.125);flex-direction:column;}.go2796217503{font-size:var(--tsqd-font-size);font-weight:700;line-height:calc(var(--tsqd-font-size) * 1);white-space:nowrap;color:#475467;}.go2201599386{font-weight:600;font-size:calc(var(--tsqd-font-size) * 0.75);background:linear-gradient( to right, #ea4037, #ff9b11 );background-clip:text;-webkit-background-clip:text;line-height:1;-webkit-text-fill-color:transparent;white-space:nowrap;}.go796298765{display:flex;gap:calc(var(--tsqd-font-size) * 0.375);box-sizing:border-box;height:calc(var(--tsqd-font-size) * 1.625);background:#f9fafb;color:#344054;border-radius:calc(var(--tsqd-font-size) * 0.25);font-size:calc(var(--tsqd-font-size) * 0.875);padding:calc(var(--tsqd-font-size) * 0.25);padding-left:calc(var(--tsqd-font-size) * 0.375);align-items:center;font-weight:500;border:1px solid #d0d5dd;user-select:none;position:relative;}.go796298765:focus-visible{outline-offset:2px;outline:2px solid #1849A9;}.go3896075193{font-size:calc(var(--tsqd-font-size) * 0.75);padding:0 5px;display:flex;align-items:center;justify-content:center;color:#667085;background-color:#eaecf0;border-radius:2px;font-variant-numeric:tabular-nums;height:calc(var(--tsqd-font-size) * 1.125);}.go466960074{position:absolute;z-index:1;background-color:#f9fafb;top:100%;left:50%;transform:translate(-50%, calc(calc(var(--tsqd-font-size) * 0.5)));padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.5);border-radius:calc(var(--tsqd-font-size) * 0.25);font-size:calc(var(--tsqd-font-size) * 0.75);border:1px solid #98a2b3;color:#475467;}.go466960074::before{top:0px;content:' ';display:block;left:50%;transform:translate(-50%, -100%);position:absolute;border-color:transparent transparent #98a2b3 transparent;border-style:solid;border-width:7px;}.go466960074::after{top:0px;content:' ';display:block;left:50%;transform:translate(-50%, calc(-100% + 2px));position:absolute;border-color:transparent transparent #f2f4f7 transparent;border-style:solid;border-width:7px;}.go4246191818{display:flex;gap:calc(var(--tsqd-font-size) * 0.5);}.go4246191818 > button{cursor:pointer;padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.5);border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#f2f4f7;border:1px solid #d0d5dd;color:#344054;font-size:calc(var(--tsqd-font-size) * 0.75);display:flex;align-items:center;line-height:calc(var(--tsqd-font-size) * 1.25);gap:calc(var(--tsqd-font-size) * 0.375);max-width:160px;}.go4246191818 > button:focus-visible{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go4246191818 > button svg{width:calc(var(--tsqd-font-size) * 0.75);height:calc(var(--tsqd-font-size) * 0.75);color:#667085;}.go676737936{padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.5);border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#f2f4f7;display:flex;box-sizing:content-box;align-items:center;gap:calc(var(--tsqd-font-size) * 0.375);max-width:160px;min-width:100px;border:1px solid #d0d5dd;height:min-content;color:#475467;}.go676737936 > svg{width:calc(var(--tsqd-font-size) * 0.75);height:calc(var(--tsqd-font-size) * 0.75);}.go676737936 input{font-size:calc(var(--tsqd-font-size) * 0.75);width:100%;background-color:#f2f4f7;border:none;padding:0;line-height:calc(var(--tsqd-font-size) * 1.25);color:#344054;}.go676737936 input::placeholder{color:#344054;}.go676737936 input:focus{outline:none;}.go676737936:focus-within{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go3040392080{padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.5);border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#f2f4f7;display:flex;align-items:center;gap:calc(var(--tsqd-font-size) * 0.375);box-sizing:content-box;max-width:160px;border:1px solid #d0d5dd;height:min-content;}.go3040392080 > svg{color:#475467;width:calc(var(--tsqd-font-size) * 0.5);height:calc(var(--tsqd-font-size) * 0.5);}.go3040392080 > select{appearance:none;color:#344054;min-width:100px;line-height:calc(var(--tsqd-font-size) * 1.25);font-size:calc(var(--tsqd-font-size) * 0.75);background-color:#f2f4f7;border:none;}.go3040392080 > select:focus{outline:none;}.go3040392080:focus-within{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go2056776863{border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#f2f4f7;border:1px solid #d0d5dd;width:calc(var(--tsqd-font-size) * 1.625);height:calc(var(--tsqd-font-size) * 1.625);justify-content:center;display:flex;align-items:center;gap:calc(var(--tsqd-font-size) * 0.375);max-width:160px;cursor:pointer;padding:0;}.go2056776863:hover{background-color:#eaecf0;}.go2056776863 svg{color:#344054;width:calc(var(--tsqd-font-size) * 0.75);height:calc(var(--tsqd-font-size) * 0.75);}.go2056776863:focus-visible{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go241189246 svg{stroke:#B54708;fill:#B54708;}.go2466263260{display:flex;align-items:center;padding:0;border:none;cursor:pointer;color:#344054;background-color:#f9fafb;line-height:1;}.go2466263260:focus{outline:none;}.go2466263260:focus-visible{outline-offset:-2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go2466263260:hover .tsqd-query-hash{background-color:#eaecf0;}.go2466263260 .tsqd-query-observer-count{padding:0 calc(var(--tsqd-font-size) * 0.25);user-select:none;min-width:calc(var(--tsqd-font-size) * 1.625);align-self:stretch;display:flex;align-items:center;justify-content:center;font-size:calc(var(--tsqd-font-size) * 0.75);font-weight:500;border-bottom-width:1px;border-bottom-style:solid;border-bottom:1px solid #d0d5dd;}.go2466263260 .tsqd-query-hash{user-select:text;font-size:calc(var(--tsqd-font-size) * 0.75);display:flex;align-items:center;min-height:calc(var(--tsqd-font-size) * 1.5);flex:1;padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.5);font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;border-bottom:1px solid #d0d5dd;text-align:left;text-overflow:clip;word-break:break-word;}.go2466263260 .tsqd-query-disabled-indicator{align-self:stretch;display:flex;align-items:center;padding:0 calc(var(--tsqd-font-size) * 0.5);color:#1d2939;background-color:#d0d5dd;border-bottom:1px solid #d0d5dd;font-size:calc(var(--tsqd-font-size) * 0.75);}.go381868107{background-color:#eaecf0;}.go1155590991{flex:1 1 700px;background-color:#f9fafb;color:#344054;font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;display:flex;flex-direction:column;overflow-y:auto;text-align:left;}.go1322271410{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;position:sticky;top:0;z-index:2;background-color:#eaecf0;padding:calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.5);font-weight:500;font-size:calc(var(--tsqd-font-size) * 0.75);line-height:calc(var(--tsqd-font-size) * 1);text-align:left;}.go3481064435{flex-wrap:wrap;margin:calc(var(--tsqd-font-size) * 0.5) 0px calc(var(--tsqd-font-size) * 0.5) 0px;display:flex;gap:calc(var(--tsqd-font-size) * 0.5);padding:0px calc(var(--tsqd-font-size) * 0.5);}.go3481064435 > button{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;font-size:calc(var(--tsqd-font-size) * 0.75);padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.5);display:flex;border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#f2f4f7;border:1px solid #d0d5dd;align-items:center;gap:calc(var(--tsqd-font-size) * 0.5);font-weight:500;line-height:calc(var(--tsqd-font-size) * 1);cursor:pointer;}.go3481064435 > button:focus-visible{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go3481064435 > button:hover{background-color:#eaecf0;}.go3481064435 > button:disabled{opacity:0.6;cursor:not-allowed;}.go3481064435 > button > span{width:calc(var(--tsqd-font-size) * 0.375);height:calc(var(--tsqd-font-size) * 0.375);border-radius:9999px;}.go3607795878{font-size:calc(var(--tsqd-font-size) * 0.75);padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.5);display:flex;border-radius:calc(var(--tsqd-font-size) * 0.25);overflow:hidden;background-color:#f2f4f7;border:1px solid #d0d5dd;align-items:center;gap:calc(var(--tsqd-font-size) * 0.5);font-weight:500;line-height:calc(var(--tsqd-font-size) * 1.25);color:#ef4444;cursor:pointer;position:relative;}.go3607795878:hover{background-color:#eaecf0;}.go3607795878 > span{width:calc(var(--tsqd-font-size) * 0.375);height:calc(var(--tsqd-font-size) * 0.375);border-radius:9999px;}.go3607795878:focus-within{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go3607795878 select{position:absolute;top:0;left:0;width:100%;height:100%;appearance:none;background-color:transparent;border:none;color:transparent;outline:none;}.go3607795878 svg path{stroke:#f87171;}.go3607795878 svg{width:calc(var(--tsqd-font-size) * 0.5);height:calc(var(--tsqd-font-size) * 0.5);}.go344010994{display:flex;flex-direction:column;gap:calc(var(--tsqd-font-size) * 0.125);border-radius:calc(var(--tsqd-font-size) * 0.25);border:1px solid #d0d5dd;background-color:#f9fafb;font-size:calc(var(--tsqd-font-size) * 0.75);color:#344054;z-index:99999;min-width:120px;padding:calc(var(--tsqd-font-size) * 0.125);}.go344010994 *{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;}.go1344014551{display:flex;align-items:center;justify-content:space-between;border-radius:calc(var(--tsqd-font-size) * 0.125);padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25);cursor:pointer;background-color:transparent;border:none;color:#344054;}.go1344014551 svg{color:#475467;transform:rotate(-90deg);width:calc(var(--tsqd-font-size) * 0.5);height:calc(var(--tsqd-font-size) * 0.5);}.go1344014551:hover{background-color:#eaecf0;}.go1344014551:focus-visible{outline-offset:2px;outline:2px solid #1849A9;}.go1344014551.data-disabled{opacity:0.6;cursor:not-allowed;}.go3776355971{padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25);font-weight:500;border-bottom:1px solid #d0d5dd;color:#667085;font-size:calc(var(--tsqd-font-size) * 0.75);}.go3060458145{display:flex;align-items:center;justify-content:space-between;color:#344054;font-size:calc(var(--tsqd-font-size) * 0.75);border-radius:calc(var(--tsqd-font-size) * 0.125);padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25);cursor:pointer;background-color:transparent;border:none;}.go3060458145 svg{color:#475467;}.go3060458145:hover{background-color:#eaecf0;}.go3060458145:focus-visible{outline-offset:2px;outline:2px solid #1849A9;}.go8818237{background-color:#EBE9FE;color:#5925DC;}.go8818237 svg{color:#5925DC;}.go8818237:hover{background-color:#EBE9FE;}.go1466597524{border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#eaecf0;border:1px solid #d0d5dd;display:flex;padding:0;font-size:calc(var(--tsqd-font-size) * 0.75);color:#344054;overflow:hidden;}.go1466597524:has(:focus-visible){outline:2px solid #1849A9;}.go1466597524 .tsqd-radio-toggle{opacity:0.5;display:flex;}.go1466597524 .tsqd-radio-toggle label{display:flex;align-items:center;cursor:pointer;line-height:calc(var(--tsqd-font-size) * 1.5);}.go1466597524 .tsqd-radio-toggle label:hover{background-color:#f2f4f7;}.go1466597524 > [data-checked]{opacity:1;background-color:#f2f4f7;}.go1466597524 > [data-checked] label:hover{background-color:#f2f4f7;}.go1466597524 .tsqd-radio-toggle:first-child{border-right:1px solid #d0d5dd;}.go1466597524 .tsqd-radio-toggle:first-child label{padding:0 calc(var(--tsqd-font-size) * 0.375) 0 calc(var(--tsqd-font-size) * 0.5);}.go1466597524 .tsqd-radio-toggle:nth-child(2) label{padding:0 calc(var(--tsqd-font-size) * 0.5) 0 calc(var(--tsqd-font-size) * 0.375);}.go3215246649{padding:calc(var(--tsqd-font-size) * 0.5);}.go3215246649 > [data-error='true']{outline:2px solid #fecaca;outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);}.go1100963409{width:100%;max-height:500px;font-family:'Fira Code', monospace;font-size:calc(var(--tsqd-font-size) * 0.75);border-radius:calc(var(--tsqd-font-size) * 0.25);field-sizing:content;padding:calc(var(--tsqd-font-size) * 0.5);background-color:#f2f4f7;color:#101828;border:1px solid #eaecf0;resize:none;}.go1100963409:focus{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #B2DDFF;}.go3150578485{color:#b91c1c;}.go2669091971{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;font-size:calc(var(--tsqd-font-size) * 0.75);padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.5);display:flex;border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#f2f4f7;border:1px solid #d0d5dd;align-items:center;gap:calc(var(--tsqd-font-size) * 0.5);font-weight:500;line-height:calc(var(--tsqd-font-size) * 1);cursor:pointer;}.go2669091971:focus-visible{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go2669091971:hover{background-color:#eaecf0;}.go2669091971:disabled{opacity:0.6;cursor:not-allowed;}.go2072408551{display:flex;align-items:center;background:#fff;color:#363636;line-height:1.3;will-change:transform;box-shadow:0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);max-width:350px;pointer-events:auto;padding:8px 10px;border-radius:8px;}.go685806154{position:relative;display:flex;justify-content:center;align-items:center;min-width:20px;min-height:20px;}.go1858758034{width:12px;height:12px;box-sizing:border-box;border:2px solid;border-radius:100%;border-color:#e0e0e0;border-right-color:#616161;animation:go1268368563 1s linear infinite;}.go1579819456{position:absolute;}.go2344853693{width:20px;opacity:0;height:20px;border-radius:10px;background:#61d345;position:relative;transform:rotate(45deg);animation:go1310225428 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;animation-delay:100ms;}.go2344853693:after{content:'';box-sizing:border-box;animation:go651618207 0.2s ease-out forwards;opacity:0;animation-delay:200ms;position:absolute;border-right:2px solid;border-bottom:2px solid;border-color:#fff;bottom:6px;left:6px;height:10px;width:6px;}.go3958317564{display:flex;justify-content:center;margin:4px 10px;color:inherit;flex:1 1 auto;white-space:pre-line;}@keyframes go3223188581{0%{transform:translate3d(0,-200%,0) scale(.6);opacity:.5;}100%{transform:translate3d(0,0,0) scale(1);opacity:1;}}</style><script src="https://va.vercel-scripts.com/v1/script.debug.js" defer="" data-sdkn="@vercel/analytics/react" data-sdkv="1.5.0"></script><meta name="referrer" content="strict-origin-when-cross-origin"><meta http-equiv="X-XSS-Protection" content="1; mode=block"><meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://cdn.jsdelivr.net https://www.googletagmanager.com 'unsafe-eval' http://localhost:*; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com data:; img-src 'self' data: blob: https: https://images.unsplash.com https://via.placeholder.com; media-src 'self' data: blob:; object-src 'none'; frame-src 'self' https://www.youtube.com https://player.vimeo.com; connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.github.com https://www.google-analytics.com http://localhost:* ws://localhost:* wss://localhost:*; worker-src 'self' blob:; manifest-src 'self'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests"></head>
  <body>
    <div id="root"><div class="min-h-screen flex flex-col bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900"><nav class="bg-white/10 backdrop-blur-md border-b border-white/10 sticky top-0 z-40"><div class="container mx-auto px-4"><div class="flex justify-between h-16"><div class="flex items-center"><a class="text-xl font-bold text-white" href="/">Festival Family</a></div><div class="hidden md:flex items-center space-x-4"><a class="
                  px-3 py-2 rounded-md text-sm font-medium transition-colors
                  bg-purple-700 text-white
                " href="/">Home</a><a class="
                  px-3 py-2 rounded-md text-sm font-medium transition-colors
                  text-white/70 hover:text-white hover:bg-white/10
                " href="/activities">Activities</a><a class="
                  px-3 py-2 rounded-md text-sm font-medium transition-colors
                  text-white/70 hover:text-white hover:bg-white/10
                " href="/famhub">FamHub</a><a class="
                  px-3 py-2 rounded-md text-sm font-medium transition-colors
                  text-white/70 hover:text-white hover:bg-white/10
                " href="/discover">Discover</a><a class="
                  px-3 py-2 rounded-md text-sm font-medium transition-colors
                  text-white/70 hover:text-white hover:bg-white/10
                " href="/profile">Profile</a></div><div class="md:hidden sm:hidden flex items-center"><div class="text-white/70 ml-2">Not signed in</div></div></div></div></nav><main class="flex-1 w-full px-4 pb-20"><div class="" style="opacity: 1;"><div class="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900"><nav class="relative z-10 p-6"><div class="max-w-7xl mx-auto flex justify-between items-center"><div class="flex items-center gap-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-music w-8 h-8 text-purple-400"><path d="M9 18V5l12-2v13"></path><circle cx="6" cy="18" r="3"></circle><circle cx="18" cy="16" r="3"></circle></svg><h1 class="text-2xl font-bold text-white">Festival Family</h1></div><a class="px-6 py-2 bg-purple-700 hover:bg-purple-600 rounded-lg text-white font-medium transition-colors" href="/auth">Sign In / Join</a></div></nav><section class="relative z-10 px-6 py-20"><div class="max-w-4xl mx-auto text-center text-white"><h1 class="text-5xl md:text-6xl font-bold mb-6 leading-tight">Find Your Tribe,<br><span class="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">Share the Vibe</span></h1><p class="text-xl md:text-2xl mb-8 text-white/80 max-w-3xl mx-auto">Never festival alone again. Connect with like-minded music lovers, discover amazing events, and build lifelong friendships in the festival community.</p><div class="flex flex-col sm:flex-row gap-4 justify-center"><a class="px-8 py-4 bg-purple-700 hover:bg-purple-600 rounded-lg text-white font-semibold text-lg transition-all transform hover:scale-105" href="/auth">Join Festival Family</a><a class="px-8 py-4 bg-white/10 hover:bg-white/20 backdrop-blur-md border border-white/20 rounded-lg text-white font-semibold text-lg transition-all" href="/discover">Explore Events</a></div></div></section><section class="relative z-10 px-6 py-20"><div class="max-w-7xl mx-auto"><div class="text-center mb-16"><h2 class="text-4xl font-bold text-white mb-4">Everything You Need for Festival Life</h2><p class="text-xl text-white/70 max-w-2xl mx-auto">From discovering events to building connections, Festival Family has all the tools to enhance your festival experience.</p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"><div class="p-6 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 hover:bg-white/15 transition-all"><div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-6 h-6 text-purple-400"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></div><h3 class="text-xl font-semibold text-white mb-2">Connect</h3><p class="text-white/70">Meet like-minded festival-goers and build your festival family through our FamHub community.</p></div><div class="p-6 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 hover:bg-white/15 transition-all"><div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar w-6 h-6 text-blue-400"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg></div><h3 class="text-xl font-semibold text-white mb-2">Discover</h3><p class="text-white/70">Find festivals and events that match your music taste and interests across the globe.</p></div><div class="p-6 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 hover:bg-white/15 transition-all"><div class="w-12 h-12 bg-pink-500/20 rounded-lg flex items-center justify-center mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart w-6 h-6 text-pink-400"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path></svg></div><h3 class="text-xl font-semibold text-white mb-2">Share</h3><p class="text-white/70">Share your festival experiences, photos, and memories with the community.</p></div><div class="p-6 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 hover:bg-white/15 transition-all"><div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield w-6 h-6 text-green-400"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path></svg></div><h3 class="text-xl font-semibold text-white mb-2">Stay Safe</h3><p class="text-white/70">Access safety resources, emergency contacts, and community support when you need it.</p></div></div></div></section><section class="relative z-10 px-6 py-20"><div class="max-w-4xl mx-auto"><div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8"><div class="text-center mb-8"><h2 class="text-3xl font-bold text-white mb-4">Join Thousands of Festival Lovers</h2><p class="text-white/70">Be part of a growing community that's revolutionizing the festival experience.</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center"><div><div class="text-4xl font-bold text-purple-400 mb-2">10K+</div><div class="text-white/70">Festival Goers</div></div><div><div class="text-4xl font-bold text-blue-400 mb-2">500+</div><div class="text-white/70">Events Listed</div></div><div><div class="text-4xl font-bold text-pink-400 mb-2">50K+</div><div class="text-white/70">Connections Made</div></div></div></div></div></section><section class="relative z-10 px-6 py-20"><div class="max-w-4xl mx-auto text-center"><div class="bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-md rounded-2xl border border-white/20 p-12"><h2 class="text-4xl font-bold text-white mb-6">Ready to Find Your Festival Family?</h2><p class="text-xl text-white/80 mb-8 max-w-2xl mx-auto">Join thousands of festival-goers who have already found their tribe. Your next festival adventure starts here.</p><a class="inline-flex items-center gap-2 px-8 py-4 bg-purple-700 hover:bg-purple-600 rounded-lg text-white font-semibold text-lg transition-all transform hover:scale-105" href="/auth"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-zap w-5 h-5"><path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"></path></svg>Get Started Now</a></div></div></section><footer class="relative z-10 px-6 py-12 border-t border-white/10"><div class="max-w-7xl mx-auto"><div class="flex flex-col md:flex-row justify-between items-center"><div class="flex items-center gap-3 mb-4 md:mb-0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-music w-6 h-6 text-purple-400"><path d="M9 18V5l12-2v13"></path><circle cx="6" cy="18" r="3"></circle><circle cx="18" cy="16" r="3"></circle></svg><span class="text-white font-semibold">Festival Family</span></div><div class="flex gap-6 text-white/60 text-sm"><a class="hover:text-white transition-colors" href="/help">Help</a><a class="hover:text-white transition-colors" href="/emergency">Safety</a><span>© 2025 Festival Family</span></div></div></div></footer></div></div></main><div class="h-16 md:hidden"></div><nav class="fixed bottom-0 left-0 right-0 w-full bg-black/80 backdrop-blur-md border-t border-white/10 shadow-lg z-40 md:hidden " role="navigation" aria-label="Main navigation"><div class="max-w-screen-xl mx-auto"><div class="grid grid-cols-5 h-16"><a aria-label="Navigate to Home" aria-current="page" class="flex flex-col items-center justify-center min-h-[44px] min-w-[44px] py-2 px-1 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-electric-violet focus:ring-offset-2 focus:ring-offset-black/80 rounded-md text-white font-medium" href="/"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" class="w-6 h-6 text-electric-violet" aria-hidden="true" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M261.56 101.28a8 8 0 0 0-11.06 0L66.4 277.15a8 8 0 0 0-2.47 5.79L63.9 448a32 32 0 0 0 32 32H192a16 16 0 0 0 16-16V328a8 8 0 0 1 8-8h80a8 8 0 0 1 8 8v136a16 16 0 0 0 16 16h96.06a32 32 0 0 0 32-32V282.94a8 8 0 0 0-2.47-5.79z"></path><path d="m490.91 244.15-74.8-71.56V64a16 16 0 0 0-16-16h-48a16 16 0 0 0-16 16v32l-57.92-55.38C272.77 35.14 264.71 32 256 32c-8.68 0-16.72 3.14-22.14 8.63l-212.7 203.5c-6.22 6-7 15.87-1.34 22.37A16 16 0 0 0 43 267.56L250.5 69.28a8 8 0 0 1 11.06 0l207.52 198.28a16 16 0 0 0 22.59-.44c6.14-6.36 5.63-16.86-.76-22.97z"></path></svg><span class="text-xs mt-1 text-electric-violet">Home</span></a><a aria-label="Navigate to Activities" class="flex flex-col items-center justify-center min-h-[44px] min-w-[44px] py-2 px-1 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-electric-violet focus:ring-offset-2 focus:ring-offset-black/80 rounded-md text-white/70 hover:text-white active:scale-95" href="/activities"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" class="w-6 h-6 " aria-hidden="true" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M480 128a64 64 0 0 0-64-64h-16V48.45c0-8.61-6.62-16-15.23-16.43A16 16 0 0 0 368 48v16H144V48.45c0-8.61-6.62-16-15.23-16.43A16 16 0 0 0 112 48v16H96a64 64 0 0 0-64 64v12a4 4 0 0 0 4 4h440a4 4 0 0 0 4-4zM32 416a64 64 0 0 0 64 64h320a64 64 0 0 0 64-64V179a3 3 0 0 0-3-3H35a3 3 0 0 0-3 3zm344-208a24 24 0 1 1-24 24 24 24 0 0 1 24-24zm0 80a24 24 0 1 1-24 24 24 24 0 0 1 24-24zm-80-80a24 24 0 1 1-24 24 24 24 0 0 1 24-24zm0 80a24 24 0 1 1-24 24 24 24 0 0 1 24-24zm0 80a24 24 0 1 1-24 24 24 24 0 0 1 24-24zm-80-80a24 24 0 1 1-24 24 24 24 0 0 1 24-24zm0 80a24 24 0 1 1-24 24 24 24 0 0 1 24-24zm-80-80a24 24 0 1 1-24 24 24 24 0 0 1 24-24zm0 80a24 24 0 1 1-24 24 24 24 0 0 1 24-24z"></path></svg><span class="text-xs mt-1 ">Activities</span></a><a aria-label="Navigate to FamHub" class="flex flex-col items-center justify-center min-h-[44px] min-w-[44px] py-2 px-1 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-electric-violet focus:ring-offset-2 focus:ring-offset-black/80 rounded-md text-white/70 hover:text-white active:scale-95" href="/famhub"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" class="w-6 h-6 " aria-hidden="true" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M336 256c-20.56 0-40.44-9.18-56-25.84-15.13-16.25-24.37-37.92-26-61-1.74-24.62 5.77-47.26 21.14-63.76S312 80 336 80c23.83 0 45.38 9.06 60.7 25.52 15.47 16.62 23 39.22 21.26 63.63-1.67 23.11-10.9 44.77-26 61C376.44 246.82 356.57 256 336 256zm66-88zm65.83 264H204.18a27.71 27.71 0 0 1-22-10.67 30.22 30.22 0 0 1-5.26-25.79c8.42-33.81 29.28-61.85 60.32-81.08C264.79 297.4 299.86 288 336 288c36.85 0 71 9 98.71 26.05 31.11 19.13 52 47.33 60.38 81.55a30.27 30.27 0 0 1-5.32 25.78A27.68 27.68 0 0 1 467.83 432zM147 260c-35.19 0-66.13-32.72-69-72.93-1.42-20.6 5-39.65 18-53.62 12.86-13.83 31-21.45 51-21.45s38 7.66 50.93 21.57c13.1 14.08 19.5 33.09 18 53.52-2.87 40.2-33.8 72.91-68.93 72.91zm65.66 31.45c-17.59-8.6-40.42-12.9-65.65-12.9-29.46 0-58.07 7.68-80.57 21.62-25.51 15.83-42.67 38.88-49.6 66.71a27.39 27.39 0 0 0 4.79 23.36A25.32 25.32 0 0 0 41.72 400h111a8 8 0 0 0 7.87-6.57c.11-.63.25-1.26.41-1.88 8.48-34.06 28.35-62.84 57.71-83.82a8 8 0 0 0-.63-13.39c-1.57-.92-3.37-1.89-5.42-2.89z"></path></svg><span class="text-xs mt-1 ">FamHub</span></a><a aria-label="Navigate to Discover" class="flex flex-col items-center justify-center min-h-[44px] min-w-[44px] py-2 px-1 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-electric-violet focus:ring-offset-2 focus:ring-offset-black/80 rounded-md text-white/70 hover:text-white active:scale-95" href="/discover"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" class="w-6 h-6 " aria-hidden="true" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><circle cx="256" cy="256" r="24"></circle><path d="M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm105.07 113.33-46.88 117.2a64 64 0 0 1-35.66 35.66l-117.2 46.88a8 8 0 0 1-10.4-10.4l46.88-117.2a64 64 0 0 1 35.66-35.66l117.2-46.88a8 8 0 0 1 10.4 10.4z"></path></svg><span class="text-xs mt-1 ">Discover</span></a><button class="flex flex-col items-center justify-center min-h-[44px] min-w-[44px] py-2 px-1 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-electric-violet focus:ring-offset-2 focus:ring-offset-black/80 rounded-md active:scale-95 text-white/70 hover:text-white" aria-label="Open quick actions menu" aria-expanded="false" aria-haspopup="menu" aria-controls="quick-actions-menu"><div class="relative"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" class="w-6 h-6 transition-transform duration-300 " aria-hidden="true" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M256 112v288m144-144H112"></path></svg></div><span class="text-xs mt-1 ">Actions</span></button></div></div></nav><div id="_rht_toaster" style="position: fixed; z-index: 9999; inset: 16px; pointer-events: none;"><div class="go4109123758" style="left: 0px; right: 0px; display: flex; position: absolute; transition: 230ms cubic-bezier(0.21, 1.02, 0.73, 1); transform: translateY(0px); top: 0px; justify-content: center;"><div class="go2072408551 backdrop-blur-md bg-white/10 border border-white/20 text-white" style="background: rgba(45, 27, 105, 0.8); color: white; animation: 0.35s cubic-bezier(0.21, 1.02, 0.73, 1) 0s 1 normal forwards running go3223188581;"><div class="go685806154"><div class="go1858758034"></div><div class="go1579819456"><div class="go2344853693"></div></div></div><div role="status" aria-live="polite" class="go3958317564">Connected to Supabase successfully</div></div></div><div class="go4109123758" style="left: 0px; right: 0px; display: flex; position: absolute; transition: 230ms cubic-bezier(0.21, 1.02, 0.73, 1); transform: translateY(52.7969px); top: 0px; justify-content: center;"><div class="go2072408551 backdrop-blur-md bg-white/10 border border-white/20 text-white" style="background: rgba(45, 27, 105, 0.8); color: white; animation: 0.35s cubic-bezier(0.21, 1.02, 0.73, 1) 0s 1 normal forwards running go3223188581;"><div class="go685806154"><div class="go1858758034"></div><div class="go1579819456"><div class="go2344853693"></div></div></div><div role="status" aria-live="polite" class="go3958317564">Connected to Supabase successfully</div></div></div></div></div><div id="_rht_toaster" style="position: fixed; z-index: 9999; inset: 16px; pointer-events: none;"><div class="go4109123758" style="left: 0px; right: 0px; display: flex; position: absolute; transition: 230ms cubic-bezier(0.21, 1.02, 0.73, 1); transform: translateY(0px); top: 0px; justify-content: flex-end;"><div class="go2072408551" style="animation: 0.35s cubic-bezier(0.21, 1.02, 0.73, 1) 0s 1 normal forwards running go3223188581;"><div class="go685806154"><div class="go1858758034"></div><div class="go1579819456"><div class="go2344853693"></div></div></div><div role="status" aria-live="polite" class="go3958317564">Connected to Supabase successfully</div></div></div><div class="go4109123758" style="left: 0px; right: 0px; display: flex; position: absolute; transition: 230ms cubic-bezier(0.21, 1.02, 0.73, 1); transform: translateY(52.7969px); top: 0px; justify-content: flex-end;"><div class="go2072408551" style="animation: 0.35s cubic-bezier(0.21, 1.02, 0.73, 1) 0s 1 normal forwards running go3223188581;"><div class="go685806154"><div class="go1858758034"></div><div class="go1579819456"><div class="go2344853693"></div></div></div><div role="status" aria-live="polite" class="go3958317564">Connected to Supabase successfully</div></div></div></div><div class="fixed bottom-4 right-4 p-4 rounded-lg shadow-lg z-50 max-w-md backdrop-blur-md bg-opacity-90"><div class="bg-green-500 text-white p-4 rounded-lg"><h3 class="font-bold mb-2">Connected</h3><p>Successfully connected to Supabase</p><div class="flex justify-between mt-2"><button type="button" class="px-3 py-1 bg-green-600 rounded hover:bg-green-700">Show Details</button><button type="button" class="px-3 py-1 bg-green-600 rounded hover:bg-green-700">Dismiss</button></div></div></div><div class="fixed bottom-4 right-4 p-4 rounded-lg shadow-lg z-50 max-w-md backdrop-blur-md bg-opacity-90"><div class="bg-green-500 text-white p-4 rounded-lg"><h3 class="font-bold mb-2">Connection Successful</h3><p>Connection successful</p><div class="mt-2 p-2 bg-green-600 rounded text-sm"><pre class="whitespace-pre-wrap">{
  "data": [
    {
      "id": "7f4f5eea-3974-4e2f-a324-00fe5458750d"
    }
  ]
}</pre></div></div></div><div class="tsqd-parent-container" style="--tsqd-panel-height: 500px; --tsqd-panel-width: 500px; --tsqd-font-size: 16px;"><div class="go2420793703 tsqd-transitions-container"><div class="go3489369143 go1754112896 tsqd-open-btn-container"><div aria-hidden="true"><svg version="1.0" viewBox="0 0 633 633"><linearGradient x1="-666.45" x2="-666.45" y1="163.28" y2="163.99" gradientTransform="matrix(633 0 0 633 422177 -103358)" gradientUnits="userSpaceOnUse" id="a-cl-0"><stop stop-color="#6BDAFF" offset="0"></stop><stop stop-color="#F9FFB5" offset=".32"></stop><stop stop-color="#FFA770" offset=".71"></stop><stop stop-color="#FF7373" offset="1"></stop></linearGradient><circle cx="316.5" cy="316.5" r="316.5" fill="url(#a-cl-0)"></circle><defs><filter x="-137.5" y="412" width="454" height="396.9" filterUnits="userSpaceOnUse" id="am-cl-0"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="-137.5" y="412" width="454" height="396.9" maskUnits="userSpaceOnUse" id="b-cl-0"><g filter="url(#am-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#b-cl-0)"><ellipse cx="89.5" cy="610.5" rx="214.5" ry="186" fill="#015064" stroke="#00CFE2" stroke-width="25"></ellipse></g><defs><filter x="316.5" y="412" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ah-cl-0"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="316.5" y="412" width="454" height="396.9" maskUnits="userSpaceOnUse" id="k-cl-0"><g filter="url(#ah-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#k-cl-0)"><ellipse cx="543.5" cy="610.5" rx="214.5" ry="186" fill="#015064" stroke="#00CFE2" stroke-width="25"></ellipse></g><defs><filter x="-137.5" y="450" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ae-cl-0"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="-137.5" y="450" width="454" height="396.9" maskUnits="userSpaceOnUse" id="j-cl-0"><g filter="url(#ae-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#j-cl-0)"><ellipse cx="89.5" cy="648.5" rx="214.5" ry="186" fill="#015064" stroke="#00A8B8" stroke-width="25"></ellipse></g><defs><filter x="316.5" y="450" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ai-cl-0"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="316.5" y="450" width="454" height="396.9" maskUnits="userSpaceOnUse" id="i-cl-0"><g filter="url(#ai-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#i-cl-0)"><ellipse cx="543.5" cy="648.5" rx="214.5" ry="186" fill="#015064" stroke="#00A8B8" stroke-width="25"></ellipse></g><defs><filter x="-137.5" y="486" width="454" height="396.9" filterUnits="userSpaceOnUse" id="aj-cl-0"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="-137.5" y="486" width="454" height="396.9" maskUnits="userSpaceOnUse" id="h-cl-0"><g filter="url(#aj-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#h-cl-0)"><ellipse cx="89.5" cy="684.5" rx="214.5" ry="186" fill="#015064" stroke="#007782" stroke-width="25"></ellipse></g><defs><filter x="316.5" y="486" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ag-cl-0"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="316.5" y="486" width="454" height="396.9" maskUnits="userSpaceOnUse" id="g-cl-0"><g filter="url(#ag-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#g-cl-0)"><ellipse cx="543.5" cy="684.5" rx="214.5" ry="186" fill="#015064" stroke="#007782" stroke-width="25"></ellipse></g><defs><filter x="272.2" y="308" width="176.9" height="129.3" filterUnits="userSpaceOnUse" id="af-cl-0"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="272.2" y="308" width="176.9" height="129.3" maskUnits="userSpaceOnUse" id="f-cl-0"><g filter="url(#af-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#f-cl-0)"><line x1="436" x2="431" y1="403.2" y2="431.8" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="11"></line><line x1="291" x2="280" y1="341.5" y2="403.5" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="11"></line><line x1="332.9" x2="328.6" y1="384.1" y2="411.2" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="11"></line><linearGradient x1="-670.75" x2="-671.59" y1="164.4" y2="164.49" gradientTransform="matrix(-184.16 -32.472 -11.461 64.997 -121359 -32126)" gradientUnits="userSpaceOnUse" id="m-cl-0"><stop stop-color="#EE2700" offset="0"></stop><stop stop-color="#FF008E" offset="1"></stop></linearGradient><path d="m344.1 363 97.7 17.2c5.8 2.1 8.2 6.1 7.1 12.1s-4.7 9.2-11 9.9l-106-18.7-57.5-59.2c-3.2-4.8-2.9-9.1 0.8-12.8s8.3-4.4 13.7-2.1l55.2 53.6z" clip-rule="evenodd" fill-rule="evenodd" fill="url(#m-cl-0)"></path><line x1="428.2" x2="429.1" y1="384.5" y2="378" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="395.2" x2="396.1" y1="379.5" y2="373" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="362.2" x2="363.1" y1="373.5" y2="367.4" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="324.2" x2="328.4" y1="351.3" y2="347.4" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="303.2" x2="307.4" y1="331.3" y2="327.4" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line></g><defs><filter x="73.2" y="113.8" width="280.6" height="317.4" filterUnits="userSpaceOnUse" id="ak-cl-0"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="73.2" y="113.8" width="280.6" height="317.4" maskUnits="userSpaceOnUse" id="e-cl-0"><g filter="url(#ak-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#e-cl-0)"><linearGradient x1="-672.16" x2="-672.16" y1="165.03" y2="166.03" gradientTransform="matrix(-100.18 48.861 97.976 200.88 -83342 -93.059)" gradientUnits="userSpaceOnUse" id="n-cl-0"><stop stop-color="#A17500" offset="0"></stop><stop stop-color="#5D2100" offset="1"></stop></linearGradient><path d="m192.3 203c8.1 37.3 14 73.6 17.8 109.1 3.8 35.4 2.8 75.1-3 119.2l61.2-16.7c-15.6-59-25.2-97.9-28.6-116.6s-10.8-51.9-22.1-99.6l-25.3 4.6" clip-rule="evenodd" fill-rule="evenodd" fill="url(#n-cl-0)"></path><g stroke="#2F8A00"><linearGradient x1="-660.23" x2="-660.23" y1="166.72" y2="167.72" gradientTransform="matrix(92.683 4.8573 -2.0259 38.657 61680 -3088.6)" gradientUnits="userSpaceOnUse" id="r-cl-0"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></linearGradient><path d="m195 183.9s-12.6-22.1-36.5-29.9c-15.9-5.2-34.4-1.5-55.5 11.1 15.9 14.3 29.5 22.6 40.7 24.9 16.8 3.6 51.3-6.1 51.3-6.1z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#r-cl-0)"></path><linearGradient x1="-661.36" x2="-661.36" y1="164.18" y2="165.18" gradientTransform="matrix(110 5.7648 -6.3599 121.35 73933 -15933)" gradientUnits="userSpaceOnUse" id="s-cl-0"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></linearGradient><path d="m194.9 184.5s-47.5-8.5-83.2 15.7c-23.8 16.2-34.3 49.3-31.6 99.4 30.3-27.8 52.1-48.5 65.2-61.9 19.8-20.2 49.6-53.2 49.6-53.2z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#s-cl-0)"></path><linearGradient x1="-656.79" x2="-656.79" y1="165.15" y2="166.15" gradientTransform="matrix(62.954 3.2993 -3.5023 66.828 42156 -8754.1)" gradientUnits="userSpaceOnUse" id="q-cl-0"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></linearGradient><path d="m195 183.9c-0.8-21.9 6-38 20.6-48.2s29.8-15.4 45.5-15.3c-6.1 21.4-14.5 35.8-25.2 43.4s-24.4 14.2-40.9 20.1z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#q-cl-0)"></path><linearGradient x1="-663.07" x2="-663.07" y1="165.44" y2="166.44" gradientTransform="matrix(152.47 7.9907 -3.0936 59.029 101884 -4318.7)" gradientUnits="userSpaceOnUse" id="p-cl-0"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></linearGradient><path d="m194.9 184.5c31.9-30 64.1-39.7 96.7-29s50.8 30.4 54.6 59.1c-35.2-5.5-60.4-9.6-75.8-12.1-15.3-2.6-40.5-8.6-75.5-18z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#p-cl-0)"></path><linearGradient x1="-662.57" x2="-662.57" y1="164.44" y2="165.44" gradientTransform="matrix(136.46 7.1517 -5.2163 99.533 91536 -11442)" gradientUnits="userSpaceOnUse" id="o-cl-0"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></linearGradient><path d="m194.9 184.5c35.8-7.6 65.6-0.2 89.2 22s37.7 49 42.3 80.3c-39.8-9.7-68.3-23.8-85.5-42.4s-32.5-38.5-46-59.9z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#o-cl-0)"></path><linearGradient x1="-656.43" x2="-656.43" y1="163.86" y2="164.86" gradientTransform="matrix(60.866 3.1899 -8.7773 167.48 41560 -25168)" gradientUnits="userSpaceOnUse" id="l-cl-0"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></linearGradient><path d="m194.9 184.5c-33.6 13.8-53.6 35.7-60.1 65.6s-3.6 63.1 8.7 99.6c27.4-40.3 43.2-69.6 47.4-88s5.6-44.1 4-77.2z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#l-cl-0)"></path><path d="m196.5 182.3c-14.8 21.6-25.1 41.4-30.8 59.4s-9.5 33-11.1 45.1" fill="none" stroke-linecap="round" stroke-width="8"></path><path d="m194.9 185.7c-24.4 1.7-43.8 9-58.1 21.8s-24.7 25.4-31.3 37.8" fill="none" stroke-linecap="round" stroke-width="8"></path><path d="m204.5 176.4c29.7-6.7 52-8.4 67-5.1s26.9 8.6 35.8 15.9" fill="none" stroke-linecap="round" stroke-width="8"></path><path d="m196.5 181.4c20.3 9.9 38.2 20.5 53.9 31.9s27.4 22.1 35.1 32" fill="none" stroke-linecap="round" stroke-width="8"></path></g></g><defs><filter x="50.5" y="399" width="532" height="633" filterUnits="userSpaceOnUse" id="al-cl-0"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="50.5" y="399" width="532" height="633" maskUnits="userSpaceOnUse" id="d-cl-0"><g filter="url(#al-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#d-cl-0)"><linearGradient x1="-666.06" x2="-666.23" y1="163.36" y2="163.75" gradientTransform="matrix(532 0 0 633 354760 -102959)" gradientUnits="userSpaceOnUse" id="u-cl-0"><stop stop-color="#FFF400" offset="0"></stop><stop stop-color="#3C8700" offset="1"></stop></linearGradient><ellipse cx="316.5" cy="715.5" rx="266" ry="316.5" fill="url(#u-cl-0)"></ellipse></g><defs><filter x="391" y="-24" width="288" height="283" filterUnits="userSpaceOnUse" id="ad-cl-0"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="391" y="-24" width="288" height="283" maskUnits="userSpaceOnUse" id="c-cl-0"><g filter="url(#ad-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#c-cl-0)"><linearGradient x1="-664.56" x2="-664.56" y1="163.79" y2="164.79" gradientTransform="matrix(227 0 0 227 151421 -37204)" gradientUnits="userSpaceOnUse" id="t-cl-0"><stop stop-color="#FFDF00" offset="0"></stop><stop stop-color="#FF9D00" offset="1"></stop></linearGradient><circle cx="565.5" cy="89.5" r="113.5" fill="url(#t-cl-0)"></circle><linearGradient x1="-644.5" x2="-645.77" y1="342" y2="342" gradientTransform="matrix(30 0 0 1 19770 -253)" gradientUnits="userSpaceOnUse" id="v-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="427" x2="397" y1="89" y2="89" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#v-cl-0)"></line><linearGradient x1="-641.56" x2="-642.83" y1="196.02" y2="196.07" gradientTransform="matrix(26.5 0 0 5.5 17439 -1025.5)" gradientUnits="userSpaceOnUse" id="aa-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="430.5" x2="404" y1="55.5" y2="50" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#aa-cl-0)"></line><linearGradient x1="-643.73" x2="-645" y1="185.83" y2="185.9" gradientTransform="matrix(29 0 0 8 19107 -1361)" gradientUnits="userSpaceOnUse" id="w-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="431" x2="402" y1="122" y2="130" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#w-cl-0)"></line><linearGradient x1="-638.94" x2="-640.22" y1="177.09" y2="177.39" gradientTransform="matrix(24 0 0 13 15783 -2145)" gradientUnits="userSpaceOnUse" id="ac-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="442" x2="418" y1="153" y2="166" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#ac-cl-0)"></line><linearGradient x1="-633.42" x2="-634.7" y1="172.41" y2="173.31" gradientTransform="matrix(20 0 0 19 13137 -3096)" gradientUnits="userSpaceOnUse" id="ab-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="464" x2="444" y1="180" y2="199" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#ab-cl-0)"></line><linearGradient x1="-619.05" x2="-619.52" y1="170.82" y2="171.82" gradientTransform="matrix(13.83 0 0 22.85 9050 -3703.4)" gradientUnits="userSpaceOnUse" id="y-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="491.4" x2="477.5" y1="203" y2="225.9" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#y-cl-0)"></line><linearGradient x1="-578.5" x2="-578.63" y1="170.31" y2="171.31" gradientTransform="matrix(7.5 0 0 24.5 4860 -3953)" gradientUnits="userSpaceOnUse" id="x-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="524.5" x2="517" y1="219.5" y2="244" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#x-cl-0)"></line><linearGradient x1="666.5" x2="666.5" y1="170.31" y2="171.31" gradientTransform="matrix(.5 0 0 24.5 231.5 -3944)" gradientUnits="userSpaceOnUse" id="z-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="564.5" x2="565" y1="228.5" y2="253" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#z-cl-0)"></line></g></svg></div><button type="button" aria-label="Open Tanstack query devtools" class="tsqd-open-btn"><svg version="1.0" viewBox="0 0 633 633"><linearGradient x1="-666.45" x2="-666.45" y1="163.28" y2="163.99" gradientTransform="matrix(633 0 0 633 422177 -103358)" gradientUnits="userSpaceOnUse" id="a-cl-1"><stop stop-color="#6BDAFF" offset="0"></stop><stop stop-color="#F9FFB5" offset=".32"></stop><stop stop-color="#FFA770" offset=".71"></stop><stop stop-color="#FF7373" offset="1"></stop></linearGradient><circle cx="316.5" cy="316.5" r="316.5" fill="url(#a-cl-1)"></circle><defs><filter x="-137.5" y="412" width="454" height="396.9" filterUnits="userSpaceOnUse" id="am-cl-1"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="-137.5" y="412" width="454" height="396.9" maskUnits="userSpaceOnUse" id="b-cl-1"><g filter="url(#am-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#b-cl-1)"><ellipse cx="89.5" cy="610.5" rx="214.5" ry="186" fill="#015064" stroke="#00CFE2" stroke-width="25"></ellipse></g><defs><filter x="316.5" y="412" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ah-cl-1"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="316.5" y="412" width="454" height="396.9" maskUnits="userSpaceOnUse" id="k-cl-1"><g filter="url(#ah-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#k-cl-1)"><ellipse cx="543.5" cy="610.5" rx="214.5" ry="186" fill="#015064" stroke="#00CFE2" stroke-width="25"></ellipse></g><defs><filter x="-137.5" y="450" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ae-cl-1"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="-137.5" y="450" width="454" height="396.9" maskUnits="userSpaceOnUse" id="j-cl-1"><g filter="url(#ae-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#j-cl-1)"><ellipse cx="89.5" cy="648.5" rx="214.5" ry="186" fill="#015064" stroke="#00A8B8" stroke-width="25"></ellipse></g><defs><filter x="316.5" y="450" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ai-cl-1"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="316.5" y="450" width="454" height="396.9" maskUnits="userSpaceOnUse" id="i-cl-1"><g filter="url(#ai-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#i-cl-1)"><ellipse cx="543.5" cy="648.5" rx="214.5" ry="186" fill="#015064" stroke="#00A8B8" stroke-width="25"></ellipse></g><defs><filter x="-137.5" y="486" width="454" height="396.9" filterUnits="userSpaceOnUse" id="aj-cl-1"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="-137.5" y="486" width="454" height="396.9" maskUnits="userSpaceOnUse" id="h-cl-1"><g filter="url(#aj-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#h-cl-1)"><ellipse cx="89.5" cy="684.5" rx="214.5" ry="186" fill="#015064" stroke="#007782" stroke-width="25"></ellipse></g><defs><filter x="316.5" y="486" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ag-cl-1"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="316.5" y="486" width="454" height="396.9" maskUnits="userSpaceOnUse" id="g-cl-1"><g filter="url(#ag-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#g-cl-1)"><ellipse cx="543.5" cy="684.5" rx="214.5" ry="186" fill="#015064" stroke="#007782" stroke-width="25"></ellipse></g><defs><filter x="272.2" y="308" width="176.9" height="129.3" filterUnits="userSpaceOnUse" id="af-cl-1"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="272.2" y="308" width="176.9" height="129.3" maskUnits="userSpaceOnUse" id="f-cl-1"><g filter="url(#af-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#f-cl-1)"><line x1="436" x2="431" y1="403.2" y2="431.8" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="11"></line><line x1="291" x2="280" y1="341.5" y2="403.5" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="11"></line><line x1="332.9" x2="328.6" y1="384.1" y2="411.2" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="11"></line><linearGradient x1="-670.75" x2="-671.59" y1="164.4" y2="164.49" gradientTransform="matrix(-184.16 -32.472 -11.461 64.997 -121359 -32126)" gradientUnits="userSpaceOnUse" id="m-cl-1"><stop stop-color="#EE2700" offset="0"></stop><stop stop-color="#FF008E" offset="1"></stop></linearGradient><path d="m344.1 363 97.7 17.2c5.8 2.1 8.2 6.1 7.1 12.1s-4.7 9.2-11 9.9l-106-18.7-57.5-59.2c-3.2-4.8-2.9-9.1 0.8-12.8s8.3-4.4 13.7-2.1l55.2 53.6z" clip-rule="evenodd" fill-rule="evenodd" fill="url(#m-cl-1)"></path><line x1="428.2" x2="429.1" y1="384.5" y2="378" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="395.2" x2="396.1" y1="379.5" y2="373" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="362.2" x2="363.1" y1="373.5" y2="367.4" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="324.2" x2="328.4" y1="351.3" y2="347.4" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="303.2" x2="307.4" y1="331.3" y2="327.4" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line></g><defs><filter x="73.2" y="113.8" width="280.6" height="317.4" filterUnits="userSpaceOnUse" id="ak-cl-1"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="73.2" y="113.8" width="280.6" height="317.4" maskUnits="userSpaceOnUse" id="e-cl-1"><g filter="url(#ak-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#e-cl-1)"><linearGradient x1="-672.16" x2="-672.16" y1="165.03" y2="166.03" gradientTransform="matrix(-100.18 48.861 97.976 200.88 -83342 -93.059)" gradientUnits="userSpaceOnUse" id="n-cl-1"><stop stop-color="#A17500" offset="0"></stop><stop stop-color="#5D2100" offset="1"></stop></linearGradient><path d="m192.3 203c8.1 37.3 14 73.6 17.8 109.1 3.8 35.4 2.8 75.1-3 119.2l61.2-16.7c-15.6-59-25.2-97.9-28.6-116.6s-10.8-51.9-22.1-99.6l-25.3 4.6" clip-rule="evenodd" fill-rule="evenodd" fill="url(#n-cl-1)"></path><g stroke="#2F8A00"><linearGradient x1="-660.23" x2="-660.23" y1="166.72" y2="167.72" gradientTransform="matrix(92.683 4.8573 -2.0259 38.657 61680 -3088.6)" gradientUnits="userSpaceOnUse" id="r-cl-1"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></linearGradient><path d="m195 183.9s-12.6-22.1-36.5-29.9c-15.9-5.2-34.4-1.5-55.5 11.1 15.9 14.3 29.5 22.6 40.7 24.9 16.8 3.6 51.3-6.1 51.3-6.1z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#r-cl-1)"></path><linearGradient x1="-661.36" x2="-661.36" y1="164.18" y2="165.18" gradientTransform="matrix(110 5.7648 -6.3599 121.35 73933 -15933)" gradientUnits="userSpaceOnUse" id="s-cl-1"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></linearGradient><path d="m194.9 184.5s-47.5-8.5-83.2 15.7c-23.8 16.2-34.3 49.3-31.6 99.4 30.3-27.8 52.1-48.5 65.2-61.9 19.8-20.2 49.6-53.2 49.6-53.2z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#s-cl-1)"></path><linearGradient x1="-656.79" x2="-656.79" y1="165.15" y2="166.15" gradientTransform="matrix(62.954 3.2993 -3.5023 66.828 42156 -8754.1)" gradientUnits="userSpaceOnUse" id="q-cl-1"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></linearGradient><path d="m195 183.9c-0.8-21.9 6-38 20.6-48.2s29.8-15.4 45.5-15.3c-6.1 21.4-14.5 35.8-25.2 43.4s-24.4 14.2-40.9 20.1z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#q-cl-1)"></path><linearGradient x1="-663.07" x2="-663.07" y1="165.44" y2="166.44" gradientTransform="matrix(152.47 7.9907 -3.0936 59.029 101884 -4318.7)" gradientUnits="userSpaceOnUse" id="p-cl-1"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></linearGradient><path d="m194.9 184.5c31.9-30 64.1-39.7 96.7-29s50.8 30.4 54.6 59.1c-35.2-5.5-60.4-9.6-75.8-12.1-15.3-2.6-40.5-8.6-75.5-18z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#p-cl-1)"></path><linearGradient x1="-662.57" x2="-662.57" y1="164.44" y2="165.44" gradientTransform="matrix(136.46 7.1517 -5.2163 99.533 91536 -11442)" gradientUnits="userSpaceOnUse" id="o-cl-1"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></linearGradient><path d="m194.9 184.5c35.8-7.6 65.6-0.2 89.2 22s37.7 49 42.3 80.3c-39.8-9.7-68.3-23.8-85.5-42.4s-32.5-38.5-46-59.9z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#o-cl-1)"></path><linearGradient x1="-656.43" x2="-656.43" y1="163.86" y2="164.86" gradientTransform="matrix(60.866 3.1899 -8.7773 167.48 41560 -25168)" gradientUnits="userSpaceOnUse" id="l-cl-1"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></linearGradient><path d="m194.9 184.5c-33.6 13.8-53.6 35.7-60.1 65.6s-3.6 63.1 8.7 99.6c27.4-40.3 43.2-69.6 47.4-88s5.6-44.1 4-77.2z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#l-cl-1)"></path><path d="m196.5 182.3c-14.8 21.6-25.1 41.4-30.8 59.4s-9.5 33-11.1 45.1" fill="none" stroke-linecap="round" stroke-width="8"></path><path d="m194.9 185.7c-24.4 1.7-43.8 9-58.1 21.8s-24.7 25.4-31.3 37.8" fill="none" stroke-linecap="round" stroke-width="8"></path><path d="m204.5 176.4c29.7-6.7 52-8.4 67-5.1s26.9 8.6 35.8 15.9" fill="none" stroke-linecap="round" stroke-width="8"></path><path d="m196.5 181.4c20.3 9.9 38.2 20.5 53.9 31.9s27.4 22.1 35.1 32" fill="none" stroke-linecap="round" stroke-width="8"></path></g></g><defs><filter x="50.5" y="399" width="532" height="633" filterUnits="userSpaceOnUse" id="al-cl-1"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="50.5" y="399" width="532" height="633" maskUnits="userSpaceOnUse" id="d-cl-1"><g filter="url(#al-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#d-cl-1)"><linearGradient x1="-666.06" x2="-666.23" y1="163.36" y2="163.75" gradientTransform="matrix(532 0 0 633 354760 -102959)" gradientUnits="userSpaceOnUse" id="u-cl-1"><stop stop-color="#FFF400" offset="0"></stop><stop stop-color="#3C8700" offset="1"></stop></linearGradient><ellipse cx="316.5" cy="715.5" rx="266" ry="316.5" fill="url(#u-cl-1)"></ellipse></g><defs><filter x="391" y="-24" width="288" height="283" filterUnits="userSpaceOnUse" id="ad-cl-1"><feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix></filter></defs><mask x="391" y="-24" width="288" height="283" maskUnits="userSpaceOnUse" id="c-cl-1"><g filter="url(#ad-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#c-cl-1)"><linearGradient x1="-664.56" x2="-664.56" y1="163.79" y2="164.79" gradientTransform="matrix(227 0 0 227 151421 -37204)" gradientUnits="userSpaceOnUse" id="t-cl-1"><stop stop-color="#FFDF00" offset="0"></stop><stop stop-color="#FF9D00" offset="1"></stop></linearGradient><circle cx="565.5" cy="89.5" r="113.5" fill="url(#t-cl-1)"></circle><linearGradient x1="-644.5" x2="-645.77" y1="342" y2="342" gradientTransform="matrix(30 0 0 1 19770 -253)" gradientUnits="userSpaceOnUse" id="v-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="427" x2="397" y1="89" y2="89" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#v-cl-1)"></line><linearGradient x1="-641.56" x2="-642.83" y1="196.02" y2="196.07" gradientTransform="matrix(26.5 0 0 5.5 17439 -1025.5)" gradientUnits="userSpaceOnUse" id="aa-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="430.5" x2="404" y1="55.5" y2="50" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#aa-cl-1)"></line><linearGradient x1="-643.73" x2="-645" y1="185.83" y2="185.9" gradientTransform="matrix(29 0 0 8 19107 -1361)" gradientUnits="userSpaceOnUse" id="w-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="431" x2="402" y1="122" y2="130" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#w-cl-1)"></line><linearGradient x1="-638.94" x2="-640.22" y1="177.09" y2="177.39" gradientTransform="matrix(24 0 0 13 15783 -2145)" gradientUnits="userSpaceOnUse" id="ac-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="442" x2="418" y1="153" y2="166" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#ac-cl-1)"></line><linearGradient x1="-633.42" x2="-634.7" y1="172.41" y2="173.31" gradientTransform="matrix(20 0 0 19 13137 -3096)" gradientUnits="userSpaceOnUse" id="ab-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="464" x2="444" y1="180" y2="199" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#ab-cl-1)"></line><linearGradient x1="-619.05" x2="-619.52" y1="170.82" y2="171.82" gradientTransform="matrix(13.83 0 0 22.85 9050 -3703.4)" gradientUnits="userSpaceOnUse" id="y-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="491.4" x2="477.5" y1="203" y2="225.9" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#y-cl-1)"></line><linearGradient x1="-578.5" x2="-578.63" y1="170.31" y2="171.31" gradientTransform="matrix(7.5 0 0 24.5 4860 -3953)" gradientUnits="userSpaceOnUse" id="x-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="524.5" x2="517" y1="219.5" y2="244" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#x-cl-1)"></line><linearGradient x1="666.5" x2="666.5" y1="170.31" y2="171.31" gradientTransform="matrix(.5 0 0 24.5 231.5 -3944)" gradientUnits="userSpaceOnUse" id="z-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></linearGradient><line x1="564.5" x2="565" y1="228.5" y2="253" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#z-cl-1)"></line></g></svg></button></div></div></div></div>
    <script type="module" src="/src/main.tsx"></script>
  

</body></html>
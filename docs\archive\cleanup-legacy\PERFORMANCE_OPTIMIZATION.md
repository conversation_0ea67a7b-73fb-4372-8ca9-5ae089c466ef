# Festival Family Performance Optimization Guide

This guide outlines strategies for optimizing the performance of the Festival Family application, focusing on React-specific optimizations, code splitting, lazy loading, and other techniques to ensure a fast and responsive user experience.

## Table of Contents

1. [Component Rendering Optimization](#component-rendering-optimization)
2. [Code Splitting and Lazy Loading](#code-splitting-and-lazy-loading)
3. [State Management Optimization](#state-management-optimization)
4. [Network and Data Fetching](#network-and-data-fetching)
5. [Image and Asset Optimization](#image-and-asset-optimization)
6. [Bundle Size Optimization](#bundle-size-optimization)
7. [Virtualization for Long Lists](#virtualization-for-long-lists)
8. [Performance Monitoring](#performance-monitoring)

## Component Rendering Optimization

### Memoization with React.memo

Use `React.memo` to prevent unnecessary re-renders of components when their props haven't changed:

```tsx
// Before optimization
function FestivalCard({ festival }) {
  // Component implementation
}

// After optimization
const FestivalCard = React.memo(function FestivalCard({ festival }) {
  // Component implementation
})
```

### Custom Comparison Functions

For complex props, provide a custom comparison function to `React.memo`:

```tsx
const FestivalCard = React.memo(
  function FestivalCard({ festival, onSelect }) {
    // Component implementation
  },
  (prevProps, nextProps) => {
    // Return true if passing nextProps to render would return
    // the same result as passing prevProps to render
    return (
      prevProps.festival.id === nextProps.festival.id &&
      prevProps.onSelect === nextProps.onSelect
    )
  }
)
```

### useMemo for Expensive Calculations

Use `useMemo` to cache the results of expensive calculations:

```tsx
function FestivalList({ festivals }) {
  // Without useMemo - recalculated on every render
  const sortedFestivals = [...festivals].sort((a, b) => 
    new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
  )
  
  // With useMemo - only recalculated when festivals changes
  const sortedFestivals = useMemo(() => {
    return [...festivals].sort((a, b) => 
      new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
    )
  }, [festivals])
  
  return (
    <div>
      {sortedFestivals.map(festival => (
        <FestivalCard key={festival.id} festival={festival} />
      ))}
    </div>
  )
}
```

### useCallback for Event Handlers

Use `useCallback` to prevent recreation of function references:

```tsx
function FestivalList({ festivals, onSelectFestival }) {
  // Without useCallback - new function created on every render
  const handleSelect = (festivalId) => {
    onSelectFestival(festivalId)
  }
  
  // With useCallback - function reference preserved between renders
  const handleSelect = useCallback((festivalId) => {
    onSelectFestival(festivalId)
  }, [onSelectFestival])
  
  return (
    <div>
      {festivals.map(festival => (
        <FestivalCard 
          key={festival.id} 
          festival={festival} 
          onSelect={handleSelect}
        />
      ))}
    </div>
  )
}
```

### Avoid Anonymous Functions in Render

Avoid creating anonymous functions in render methods:

```tsx
// Bad practice - creates new function on every render
return (
  <button onClick={() => handleClick(item.id)}>
    Click me
  </button>
)

// Better practice - use useCallback
const handleItemClick = useCallback((id) => {
  handleClick(id)
}, [handleClick])

return (
  <button onClick={() => handleItemClick(item.id)}>
    Click me
  </button>
)

// Best practice - move the component out and pass the id as a prop
function ItemButton({ id, onClick, children }) {
  const handleClick = useCallback(() => {
    onClick(id)
  }, [id, onClick])
  
  return <button onClick={handleClick}>{children}</button>
}

// Usage
return <ItemButton id={item.id} onClick={handleClick}>Click me</ItemButton>
```

## Code Splitting and Lazy Loading

### Route-Based Code Splitting

Split your code by routes to reduce the initial bundle size:

```tsx
// src/main.tsx
import React, { lazy, Suspense } from 'react'
import { createBrowserRouter } from 'react-router-dom'
import LoadingSpinner from './components/ui/LoadingSpinner'

// Lazy load pages
const Home = lazy(() => import('./pages/Home'))
const Activities = lazy(() => import('./pages/Activities'))
const FamHub = lazy(() => import('./pages/FamHub'))
const Discover = lazy(() => import('./pages/Discover'))
const Profile = lazy(() => import('./pages/Profile'))
const Auth = lazy(() => import('./pages/Auth'))

const router = createBrowserRouter([
  {
    path: '/auth',
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <Auth />
      </Suspense>
    ),
  },
  {
    path: '/',
    element: <Layout />,
    children: [
      { 
        index: true, 
        element: (
          <Suspense fallback={<LoadingSpinner />}>
            <Home />
          </Suspense>
        ) 
      },
      { 
        path: 'activities', 
        element: (
          <Suspense fallback={<LoadingSpinner />}>
            <Activities />
          </Suspense>
        ) 
      },
      // Other routes...
    ],
  },
])
```

### Component-Level Code Splitting

Split large components that aren't immediately needed:

```tsx
// src/components/festivals/FestivalMap.tsx
import React, { lazy, Suspense } from 'react'
import LoadingSpinner from '../ui/LoadingSpinner'

// Lazy load the map component which might be large
const MapComponent = lazy(() => import('./MapComponent'))

function FestivalMap({ location }) {
  const [showMap, setShowMap] = useState(false)
  
  return (
    <div>
      <button onClick={() => setShowMap(true)}>Show Map</button>
      
      {showMap && (
        <Suspense fallback={<LoadingSpinner />}>
          <MapComponent location={location} />
        </Suspense>
      )}
    </div>
  )
}
```

### Prefetching

Prefetch components that are likely to be needed soon:

```tsx
// src/pages/FestivalList.tsx
import React, { lazy, Suspense, useEffect } from 'react'

const FestivalDetails = lazy(() => import('./FestivalDetails'))

function FestivalList({ festivals }) {
  // Prefetch FestivalDetails when FestivalList is mounted
  useEffect(() => {
    const prefetchFestivalDetails = import('./FestivalDetails')
  }, [])
  
  // Component implementation
}
```

## State Management Optimization

### Use Local State When Possible

Keep state as local as possible to minimize re-renders:

```tsx
// Bad practice - global state for local UI state
const useUIStore = create((set) => ({
  isModalOpen: false,
  toggleModal: () => set((state) => ({ isModalOpen: !state.isModalOpen })),
}))

function Modal() {
  const { isModalOpen, toggleModal } = useUIStore()
  // Component implementation
}

// Good practice - local state for UI elements
function Modal() {
  const [isOpen, setIsOpen] = useState(false)
  const toggle = () => setIsOpen(!isOpen)
  // Component implementation
}
```

### Normalize Complex State

Normalize complex state to avoid deep nesting and make updates more efficient:

```tsx
// Before normalization
const [festivals, setFestivals] = useState([
  {
    id: '1',
    name: 'Festival A',
    activities: [
      { id: 'a1', name: 'Activity 1' },
      { id: 'a2', name: 'Activity 2' },
    ],
  },
  // More festivals...
])

// After normalization
const [festivals, setFestivals] = useState({
  entities: {
    '1': { id: '1', name: 'Festival A', activityIds: ['a1', 'a2'] },
    // More festivals...
  },
  ids: ['1', '2', '3'],
})

const [activities, setActivities] = useState({
  entities: {
    'a1': { id: 'a1', name: 'Activity 1', festivalId: '1' },
    'a2': { id: 'a2', name: 'Activity 2', festivalId: '1' },
    // More activities...
  },
  ids: ['a1', 'a2', 'a3'],
})
```

### Selective State Updates

Update only the parts of state that have changed:

```tsx
// Inefficient - recreates the entire array
const updateFestival = (id, updates) => {
  setFestivals(festivals.map(festival => 
    festival.id === id ? { ...festival, ...updates } : festival
  ))
}

// Efficient - updates only the changed festival
const updateFestival = (id, updates) => {
  setFestivals(prev => ({
    ...prev,
    entities: {
      ...prev.entities,
      [id]: {
        ...prev.entities[id],
        ...updates,
      },
    },
  }))
}
```

### Use Selectors with React Query

Use selectors to extract only the needed data from query results:

```tsx
// src/hooks/festivals/useFestival.ts
import { useQuery } from '@tanstack/react-query'
import { festivalService } from '@/lib/supabase/services/festival-service'

export function useFestival(id, selector) {
  return useQuery({
    queryKey: ['festivals', id],
    queryFn: async () => {
      const { data, error } = await festivalService.getFestival(id)
      if (error) throw error
      return data
    },
    select: selector, // Apply selector to the data
  })
}

// Usage
function FestivalHeader({ festivalId }) {
  // Only select the name and date, not the entire festival object
  const { data } = useFestival(festivalId, festival => ({
    name: festival.name,
    startDate: festival.start_date,
  }))
  
  return (
    <div>
      <h1>{data?.name}</h1>
      <p>{data?.startDate}</p>
    </div>
  )
}
```

## Network and Data Fetching

### Implement Request Deduplication

Use React Query to automatically deduplicate identical requests:

```tsx
// src/hooks/festivals/useFestivals.ts
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { festivalService } from '@/lib/supabase/services/festival-service'

export function useFestivals() {
  return useQuery({
    queryKey: ['festivals'],
    queryFn: async () => {
      const { data, error } = await festivalService.getFestivals()
      if (error) throw error
      return data
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

// Usage in multiple components
function FestivalList() {
  const { data: festivals } = useFestivals()
  // Component implementation
}

function FestivalCount() {
  const { data: festivals } = useFestivals()
  return <div>Total Festivals: {festivals?.length || 0}</div>
}
```

### Implement Caching

Configure React Query with appropriate caching settings:

```tsx
// src/main.tsx
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      cacheTime: 1000 * 60 * 30, // 30 minutes
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
})

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <App />
    </QueryClientProvider>
  </React.StrictMode>,
)
```

### Optimize Data Fetching

Fetch only the data you need:

```tsx
// Inefficient - fetches all fields
const { data: festivals } = await supabase.from('festivals').select('*')

// Efficient - fetches only needed fields
const { data: festivals } = await supabase
  .from('festivals')
  .select('id, name, start_date, image_url')
```

### Implement Pagination

Use pagination for large datasets:

```tsx
// src/hooks/festivals/usePaginatedFestivals.ts
import { useInfiniteQuery } from '@tanstack/react-query'
import { festivalService } from '@/lib/supabase/services/festival-service'

export function usePaginatedFestivals(pageSize = 10) {
  return useInfiniteQuery({
    queryKey: ['festivals', 'paginated', pageSize],
    queryFn: async ({ pageParam = 0 }) => {
      const { data, error } = await festivalService.getPaginatedFestivals(pageParam, pageSize)
      if (error) throw error
      return data
    },
    getNextPageParam: (lastPage, allPages) => {
      return lastPage.length === pageSize ? allPages.length : undefined
    },
  })
}

// Usage
function FestivalList() {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = usePaginatedFestivals()
  
  return (
    <div>
      {data?.pages.map((page, i) => (
        <React.Fragment key={i}>
          {page.map(festival => (
            <FestivalCard key={festival.id} festival={festival} />
          ))}
        </React.Fragment>
      ))}
      
      {hasNextPage && (
        <button
          onClick={() => fetchNextPage()}
          disabled={isFetchingNextPage}
        >
          {isFetchingNextPage ? 'Loading more...' : 'Load more'}
        </button>
      )}
    </div>
  )
}
```

## Image and Asset Optimization

### Responsive Images

Use responsive images to load appropriately sized images:

```tsx
// src/components/festivals/FestivalImage.tsx
function FestivalImage({ festival }) {
  return (
    <div className="relative h-48 w-full">
      <img
        src={festival.image_url}
        srcSet={`
          ${festival.image_url_small} 400w,
          ${festival.image_url_medium} 800w,
          ${festival.image_url} 1200w
        `}
        sizes="(max-width: 640px) 400px, (max-width: 1024px) 800px, 1200px"
        alt={festival.name}
        className="h-full w-full object-cover rounded-t-lg"
        loading="lazy"
      />
    </div>
  )
}
```

### Lazy Loading Images

Use the `loading="lazy"` attribute for images:

```tsx
<img
  src={festival.image_url}
  alt={festival.name}
  loading="lazy"
  className="h-full w-full object-cover"
/>
```

### Image Optimization with Vite

Configure Vite to optimize images:

```js
// vite.config.js
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { imagetools } from 'vite-imagetools'

export default defineConfig({
  plugins: [
    react(),
    imagetools({
      defaultDirectives: new URLSearchParams('?format=webp&quality=80'),
    }),
  ],
})
```

### Use Modern Image Formats

Use modern image formats like WebP:

```tsx
// src/components/festivals/FestivalImage.tsx
function FestivalImage({ festival }) {
  return (
    <picture>
      <source
        srcSet={festival.image_url_webp}
        type="image/webp"
      />
      <source
        srcSet={festival.image_url}
        type="image/jpeg"
      />
      <img
        src={festival.image_url}
        alt={festival.name}
        loading="lazy"
        className="h-full w-full object-cover"
      />
    </picture>
  )
}
```

## Bundle Size Optimization

### Tree Shaking

Ensure your bundler can perform tree shaking by using ES modules:

```tsx
// Bad - imports the entire library
import _ from 'lodash'

// Good - imports only what's needed
import { debounce } from 'lodash-es'
```

### Import Cost Analysis

Use the Import Cost extension in VS Code to monitor the size of imported packages:

```tsx
// Large import (avoid)
import { Button, Card, Avatar, ... } from '@mui/material'

// Smaller, targeted imports (prefer)
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
```

### Replace Heavy Libraries

Replace heavy libraries with lighter alternatives:

```tsx
// Heavy library
import moment from 'moment' // ~300KB

// Lighter alternative
import { format, parseISO } from 'date-fns' // ~15KB for these functions
```

### Use Bundle Analyzer

Use the Bundle Analyzer to identify large dependencies:

```bash
# Install the plugin
npm install --save-dev rollup-plugin-visualizer

# Add to vite.config.js
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { visualizer } from 'rollup-plugin-visualizer'

export default defineConfig({
  plugins: [
    react(),
    visualizer({
      open: true,
      gzipSize: true,
      brotliSize: true,
    }),
  ],
})
```

## Virtualization for Long Lists

### React Window for Long Lists

Use `react-window` to virtualize long lists:

```tsx
// src/components/festivals/VirtualizedFestivalList.tsx
import { FixedSizeList } from 'react-window'
import AutoSizer from 'react-virtualized-auto-sizer'

function VirtualizedFestivalList({ festivals }) {
  const Row = ({ index, style }) => (
    <div style={style}>
      <FestivalCard festival={festivals[index]} />
    </div>
  )
  
  return (
    <div style={{ height: '600px' }}>
      <AutoSizer>
        {({ height, width }) => (
          <FixedSizeList
            height={height}
            width={width}
            itemCount={festivals.length}
            itemSize={200} // Height of each item
          >
            {Row}
          </FixedSizeList>
        )}
      </AutoSizer>
    </div>
  )
}
```

### TanStack Virtual

Use TanStack Virtual for more flexible virtualization:

```tsx
// src/components/festivals/TanStackVirtualList.tsx
import { useVirtualizer } from '@tanstack/react-virtual'

function TanStackVirtualList({ festivals }) {
  const parentRef = useRef(null)
  
  const rowVirtualizer = useVirtualizer({
    count: festivals.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 200, // Height of each item
  })
  
  return (
    <div
      ref={parentRef}
      style={{ height: '600px', overflow: 'auto' }}
    >
      <div
        style={{
          height: `${rowVirtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {rowVirtualizer.getVirtualItems().map(virtualRow => (
          <div
            key={virtualRow.index}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: `${virtualRow.size}px`,
              transform: `translateY(${virtualRow.start}px)`,
            }}
          >
            <FestivalCard festival={festivals[virtualRow.index]} />
          </div>
        ))}
      </div>
    </div>
  )
}
```

## Performance Monitoring

### React DevTools Profiler

Use the React DevTools Profiler to identify performance issues:

1. Open React DevTools in your browser
2. Go to the Profiler tab
3. Click the record button and interact with your app
4. Analyze the results to identify components that render too often or take too long to render

### Web Vitals Monitoring

Monitor Web Vitals to track real-world performance:

```tsx
// src/main.tsx
import { onCLS, onFID, onLCP } from 'web-vitals'

function sendToAnalytics({ name, delta, value, id }) {
  // Send to your analytics service
  console.log({ name, delta, value, id })
}

onCLS(sendToAnalytics)
onFID(sendToAnalytics)
onLCP(sendToAnalytics)
```

### Performance Budget

Establish a performance budget for your application:

```js
// vite.config.js
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { visualizer } from 'rollup-plugin-visualizer'
import { budgetPlugin } from 'vite-plugin-bundle-budget'

export default defineConfig({
  plugins: [
    react(),
    visualizer(),
    budgetPlugin({
      budget: {
        // Maximum bundle size in KB
        maximumBundleSize: 300,
        // Maximum initial JS size in KB
        maximumInitialJsSize: 150,
        // Maximum initial CSS size in KB
        maximumInitialCssSize: 50,
      },
    }),
  ],
})
```

### Lighthouse CI

Set up Lighthouse CI to monitor performance in your CI/CD pipeline:

```yaml
# .github/workflows/lighthouse.yml
name: Lighthouse CI
on: [push]
jobs:
  lighthouse:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js
        uses: actions/setup-node@v1
        with:
          node-version: '16.x'
      - name: Install dependencies
        run: npm ci
      - name: Build
        run: npm run build
      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli
          lhci autorun
```

---

By implementing these performance optimization strategies, the Festival Family app will provide a faster, more responsive user experience. These optimizations address various aspects of performance, from component rendering to network requests and bundle size, ensuring that the application remains performant as it grows in complexity.

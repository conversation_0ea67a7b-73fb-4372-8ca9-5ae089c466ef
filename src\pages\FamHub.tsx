import React, { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle, Users, BookOpen, Info, Grid3X3, List } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { ErrorBoundary } from 'react-error-boundary';
import { AlertCircle, RefreshCw, Search, Filter, Heart, Share2, MapPin, Clock, Star, X } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { simulateHapticFeedback, isMobileViewport, createTouchHandler } from '../utils/mobileUX';
import MobileUXTester from '../components/testing/MobileUXTester';
import { useCommunities, useLocalInfo, useResources, useChatLinks } from '@/hooks/useUnifiedData';
import { usePersonalization, type PersonalizedContent } from '@/hooks/usePersonalization';
import { useFavorites } from '@/hooks/useFavorites';
import TipDetailsModal from '@/components/tips/TipDetailsModal';
import GuideDetailsModal from '@/components/guides/GuideDetailsModal';
import FAQDetailsModal from '@/components/faqs/FAQDetailsModal';
import { UnifiedModal } from '@/components/design-system/UnifiedModal';
// Import unified design system components
import {
  PageWrapper,
  UnifiedCard,
  UnifiedButton,
  UnifiedBadge,
  UnifiedFilterBar,
  GridLayout,
  FlexLayout
} from '@/components/design-system';
import { BentoCard, BentoGrid } from '@/components/design-system/BentoGrid';
import { useActivityTracking } from '@/hooks/useUnifiedTracking';

type Tab = 'CHAT' | 'COMMUNITIES' | 'RESOURCES' | 'LOCAL_INFO';
type ViewMode = 'grid' | 'list';

const filterTabs = [
  { id: 'CHAT', label: 'Chat', icon: MessageCircle, colorVariant: 'default' as const },
  { id: 'COMMUNITIES', label: 'Communities', icon: Users, colorVariant: 'info' as const },
  { id: 'RESOURCES', label: 'Resources', icon: BookOpen, colorVariant: 'success' as const },
  { id: 'LOCAL_INFO', label: 'Local Info', icon: Info, colorVariant: 'warning' as const },
];

// Chat Section Component with Unified Data Service
const ChatSection = () => {
  const { data: chatLinksData, isLoading: loading, error } = useChatLinks();

  // Transform data to match expected format with fallback
  const chatLinks = React.useMemo(() => {
    if (chatLinksData.length > 0) {
      return chatLinksData;
    }

    // Fallback to default chat options if no data
    return [
      {
        id: 'whatsapp',
        title: 'WhatsApp Group',
        description: 'Join our main WhatsApp community for real-time chat',
        url: 'https://wa.me/groupinvite',
        category: 'CHAT'
      },
      {
        id: 'discord',
        title: 'Discord Server',
        description: 'Connect on Discord for voice and text chat',
        url: 'https://discord.gg/festivalfamily',
        category: 'CHAT'
      },
      {
        id: 'telegram',
        title: 'Telegram Channel',
        description: 'Get updates and chat on Telegram',
        url: 'https://t.me/festivalfamily',
        category: 'CHAT'
      }
    ];
  }, [chatLinksData]);

  // Track external link clicks for analytics
  const handleChatLinkClick = async (chat: any) => {
    try {
      // Note: This would be implemented with the unified data service in a real scenario
      console.log('Link click tracked:', chat.id);
    } catch (error) {
      // Don't block the user if tracking fails
      console.warn('Failed to track link click:', error);
    }

    // Open the external link
    window.open(chat.url, '_blank', 'noopener,noreferrer');
  };

  if (loading) {
    return (
      <Card className="bg-card border border-border">
        <CardContent className="p-6 text-center">
          <div className="text-muted-foreground">Loading chat options...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <MessageCircle className="w-8 h-8 mx-auto text-primary mb-2" />
        <h3 className="text-lg font-semibold text-foreground">Festival Chat Groups</h3>
        <p className="text-muted-foreground text-sm">Connect with fellow festival-goers on your favorite platform</p>
      </div>
      
      {/* AuthenticatedHome 4-Card Pattern: Full-Space Chat Grid */}
      <BentoGrid cols={2} gap="sm">
        {chatLinks.map((chat, index) => {
          // Enhanced 2025 Visual Excellence Color Pattern - Stronger, More Vibrant
          const getColorTheme = (index: number) => {
            const colorThemes = [
              'bg-gradient-to-br from-blue-500/20 to-blue-600/25 border-blue-500/30 shadow-lg shadow-blue-500/10',
              'bg-gradient-to-br from-emerald-500/20 to-emerald-600/25 border-emerald-500/30 shadow-lg shadow-emerald-500/10',
              'bg-gradient-to-br from-violet-500/20 to-violet-600/25 border-violet-500/30 shadow-lg shadow-violet-500/10',
              'bg-gradient-to-br from-orange-500/20 to-orange-600/25 border-orange-500/30 shadow-lg shadow-orange-500/10',
              'bg-gradient-to-br from-rose-500/20 to-rose-600/25 border-rose-500/30 shadow-lg shadow-rose-500/10',
              'bg-gradient-to-br from-amber-500/20 to-amber-600/25 border-amber-500/30 shadow-lg shadow-amber-500/10',
              'bg-gradient-to-br from-fuchsia-500/20 to-fuchsia-600/25 border-fuchsia-500/30 shadow-lg shadow-fuchsia-500/10',
              'bg-gradient-to-br from-cyan-500/20 to-cyan-600/25 border-cyan-500/30 shadow-lg shadow-cyan-500/10'
            ];
            return colorThemes[index % colorThemes.length];
          };

          return (
            <BentoCard
              key={chat.id}
              title={chat.title}
              description={chat.description || 'Join this chat group to connect with other festival enthusiasts'}
              variant="glassmorphism"
              interactive
              onClick={() => handleChatLinkClick(chat)}
              className={getColorTheme(index)}
              icon={<span className="text-lg">💬</span>}
              action={
                <UnifiedBadge variant="category" className="text-xs">
                  {chat.category}
                </UnifiedBadge>
              }
            >
              <UnifiedButton
                onClick={() => handleChatLinkClick(chat)}
                className="w-full mt-2"
                size="sm"
                variant="primary"
              >
                Join Chat
              </UnifiedButton>
            </BentoCard>
          );
        })}
      </BentoGrid>
      
      {chatLinks.length === 0 && (
        <Card className="bg-card border border-border">
          <CardContent className="p-6 text-center">
            <MessageCircle className="w-12 h-12 mx-auto text-primary mb-4" />
            <h3 className="text-xl font-semibold mb-2 text-card-foreground">No Chat Groups Available</h3>
            <p className="text-muted-foreground">Check back later for chat group links</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

// Enhanced Mobile-First Communities Section Component
const CommunitiesSection = ({
  viewMode,
  searchQuery,
  favorites,
  onFavoriteToggle,
  isMobile
}: {
  viewMode: ViewMode;
  searchQuery: string;
  favorites: Set<string>;
  onFavoriteToggle: (id: string) => void;
  isMobile: boolean;
}) => {
  const { data: communities, isLoading: loading, error } = useCommunities();

  // Transform communities data to match expected format
  const transformedCommunities = React.useMemo(() => {
    return communities.map(community => ({
      id: community.id,
      name: community.name,
      description: community.description || 'Join this community to connect with fellow festival-goers.',
      members: community.member_count || 0,
      category: community.category || 'General',
      featured: community.is_featured || false,
      externalLink: community.external_url,
      type: community.type || 'external'
    }));
  }, [communities]);

  const filteredCommunities = transformedCommunities.filter(community =>
    community.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    community.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    community.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCommunityClick = useCallback((community: any) => {
    simulateHapticFeedback('light');

    // If community has external link, open it
    if (community.externalLink?.url) {
      window.open(community.externalLink.url, '_blank', 'noopener,noreferrer');
      toast.success(`Opening ${community.externalLink.title || community.name}...`);
    } else {
      toast.success('Opening community...');
    }
  }, []);

  const handleJoinCommunity = useCallback((e: React.MouseEvent, community: any) => {
    e.stopPropagation();
    simulateHapticFeedback('medium');

    // If community has external link, open it
    if (community.externalLink) {
      window.open(community.externalLink, '_blank', 'noopener,noreferrer');
      toast.success(`Joining ${community.name}!`);
    } else if (community.type === 'group') {
      // Handle internal group joining (could link to group page later)
      toast.success(`Joined ${community.name}!`);
    } else {
      toast.success('Joined community!');
    }
  }, []);

  // Show loading state
  if (loading) {
    return (
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="bg-card border border-border h-full animate-pulse">
            <CardContent className="p-4">
              <div className="h-4 bg-muted rounded mb-2"></div>
              <div className="h-3 bg-muted/50 rounded mb-4"></div>
              <div className="h-8 bg-muted/50 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Enhanced 2025 Visual Excellence Color Pattern - Stronger, More Vibrant
  const getColorTheme = (index: number) => {
    const colorThemes = [
      'bg-gradient-to-br from-blue-500/20 to-blue-600/25 border-blue-500/30 shadow-lg shadow-blue-500/10',
      'bg-gradient-to-br from-emerald-500/20 to-emerald-600/25 border-emerald-500/30 shadow-lg shadow-emerald-500/10',
      'bg-gradient-to-br from-violet-500/20 to-violet-600/25 border-violet-500/30 shadow-lg shadow-violet-500/10',
      'bg-gradient-to-br from-orange-500/20 to-orange-600/25 border-orange-500/30 shadow-lg shadow-orange-500/10',
      'bg-gradient-to-br from-rose-500/20 to-rose-600/25 border-rose-500/30 shadow-lg shadow-rose-500/10',
      'bg-gradient-to-br from-amber-500/20 to-amber-600/25 border-amber-500/30 shadow-lg shadow-amber-500/10',
      'bg-gradient-to-br from-fuchsia-500/20 to-fuchsia-600/25 border-fuchsia-500/30 shadow-lg shadow-fuchsia-500/10',
      'bg-gradient-to-br from-cyan-500/20 to-cyan-600/25 border-cyan-500/30 shadow-lg shadow-cyan-500/10'
    ];
    return colorThemes[index % colorThemes.length];
  };

  return (
    <>
      <BentoGrid cols={2} gap="sm">
        {filteredCommunities.map((community, index) => (
          <motion.div
            key={community.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className="group"
          >
            <BentoCard
              title={community.name}
              description={community.description}
              variant="glassmorphism"
              interactive
              onClick={() => handleCommunityClick(community)}
              className={getColorTheme(index)}
              icon={<span className="text-lg">👥</span>}
              action={
                <div className="flex items-center gap-2">
                  <UnifiedBadge variant="category" className="text-xs">
                    {community.category}
                  </UnifiedBadge>
                  {community.featured && (
                    <UnifiedBadge variant="secondary" className="text-xs">
                      Featured
                    </UnifiedBadge>
                  )}
                  <UnifiedButton
                    onClick={(e) => {
                      e.stopPropagation();
                      handleJoinCommunity(e, community);
                    }}
                    size="sm"
                    variant="primary"
                    className="min-h-[44px] px-4 touch-manipulation"
                  >
                    {community.externalLink ? 'Join Now' : 'Join Community'}
                  </UnifiedButton>
                </div>
              }
          >
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <Users className="w-3 h-3" />
                <span>{community.members} members</span>
              </div>
          </BentoCard>
        </motion.div>
      ))}
    </BentoGrid>

    {filteredCommunities.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="col-span-full text-center py-12"
        >
          <Users className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium text-muted-foreground mb-2">No communities found</h3>
          <p className="text-muted-foreground text-sm">
            {searchQuery ? `No communities match "${searchQuery}"` : 'No communities available at the moment'}
          </p>
        </motion.div>
      )}
    </>
  );
};

// Tracked Resource Card Component
const TrackedResourceCard = ({ resource, index = 0, onClick }: { resource: any, index?: number, onClick: (resource: any) => void }) => {
  const { trackView } = useActivityTracking();

  // Track view when component mounts
  useEffect(() => {
    if (resource.id && resource.type) {
      console.log(`🎯 FamHub Resource: Tracking view for ${resource.type} ${resource.id} (${(resource as any).title || (resource as any).question})`);
      trackView(resource.type, resource.id).catch(console.error);
    }
  }, [resource.id, resource.type, trackView]);

  const getResourceIcon = (type: string) => {
    switch (type) {
      case 'tip': return '💡';
      case 'guide': return '📖';
      case 'faq': return '❓';
      default: return '📄';
    }
  };

  const getResourceTypeLabel = (type: string) => {
    switch (type) {
      case 'tip': return 'Tip';
      case 'guide': return 'Guide';
      case 'faq': return 'FAQ';
      default: return 'Resource';
    }
  };

  // Enhanced 2025 Visual Excellence Color Pattern - Stronger, More Vibrant
  const getColorTheme = (index: number) => {
    const colorThemes = [
      'bg-gradient-to-br from-blue-500/20 to-blue-600/25 border-blue-500/30 shadow-lg shadow-blue-500/10',
      'bg-gradient-to-br from-emerald-500/20 to-emerald-600/25 border-emerald-500/30 shadow-lg shadow-emerald-500/10',
      'bg-gradient-to-br from-violet-500/20 to-violet-600/25 border-violet-500/30 shadow-lg shadow-violet-500/10',
      'bg-gradient-to-br from-orange-500/20 to-orange-600/25 border-orange-500/30 shadow-lg shadow-orange-500/10',
      'bg-gradient-to-br from-rose-500/20 to-rose-600/25 border-rose-500/30 shadow-lg shadow-rose-500/10',
      'bg-gradient-to-br from-amber-500/20 to-amber-600/25 border-amber-500/30 shadow-lg shadow-amber-500/10',
      'bg-gradient-to-br from-fuchsia-500/20 to-fuchsia-600/25 border-fuchsia-500/30 shadow-lg shadow-fuchsia-500/10',
      'bg-gradient-to-br from-cyan-500/20 to-cyan-600/25 border-cyan-500/30 shadow-lg shadow-cyan-500/10'
    ];
    return colorThemes[index % colorThemes.length];
  };

  return (
    <BentoCard
      key={`${resource.type}-${resource.id}`}
      title={(resource as any).title || (resource as any).question}
      description={(resource as any).description || (resource as any).answer || (resource as any).content?.substring(0, 100) + '...'}
      variant="glassmorphism"
      interactive
      onClick={() => onClick(resource)}
      className={getColorTheme(index)}
      icon={<span className="text-lg">{getResourceIcon(resource.type)}</span>}
      action={
        <div className="flex items-center gap-2">
          <UnifiedBadge variant="secondary" className="text-xs">
            {getResourceTypeLabel(resource.type)}
          </UnifiedBadge>
          {resource.is_featured && (
            <UnifiedBadge variant="priority" className="text-xs border-festival-warning text-festival-warning">
              Featured
            </UnifiedBadge>
          )}
          <UnifiedButton
            onClick={(e) => {
              e.stopPropagation();
              onClick(resource);
            }}
            size="sm"
            variant="outline"
            className="text-xs min-h-[44px] px-4 touch-manipulation"
          >
            Read More
          </UnifiedButton>
        </div>
      }
    >
      <div className="flex items-center justify-between text-xs text-muted-foreground">
        <span>{resource.view_count || 0} views</span>
        <div className="flex items-center gap-2">
          <span>Click to read more</span>
          {resource.is_featured && (
            <span className="text-festival-warning">⭐</span>
          )}
        </div>
      </div>
    </BentoCard>
  );
};

// Resources Section Component with Unified Data Service
const ResourcesSection = () => {
  const { data: resources, isLoading: loading, error } = useResources();
  const [selectedTip, setSelectedTip] = useState<any>(null);
  const [selectedGuide, setSelectedGuide] = useState<any>(null);
  const [selectedFaq, setSelectedFaq] = useState<any>(null);

  const handleResourceClick = (resource: any) => {
    if (resource.type === 'tip') {
      setSelectedTip(resource);
    } else if (resource.type === 'guide') {
      setSelectedGuide(resource);
    } else if (resource.type === 'faq') {
      setSelectedFaq(resource);
    }
  };

  const getResourceIcon = (type: string) => {
    switch (type) {
      case 'tip': return '💡';
      case 'guide': return '📖';
      case 'faq': return '❓';
      default: return '📄';
    }
  };

  const getResourceTypeLabel = (type: string) => {
    switch (type) {
      case 'tip': return 'Tip';
      case 'guide': return 'Guide';
      case 'faq': return 'FAQ';
      default: return 'Resource';
    }
  };

  if (loading) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        Loading resources...
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        <div className="flex flex-wrap gap-3 mb-4">
          <UnifiedBadge variant="category" className="bg-primary/50 hover:bg-primary cursor-pointer">
            Tips
          </UnifiedBadge>
          <UnifiedBadge variant="category" className="bg-primary/50 hover:bg-primary cursor-pointer">
            Guides
          </UnifiedBadge>
          <UnifiedBadge variant="category" className="bg-primary/50 hover:bg-primary cursor-pointer">
            FAQs
          </UnifiedBadge>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('/resources', '_blank')}
          >
            View All Resources
          </Button>
        </div>

        {/* AuthenticatedHome 4-Card Pattern: Full-Space Resources Grid */}
        <BentoGrid cols={2} gap="sm">
          {resources.map((resource, index) => (
            <TrackedResourceCard
              key={`${resource.type}-${resource.id}`}
              resource={resource}
              index={index}
              onClick={handleResourceClick}
            />
          ))}
        </BentoGrid>

        {resources.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            No resources available yet. Check back soon!
          </div>
        )}
      </div>

      {/* Import and use the modal components */}
      {selectedTip && (
        <TipDetailsModal
          tip={selectedTip}
          isOpen={!!selectedTip}
          onClose={() => setSelectedTip(null)}
        />
      )}
      {selectedGuide && (
        <GuideDetailsModal
          guide={selectedGuide}
          isOpen={!!selectedGuide}
          onClose={() => setSelectedGuide(null)}
        />
      )}
      {selectedFaq && (
        <FAQDetailsModal
          faq={selectedFaq}
          isOpen={!!selectedFaq}
          onClose={() => setSelectedFaq(null)}
        />
      )}
    </>
  );
};

// Local Info Section Component with Location-based Content
const LocalInfoSection = ({
  viewMode,
  searchQuery,
  favorites,
  onFavoriteToggle,
  isMobile
}: {
  viewMode: ViewMode;
  searchQuery: string;
  favorites: Set<string>;
  onFavoriteToggle: (id: string) => void;
  isMobile: boolean;
}) => {
  const { data: localInfoData, isLoading: loading, error } = useLocalInfo();
  const [selectedItem, setSelectedItem] = React.useState<any>(null);
  const [userLocation, setUserLocation] = React.useState<string>('');

  // Get user's location context (could be from profile, current festival, etc.)
  const locationContext = React.useMemo(() => {
    // Try to get location from user profile or current context
    // For now, we'll use a general context but this could be enhanced
    return {
      userLocation: userLocation || undefined,
      // Could add festival context if user is attending a specific festival
      // festivalLocation: currentFestival?.location,
    };
  }, [userLocation]);

  // Transform data to match expected format
  const localInfo = React.useMemo(() => {
    return localInfoData.map(item => ({
      id: item.id,
      title: item.title,
      description: item.description || '',
      category: item.category.charAt(0).toUpperCase() + item.category.slice(1),
      type: item.category as 'venue' | 'accommodation' | 'transport',
      link: item.link,
      is_featured: item.is_featured || false,
      priority: item.priority || 0
    }));
  }, [localInfoData]);

  // Filter local info based on search query
  const filteredLocalInfo = localInfo.filter(item =>
    item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (item.description && item.description.toLowerCase().includes(searchQuery.toLowerCase())) || // Add null check
    item.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleItemClick = useCallback((item: any) => {
    try {
      simulateHapticFeedback('light');
      if (item?.link) {
        window.open(item.link, '_blank', 'noopener,noreferrer');
        toast.success(`Opening ${item.title || 'local info'}...`);
      } else if (item) {
        setSelectedItem(item);
      }
    } catch (error) {
      console.error('Error handling local info click:', error);
      toast.error('Unable to open local info');
    }
  }, []);

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'transportation': return '🚌';
      case 'food': return '🍕';
      case 'accommodation': return '🏨';
      case 'safety': return '🚨';
      case 'weather': return '🌤️';
      case 'attractions': return '🎡';
      default: return '📍';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'transportation': return 'bg-festival-purple-500/20';
      case 'food': return 'bg-festival-orange-500/20';
      case 'accommodation': return 'bg-success-500/20';
      case 'safety': return 'bg-error-500/20';
      case 'weather': return 'bg-festival-teal-500/20';
      case 'attractions': return 'bg-festival-purple-600/20';
      default: return 'bg-secondary/20';
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="bg-card border border-border h-full animate-pulse">
            <CardContent className="p-4">
              <div className="h-4 bg-muted rounded mb-2"></div>
              <div className="h-3 bg-muted/50 rounded mb-4"></div>
              <div className="h-8 bg-muted/50 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Import the enhanced LocalInfo component
  const LocalInfoComponent = React.lazy(() => import('@/components/famhub/LocalInfo'));

  return (
    <>
      <React.Suspense fallback={
        <div className="animate-pulse">
          <div className="bg-white/5 rounded-lg p-4 border border-white/10">
            <div className="h-4 bg-white/10 rounded mb-2"></div>
            <div className="h-3 bg-white/10 rounded mb-2"></div>
            <div className="h-3 bg-white/10 rounded w-1/2"></div>
          </div>
        </div>
      }>
        <LocalInfoComponent
          items={filteredLocalInfo}
          locationContext={locationContext}
          showLocationFilter={true}
          maxItems={viewMode === 'grid' ? 9 : 6}
        />
      </React.Suspense>

      {/* Fallback to original implementation if needed */}
      <div className={`grid gap-4 ${viewMode === 'grid' ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'} hidden`}>
        {filteredLocalInfo.map((item, index) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className="group"
          >
            <Card
              className="bg-card border border-border h-full cursor-pointer hover:bg-card/80 transition-all duration-300 overflow-hidden"
              {...createTouchHandler(() => handleItemClick(item))}
            >
              <CardContent className={`p-4 ${viewMode === 'list' ? 'flex items-center gap-4' : 'flex flex-col gap-4'}`}>
                {/* Local Info Header */}
                <div className={`flex items-start justify-between ${viewMode === 'list' ? 'flex-shrink-0' : 'w-full'}`}>
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{getCategoryIcon(item.category)}</span>
                    {item.is_featured && (
                      <Badge className="bg-yellow-500/20 text-yellow-300 text-xs">
                        Featured
                      </Badge>
                    )}
                  </div>
                  {/* Note: Favorites not supported for external links - user_favorites table is activity-specific */}
                </div>

                {/* Local Info Content */}
                <div className={`${viewMode === 'list' ? 'flex-1' : 'w-full'}`}>
                  <div className="flex items-center gap-2 mb-2">
                    <CardTitle className="text-lg">{item.title}</CardTitle>
                  </div>

                  <Badge className={`${getCategoryColor(item.category)} text-primary-foreground mb-3`}>
                    {item.category}
                  </Badge>

                  <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                    {item.description}
                  </p>

                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">
                      {item.link ? 'Click to visit link' : 'Click for details'}
                    </span>
                    {item.link && (
                      <Badge variant="outline" className="border-border text-muted-foreground text-xs">
                        External Link
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}

        {filteredLocalInfo.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="col-span-full text-center py-12"
          >
            <Info className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-muted-foreground mb-2">No local info found</h3>
            <p className="text-muted-foreground text-sm">
              {searchQuery ? `No local info matches "${searchQuery}"` : 'No local information available at the moment'}
            </p>
          </motion.div>
        )}
      </div>

      {/* Local Info Detail Modal */}
      <UnifiedModal
        open={!!selectedItem}
        onClose={() => setSelectedItem(null)}
        title={selectedItem?.title || 'Local Information'}
        itemId={selectedItem?.id || 'local-info'}
        contentType="local_info"
        category={selectedItem?.category || 'general'}
        itemType="local_info"
        size="md"
        showCloseButton={true}
      >
        {selectedItem && (
          <div className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <span className="text-2xl">{getCategoryIcon(selectedItem.category)}</span>
              <h3 className="text-xl font-bold text-foreground">{selectedItem.title}</h3>
            </div>

            <Badge className={`${getCategoryColor(selectedItem.category)} text-primary-foreground mb-4`}>
              {selectedItem.category}
            </Badge>

            <p className="text-muted-foreground mb-6">{selectedItem.description}</p>

            <div className="flex gap-2">
              {selectedItem.link && (
                <Button
                  onClick={() => {
                    window.open(selectedItem.link, '_blank', 'noopener,noreferrer');
                    setSelectedItem(null);
                  }}
                  className="bg-purple-700 hover:bg-purple-600"
                >
                  Visit Link
                </Button>
              )}
              <Button
                variant="outline"
                onClick={() => setSelectedItem(null)}
              >
                Close
              </Button>
            </div>
          </div>
        )}
      </UnifiedModal>
    </>
  );
};

// Error fallback component for FamHub page
const FamHubErrorFallback = ({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) => (
  <div className="min-h-screen flex flex-col bg-gradient-to-br from-background via-muted to-card">
    <div className="container mx-auto py-8 px-4">
      <Card className="p-6 bg-card border border-border">
        <CardContent className="text-center">
          <AlertCircle className="h-12 w-12 mx-auto text-destructive mb-4" />
          <h2 className="text-2xl font-bold mb-2 text-card-foreground">Unable to Load FamHub</h2>
          <p className="text-muted-foreground mb-6">
            We're having trouble loading the community features. This might be a temporary issue.
          </p>
          <div className="bg-red-500/20 rounded-md p-4 mb-6">
            <p className="text-sm text-red-300">{error.message}</p>
          </div>
          <Button
            onClick={resetErrorBoundary}
            className="bg-primary hover:bg-primary/90"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    </div>
  </div>
);

const FamHub: React.FC = () => {
  const [activeTab, setActiveTab] = useState<Tab>('CHAT');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');

  // Mobile-specific state
  const [isMobile, setIsMobile] = useState(false);

  // Personalization
  const {
    getPersonalizedTips,
    getPersonalizedGuides,
    sortByPersonalization
  } = usePersonalization();
  const [personalizedContent, setPersonalizedContent] = useState<{
    tips: PersonalizedContent<any>[];
    guides: PersonalizedContent<any>[];
  }>({ tips: [], guides: [] });
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  // Database-driven favorites system
  const { favorites, toggleFavorite: handleFavoriteToggle, isFavorite } = useFavorites('external_link');

  // Modal states for personalized content
  const [selectedPersonalizedTip, setSelectedPersonalizedTip] = useState<any>(null);
  const [selectedPersonalizedGuide, setSelectedPersonalizedGuide] = useState<any>(null);

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => setIsMobile(isMobileViewport());
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Fetch personalized content
  const fetchPersonalizedContent = useCallback(async () => {
    try {
      const [tips, guides] = await Promise.all([
        getPersonalizedTips(3),
        getPersonalizedGuides(3)
      ]);

      setPersonalizedContent({ tips, guides });
    } catch (error) {
      console.error('Error fetching personalized content:', error);
    }
  }, [getPersonalizedTips, getPersonalizedGuides]);

  // Load personalized content on mount
  useEffect(() => {
    fetchPersonalizedContent();
  }, [fetchPersonalizedContent]);

  // Enhanced tab change with haptic feedback
  const handleTabChange = useCallback((newTab: Tab) => {
    setActiveTab(newTab);
    simulateHapticFeedback('light');
  }, []);

  // View mode toggle with haptic feedback
  const handleViewModeToggle = useCallback(() => {
    setViewMode(prev => prev === 'grid' ? 'list' : 'grid');
    simulateHapticFeedback('light');
  }, []);

  // Pull-to-refresh functionality
  const handleRefresh = useCallback(async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    simulateHapticFeedback('medium');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast.success('Content refreshed!');
    } catch (error) {
      console.error('Refresh failed:', error);
      toast.error('Failed to refresh content');
    } finally {
      setIsRefreshing(false);
    }
  }, [isRefreshing]);

  // Enhanced favorite toggle with haptic feedback and database integration
  const handleFavoriteToggleWithHaptic = useCallback(async (itemId: string) => {
    simulateHapticFeedback('light');
    await handleFavoriteToggle(itemId, 'external_link');
  }, [handleFavoriteToggle]);

  return (
    <ErrorBoundary FallbackComponent={FamHubErrorFallback}>
      <PageWrapper
        title="FamHub"
        subtitle="Connect with your festival family"
        hideTitleOnMobile={true}
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Compact Horizontal Greeting Banner with Refresh */}
          <div className="bg-gradient-to-r from-primary/10 to-accent/10 border border-primary/20 rounded-lg px-4 py-2 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-lg">👥</span>
                <span className="font-medium text-foreground">
                  Connect with Your Tribe
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground hidden sm:block">
                  Find your festival family
                </span>
                <UnifiedButton
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setIsRefreshing(true);
                    setTimeout(() => setIsRefreshing(false), 1000);
                    toast.success('Refreshing FamHub...');
                    simulateHapticFeedback('light');
                  }}
                  disabled={isRefreshing}
                  className="text-xs"
                >
                  <RefreshCw className={`w-3 h-3 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                  <span className="hidden lg:inline">Refresh</span>
                </UnifiedButton>
              </div>
            </div>
          </div>

          {/* Search and Filter Section - Mobile-First Design */}
          <UnifiedCard variant="elevated" className="p-6 mb-6">
            <FlexLayout direction="col" gap="md">
              {/* Search Bar with Integrated Filter Controls */}
              <FlexLayout align="center" gap="sm">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-light" />
                  <input
                    type="text"
                    placeholder={isMobile ? "Search..." : "Search communities..."}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-12 py-2 bg-card border border-neutral-200 rounded-lg text-primary-light placeholder:text-muted-light focus:border-festival-orange focus:outline-none transition-colors"
                    style={{ fontSize: '16px' }} // Prevents zoom on iOS
                  />
                  {/* Mobile: Integrated filter toggle */}
                  {isMobile && (
                    <UnifiedButton
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowFilters(!showFilters)}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1"
                    >
                      <Filter className="w-4 h-4" />
                    </UnifiedButton>
                  )}
                </div>

                {/* Desktop: Side-by-side filter controls */}
                {!isMobile && (
                  <FlexLayout align="center" gap="sm">
                    <UnifiedButton
                      variant="outline"
                      size="sm"
                      onClick={() => setShowFilters(!showFilters)}
                    >
                      <Filter className="w-4 h-4" />
                      <span className="ml-2">Filters</span>
                    </UnifiedButton>

                    <UnifiedButton
                      variant="outline"
                      size="sm"
                      onClick={handleViewModeToggle}
                      aria-label={viewMode === 'grid' ? 'Switch to list view' : 'Switch to grid view'}
                    >
                      {viewMode === 'grid' ? <List className="w-4 h-4" /> : <Grid3X3 className="w-4 h-4" />}
                    </UnifiedButton>
                  </FlexLayout>
                )}
              </FlexLayout>

              {/* Mobile: Collapsible filter panel */}
              {isMobile && showFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.2 }}
                  className="border-t border-border pt-4"
                >
                  <FlexLayout align="center" gap="sm" wrap>
                    <UnifiedButton
                      variant="outline"
                      size="sm"
                      onClick={handleViewModeToggle}
                      aria-label={viewMode === 'grid' ? 'Switch to list view' : 'Switch to grid view'}
                    >
                      {viewMode === 'grid' ? <List className="w-4 h-4" /> : <Grid3X3 className="w-4 h-4" />}
                      <span className="ml-2">{viewMode === 'grid' ? 'List' : 'Grid'}</span>
                    </UnifiedButton>
                  </FlexLayout>
                </motion.div>
              )}
            </FlexLayout>
          </UnifiedCard>

          {/* Personalized Content Section */}
          {personalizedContent.tips.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mb-8"
            >
              <UnifiedCard variant="elevated" className="p-6">
                <FlexLayout justify="between" align="center" className="mb-4">
                  <div>
                    <h2 className="text-xl font-bold text-primary-light">✨ Recommended for You</h2>
                    <p className="text-sm text-muted-light">Tips and guides based on your interests</p>
                  </div>
                  <UnifiedBadge variant="secondary" className="bg-accent/20 text-accent">
                    Personalized
                  </UnifiedBadge>
                </FlexLayout>

                <GridLayout cols={isMobile ? 1 : 2} gap="md">
                  {personalizedContent.tips.slice(0, 2).map((item) => (
                    <BentoCard
                      key={item.item.id}
                      title={item.item.title}
                      description={item.item.description || 'Helpful festival tip'}
                      variant="glassmorphism"
                      interactive
                      onClick={() => setSelectedPersonalizedTip(item.item)}
                      icon={<Star className="w-5 h-5 text-accent" />}
                      className="border-accent/20 bg-accent/5 hover:bg-accent/10"
                    >
                      <div className="text-xs text-accent font-medium">
                        {item.reason}
                      </div>
                    </BentoCard>
                  ))}

                  {personalizedContent.guides.slice(0, 2).map((item) => (
                    <BentoCard
                      key={item.item.id}
                      title={item.item.title}
                      description={item.item.description || 'Comprehensive festival guide'}
                      variant="glassmorphism"
                      interactive
                      onClick={() => setSelectedPersonalizedGuide(item.item)}
                      icon={<BookOpen className="w-5 h-5 text-primary" />}
                      className="border-primary/20 bg-primary/5 hover:bg-primary/10"
                    >
                      <div className="text-xs text-primary font-medium">
                        {item.reason}
                      </div>
                    </BentoCard>
                  ))}
                </GridLayout>
              </UnifiedCard>
            </motion.div>
          )}

          {/* FamHub Filter Bar - Unified Design System */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mb-6"
          >
            <UnifiedFilterBar
              searchValue=""
              onSearchChange={() => {}} // No search functionality needed for FamHub tabs
              filterOptions={filterTabs}
              activeFilter={activeTab}
              onFilterChange={(filterId) => handleTabChange(filterId as Tab)}
              showSearch={false} // Hide search for tab navigation
              className="w-full"
            />
          </motion.div>

          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              {activeTab === 'CHAT' && <ChatSection />}
              {activeTab === 'COMMUNITIES' && (
                <CommunitiesSection
                  viewMode={viewMode}
                  searchQuery={searchQuery}
                  favorites={favorites}
                  onFavoriteToggle={handleFavoriteToggleWithHaptic}
                  isMobile={isMobile}
                />
              )}
              {activeTab === 'RESOURCES' && <ResourcesSection />}
              {activeTab === 'LOCAL_INFO' && (
                <LocalInfoSection
                  viewMode={viewMode}
                  searchQuery={searchQuery}
                  favorites={favorites}
                  onFavoriteToggle={handleFavoriteToggleWithHaptic}
                  isMobile={isMobile}
                />
              )}
            </motion.div>
          </AnimatePresence>

            {/* Development-only Mobile UX Testing Tool */}
            <MobileUXTester />
          </motion.div>
        </PageWrapper>

        {/* Personalized Content Modals */}
        {selectedPersonalizedTip && (
          <TipDetailsModal
            tip={selectedPersonalizedTip}
            isOpen={!!selectedPersonalizedTip}
            onClose={() => setSelectedPersonalizedTip(null)}
          />
        )}
        {selectedPersonalizedGuide && (
          <GuideDetailsModal
            guide={selectedPersonalizedGuide}
            isOpen={!!selectedPersonalizedGuide}
            onClose={() => setSelectedPersonalizedGuide(null)}
          />
        )}
      </ErrorBoundary>
  );
};

export default FamHub;

-- Database Reality Check for Festival Family
-- This script analyzes the ACTUAL current state of your database
-- Run this in Supabase SQL Editor to get the complete picture

-- ============================================================================
-- PART 1: ALL EXISTING TABLES
-- ============================================================================

SELECT 'EXISTING TABLES' as analysis_section;

SELECT 
    schemaname,
    tablename,
    tableowner,
    hasindexes,
    hasrules,
    hastriggers
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY tablename;

-- ============================================================================
-- PART 2: ALL EXISTING FUNCTIONS
-- ============================================================================

SELECT 'EXISTING FUNCTIONS' as analysis_section;

SELECT 
    routine_name,
    routine_type,
    data_type as return_type,
    routine_definition
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_type = 'FUNCTION'
ORDER BY routine_name;

-- ============================================================================
-- PART 3: DETAILED TABLE STRUCTURES
-- ============================================================================

SELECT 'TABLE STRUCTURES' as analysis_section;

-- Check each table we care about
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN ('announcements', 'tips', 'faqs', 'content_management', 'user_preferences', 'emergency_contacts', 'safety_information', 'profiles', 'festivals', 'activities', 'groups')
ORDER BY table_name, ordinal_position;

-- ============================================================================
-- PART 4: CHECK SPECIFIC MISSING ELEMENTS
-- ============================================================================

SELECT 'MISSING ELEMENTS CHECK' as analysis_section;

-- Check if FAQ table has required columns
SELECT 
    'faqs_category_column' as check_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'faqs' AND column_name = 'category'
        ) THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status;

SELECT 
    'faqs_order_index_column' as check_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'faqs' AND column_name = 'order_index'
        ) THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status;

-- Check if critical tables exist
SELECT 
    'content_management_table' as check_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'content_management'
        ) THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status;

SELECT 
    'user_preferences_table' as check_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'user_preferences'
        ) THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status;

SELECT 
    'emergency_contacts_table' as check_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'emergency_contacts'
        ) THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status;

SELECT 
    'safety_information_table' as check_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'safety_information'
        ) THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status;

-- Check if admin functions exist
SELECT 
    'is_admin_function' as check_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.routines 
            WHERE routine_name = 'is_admin'
        ) THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status;

SELECT 
    'is_super_admin_function' as check_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.routines 
            WHERE routine_name = 'is_super_admin'
        ) THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status;

SELECT 
    'is_content_admin_function' as check_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.routines 
            WHERE routine_name = 'is_content_admin'
        ) THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status;

SELECT 
    'can_manage_groups_function' as check_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.routines 
            WHERE routine_name = 'can_manage_groups'
        ) THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status;

-- ============================================================================
-- PART 5: MIGRATION HISTORY
-- ============================================================================

SELECT 'MIGRATION HISTORY' as analysis_section;

-- Check if migration tracking table exists and what's been applied
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'schema_migrations'
        ) THEN 'schema_migrations table EXISTS' 
        ELSE 'schema_migrations table MISSING' 
    END as migration_tracking_status;

-- If it exists, show what migrations have been applied
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'schema_migrations') THEN
        RAISE NOTICE 'Migration tracking table exists - checking applied migrations...';
    ELSE
        RAISE NOTICE 'No migration tracking table found';
    END IF;
END $$;

-- ============================================================================
-- PART 6: RLS POLICIES
-- ============================================================================

SELECT 'RLS POLICIES' as analysis_section;

SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- ============================================================================
-- PART 7: SAMPLE DATA CHECK
-- ============================================================================

SELECT 'SAMPLE DATA CHECK' as analysis_section;

-- Check record counts in existing tables
DO $$
DECLARE
    table_record record;
    sql_query text;
    record_count integer;
BEGIN
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name IN ('announcements', 'tips', 'faqs', 'content_management', 'user_preferences', 'emergency_contacts', 'safety_information', 'profiles', 'festivals', 'activities', 'groups')
    LOOP
        sql_query := 'SELECT COUNT(*) FROM ' || table_record.table_name;
        EXECUTE sql_query INTO record_count;
        RAISE NOTICE 'Table %: % records', table_record.table_name, record_count;
    END LOOP;
END $$;

-- ============================================================================
-- PART 8: SUMMARY REPORT
-- ============================================================================

SELECT 'SUMMARY REPORT' as analysis_section;

WITH table_analysis AS (
    SELECT 
        COUNT(*) as total_tables,
        COUNT(CASE WHEN table_name IN ('announcements', 'tips', 'faqs', 'content_management', 'user_preferences', 'emergency_contacts', 'safety_information') THEN 1 END) as required_tables
    FROM information_schema.tables 
    WHERE table_schema = 'public'
),
function_analysis AS (
    SELECT 
        COUNT(*) as total_functions,
        COUNT(CASE WHEN routine_name IN ('is_admin', 'is_super_admin', 'is_content_admin', 'can_manage_groups') THEN 1 END) as admin_functions
    FROM information_schema.routines 
    WHERE routine_schema = 'public' AND routine_type = 'FUNCTION'
)
SELECT 
    t.total_tables,
    t.required_tables,
    CASE WHEN t.required_tables = 7 THEN '✅ ALL REQUIRED TABLES' ELSE '❌ MISSING TABLES' END as table_status,
    f.total_functions,
    f.admin_functions,
    CASE WHEN f.admin_functions = 4 THEN '✅ ALL ADMIN FUNCTIONS' ELSE '❌ MISSING FUNCTIONS' END as function_status,
    CASE 
        WHEN t.required_tables = 7 AND f.admin_functions = 4 THEN '🎉 DATABASE COMPLETE'
        WHEN t.required_tables >= 3 AND f.admin_functions >= 1 THEN '⚠️ PARTIALLY COMPLETE'
        ELSE '🚨 MAJOR ISSUES'
    END as overall_status
FROM table_analysis t, function_analysis f;

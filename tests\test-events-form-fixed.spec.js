/**
 * Test Events Form After Schema Fix
 * 
 * This test verifies that the events form now works with the correct database schema
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

test('Test Fixed Events Form Submission', async ({ page }) => {
  console.log('🧪 Testing fixed events form submission...');
  
  // Capture console logs and network requests
  const consoleLogs = [];
  const networkRequests = [];
  
  page.on('console', msg => {
    consoleLogs.push(`${msg.type()}: ${msg.text()}`);
  });
  
  page.on('request', request => {
    if (request.url().includes('supabase') && request.method() === 'POST') {
      networkRequests.push({
        method: request.method(),
        url: request.url(),
        postData: request.postData()
      });
    }
  });
  
  const loginSuccess = await loginAsAdmin(page);
  console.log(`Login success: ${loginSuccess}`);
  
  if (loginSuccess) {
    // Navigate to events form
    await page.goto('/admin/events/new');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    console.log('📝 Filling out events form with correct schema...');
    
    // Fill out the form with test data matching the actual database schema
    const testData = {
      title: 'Schema Fixed Test Event ' + Date.now(),
      description: 'This event tests the fixed database schema with proper field mapping.',
      location: 'Test Event Location'
    };
    
    // Fill form fields
    await page.fill('input[name="title"]', testData.title);
    await page.fill('textarea[name="description"]', testData.description);
    await page.fill('input[name="location"]', testData.location);
    
    // Add start and end dates
    const startDate = new Date();
    startDate.setDate(startDate.getDate() + 7); // Next week
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 9); // Next week + 2 days
    
    const startDateString = startDate.toISOString().slice(0, 16); // Format for datetime-local
    const endDateString = endDate.toISOString().slice(0, 16);
    
    await page.fill('input[name="start_date"]', startDateString);
    await page.fill('input[name="end_date"]', endDateString);
    
    // Add a valid festival ID (from database)
    await page.fill('input[name="festival_id"]', '49239f09-0738-4e3a-9ea0-475f2f780e88');
    
    await page.screenshot({ path: 'test-results/events-form-fixed-filled.png', fullPage: true });
    
    console.log('✅ Events form filled with correct schema data');
    
    // Clear previous network logs
    networkRequests.length = 0;
    
    // Submit the form
    console.log('🚀 Submitting events form...');
    await page.click('button[type="submit"]');
    
    // Wait for submission to complete
    await page.waitForTimeout(5000);
    
    const currentUrl = page.url();
    console.log(`Current URL after submission: ${currentUrl}`);
    
    await page.screenshot({ path: 'test-results/events-form-fixed-submitted.png', fullPage: true });
    
    // Check for success indicators
    const hasSuccessToast = await page.locator('.toast, [data-testid*="toast"], [class*="toast"]').count() > 0;
    const wasRedirected = currentUrl.includes('/admin/events') && !currentUrl.includes('/new');
    const hasErrorMessage = await page.locator('text="Error", text="Failed"').count() > 0;
    
    console.log(`Has success toast: ${hasSuccessToast}`);
    console.log(`Was redirected: ${wasRedirected}`);
    console.log(`Has error message: ${hasErrorMessage}`);
    
    // Print network activity
    console.log('\n🌐 Network Requests:');
    networkRequests.forEach((req, i) => {
      console.log(`  ${i + 1}. ${req.method} ${req.url}`);
      if (req.postData) {
        console.log(`     Data: ${req.postData.substring(0, 200)}...`);
      }
    });
    
    // Print relevant console logs
    console.log('\n📋 Console logs (errors and important messages):');
    consoleLogs.filter(log => 
      log.includes('error') || 
      log.includes('Error') || 
      log.includes('saving') || 
      log.includes('submit') ||
      log.includes('supabase') ||
      log.includes('event')
    ).forEach(log => console.log(`  ${log}`));
    
    // Check if we can see the created event
    if (wasRedirected) {
      console.log('🔍 Checking if event was created...');
      await page.waitForTimeout(2000);
      
      const eventVisible = await page.locator(`text="${testData.title}"`).count() > 0;
      console.log(`Event visible in list: ${eventVisible}`);
      
      if (eventVisible) {
        console.log('✅ SUCCESS: Event was created and is visible');
      } else {
        console.log('❌ ISSUE: Event was not found in the list');
      }
    }
    
    // Final assessment
    const formWorking = wasRedirected && !hasErrorMessage;
    console.log(`\n🎯 FINAL RESULT: Events form submission ${formWorking ? 'SUCCESSFUL' : 'FAILED'}`);
    
    if (formWorking) {
      console.log('🎉 Events form is now working with correct database schema!');
    } else {
      console.log('❌ Events form still has issues that need investigation');
    }
  }
});

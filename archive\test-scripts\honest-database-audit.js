/**
 * Honest Database Audit - Festival Family
 * 
 * This script provides a truthful assessment of what database tables
 * and functionality actually exist and work in the current application.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 HONEST DATABASE AUDIT - Festival Family');
console.log('==========================================');
console.log(`🔗 Supabase URL: ${supabaseUrl ? 'SET' : 'MISSING'}`);
console.log(`🔑 Service Key: ${supabaseServiceKey ? 'SET' : 'MISSING'}`);

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('📋 Required variables:');
  console.log('   - VITE_SUPABASE_URL');
  console.log('   - SUPABASE_SERVICE_ROLE_KEY (or VITE_SUPABASE_ANON_KEY)');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Test 1: Basic Database Connectivity
async function testDatabaseConnectivity() {
  console.log('\n🔌 Test 1: Basic Database Connectivity');
  console.log('-------------------------------------');
  
  try {
    // Simple connectivity test
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (error) {
      console.log('❌ Database connection failed:', error.message);
      return false;
    } else {
      console.log('✅ Database connection successful');
      return true;
    }
  } catch (err) {
    console.log('💥 Database connectivity error:', err.message);
    return false;
  }
}

// Test 2: Core Tables Existence Check
async function checkCoreTablesExistence() {
  console.log('\n📋 Test 2: Core Tables Existence Check');
  console.log('-------------------------------------');
  
  const coreTables = [
    'profiles',
    'festivals', 
    'events',
    'activities',
    'groups',
    'group_members'
  ];
  
  const results = {};
  
  for (const table of coreTables) {
    try {
      const { data, error, count } = await supabase
        .from(table)
        .select('*', { count: 'exact' })
        .limit(1);
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
        results[table] = { exists: false, error: error.message };
      } else {
        console.log(`✅ ${table}: EXISTS (${count} records)`);
        results[table] = { exists: true, count: count, sampleData: data };
      }
    } catch (err) {
      console.log(`💥 ${table}: ${err.message}`);
      results[table] = { exists: false, error: err.message };
    }
  }
  
  return results;
}

// Test 3: New Tables Claimed to be Implemented
async function checkNewTablesExistence() {
  console.log('\n🆕 Test 3: New Tables Claimed to be Implemented');
  console.log('-----------------------------------------------');
  
  const newTables = [
    'content_management',
    'user_preferences',
    'emergency_contacts',
    'safety_information',
    'announcements',
    'tips',
    'faqs'
  ];
  
  const results = {};
  
  for (const table of newTables) {
    try {
      const { data, error, count } = await supabase
        .from(table)
        .select('*', { count: 'exact' })
        .limit(1);
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
        results[table] = { exists: false, error: error.message };
      } else {
        console.log(`✅ ${table}: EXISTS (${count} records)`);
        results[table] = { exists: true, count: count, sampleData: data };
      }
    } catch (err) {
      console.log(`💥 ${table}: ${err.message}`);
      results[table] = { exists: false, error: err.message };
    }
  }
  
  return results;
}

// Test 4: Admin Functions Existence
async function checkAdminFunctions() {
  console.log('\n⚙️ Test 4: Admin Functions Existence');
  console.log('-----------------------------------');
  
  const adminFunctions = [
    'is_admin',
    'is_super_admin', 
    'is_content_admin',
    'can_manage_groups',
    'change_user_role'
  ];
  
  const results = {};
  
  for (const func of adminFunctions) {
    try {
      const { data, error } = await supabase.rpc(func);
      
      if (error) {
        console.log(`❌ ${func}: ${error.message}`);
        results[func] = { exists: false, error: error.message };
      } else {
        console.log(`✅ ${func}: EXISTS (result: ${data})`);
        results[func] = { exists: true, result: data };
      }
    } catch (err) {
      console.log(`💥 ${func}: ${err.message}`);
      results[func] = { exists: false, error: err.message };
    }
  }
  
  return results;
}

// Test 5: Sample CRUD Operations
async function testSampleCrudOperations() {
  console.log('\n🔄 Test 5: Sample CRUD Operations');
  console.log('---------------------------------');
  
  const results = {
    profiles: { read: false, update: false },
    announcements: { create: false, read: false, update: false, delete: false }
  };
  
  // Test profiles read
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, email, full_name')
      .limit(1);
    
    if (!error && data) {
      console.log(`✅ Profiles READ: Working (${data.length} records)`);
      results.profiles.read = true;
      
      // Test profiles update if we have a record
      if (data.length > 0) {
        const testProfile = data[0];
        const originalName = testProfile.full_name;
        const testName = `Test ${Date.now()}`;
        
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ full_name: testName })
          .eq('id', testProfile.id);
        
        if (!updateError) {
          console.log('✅ Profiles UPDATE: Working');
          results.profiles.update = true;
          
          // Restore original name
          await supabase
            .from('profiles')
            .update({ full_name: originalName })
            .eq('id', testProfile.id);
        } else {
          console.log(`❌ Profiles UPDATE: ${updateError.message}`);
        }
      }
    } else {
      console.log(`❌ Profiles READ: ${error?.message || 'No data'}`);
    }
  } catch (err) {
    console.log(`💥 Profiles operations: ${err.message}`);
  }
  
  // Test announcements CRUD (if table exists)
  try {
    const { data, error } = await supabase
      .from('announcements')
      .select('*')
      .limit(1);
    
    if (!error) {
      console.log(`✅ Announcements READ: Working (${data?.length || 0} records)`);
      results.announcements.read = true;
      
      // Test create
      const testAnnouncement = {
        title: 'Test Announcement',
        content: 'This is a test announcement for validation.',
        type: 'info',
        priority: 'low',
        is_active: false
      };
      
      const { data: created, error: createError } = await supabase
        .from('announcements')
        .insert([testAnnouncement])
        .select()
        .single();
      
      if (!createError && created) {
        console.log('✅ Announcements CREATE: Working');
        results.announcements.create = true;
        
        // Test update
        const { error: updateError } = await supabase
          .from('announcements')
          .update({ title: 'Updated Test Announcement' })
          .eq('id', created.id);
        
        if (!updateError) {
          console.log('✅ Announcements UPDATE: Working');
          results.announcements.update = true;
        }
        
        // Test delete
        const { error: deleteError } = await supabase
          .from('announcements')
          .delete()
          .eq('id', created.id);
        
        if (!deleteError) {
          console.log('✅ Announcements DELETE: Working');
          results.announcements.delete = true;
        }
      } else {
        console.log(`❌ Announcements CREATE: ${createError?.message}`);
      }
    } else {
      console.log(`❌ Announcements table: ${error.message}`);
    }
  } catch (err) {
    console.log(`💥 Announcements operations: ${err.message}`);
  }
  
  return results;
}

// Test 6: Storage Buckets Check
async function checkStorageBuckets() {
  console.log('\n🗄️ Test 6: Storage Buckets Check');
  console.log('--------------------------------');
  
  try {
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.log(`❌ Storage buckets: ${error.message}`);
      return { accessible: false, error: error.message };
    } else {
      console.log(`✅ Storage accessible: ${buckets.length} buckets found`);
      buckets.forEach(bucket => {
        console.log(`   📁 ${bucket.name} (${bucket.public ? 'public' : 'private'})`);
      });
      
      // Check for avatars bucket specifically
      const avatarBucket = buckets.find(b => b.name === 'avatars');
      if (avatarBucket) {
        console.log('✅ Avatars bucket exists');
      } else {
        console.log('⚠️ Avatars bucket not found');
      }
      
      return { accessible: true, buckets: buckets };
    }
  } catch (err) {
    console.log(`💥 Storage check error: ${err.message}`);
    return { accessible: false, error: err.message };
  }
}

// Main execution
async function main() {
  try {
    console.log(`🕐 Audit started at: ${new Date().toISOString()}\n`);
    
    // Run all tests
    const connectivity = await testDatabaseConnectivity();
    
    if (!connectivity) {
      console.log('\n🚨 CRITICAL: Database connectivity failed. Cannot proceed with audit.');
      process.exit(1);
    }
    
    const coreTablesResults = await checkCoreTablesExistence();
    const newTablesResults = await checkNewTablesExistence();
    const adminFunctionsResults = await checkAdminFunctions();
    const crudResults = await testSampleCrudOperations();
    const storageResults = await checkStorageBuckets();
    
    // Summary
    console.log('\n📊 HONEST DATABASE AUDIT SUMMARY');
    console.log('=================================');
    
    const coreTablesWorking = Object.values(coreTablesResults).filter(t => t.exists).length;
    const totalCoreTables = Object.keys(coreTablesResults).length;
    
    const newTablesWorking = Object.values(newTablesResults).filter(t => t.exists).length;
    const totalNewTables = Object.keys(newTablesResults).length;
    
    const adminFunctionsWorking = Object.values(adminFunctionsResults).filter(f => f.exists).length;
    const totalAdminFunctions = Object.keys(adminFunctionsResults).length;
    
    console.log(`🗄️ Core Tables: ${coreTablesWorking}/${totalCoreTables} working`);
    console.log(`🆕 New Tables: ${newTablesWorking}/${totalNewTables} working`);
    console.log(`⚙️ Admin Functions: ${adminFunctionsWorking}/${totalAdminFunctions} working`);
    console.log(`🔄 CRUD Operations: Profiles(${results.profiles.read ? 'R' : ''}${results.profiles.update ? 'U' : ''}), Announcements(${Object.values(crudResults.announcements).filter(Boolean).length}/4)`);
    console.log(`🗄️ Storage: ${storageResults.accessible ? 'Working' : 'Issues'}`);
    
    // Honest assessment
    console.log('\n🎯 HONEST ASSESSMENT:');
    
    if (coreTablesWorking === totalCoreTables) {
      console.log('✅ Core application tables are working');
    } else {
      console.log('❌ Some core application tables have issues');
    }
    
    if (newTablesWorking > 0) {
      console.log(`✅ ${newTablesWorking} new feature tables are working`);
    } else {
      console.log('❌ No new feature tables are working');
    }
    
    if (adminFunctionsWorking > 0) {
      console.log(`✅ ${adminFunctionsWorking} admin functions are working`);
    } else {
      console.log('❌ No admin functions are working');
    }
    
    // Overall readiness
    const overallScore = (
      (coreTablesWorking / totalCoreTables) * 0.4 +
      (newTablesWorking / totalNewTables) * 0.3 +
      (adminFunctionsWorking / totalAdminFunctions) * 0.2 +
      (storageResults.accessible ? 1 : 0) * 0.1
    ) * 100;
    
    console.log(`\n📈 Overall Database Readiness: ${Math.round(overallScore)}%`);
    
    if (overallScore >= 80) {
      console.log('🎉 Database is in good shape for production');
    } else if (overallScore >= 60) {
      console.log('⚠️ Database needs some work before production');
    } else {
      console.log('🚨 Database needs significant work before production');
    }
    
    console.log(`\n🕐 Audit completed at: ${new Date().toISOString()}`);
    
  } catch (error) {
    console.error('💥 Honest database audit failed:', error);
  }
}

main();

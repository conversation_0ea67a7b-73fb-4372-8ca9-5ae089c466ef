{"testType": "XSS Vulnerability Assessment", "totalPayloads": 6, "results": [{"payload": "<script>alert(\"XSS\")</script>", "error": "Bio field not accessible", "timestamp": "2025-06-04T07:56:18.402Z"}, {"payload": "javascript:alert(\"XSS\")", "error": "Bio field not accessible", "timestamp": "2025-06-04T07:56:18.412Z"}, {"payload": "<img src=\"x\" onerror=\"alert('XSS')\">", "error": "Bio field not accessible", "timestamp": "2025-06-04T07:56:18.420Z"}, {"payload": "<svg onload=\"alert('XSS')\">", "error": "Bio field not accessible", "timestamp": "2025-06-04T07:56:18.430Z"}, {"payload": "\"><script>alert(\"XSS\")</script>", "error": "Bio field not accessible", "timestamp": "2025-06-04T07:56:18.440Z"}, {"payload": "<iframe src=\"javascript:alert('XSS')\"></iframe>", "error": "Bio field not accessible", "timestamp": "2025-06-04T07:56:18.448Z"}], "summary": {"vulnerablePayloads": 0, "protectedPayloads": 0, "erroredPayloads": 6}, "timestamp": "2025-06-04T07:56:18.448Z"}
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/providers/ConsolidatedAuthProvider';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { UserPlus, Check, X, Clock, Send } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface GroupInvitation {
  id: string;
  group_id: string;
  inviter_id: string;
  invitee_id: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  message?: string;
  expires_at: string;
  created_at: string;
  responded_at?: string;
  group: {
    id: string;
    name: string;
    description?: string;
    is_private: boolean;
  };
  inviter: {
    id: string;
    full_name?: string;
    username?: string;
  };
}

interface GroupInvitationsProps {
  groupId?: string; // If provided, show invitations for specific group
  userId?: string;  // If provided, show invitations for specific user
}

export const GroupInvitations: React.FC<GroupInvitationsProps> = ({ 
  groupId, 
  userId 
}) => {
  const { user } = useAuth();
  const [invitations, setInvitations] = useState<GroupInvitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchInvitations();
    }
  }, [user, groupId, userId]);

  const fetchInvitations = async () => {
    try {
      setLoading(true);
      
      let query = supabase
        .from('group_invitations')
        .select(`
          *,
          group:groups(id, name, description, is_private),
          inviter:profiles!group_invitations_inviter_id_fkey(id, full_name, username)
        `)
        .order('created_at', { ascending: false });

      // Filter by group if specified
      if (groupId) {
        query = query.eq('group_id', groupId);
      }

      // Filter by user if specified (otherwise show current user's invitations)
      const targetUserId = userId || user?.id;
      if (targetUserId) {
        query = query.eq('invitee_id', targetUserId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching invitations:', error);
        toast.error('Failed to load invitations');
        return;
      }

      // Filter out any invalid data and properly type the results
      const validInvitations = (data || []).filter(item =>
        item &&
        typeof item.id === 'string' &&
        item.group &&
        typeof item.group === 'object' &&
        !('error' in item.group) &&
        item.inviter &&
        typeof item.inviter === 'object'
      ).map(item => ({
        ...item,
        inviter_id: item.inviter_id || '', // Use correct database field name
        invitee_id: item.invitee_id, // Use correct database field name
        status: item.status || 'pending',
        expires_at: (item as any).expires_at || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        group: (item.group as unknown) as { id: string; name: string; description?: string; is_private: boolean },
        inviter: (item.inviter as any).id ? (item.inviter as unknown) as { id: string; full_name?: string; username?: string } : { id: '', full_name: '', username: '' }
      })) as GroupInvitation[];

      setInvitations(validInvitations);
    } catch (error) {
      console.error('Error fetching invitations:', error);
      toast.error('Failed to load invitations');
    } finally {
      setLoading(false);
    }
  };

  const handleInvitationResponse = async (invitationId: string, action: 'accept' | 'decline') => {
    try {
      setActionLoading(invitationId);

      if (action === 'accept') {
        // Use the accept_group_invitation function
        const { data, error } = await supabase
          .rpc('accept_group_invitation' as never, { invitation_id: invitationId } as never);

        if (error) {
          console.error('Error accepting invitation:', error);
          toast.error('Failed to accept invitation');
          return;
        }

        if (data) {
          toast.success('Successfully joined the group!');
        } else {
          toast.error('Failed to accept invitation - it may have expired');
          return;
        }
      } else {
        // Decline invitation
        const { error } = await supabase
          .from('group_invitations')
          .update({ 
            status: 'declined', 
            responded_at: new Date().toISOString() 
          })
          .eq('id', invitationId)
          .eq('invitee_id', user?.id || '');

        if (error) {
          console.error('Error declining invitation:', error);
          toast.error('Failed to decline invitation');
          return;
        }

        toast.success('Invitation declined');
      }

      // Refresh invitations
      await fetchInvitations();
    } catch (error) {
      console.error('Error handling invitation response:', error);
      toast.error('Failed to respond to invitation');
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'accepted':
        return <Check className="w-4 h-4 text-green-500" />;
      case 'declined':
        return <X className="w-4 h-4 text-red-500" />;
      case 'expired':
        return <Clock className="w-4 h-4 text-gray-400" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'accepted':
        return 'Accepted';
      case 'declined':
        return 'Declined';
      case 'expired':
        return 'Expired';
      default:
        return status;
    }
  };

  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date();
  };

  const canRespond = (invitation: GroupInvitation) => {
    return invitation.status === 'pending' && 
           !isExpired(invitation.expires_at) && 
           invitation.invitee_id === user?.id;
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="p-4 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </Card>
        ))}
      </div>
    );
  }

  if (invitations.length === 0) {
    return (
      <Card className="p-8 text-center">
        <UserPlus className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No Group Invitations
        </h3>
        <p className="text-gray-500">
          {groupId 
            ? "No invitations have been sent for this group yet."
            : "You don't have any group invitations at the moment."
          }
        </p>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">
          Group Invitations
        </h2>
        <span className="text-sm text-gray-500">
          {invitations.filter(inv => inv.status === 'pending').length} pending
        </span>
      </div>

      {invitations.map((invitation) => (
        <Card key={invitation.id} className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="font-medium text-gray-900">
                  {invitation.group.name}
                </h3>
                <div className="flex items-center gap-1">
                  {getStatusIcon(invitation.status)}
                  <span className="text-sm text-gray-500">
                    {getStatusText(invitation.status)}
                  </span>
                </div>
              </div>

              <p className="text-sm text-gray-600 mb-2">
                Invited by {invitation.inviter.full_name || invitation.inviter.username || 'Unknown'}
              </p>

              {invitation.group.description && (
                <p className="text-sm text-gray-500 mb-2">
                  {invitation.group.description}
                </p>
              )}

              {invitation.message && (
                <div className="bg-gray-50 rounded-lg p-3 mb-3">
                  <p className="text-sm text-gray-700">
                    "{invitation.message}"
                  </p>
                </div>
              )}

              <div className="flex items-center gap-4 text-xs text-gray-500">
                <span>
                  Invited {new Date(invitation.created_at).toLocaleDateString()}
                </span>
                <span>
                  Expires {new Date(invitation.expires_at).toLocaleDateString()}
                </span>
                {invitation.group.is_private && (
                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                    Private Group
                  </span>
                )}
              </div>
            </div>

            {canRespond(invitation) && (
              <div className="flex gap-2 ml-4">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleInvitationResponse(invitation.id, 'decline')}
                  disabled={actionLoading === invitation.id}
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  <X className="w-4 h-4" />
                  Decline
                </Button>
                <Button
                  size="sm"
                  onClick={() => handleInvitationResponse(invitation.id, 'accept')}
                  disabled={actionLoading === invitation.id}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Check className="w-4 h-4" />
                  Accept
                </Button>
              </div>
            )}
          </div>

          {isExpired(invitation.expires_at) && invitation.status === 'pending' && (
            <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
              This invitation has expired
            </div>
          )}
        </Card>
      ))}
    </div>
  );
};

export default GroupInvitations;

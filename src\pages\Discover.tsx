import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { DiscoverFilters } from '@/types/constants';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ErrorBoundary } from 'react-error-boundary';
import { AlertCircle, RefreshCw, Filter, Calendar, MapPin, Users, Heart, Share2, ChevronRight, Star, Compass, TrendingUp, Sparkles } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '../providers/ConsolidatedAuthProvider';
import { recommendationEngine, type EventRecommendation } from '@/lib/intelligence';
import { FestivalType } from '@/types/core';
import { optimizeEventDescription } from '@/utils/textOptimization';
import type { Event as DatabaseEvent } from '@/types/database';
import { Button } from '@/components/ui/button';
import { useReducedMotion, motionClasses } from '@/hooks/ui/useReducedMotion';
import { toast } from 'react-hot-toast';
import { simulateHapticFeedback, isMobileViewport, createTouchHandler } from '../utils/mobileUX';
import MobileUXTester from '../components/testing/MobileUXTester';
import FallbackImage from '@/components/ui/FallbackImage';
import { EventDetailsModal } from '@/components/design-system/EventDetailsModal';
import { useActivityTracking } from '@/hooks/useUnifiedTracking';
// Import unified design system components
import {
  PageWrapper,
  UnifiedCard,
  UnifiedButton,
  UnifiedBadge,
  UnifiedFilterBar,
  GridLayout,
  FlexLayout
} from '@/components/design-system';
import { BentoCard, BentoGrid } from '@/components/design-system/BentoGrid';
import { EnhancedUnifiedBadge } from '@/components/design-system/EnhancedUnifiedBadge';
import { useEnhancedColorMapping } from '@/hooks/useEnhancedColorMapping';

// Enhanced Mobile-First Event Card component
interface EventCardProps {
  event: DatabaseEvent;
  onClick: (element: HTMLElement | null) => void;
  index?: number;
}

const EventCard = React.memo(({ event, onClick, index = 0 }: EventCardProps) => {
  const [isFavorited, setIsFavorited] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const { trackView } = useActivityTracking();

  // Track view when component mounts
  useEffect(() => {
    if (event.id) {
      console.log(`🎯 Discover EventCard: Tracking view for event ${event.id} (${event.title})`);
      trackView('event', event.id).catch(console.error);
    }
  }, [event.id, trackView]);

  const handleFavorite = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setIsFavorited(!isFavorited);
    simulateHapticFeedback('light');
    toast.success(isFavorited ? 'Removed from favorites' : 'Added to favorites');
  }, [isFavorited]);

  const handleShare = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    simulateHapticFeedback('light');
    toast.success('Event shared!');
  }, []);

  const handleCardClick = useCallback(() => {
    onClick(cardRef.current);
  }, [onClick]);

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="group"
    >
      <BentoCard
        title={event.title}
        description={optimizeEventDescription(event.description, isMobileViewport())}
        variant="glassmorphism"
        interactive
        onClick={handleCardClick}
        imageUrl={event.image_url || undefined}
        className="bg-gradient-to-r from-primary/10 to-accent/5 border-primary/20 group-hover:scale-[1.02]"
        icon={<Calendar className="w-5 h-5 text-primary" />}
        overlayBadge={
          <div className="flex items-center gap-2">
            <EnhancedUnifiedBadge
              contentType="festivals"
              category="main"
              size="sm"
              overlayMode={true}
            >
              Event
            </EnhancedUnifiedBadge>
            {event.is_active && (
              <EnhancedUnifiedBadge
                contentType="festivals"
                category="main"
                size="sm"
                overlayMode={true}
              >
                Featured
              </EnhancedUnifiedBadge>
            )}
          </div>
        }
        badgePosition="top-right"
      >
        <div className="space-y-2">
          {/* Event Details */}
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              <span>{new Date(event.start_date).toLocaleDateString()}</span>
            </div>
            <div className="flex items-center gap-1">
              <MapPin className="w-3 h-3" />
              <span className="truncate">{event.location}</span>
            </div>
          </div>

          {/* Capacity */}
          {event.capacity && (
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Users className="w-3 h-3" />
              <span>{event.capacity} capacity</span>
            </div>
          )}

          {/* Action Button */}
          <UnifiedButton
            onClick={() => onClick(cardRef.current)}
            size="sm"
            variant="primary"
            className="w-full mt-2"
          >
            View Details
          </UnifiedButton>
        </div>
      </BentoCard>
    </motion.div>
  );
});

EventCard.displayName = 'EventCard';

// Error fallback component for Discover page
const DiscoverErrorFallback = ({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) => (
  <div className="min-h-screen flex flex-col bg-gradient-to-br from-background via-muted to-card">
    <div className="container mx-auto px-4 py-8">
      <Card className="p-6 bg-card backdrop-blur-md border border-border">
        <CardContent className="text-center">
          <AlertCircle className="h-12 w-12 mx-auto text-red-400 mb-4" />
          <h2 className="text-2xl font-bold mb-2 text-card-foreground">Unable to Load Events</h2>
          <p className="text-muted-foreground mb-6">
            We're having trouble loading the events. This might be a temporary issue.
          </p>
          <div className="bg-red-500/20 rounded-md p-4 mb-6">
            <p className="text-sm text-red-300">{error.message}</p>
          </div>
          <Button
            onClick={resetErrorBoundary}
            className="bg-primary hover:bg-primary/90"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    </div>
  </div>
);

const Discover: React.FC = () => {
  const { user } = useAuth();

  // Mobile state management
  const [selectedCategory, setSelectedCategory] = useState<string>(DiscoverFilters.ALL_CATEGORIES);
  const [selectedLocation, setSelectedLocation] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isMobile, setIsMobile] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Real events data state
  const [events, setEvents] = useState<DatabaseEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Available categories and locations from real data
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const [availableLocations, setAvailableLocations] = useState<string[]>([]);

  // Event details modal state
  const [selectedEvent, setSelectedEvent] = useState<DatabaseEvent | null>(null);

  // Intelligence features state
  const [recommendations, setRecommendations] = useState<EventRecommendation[]>([]);
  const [trendingEvents, setTrendingEvents] = useState<DatabaseEvent[]>([]);
  const [showRecommendations, setShowRecommendations] = useState(true);
  const [recommendationsLoading, setRecommendationsLoading] = useState(false);

  // Fetch real events from database
  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true);
        setError(null);

        const { data, error } = await supabase
          .from('events')
          .select(`
            id,
            title,
            description,
            start_date,
            end_date,
            location,
            category,
            status,
            is_active,
            image_url,
            created_at,
            festivals:festival_id (
              name
            )
          `)
          .eq('status', 'PUBLISHED')
          .order('start_date', { ascending: true });

        if (error) throw error;

        // Transform data to match expected format
        const transformedEvents = data?.map(event => ({
          id: event.id,
          title: event.title,
          description: event.description || 'Join this exciting event!',
          start_date: event.start_date,
          end_date: event.end_date,
          location: event.location || 'Location TBD',
          category: event.category,
          created_by: null, // Not selected in query
          created_at: event.created_at,
          updated_at: null, // Not selected in query
          festival_id: null, // Not selected in query
          image_url: event.image_url,
          is_active: event.is_active,
          capacity: null, // Not selected in query
          registration_required: null, // Not selected in query
          status: event.status
        })) || [];

        setEvents(transformedEvents);

        // Extract unique categories and locations for filters
        const categories = [...new Set(transformedEvents.map(e => e.category).filter((cat): cat is string => Boolean(cat)))];
        const locations = [...new Set(transformedEvents.map(e => e.location).filter((loc): loc is string => Boolean(loc)))];

        setAvailableCategories(categories);
        setAvailableLocations(locations);

      } catch (error) {
        console.error('Error fetching events:', error);
        setError('Failed to load events');
        setEvents([]);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();

    // Fetch intelligent recommendations if user is authenticated
    if (user) {
      fetchRecommendations();
      fetchTrendingEvents();
    }
  }, [user]);

  // Fetch personalized recommendations
  const fetchRecommendations = async () => {
    if (!user) return;

    try {
      setRecommendationsLoading(true);
      const { data, error } = await recommendationEngine.getEventRecommendations(user.id, {
        maxResults: 5
      });

      if (error) {
        console.error('Error fetching recommendations:', error);
        return;
      }

      setRecommendations(data || []);
    } catch (error) {
      console.error('Error fetching recommendations:', error);
    } finally {
      setRecommendationsLoading(false);
    }
  };

  // Fetch trending events
  const fetchTrendingEvents = async () => {
    try {
      const { data, error } = await recommendationEngine.getTrendingEvents(3);

      if (error) {
        console.error('Error fetching trending events:', error);
        return;
      }

      setTrendingEvents(data || []);
    } catch (error) {
      console.error('Error fetching trending events:', error);
    }
  };

  // Helper function to format event dates with null safety
  const formatEventDate = (startDate: string, endDate: string) => {
    if (!startDate) return 'Date TBD';

    const start = new Date(startDate);
    if (isNaN(start.getTime())) return 'Invalid Date';

    const end = endDate ? new Date(endDate) : null;
    if (end && isNaN(end.getTime())) return 'Invalid Date';

    const formatOptions: Intl.DateTimeFormatOptions = {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    };

    if (end && start.toDateString() !== end.toDateString()) {
      return `${start.toLocaleDateString('en-US', formatOptions)} - ${end.toLocaleDateString('en-US', formatOptions)}`;
    } else {
      return start.toLocaleDateString('en-US', formatOptions);
    }
  };

  // Check if mobile viewport
  useEffect(() => {
    const checkMobile = () => setIsMobile(isMobileViewport());
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Use reduced motion hook for accessibility
  const { shouldAnimate } = useReducedMotion();

  // Handle pull-to-refresh with real data
  const handleRefresh = useCallback(async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    simulateHapticFeedback('medium');

    try {
      const { data, error } = await supabase
        .from('events')
        .select(`
          id,
          title,
          description,
          start_date,
          end_date,
          location,
          category,
          status,
          is_active,
          created_at,
          festivals:festival_id (
            name
          )
        `)
        .eq('status', 'PUBLISHED')
        .order('start_date', { ascending: true });

      if (error) throw error;

      // Transform data to match expected format
      const transformedEvents = data?.map(event => ({
        id: event.id,
        title: event.title,
        description: event.description || 'Join this exciting event!',
        start_date: event.start_date,
        end_date: event.end_date,
        location: event.location || 'Location TBD',
        category: event.category,
        created_by: null, // Not selected in query
        created_at: event.created_at,
        updated_at: null, // Not selected in query
        festival_id: null, // Not selected in query
        image_url: null, // Not selected in query
        is_active: event.is_active,
        capacity: null, // Not selected in query
        registration_required: null, // Not selected in query
        status: event.status
      })) || [];

      setEvents(transformedEvents);

      // Update categories and locations
      const categories = [...new Set(transformedEvents.map(e => e.category).filter((cat): cat is string => Boolean(cat)))];
      const locations = [...new Set(transformedEvents.map(e => e.location).filter((loc): loc is string => Boolean(loc)))];

      setAvailableCategories(categories);
      setAvailableLocations(locations);

      // Refresh intelligent features if user is authenticated
      if (user) {
        await Promise.all([
          fetchRecommendations(),
          fetchTrendingEvents()
        ]);
      }

      toast.success('Events refreshed!');
    } catch (error) {
      console.error('Refresh failed:', error);
      toast.error('Failed to refresh events');
    } finally {
      setIsRefreshing(false);
    }
  }, [isRefreshing, user]);

  // Memoize filtered events for better performance with real data
  const filteredEvents = useMemo(() => {
    if (loading) return [];

    return events.filter((event) => {
      const matchesCategory = selectedCategory === DiscoverFilters.ALL_CATEGORIES || event.category === selectedCategory;
      const matchesLocation = selectedLocation === 'all' || event.location === selectedLocation;
      const matchesSearch = event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.description.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesCategory && matchesLocation && matchesSearch;
    });
  }, [events, selectedCategory, selectedLocation, searchQuery, loading]);

  // State for tracking trigger element for modal positioning
  const [triggerElement, setTriggerElement] = useState<HTMLElement | null>(null);

  // Enhanced event click handler with event details modal
  const handleEventClick = useCallback((event: DatabaseEvent, element: HTMLElement | null) => {
    simulateHapticFeedback('medium');
    setSelectedEvent(event);
    setTriggerElement(element);
    toast.success(`Opening ${event.title}...`);
  }, []);

  return (
    <ErrorBoundary FallbackComponent={DiscoverErrorFallback}>
      <PageWrapper
        title="Discover Events"
        subtitle="Find new festivals and events"
        hideTitleOnMobile={true}
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="mobile-page-padding space-y-4 md:space-y-6"
        >
          {/* Compact Action Bar */}
          <div className="bg-gradient-to-r from-primary/10 to-accent/10 border border-primary/20 rounded-lg px-4 py-2 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-lg">🎪</span>
                <span className="text-sm text-muted-foreground">
                  Find your next adventure
                </span>
              </div>
              <div className="flex items-center gap-2">
                <UnifiedButton
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setIsRefreshing(true);
                    handleRefresh();
                    simulateHapticFeedback('light');
                  }}
                  disabled={isRefreshing}
                  className="text-xs"
                >
                  <RefreshCw className={`w-3 h-3 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                  <span className="hidden lg:inline">Refresh</span>
                </UnifiedButton>
                <UnifiedButton
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setShowFilters(!showFilters);
                    simulateHapticFeedback('light');
                  }}
                  className="text-xs"
                >
                  <Filter className="w-3 h-3" />
                </UnifiedButton>
              </div>
            </div>
          </div>

          {/* Show Recommendations Button when hidden */}
          {user && recommendations.length > 0 && !showRecommendations && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-4"
            >
              <UnifiedButton
                variant="outline"
                size="sm"
                onClick={() => setShowRecommendations(true)}
                className="w-full"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Show Personalized Recommendations ({recommendations.length})
              </UnifiedButton>
            </motion.div>
          )}

          {/* Personalized Recommendations Section */}
          {user && recommendations.length > 0 && showRecommendations && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mobile-section-spacing"
            >
              <UnifiedCard variant="elevated" className="mobile-card-padding md:p-6">
                <FlexLayout justify="between" align="center" className="mb-4">
                  <div>
                    <FlexLayout align="center" className="mb-2">
                      <Sparkles className="w-5 h-5 mr-2 text-festival-orange" />
                      <h2 className="text-responsive-lg font-bold text-primary-light">Recommended for You</h2>
                    </FlexLayout>
                    <p className="text-responsive text-muted-light">Based on your interests and activity</p>
                  </div>
                  <UnifiedButton
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowRecommendations(!showRecommendations)}
                  >
                    {showRecommendations ? 'Hide' : 'Show'}
                  </UnifiedButton>
                </FlexLayout>

                {/* Enhanced 2025 Visual Excellence: Responsive Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-4 sm:gap-6">
                  {recommendations.slice(0, 4).map((recommendation, index) => {
                    // Use theme-aware colors instead of hardcoded gradients
                    const colorThemes = [
                      'bg-gradient-to-br from-primary/20 to-primary/25 border-primary/30',
                      'bg-gradient-to-br from-secondary/20 to-secondary/25 border-secondary/30',
                      'bg-gradient-to-br from-accent/20 to-accent/25 border-accent/30',
                      'bg-gradient-to-br from-muted/20 to-muted/25 border-muted/30'
                    ];

                    return (
                      <BentoCard
                        key={index}
                        title={recommendation.event.title}
                        description={optimizeEventDescription(recommendation.event.description, isMobileViewport())}
                        variant="glassmorphism"
                        interactive
                        onClick={() => handleEventClick(recommendation.event, null)}
                        className={colorThemes[index % colorThemes.length]}
                        icon={<Calendar className="w-5 h-5 text-primary" />}
                        action={
                          <div className="flex items-center gap-2">
                            <EnhancedUnifiedBadge
                              contentType="festivals"
                              category="recommended"
                              size="sm"
                            >
                              New event for you
                            </EnhancedUnifiedBadge>
                            <UnifiedButton
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEventClick(recommendation.event, null);
                              }}
                              className="text-xs"
                            >
                              View Details
                            </UnifiedButton>
                          </div>
                        }
                      >
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            <span>{new Date(recommendation.event.start_date).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="w-3 h-3" />
                            <span>{recommendation.event.location}</span>
                          </div>
                        </div>
                      </BentoCard>
                    );
                  })}
                </div>
              </UnifiedCard>
            </motion.div>
          )}

          {/* Trending Events Section */}
          {trendingEvents.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="mobile-section-spacing"
            >
              <UnifiedCard variant="elevated" className="mobile-card-padding md:p-6">
                <FlexLayout align="center" className="mb-4">
                  <TrendingUp className="w-5 h-5 mr-2 text-festival-orange" />
                  <h2 className="text-xl font-bold text-primary-light">Trending Events</h2>
                </FlexLayout>

                {/* Enhanced 2025 Visual Excellence: Responsive Vibrant Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-4 sm:gap-6">
                  {trendingEvents.slice(0, 4).map((event, index) => {
                    // Use database-driven colors instead of hardcoded gradients
                    const fallbackColors = [
                      'bg-gradient-to-br from-primary/20 to-primary/25 border-primary/30',
                      'bg-gradient-to-br from-secondary/20 to-secondary/25 border-secondary/30',
                      'bg-gradient-to-br from-accent/20 to-accent/25 border-accent/30',
                      'bg-gradient-to-br from-muted/20 to-muted/25 border-muted/30'
                    ];

                    return (
                      <BentoCard
                        key={event.id}
                        title={event.title}
                        description={event.location || 'Main Stage'}
                        variant="glassmorphism"
                        interactive
                        onClick={() => handleEventClick(event, null)}
                        className={fallbackColors[index % fallbackColors.length]}
                        icon={<TrendingUp className="w-5 h-5 text-primary" />}
                        action={
                          <EnhancedUnifiedBadge
                            contentType="festivals"
                            category="trending"
                            size="sm"
                          >
                            Trending
                          </EnhancedUnifiedBadge>
                        }
                      >
                        <div className="text-center">
                          <EnhancedUnifiedBadge
                            contentType="festivals"
                            category="trending"
                            size="sm"
                          >
                            Popular event
                          </EnhancedUnifiedBadge>
                        </div>
                      </BentoCard>
                    );
                  })}
                </div>
              </UnifiedCard>
            </motion.div>
          )}

          {/* Standardized Filter Bar */}
          <UnifiedFilterBar
            searchValue={searchQuery}
            onSearchChange={setSearchQuery}
            searchPlaceholder="Search events..."
            filterOptions={[
              { id: 'all', label: 'All', count: events.length },
              { id: 'published', label: 'Published', count: events.filter(e => e.status === 'PUBLISHED').length },
              { id: 'upcoming', label: 'Upcoming', count: events.filter(e => new Date(e.start_date) > new Date()).length }
            ]}
            activeFilter={selectedCategory === DiscoverFilters.ALL_CATEGORIES ? 'all' : selectedCategory}
            onFilterChange={(filterId) => {
              if (filterId === 'all') {
                setSelectedCategory(DiscoverFilters.ALL_CATEGORIES);
              } else if (filterId === 'published') {
                setSelectedCategory('published');
              } else if (filterId === 'upcoming') {
                setSelectedCategory('upcoming');
              }
            }}
            className="mb-6"
          />

          {/* Filters Panel with Unified Design */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <UnifiedCard variant="outlined" className="p-6 mb-6">
                  <h4 className="text-lg font-semibold text-primary-light mb-4">Filters</h4>
                  <GridLayout cols={2} gap="md">
                    {/* Category Filter */}
                    <div>
                      <label className="block text-sm font-medium text-secondary-light mb-2">Category</label>
                      <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                        <SelectTrigger
                          className="w-full min-h-[44px] bg-card border border-neutral-200 text-primary-light focus:ring-2 focus:ring-festival-orange focus:border-festival-orange rounded-lg"
                          aria-label="Filter by category"
                        >
                          <SelectValue placeholder="Category" />
                        </SelectTrigger>
                        <SelectContent className="bg-card border border-neutral-200 text-primary-light">
                          <SelectItem value={DiscoverFilters.ALL_CATEGORIES}>All Categories</SelectItem>
                          {availableCategories.map((category) => (
                            <SelectItem key={category} value={category}>{category}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Location Filter */}
                    <div>
                      <label className="block text-sm font-medium text-secondary-light mb-2">Location</label>
                      <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                        <SelectTrigger
                          className="w-full min-h-[44px] bg-card border border-neutral-200 text-primary-light focus:ring-2 focus:ring-festival-orange focus:border-festival-orange rounded-lg"
                          aria-label="Filter by location"
                        >
                          <SelectValue placeholder="Location" />
                        </SelectTrigger>
                        <SelectContent className="bg-card border border-neutral-200 text-primary-light">
                          <SelectItem value="all">All Locations</SelectItem>
                          {availableLocations.map((location) => (
                            <SelectItem key={location} value={location}>{location}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </GridLayout>
                </UnifiedCard>
              </motion.div>
            )}
          </AnimatePresence>

                {/* Enhanced Events Grid */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                >
                  {loading ? (
                    /* Loading State */
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
                      {[1, 2, 3, 4].map((i) => (
                        <div key={i} className="bg-card backdrop-blur-md border border-border rounded-xl p-4 animate-pulse">
                          <div className="h-4 bg-muted rounded mb-2"></div>
                          <div className="h-3 bg-muted/50 rounded mb-4"></div>
                          <div className="h-8 bg-muted/50 rounded"></div>
                        </div>
                      ))}
                    </div>
                  ) : error ? (
                    /* Error State */
                    <div className="text-center py-12 sm:py-16">
                      <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5 }}
                        className="w-20 h-20 bg-red-500/10 rounded-full flex items-center justify-center mx-auto mb-6"
                      >
                        <AlertCircle className="w-10 h-10 text-red-400" />
                      </motion.div>
                      <h3 className="text-lg sm:text-xl font-medium text-muted-foreground mb-2">Failed to load events</h3>
                      <p className="text-muted-foreground text-sm sm:text-base mb-6 max-w-md mx-auto">
                        {error}
                      </p>
                      <motion.button
                        onClick={handleRefresh}
                        disabled={isRefreshing}
                        className="inline-flex items-center gap-2 px-4 py-2 bg-secondary hover:bg-secondary/80 rounded-lg text-secondary-foreground font-medium text-sm transition-all duration-300 disabled:opacity-50"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                        Try Again
                      </motion.button>
                    </div>
                  ) : filteredEvents.length > 0 ? (
                    <>
                      {/* Results Summary */}
                      <div className="flex items-center justify-between mb-4">
                        <p className="text-muted-foreground text-sm">
                          {filteredEvents.length} event{filteredEvents.length !== 1 ? 's' : ''} found
                        </p>
                        {searchQuery && (
                          <Badge variant="outline" className="text-xs">
                            "{searchQuery}"
                          </Badge>
                        )}
                      </div>

                      {/* Mobile-Optimized Events Grid */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
                        {filteredEvents.map((event, index) => (
                          <EventCard
                            key={event.id}
                            event={event}
                            index={index}
                            onClick={() => handleEventClick(event, null)}
                          />
                        ))}
                      </div>
                    </>
                  ) : (
                    /* Enhanced Empty State */
                    <div className="text-center py-12 sm:py-16">
                      <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5 }}
                        className="w-20 h-20 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-6"
                      >
                        <Compass className="w-10 h-10 text-muted-foreground" />
                      </motion.div>
                      <h3 className="text-lg sm:text-xl font-medium text-muted-foreground mb-2">No events found</h3>
                      <p className="text-muted-foreground text-sm sm:text-base mb-6 max-w-md mx-auto">
                        {searchQuery
                          ? `No events match "${searchQuery}". Try adjusting your search terms or filters.`
                          : 'Try adjusting your filters to discover amazing events.'
                        }
                      </p>
                      {(searchQuery || selectedCategory !== DiscoverFilters.ALL_CATEGORIES || selectedLocation !== 'all') && (
                        <motion.button
                          onClick={() => {
                            setSearchQuery('');
                            setSelectedCategory(DiscoverFilters.ALL_CATEGORIES);
                            setSelectedLocation('all');
                            simulateHapticFeedback('light');
                            toast.success('Filters cleared');
                          }}
                          className="inline-flex items-center gap-2 px-4 py-2 bg-secondary hover:bg-secondary/80 rounded-lg text-secondary-foreground font-medium text-sm transition-all duration-300"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          Clear Filters
                        </motion.button>
                      )}
                    </div>
                  )}
                </motion.div>

          {/* Development-only Mobile UX Testing Tool */}
          <MobileUXTester />

          {/* Enhanced Event Details Modal with ResponsiveModal System */}
          <EventDetailsModal
            event={selectedEvent}
            isOpen={!!selectedEvent}
            onClose={() => {
              setSelectedEvent(null);
              setTriggerElement(null);
            }}
            triggerElement={triggerElement}
          />
        </motion.div>
      </PageWrapper>
    </ErrorBoundary>
  );
};

export default Discover;

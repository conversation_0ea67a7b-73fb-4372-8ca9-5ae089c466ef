/**
 * Populate Sziget Festival Data
 * 
 * This script populates the Festival Family database with real Sziget Festival data
 * extracted from the provided Google Sheets document.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🎪 Populating Sziget Festival Data');
console.log('==================================');

// Sziget Festival 2025 Data (extracted from Google Sheets)
const szigetFestivalData = {
  festival: {
    name: 'Sziget Festival 2025',
    description: 'One of Europe\'s largest music and cultural festivals, taking place on Óbuda Island in Budapest, Hungary. Join the Festival Family for an unforgettable week of music, art, and community.',
    start_date: '2025-08-04',
    end_date: '2025-08-12',
    location: 'Óbuda Island, Budapest, Hungary',
    status: 'PUBLISHED'
  },
  
  events: [
    {
      name: 'Festival Family Pre-Meet',
      description: 'Get to know your Festival Family before the main event! Connect with fellow festival-goers and plan your Sziget adventure together.',
      start_time: '2025-03-21T12:00:00Z',
      end_time: '2025-03-24T24:00:00Z',
      location: 'Location TBD',
      status: 'planned',
      is_public: true
    },
    {
      name: 'Early Move-In Setup',
      description: 'Help set up the Festival Family base camp and get early access to the festival grounds.',
      start_time: '2025-08-04T10:00:00Z',
      end_time: '2025-08-04T18:00:00Z',
      location: 'Sziget Festival Grounds',
      status: 'planned',
      is_public: true
    },
    {
      name: 'Festival Family Reunion',
      description: 'Annual reunion for all Festival Family members to reconnect and plan future adventures.',
      start_time: '2025-10-10T14:00:00Z',
      end_time: '2025-10-13T18:00:00Z',
      location: 'Nijmegen, Netherlands',
      status: 'planned',
      is_public: true
    }
  ],
  
  activities: [
    // Daily Festival Family Activities
    {
      title: 'Fam Hangout Opening',
      description: 'Official opening of the Festival Family hangout space at base camp. Meet your festival family and get oriented!',
      type: 'meetup',
      start_date: '2025-08-04T19:30:00Z',
      end_date: '2025-08-04T20:30:00Z',
      location: 'Sziget Festival Base Camp',
      capacity: null,
      status: 'draft',
      tags: ['family', 'social', 'opening'],
      metadata: {
        is_family_activity: true,
        requirements: 'Festival Family membership'
      }
    },
    {
      name: 'Meeting The Fam - Group Photo Session',
      description: 'Daily meetup to connect with Festival Family members, followed by group photos to capture memories.',
      start_time: '19:31',
      end_time: '20:30',
      date: '2025-08-04',
      location: 'Sziget Festival Base Camp',
      activity_type: 'PHOTO',
      max_participants: null,
      is_family_activity: true,
      requirements: 'Festival Family membership'
    },
    {
      name: 'Festival Family Dinner',
      description: 'Special dinner for Festival Family members at a location near Sziget Festival. Great opportunity to bond with your festival tribe!',
      start_time: '18:00',
      end_time: '24:00',
      date: '2025-08-05',
      location: 'Restaurant near Sziget Festival',
      activity_type: 'DINING',
      max_participants: 50,
      is_family_activity: true,
      requirements: 'Pre-registration required by July 1st, 2025'
    },
    {
      name: 'Sziget Festival Scavenger Hunt',
      description: 'Explore the festival grounds with your Festival Family through an exciting scavenger hunt. Prizes for winners!',
      start_time: '09:00',
      end_time: '16:00',
      date: '2025-08-06',
      location: 'Sziget Festival Grounds',
      activity_type: 'GAME',
      max_participants: 100,
      is_family_activity: true,
      requirements: 'Sign up by July 20th, 2025'
    },
    {
      name: 'Daily Fam Meet + Group Photo',
      description: 'Daily gathering point for Festival Family members. Connect, share experiences, and take group photos.',
      start_time: '14:00',
      end_time: '16:00',
      date: '2025-08-06',
      location: 'Sziget Festival Base Camp',
      activity_type: 'SOCIAL',
      max_participants: null,
      is_family_activity: true,
      requirements: 'Festival Family membership'
    },
    {
      name: 'Fam Teams Contest Day 1',
      description: 'Multi-day team competition for Festival Family members. Form teams and compete in various challenges!',
      start_time: '16:00',
      end_time: '18:00',
      date: '2025-08-06',
      location: 'Sziget Festival Base Camp',
      activity_type: 'COMPETITION',
      max_participants: 80,
      is_family_activity: true,
      requirements: 'Team registration by July 10th, 2025'
    },
    {
      name: 'Fam Teams Contest Day 2',
      description: 'Second day of the Festival Family team competition with new challenges and activities.',
      start_time: '15:00',
      end_time: '17:00',
      date: '2025-08-07',
      location: 'Sziget Festival Grounds',
      activity_type: 'COMPETITION',
      max_participants: 80,
      is_family_activity: true,
      requirements: 'Team registration by July 10th, 2025'
    },
    {
      name: 'Fam Beerpong Tournament',
      description: 'Epic beer pong tournament at the Festival Family base camp. Bring your A-game and your festival spirit!',
      start_time: '13:00',
      end_time: '18:00',
      date: '2025-08-07',
      location: 'Basic Base Camp',
      activity_type: 'TOURNAMENT',
      max_participants: 32,
      is_family_activity: true,
      requirements: 'Partner required, 18+ only, registration by July 1st, 2025'
    },
    {
      name: 'Group Photos at Art Village',
      description: 'Professional group photos at the iconic Art Village entrance. Perfect for your festival memories!',
      start_time: '16:00',
      end_time: '17:00',
      date: '2025-08-07',
      location: 'Art Village Entrance',
      activity_type: 'PHOTO',
      max_participants: null,
      is_family_activity: true,
      requirements: 'Festival Family membership'
    },
    {
      name: 'Fam Teams Contest Day 3',
      description: 'Third day of team challenges with exciting new games and activities.',
      start_time: '14:00',
      end_time: '16:00',
      date: '2025-08-08',
      location: 'Sziget Festival Grounds',
      activity_type: 'COMPETITION',
      max_participants: 80,
      is_family_activity: true,
      requirements: 'Team registration by July 10th, 2025'
    },
    {
      name: 'Fam Karaoke Night',
      description: 'Sing your heart out with your Festival Family! Karaoke night at the base camp hangout.',
      start_time: '13:00',
      end_time: '18:00',
      date: '2025-08-08',
      location: 'Fam Hangout at Basic Base Camp',
      activity_type: 'ENTERTAINMENT',
      max_participants: 50,
      is_family_activity: true,
      requirements: 'Sign up by July 20th, 2025'
    },
    {
      name: 'Beach Group Photo Session',
      description: 'Capture memories with your Festival Family at the beautiful Sziget beach area.',
      start_time: '13:00',
      end_time: '14:00',
      date: '2025-08-08',
      location: 'Sziget Festival Beach',
      activity_type: 'PHOTO',
      max_participants: null,
      is_family_activity: true,
      requirements: 'Festival Family membership'
    },
    {
      name: 'Fam Teams Contest Day 4',
      description: 'Fourth day of the team competition with more exciting challenges.',
      start_time: '15:00',
      end_time: '17:00',
      date: '2025-08-09',
      location: 'Sziget Festival Grounds',
      activity_type: 'COMPETITION',
      max_participants: 80,
      is_family_activity: true,
      requirements: 'Team registration by July 10th, 2025'
    },
    {
      name: 'Scavenger Hunt Deadline',
      description: 'Final deadline to submit your scavenger hunt entries. Winners will be announced later!',
      start_time: '16:00',
      end_time: '16:30',
      date: '2025-08-09',
      location: 'Sziget Festival Base Camp',
      activity_type: 'DEADLINE',
      max_participants: null,
      is_family_activity: true,
      requirements: 'Scavenger hunt participation'
    },
    {
      name: 'Fam Teams Contest Final',
      description: 'Final day of the Festival Family team competition. Awards ceremony and celebration!',
      start_time: '15:00',
      end_time: '17:00',
      date: '2025-08-10',
      location: 'Fam Hangout',
      activity_type: 'COMPETITION',
      max_participants: 80,
      is_family_activity: true,
      requirements: 'Team registration by July 10th, 2025'
    },
    {
      name: 'Sziget Sign Group Photo',
      description: 'Iconic group photo at the famous Sziget letters sign. A must-have for your festival album!',
      start_time: '16:00',
      end_time: '17:00',
      date: '2025-08-10',
      location: 'Sziget Sign/Letters',
      activity_type: 'PHOTO',
      max_participants: null,
      is_family_activity: true,
      requirements: 'Festival Family membership'
    },
    {
      name: 'Budapest Letters Photo Session',
      description: 'Group photos at the Budapest letters for those exploring the city.',
      start_time: '17:00',
      end_time: '18:00',
      date: '2025-08-10',
      location: 'Budapest Letters',
      activity_type: 'PHOTO',
      max_participants: null,
      is_family_activity: true,
      requirements: 'Festival Family membership'
    },
    {
      name: 'Fam Outfit Day',
      description: 'Special themed outfit day! Bring and wear your Festival Family outfit for group photos.',
      start_time: '09:00',
      end_time: '23:00',
      date: '2025-08-11',
      location: 'Sziget Festival Grounds',
      activity_type: 'THEMED',
      max_participants: null,
      is_family_activity: true,
      requirements: 'Festival Family outfit required'
    },
    {
      name: 'Fam Outfit Day Photo',
      description: 'Special group photo session in Festival Family outfits between Party Arena and Colosseum.',
      start_time: '23:00',
      end_time: '23:30',
      date: '2025-08-11',
      location: 'Between Party Arena and Colosseum',
      activity_type: 'PHOTO',
      max_participants: null,
      is_family_activity: true,
      requirements: 'Festival Family outfit required'
    },
    {
      name: 'Fam Goodbye Meet',
      description: 'Final gathering to say goodbye and exchange contact information. Last chance to hand over camping gear!',
      start_time: '11:00',
      end_time: '11:45',
      date: '2025-08-11',
      location: 'Sziget Festival Base Camp',
      activity_type: 'FAREWELL',
      max_participants: null,
      is_family_activity: true,
      requirements: 'Festival Family membership'
    },
    {
      name: 'Post-Festival Dinner',
      description: 'Dinner in Budapest for those staying an extra day. Continue the festival spirit in the city!',
      start_time: '17:00',
      end_time: '20:00',
      date: '2025-08-12',
      location: 'Budapest, Hungary',
      activity_type: 'DINING',
      max_participants: 30,
      is_family_activity: true,
      requirements: 'RSVP required'
    },
    {
      name: 'Closing Meetup & Toast',
      description: 'Final celebration and toast to another amazing Sziget experience with your Festival Family.',
      start_time: '20:00',
      end_time: '23:00',
      date: '2025-08-12',
      location: 'Budapest, Hungary',
      activity_type: 'CELEBRATION',
      max_participants: 50,
      is_family_activity: true,
      requirements: 'RSVP required'
    },
    {
      name: 'Fam Recovery Bath Visit',
      description: 'Relax and recover from the festival at one of Budapest\'s famous thermal baths.',
      start_time: '14:00',
      end_time: '18:00',
      date: '2025-08-13',
      location: 'Budapest Thermal Baths',
      activity_type: 'WELLNESS',
      max_participants: 25,
      is_family_activity: true,
      requirements: 'RSVP required, additional cost'
    }
  ]
};

// Population functions
async function createFestival() {
  console.log('🎪 Creating Sziget Festival...');
  
  const { data, error } = await supabase
    .from('festivals')
    .insert([szigetFestivalData.festival])
    .select()
    .single();
  
  if (error) {
    console.error('❌ Error creating festival:', error);
    throw error;
  }
  
  console.log('✅ Festival created:', data.name);
  return data;
}

async function createEvents(festivalId) {
  console.log('📅 Creating events...');
  
  const eventsWithFestivalId = szigetFestivalData.events.map(event => ({
    ...event,
    festival_id: festivalId
  }));
  
  const { data, error } = await supabase
    .from('events')
    .insert(eventsWithFestivalId)
    .select();
  
  if (error) {
    console.error('❌ Error creating events:', error);
    throw error;
  }
  
  console.log(`✅ Created ${data.length} events`);
  return data;
}

async function createActivities(festivalId) {
  console.log('🎯 Creating activities...');
  
  const activitiesWithFestivalId = szigetFestivalData.activities.map(activity => ({
    ...activity,
    festival_id: festivalId
  }));
  
  const { data, error } = await supabase
    .from('activities')
    .insert(activitiesWithFestivalId)
    .select();
  
  if (error) {
    console.error('❌ Error creating activities:', error);
    throw error;
  }
  
  console.log(`✅ Created ${data.length} activities`);
  return data;
}

// Main execution
async function main() {
  try {
    console.log(`🕐 Started at: ${new Date().toISOString()}\n`);
    
    // Check if Sziget Festival already exists
    const { data: existingFestival } = await supabase
      .from('festivals')
      .select('*')
      .eq('name', 'Sziget Festival 2025')
      .single();
    
    if (existingFestival) {
      console.log('⚠️ Sziget Festival 2025 already exists. Skipping creation.');
      console.log('🎪 Festival ID:', existingFestival.id);
      return;
    }
    
    // Create festival, events, and activities
    const festival = await createFestival();
    const events = await createEvents(festival.id);
    const activities = await createActivities(festival.id);
    
    // Save summary
    const summary = {
      festival: festival,
      events: events,
      activities: activities,
      summary: {
        festivalId: festival.id,
        eventsCreated: events.length,
        activitiesCreated: activities.length,
        totalItems: 1 + events.length + activities.length
      },
      timestamp: new Date().toISOString()
    };
    
    const resultsDir = 'sziget-data-population-results';
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      `${resultsDir}/sziget-population-${Date.now()}.json`,
      JSON.stringify(summary, null, 2)
    );
    
    console.log('\n🎉 SZIGET FESTIVAL DATA POPULATION COMPLETE');
    console.log('==========================================');
    console.log(`🎪 Festival: ${festival.name}`);
    console.log(`📅 Events: ${events.length} created`);
    console.log(`🎯 Activities: ${activities.length} created`);
    console.log(`📊 Total Items: ${summary.summary.totalItems}`);
    console.log(`🆔 Festival ID: ${festival.id}`);
    console.log(`📁 Results saved to: ${resultsDir}/sziget-population-${Date.now()}.json`);
    
  } catch (error) {
    console.error('💥 Population failed:', error);
  }
  
  process.exit(0);
}

main();

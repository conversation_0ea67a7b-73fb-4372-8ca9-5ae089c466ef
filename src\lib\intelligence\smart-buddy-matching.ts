/**
 * Smart Festival Buddy Matching System
 * 
 * AI-powered matching system that leverages our standardized architecture
 * to connect festival-goers based on music preferences, activity interests,
 * location proximity, and behavioral patterns.
 * 
 * @module SmartBuddyMatching
 * @version 1.0.0
 */

import { BaseService, ServiceResponse } from '../supabase/services/base-service';
import type { Profile } from '@/types';

// ============================================================================
// MATCHING TYPES
// ============================================================================

export interface MusicCompatibility {
  sharedGenres: string[];
  sharedArtists: string[];
  compatibilityScore: number; // 0-100
  topMatches: string[];
}

export interface ActivityCompatibility {
  sharedInterests: string[];
  sharedActivities: string[];
  compatibilityScore: number; // 0-100
  recommendedActivities: string[];
}

export interface LocationCompatibility {
  distance: number; // in km
  sameCity: boolean;
  sameRegion: boolean;
  compatibilityScore: number; // 0-100
}

export interface BuddyMatchScore {
  overall: number; // 0-100
  music: number;
  activities: number;
  location: number;
  behavioral: number;
  confidence: 'low' | 'medium' | 'high';
}

export interface SmartBuddyMatch {
  profile: Profile;
  matchScore: BuddyMatchScore;
  musicCompatibility: MusicCompatibility;
  activityCompatibility: ActivityCompatibility;
  locationCompatibility: LocationCompatibility;
  matchReasons: string[];
  suggestedActivities: string[];
  estimatedConnectionStrength: 'weak' | 'moderate' | 'strong';
}

export interface MatchingFilters {
  maxDistance?: number; // km
  minMusicCompatibility?: number; // 0-100
  minActivityCompatibility?: number; // 0-100
  ageRange?: [number, number];
  preferredGenders?: string[];
  onlyActiveUsers?: boolean;
  excludeExistingConnections?: boolean;
}

export interface MatchingPreferences {
  prioritizeMusic: boolean;
  prioritizeActivities: boolean;
  prioritizeLocation: boolean;
  openToNewGenres: boolean;
  preferGroupActivities: boolean;
  maxMatches: number;
}

// ============================================================================
// SMART BUDDY MATCHING SERVICE
// ============================================================================

export class SmartBuddyMatchingService extends BaseService {
  private readonly MUSIC_WEIGHT = 0.3;
  private readonly ACTIVITY_WEIGHT = 0.4;
  private readonly LOCATION_WEIGHT = 0.2;
  private readonly BEHAVIORAL_WEIGHT = 0.1;

  /**
   * Find smart buddy matches for a user
   */
  async findSmartMatches(
    userId: string,
    filters: MatchingFilters = {},
    preferences: MatchingPreferences = {
      prioritizeMusic: true,
      prioritizeActivities: true,
      prioritizeLocation: false,
      openToNewGenres: true,
      preferGroupActivities: false,
      maxMatches: 10
    }
  ): Promise<ServiceResponse<SmartBuddyMatch[]>> {
    try {
      console.log(`🧠 Finding smart matches for user ${userId}...`);

      // Get user's comprehensive profile
      const userProfile = await this.getUserComprehensiveProfile(userId);
      if (!userProfile.data) {
        return { data: [], error: new Error('User profile not found'), status: 'error' };
      }

      // Get potential candidates
      const candidates = await this.getPotentialCandidates(userId, filters);
      if (!candidates.data || candidates.data.length === 0) {
        return { data: [], error: null, status: 'success' };
      }

      // Calculate compatibility scores for each candidate
      const matches: SmartBuddyMatch[] = [];
      
      for (const candidate of candidates.data) {
        const match = await this.calculateBuddyMatch(
          userProfile.data,
          candidate,
          preferences
        );
        
        if (match && match.matchScore.overall >= 30) { // Minimum 30% compatibility
          matches.push(match);
        }
      }

      // Sort by overall match score
      matches.sort((a, b) => b.matchScore.overall - a.matchScore.overall);

      // Limit results
      const limitedMatches = matches.slice(0, preferences.maxMatches);

      console.log(`✨ Found ${limitedMatches.length} smart matches`);
      
      return {
        data: limitedMatches,
        error: null,
        status: 'success'
      };

    } catch (error) {
      console.error('❌ Smart matching failed:', error);
      return {
        data: [],
        error: error as Error,
        status: 'error'
      };
    }
  }

  /**
   * Get user's comprehensive profile including preferences and history
   */
  private async getUserComprehensiveProfile(userId: string) {
    try {
      // Get basic profile
      const { data: profile } = await this.client
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (!profile) {
        return { data: null, error: new Error('Profile not found'), status: 'error' as const };
      }

      // Get music preferences
      const { data: musicGenres } = await this.client
        .from('music_genre_preferences')
        .select('genre, preference_level')
        .eq('user_id', userId);

      const { data: artistPrefs } = await this.client
        .from('artist_preferences')
        .select('artist_name, preference_level, genre')
        .eq('user_id', userId);

      // Get activity history
      const { data: activityHistory } = await this.client
        .from('activity_participants')
        .select(`
          activities (type, title, location, tags)
        `)
        .eq('user_id', userId)
        .eq('status', 'registered');

      // Get user preferences
      const { data: userPreferences } = await this.client
        .from('user_preferences')
        .select('preference_type, preference_key, preference_value, weight')
        .eq('user_id', userId);

      return {
        data: {
          profile,
          musicGenres: musicGenres || [],
          artistPreferences: artistPrefs || [],
          activityHistory: activityHistory || [],
          userPreferences: userPreferences || []
        },
        error: null,
        status: 'success' as const
      };

    } catch (error) {
      return {
        data: null,
        error: error as Error,
        status: 'error' as const
      };
    }
  }

  /**
   * Get potential matching candidates
   */
  private async getPotentialCandidates(userId: string, filters: MatchingFilters) {
    try {
      let query = this.client
        .from('profiles')
        .select('*')
        .neq('id', userId);

      // Filter out existing connections if requested
      if (filters.excludeExistingConnections) {
        const { data: existingConnections } = await this.client
          .from('connections')
          .select('connected_user_id')
          .eq('user_id', userId);

        if (existingConnections && existingConnections.length > 0) {
          const connectedIds = existingConnections.map(c => c.connected_user_id);
          query = query.not('id', 'in', `(${connectedIds.join(',')})`);
        }
      }

      // Only active users if requested
      if (filters.onlyActiveUsers) {
        // Consider users who have been active in the last 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        query = query.gte('updated_at', thirtyDaysAgo.toISOString());
      }

      const { data: candidates } = await query.limit(50); // Reasonable limit for processing

      return {
        data: candidates || [],
        error: null,
        status: 'success' as const
      };

    } catch (error) {
      return {
        data: [],
        error: error as Error,
        status: 'error' as const
      };
    }
  }

  /**
   * Calculate comprehensive buddy match score
   */
  private async calculateBuddyMatch(
    userProfile: any,
    candidateProfile: Profile,
    preferences: MatchingPreferences
  ): Promise<SmartBuddyMatch | null> {
    try {
      // Get candidate's preferences and history
      const candidateData = await this.getUserComprehensiveProfile(candidateProfile.id);
      if (!candidateData.data) return null;

      // Calculate music compatibility
      const musicCompatibility = this.calculateMusicCompatibility(
        userProfile,
        candidateData.data
      );

      // Calculate activity compatibility
      const activityCompatibility = this.calculateActivityCompatibility(
        userProfile,
        candidateData.data
      );

      // Calculate location compatibility
      const locationCompatibility = this.calculateLocationCompatibility(
        userProfile.profile,
        candidateProfile
      );

      // Calculate behavioral compatibility
      const behavioralScore = this.calculateBehavioralCompatibility(
        userProfile,
        candidateData.data
      );

      // Calculate weighted overall score
      const weights = this.getAdjustedWeights(preferences);
      const overallScore = Math.round(
        musicCompatibility.compatibilityScore * weights.music +
        activityCompatibility.compatibilityScore * weights.activities +
        locationCompatibility.compatibilityScore * weights.location +
        behavioralScore * weights.behavioral
      );

      // Generate match reasons
      const matchReasons = this.generateMatchReasons(
        musicCompatibility,
        activityCompatibility,
        locationCompatibility,
        behavioralScore
      );

      // Suggest activities
      const suggestedActivities = this.suggestActivities(
        userProfile,
        candidateData.data
      );

      // Determine confidence level
      const confidence = this.determineConfidence(overallScore, matchReasons.length);

      // Estimate connection strength
      const connectionStrength = this.estimateConnectionStrength(overallScore);

      const matchScore: BuddyMatchScore = {
        overall: overallScore,
        music: musicCompatibility.compatibilityScore,
        activities: activityCompatibility.compatibilityScore,
        location: locationCompatibility.compatibilityScore,
        behavioral: behavioralScore,
        confidence
      };

      return {
        profile: candidateProfile,
        matchScore,
        musicCompatibility,
        activityCompatibility,
        locationCompatibility,
        matchReasons,
        suggestedActivities,
        estimatedConnectionStrength: connectionStrength
      };

    } catch (error) {
      console.error('❌ Error calculating buddy match:', error);
      return null;
    }
  }

  /**
   * Calculate music compatibility between two users
   */
  private calculateMusicCompatibility(userProfile: any, candidateProfile: any): MusicCompatibility {
    const userGenres = (userProfile.musicGenres || []);
    const candidateGenres = (candidateProfile.musicGenres || []);

    const userArtists = (userProfile.artistPreferences || []);
    const candidateArtists = (candidateProfile.artistPreferences || []);

    // Find shared genres
    const sharedGenres: string[] = [];
    userGenres.forEach((userGenre: any) => {
      const match = candidateGenres.find((candidateGenre: any) =>
        candidateGenre.genre === userGenre.genre
      );
      if (match) {
        sharedGenres.push(userGenre.genre);
      }
    });

    // Find shared artists
    const sharedArtists: string[] = [];
    userArtists.forEach((userArtist: any) => {
      const match = candidateArtists.find((candidateArtist: any) =>
        candidateArtist.artist_name === userArtist.artist_name
      );
      if (match) {
        sharedArtists.push(userArtist.artist_name);
      }
    });

    // Calculate compatibility score with realistic algorithm
    let compatibilityScore = 30; // Base compatibility score

    // Genre compatibility (40% weight)
    if (userGenres.length > 0 && candidateGenres.length > 0) {
      const genreScore = (sharedGenres.length / Math.max(userGenres.length, candidateGenres.length)) * 40;
      compatibilityScore += genreScore;
    }

    // Artist compatibility (30% weight)
    if (userArtists.length > 0 && candidateArtists.length > 0) {
      const artistScore = (sharedArtists.length / Math.max(userArtists.length, candidateArtists.length)) * 30;
      compatibilityScore += artistScore;
    }

    // Bonus for having any music preferences at all (shows engagement)
    if ((userGenres.length > 0 || userArtists.length > 0) &&
        (candidateGenres.length > 0 || candidateArtists.length > 0)) {
      compatibilityScore += 10;
    }

    return {
      sharedGenres,
      sharedArtists,
      compatibilityScore: Math.min(Math.round(compatibilityScore), 100),
      topMatches: [...sharedGenres, ...sharedArtists].slice(0, 5)
    };
  }

  /**
   * Calculate activity compatibility between two users
   */
  private calculateActivityCompatibility(userProfile: any, candidateProfile: any): ActivityCompatibility {
    const userInterests = userProfile.profile.interests || [];
    const candidateInterests = candidateProfile.profile.interests || [];
    
    const userActivities = userProfile.activityHistory.map((h: any) => h.activities?.type).filter(Boolean);
    const candidateActivities = candidateProfile.activityHistory.map((h: any) => h.activities?.type).filter(Boolean);

    const sharedInterests = userInterests.filter((interest: string) => candidateInterests.includes(interest));
    const sharedActivities = userActivities.filter((activity: string) => candidateActivities.includes(activity));

    // Calculate compatibility score
    const interestScore = userInterests.length > 0 ? (sharedInterests.length / userInterests.length) * 100 : 0;
    const activityScore = userActivities.length > 0 ? (sharedActivities.length / userActivities.length) * 100 : 0;
    
    const compatibilityScore = Math.round((interestScore * 0.7 + activityScore * 0.3));

    return {
      sharedInterests,
      sharedActivities,
      compatibilityScore,
      recommendedActivities: [...new Set([...sharedInterests, ...sharedActivities])].slice(0, 5)
    };
  }

  /**
   * Calculate location compatibility between two users
   */
  private calculateLocationCompatibility(userProfile: Profile, candidateProfile: Profile): LocationCompatibility {
    const userLocation = userProfile.location;
    const candidateLocation = candidateProfile.location;

    if (!userLocation || !candidateLocation) {
      return {
        distance: 0,
        sameCity: false,
        sameRegion: false,
        compatibilityScore: 50 // Neutral score when location unknown
      };
    }

    const sameCity = userLocation.toLowerCase() === candidateLocation.toLowerCase();
    const sameRegion = this.isSameRegion(userLocation, candidateLocation);
    
    // Simplified distance calculation (would use actual geolocation in production)
    const distance = sameCity ? 0 : sameRegion ? 50 : 200;
    
    let compatibilityScore = 100;
    if (!sameCity) {
      compatibilityScore = sameRegion ? 70 : 30;
    }

    return {
      distance,
      sameCity,
      sameRegion,
      compatibilityScore
    };
  }

  /**
   * Calculate behavioral compatibility score
   */
  private calculateBehavioralCompatibility(userProfile: any, candidateProfile: any): number {
    // Simple behavioral scoring based on activity patterns
    const userActivityCount = userProfile.activityHistory.length;
    const candidateActivityCount = candidateProfile.activityHistory.length;
    
    // Users with similar activity levels tend to be more compatible
    const activityDifference = Math.abs(userActivityCount - candidateActivityCount);
    const maxActivities = Math.max(userActivityCount, candidateActivityCount, 1);
    
    return Math.round(Math.max(0, 100 - (activityDifference / maxActivities) * 100));
  }

  /**
   * Get adjusted weights based on user preferences
   */
  private getAdjustedWeights(preferences: MatchingPreferences) {
    let weights = {
      music: this.MUSIC_WEIGHT,
      activities: this.ACTIVITY_WEIGHT,
      location: this.LOCATION_WEIGHT,
      behavioral: this.BEHAVIORAL_WEIGHT
    };

    // Adjust weights based on preferences
    if (preferences.prioritizeMusic) {
      weights.music *= 1.5;
    }
    if (preferences.prioritizeActivities) {
      weights.activities *= 1.5;
    }
    if (preferences.prioritizeLocation) {
      weights.location *= 2;
    }

    // Normalize weights to sum to 1
    const total = weights.music + weights.activities + weights.location + weights.behavioral;
    weights.music /= total;
    weights.activities /= total;
    weights.location /= total;
    weights.behavioral /= total;

    return weights;
  }

  /**
   * Generate human-readable match reasons
   */
  private generateMatchReasons(
    music: MusicCompatibility,
    activities: ActivityCompatibility,
    location: LocationCompatibility,
    behavioral: number
  ): string[] {
    const reasons: string[] = [];

    if (music.sharedGenres.length > 0) {
      reasons.push(`🎵 You both love ${music.sharedGenres.slice(0, 2).join(' and ')} music`);
    }
    
    if (music.sharedArtists.length > 0) {
      reasons.push(`🎤 You both enjoy ${music.sharedArtists.slice(0, 2).join(' and ')}`);
    }

    if (activities.sharedInterests.length > 0) {
      reasons.push(`🎯 You share interests in ${activities.sharedInterests.slice(0, 2).join(' and ')}`);
    }

    if (location.sameCity) {
      reasons.push(`📍 You're both in the same city`);
    } else if (location.sameRegion) {
      reasons.push(`🗺️ You're in the same region`);
    }

    if (behavioral > 70) {
      reasons.push(`⚡ You have similar activity levels`);
    }

    return reasons;
  }

  /**
   * Suggest activities for matched users
   */
  private suggestActivities(userProfile: any, candidateProfile: any): string[] {
    const suggestions: string[] = [];
    
    // Suggest based on shared interests
    const sharedInterests = (userProfile.profile.interests || [])
      .filter((interest: string) => (candidateProfile.profile.interests || []).includes(interest));
    
    suggestions.push(...sharedInterests.slice(0, 3));

    // Add some general festival activities
    suggestions.push('Music Discovery Session', 'Festival Food Tour', 'Photo Walk');

    return [...new Set(suggestions)].slice(0, 5);
  }

  /**
   * Determine confidence level based on score and reasons
   */
  private determineConfidence(score: number, reasonCount: number): 'low' | 'medium' | 'high' {
    if (score >= 80 && reasonCount >= 3) return 'high';
    if (score >= 60 && reasonCount >= 2) return 'medium';
    return 'low';
  }

  /**
   * Estimate connection strength
   */
  private estimateConnectionStrength(score: number): 'weak' | 'moderate' | 'strong' {
    if (score >= 75) return 'strong';
    if (score >= 50) return 'moderate';
    return 'weak';
  }

  /**
   * Check if two locations are in the same region (simplified)
   */
  private isSameRegion(location1: string, location2: string): boolean {
    // Simplified region matching - in production, use proper geolocation
    const region1 = location1.split(',')[0]?.trim().toLowerCase();
    const region2 = location2.split(',')[0]?.trim().toLowerCase();
    return region1 === region2;
  }
}

// Create and export singleton instance
export const smartBuddyMatchingService = new SmartBuddyMatchingService();

import React, { useState } from 'react';
import { Calendar, MapPin, Users, Star, Filter, Search, Heart, ExternalLink } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { cn } from '../lib/utils';
import { FestivalCard } from '../components/festivals/FestivalCard';
import { useFestivals } from '../hooks/useFestivals';
import { PageWrapper } from '../components/design-system';

const Festivals: React.FC = () => {
  const { festivals, isLoading, error } = useFestivals();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  // Filter festivals based on search and status
  const filteredFestivals = festivals.filter(festival => {
    const matchesSearch = festival.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         festival.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         festival.location?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'featured' && festival.is_active) || // Use is_active instead of featured
                         (statusFilter === 'upcoming' && festival.status === 'PUBLISHED');
    
    return matchesSearch && matchesStatus;
  });

  const toggleFavorite = (festivalId: string) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(festivalId)) {
      newFavorites.delete(festivalId);
    } else {
      newFavorites.add(festivalId);
    }
    setFavorites(newFavorites);
  };

  if (isLoading) {
    return (
      <PageWrapper title="Festivals" subtitle="Discover amazing festivals">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading festivals...</p>
          </div>
        </div>
      </PageWrapper>
    );
  }

  if (error) {
    return (
      <PageWrapper title="Festivals" subtitle="Discover amazing festivals">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-center">
            <p className="text-destructive mb-4">Error loading festivals</p>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper title="Festivals" subtitle="Discover amazing festivals">
      <div className="space-y-6">
        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search festivals, locations, or genres..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant={statusFilter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('all')}
            >
              <Filter className="w-4 h-4 mr-2" />
              All
            </Button>
            <Button
              variant={statusFilter === 'featured' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('featured')}
            >
              <Star className="w-4 h-4 mr-2" />
              Featured
            </Button>
            <Button
              variant={statusFilter === 'upcoming' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('upcoming')}
            >
              <Calendar className="w-4 h-4 mr-2" />
              Upcoming
            </Button>
          </div>
        </div>

        {/* Festival Count */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-primary" />
            <h1 className="text-2xl font-bold">Festivals</h1>
            <span className="text-muted-foreground">{filteredFestivals.length} festivals</span>
          </div>
        </div>

        {/* Festivals Grid */}
        {filteredFestivals.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold text-foreground mb-2">No festivals found</h3>
              <p className="text-muted-foreground text-center">
                Try adjusting your search or filter criteria.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredFestivals.map((festival) => (
              <FestivalCard
                key={festival.id}
                festival={festival}
                isFeatured={festival.is_active || false} // Use is_active instead of featured
                showAttendees={true}
              />
            ))}
          </div>
        )}
      </div>
    </PageWrapper>
  );
};

export default Festivals;

# Festival Family - Development Guide

## 🎯 **Quick Start for Developers**

This guide provides essential information for developers working on Festival Family after the comprehensive architectural cleanup.

**Status:** ✅ Single Source of Truth Architecture Implemented  
**Last Updated:** December 2024

---

## 🚀 **Essential Patterns**

### **1. Database Operations**

```typescript
// ✅ ALWAYS use the main client
import { supabase } from '@/lib/supabase'

// ✅ Use actual database field names
const { data: events } = await supabase
  .from('events')
  .select('id, name, description, start_date, end_date, location, festival_id')

// ✅ Handle proper ID types
const eventId: number = event.id        // events use number IDs
const profileId: string = profile.id    // profiles use string IDs
```

### **2. Type Imports**

```typescript
// ✅ CORRECT - Import from main types
import { Event, Activity, Profile, Festival } from '@/types'

// ❌ INCORRECT - Don't create duplicate types
interface MyEvent { ... } // Don't do this
```

### **3. Component Props**

```typescript
// ✅ CORRECT - Use database-aligned types
interface EventCardProps {
  event: Event  // Uses actual database fields
}

const EventCard: React.FC<EventCardProps> = ({ event }) => {
  return (
    <div>
      <h3>{event.name}</h3>  {/* NOT event.title */}
      <p>{event.description}</p>
      <span>{event.start_date}</span>  {/* NOT event.start_time */}
    </div>
  )
}
```

---

## 📊 **Database Schema Reference**

### **Critical Field Mappings**

| Entity | Use This Field | NOT This Field | Type |
|--------|---------------|----------------|------|
| **Events** | `name` | `title` | `string` |
| **Events** | `start_date` | `start_time` | `string` |
| **Events** | `end_date` | `end_time` | `string` |
| **Events** | `id` | - | `number` |
| **Activities** | `name` | `title` | `string` |
| **Activities** | `start_time` | `start_date` | `string \| null` |
| **Activities** | `end_time` | `end_date` | `string \| null` |
| **Activities** | `id` | - | `number` |
| **Announcements** | `title` | `name` | `string` |
| **Announcements** | `id` | - | `number` |
| **Profiles** | `id` | - | `string` |

### **Fields That DON'T Exist**

```typescript
// ❌ These fields were removed during cleanup - DON'T USE
event.image_url        // Use placeholder images
event.status           // Use computed status
event.is_active        // Use date-based logic
announcement.is_active // All announcements are active
announcement.priority  // Use created_at for ordering
activity.status        // Use computed status
activity.tags          // Use type field instead
activity.metadata      // Use individual fields
```

---

## 🔧 **Common Development Tasks**

### **Creating a New Component**

```typescript
// 1. Import types correctly
import { Event } from '@/types'
import { supabase } from '@/lib/supabase'

// 2. Define props with actual database types
interface MyComponentProps {
  event: Event
}

// 3. Use actual database fields
const MyComponent: React.FC<MyComponentProps> = ({ event }) => {
  return (
    <div>
      <h2>{event.name}</h2>  {/* ✅ Correct field */}
      <p>{event.description}</p>
      <span>{new Date(event.start_date).toLocaleDateString()}</span>
    </div>
  )
}
```

### **Fetching Data**

```typescript
// ✅ CORRECT - Use actual database fields
const fetchEvents = async () => {
  const { data, error } = await supabase
    .from('events')
    .select(`
      id,
      name,
      description,
      start_date,
      end_date,
      location,
      festival_id,
      festivals (
        id,
        name
      )
    `)
    .order('start_date', { ascending: true })

  if (error) throw error
  return data
}
```

### **Form Handling**

```typescript
// ✅ CORRECT - Use database-aligned form schema
const eventSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  start_date: z.string(),
  end_date: z.string(),
  location: z.string().optional(),
  festival_id: z.number()
})

type EventFormData = z.infer<typeof eventSchema>
```

---

## 🚨 **Common Pitfalls to Avoid**

### **1. Wrong Field Names**

```typescript
// ❌ WRONG - These fields don't exist
event.title           // Use event.name
event.image_url       // Use placeholder image
announcement.is_active // Field doesn't exist
activity.start_date   // Use activity.start_time

// ✅ CORRECT - Use actual database fields
event.name
event.description
announcement.title
activity.start_time
```

### **2. Wrong ID Types**

```typescript
// ❌ WRONG - Incorrect ID types
const eventId: string = event.id  // events use number IDs
const profileId: number = profile.id  // profiles use string IDs

// ✅ CORRECT - Proper ID types
const eventId: number = event.id
const profileId: string = profile.id
```

### **3. Wrong Import Paths**

```typescript
// ❌ WRONG - Don't use these paths
import { supabase } from '../lib/supabase/client'
import { supabase } from './lib/supabase/core-client'
import { Event } from '../types/core'

// ✅ CORRECT - Use standardized paths
import { supabase } from '@/lib/supabase'
import { Event } from '@/types'
```

---

## 🔍 **Debugging Guide**

### **TypeScript Errors**

```typescript
// Error: Property 'title' does not exist on type 'Event'
// Solution: Use 'name' instead of 'title'
event.name // ✅

// Error: Property 'image_url' does not exist on type 'Event'
// Solution: Use placeholder image
const imageUrl = '/default-event-image.jpg' // ✅

// Error: Type 'string' is not assignable to type 'number'
// Solution: Check ID types in database schema
const eventId: number = event.id // ✅
```

### **Database Errors**

```sql
-- Error: column "is_active" does not exist
-- Solution: Field was removed, use alternative logic

-- Error: column "image_url" does not exist  
-- Solution: Field doesn't exist, use placeholder images

-- Error: invalid input syntax for type integer: "string-id"
-- Solution: Check ID types (some are numbers, some are strings)
```

---

## 📝 **Code Review Checklist**

### **Before Submitting PR**

- [ ] Uses `@/` import aliases
- [ ] Uses actual database field names
- [ ] Proper ID types (number vs string)
- [ ] No references to non-existent fields
- [ ] Imports from `@/lib/supabase` for client
- [ ] Imports from `@/types` for types
- [ ] Error handling implemented
- [ ] TypeScript compilation passes

### **Reviewing Others' Code**

- [ ] Check import paths are standardized
- [ ] Verify database field names are correct
- [ ] Confirm ID types match database schema
- [ ] Ensure no duplicate client instances
- [ ] Validate type usage is consistent

---

## 🛠️ **Development Tools**

### **Useful Commands**

```bash
# Check TypeScript compilation
npm run type-check

# Run development server
npm run dev

# Build for production
npm run build

# Run tests
npm run test
```

### **VS Code Extensions**

- TypeScript Importer
- Auto Import - ES6, TS, JSX, TSX
- Supabase Snippets

---

## 📚 **Additional Resources**

- [Database Schema Reference](./ARCHITECTURE.md#database-schema--types)
- [Import Path Standards](./ARCHITECTURE.md#import-path-standards)
- [Service Layer Guide](./ARCHITECTURE.md#service-layer-architecture)
- [Supabase Documentation](https://supabase.com/docs)

---

## 🎉 **Success Metrics**

After following this guide, you should achieve:

- ✅ Zero TypeScript compilation errors
- ✅ Consistent import patterns
- ✅ Proper database field usage
- ✅ Type-safe component development
- ✅ Maintainable, scalable code

---

**Happy Coding! 🚀**

*This guide reflects the current state after comprehensive architectural cleanup. All patterns and examples have been validated against the actual codebase.*

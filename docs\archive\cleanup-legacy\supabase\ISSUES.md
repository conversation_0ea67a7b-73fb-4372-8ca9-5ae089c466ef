# Supabase Integration Issues

## 1. Duplicate Client Initialization

### Description
The Supabase client is initialized in multiple locations throughout the codebase, leading to inconsistent configuration and potential bugs.

### Locations
- `src/lib/supabase.ts`
- `src/lib/supabase/client.ts`

### Example
```typescript
// src/lib/supabase.ts
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    storage: window.localStorage,
  }
})

// src/lib/supabase/client.ts (different configuration)
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

export const supabase = createClient<Database>(supabaseUrl, supabase<PERSON><PERSON><PERSON><PERSON>, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    storage: window.localStorage,
  }
})
```

### Impact
- Inconsistent client behavior depending on import source
- Potential memory leaks from multiple client instances
- Difficulty tracking authentication state
- Increased bundle size

## 2. Inconsistent Service Abstractions

### Description
Some parts of the codebase use the Supabase client directly, while others use service abstractions, leading to inconsistent patterns and potential duplication of logic.

### Patterns
- Direct client usage: `supabase.from('table').select()`
- Service abstraction: `authService.signIn()`
- Mixed approaches within the same component

### Impact
- Inconsistent error handling
- Duplicated business logic
- Difficulty implementing cross-cutting concerns
- Challenges with testing and mocking

## 3. Inadequate Error Handling

### Description
Error handling for Supabase operations is inconsistent, with some code throwing errors and other code returning them as part of a response object.

### Patterns
```typescript
// Pattern 1: Throwing errors
async function getProfile(userId: string) {
  const { data, error } = await supabase
    .from('profiles')
    .select()
    .eq('id', userId)
    .single()
  
  if (error) throw error
  return data
}

// Pattern 2: Returning errors
async function getProfile(userId: string) {
  const { data, error } = await supabase
    .from('profiles')
    .select()
    .eq('id', userId)
    .single()
  
  return { data, error }
}
```

### Impact
- Inconsistent error handling across the application
- Difficulty implementing global error handling
- Potential unhandled exceptions
- Poor user experience when errors occur

## 4. Missing Type Safety

### Description
Some Supabase operations lack proper type safety, using generic types or type assertions without validation.

### Examples
```typescript
// Missing type safety
const { data } = await supabase.from('profiles').select()
return data as Profile[] // Unsafe type assertion

// Missing null checks
const { data } = await supabase.from('profiles').select().eq('id', userId).single()
return data.name // Potential null reference
```

### Impact
- Runtime type errors
- Null reference exceptions
- Difficulty refactoring
- Reduced IDE assistance

## 5. Inefficient Query Patterns

### Description
Some queries fetch more data than necessary or make multiple requests when a single request would suffice.

### Examples
```typescript
// Inefficient: Fetching all fields when only a few are needed
const { data } = await supabase.from('festivals').select('*')

// Inefficient: Multiple requests instead of joins
const { data: festival } = await supabase.from('festivals').select().eq('id', festivalId).single()
const { data: activities } = await supabase.from('activities').select().eq('festival_id', festivalId)
```

### Impact
- Increased bandwidth usage
- Slower page loads
- Unnecessary processing
- Potential race conditions
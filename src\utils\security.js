/**
 * Security Utilities
 * 
 * This module provides security utilities for input sanitization,
 * XSS protection, and other security measures.
 */

// Environment-based security configuration
const isDevelopment = import.meta.env.DEV;
const isProduction = import.meta.env.PROD;

/**
 * XSS Protection Configuration
 */
const XSS_CONFIG = {
  // Development: More lenient for testing
  development: {
    allowedTags: ['b', 'i', 'em', 'strong', 'p', 'br'],
    allowedAttributes: {},
    stripTags: false,
    logSanitization: true
  },
  // Production: Strict security
  production: {
    allowedTags: [],
    allowedAttributes: {},
    stripTags: true,
    logSanitization: false
  }
};

/**
 * Get current security configuration based on environment
 */
function getSecurityConfig() {
  return isDevelopment ? XSS_CONFIG.development : XSS_CONFIG.production;
}

/**
 * Basic HTML entity encoding for XSS protection
 */
function encodeHtmlEntities(str) {
  if (typeof str !== 'string') return str;
  
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

/**
 * Decode HTML entities (for display purposes)
 */
function decodeHtmlEntities(str) {
  if (typeof str !== 'string') return str;
  
  return str
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#x27;/g, "'")
    .replace(/&#x2F;/g, '/');
}

/**
 * Remove potentially dangerous HTML tags and attributes
 */
function stripDangerousHtml(str) {
  if (typeof str !== 'string') return str;
  
  const config = getSecurityConfig();
  
  // Remove script tags and their content
  str = str.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  
  // Remove dangerous event handlers
  str = str.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '');
  
  // Remove javascript: URLs
  str = str.replace(/javascript:/gi, '');
  
  // Remove data: URLs (can contain scripts)
  str = str.replace(/data:/gi, '');
  
  // Remove vbscript: URLs
  str = str.replace(/vbscript:/gi, '');
  
  // Remove dangerous tags
  const dangerousTags = [
    'script', 'object', 'embed', 'link', 'style', 'iframe', 'frame', 
    'frameset', 'applet', 'meta', 'form', 'input', 'button', 'textarea'
  ];
  
  dangerousTags.forEach(tag => {
    const regex = new RegExp(`<${tag}\\b[^>]*>.*?<\\/${tag}>`, 'gi');
    str = str.replace(regex, '');
    
    // Also remove self-closing versions
    const selfClosingRegex = new RegExp(`<${tag}\\b[^>]*\\/>`, 'gi');
    str = str.replace(selfClosingRegex, '');
  });
  
  if (config.stripTags) {
    // In production, strip all HTML tags
    str = str.replace(/<[^>]*>/g, '');
  }
  
  return str;
}

/**
 * Comprehensive input sanitization
 */
export function sanitizeInput(input, options = {}) {
  if (typeof input !== 'string') return input;
  
  const config = getSecurityConfig();
  const {
    allowHtml = false,
    maxLength = 10000,
    trimWhitespace = true,
    logSanitization = config.logSanitization
  } = options;
  
  let sanitized = input;
  const original = input;
  
  // Trim whitespace if requested
  if (trimWhitespace) {
    sanitized = sanitized.trim();
  }
  
  // Enforce maximum length
  if (sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength);
  }
  
  // Apply XSS protection
  if (allowHtml) {
    // Strip dangerous HTML but allow safe tags
    sanitized = stripDangerousHtml(sanitized);
  } else {
    // Encode all HTML entities
    sanitized = encodeHtmlEntities(sanitized);
  }
  
  // Log sanitization in development
  if (logSanitization && sanitized !== original) {
    console.log('🧹 Input sanitized:', {
      original: original.substring(0, 100) + (original.length > 100 ? '...' : ''),
      sanitized: sanitized.substring(0, 100) + (sanitized.length > 100 ? '...' : ''),
      environment: isDevelopment ? 'development' : 'production'
    });
  }
  
  return sanitized;
}

/**
 * Sanitize user profile data
 */
export function sanitizeProfileData(profileData) {
  const sanitized = { ...profileData };
  
  // Sanitize text fields
  if (sanitized.full_name) {
    sanitized.full_name = sanitizeInput(sanitized.full_name, { maxLength: 100 });
  }
  
  if (sanitized.username) {
    sanitized.username = sanitizeInput(sanitized.username, { maxLength: 50 });
  }
  
  if (sanitized.bio) {
    sanitized.bio = sanitizeInput(sanitized.bio, { 
      maxLength: 500,
      allowHtml: false // Bio should not contain HTML
    });
  }
  
  if (sanitized.location) {
    sanitized.location = sanitizeInput(sanitized.location, { maxLength: 100 });
  }
  
  // Validate email format (basic check)
  if (sanitized.email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(sanitized.email)) {
      console.warn('⚠️ Invalid email format detected:', sanitized.email);
    }
  }
  
  return sanitized;
}

/**
 * Sanitize announcement data
 */
export function sanitizeAnnouncementData(announcementData) {
  const sanitized = { ...announcementData };
  
  if (sanitized.title) {
    sanitized.title = sanitizeInput(sanitized.title, { maxLength: 200 });
  }
  
  if (sanitized.content) {
    sanitized.content = sanitizeInput(sanitized.content, { 
      maxLength: 2000,
      allowHtml: isDevelopment // Allow HTML in development for testing
    });
  }
  
  return sanitized;
}

/**
 * Validate and sanitize form data
 */
export function sanitizeFormData(formData, fieldConfig = {}) {
  const sanitized = {};
  
  Object.keys(formData).forEach(key => {
    const value = formData[key];
    const config = fieldConfig[key] || {};
    
    if (typeof value === 'string') {
      sanitized[key] = sanitizeInput(value, {
        maxLength: config.maxLength || 1000,
        allowHtml: config.allowHtml || false,
        trimWhitespace: config.trimWhitespace !== false
      });
    } else {
      sanitized[key] = value;
    }
  });
  
  return sanitized;
}

/**
 * Rate limiting utilities
 */
class RateLimiter {
  constructor() {
    this.attempts = new Map();
    this.config = {
      development: {
        maxAttempts: 20, // Higher limit for development
        windowMs: 60 * 1000, // 1 minute
        blockDurationMs: 5 * 60 * 1000 // 5 minutes
      },
      production: {
        maxAttempts: 5, // Strict limit for production
        windowMs: 60 * 1000, // 1 minute
        blockDurationMs: 15 * 60 * 1000 // 15 minutes
      }
    };
  }
  
  getConfig() {
    return isDevelopment ? this.config.development : this.config.production;
  }
  
  isRateLimited(identifier) {
    const config = this.getConfig();
    const now = Date.now();
    const key = `${identifier}`;
    
    if (!this.attempts.has(key)) {
      this.attempts.set(key, { count: 0, firstAttempt: now, blockedUntil: null });
    }
    
    const record = this.attempts.get(key);
    
    // Check if currently blocked
    if (record.blockedUntil && now < record.blockedUntil) {
      return {
        isLimited: true,
        remainingTime: Math.ceil((record.blockedUntil - now) / 1000),
        reason: 'blocked'
      };
    }
    
    // Reset window if expired
    if (now - record.firstAttempt > config.windowMs) {
      record.count = 0;
      record.firstAttempt = now;
      record.blockedUntil = null;
    }
    
    // Check if limit exceeded
    if (record.count >= config.maxAttempts) {
      record.blockedUntil = now + config.blockDurationMs;
      return {
        isLimited: true,
        remainingTime: Math.ceil(config.blockDurationMs / 1000),
        reason: 'rate_limit_exceeded'
      };
    }
    
    return { isLimited: false };
  }
  
  recordAttempt(identifier) {
    const key = `${identifier}`;
    const record = this.attempts.get(key) || { count: 0, firstAttempt: Date.now(), blockedUntil: null };
    record.count++;
    this.attempts.set(key, record);
    
    const config = this.getConfig();
    return {
      attemptsRemaining: Math.max(0, config.maxAttempts - record.count),
      windowReset: record.firstAttempt + config.windowMs
    };
  }
  
  reset(identifier) {
    this.attempts.delete(`${identifier}`);
  }
}

// Global rate limiter instance
export const authRateLimiter = new RateLimiter();

/**
 * Security headers for development vs production
 */
export function getSecurityHeaders() {
  const baseHeaders = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin'
  };
  
  if (isProduction) {
    return {
      ...baseHeaders,
      'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co;",
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
    };
  } else {
    return {
      ...baseHeaders,
      'Content-Security-Policy': "default-src 'self' 'unsafe-inline' 'unsafe-eval'; connect-src 'self' http://localhost:* https://*.supabase.co;"
    };
  }
}

/**
 * Validate role changes (application-level security)
 */
export function validateRoleChange(currentUserRole, targetUserRole, newRole) {
  // Only SUPER_ADMIN can change roles
  if (currentUserRole !== 'SUPER_ADMIN') {
    throw new Error('Only SUPER_ADMIN can change user roles');
  }
  
  // Validate new role
  const validRoles = ['USER', 'MODERATOR', 'CONTENT_ADMIN', 'SUPER_ADMIN'];
  if (!validRoles.includes(newRole)) {
    throw new Error(`Invalid role: ${newRole}`);
  }
  
  // Log role changes for audit
  console.log('🔐 Role change validation:', {
    currentUserRole,
    targetUserRole,
    newRole,
    timestamp: new Date().toISOString()
  });
  
  return true;
}

/**
 * Security configuration export
 */
export const securityConfig = {
  isDevelopment,
  isProduction,
  xssConfig: getSecurityConfig(),
  rateLimitConfig: new RateLimiter().getConfig()
};

export default {
  sanitizeInput,
  sanitizeProfileData,
  sanitizeAnnouncementData,
  sanitizeFormData,
  authRateLimiter,
  getSecurityHeaders,
  validateRoleChange,
  securityConfig
};

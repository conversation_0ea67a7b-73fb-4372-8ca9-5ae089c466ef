# Test info

- Name: Festival Family Admin Dashboard Tests >> Emergency Management Test
- Location: C:\Users\<USER>\CascadeProjects\festival-family\tests\database-verification.spec.ts:258:3

# Error details

```
Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
╔═════════════════════════════════════════════════════════════════════════╗
║ Looks like Playwright Test or Playwright was just installed or updated. ║
║ Please run the following command to download new browsers:              ║
║                                                                         ║
║     npx playwright install                                              ║
║                                                                         ║
║ <3 Playwright Team                                                      ║
╚═════════════════════════════════════════════════════════════════════════╝
```

# Test source

```ts
  158 |       fullPage: true 
  159 |     });
  160 |     
  161 |     console.log('👤 Profile System Result:', result);
  162 |     
  163 |     // Verify profile system is accessible
  164 |     expect(result).toMatch(/(Profile|profile|fields|storage)/);
  165 |   });
  166 | });
  167 |
  168 | test.describe('Festival Family Admin Dashboard Tests', () => {
  169 |   test.beforeEach(async ({ page }) => {
  170 |     test.setTimeout(60000);
  171 |   });
  172 |
  173 |   test('Admin Dashboard Authentication', async ({ page }) => {
  174 |     await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
  175 |     await page.waitForLoadState('networkidle');
  176 |     
  177 |     // Click authentication test
  178 |     await page.click('button:has-text("Test Admin Authentication")');
  179 |     
  180 |     // Wait for results
  181 |     await page.waitForSelector('#auth-result', { timeout: 10000 });
  182 |     
  183 |     const result = await page.textContent('#auth-result');
  184 |     
  185 |     // Take screenshot
  186 |     await page.screenshot({ 
  187 |       path: 'test-results/admin-authentication.png',
  188 |       fullPage: true 
  189 |     });
  190 |     
  191 |     console.log('🔐 Admin Authentication Result:', result);
  192 |     
  193 |     // Check if user needs to sign in or is already authenticated
  194 |     if (result?.includes('No active session')) {
  195 |       console.log('⚠️ User needs to sign in for admin testing');
  196 |     } else {
  197 |       expect(result).toMatch(/(authenticated|admin)/i);
  198 |     }
  199 |   });
  200 |
  201 |   test('Admin Functions Status', async ({ page }) => {
  202 |     await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
  203 |     await page.waitForLoadState('networkidle');
  204 |     
  205 |     // Click admin functions test
  206 |     await page.click('button:has-text("Test All Admin Functions")');
  207 |     
  208 |     // Wait for results and grid
  209 |     await page.waitForSelector('#admin-functions-result', { timeout: 10000 });
  210 |     await page.waitForSelector('.status-item', { timeout: 5000 });
  211 |     
  212 |     // Get function status
  213 |     const statusItems = await page.$$('.status-item');
  214 |     const functionResults = [];
  215 |     
  216 |     for (const item of statusItems) {
  217 |       const text = await item.textContent();
  218 |       const className = await item.getAttribute('class');
  219 |       functionResults.push({
  220 |         text: text?.trim(),
  221 |         working: className?.includes('working') || false
  222 |       });
  223 |     }
  224 |     
  225 |     // Take screenshot
  226 |     await page.screenshot({ 
  227 |       path: 'test-results/admin-functions-status.png',
  228 |       fullPage: true 
  229 |     });
  230 |     
  231 |     console.log('⚙️ Admin Functions Status:');
  232 |     functionResults.forEach(func => {
  233 |       console.log(`${func.working ? '✅' : '❌'} ${func.text}`);
  234 |     });
  235 |   });
  236 |
  237 |   test('Content Management Test', async ({ page }) => {
  238 |     await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
  239 |     await page.waitForLoadState('networkidle');
  240 |     
  241 |     // Click content management test
  242 |     await page.click('button:has-text("Test Content CRUD")');
  243 |     
  244 |     // Wait for results
  245 |     await page.waitForSelector('#content-result', { timeout: 15000 });
  246 |     
  247 |     const result = await page.textContent('#content-result');
  248 |     
  249 |     // Take screenshot
  250 |     await page.screenshot({ 
  251 |       path: 'test-results/content-management.png',
  252 |       fullPage: true 
  253 |     });
  254 |     
  255 |     console.log('📝 Content Management Result:', result);
  256 |   });
  257 |
> 258 |   test('Emergency Management Test', async ({ page }) => {
      |   ^ Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
  259 |     await page.goto('file://' + process.cwd() + '/admin-dashboard-test.html');
  260 |     await page.waitForLoadState('networkidle');
  261 |     
  262 |     // Click emergency management test
  263 |     await page.click('button:has-text("Test Emergency CRUD")');
  264 |     
  265 |     // Wait for results
  266 |     await page.waitForSelector('#emergency-result', { timeout: 10000 });
  267 |     
  268 |     const result = await page.textContent('#emergency-result');
  269 |     
  270 |     // Take screenshot
  271 |     await page.screenshot({ 
  272 |       path: 'test-results/emergency-management.png',
  273 |       fullPage: true 
  274 |     });
  275 |     
  276 |     console.log('🚨 Emergency Management Result:', result);
  277 |   });
  278 |
  279 |   test('Application Accessibility Test', async ({ page }) => {
  280 |     // Test the main application
  281 |     await page.goto('http://localhost:5173');
  282 |     
  283 |     // Wait for app to load
  284 |     await page.waitForLoadState('networkidle');
  285 |     
  286 |     // Take screenshot of main app
  287 |     await page.screenshot({ 
  288 |       path: 'test-results/main-application.png',
  289 |       fullPage: true 
  290 |     });
  291 |     
  292 |     // Check if app loads without errors
  293 |     const title = await page.title();
  294 |     expect(title).toBeTruthy();
  295 |     
  296 |     console.log('🎪 Main Application Title:', title);
  297 |     
  298 |     // Try to navigate to admin dashboard
  299 |     try {
  300 |       await page.goto('http://localhost:5173/admin');
  301 |       await page.waitForLoadState('networkidle', { timeout: 5000 });
  302 |       
  303 |       // Take screenshot of admin dashboard
  304 |       await page.screenshot({ 
  305 |         path: 'test-results/admin-dashboard.png',
  306 |         fullPage: true 
  307 |       });
  308 |       
  309 |       console.log('✅ Admin dashboard accessible');
  310 |     } catch (error) {
  311 |       console.log('⚠️ Admin dashboard may require authentication:', error);
  312 |     }
  313 |   });
  314 | });
  315 |
```
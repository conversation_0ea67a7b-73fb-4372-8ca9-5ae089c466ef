/**
 * Admin-to-User Visibility Pipeline Test
 * 
 * Tests the complete data flow: Admin creates → Database stores → User pages display
 */

import { test, expect } from '@playwright/test';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

async function loginAsAdmin(page) {
  await page.goto('/auth');
  await page.waitForLoadState('networkidle');
  await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email);
  await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password);
  await page.click('button[type="submit"]');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  return !page.url().includes('/auth');
}

async function logoutAndBrowseAsUser(page) {
  // Logout from admin
  await page.goto('/');
  await page.waitForLoadState('networkidle');
  
  // Look for logout/sign out button
  const logoutButton = page.locator('button:has-text("Logout"), button:has-text("Sign Out"), a:has-text("Logout")');
  if (await logoutButton.count() > 0) {
    await logoutButton.first().click();
    await page.waitForTimeout(2000);
  }
  
  // Navigate to home page as unauthenticated user
  await page.goto('/');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(2000);
  
  return !page.url().includes('/admin');
}

test('Admin-to-User Visibility Pipeline Test', async ({ page }) => {
  console.log('🧪 Testing Admin-to-User visibility pipeline...');
  
  const testData = {
    activity: {
      title: 'Pipeline Test Activity ' + Date.now(),
      description: 'This activity tests the admin-to-user visibility pipeline.',
      location: 'Pipeline Test Location',
      type: 'workshop'
    }
  };
  
  // STEP 1: Login as admin and create test content
  console.log('👨‍💼 STEP 1: Creating content as admin...');
  
  const loginSuccess = await loginAsAdmin(page);
  console.log(`✅ Admin login success: ${loginSuccess}`);
  
  if (loginSuccess) {
    // Create test activity
    await page.goto('/admin/activities/new');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    console.log('📝 Creating test activity...');
    
    // Fill activity form
    await page.fill('input[name="title"]', testData.activity.title);
    await page.fill('textarea[name="description"]', testData.activity.description);
    await page.fill('input[name="location"]', testData.activity.location);
    
    // Set activity type
    const typeSelect = page.locator('select[name="type"]');
    if (await typeSelect.isVisible()) {
      await typeSelect.selectOption(testData.activity.type);
    }
    
    // Set dates
    const startDate = new Date();
    startDate.setDate(startDate.getDate() + 7);
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 8);
    
    await page.fill('input[name="start_date"]', startDate.toISOString().slice(0, 16));
    await page.fill('input[name="end_date"]', endDate.toISOString().slice(0, 16));
    
    // Set festival ID
    await page.fill('input[name="festival_id"]', '49239f09-0738-4e3a-9ea0-475f2f780e88');
    
    await page.screenshot({ path: 'test-results/admin-create-activity.png', fullPage: true });
    
    // Submit form
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    console.log('✅ Test activity created in admin');
    
    // Verify activity appears in admin list
    await page.goto('/admin/activities');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const activityInAdminList = await page.locator(`text="${testData.activity.title}"`).count() > 0;
    console.log(`✅ Activity visible in admin list: ${activityInAdminList}`);
    
    await page.screenshot({ path: 'test-results/admin-activity-list.png', fullPage: true });
  }
  
  // STEP 2: Browse as user and check visibility
  console.log('\n👤 STEP 2: Browsing as user...');
  
  const userBrowsingSuccess = await logoutAndBrowseAsUser(page);
  console.log(`✅ User browsing mode: ${userBrowsingSuccess}`);
  
  if (userBrowsingSuccess) {
    // Check Activities page
    console.log('🔍 Checking user Activities page...');
    
    await page.goto('/activities');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const activitiesPageLoaded = !page.url().includes('404') && !page.url().includes('error');
    const activityOnUserPage = await page.locator(`text="${testData.activity.title}"`).count() > 0;
    const hasActivityCards = await page.locator('.card, .activity-card, [data-testid*="activity"]').count() > 0;
    
    console.log(`Activities page assessment:`);
    console.log(`  Page loaded: ${activitiesPageLoaded}`);
    console.log(`  Test activity visible: ${activityOnUserPage}`);
    console.log(`  Has activity cards: ${hasActivityCards}`);
    
    await page.screenshot({ path: 'test-results/user-activities-page.png', fullPage: true });
    
    // Check Discover page
    console.log('🔍 Checking user Discover page...');
    
    await page.goto('/discover');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const discoverPageLoaded = !page.url().includes('404') && !page.url().includes('error');
    const activityOnDiscoverPage = await page.locator(`text="${testData.activity.title}"`).count() > 0;
    const hasDiscoverContent = await page.locator('.card, .discover-item, [data-testid*="discover"]').count() > 0;
    
    console.log(`Discover page assessment:`);
    console.log(`  Page loaded: ${discoverPageLoaded}`);
    console.log(`  Test activity visible: ${activityOnDiscoverPage}`);
    console.log(`  Has discover content: ${hasDiscoverContent}`);
    
    await page.screenshot({ path: 'test-results/user-discover-page.png', fullPage: true });
    
    // Check Home page
    console.log('🔍 Checking user Home page...');
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const homePageLoaded = !page.url().includes('404') && !page.url().includes('error');
    const activityOnHomePage = await page.locator(`text="${testData.activity.title}"`).count() > 0;
    const hasHomeContent = await page.locator('.card, .activity, .event').count() > 0;
    
    console.log(`Home page assessment:`);
    console.log(`  Page loaded: ${homePageLoaded}`);
    console.log(`  Test activity visible: ${activityOnHomePage}`);
    console.log(`  Has content: ${hasHomeContent}`);
    
    await page.screenshot({ path: 'test-results/user-home-page.png', fullPage: true });
  }
  
  // STEP 3: Assessment
  console.log('\n📊 PIPELINE ASSESSMENT:');
  console.log('========================');
  
  const adminCreationWorking = loginSuccess;
  const userPagesLoading = userBrowsingSuccess;
  const dataVisibilityWorking = false; // Will be determined by actual visibility
  
  console.log(`Admin creation: ${adminCreationWorking ? '✅ WORKING' : '❌ FAILED'}`);
  console.log(`User pages loading: ${userPagesLoading ? '✅ WORKING' : '❌ FAILED'}`);
  console.log(`Data visibility: ${dataVisibilityWorking ? '✅ WORKING' : '⚠️ NEEDS INVESTIGATION'}`);
  
  console.log('\n🎯 NEXT STEPS NEEDED:');
  console.log('- Investigate user-facing data loading');
  console.log('- Check database queries on user pages');
  console.log('- Verify component data binding');
  
  console.log('✅ Admin-to-User pipeline test completed');
});

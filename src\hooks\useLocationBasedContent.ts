/**
 * Location-based Content Hook
 *
 * React hook for accessing location-based content recommendations
 * with caching and real-time updates.
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  locationBasedContentService, 
  type LocationContext, 
  type LocationFilter, 
  type LocationBasedContent 
} from '@/lib/services/locationBasedContentService';
import { useOptimizedRealtime } from '@/hooks/realtime/useOptimizedRealtime';

interface UseLocationBasedContentOptions {
  enabled?: boolean;
  refetchOnWindowFocus?: boolean;
  cacheTime?: number;
}

interface UseLocationBasedContentResult {
  data: LocationBasedContent | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  locationSuggestions: string[];
  getLocationSuggestions: (query: string) => Promise<void>;
}

export function useLocationBasedContent(
  context: LocationContext,
  filter: LocationFilter = {},
  options: UseLocationBasedContentOptions = {}
): UseLocationBasedContentResult {
  const {
    enabled = true,
    refetchOnWindowFocus = false,
    cacheTime = 5 * 60 * 1000 // 5 minutes
  } = options;

  const [data, setData] = useState<LocationBasedContent | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [locationSuggestions, setLocationSuggestions] = useState<string[]>([]);

  // Memoize context and filter to prevent unnecessary refetches
  const memoizedContext = useMemo(() => context, [
    context.festivalLocation,
    context.eventLocation,
    context.userLocation,
    context.radius
  ]);

  const memoizedFilter = useMemo(() => filter, [
    filter.category,
    filter.priority,
    filter.featured,
    filter.active
  ]);

  // Real-time subscription for local info updates
  useOptimizedRealtime('local_info', ['location', 'content'], {
    event: '*',
    priority: 'medium',
    callback: (payload) => {
      console.log('🔄 Location Content: Real-time update received');
      if (enabled && data) {
        refetch();
      }
    }
  });

  // Real-time subscription for festivals updates
  useOptimizedRealtime('festivals', ['location', 'festivals'], {
    event: '*',
    priority: 'medium',
    callback: (payload) => {
      console.log('🔄 Location Content: Festival update received');
      if (enabled && data && (memoizedContext.festivalLocation || memoizedContext.userLocation)) {
        refetch();
      }
    }
  });

  // Real-time subscription for events updates
  useOptimizedRealtime('events', ['location', 'events'], {
    event: '*',
    priority: 'medium',
    callback: (payload) => {
      console.log('🔄 Location Content: Event update received');
      if (enabled && data && (memoizedContext.eventLocation || memoizedContext.userLocation)) {
        refetch();
      }
    }
  });

  // Fetch location-based content
  const fetchContent = useCallback(async () => {
    if (!enabled) return;

    try {
      setIsLoading(true);
      setError(null);

      console.log('📍 Location Hook: Fetching content for context:', memoizedContext);

      const result = await locationBasedContentService.getLocationBasedContent(
        memoizedContext,
        memoizedFilter
      );

      setData(result);
      console.log('✅ Location Hook: Content fetched successfully', {
        localInfo: result.localInfo.length,
        score: result.locationScore
      });
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch location-based content');
      setError(error);
      console.error('❌ Location Hook: Error fetching content:', error);
    } finally {
      setIsLoading(false);
    }
  }, [enabled, memoizedContext, memoizedFilter]);

  // Refetch function
  const refetch = useCallback(async () => {
    await fetchContent();
  }, [fetchContent]);

  // Get location suggestions
  const getLocationSuggestions = useCallback(async (query: string) => {
    try {
      console.log('📍 Location Hook: Fetching suggestions for:', query);
      const suggestions = await locationBasedContentService.getLocationSuggestions(query);
      setLocationSuggestions(suggestions);
      console.log('✅ Location Hook: Suggestions fetched:', suggestions.length);
    } catch (err) {
      console.error('❌ Location Hook: Error fetching suggestions:', err);
      setLocationSuggestions([]);
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    if (enabled) {
      fetchContent();
    }
  }, [fetchContent, enabled]);

  // Window focus refetch
  useEffect(() => {
    if (!refetchOnWindowFocus) return;

    const handleFocus = () => {
      if (enabled && data) {
        refetch();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchOnWindowFocus, enabled, data, refetch]);

  return {
    data,
    isLoading,
    error,
    refetch,
    locationSuggestions,
    getLocationSuggestions
  };
}

/**
 * Hook for getting location-based local information only
 */
export function useLocationBasedLocalInfo(
  context: LocationContext,
  filter: LocationFilter = {},
  options: UseLocationBasedContentOptions = {}
) {
  const { data, isLoading, error, refetch } = useLocationBasedContent(context, filter, options);

  return {
    localInfo: data?.localInfo || [],
    locationScore: data?.locationScore || 0,
    isLoading,
    error,
    refetch
  };
}

/**
 * Hook for getting location suggestions with debouncing
 */
export function useLocationSuggestions(debounceMs: number = 300) {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Debounced fetch
  useEffect(() => {
    if (!query.trim()) {
      setSuggestions([]);
      return;
    }

    const timeoutId = setTimeout(async () => {
      try {
        setIsLoading(true);
        const results = await locationBasedContentService.getLocationSuggestions(query);
        setSuggestions(results);
      } catch (error) {
        console.error('❌ Location Suggestions: Error:', error);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, debounceMs);

    return () => clearTimeout(timeoutId);
  }, [query, debounceMs]);

  return {
    query,
    setQuery,
    suggestions,
    isLoading
  };
}

/**
 * Hook for getting contextual recommendations based on current page/content
 */
export function useContextualLocationContent(
  contentType: 'festival' | 'event' | 'activity' | 'general',
  contentId?: string,
  options: UseLocationBasedContentOptions = {}
) {
  const [context, setContext] = useState<LocationContext>({});

  // Build context based on content type and ID
  useEffect(() => {
    const buildContext = async () => {
      if (!contentId) {
        setContext({});
        return;
      }

      try {
        let newContext: LocationContext = {};

        switch (contentType) {
          case 'festival':
            // Fetch festival location
            const festival = await locationBasedContentService.getRelevantFestivals({
              festivalLocation: contentId
            });
            if (festival && festival.length > 0) {
              newContext.festivalLocation = festival[0].location;
            }
            break;

          case 'event':
            // Fetch event location
            const event = await locationBasedContentService.getRelevantEvents({
              eventLocation: contentId
            });
            if (event && event.length > 0) {
              newContext.eventLocation = event[0].location;
            }
            break;

          case 'activity':
            // Fetch activity location
            const activity = await locationBasedContentService.getRelevantActivities({
              userLocation: contentId
            });
            if (activity && activity.length > 0) {
              newContext.userLocation = activity[0].location;
            }
            break;
        }

        setContext(newContext);
      } catch (error) {
        console.error('❌ Contextual Location: Error building context:', error);
        setContext({});
      }
    };

    buildContext();
  }, [contentType, contentId]);

  return useLocationBasedContent(context, {}, options);
}

export default useLocationBasedContent;

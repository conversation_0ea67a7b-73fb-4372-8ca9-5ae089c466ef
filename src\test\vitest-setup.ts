/**
 * Vitest Test Setup
 * 
 * Setup configuration specifically for Vitest testing of the
 * standardized Festival Family codebase.
 * 
 * @module VitestSetup
 * @version 1.0.0
 */

import { expect, afterEach, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';

// Extend Vitest's expect with jest-dom matchers
expect.extend(matchers);

// Cleanup after each test
afterEach(() => {
  cleanup();
});

// Mock environment variables
Object.defineProperty(window, 'process', {
  value: {
    env: {
      NODE_ENV: 'test',
      VITE_SUPABASE_URL: 'https://test.supabase.co',
      VITE_SUPABASE_ANON_KEY: 'test-key',
      VITE_ENVIRONMENT: 'test'
    }
  }
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock performance API
Object.defineProperty(window, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByType: vi.fn(() => []),
    getEntriesByName: vi.fn(() => []),
  }
});

// Mock Supabase client
vi.mock('@/lib/supabase/core-client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      neq: vi.fn().mockReturnThis(),
      gt: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      lt: vi.fn().mockReturnThis(),
      lte: vi.fn().mockReturnThis(),
      like: vi.fn().mockReturnThis(),
      ilike: vi.fn().mockReturnThis(),
      is: vi.fn().mockReturnThis(),
      in: vi.fn().mockReturnThis(),
      contains: vi.fn().mockReturnThis(),
      containedBy: vi.fn().mockReturnThis(),
      rangeGt: vi.fn().mockReturnThis(),
      rangeGte: vi.fn().mockReturnThis(),
      rangeLt: vi.fn().mockReturnThis(),
      rangeLte: vi.fn().mockReturnThis(),
      rangeAdjacent: vi.fn().mockReturnThis(),
      overlaps: vi.fn().mockReturnThis(),
      textSearch: vi.fn().mockReturnThis(),
      match: vi.fn().mockReturnThis(),
      not: vi.fn().mockReturnThis(),
      or: vi.fn().mockReturnThis(),
      filter: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      range: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null }),
      maybeSingle: vi.fn().mockResolvedValue({ data: null, error: null }),
      then: vi.fn().mockResolvedValue({ data: [], error: null }),
    })),
    auth: {
      getUser: vi.fn().mockResolvedValue({ 
        data: { user: { id: 'test-user-123' } }, 
        error: null 
      }),
      getSession: vi.fn().mockResolvedValue({ 
        data: { session: { user: { id: 'test-user-123' } } }, 
        error: null 
      }),
      signInWithPassword: vi.fn().mockResolvedValue({ 
        data: { user: { id: 'test-user-123' } }, 
        error: null 
      }),
      signOut: vi.fn().mockResolvedValue({ error: null }),
      onAuthStateChange: vi.fn(() => ({ data: { subscription: { unsubscribe: vi.fn() } } })),
    },
    channel: vi.fn(() => ({
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn().mockReturnThis(),
      unsubscribe: vi.fn().mockResolvedValue({ status: 'ok' }),
    })),
    removeChannel: vi.fn(),
  }
}));

// Mock unified data service
vi.mock('@/lib/data/unified-data-service', () => ({
  unifiedDataService: {
    getActivities: vi.fn().mockResolvedValue([]),
    getActivity: vi.fn().mockResolvedValue(null),
    createActivity: vi.fn().mockResolvedValue({ id: 'test-activity' }),
    updateActivity: vi.fn().mockResolvedValue({ id: 'test-activity' }),
    deleteActivity: vi.fn().mockResolvedValue(true),
    getFestivals: vi.fn().mockResolvedValue([]),
    getEvents: vi.fn().mockResolvedValue([]),
  }
}));

// Mock enhanced color mapping service
vi.mock('@/lib/services/enhancedColorMappingService', () => ({
  enhancedColorMappingService: {
    getColorsForContent: vi.fn().mockReturnValue({
      background: '#8b5cf6',
      foreground: '#ffffff',
      border: '#7c3aed'
    }),
    getBadgeProps: vi.fn().mockReturnValue({
      variant: 'default',
      className: 'bg-primary text-primary-foreground'
    }),
    getGradientForContent: vi.fn().mockReturnValue('from-purple-500 to-pink-500')
  }
}));

// Mock unified interaction service
vi.mock('@/lib/services/unifiedInteractionService', () => ({
  unifiedInteractionService: {
    handleInteraction: vi.fn().mockResolvedValue({ success: true }),
    getInteractionState: vi.fn().mockResolvedValue({
      isActive: false,
      count: 0,
      isLoading: false
    }),
    getInteractionConfig: vi.fn().mockReturnValue({
      icon: 'Heart',
      label: 'Favorite',
      activeLabel: 'Favorited',
      loadingLabel: 'Adding to favorites...',
      successMessage: 'Added to favorites',
      errorMessage: 'Failed to add to favorites'
    }),
    toggleFavorite: vi.fn().mockResolvedValue({ success: true }),
    setRSVP: vi.fn().mockResolvedValue({ success: true }),
    joinActivity: vi.fn().mockResolvedValue({ success: true }),
  }
}));

// Mock user interactions hook
vi.mock('@/hooks/useUserInteractions', () => ({
  useUserInteractions: vi.fn(() => ({
    participantCount: 5,
    isLoading: false,
    error: null,
    isFavorited: false,
    isJoined: false,
    rsvpStatus: 'pending',
    toggleFavorite: vi.fn(),
    toggleJoin: vi.fn(),
    setRSVP: vi.fn(),
  }))
}));

// Mock auth provider
vi.mock('@/providers/ConsolidatedAuthProvider', () => ({
  useAuth: vi.fn(() => ({
    user: { id: 'test-user-123', email: '<EMAIL>' },
    isAuthenticated: true,
    isLoading: false,
    signIn: vi.fn(),
    signOut: vi.fn(),
    signUp: vi.fn(),
  })),
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock performance monitor
vi.mock('@/lib/monitoring/performance-monitor', () => ({
  performanceMonitor: {
    monitorComponent: vi.fn(),
    monitorService: vi.fn(),
    monitorInteraction: vi.fn(),
    getMetrics: vi.fn(() => []),
    getComponentMetrics: vi.fn(() => new Map()),
    getServiceMetrics: vi.fn(() => new Map()),
  },
  usePerformanceMonitoring: vi.fn(() => ({
    monitorRender: vi.fn(),
    monitorMount: vi.fn(),
    monitorUpdate: vi.fn(),
    monitorError: vi.fn(),
  }))
}));

// Mock React Query
vi.mock('@tanstack/react-query', async () => {
  const actual = await vi.importActual('@tanstack/react-query');
  return {
    ...actual,
    useQuery: vi.fn(() => ({
      data: null,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })),
    useMutation: vi.fn(() => ({
      mutate: vi.fn(),
      mutateAsync: vi.fn(),
      isLoading: false,
      error: null,
      data: null,
    })),
    useQueryClient: vi.fn(() => ({
      invalidateQueries: vi.fn(),
      setQueryData: vi.fn(),
      getQueryData: vi.fn(),
    })),
  };
});

// Console suppression for cleaner test output
const originalError = console.error;
const originalWarn = console.warn;

beforeEach(() => {
  console.error = vi.fn();
  console.warn = vi.fn();
});

afterEach(() => {
  console.error = originalError;
  console.warn = originalWarn;
});

// Global test utilities
global.testUtils = {
  createMockActivity: (overrides = {}) => ({
    id: 'test-activity-123',
    title: 'Test Activity',
    description: 'Test activity description',
    type: 'meetup',
    status: 'published',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides
  }),
  
  createMockUser: (overrides = {}) => ({
    id: 'test-user-123',
    email: '<EMAIL>',
    name: 'Test User',
    ...overrides
  }),
  
  createMockEvent: (overrides = {}) => ({
    id: 'test-event-123',
    title: 'Test Event',
    description: 'Test event description',
    startDate: new Date().toISOString(),
    endDate: new Date().toISOString(),
    ...overrides
  })
};

// Type declarations for global test utilities
declare global {
  var testUtils: {
    createMockActivity: (overrides?: any) => any;
    createMockUser: (overrides?: any) => any;
    createMockEvent: (overrides?: any) => any;
  };
}

#!/usr/bin/env node

/**
 * Test Automation Watcher
 * 
 * Intelligent file change detection system that automatically triggers
 * appropriate tests based on file modifications, enhancing developer productivity.
 */

import chokidar from 'chokidar';
import { execSync } from 'child_process';
import path from 'path';
import fs from 'fs';
import debounce from 'lodash.debounce';

// Configuration
const WATCH_PATTERNS = [
  'src/**/*.{ts,tsx,js,jsx}',
  'src/**/*.test.{ts,tsx,js,jsx}',
  'tests/**/*.{ts,tsx,js,jsx}',
  'playwright.config.js',
  'jest.config.js',
  'package.json'
];

const IGNORE_PATTERNS = [
  '**/node_modules/**',
  '**/dist/**',
  '**/build/**',
  '**/.git/**',
  '**/coverage/**',
  '**/test-results/**'
];

// Test execution strategies
const TEST_STRATEGIES = {
  // Component changes trigger component and integration tests
  COMPONENT: {
    patterns: [/src\/components\/.*\.(ts|tsx)$/],
    tests: ['unit', 'integration'],
    priority: 'high'
  },
  
  // Hook changes trigger hook and component tests
  HOOK: {
    patterns: [/src\/hooks\/.*\.(ts|tsx)$/],
    tests: ['unit', 'integration'],
    priority: 'high'
  },
  
  // Page changes trigger page and E2E tests
  PAGE: {
    patterns: [/src\/pages\/.*\.(ts|tsx)$/],
    tests: ['unit', 'e2e'],
    priority: 'medium'
  },
  
  // Service/API changes trigger service and integration tests
  SERVICE: {
    patterns: [/src\/(api|lib|services)\/.*\.(ts|tsx)$/],
    tests: ['unit', 'integration'],
    priority: 'high'
  },
  
  // Type changes trigger type checking and related tests
  TYPE: {
    patterns: [/src\/types\/.*\.ts$/],
    tests: ['typecheck', 'unit'],
    priority: 'medium'
  },
  
  // Test file changes trigger only that specific test
  TEST: {
    patterns: [/.*\.test\.(ts|tsx|js|jsx)$/],
    tests: ['specific'],
    priority: 'immediate'
  },
  
  // Config changes trigger full test suite
  CONFIG: {
    patterns: [/playwright\.config\.js$/, /jest\.config\.js$/, /package\.json$/],
    tests: ['all'],
    priority: 'critical'
  },
  
  // Admin changes trigger admin-specific tests
  ADMIN: {
    patterns: [/src\/(components\/admin|pages\/admin)\/.*\.(ts|tsx)$/],
    tests: ['admin', 'integration'],
    priority: 'high'
  }
};

// State management
let isRunning = false;
let testQueue = new Set();
let lastRunTime = 0;
const MIN_RUN_INTERVAL = 2000; // Minimum 2 seconds between test runs

// Utility functions
function logInfo(message) {
  console.log(`🤖 [Test Automation] ${message}`);
}

function logSuccess(message) {
  console.log(`✅ [Test Automation] ${message}`);
}

function logError(message) {
  console.log(`❌ [Test Automation] ${message}`);
}

function logWarning(message) {
  console.log(`⚠️  [Test Automation] ${message}`);
}

function getFileType(filePath) {
  for (const [type, config] of Object.entries(TEST_STRATEGIES)) {
    if (config.patterns.some(pattern => pattern.test(filePath))) {
      return { type, config };
    }
  }
  return { type: 'UNKNOWN', config: { tests: ['unit'], priority: 'low' } };
}

function getRelatedTestFile(filePath) {
  // Convert source file to test file path
  const relativePath = path.relative('src', filePath);
  const testPath = path.join('src/__tests__', relativePath.replace(/\.(ts|tsx)$/, '.test.$1'));
  
  if (fs.existsSync(testPath)) {
    return testPath;
  }
  
  // Try alternative test locations
  const componentTestPath = filePath.replace(/\.(ts|tsx)$/, '.test.$1');
  if (fs.existsSync(componentTestPath)) {
    return componentTestPath;
  }
  
  return null;
}

function runCommand(command, description) {
  try {
    logInfo(`Running: ${description}`);
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: 'pipe',
      timeout: 60000 // 1 minute timeout
    });
    logSuccess(`${description} completed`);
    return { success: true, output };
  } catch (error) {
    logError(`${description} failed: ${error.message}`);
    return { success: false, output: error.stdout || error.message };
  }
}

function executeTests(testTypes, filePath) {
  const results = [];
  
  for (const testType of testTypes) {
    let command, description;
    
    switch (testType) {
      case 'unit':
        command = 'npm run test:unit -- --passWithNoTests';
        description = 'Unit tests';
        break;
        
      case 'integration':
        command = 'npm run test:unit -- --testPathPattern=integration --passWithNoTests';
        description = 'Integration tests';
        break;
        
      case 'e2e':
        command = 'npm run test:e2e:chromium';
        description = 'E2E tests (Chromium only)';
        break;
        
      case 'admin':
        command = 'npm run test:unit -- --testPathPattern=admin --passWithNoTests';
        description = 'Admin tests';
        break;
        
      case 'typecheck':
        command = 'npm run type-check';
        description = 'TypeScript type checking';
        break;
        
      case 'specific':
        const testFile = getRelatedTestFile(filePath);
        if (testFile) {
          command = `npm run test:unit -- ${testFile} --passWithNoTests`;
          description = `Specific test: ${path.basename(testFile)}`;
        } else {
          logWarning(`No test file found for ${filePath}`);
          continue;
        }
        break;
        
      case 'all':
        command = 'npm run test:quick';
        description = 'Quick test suite (unit + E2E Chromium)';
        break;
        
      default:
        logWarning(`Unknown test type: ${testType}`);
        continue;
    }
    
    const result = runCommand(command, description);
    results.push({ testType, ...result });
  }
  
  return results;
}

function generateTestReport(filePath, fileType, results) {
  const timestamp = new Date().toISOString();
  const report = {
    timestamp,
    trigger: {
      file: filePath,
      type: fileType.type,
      priority: fileType.config.priority
    },
    results: results.map(r => ({
      testType: r.testType,
      success: r.success,
      duration: 'N/A' // Could be enhanced to track duration
    })),
    summary: {
      total: results.length,
      passed: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      successRate: Math.round((results.filter(r => r.success).length / results.length) * 100)
    }
  };
  
  // Save report
  const reportDir = 'test-automation-reports';
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  const reportFile = path.join(reportDir, `report-${Date.now()}.json`);
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  
  // Log summary
  logInfo(`Test Report Summary:`);
  logInfo(`  File: ${path.basename(filePath)}`);
  logInfo(`  Type: ${fileType.type}`);
  logInfo(`  Tests: ${report.summary.passed}/${report.summary.total} passed (${report.summary.successRate}%)`);
  
  if (report.summary.failed > 0) {
    logError(`${report.summary.failed} test(s) failed - check individual test outputs`);
  }
  
  return report;
}

// Debounced test execution to prevent excessive runs
const debouncedTestExecution = debounce(async (filePath) => {
  if (isRunning) {
    logWarning('Tests already running, queuing request...');
    testQueue.add(filePath);
    return;
  }
  
  const now = Date.now();
  if (now - lastRunTime < MIN_RUN_INTERVAL) {
    logInfo('Rate limiting test execution...');
    setTimeout(() => debouncedTestExecution(filePath), MIN_RUN_INTERVAL);
    return;
  }
  
  isRunning = true;
  lastRunTime = now;
  
  try {
    logInfo(`File changed: ${filePath}`);
    
    const fileType = getFileType(filePath);
    logInfo(`Detected file type: ${fileType.type} (Priority: ${fileType.config.priority})`);
    
    const results = executeTests(fileType.config.tests, filePath);
    const report = generateTestReport(filePath, fileType, results);
    
    // Process queued files if any
    if (testQueue.size > 0) {
      const nextFile = testQueue.values().next().value;
      testQueue.delete(nextFile);
      setTimeout(() => debouncedTestExecution(nextFile), 1000);
    }
    
  } catch (error) {
    logError(`Test execution failed: ${error.message}`);
  } finally {
    isRunning = false;
  }
}, 1000);

// File watcher setup
function startWatcher() {
  logInfo('Starting Test Automation Watcher...');
  logInfo(`Watching patterns: ${WATCH_PATTERNS.join(', ')}`);
  
  const watcher = chokidar.watch(WATCH_PATTERNS, {
    ignored: IGNORE_PATTERNS,
    ignoreInitial: true,
    persistent: true,
    awaitWriteFinish: {
      stabilityThreshold: 500,
      pollInterval: 100
    }
  });
  
  watcher.on('change', (filePath) => {
    debouncedTestExecution(filePath);
  });
  
  watcher.on('add', (filePath) => {
    if (filePath.includes('.test.')) {
      logInfo(`New test file detected: ${filePath}`);
      debouncedTestExecution(filePath);
    }
  });
  
  watcher.on('error', (error) => {
    logError(`Watcher error: ${error.message}`);
  });
  
  watcher.on('ready', () => {
    logSuccess('Test Automation Watcher is ready and monitoring file changes');
    logInfo('Press Ctrl+C to stop the watcher');
  });
  
  // Graceful shutdown
  process.on('SIGINT', () => {
    logInfo('Shutting down Test Automation Watcher...');
    watcher.close();
    process.exit(0);
  });
  
  return watcher;
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
  startWatcher();
}

export { startWatcher, executeTests, generateTestReport };

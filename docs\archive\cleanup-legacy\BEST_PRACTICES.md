# Festival Family Best Practices Guide

This guide outlines modern React and TypeScript best practices tailored specifically for the Festival Family application. Following these guidelines will help make the app more stable, maintainable, and performant.

## Table of Contents

1. [Code Organization](#code-organization)
2. [Component Design](#component-design)
3. [State Management](#state-management)
4. [TypeScript Best Practices](#typescript-best-practices)
5. [Performance Optimization](#performance-optimization)
6. [Error Handling](#error-handling)
7. [Testing Strategy](#testing-strategy)
8. [Styling Approach](#styling-approach)

## Code Organization

### Project Structure

Adopt a feature-based folder structure that organizes code by domain rather than by technical type:

```
src/
├── assets/                # Static assets like images, fonts
├── components/            # Shared UI components
│   ├── ui/                # Basic UI elements (buttons, inputs, etc.)
│   └── common/            # Composite components used across features
├── features/              # Feature-specific modules
│   ├── auth/              # Authentication feature
│   │   ├── components/    # Components specific to auth
│   │   ├── hooks/         # Auth-related hooks
│   │   ├── utils/         # Auth-specific utilities
│   │   ├── types.ts       # Auth-related types
│   │   └── index.ts       # Public API for the feature
│   ├── festivals/         # Festivals feature
│   └── profile/           # User profile feature
├── hooks/                 # Shared hooks
├── lib/                   # External library integrations (Supabase, etc.)
├── utils/                 # Shared utility functions
├── types/                 # Shared TypeScript types
├── contexts/              # Global context providers
├── pages/                 # Route components
└── App.tsx                # Root component
```

### File Naming Conventions

- Use **PascalCase** for React components: `ProfileCard.tsx`
- Use **camelCase** for utilities, hooks, and non-component files: `useAuth.ts`, `dateUtils.ts`
- Use **kebab-case** for assets: `profile-avatar.png`
- Use `.tsx` extension for files with JSX
- Use `.ts` extension for pure TypeScript files
- Use `index.ts` files to export public APIs from directories

### File Size Limits

- Keep all files under 500 lines
- If a file exceeds 300 lines, consider splitting it into smaller, focused files
- Use composition to break down large components into smaller ones

## Component Design

### Component Hierarchy

Organize components into a clear hierarchy:

1. **UI Components**: Basic, reusable UI elements (buttons, inputs, cards)
2. **Feature Components**: Components specific to a feature
3. **Page Components**: Top-level components that compose feature components

### Component Composition

Use composition over inheritance:

```tsx
// Instead of one large component:
function ProfilePage() {
  return (
    <div>
      <ProfileHeader user={user} />
      <ProfileDetails user={user} />
      <UserFestivals festivals={festivals} />
      <UserActivities activities={activities} />
    </div>
  );
}
```

### Separation of Concerns

Separate UI rendering from data fetching and business logic:

```tsx
// Data container component
function ProfilePageContainer() {
  const { data: user } = useQuery(['user'], fetchUser);
  const { data: festivals } = useQuery(['festivals'], fetchUserFestivals);
  
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  
  return <ProfilePage user={user} festivals={festivals} />;
}

// Presentational component
function ProfilePage({ user, festivals }) {
  return (
    <div>
      <ProfileHeader user={user} />
      <ProfileDetails user={user} />
      <UserFestivals festivals={festivals} />
    </div>
  );
}
```

### Custom Hooks for Logic Reuse

Extract complex logic into custom hooks:

```tsx
// Custom hook for festival filtering and sorting
function useFestivalFiltering(festivals, filterOptions) {
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [searchQuery, setSearchQuery] = useState('');
  
  const filteredFestivals = useMemo(() => {
    return festivals
      .filter(/* filtering logic */)
      .sort(/* sorting logic */);
  }, [festivals, filter, sortBy, searchQuery]);
  
  return {
    filteredFestivals,
    filter,
    setFilter,
    sortBy,
    setSortBy,
    searchQuery,
    setSearchQuery
  };
}
```

## State Management

### State Management Decision Tree

Use this decision tree to determine the appropriate state management approach:

1. **Component State** (useState): For local UI state that doesn't need to be shared
2. **Context API**: For state that needs to be shared across multiple components in a subtree
3. **React Query**: For server state (data fetching, caching, synchronization)
4. **Zustand**: For global application state that needs to be accessed across the entire app

### React Query for Server State

Use React Query for all data fetching operations:

```tsx
// Define query hooks in a central location
export function useFestival(festivalId) {
  return useQuery(
    ['festival', festivalId],
    () => supabase.from('festivals').select('*').eq('id', festivalId).single(),
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
      enabled: !!festivalId,
    }
  );
}

// Use in components
function FestivalDetails({ festivalId }) {
  const { data, isLoading, error } = useFestival(festivalId);
  
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  
  return <div>{/* Render festival details */}</div>;
}
```

### Context for Shared State

Use Context API for state that needs to be shared across components:

```tsx
// Create a context with a default value
const ThemeContext = createContext({
  theme: 'light',
  toggleTheme: () => {},
});

// Create a provider component
export function ThemeProvider({ children }) {
  const [theme, setTheme] = useState('light');
  
  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light');
  };
  
  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

// Create a custom hook for using the context
export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
```

### Zustand for Global State

Use Zustand for global application state:

```tsx
// Create a store
import create from 'zustand';

interface AppState {
  isNavOpen: boolean;
  toggleNav: () => void;
}

export const useAppStore = create<AppState>((set) => ({
  isNavOpen: false,
  toggleNav: () => set((state) => ({ isNavOpen: !state.isNavOpen })),
}));

// Use in components
function Navigation() {
  const { isNavOpen, toggleNav } = useAppStore();
  
  return (
    <>
      <button onClick={toggleNav}>Toggle Nav</button>
      {isNavOpen && <NavMenu />}
    </>
  );
}
```

## TypeScript Best Practices

### Type Definitions

Define types in dedicated files:

```tsx
// types/festival.ts
export interface Festival {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  location: string;
  description: string;
  imageUrl?: string;
}

// Use in components
import { Festival } from '@/types/festival';

function FestivalCard({ festival }: { festival: Festival }) {
  return <div>{/* Render festival */}</div>;
}
```

### Prop Types

Use TypeScript interfaces for component props:

```tsx
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'tertiary';
  size?: 'small' | 'medium' | 'large';
  isFullWidth?: boolean;
  isDisabled?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
}

export function Button({
  variant = 'primary',
  size = 'medium',
  isFullWidth = false,
  isDisabled = false,
  onClick,
  children,
}: ButtonProps) {
  // Component implementation
}
```

### Type Guards

Use type guards to handle conditional rendering:

```tsx
interface SuccessResponse {
  status: 'success';
  data: any;
}

interface ErrorResponse {
  status: 'error';
  error: string;
}

type ApiResponse = SuccessResponse | ErrorResponse;

function isSuccessResponse(response: ApiResponse): response is SuccessResponse {
  return response.status === 'success';
}

function handleResponse(response: ApiResponse) {
  if (isSuccessResponse(response)) {
    // TypeScript knows response has data property
    return response.data;
  } else {
    // TypeScript knows response has error property
    throw new Error(response.error);
  }
}
```

## Performance Optimization

### Memoization

Use `useMemo` and `useCallback` for expensive calculations and callback functions:

```tsx
// Memoize expensive calculations
const sortedFestivals = useMemo(() => {
  return [...festivals].sort((a, b) => 
    new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
  );
}, [festivals]);

// Memoize callback functions
const handleFestivalSelect = useCallback((festivalId: string) => {
  navigate(`/festivals/${festivalId}`);
}, [navigate]);
```

### React.memo for Component Memoization

Use `React.memo` for components that render often but with the same props:

```tsx
const FestivalCard = React.memo(function FestivalCard({ festival }: { festival: Festival }) {
  return <div>{/* Render festival */}</div>;
});
```

### Code Splitting

Use dynamic imports to split code by route:

```tsx
// In your router configuration
const ProfilePage = lazy(() => import('./pages/Profile'));
const FestivalPage = lazy(() => import('./pages/Festival'));

// Wrap with Suspense
<Suspense fallback={<LoadingSpinner />}>
  <Routes>
    <Route path="/profile" element={<ProfilePage />} />
    <Route path="/festivals/:id" element={<FestivalPage />} />
  </Routes>
</Suspense>
```

### Virtualization for Long Lists

Use virtualization for long lists:

```tsx
import { useVirtualizer } from '@tanstack/react-virtual';

function FestivalList({ festivals }) {
  const parentRef = useRef(null);
  
  const rowVirtualizer = useVirtualizer({
    count: festivals.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 100,
  });
  
  return (
    <div ref={parentRef} style={{ height: '500px', overflow: 'auto' }}>
      <div
        style={{
          height: `${rowVirtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {rowVirtualizer.getVirtualItems().map((virtualRow) => (
          <div
            key={virtualRow.index}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: `${virtualRow.size}px`,
              transform: `translateY(${virtualRow.start}px)`,
            }}
          >
            <FestivalCard festival={festivals[virtualRow.index]} />
          </div>
        ))}
      </div>
    </div>
  );
}
```

## Error Handling

### Error Boundaries

Use error boundaries to catch and handle errors:

```tsx
import { ErrorBoundary } from 'react-error-boundary';

function ErrorFallback({ error, resetErrorBoundary }) {
  return (
    <div role="alert">
      <p>Something went wrong:</p>
      <pre>{error.message}</pre>
      <button onClick={resetErrorBoundary}>Try again</button>
    </div>
  );
}

function App() {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onReset={() => {
        // Reset the state of your app here
      }}
    >
      <YourApp />
    </ErrorBoundary>
  );
}
```

### Async Error Handling

Use try/catch blocks for async operations:

```tsx
async function handleSubmit() {
  try {
    setIsLoading(true);
    await submitForm(formData);
    toast.success('Form submitted successfully');
    navigate('/success');
  } catch (error) {
    console.error('Form submission error:', error);
    toast.error(error.message || 'An error occurred');
  } finally {
    setIsLoading(false);
  }
}
```

## Testing Strategy

### Component Testing

Test components using React Testing Library:

```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

describe('Button', () => {
  test('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });
  
  test('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### Hook Testing

Test custom hooks using React Hooks Testing Library:

```tsx
import { renderHook, act } from '@testing-library/react-hooks';
import { useCounter } from './useCounter';

describe('useCounter', () => {
  test('should increment counter', () => {
    const { result } = renderHook(() => useCounter());
    
    act(() => {
      result.current.increment();
    });
    
    expect(result.current.count).toBe(1);
  });
});
```

## Styling Approach

### Tailwind CSS

Use Tailwind CSS for consistent styling:

```tsx
function Button({ variant = 'primary', size = 'medium', children }) {
  const baseClasses = 'font-medium rounded focus:outline-none focus:ring-2';
  
  const variantClasses = {
    primary: 'bg-electric-violet hover:bg-electric-violet-dark text-white',
    secondary: 'bg-white border border-electric-violet text-electric-violet',
    tertiary: 'bg-transparent text-electric-violet hover:underline',
  };
  
  const sizeClasses = {
    small: 'py-1 px-3 text-sm',
    medium: 'py-2 px-4 text-base',
    large: 'py-3 px-6 text-lg',
  };
  
  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]}`;
  
  return <button className={classes}>{children}</button>;
}
```

### Component Variants with cva

Use class-variance-authority (cva) for component variants:

```tsx
import { cva } from 'class-variance-authority';

const buttonVariants = cva(
  'font-medium rounded focus:outline-none focus:ring-2',
  {
    variants: {
      variant: {
        primary: 'bg-electric-violet hover:bg-electric-violet-dark text-white',
        secondary: 'bg-white border border-electric-violet text-electric-violet',
        tertiary: 'bg-transparent text-electric-violet hover:underline',
      },
      size: {
        small: 'py-1 px-3 text-sm',
        medium: 'py-2 px-4 text-base',
        large: 'py-3 px-6 text-lg',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'medium',
    },
  }
);

function Button({
  variant,
  size,
  className,
  children,
  ...props
}) {
  return (
    <button
      className={buttonVariants({ variant, size, className })}
      {...props}
    >
      {children}
    </button>
  );
}
```

---

By following these best practices, the Festival Family app will become more maintainable, performant, and easier to develop. These guidelines address the specific issues identified in the codebase while providing a clear path forward for future development.

-- WORKING Privilege Escalation Fix
-- This migration implements secure RLS policies WITHOUT using NEW/OLD in policies
-- NEW/OLD only work in triggers, NOT in RLS policies!

-- First, ensure RLS is enabled on profiles table
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies that may allow privilege escalation
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON profiles;
DROP POLICY IF EXISTS "Enable update for users based on email" ON profiles;

-- Create secure, granular RLS policies (WITHOUT NEW/OLD references)

-- 1. Profile Reading Policies
-- Allow users to read their own profile
CREATE POLICY "Users can read own profile" ON profiles
FOR SELECT USING (auth.uid() = id);

-- Allow admins to read all profiles (for admin functionality)
CREATE POLICY "Admins can read all profiles" ON profiles
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND role IN ('SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR')
  )
);

-- 2. Profile Creation Policies
-- Allow authenticated users to create their own profile
CREATE POLICY "Users can create own profile" ON profiles
FOR INSERT WITH CHECK (auth.uid() = id);

-- 3. Profile Update Policies (SIMPLIFIED - NO NEW/OLD)
-- Users can update their own profile (role changes will be handled by triggers)
CREATE POLICY "Users can update own profile" ON profiles
FOR UPDATE USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- 4. Admin Profile Management
-- Allow admins to update user profiles for admin functionality
CREATE POLICY "Admins can update user profiles" ON profiles
FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND role IN ('SUPER_ADMIN', 'CONTENT_ADMIN')
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND role IN ('SUPER_ADMIN', 'CONTENT_ADMIN')
  )
);

-- 5. Profile Deletion Policies
-- Only SUPER_ADMIN can delete profiles
CREATE POLICY "Only super admins can delete profiles" ON profiles
FOR DELETE USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND role = 'SUPER_ADMIN'
  )
);

-- Create a trigger function to prevent unauthorized role changes
CREATE OR REPLACE FUNCTION prevent_role_escalation()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
  current_user_role TEXT;
BEGIN
  -- Get the current user's role
  SELECT role INTO current_user_role
  FROM profiles
  WHERE id = auth.uid();
  
  -- If this is an INSERT, allow it (role will be set to default)
  IF TG_OP = 'INSERT' THEN
    -- Set default role if not specified
    IF NEW.role IS NULL THEN
      NEW.role := 'USER';
    END IF;
    RETURN NEW;
  END IF;
  
  -- If this is an UPDATE and role is being changed
  IF TG_OP = 'UPDATE' AND NEW.role IS DISTINCT FROM OLD.role THEN
    -- Only SUPER_ADMIN can change roles
    IF current_user_role != 'SUPER_ADMIN' THEN
      RAISE EXCEPTION 'Only SUPER_ADMIN can change user roles. Current user role: %', current_user_role;
    END IF;
    
    -- Validate the new role
    IF NEW.role NOT IN ('USER', 'MODERATOR', 'CONTENT_ADMIN', 'SUPER_ADMIN') THEN
      RAISE EXCEPTION 'Invalid role: %', NEW.role;
    END IF;
    
    RAISE NOTICE 'Role changed from % to % by SUPER_ADMIN %', OLD.role, NEW.role, auth.uid();
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create the trigger to prevent role escalation
DROP TRIGGER IF EXISTS prevent_role_escalation_trigger ON profiles;
CREATE TRIGGER prevent_role_escalation_trigger
  BEFORE INSERT OR UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION prevent_role_escalation();

-- Create a function to safely change user roles (for development and admin use)
CREATE OR REPLACE FUNCTION change_user_role(
  target_user_id UUID,
  new_role TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_role TEXT;
BEGIN
  -- Get the current user's role
  SELECT role INTO current_user_role
  FROM profiles
  WHERE id = auth.uid();
  
  -- Only SUPER_ADMIN can change roles
  IF current_user_role != 'SUPER_ADMIN' THEN
    RAISE EXCEPTION 'Only SUPER_ADMIN can change user roles';
  END IF;
  
  -- Validate the new role
  IF new_role NOT IN ('USER', 'MODERATOR', 'CONTENT_ADMIN', 'SUPER_ADMIN') THEN
    RAISE EXCEPTION 'Invalid role: %', new_role;
  END IF;
  
  -- Update the role
  UPDATE profiles
  SET role = new_role, updated_at = NOW()
  WHERE id = target_user_id;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;

-- Create audit log table for tracking role changes (if it doesn't exist)
CREATE TABLE IF NOT EXISTS audit_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  action TEXT NOT NULL,
  table_name TEXT NOT NULL,
  record_id UUID,
  old_values JSONB,
  new_values JSONB,
  changed_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on audit_log
ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;

-- Only admins can read audit logs
CREATE POLICY "Admins can read audit logs" ON audit_log
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND role IN ('SUPER_ADMIN', 'CONTENT_ADMIN')
  )
);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON profiles TO authenticated;
GRANT ALL ON audit_log TO authenticated;
GRANT EXECUTE ON FUNCTION change_user_role TO authenticated;

-- Add helpful comments
COMMENT ON FUNCTION prevent_role_escalation IS 
'Trigger function that prevents unauthorized role changes - only SUPER_ADMIN can change roles';

COMMENT ON FUNCTION change_user_role IS 
'Safe function for changing user roles - only SUPER_ADMIN can use this';

-- Verify the existing admin account still has SUPER_ADMIN role
DO $$
DECLARE
  admin_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO admin_count
  FROM profiles
  WHERE email = '<EMAIL>' AND role = 'SUPER_ADMIN';
  
  IF admin_count = 0 THEN
    RAISE NOTICE 'WARNING: <EMAIL> does not have SUPER_ADMIN role!';
  ELSE
    RAISE NOTICE 'SUCCESS: <EMAIL> has SUPER_ADMIN role preserved';
  END IF;
END $$;

-- Log successful migration
DO $$
BEGIN
  RAISE NOTICE 'SUCCESS: Privilege escalation prevention implemented successfully';
  RAISE NOTICE 'INFO: RLS policies created WITHOUT NEW/OLD references';
  RAISE NOTICE 'INFO: Role change prevention handled by trigger function';
  RAISE NOTICE 'INFO: Admin functionality <NAME_EMAIL>';
  RAISE NOTICE 'INFO: Use change_user_role() function for safe role changes';
END $$;

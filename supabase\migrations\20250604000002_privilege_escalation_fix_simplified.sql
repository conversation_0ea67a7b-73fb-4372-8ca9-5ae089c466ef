-- Simplified Privilege Escalation Fix
-- This migration implements secure RLS policies to prevent unauthorized role changes
-- Simplified version to avoid complex trigger issues

-- First, ensure RLS is enabled on profiles table
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies that may allow privilege escalation
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON profiles;
DROP POLICY IF EXISTS "Enable update for users based on email" ON profiles;

-- Create secure, granular RLS policies

-- 1. Profile Reading Policies
-- Allow users to read their own profile
CREATE POLICY "Users can read own profile" ON profiles
FOR SELECT USING (auth.uid() = id);

-- Allow admins to read all profiles (for admin functionality)
CREATE POLICY "Admins can read all profiles" ON profiles
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND role IN ('SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR')
  )
);

-- 2. Profile Creation Policies
-- Allow authenticated users to create their own profile
CREATE POLICY "Users can create own profile" ON profiles
FOR INSERT WITH CHECK (auth.uid() = id);

-- 3. Profile Update Policies (Non-Role Fields)
-- Users can update their own profile but NOT the role field
CREATE POLICY "Users can update own profile non-role fields" ON profiles
FOR UPDATE USING (auth.uid() = id)
WITH CHECK (
  auth.uid() = id 
  AND (
    -- Ensure role field is not being changed or is being set to the same value
    NEW.role IS NULL 
    OR NEW.role = OLD.role 
    OR OLD.role IS NULL
  )
);

-- 4. Role Management Policies (CRITICAL SECURITY)
-- Only SUPER_ADMIN can change user roles
CREATE POLICY "Only super admins can manage user roles" ON profiles
FOR UPDATE USING (
  -- Only SUPER_ADMIN can initiate role changes
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND role = 'SUPER_ADMIN'
  )
  -- And only when actually changing roles
  AND NEW.role IS DISTINCT FROM OLD.role
)
WITH CHECK (
  -- Verify the user making the change is still SUPER_ADMIN
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND role = 'SUPER_ADMIN'
  )
);

-- 5. Admin Profile Management
-- Allow admins to update other users' non-role fields for admin functionality
CREATE POLICY "Admins can update user profiles non-role fields" ON profiles
FOR UPDATE USING (
  -- Admin is making the change
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND role IN ('SUPER_ADMIN', 'CONTENT_ADMIN')
  )
  -- And not changing roles (roles handled by separate policy)
  AND (NEW.role IS NULL OR NEW.role = OLD.role)
)
WITH CHECK (
  -- Verify admin status and no role changes
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND role IN ('SUPER_ADMIN', 'CONTENT_ADMIN')
  )
  AND (NEW.role IS NULL OR NEW.role = OLD.role)
);

-- 6. Profile Deletion Policies
-- Only SUPER_ADMIN can delete profiles
CREATE POLICY "Only super admins can delete profiles" ON profiles
FOR DELETE USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND role = 'SUPER_ADMIN'
  )
);

-- Create a function to safely change user roles (for development and admin use)
CREATE OR REPLACE FUNCTION change_user_role(
  target_user_id UUID,
  new_role TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_role TEXT;
BEGIN
  -- Get the current user's role
  SELECT role INTO current_user_role
  FROM profiles
  WHERE id = auth.uid();
  
  -- Only SUPER_ADMIN can change roles
  IF current_user_role != 'SUPER_ADMIN' THEN
    RAISE EXCEPTION 'Only SUPER_ADMIN can change user roles';
  END IF;
  
  -- Validate the new role
  IF new_role NOT IN ('USER', 'MODERATOR', 'CONTENT_ADMIN', 'SUPER_ADMIN') THEN
    RAISE EXCEPTION 'Invalid role: %', new_role;
  END IF;
  
  -- Update the role
  UPDATE profiles
  SET role = new_role, updated_at = NOW()
  WHERE id = target_user_id;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;

-- Create audit log table for tracking role changes (if it doesn't exist)
CREATE TABLE IF NOT EXISTS audit_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  action TEXT NOT NULL,
  table_name TEXT NOT NULL,
  record_id UUID,
  old_values JSONB,
  new_values JSONB,
  changed_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on audit_log
ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;

-- Only admins can read audit logs
CREATE POLICY "Admins can read audit logs" ON audit_log
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND role IN ('SUPER_ADMIN', 'CONTENT_ADMIN')
  )
);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON profiles TO authenticated;
GRANT ALL ON audit_log TO authenticated;
GRANT EXECUTE ON FUNCTION change_user_role TO authenticated;

-- Add helpful comments
COMMENT ON POLICY "Users can read own profile" ON profiles IS 
'Allows users to read their own profile data';

COMMENT ON POLICY "Admins can read all profiles" ON profiles IS 
'Allows admins to read all profiles for admin functionality';

COMMENT ON POLICY "Users can update own profile non-role fields" ON profiles IS 
'Allows users to update their own profile but prevents role changes';

COMMENT ON POLICY "Only super admins can manage user roles" ON profiles IS 
'SECURITY CRITICAL: Only SUPER_ADMIN can change user roles - prevents privilege escalation';

COMMENT ON FUNCTION change_user_role IS 
'Safe function for changing user roles - only SUPER_ADMIN can use this. Use: SELECT change_user_role(user_id, new_role)';

-- Verify the existing admin account still has SUPER_ADMIN role
DO $$
DECLARE
  admin_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO admin_count
  FROM profiles
  WHERE email = '<EMAIL>' AND role = 'SUPER_ADMIN';
  
  IF admin_count = 0 THEN
    RAISE NOTICE 'WARNING: <EMAIL> does not have SUPER_ADMIN role!';
  ELSE
    RAISE NOTICE 'SUCCESS: <EMAIL> has SUPER_ADMIN role preserved';
  END IF;
END $$;

-- Log successful migration
DO $$
BEGIN
  RAISE NOTICE 'SUCCESS: Privilege escalation prevention implemented successfully';
  RAISE NOTICE 'INFO: Secure RLS policies created for profiles table';
  RAISE NOTICE 'INFO: Admin functionality <NAME_EMAIL>';
  RAISE NOTICE 'INFO: Use change_user_role() function for safe role changes';
END $$;

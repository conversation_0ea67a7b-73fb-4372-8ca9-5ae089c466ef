import { supabase } from '@/lib/supabase';

interface FamActivityData {
  month: string;
  dateFromTill: string;
  timeFromTill: string;
  where: string;
  what: string;
  plansAreFinal: string;
  payNeeded: string;
  needToSignUp: string;
  signUpDeadline: string;
  contact: string;
}

interface FestivalData {
  "Festival Family Info": any;
  "Fam Activities": {
    raw: any[][];
    data: any[];
  };
}

class FestivalDataMigrationService {
  /**
   * Parse the festival family data JSON and extract activities
   */
  private parseActivitiesData(festivalData: FestivalData): FamActivityData[] {
    const activities: FamActivityData[] = [];
    
    if (!festivalData["Fam Activities"]?.data) {
      console.warn('No Fam Activities data found');
      return activities;
    }

    const data = festivalData["Fam Activities"].data;
    
    for (const item of data) {
      // Skip header rows and empty entries
      if (!item.Month ||
          !item['What?'] ||
          item['What?'] === '' ||
          item.Month.includes('Festival Family Activities') ||
          item.Month.includes('overview') ||
          item.Month.includes('Your name') ||
          item.Month.includes('Fam Activity/Event Organisers')) {
        continue;
      }

      const activity: FamActivityData = {
        month: item.Month || '',
        dateFromTill: item['Date from-till'] || '',
        timeFromTill: item['Time from-till'] || '',
        where: item['Where?'] || '',
        what: item['What?'] || '',
        plansAreFinal: item['Plans are final?'] || 'FALSE',
        payNeeded: item['Pay needed?'] || 'FALSE',
        needToSignUp: item['Need to sign-up?'] || 'FALSE',
        signUpDeadline: item['Sign-up deadline? (yyyy-mm-dd)'] || item['Sign-up deadline? (dd-mm-yyyy)'] || '',
        contact: item['Contact'] || ''
      };

      // Only add activities with meaningful content
      if (activity.what && activity.what.trim() !== '' && activity.what !== 'What?') {
        activities.push(activity);
        console.log(`📝 Parsed activity: ${activity.what} (${activity.month})`);
      }
    }

    return activities;
  }

  /**
   * Research-based comprehensive activity categorization system
   * Based on festival app best practices and Festival Family's actual content
   */
  private determineActivityType(what: string, where: string = '', month: string = ''): 'workshop' | 'meetup' | 'performance' | 'game' | 'social' | 'food' | 'other' {
    const whatLower = what.toLowerCase();
    const whereLower = where.toLowerCase();
    const monthLower = month.toLowerCase();

    // Festival-specific categorization
    if (whereLower.includes('sziget') || whereLower.includes('balaton')) {
      if (whatLower.includes('daily meet') || whatLower.includes('hangout') || whatLower.includes('assembly')) {
        return 'meetup';
      }
      if (whatLower.includes('scavenger hunt') || whatLower.includes('competition') || whatLower.includes('hunt')) {
        return 'game';
      }
      if (whatLower.includes('dinner') || whatLower.includes('food')) {
        return 'food';
      }
    }

    // Pre-meet and reunion events (high-value social gatherings)
    if (whatLower.includes('pre-meet') || whatLower.includes('reunion') || whatLower.includes('new years')) {
      return 'social';
    }

    // Raffle and competitions
    if (whatLower.includes('raffle') || whatLower.includes('competition') || whatLower.includes('picture')) {
      return 'game';
    }

    // Workshops and educational content
    if (whatLower.includes('workshop') || whatLower.includes('training') || whatLower.includes('getting to know')) {
      return 'workshop';
    }

    // Social gatherings and parties
    if (whatLower.includes('party') || whatLower.includes('celebration') || whatLower.includes('vacation')) {
      return 'social';
    }

    // Meetups and gatherings
    if (whatLower.includes('meet') || whatLower.includes('gathering') || whatLower.includes('assembly') ||
        whatLower.includes('hangout') || whatLower.includes('greet')) {
      return 'meetup';
    }

    // Performances and shows
    if (whatLower.includes('performance') || whatLower.includes('show') || whatLower.includes('concert')) {
      return 'performance';
    }

    // Food-related activities
    if (whatLower.includes('food') || whatLower.includes('dinner') || whatLower.includes('lunch') || whatLower.includes('breakfast')) {
      return 'food';
    }

    // Default fallback
    return 'other';
  }

  /**
   * Parse date string and convert to proper format
   */
  private parseDate(dateStr: string, month: string): Date | null {
    if (!dateStr || dateStr === '') return null;

    try {
      // Handle various date formats
      const currentYear = new Date().getFullYear();
      
      // Extract year from month if present (e.g., "January '26")
      let year = currentYear;
      const yearMatch = month.match(/'(\d{2})/);
      if (yearMatch) {
        year = 2000 + parseInt(yearMatch[1]);
      }

      // Handle date ranges like "21-24" or "26-3"
      const dateMatch = dateStr.match(/(\d+)(-(\d+))?/);
      if (dateMatch) {
        const startDay = parseInt(dateMatch[1]);
        
        // Extract month name from month string
        const monthName = month.replace(/'.*/, '').trim();
        const monthMap: { [key: string]: number } = {
          'january': 0, 'february': 1, 'march': 2, 'april': 3,
          'may': 4, 'june': 5, 'july': 6, 'august': 7,
          'september': 8, 'october': 9, 'november': 10, 'december': 11
        };
        
        const monthIndex = monthMap[monthName.toLowerCase()];
        if (monthIndex !== undefined) {
          return new Date(year, monthIndex, startDay);
        }
      }

      return null;
    } catch (error) {
      console.warn('Error parsing date:', dateStr, error);
      return null;
    }
  }

  /**
   * Migrate activities from festival data to database
   */
  async migrateActivities(festivalData: FestivalData): Promise<{ success: boolean; count: number; errors: string[] }> {
    const activities = this.parseActivitiesData(festivalData);
    const errors: string[] = [];
    let successCount = 0;

    console.log(`Found ${activities.length} activities to migrate`);

    for (const activity of activities) {
      try {
        const startDate = this.parseDate(activity.dateFromTill, activity.month);
        const activityType = this.determineActivityType(activity.what, activity.where, activity.month);

        const dbActivity = {
          title: activity.what,
          description: `${activity.where ? `Location: ${activity.where}\n` : ''}${activity.timeFromTill ? `Time: ${activity.timeFromTill}\n` : ''}${activity.contact ? `Contact: ${activity.contact}` : ''}`.trim(),
          type: activityType,
          location: activity.where || 'TBD', // Location is required, use 'TBD' as fallback
          start_date: startDate?.toISOString() || null,
          end_date: null,
          capacity: null,
          status: 'published',
          is_featured: activity.plansAreFinal === 'TRUE',
          festival_id: null,
          created_by: null, // Will be set by RLS
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        const { error } = await supabase
          .from('activities')
          .insert(dbActivity);

        if (error) {
          errors.push(`Failed to insert activity "${activity.what}": ${error.message}`);
        } else {
          successCount++;
          console.log(`✅ Migrated activity: ${activity.what}`);
        }
      } catch (error) {
        errors.push(`Error processing activity "${activity.what}": ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return {
      success: errors.length === 0,
      count: successCount,
      errors
    };
  }

  /**
   * Load festival data from JSON file and migrate to database
   */
  async loadAndMigrateData(): Promise<{ success: boolean; count: number; errors: string[] }> {
    try {
      // In a real implementation, you would load this from the file system
      // For now, we'll return a placeholder result
      console.log('Festival data migration service ready');
      
      return {
        success: true,
        count: 0,
        errors: ['Data loading not implemented yet - requires file system access']
      };
    } catch (error) {
      return {
        success: false,
        count: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Clear existing activities and migrate fresh data
   */
  async refreshActivitiesData(festivalData: FestivalData): Promise<{ success: boolean; count: number; errors: string[] }> {
    try {
      // First, clear existing activities (optional - you might want to keep existing data)
      console.log('Refreshing activities data...');
      
      // Migrate new activities
      return await this.migrateActivities(festivalData);
    } catch (error) {
      return {
        success: false,
        count: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }
}

// Export singleton instance
export const festivalDataMigrationService = new FestivalDataMigrationService();

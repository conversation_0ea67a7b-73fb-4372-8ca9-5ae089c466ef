-- Fix Existing Schema Issues for Festival Family
-- This script fixes existing tables and adds missing functions
-- Run this in Supabase SQL Editor to complete the database setup

-- ============================================================================
-- FIX EXISTING TABLE SCHEMAS
-- ============================================================================

-- Fix FAQ table - add missing category column
ALTER TABLE faqs ADD COLUMN IF NOT EXISTS category TEXT DEFAULT 'GENERAL' 
CHECK (category IN ('GENERAL', 'TICKETS', 'ACCOMMODATION', 'TRANSPORTATION', 'FESTIVAL', 'SAFETY'));

-- Fix announcements table - ensure it has the right structure
ALTER TABLE announcements ADD COLUMN IF NOT EXISTS announcement_type TEXT DEFAULT 'information' 
CHECK (announcement_type IN ('information', 'warning', 'success', 'error', 'urgent'));

ALTER TABLE announcements ADD COLUMN IF NOT EXISTS target_audience TEXT DEFAULT 'all' 
CHECK (target_audience IN ('all', 'admins', 'users', 'festival_family'));

ALTER TABLE announcements ADD COLUMN IF NOT EXISTS is_pinned BOOLEAN DEFAULT false;
ALTER TABLE announcements ADD COLUMN IF NOT EXISTS start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW();
ALTER TABLE announcements ADD COLUMN IF NOT EXISTS end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL;
ALTER TABLE announcements ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb;

-- Fix tips table - ensure it has the right structure  
ALTER TABLE tips ADD COLUMN IF NOT EXISTS category TEXT DEFAULT 'GENERAL'
CHECK (category IN ('PACKING', 'BUDGET', 'NAVIGATION', 'SAFETY', 'TRANSPORTATION', 'ACCOMMODATION', 'ATTRACTIONS', 'GENERAL'));

ALTER TABLE tips ADD COLUMN IF NOT EXISTS order_index INTEGER DEFAULT 0;
ALTER TABLE tips ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb;

-- ============================================================================
-- CREATE MISSING TABLES
-- ============================================================================

-- Create content management table if it doesn't exist
CREATE TABLE IF NOT EXISTS content_management (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    content_key TEXT NOT NULL UNIQUE,
    content_type TEXT NOT NULL CHECK (content_type IN ('hero', 'marketing', 'contact', 'emergency', 'page_content', 'ui_text')),
    title TEXT,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT true,
    version INTEGER DEFAULT 1,
    language TEXT DEFAULT 'en',
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create user preferences table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    preference_category TEXT NOT NULL CHECK (preference_category IN ('notifications', 'privacy', 'festival', 'communication', 'accessibility')),
    preference_key TEXT NOT NULL,
    preference_value JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    -- Ensure one preference per user per category/key combination
    UNIQUE(user_id, preference_category, preference_key)
);

-- Create emergency contacts table if it doesn't exist
CREATE TABLE IF NOT EXISTS emergency_contacts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    festival_id UUID REFERENCES festivals(id) ON DELETE CASCADE,
    contact_type TEXT NOT NULL CHECK (contact_type IN ('festival_organizer', 'medical', 'security', 'local_emergency', 'festival_family')),
    name TEXT NOT NULL,
    phone TEXT,
    email TEXT,
    description TEXT,
    is_primary BOOLEAN DEFAULT false,
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create safety information table if it doesn't exist
CREATE TABLE IF NOT EXISTS safety_information (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    festival_id UUID REFERENCES festivals(id) ON DELETE CASCADE,
    safety_category TEXT NOT NULL CHECK (safety_category IN ('general', 'medical', 'security', 'weather', 'transportation', 'emergency_procedures')),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    is_alert BOOLEAN DEFAULT false,
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- ============================================================================
-- CREATE MISSING ADMIN FUNCTIONS
-- ============================================================================

-- Create or replace the is_super_admin helper function (MISSING)
CREATE OR REPLACE FUNCTION is_super_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Direct query without RLS to avoid recursion
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = user_id 
        AND role = 'SUPER_ADMIN'
    );
END;
$$;

-- Create or replace the is_content_admin helper function (MISSING)
CREATE OR REPLACE FUNCTION is_content_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Direct query without RLS to avoid recursion
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = user_id 
        AND role IN ('SUPER_ADMIN', 'CONTENT_ADMIN')
    );
END;
$$;

-- Create or replace the can_manage_groups helper function (MISSING)
CREATE OR REPLACE FUNCTION can_manage_groups(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Direct query without RLS to avoid recursion
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = user_id 
        AND role IN ('SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR')
    );
END;
$$;

-- ============================================================================
-- ROW LEVEL SECURITY POLICIES
-- ============================================================================

-- Enable RLS on new tables (only if they don't already have it)
ALTER TABLE content_management ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE emergency_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE safety_information ENABLE ROW LEVEL SECURITY;

-- Content management policies
DROP POLICY IF EXISTS "Anyone can view active content" ON content_management;
CREATE POLICY "Anyone can view active content" 
    ON content_management FOR SELECT 
    USING (is_active = true);

DROP POLICY IF EXISTS "Content admins can manage content" ON content_management;
CREATE POLICY "Content admins can manage content" 
    ON content_management FOR ALL 
    USING (is_content_admin(auth.uid()));

-- User preferences policies
DROP POLICY IF EXISTS "Users can view their own preferences" ON user_preferences;
CREATE POLICY "Users can view their own preferences" 
    ON user_preferences FOR SELECT 
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can manage their own preferences" ON user_preferences;
CREATE POLICY "Users can manage their own preferences" 
    ON user_preferences FOR ALL 
    USING (auth.uid() = user_id);

-- Emergency contacts policies
DROP POLICY IF EXISTS "Anyone can view active emergency contacts" ON emergency_contacts;
CREATE POLICY "Anyone can view active emergency contacts" 
    ON emergency_contacts FOR SELECT 
    USING (is_active = true);

DROP POLICY IF EXISTS "Content admins can manage emergency contacts" ON emergency_contacts;
CREATE POLICY "Content admins can manage emergency contacts" 
    ON emergency_contacts FOR ALL 
    USING (is_content_admin(auth.uid()));

-- Safety information policies
DROP POLICY IF EXISTS "Anyone can view active safety information" ON safety_information;
CREATE POLICY "Anyone can view active safety information" 
    ON safety_information FOR SELECT 
    USING (is_active = true);

DROP POLICY IF EXISTS "Content admins can manage safety information" ON safety_information;
CREATE POLICY "Content admins can manage safety information" 
    ON safety_information FOR ALL 
    USING (is_content_admin(auth.uid()));

-- ============================================================================
-- SAMPLE DATA (FIXED)
-- ============================================================================

-- Insert sample content management data
INSERT INTO content_management (content_key, content_type, title, content, metadata) VALUES
('hero_title', 'hero', 'Welcome to Festival Family', 'Find your tribe at festivals worldwide', '{"editable": true, "section": "hero"}'),
('hero_subtitle', 'hero', 'Connect with like-minded festival-goers', 'Join activities, make friends, and create unforgettable memories together', '{"editable": true, "section": "hero"}'),
('app_tagline', 'marketing', 'Your Festival Community Awaits', 'Connect, coordinate, and celebrate with fellow festival lovers', '{"editable": true, "section": "marketing"}'),
('contact_email', 'contact', 'Contact Email', '<EMAIL>', '{"editable": true, "type": "email"}'),
('contact_support', 'contact', 'Support Email', '<EMAIL>', '{"editable": true, "type": "email"}'),
('emergency_hotline', 'emergency', 'Festival Family Emergency Line', '+31 6 12345678', '{"editable": true, "type": "phone", "available": "24/7"}')
ON CONFLICT (content_key) DO NOTHING;

-- Insert sample FAQ (NOW WITH CORRECT COLUMN)
INSERT INTO faqs (question, answer, category, order_index) VALUES
('How do I join Festival Family activities?', 'You can join Festival Family activities by browsing the activities section and clicking "Join" on any activity that interests you. Make sure to check the requirements and deadlines!', 'FESTIVAL', 1),
('What should I pack for Sziget Festival?', 'Check out our comprehensive packing guide in the Tips section! Essential items include comfortable shoes, sunscreen, portable charger, and cash in Hungarian Forint.', 'GENERAL', 2)
ON CONFLICT DO NOTHING;

-- Grant permissions to authenticated users
GRANT EXECUTE ON FUNCTION is_admin TO authenticated;
GRANT EXECUTE ON FUNCTION is_super_admin TO authenticated;
GRANT EXECUTE ON FUNCTION is_content_admin TO authenticated;
GRANT EXECUTE ON FUNCTION can_manage_groups TO authenticated;

-- ============================================================================
-- VALIDATION
-- ============================================================================

-- Test that all tables and functions are working
DO $$
DECLARE
    content_count INTEGER;
    preferences_count INTEGER;
    emergency_count INTEGER;
    safety_count INTEGER;
    announcements_count INTEGER;
    tips_count INTEGER;
    faqs_count INTEGER;
    admin_test BOOLEAN;
    super_admin_test BOOLEAN;
    content_admin_test BOOLEAN;
    groups_test BOOLEAN;
BEGIN
    -- Check all tables
    SELECT COUNT(*) INTO content_count FROM content_management;
    SELECT COUNT(*) INTO preferences_count FROM user_preferences;
    SELECT COUNT(*) INTO emergency_count FROM emergency_contacts;
    SELECT COUNT(*) INTO safety_count FROM safety_information;
    SELECT COUNT(*) INTO announcements_count FROM announcements;
    SELECT COUNT(*) INTO tips_count FROM tips;
    SELECT COUNT(*) INTO faqs_count FROM faqs;
    
    -- Test all admin functions
    SELECT is_admin() INTO admin_test;
    SELECT is_super_admin() INTO super_admin_test;
    SELECT is_content_admin() INTO content_admin_test;
    SELECT can_manage_groups() INTO groups_test;
    
    RAISE NOTICE 'SUCCESS: All tables accessible';
    RAISE NOTICE 'content_management: % records', content_count;
    RAISE NOTICE 'user_preferences: % records', preferences_count;
    RAISE NOTICE 'emergency_contacts: % records', emergency_count;
    RAISE NOTICE 'safety_information: % records', safety_count;
    RAISE NOTICE 'announcements: % records', announcements_count;
    RAISE NOTICE 'tips: % records', tips_count;
    RAISE NOTICE 'faqs: % records', faqs_count;
    RAISE NOTICE 'Admin functions test: is_admin=%, is_super_admin=%, is_content_admin=%, can_manage_groups=%', admin_test, super_admin_test, content_admin_test, groups_test;
END $$;

# Technical Debt Document

This document tracks known technical debt in the Festival Family application.

## Previously Addressed Issues

Significant effort has been invested in resolving previously identified technical debt, particularly concerning the TypeScript type system. Key areas addressed include:

*   **Type System Inconsistencies:** Standardized activity types and aligned interface definitions with data structures (including mock data).
*   **Inconsistent Type Imports:** Established clearer patterns for type imports, primarily leveraging Supabase-generated types.
*   **Type Conversion Complexity:** Simplified type conversions by adopting strategies like direct Supabase type usage and explicit mapping where necessary.

Details of the solutions and strategies implemented can be found in `scratchpad.md`.

## Current Technical Debt

*(Add new items here as they are identified)*

*   **Item:** [Brief description of the debt]
    *   **Impact:** [Consequences of the debt]
    *   **Recommendation:** [Suggested solution or mitigation]
    *   **Priority:** [High/Medium/Low]

## Documentation Review

*   **`scratchpad.md`:** Contains valuable lessons learned during development, especially regarding TypeScript and Supabase integration. Consider extracting key decisions into ADRs (Architecture Decision Records) or updating the main `README.md` for better long-term visibility. The file itself serves as a historical log.
*   **`ROADMAP.md`:** No formal roadmap document currently exists in the project root.

## Best Practices & Structure

*   **Tailwind Configuration:** `tailwind.config.js` and `tailwind.config.d.ts` are currently in the project root. This is standard practice and generally recommended for discoverability, especially in projects of this size. Moving them might introduce unnecessary complexity unless the project grows significantly larger and requires a more modular configuration structure.
*   **Project Rules:** A `.trae/project_rules.md` file has been created to capture the project vision and AI interaction guidelines.